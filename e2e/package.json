{"name": "web-insights-e2e", "version": "1.0.0", "license": "UNLICENSED", "scripts": {"test": "TEST_ENV=local cucumber-js --config cucumber.js --require-module ts-node/register --tags=${TEST_TAGS:-'not @skip'}", "test:ci": "TEST_ENV=ci cucumber-js --config cucumber.js --require-module ts-node/register --tags=${TEST_TAGS:-'not @skip'}", "test:dev": "TEST_ENV=dev cucumber-js --config cucumber.js --require-module ts-node/register --tags=${TEST_TAGS:-'not @skip'}", "test:dev:watch": "./node_modules/.bin/nodemon --exec yarn test:dev", "test:watch": "./node_modules/.bin/nodemon --exec yarn test"}, "dependencies": {}, "devDependencies": {"@cucumber/cucumber": "12.1.0", "@faker-js/faker": "9.9.0", "@playwright/browser-chromium": "1.54.2", "@playwright/test": "1.54.2", "@types/chai": "5.2.2", "cucumber-html-reporter": "7.2.0", "cucumber-junit": "1.7.1", "cucumber-pretty": "6.0.1", "cucumberjs-junitxml": "1.0.0", "graphql-tag": "2.12.6", "nodemon": "3.1.10", "playwright": "1.54.2", "ts-node": "10.9.2"}, "resolutions": {"string-width": "4.2.3", "strip-ansi": "6.0.1"}}