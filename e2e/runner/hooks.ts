import { getValueByPath } from '../utils/get-object-path-value';
import { After, AfterAll, Before, BeforeAll, ITestCaseHookParameter, setDefaultTimeout } from '@cucumber/cucumber';
import { <PERSON><PERSON><PERSON>, BrowserContext, BrowserContextOptions, chromium, Page } from 'playwright';

declare global {
  var page: Page;
  var context: BrowserContext;
  var browser: Browser;
  var ctxData: {
    getCtxValue: (key: string) => any;
    getCtxData: () => IStepsLoggedInUser & Record<string, any>;
    setCtxData: (data: IStepsLoggedInUser) => void;
  };
}

setDefaultTimeout(-1);

interface IStepsLoggedInUser {
  _id?: string;
  token?: string;
  email?: string;
  logInPhoneNumber?: string;
  isLoggedIn?: boolean;

  [key: string]: any;
}

let CtxData = {};

BeforeAll(async () => {
  global.ctxData = {
    getCtxData() {
      return CtxData;
    },
    getCtxValue(key: string) {
      const value = getValueByPath(CtxData, key);
      return CtxData ? value : undefined;
    },
    setCtxData(data: IStepsLoggedInUser) {
      CtxData = { ...CtxData, ...data };
    }
  };

  global.browser = await chromium.launch({
    headless: false,
    chromiumSandbox: false
    // timeout: 5000
  });
});

AfterAll(async () => {
  await global.browser.close();
});

const isCI = process.env.TEST_ENV === 'ci';

function getTimezoneTag(scenario: ITestCaseHookParameter) {
  const timezoneTag = scenario?.pickle?.tags?.find((tag) => tag.name.includes('@timezoneId'));
  const timezoneId = timezoneTag?.name?.split('=')?.[1];
  global.ctxData.setCtxData({ timezoneId });
  if (!timezoneId) {
    global.ctxData.setCtxData({ timezoneId: undefined });
  }
  return timezoneId;
}

Before(async (scenario: ITestCaseHookParameter) => {
  const timezoneId = getTimezoneTag(scenario);
  global.context = await global.browser.newContext(
    isCI
      ? {}
      : ({
          timezoneId: timezoneId,
          recordVideo: {
            dir: 'videos/' + scenario.pickle.name
          }
        } as BrowserContextOptions)
  );
  global.page = await global.context.newPage();
});

const skipIsExperiment = (scenario: ITestCaseHookParameter) => {
  const isExperiment = scenario?.pickle?.tags?.find((tag) => tag.name.includes('@isExperiment'));
  const experimentDate = isExperiment?.name?.split('=')?.[1];

  if (!isExperiment || !experimentDate) return;
  if (new Date(experimentDate) < new Date()) return;

  if (scenario.result.status !== 'PASSED' && scenario.result.status !== 'SKIPPED') {
    console.log('/\n Experiment status: ', scenario.result.status);
    console.log('FEATURE: ', scenario.gherkinDocument.uri);
    console.log('SCENARIO: ', scenario.pickle.name);
    console.log(scenario.result.message);
    // @ts-ignore
    scenario.result.status = 'SKIPPED';
  }
};

After(async (scenario: ITestCaseHookParameter) => {
  skipIsExperiment(scenario);

  await global.page.close();
  await global.context.close();
});
