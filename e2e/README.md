### E2E Setup

#### Tools

- Playwright (automation)
- <PERSON><PERSON><PERSON><PERSON> (BDD)
- <PERSON><PERSON> (assertions)

To run the tests:

```bash
yarn test
```

#### To do

_Basic setup is done where <PERSON><PERSON> in instantiating an instance of Chrome to launch web-insights from the local system and checking for assertions and reporting success/failures_

Following are some of the few things in to do:

- [ ] Watch mode
- [ ] Environment based configs
- [ ] CI/CD setup
- [ ] Port from Cypress to Playwright
