import { When } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import { DateTime } from 'luxon';
import { moduleFarm } from '../../sdks/module-farm';
import { moduleUser } from '../../sdks/module-user';
import { getConfig } from '../../utils/get-config';
import { getFakerValue } from '../../utils/get-faker-value';
import { GetApiResponse } from '../custom-commands/wait-for-api';

const changeFarmTimeZone = async (farmId: string, timeZone: string) => {
  const { adminUsername: email, adminPassword: password } = getConfig('backoffice');
  // Admin login
  const { data: backofficeUserLoginData } = await moduleUser.query({
    v1LogInByEmail: [
      { email, password, rememberMe: true },
      { token: true, user: { _id: true } }
    ]
  });

  const { data: farmCreateOneData } = await moduleFarm.mutation(
    {
      farmUpdateById: [
        {
          _id: farmId,
          record: { timezone: timeZone }
        },
        { record: { _id: true } }
      ]
    },
    {
      headers: {
        'Content-Type': 'application/json',
        'x-jwt-token': backofficeUserLoginData?.v1LogInByEmail?.token
      }
    }
  );
};

const getDateFormatted = (date: string) => {
  const [day, month, year] = date.split('-');
  const expectedDate = DateTime.fromObject({ day: +day, month: +month, year: +year }).toFormat('dd MMM yyyy');
  return expectedDate;
};

When('I test multiple farms dates with timezone', async function () {
  const { baseUrlInsights } = getConfig();

  await page.goto(`${baseUrlInsights}/farm/572-farm-for-timezone-test`);

  // Get current farm data
  const v1ListFarmsResponse = await GetApiResponse('v1ListFarms');
  const farmData = v1ListFarmsResponse?.v1ListFarms?.farms?.[0];
  expect(farmData).toBeTruthy();

  const timezones = ['Europe/London', 'Asia/Dubai', 'Europe/Berlin', 'America/Toronto'];
  const timezone = timezones[Math.floor(Math.random() * timezones.length)];

  // Change farm timezone
  await changeFarmTimeZone(farmData._id, timezone);

  // Validate page rendered correctly
  const pondsTab = await page.waitForSelector('[data-cy=production-insights-link]');
  await pondsTab.click();

  // Create new pond
  await page.locator('[data-cy=create-new-pond-btn]').click();
  await page.waitForTimeout(500);

  const pondModal = page.locator('[data-cy=add-edit-pond-modal]');
  await pondModal.waitFor({ state: 'visible' });
  await pondModal.locator('input#name').fill(getFakerValue('pondName'));
  await pondModal.locator('input#size').fill(getFakerValue('number'));
  await pondModal.locator('#superVisorId').click();
  const option = pondModal.locator(`#superVisorId .react-select__option`);
  const elementCount = await option.count();
  if (elementCount > 0) {
    await option.first().click();
  } else {
    await pondModal.click();
  }

  await Promise.all([
    GetApiResponse('v1CreatePond'),
    GetApiResponse('v1ListPonds'),
    page.click('[data-cy=add-edit-pond-modal] button[type=submit]')
  ]);

  // stock the pond

  await page.waitForSelector('[data-cy=pond-view-screen]', { state: 'visible' });
  // TODO: REMOVE WHEN FIX THE BUG
  await page.reload();
  await page.locator('[data-cy=pond-view-screen]').waitFor({ state: 'hidden' });
  await page.locator('[data-cy=pond-view-screen]').waitFor({ state: 'visible' });
  // TODO END

  await page.locator('[data-cy=pond-view-actions] [data-cy=action-menu-button]').click();
  await page.locator('[data-cy=pond-view-actions] [role=menu] [data-cy=stock-pond-btn]').click();

  const stockModal = page.locator('[data-cy=stock-pond-modal]');
  await stockModal.waitFor({ state: 'visible' });
  await stockModal.locator('input#cycle').fill('1');
  await stockModal.locator('input#seedingQuantity').fill(getFakerValue('number'));
  await stockModal.locator('input#seedingAverageWeight').fill(getFakerValue('number'));
  await stockModal.locator('input#stockedAt').click();
  await stockModal.locator(`.react-datepicker__day--015`).click();

  let dateValue = await stockModal.locator('input#stockedAt').getAttribute('value');
  await stockModal.locator('button[type=submit]').click();

  // skip production form
  await page.waitForSelector('[data-cy=production-target-form]');
  await page.locator('[data-cy=production-target-form] [data-cy=later-btn]').click();

  // Validate stock created
  await page.waitForSelector('[data-cy=stock-pond-modal]', { state: 'hidden' });
  await page.waitForSelector('[data-cy=pond-view-actions] [data-cy=action-menu-button]', { state: 'visible' });
  await page.waitForSelector('[data-cy=pond-view-tabs]', { state: 'visible' });
  await page.locator('[data-cy=pond-view-tabs] [data-cy=insights-tab]').click();

  // validate stocked date
  expect(page.locator('[data-cy=stocking-details] [data-cy=stocking-date]')).toContainText(getDateFormatted(dateValue));

  // edit stocking date
  await global.page.locator('[data-cy=edit-stock-btn]').click();
  const editStockModal = page.locator('[data-cy=stock-pond-modal]');
  await editStockModal.waitFor({ state: 'visible' });
  await editStockModal.locator('input#stockedAt').click();
  await editStockModal.locator(`.react-datepicker__day--018`).click();
  dateValue = await editStockModal.locator('input#stockedAt').getAttribute('value');
  await editStockModal.locator('button[type=submit]').click();

  // Validate stock updated
  await page.waitForSelector('[data-cy=stocking-details]', { state: 'visible' });

  // validate stocked date
  expect(page.locator('[data-cy=stocking-details] [data-cy=stocking-date]')).toContainText(getDateFormatted(dateValue));

  // Harvest pond
  await page.locator('[data-cy=pond-view-actions] [data-cy=action-menu-button]').click();
  await page.locator('[data-cy=pond-view-actions] [role=menu] [data-cy=harvest-pond-btn]').click();
  const harvestModal = page.locator('[data-cy=harvest-population-modal]');
  await harvestModal.waitFor({ state: 'visible' });
  await harvestModal.locator('input#date').click();
  await harvestModal.locator(`.react-datepicker__day--020`).click();
  dateValue = await harvestModal.locator('input#date').getAttribute('value');
  await harvestModal.locator('input#lbsHarvested').fill(getFakerValue('number'));
  await harvestModal.locator('input#weight').fill(getFakerValue('number'));
  await harvestModal.locator('button[type=submit]').click();
  await harvestModal.waitFor({ state: 'hidden' });

  // Validate harvest created
  page.waitForSelector('[data-cy=harvest-details]', { state: 'visible' });
  expect(page.locator('[data-cy=harvest-details] [data-cy=harvest-date]')).toContainText(getDateFormatted(dateValue));

  // Edit harvest date
  await page.locator('[data-cy=harvest-details] [data-cy=edit-harvest-btn]').click();
  await harvestModal.waitFor({ state: 'visible' });
  await harvestModal.locator('input#date').click();
  await harvestModal.locator(`.react-datepicker__day--022`).click();
  dateValue = await harvestModal.locator('input#date').getAttribute('value');
  await harvestModal.locator('button[type=submit]').click();

  // Validate harvest updated
  await page.waitForSelector('[data-cy=section-loader]', { state: 'visible' });
  await page.waitForSelector('[data-cy=section-loader]', { state: 'hidden' });
  expect(page.locator('[data-cy=harvest-details] [data-cy=harvest-date]')).toContainText(getDateFormatted(dateValue));

  // Delete pond
  await page.locator('[data-cy=action-menu-button]').click();
  await page.locator('[data-cy=archive-pond-btn]').click();
  const archivePondModal = page.locator('[data-cy=archive-pond-modal]');
  await archivePondModal.waitFor({ state: 'visible' });
  await archivePondModal.locator('[data-cy=archive-pond-submit-btn]').click();

  // Validate pond deleted
  await archivePondModal.waitFor({ state: 'hidden' });
  await page.waitForSelector('[data-cy=harvest-details] [data-cy=harvest-pond-btn]', { state: 'hidden' });
});
