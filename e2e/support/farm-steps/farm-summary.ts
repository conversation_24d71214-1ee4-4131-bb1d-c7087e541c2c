import { When } from '@cucumber/cucumber';
import { customLocator } from '../custom-commands/selectors';
import { expect } from 'playwright/test';

When('I match pond selected variables from {string} with modal', async function (storeKey: string) {
  const viewFields = ctxData.getCtxValue(storeKey)?.v1GetAccountDetails?.user?.preferences?.viewFields?.pondVariables;
  if (!viewFields?.length) return true;

  const selectedVariableLocator = '[data-cy=customize-view-selected-variables]';
  const modalLocator = '[data-cy=customize-view-modal]';

  viewFields.forEach(async (field: string) => {
    const locator = customLocator(`${modalLocator} input#${field}`);
    await expect(locator).toBeChecked();

    const selectedLocator = customLocator(`${selectedVariableLocator} [data-cy=variable-${field}]`);
    await selectedLocator.waitFor({ state: 'visible' });
  });

  const variableName = viewFields[0];
  await customLocator(`${modalLocator} [data-cy=${variableName}-label]`).click();
  await customLocator(`${selectedVariableLocator} [data-cy=variable-${variableName}]`).waitFor({ state: 'hidden' });

  //   reset the unselected variable
  await customLocator(`${modalLocator} [data-cy=${variableName}-label]`).click();
  await customLocator(`${selectedVariableLocator} [data-cy=variable-${variableName}]`).waitFor({ state: 'visible' });

  //   remove from selected Section
  await customLocator(
    `${selectedVariableLocator} [data-cy=variable-${variableName}] [data-cy=remove-variable]`
  ).click();
  await expect(customLocator(`${modalLocator} input#${variableName}`)).not.toBeChecked();

  //   reset the unselected variable
  await customLocator(`${modalLocator} [data-cy=${variableName}-label]`).click();
  await customLocator(`${selectedVariableLocator} [data-cy=variable-${variableName}]`).waitFor({ state: 'visible' });
});
