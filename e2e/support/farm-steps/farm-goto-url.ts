import { createNewFarm } from '../../utils/create-new-farm';
import { getConfig } from '../../utils/get-config';
import { slugify } from '../../utils/string';
import { When } from '@cucumber/cucumber';
import { getListFarms, getUserToken } from '../../utils/module-farm-apis';

async function checkIfFarmExist(farmName: string) {
  let farmData;
  try {
    const token = await getUserToken();
    const { v1ListFarms } = await getListFarms({ token, filter: { name: farmName?.toLowerCase() } });
    await page.locator('[data-cy=farm-selector-btn]').click();
    farmData = v1ListFarms?.farms?.find((ele) => ele?.name?.toLowerCase() === farmName?.toLowerCase());
  } catch (e) {
    console.error(`[checkIfFarmExist] Error: ${e}`);
  }

  return farmData;
}

When('I go to current farm page', async () => {
  const farmName = process.env.TESTING_FARM_NAME ?? 'E2e farm';
  const { baseUrlInsights } = getConfig();
  // get farm from ctx
  const currentFarm = ctxData.getCtxValue('CURRENT_TESTING_FARM');
  if (currentFarm) {
    const { eid, name } = currentFarm;
    await page.goto(`${baseUrlInsights}/farm/${eid}-${slugify(name)}`);
    return true;
  }

  await page.goto(`${baseUrlInsights}/farm/271-e2e-farm`);
  await page.waitForLoadState('networkidle');

  const farmFromList = await checkIfFarmExist(farmName);
  if (farmFromList) {
    const { eid, name } = farmFromList;
    await page.goto(`${baseUrlInsights}/farm/${eid}-${slugify(name)}`);
    ctxData.setCtxData({ CURRENT_TESTING_FARM: { ...farmFromList, slug: `${eid}-${slugify(name)}` } });
    return true;
  }

  const newFarmData = await createNewFarm(farmName);
  if (newFarmData) {
    const { eid, name } = newFarmData;
    await page.goto(`${baseUrlInsights}/farm/${eid}-${slugify(name)}`);
    ctxData.setCtxData({ CURRENT_TESTING_FARM: { ...newFarmData, slug: `${eid}-${slugify(name)}` } });
    return true;
  }

  return 'skipped';
});
