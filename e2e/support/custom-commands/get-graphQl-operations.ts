import gql from 'graphql-tag';

export const getQueryOperationNameList = (query: string): Array<string> => {
  const obj = gql`
    ${query}
  `;
  if (obj.definitions[0]['operation'] !== 'query') return [];
  return obj.definitions[0]['selectionSet']['selections'].map((ele) => ele.name.value);
};

export const getMutationOperationNameList = (query: string): Array<string> => {
  const obj = gql`
    ${query}
  `;
  if (obj.definitions[0]['operation'] !== 'mutation') return [];
  return obj.definitions[0]['selectionSet']['selections'].map((ele) => ele.name.value);
};

export const hasQueryOperationName = (query: string, operationName: string): boolean => {
  const operationNameList = getQueryOperationNameList(query);
  return operationNameList.some((ele) => ele === operationName);
};

export const hasMutationOperationName = (query: string, operationName: string): boolean => {
  const operationNameList = getMutationOperationNameList(query);
  return operationNameList.some((ele) => ele === operationName);
};

export const hasOperationName = (query: string, operationName: string): boolean =>
  hasQueryOperationName(query, operationName) || hasMutationOperationName(query, operationName);
