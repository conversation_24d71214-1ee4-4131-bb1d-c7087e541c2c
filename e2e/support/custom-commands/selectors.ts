import { replaceWithCtxValue } from '../../utils/replace-with-ctx-value';
import { Page } from 'playwright';

type GenericFunctionType<T> = T extends (selector: string, ...args: infer U) => any
  ? (selector: string, ...args: Partial<U>) => ReturnType<T>
  : never;

export const customLocator: GenericFunctionType<Page['locator']> = (_selector, ...rest) => {
  const selector = replaceWithCtxValue(_selector);
  return page.locator(selector, ...rest);
};

export const custom$: GenericFunctionType<Page['$']> = async (_selector, ...rest) => {
  const selector = replaceWithCtxValue(_selector);
  return await page.$(selector, ...rest);
};
