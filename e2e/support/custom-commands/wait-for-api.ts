import { hasOperationName } from './get-graphQl-operations';

export async function GetApiResponse(operation: string) {
  let apiResponse;
  await global.page.waitForResponse(
    async (response) => {
      const postDataJSON = response.request().postDataJSON();
      if (!postDataJSON) return false;
      if (hasOperationName(postDataJSON.query, operation)) {
        const body = await response.body();
        const responseData = JSON.parse(body.toString());
        apiResponse = responseData.data;
        return true;
      }
      return false;
    },
    { timeout: 10000 }
  );
  return apiResponse;
}
