import { getConfig } from '../../utils/get-config';
import { Given } from '@cucumber/cucumber';

Given('I am logged in as an admin', async function () {
  const { baseUrlInsights, adminUsername, adminPassword } = getConfig();

  await page.goto(`${baseUrlInsights}/login`);
  await page.fill('#email', adminUsername);
  await page.fill('#password', adminPassword);
  await page.locator('#login-btn').click();

  await page.waitForNavigation();
});

Given('I am logged in with a {string} and {string}', async function (emailVal: string, passwordVal: string) {
  const { baseUrlInsights } = getConfig();
  const email = ctxData.getCtxValue(emailVal) ?? emailVal;
  const password = ctxData.getCtxValue(passwordVal) ?? passwordVal;

  await page.goto(`${baseUrlInsights}/login`);
  await page.fill('#email', email);
  await page.fill('#password', password);
  await page.locator('#login-btn').click();

  await page.waitForNavigation();
});
