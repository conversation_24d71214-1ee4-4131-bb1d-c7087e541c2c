import { Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';

Then('I should see url contain {string}', async (param: string) => {
  const pageUrl = page.url();
  expect(pageUrl).toContain(param);
});

Then('I should see url search params contains {string}', async (params: string) => {
  await page.waitForURL((url) => {
    const urlSearch = url.search;
    return urlSearch.includes(params);
  });
});

Then('I should see url search params equals {string}', async (params: string) => {
  await page.waitForURL((url) => {
    const urlSearch = url.search;
    return urlSearch === params;
  });
  //wait for nextJS routing to finish
  await page.waitForTimeout(1000);
});
