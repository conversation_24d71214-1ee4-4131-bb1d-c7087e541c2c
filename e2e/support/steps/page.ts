import { Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import { getConfig } from '../../utils/get-config';

Then('I should be on the {string} page', async function (pageName: string) {
  const config = getConfig();
  await page.waitForURL(`${config.baseUrlInsights}${pageName}`);
});

Then('I should be on page starting with {string}', async function (pageName: string) {
  const config = getConfig();
  await expect(page).toHaveURL(new RegExp(`^${config.baseUrlInsights}${pageName}`, 'i'));
});

Then('I should not be on the {string} page', async function (pageName: string) {
  const config = getConfig();
  const url = page.url();
  expect(url).not.toEqual(`${config.baseUrlInsights}${pageName}`);
});

Then('wait for page navigation', async function () {
  // todo migrate to waitForURL
  try {
    await page.waitForNavigation();
  } catch (e) {
    console.error(`[wait for page navigation] Error: ${e}`);
  }
});

Then('Reload page', async function () {
  await page.reload();
});

Then('log context', async () => {
  const text = ctxData.getCtxData();

  console.log(text);
});

Then('Temp step', async () => {
  await page.reload();
  await page.locator('[data-cy=pond-view-screen]').waitFor({ state: 'hidden' });
  await page.locator('[data-cy=pond-view-screen]').waitFor({ state: 'visible' });
});
