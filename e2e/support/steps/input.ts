import { getConfig } from '../../utils/get-config';
import { getFakerValue } from '../../utils/get-faker-value';
import { customLocator } from '../custom-commands/selectors';
import { Then, When } from '@cucumber/cucumber';
import { expect } from '@playwright/test';

When('I fill in {string} with {string}', async function (field: string, value: string) {
  const config = getConfig();
  value = config[value] ?? value;
  await page.locator(field).clear();
  await page.fill(field, value);
});

When('I fill in {string} with value of {string}', async function (field: string, key: string) {
  const value = ctxData.getCtxValue(key);
  await page.fill(field, value);
});

When('I clear {string}', async function (field: string) {
  await page.locator(field).fill('');
});

Then(
  'I fill in {string} with faker {string} as {string}',
  async function (field: string, fakerType: string, fakerKey: string) {
    let randomValue = getFakerValue(fakerType);
    ctxData.setCtxData({ [fakerKey]: randomValue });
    await page.locator(field).clear();
    await page.fill(field, randomValue);
  }
);

Then('I should see input {string} with value {string}', async function (selector: string, _value: string) {
  const value = ctxData.getCtxValue(_value) ?? _value;
  const input = page.locator(selector);
  const inputText = await input.inputValue();
  expect(inputText).toBe(value);
});

Then(
  'I should see input {string} with value {string} and suffix {string}',
  async function (selector: string, _value: string, suffix: string) {
    const value = ctxData.getCtxValue(_value) ?? _value;
    const input = page.locator(selector);
    const inputText = await input.inputValue();
    expect(inputText).toBe(`${value}${suffix ?? ''}`);
  }
);

Then('I should see number input {string} with value {string}', async function (selector: string, _value: string) {
  const value = ctxData.getCtxValue(_value) ?? _value;
  const input = page.locator(selector);
  const inputText = await input.inputValue();
  expect(inputText).toBe(Number(value).toFixed(1));
});

Then(
  'I should see number input {string} with value {string} and suffix {string}',
  async function (selector: string, _value: string, suffix: string) {
    const value = ctxData.getCtxValue(_value) ?? _value;
    const input = page.locator(selector);
    const inputText = await input.inputValue();
    expect(inputText).toBe(`${Number(value).toFixed(1)}${suffix ?? ''}`);
  }
);

Then('I react-select random option from {string} in {string}', async function (selector: string, wrapper: string) {
  await page.locator(selector).click();
  const elementCount = await page.locator(`${selector} .react-select__option`).count();
  if (elementCount > 0) {
    await page.locator(`${selector} .react-select__option`).first().click();
  } else {
    await page.locator(wrapper).click();
  }
});

Then('I react-select {string} from {string}', async function (option: string, selector: string) {
  const opt = ctxData.getCtxValue(option) ?? option;
  await page.locator(selector).click();
  await page.locator(`${selector} .react-select__option:has-text(${opt})`).first().click();
});

When('I select date as today for field {string}', async (selector: string) => {
  await page.locator(selector).click();
  await page.locator('.react-datepicker__day--today').click();
});

When('I select day {int} for field {string}', async (day: number, selector: string) => {
  const daySelector = `.react-datepicker__day--${day < 10 ? `00${day}` : `0${day}`}`;
  await page.locator(selector).click();
  await page.locator(daySelector).first().click();
});

Then('I clear pinInput {string} with length {int}', async function (selector: string, value: number) {
  await page.locator(`${selector} [id="pin-input:code:0"]`).click();
  for (let i = 0; i < value; i++) {
    await page.keyboard.press('Backspace');
  }
});
Then('I fill in pinInput {string} with value {string}', async function (selector: string, value: string) {
  await page.locator(`${selector} [id="pin-input:code:0"]`).click();
  for (let i = 0; i < value.length; i++) {
    await page.keyboard.press(value[i]);
  }
});

Then('Input {string} should be disabled', async (selector: string) => {
  await customLocator(selector).isDisabled();
});

Then('Input {string} should be enabled', async (selector: string) => {
  await customLocator(selector).isEnabled();
});

Then('I select value {string} from {string}', async (value: string, selector: string) => {
  await customLocator(selector).selectOption(value);
});

Then('I upload file {string} with selector {string}', async (file: string, selector: string) => {
  const [fileChooser] = await Promise.all([page.waitForEvent('filechooser'), page.locator(selector).click()]);
  await fileChooser.setFiles([file]);
});

Then('Input {string} should be checked', async (selector: string) => {
  const locator = customLocator(selector);
  await expect(locator).toBeChecked();
});

Then('Input {string} should not be checked', async (selector: string) => {
  const locator = customLocator(selector);
  await expect(locator).not.toBeChecked();
});
