import { getConfig } from '../../utils/get-config';
import { createFarm, createUserMembership } from '../../utils/module-farm-apis';
import { getUserMany, userLoginByEmail } from '../../utils/module-user-apis';
import { hasOperationName } from '../custom-commands/get-graphQl-operations';
import { DataTable, Then } from '@cucumber/cucumber';
import { faker } from '@faker-js/faker';

Then('Api with name {string} should be called', async function (operation: string) {
  await page.waitForRequest((request) => {
    const query = request.postDataJSON().query;
    return hasOperationName(query, operation);
  });
});

Then('Wait for Api {string} response', async function (operation: string) {
  await page.waitForResponse((response) => {
    const postDataJSON = response.request().postDataJSON();
    if (!postDataJSON) return false;
    return hasOperationName(postDataJSON.query, operation);
  });
});

Then('I save {string} api response as {string}', async (operation: string, key: string) => {
  await page.waitForResponse(async (response) => {
    const postDataJSON = response.request().postDataJSON();
    if (!postDataJSON) return false;
    if (hasOperationName(postDataJSON.query, operation)) {
      const body = await response.body();
      const responseData = JSON.parse(body.toString());
      ctxData.setCtxData({ [key]: responseData.data });
      return true;
    }
    return false;
  });
});

Then(
  'Backoffice admin add {int} farms and permissions for {string}',
  async function (count: number, ctxEmailKey: string) {
    const { adminUsername: email, adminPassword: password } = getConfig('backoffice');

    // Admin login
    const { token } = await userLoginByEmail(email, password);

    // Get insights user
    const userManyData = await getUserMany({ token, filter: { email: ctxData.getCtxValue(ctxEmailKey) } });

    for (let i = 0; i < count; i++) {
      const farmData = await createFarm({
        token,
        record: { status: 'active', name: faker.company.name() }
      });

      await createUserMembership({
        token,
        record: {
          role: 'admin',
          entity: 'farm',
          userId: userManyData[0]._id,
          entityId: farmData._id
        }
      });
    }
  }
);

Then(
  'Backoffice admin add farms with permissions for {string}',
  async function (ctxEmailKey: string, farnDataTable: DataTable) {
    const { adminUsername: email, adminPassword: password } = getConfig('backoffice');

    // Admin login
    const adminLogin = await userLoginByEmail(email, password);

    const { token } = adminLogin || {};

    // Get insights user
    const userManyData = (await getUserMany({ token, filter: { email: ctxData.getCtxValue(ctxEmailKey) } })) || [];

    for (const [farmName, key] of farnDataTable.rows()) {
      const farmData = await createFarm({
        token,
        record: { status: 'active', name: farmName }
      });
      ctxData.setCtxData({ [key]: farmName });

      await createUserMembership({
        token,
        record: {
          role: 'admin',
          entity: 'farm',
          userId: userManyData[0]?._id,
          entityId: farmData?._id
        }
      });
    }
  }
);

Then('wait for {int} seconds', async function (seconds: number) {
  await page.waitForTimeout(seconds * 1000);
});

Then('log store', async function () {
  console.log(ctxData.getCtxData());
});
