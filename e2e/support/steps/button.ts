import { expect } from '@playwright/test';
import { getValueByPath } from '../../utils/get-object-path-value';
import { custom$, customLocator } from '../custom-commands/selectors';
import { GetApiResponse } from '../custom-commands/wait-for-api';
import { DataTable, Then, When } from '@cucumber/cucumber';

Then('I press {string}', async (selector: string) => {
  await custom$(selector).then((el) => el.scrollIntoViewIfNeeded({ timeout: 1000 }));
  await custom$(selector).then((el) => el.click());
});

Then('I press {string} at position {int} {int}', async (selector: string, x: number, y: number) => {
  await custom$(selector).then((el) => el.click({ position: { x, y } }));
});

When('I press {string} and save api response as following', async (selector: string, dataTable: DataTable) => {
  const [query, responsePath, ctxKey] = dataTable.rows()[0];
  const [apiResponse] = await Promise.all([GetApiResponse(query), customLocator(selector).click()]);
  ctxData.setCtxData({ [ctxKey]: getValueByPath(apiResponse, responsePath) });
});

When('I press {string} and wait for {string} api response', async (selector: string, query: string) => {
  await Promise.all([GetApiResponse(query), page.locator(selector).click()]);
});

Then('Button {string} should be disabled', async (selector: string) => {
  const isDisabled = await (await custom$(selector)).isDisabled();
  expect(isDisabled).toBe(true);
});

Then('Button {string} should not be disabled', async (selector: string) => {
  const isDisabled = await (await custom$(selector)).isDisabled();
  expect(isDisabled).toBe(false);
});
