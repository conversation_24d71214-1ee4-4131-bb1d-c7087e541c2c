import { getConfig } from '../../utils/get-config';
import { replaceWithCtxValue } from '../../utils/replace-with-ctx-value';
import { Given } from '@cucumber/cucumber';

Given('I am on the {string} page', async function (pageName: string) {
  const { baseUrlInsights } = getConfig();
  await page.goto(`${baseUrlInsights}${pageName}`);
});

Given('I visit {string} page', async function (route: string) {
  const config = getConfig();
  const _route = replaceWithCtxValue(route) || route;
  await page.goto(`${config.baseUrlInsights}${_route}`);
});
