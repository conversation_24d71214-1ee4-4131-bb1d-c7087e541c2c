import { custom$, customLocator } from '../custom-commands/selectors';
import { DataTable, Given, Then, When } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import { replaceWithCtxValue } from '../../utils/replace-with-ctx-value';

Then('I should see element {string}', async function (selector: string) {
  const element = customLocator(selector);
  await element.waitFor({ state: 'visible' });
});

Then('I should not see element {string}', async function (selector: string) {
  await customLocator(selector).waitFor({ state: 'hidden' });
});

When('Element {string} is visible', async function (selector: string) {
  const element = customLocator(selector);
  await element.waitFor({ state: 'visible' });
});

When('Element {string} is not visible', async function (selector: string) {
  const element = customLocator(selector);
  await element.waitFor({ state: 'hidden' });
});

Then('I should see element {string} with text {string}', async function (selector: string, _text: string) {
  const text = replaceWithCtxValue(_text) ?? _text;
  await expect(customLocator(selector)).toContainText(text.toString(), { ignoreCase: true });
});

Then('I should see one of these elements', async (table: DataTable) => {
  let result = false;
  const data = table.rows();
  for (const row of data) {
    const [selector] = row;
    const element = await page.$(selector);

    if (!!element) {
      result = true;
      break;
    }
  }
  expect(result).toBe(true);
});

/**
 * @param {dataTable} | selector | state |
 *  @description State can be visible, hidden, attached, and detached
 *  - 'attached' - wait for element to be present in DOM.
 *  - 'detached' - wait for element to not be present in DOM.
 *  - 'visible' - wait for element to have non-empty bounding box and no visibility:hidden. Note that element without any content or with display:none has an empty bounding box and is not considered visible.
 *  - 'hidden' - wait for element to be either detached from DOM, or have an empty bounding box or visibility:hidden. This is opposite to the 'visible' option.
 *
 *  @example
 *   | selector           | state     |
 *   | #id                | visible   |
 *   | #id2               | hidden    |
 *   | [data-cy=test]     | attached  |
 *   | #id3               | detached  |
 */
type StateType = 'visible' | 'attached' | 'detached' | 'hidden';
When('Elements visibility on the page match the following', async function (dataTable: DataTable) {
  for (const row of dataTable.rows()) {
    const [selector, _state] = row;
    const state: StateType = ['visible', 'hidden', 'attached', 'detached'].includes(_state.toLowerCase())
      ? (_state.toLowerCase() as StateType)
      : 'visible';
    const element = customLocator(selector);
    await element.waitFor({ state });
  }
});

When(
  'Elements visibility on the {string} match the following',
  async function (prefixSelector: string, dataTable: DataTable) {
    for (const row of dataTable.rows()) {
      const [selector, _state] = row;
      const state: StateType = ['visible', 'hidden', 'attached', 'detached'].includes(_state.toLowerCase())
        ? (_state.toLowerCase() as StateType)
        : 'visible';
      const element = customLocator(`${prefixSelector} ${selector}`);
      await element.waitFor({ state });
    }
  }
);

Then(
  'I should see element {string} with attribute {string} equals {string}',
  async function (selector: string, attribute: string, attributeValue: string) {
    let selectorFromCtx = ctxData.getCtxValue(selector);
    if (selectorFromCtx) selectorFromCtx = `[data-cy="${selectorFromCtx}"]`;

    const ele = await page.$(selectorFromCtx ?? selector);
    expect(await ele.getAttribute(attribute)).toEqual(attributeValue);
  }
);

Then(
  'I should see element {string} with attribute {string} similar to {string}',
  async function (selector: string, attribute: string, attributeValue: string) {
    let selectorFromCtx = ctxData.getCtxValue(selector);
    if (selectorFromCtx) selectorFromCtx = `[data-cy="${selectorFromCtx}"]`;

    const ele = await page.$(selectorFromCtx ?? selector);
    expect(await ele.getAttribute(attribute)).toContain(attributeValue);
  }
);

Then('I wait for element {string} to disappear', async function (selector: string) {
  await page.waitForSelector(selector, { state: 'hidden' });
});

Then('Skip scenario if element {string} does not exist', async (selector: string) => {
  const ele = await custom$(selector);
  if (!ele) {
    return 'skipped';
  }
});

When('I hover over {string}', async (selector: string) => {
  await customLocator(selector).hover();
});

Given('I close all toasts', async () => {
  const toasts = await page.$$('[data-cy*=toast-] .close-toast');
  for (const ele of toasts) {
    const toast = page.locator('[data-cy*=toast-] .close-toast');
    if ((await toast.count()) <= 0) break;
    await toast.first().click();
  }
});

Then('I should see {string} elements of {string}', async (count: string, selector: string) => {
  const countValue = ctxData.getCtxValue(count) ?? count;
  if ((typeof countValue !== 'number' && typeof countValue !== 'string') || isNaN(+countValue)) {
    throw new Error(`${countValue} is not a valid number`);
  }
  const elements = await page.$$(selector);
  expect(elements.length).toBe(+countValue);
});

Then(
  'Element {string} should have attribute {string} with value {string}',
  async (selector: string, attributeName: string, attributeValue: string) => {
    const element = await custom$(selector);
    const attribute_Value = await element.getAttribute(attributeName);
    const attribute_name = replaceWithCtxValue(attributeValue);
    expect(attribute_Value).toEqual(attribute_name);
  }
);
