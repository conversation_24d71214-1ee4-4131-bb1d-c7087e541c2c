import { Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';

Then('I should see file list matches with {string}', async (key: string) => {
  const list = ctxData.getCtxValue(key);
  if (list.length === 0) {
    const noDataFound = page.locator('[data-cy=pond-file-list] [data-cy=table-has-no-data]');
    await expect(noDataFound).toBeVisible();
    return;
  }
  const tableRows = await page.$$('[data-cy=pond-file-list] [data-cy=table-data] tr');
  expect(tableRows).toHaveLength(list.length);

  const filesData = list[0];

  const { name, extension } = filesData;
  const fileName = page.locator('[data-cy=file-name]');
  await expect(fileName).toContainText(`${name}.${extension}`);
});
