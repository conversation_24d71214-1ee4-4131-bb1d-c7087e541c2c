import { DataTable, Then } from '@cucumber/cucumber';
import { replaceWithCtxValue } from '../../utils/replace-with-ctx-value';
import { createPopulationTestData } from '../../utils/create-test-data';

Then('Create populations for pondId {string}', async (id: string, dataTable: DataTable) => {
  const pondId = replaceWithCtxValue(id) ?? id;
  const [numberOfMonitorings, growthAverageWeight, daysBetweenMonitorings, weightCv, targetWeight] =
    dataTable.rows()[0];

  const res = await createPopulationTestData({
    pondId,
    numberOfMonitorings: numberOfMonitorings ? +numberOfMonitorings : undefined,
    growthAverageWeight: growthAverageWeight ? +growthAverageWeight : undefined,
    daysBetweenMonitorings: daysBetweenMonitorings ? +daysBetweenMonitorings : undefined,
    weightCv: weightCv ? +weightCv : undefined,
    targetWeight: targetWeight ? +targetWeight : undefined
  });
});
