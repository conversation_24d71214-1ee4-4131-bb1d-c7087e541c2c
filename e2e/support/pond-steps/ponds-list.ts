import { Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import { slugify } from '../../utils/string';
import { formatNumber } from '../../utils/ponds-numbers-format';
import keyBy from 'lodash/keyBy';

Then('I should see ponds list matches with {string}', async (key: string) => {
  const list = ctxData.getCtxValue(key);
  if (list.length === 0) {
    const noDataFound = page.locator('[data-cy=ponds-list-no-data]');
    const createNewPondBtn = noDataFound.locator('[data-cy=create-new-pond-btn]');
    await expect(noDataFound).toBeVisible();
    await expect(createNewPondBtn).toBeVisible();

    await createNewPondBtn.click();
    await page.locator('[data-cy=add-edit-pond-modal]').waitFor({ state: 'visible' });
    await page.locator('[data-cy=add-edit-pond-modal] [data-cy=cancel-btn]').click();
    await page.locator('[data-cy=add-edit-pond-modal]').waitFor({ state: 'hidden' });

    return true;
  }

  list.v1ListPonds.ponds.forEach(async (ele) => {
    const { _id, name, eid, currentPopulationId } = ele;
    const pondEle = page.locator(`[data-cy=ponds-list-data] [data-cy='${_id}']`);
    const pondName = pondEle.locator('[data-cy=pond-name]');
    const pondSlug = `${eid}-${slugify(name)}`;

    await expect(pondEle).toBeVisible();
    await expect(pondName).toContainText(name, { ignoreCase: true });
    const pondBtn = page.locator(`[data-cy=ponds-list-data] [data-cy="${_id}"] a[data-cy="view-pond-btn"]`);
    await expect(pondBtn).toBeVisible();
    expect(await pondBtn.getAttribute('href')).toContain(`${pondSlug}`);

    if (!currentPopulationId) {
      const emptyPondElement = pondEle.locator('[data-cy=new-pond-empty-state]');
      const stockPondBtn = emptyPondElement.locator('[data-cy=stock-pond-btn]');
      const stockPondsModal = page.locator('[data-cy=stock-pond-modal]');

      await expect(emptyPondElement).toBeVisible();
      await expect(stockPondBtn).toBeVisible();

      await stockPondBtn.click();
      await stockPondsModal.waitFor({ state: 'visible' });
      await stockPondsModal.locator('[data-cy=cancel-btn]').click();
      await stockPondsModal.waitFor({ state: 'hidden' });
    }
  });
});

Then(
  'I should see farm view list matches with {string} {string}',
  async (pondsListKey: string, populationListKey: string) => {
    const pondsList = ctxData.getCtxValue(pondsListKey);
    const populationsList = ctxData.getCtxValue(populationListKey);
    const populationsHashmap = keyBy(populationsList.v1ListPopulations.populations, '_id');

    if (pondsList.length === 0) {
      const noDataFound = page.locator('[data-cy=ponds-list-no-data]');
      const createNewPondBtn = noDataFound.locator('[data-cy=create-new-pond-btn]');
      await expect(noDataFound).toBeVisible();
      await expect(createNewPondBtn).toBeVisible();

      await createNewPondBtn.click();
      await page.locator('[data-cy=add-edit-pond-modal]').waitFor({ state: 'visible' });
      await page.locator('[data-cy=add-edit-pond-modal] [data-cy=cancel-btn]').click();
      await page.locator('[data-cy=add-edit-pond-modal]').waitFor({ state: 'hidden' });

      return;
    }
    await page.locator('#farm-overview-table').waitFor({ state: 'visible' });
    const pondElements = page.locator('#farm-overview-table .ht_master table tbody tr');
    expect(await pondElements.count()).toBe(pondsList.v1ListPonds.totalRecords);

    pondsList.v1ListPonds.ponds.forEach(async (ele) => {
      const { _id, name, currentPopulationId, size } = ele;
      const population = populationsHashmap[currentPopulationId];
      const parent = pondElements.filter({ has: page.locator(`[data-cy='${_id}']`) });

      if (population?._id) {
        const { seedingAverageWeight } = population ?? {};
        const nameLocator = parent.locator('td >> nth=0');
        const hectaresLocator = parent.locator('td >> nth=1');
        const stockingLocator = parent.locator('td >> nth=2');

        await expect(nameLocator).toContainText(name, { ignoreCase: true });
        await expect(hectaresLocator).toContainText(formatNumber(size, { fractionDigits: 2 }).toString());
        await expect(stockingLocator).toContainText(
          formatNumber(seedingAverageWeight, { fractionDigits: 2 }).toString()
        );
      }
    });
  }
);

Then('I delete all files', async () => {
  const tableData = page.locator('[data-cy=farm-documents] [data-cy=list-table] [data-cy=table-data]');

  if ((await tableData.count()) > 0) {
    const elements = tableData.locator('tr');
    const elementsCount = await elements.count();

    for (let i = 0; i < elementsCount; i++) {
      const fileRow = tableData.locator(`tr >> nth=0`);
      await fileRow.locator('[data-cy=delete-btn]').click();
      await page.locator('[data-cy=delete-pond-file-modal]').waitFor({ state: 'visible' });
      await page.locator('[data-cy=delete-pond-file-modal] [data-cy=confirm-delete]').waitFor({ state: 'visible' });
      await page.locator('[data-cy=delete-pond-file-modal] [data-cy=confirm-delete]').click();

      await page
        .locator('[data-cy=farm-documents] [data-cy=list-table] [data-cy=table-data]')
        .waitFor({ state: 'visible' });
    }
  }
});
