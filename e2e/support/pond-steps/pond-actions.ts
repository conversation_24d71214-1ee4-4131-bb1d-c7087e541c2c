import { Then, When } from '@cucumber/cucumber';
import { getFakerValue } from '../../utils/get-faker-value';
import { getListPonds, getUserToken } from '../../utils/module-farm-apis';
import { GetApiResponse } from '../custom-commands/wait-for-api';

When('I get pond {string} data from farm {string} as {string}', async (pondName: string, id: string, key: string) => {
  const farmId = ctxData.getCtxValue(id) ?? id;
  const token = await getUserToken();
  const data = await getListPonds({ token, filter: { farmId: [farmId] } });
  const currenPond = data.ponds?.find((pond) => pond.name.toLowerCase() === pondName.toLowerCase());
  if (currenPond) {
    ctxData.setCtxData({ [key]: currenPond });
    return 'SUCCESS';
  }

  await page.click('[data-cy=create-new-pond-btn]');
  await page.waitForTimeout(500);

  const pondModal = page.locator('[data-cy=add-edit-pond-modal]');
  await pondModal.waitFor({ state: 'visible' });
  await pondModal.locator('input#name').type(getFakerValue('pondName'));
  await pondModal.locator('input#size').type(getFakerValue('number'));
  await pondModal.locator('#superVisorId').click();
  const option = pondModal.locator(`#superVisorId .react-select__option`);
  const elementCount = await option.count();
  if (elementCount > 0) {
    await option.first().click();
  } else {
    await pondModal.click();
  }

  const [createPondRes, listPondsRes] = await Promise.all([
    GetApiResponse('v1CreatePond'),
    GetApiResponse('v1ListPonds'),
    page.click('[data-cy=add-edit-pond-modal] button[type=submit]')
  ]);
  const pondData = listPondsRes?.v1ListPonds?.ponds?.find(
    (pond: any) => pond._id === createPondRes.v1CreatePond.pond._id
  );
  if (pondData) {
    ctxData.setCtxData({ [key]: pondData });
    return 'SUCCESS';
  }
});

When('I create a pond and save data as {string}', async function (key: string) {
  await page.click('[data-cy=create-new-pond-btn]');
  await page.waitForTimeout(500);

  const pondModal = page.locator('[data-cy=add-edit-pond-modal]');
  await pondModal.waitFor({ state: 'visible' });
  await pondModal.locator('input#name').type(getFakerValue('pondName'));
  await pondModal.locator('input#size').type(getFakerValue('number'));
  await pondModal.locator('#superVisorId').click();
  const option = pondModal.locator(`#superVisorId .react-select__option`);
  const elementCount = await option.count();
  if (elementCount > 0) {
    await option.first().click();
  } else {
    await pondModal.click();
  }

  const [createPondRes, listPondsRes] = await Promise.all([
    GetApiResponse('v1CreatePond'),
    GetApiResponse('v1ListPonds'),
    page.click('[data-cy=add-edit-pond-modal] button[type=submit]')
  ]);
  const pondData = listPondsRes?.v1ListPonds?.ponds?.find(
    (pond: any) => pond._id === createPondRes.v1CreatePond.pond._id
  );
  ctxData.setCtxData({ [key]: pondData });
});

When('I create a pond by trigger {string} and save data as {string}', async function (locator: string, key: string) {
  await page.click(locator);

  const pondModal = page.locator('[data-cy=add-edit-pond-modal]');
  await pondModal.waitFor({ state: 'visible' });
  await pondModal.locator('input#name').type(getFakerValue('pondName'));
  await pondModal.locator('input#size').type(getFakerValue('number'));
  await pondModal.locator('#superVisorId').click();
  const option = pondModal.locator(`#superVisorId .react-select__option`);
  const elementCount = await option.count();
  if (elementCount > 0) {
    await option.first().click();
  } else {
    await pondModal.click();
  }

  const [createPondRes, listPondsRes] = await Promise.all([
    GetApiResponse('v1CreatePond'),
    GetApiResponse('v1ListPonds'),
    page.click('[data-cy=add-edit-pond-modal] button[type=submit]')
  ]);
  const pondData = listPondsRes?.v1ListPonds?.ponds?.find(
    (pond: any) => pond._id === createPondRes.v1CreatePond.pond._id
  );
  ctxData.setCtxData({ [key]: pondData });
});

/**
 *  @description harvest pond then archive it,
 * This step work on ui level, so you have to be in the pond details page and no modals are open to use it
 *
 */
Then('I harvest and archive the pond', async () => {
  await (await page.$('[data-cy=pond-view-actions]')).click();
  await (await page.$('[data-cy=harvest-pond-btn]')).click();
  const harvestModal = page.locator('[data-cy=harvest-population-modal]');
  await harvestModal.waitFor({ state: 'visible' });
  await harvestModal.locator('input#lbsHarvested').fill(getFakerValue('number'));
  await harvestModal.locator('input#weight').fill(getFakerValue('number'));
  await (await page.$('[data-cy=harvest-population-modal] button[type=submit]')).click();
  await page.locator('[data-cy=harvest-population-modal]').waitFor({ state: 'hidden' });
  await (await page.$('[data-cy=pond-view-actions] [data-cy=action-menu-button]')).click();
  await (await page.$('[data-cy=archive-pond-btn]')).click();
  await page.locator('[data-cy=archive-pond-modal]').waitFor({ state: 'visible' });
  await (await page.$('[data-cy=archive-pond-modal] [data-cy=archive-pond-submit-btn]')).click();
  await page.locator('[data-cy=archive-pond-modal]').waitFor({ state: 'hidden' });
});

Then('I archive the pond', async function () {
  const actionsElement = page.locator('[data-cy=pond-view-actions]');
  await actionsElement.click();
  await actionsElement.locator('[data-cy=archive-pond-btn]').click();

  await page.locator('[data-cy=archive-pond-modal]').waitFor({ state: 'visible' });
  await page.locator('[data-cy=archive-pond-modal] [data-cy=archive-pond-submit-btn]').click();

  await page.locator('[data-cy=archive-pond-modal]').waitFor({ state: 'hidden' });
});
