import { When } from '@cucumber/cucumber';
import { expect } from 'playwright/test';

When('I see weight table data based on pondsList {string}', async function (storeKey: string) {
  const pondsList = ctxData.getCtxValue(storeKey)?.v1ListPonds?.ponds;

  if (!pondsList?.length) return;

  const pondListRows = page.locator('[data-cy=ponds-weight-data] > div');

  expect(await pondListRows.count()).toBe(pondsList.length);

  pondsList.forEach(async (pond) => {
    const pondLocator = page.locator(`[data-cy=pond-${pond._id}]`);
    const pondNameLocator = pondLocator.locator('[data-cy=pond-name]');

    await pondLocator.waitFor({ state: 'visible' });
    expect(await pondNameLocator.textContent()).toContain(pond.name);

    await pondLocator.locator('[data-cy=pond-average-weight]').waitFor({ state: 'visible' });

    // average weight cell should have on of three states
    let foundOneElement = false;

    for (const selector of ['pond-has-no-population', 'pond-has-no-monitoring', 'pond-average-weight-value']) {
      const eleCount = await pondLocator.locator(`[data-cy=${selector}]`).count();

      if (eleCount === 1) {
        foundOneElement = true;
        break;
      }
    }
    expect(foundOneElement).toBe(true);

    const currentPopulationId = pond.currentPopulationId;
    if (!currentPopulationId) {
      await pondLocator.locator(`[data-cy=pond-has-no-population]`).waitFor({ state: 'visible' });
      await pondLocator.locator(`[data-cy=pond-has-no-monitoring]`).waitFor({ state: 'hidden' });
      await pondLocator.locator(`[data-cy=pond-average-weight-value]`).waitFor({ state: 'hidden' });
    } else {
      await pondLocator.locator(`[data-cy=pond-has-no-population]`).waitFor({ state: 'hidden' });
    }
  });

  //   Test input
  await page.locator('[data-cy=edit-weight-data-btn]').click();
  await page.locator('[data-cy=weight-data-actions] [data-cy=save-btn]').waitFor({ state: 'visible' });

  const inputLocator = page.locator('[data-cy=ponds-weight-data] [data-cy=pond-edit-manual-weight-input]');
  if ((await inputLocator.count()) > 0) {
    const testInput = inputLocator.first().locator('input');
    await page.locator('[data-cy=average-weight-error]').waitFor({ state: 'hidden' });

    await testInput.fill('400');
    await testInput.blur();

    await page.locator('[data-cy=average-weight-error]').waitFor({ state: 'visible' });
    await page.locator('[data-cy=revert-to-kampi-weight_btn]').waitFor({ state: 'visible' });

    await page.locator('[data-cy=revert-to-kampi-weight_btn]').click();
    await page.locator('[data-cy=average-weight-error]').waitFor({ state: 'hidden' });

    await page.locator('[data-cy=weight-data-actions] [data-cy=cancel-btn]').click();
    await page.locator('[data-cy=weight-data-actions] [data-cy=save-btn]').waitFor({ state: 'hidden' });
    await page.locator('[data-cy=edit-weight-data-btn]').waitFor({ state: 'visible' });
  }
});
