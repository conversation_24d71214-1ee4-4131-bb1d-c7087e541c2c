import { When } from '@cucumber/cucumber';
import { expect } from 'playwright/test';

When('I should see ponds rendered correctly on the survival table from {string}', async function (storeKey: string) {
  const pondsList = ctxData.getCtxValue(storeKey)?.v1ListPonds?.ponds;

  if (!pondsList?.length) return;

  const tableRows = page.locator(`.ht_master .htCore tbody tr`);
  expect(await tableRows.count()).toBe(pondsList.length);

  pondsList.forEach(async (pond) => {
    const ffff = tableRows.locator('td:first-child', { hasText: pond.name });
    expect(await ffff.count()).toBe(1);
  });

  const rowsArray = await tableRows.all();

  rowsArray.forEach(async (row) => {
    const dimmedCells = row.locator('td[role=gridcell].htDimmed');
    const cellsCount = await dimmedCells.count();

    expect(cellsCount).toBe(2);
  });
});

When('I should be able to edit  in survival data updater', async function () {
  const row = page.locator('.ht_master tr', { has: page.locator('td[role=gridcell].htEdit') }).first();
  const pondNameElement = row.locator('td:first-child');
  const survivalElement = row.locator('td:last-child');

  const saveBtnElement = page.locator('[data-cy=survival-data-actions] [data-cy=save-btn]');
  const cancelBtnElement = page.locator('[data-cy=survival-data-actions] [data-cy=cancel-btn]');

  const hasEditableRow = (await row.count()) > 0;

  if (!hasEditableRow) return;

  // Start editing
  await (await page.$('[data-cy=edit-survival-data-btn]')).click();
  await saveBtnElement.waitFor({ state: 'visible' });

  // On edit survival cell should be editable
  expect(await survivalElement.getAttribute('class')).not.toContain('htDimmed');
  expect(await pondNameElement.getAttribute('class')).toContain('htDimmed');

  await survivalElement.click();
  await page.keyboard.type('700');
  await row.locator('td:last-child.htInvalid').waitFor({ state: 'hidden' });

  // Validate the updated values are actually exist
  await pondNameElement.click();
  expect(await survivalElement.textContent()).toContain('700');

  //   Validate that error shown wheen wee write value greater than 100
  await row.locator('td:last-child.htInvalid').waitFor({ state: 'visible' });

  //   Validate that click save button doesn't trigger API
  await saveBtnElement.click();
  await row.locator('td:last-child.htInvalid').waitFor({ state: 'visible' });

  // Cancel editing
  await cancelBtnElement.click();
  await row.locator('td:last-child.htInvalid').waitFor({ state: 'hidden' });
  await saveBtnElement.waitFor({ state: 'hidden' });
  await cancelBtnElement.waitFor({ state: 'hidden' });
});
