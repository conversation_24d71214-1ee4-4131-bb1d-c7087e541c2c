import { When } from '@cucumber/cucumber';
import { expect } from 'playwright/test';
import { FarmDailyParameters } from '@xpertsea/module-farm-sdk';

When('I fill data in daily parameters for editable pond from farm {string}', async function (storeKey: string) {
  const currentFarm = ctxData.getCtxValue('CURRENT_TESTING_FARM');
  const farmsList = ctxData.getCtxValue(storeKey)?.v1ListFarms?.farms;

  if (!currentFarm || !farmsList?.length) return;

  const farmData = farmsList.find((f) => f._id === currentFarm._id);
  const dailyParameters: FarmDailyParameters[] = farmData.dailyParameters?.filter(
    (ele: FarmDailyParameters) => ele.status !== 'inactive'
  );

  const tableRows = await page.$$(`.ht_master .htCore thead th`);
  expect(tableRows).toHaveLength(dailyParameters.length + 1);

  for (let i = 0; i < dailyParameters.length; i++) {
    const cc = await tableRows[i + 1].textContent();
    expect(cc).toContain(dailyParameters[i].name);
  }

  const row = page.locator('.ht_master tr', { has: page.locator('td[role=gridcell].htEdit') }).first();
  const pondNameElement = row.locator('td:first-child');
  const dailyParametersElements = row.locator('td:not(:first-child)');

  const hasEditableRow = (await row.count()) > 0;

  //   Clean up the row
  if (hasEditableRow) {
    await (await page.$('[data-cy=edit-daily-params-btn]')).click();
    await page.locator('[data-cy=daily-params-actions]').waitFor({ state: 'visible' });

    await dailyParametersElements.all().then(async (val) => {
      for (let i = 0; i < val.length; i++) {
        await val[i].click();
        await page.keyboard.press('Backspace');
      }
    });

    await page.click('[data-cy=daily-params-actions] [data-cy=save-btn]');

    await page.locator('[data-cy=daily-param-table]').waitFor({ state: 'hidden' });
    await page.locator('[data-cy=data-updater-loader]').waitFor({ state: 'visible' });
    await page.locator('[data-cy=daily-param-table]').waitFor({ state: 'visible' });
  }

  if (hasEditableRow) {
    //
    let DailyParametersOriginalValues = [];
    for (const ele of await dailyParametersElements.all()) {
      expect(await ele.getAttribute('class')).toContain('htDimmed');
      DailyParametersOriginalValues.push(await ele.textContent());
    }
    expect(await pondNameElement.getAttribute('class')).toContain('htDimmed');

    // Start editing
    await (await page.$('[data-cy=edit-daily-params-btn]')).click();
    await page.locator('[data-cy=daily-params-actions]').waitFor({ state: 'visible' });

    // On edit all daily parameters cells should be editable
    for (const ele of await dailyParametersElements.all()) {
      expect(await ele.getAttribute('class')).not.toContain('htDimmed');
    }
    expect(await pondNameElement.getAttribute('class')).toContain('htDimmed');

    // update values of first daily parameters
    await dailyParametersElements.first().click();
    await page.keyboard.type('70');

    // Validate the updated values are actually exist
    await pondNameElement.first().click();
    expect(await dailyParametersElements.first().textContent()).toContain('70');

    // Cancel editing
    await page.click('[data-cy=daily-params-actions] [data-cy=cancel-btn]');
    await page.locator('[data-cy=daily-params-actions]').waitFor({ state: 'hidden' });

    // After cancel pressed, all values should be reset to original values
    expect(await dailyParametersElements.first().textContent()).toBe(DailyParametersOriginalValues[0]);
  }

  const generateNumber = (i: number) => `${70 * (i + 1)}`;

  if (hasEditableRow) {
    await (await page.$('[data-cy=edit-daily-params-btn]')).click();
    await page.locator('[data-cy=daily-params-actions]').waitFor({ state: 'visible' });

    await dailyParametersElements.all().then(async (val) => {
      for (let i = 0; i < val.length; i++) {
        await val[i].click();
        await page.keyboard.type(generateNumber(i));
      }
    });

    await page.click('[data-cy=daily-params-actions] [data-cy=save-btn]');

    await page.locator('[data-cy=daily-param-table]').waitFor({ state: 'hidden' });
    await page.locator('[data-cy=data-updater-loader]').waitFor({ state: 'visible' });
    await page.locator('[data-cy=daily-param-table]').waitFor({ state: 'visible' });

    await dailyParametersElements.all().then(async (val) => {
      for (let i = 0; i < val.length; i++) {
        expect(await val[i].textContent()).toContain(generateNumber(i));
      }
    });

    // Clean up data
    await (await page.$('[data-cy=edit-daily-params-btn]')).click();
    await page.locator('[data-cy=daily-params-actions]').waitFor({ state: 'visible' });

    await dailyParametersElements.all().then(async (val) => {
      for (let i = 0; i < val.length; i++) {
        await val[i].click();
        await page.keyboard.press('Backspace');
      }
    });

    await page.click('[data-cy=daily-params-actions] [data-cy=save-btn]');

    await page.locator('[data-cy=daily-param-table]').waitFor({ state: 'hidden' });
    await page.locator('[data-cy=data-updater-loader]').waitFor({ state: 'visible' });
    await page.locator('[data-cy=daily-param-table]').waitFor({ state: 'visible' });
  }
});
