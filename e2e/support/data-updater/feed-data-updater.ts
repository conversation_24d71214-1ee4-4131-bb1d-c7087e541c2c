import { When } from '@cucumber/cucumber';
import { expect } from 'playwright/test';
import { FarmFeedTypes } from '@xpertsea/module-farm-sdk';

When('I fill data for editable pond from farm {string}', async function (storeKey: string) {
  const currentFarm = ctxData.getCtxValue('CURRENT_TESTING_FARM');
  const farmsList = ctxData.getCtxValue(storeKey)?.v1ListFarms?.farms;

  if (!currentFarm || !farmsList?.length) return;

  const farmData = farmsList.find((f: FarmFeedTypes) => f._id === currentFarm._id);
  const feedTypes: FarmFeedTypes[] = farmData.feedTypes?.filter((ele: FarmFeedTypes) => ele.status === 'active');

  const tableRows = await page.$$(`.ht_master .htCore thead th`);
  expect(tableRows).toHaveLength(feedTypes.length + 2);

  feedTypes.forEach(async (f, i: number) => {
    expect(await tableRows[i + 1].textContent()).toContain(f.feedType);
  });

  const row = page.locator('.ht_master tr', { has: page.locator('td[role=gridcell].htEdit') }).first();
  const pondNameElement = row.locator('td:first-child');
  const feedDataElements = row.locator('td:not(:first-child):not(:last-child)');
  const otherDirectCostElement = row.locator('td:last-child');

  const hasEditableRow = (await row.count()) > 0;

  //   Clean up the row
  if (hasEditableRow) {
    await (await page.$('[data-cy=edit-weekly-data]')).click();
    await page.locator('[data-cy=weekly-data-table-actions]').waitFor({ state: 'visible' });

    await otherDirectCostElement.click();
    await page.keyboard.press('Backspace');
    await feedDataElements.all().then(async (val) => {
      for (let i = 0; i < val.length; i++) {
        await val[i].click();
        await page.keyboard.press('Backspace');
      }
    });

    await page.click('[data-cy=weekly-data-table-actions] [data-cy=save-btn]');

    await page.locator('[data-cy=weekly-data-table]').waitFor({ state: 'hidden' });
    await page.locator('[data-cy=data-updater-loader]').waitFor({ state: 'visible' });
    await page.locator('[data-cy=weekly-data-table]').waitFor({ state: 'visible' });
  }

  if (hasEditableRow) {
    const otherDirectCostOriginalValue = await otherDirectCostElement.textContent();
    let feedDataOriginalValues = [];

    for (const ele of await feedDataElements.all()) {
      expect(await ele.getAttribute('class')).toContain('htDimmed');
      feedDataOriginalValues.push(await ele.textContent());
    }
    expect(await otherDirectCostElement.getAttribute('class')).toContain('htDimmed');

    // Start editing
    await (await page.$('[data-cy=edit-weekly-data]')).click();
    await page.locator('[data-cy=weekly-data-table-actions]').waitFor({ state: 'visible' });

    // On edit all feed data cells and other direct cost cell should be editable
    expect(await otherDirectCostElement.getAttribute('class')).not.toContain('htDimmed');
    for (const ele of await feedDataElements.all()) {
      expect(await ele.getAttribute('class')).not.toContain('htDimmed');
    }
    expect(await pondNameElement.getAttribute('class')).toContain('htDimmed');

    // update values of feed type and other direct cost
    await otherDirectCostElement.click();
    await page.keyboard.type('700');

    await feedDataElements.first().click();
    await page.keyboard.type('800');

    // Validate the updated values are actually exist
    await pondNameElement.first().click();
    expect(await otherDirectCostElement.textContent()).toContain('700');
    expect(await feedDataElements.first().textContent()).toContain('800');

    // Cancel editing
    await page.click('[data-cy=weekly-data-table-actions] [data-cy=cancel-btn]');
    await page.locator('[data-cy=weekly-data-table-actions]').waitFor({ state: 'hidden' });

    // After cancel pressed, all values should be reset to original values
    expect(await otherDirectCostElement.textContent()).toBe(otherDirectCostOriginalValue);
    expect(await feedDataElements.first().textContent()).toBe(feedDataOriginalValues[0]);
  }

  //   Helpers
  const getExactNumber = (val: string) => val.match(/\d/g).join('');
  const generateNumber = (i: number) => `${70 * (i + 1)}`;

  if (hasEditableRow) {
    await (await page.$('[data-cy=edit-weekly-data]')).click();
    await page.locator('[data-cy=weekly-data-table-actions]').waitFor({ state: 'visible' });

    await otherDirectCostElement.click();
    await page.keyboard.type('700');

    await feedDataElements.all().then(async (val) => {
      for (let i = 0; i < val.length; i++) {
        await val[i].click();
        await page.keyboard.type(generateNumber(i));
      }
    });

    await page.click('[data-cy=weekly-data-table-actions] [data-cy=save-btn]');

    await page.locator('[data-cy=weekly-data-table]').waitFor({ state: 'hidden' });
    await page.locator('[data-cy=data-updater-loader]').waitFor({ state: 'visible' });
    await page.locator('[data-cy=weekly-data-table]').waitFor({ state: 'visible' });

    expect(await otherDirectCostElement.textContent()).toContain('700');
    await feedDataElements.all().then(async (val) => {
      for (let i = 0; i < val.length; i++) {
        expect(getExactNumber(await val[i].textContent())).toBe(generateNumber(i));
      }
    });

    await page.waitForTimeout(300);

    // Clean up data
    await (await page.$('[data-cy=edit-weekly-data]')).click();
    await page.locator('[data-cy=weekly-data-table-actions]').waitFor({ state: 'visible' });

    await otherDirectCostElement.click();
    await page.keyboard.press('Backspace');
    await feedDataElements.all().then(async (val) => {
      for (let i = 0; i < val.length; i++) {
        await val[i].click();
        await page.keyboard.press('Backspace');
      }
    });

    await page.click('[data-cy=weekly-data-table-actions] [data-cy=save-btn]');

    await page.locator('[data-cy=weekly-data-table]').waitFor({ state: 'hidden' });
    await page.locator('[data-cy=data-updater-loader]').waitFor({ state: 'visible' });
    await page.locator('[data-cy=weekly-data-table]').waitFor({ state: 'visible' });
  }
});

When('I fill data for daily feed editable pond from farm {string}', async function (storeKey: string) {
  const currentFarm = ctxData.getCtxValue('CURRENT_TESTING_FARM');
  const farmsList = ctxData.getCtxValue(storeKey)?.v1ListFarms?.farms;

  if (!currentFarm || !farmsList?.length) return;

  const farmData = farmsList.find((f: FarmFeedTypes) => f._id === currentFarm._id);
  const feedTypes: FarmFeedTypes[] = farmData.feedTypes?.filter((ele: FarmFeedTypes) => ele.status === 'active');

  const tableRows = await page.$$(`.ht_master .htCore thead th`);

  for (let i = 0; i < feedTypes.length; i++) {
    const f = feedTypes[i];
    const tablerowTextContent = await tableRows[i + 1].textContent();
    expect(tablerowTextContent).toContain(f.feedType);
  }

  const row = page.locator('.ht_master tr', { has: page.locator('td[role=gridcell].htEdit') }).first();
  const pondNameElement = row.locator('td:first-child');
  const feedDataElements = row.locator('td:not(:first-child):not(:nth-last-child(-n+2))');

  const otherDirectCostElement = row.locator('td:last-child');

  const hasEditableRow = (await row.count()) > 0;

  if (hasEditableRow) {
    await (await page.$('[data-cy=edit-daily-data-btn]')).click();
    await page.locator('[data-cy=daily-data-table-actions]').waitFor({ state: 'visible' });

    await otherDirectCostElement.click();
    await page.keyboard.press('Backspace');
    await feedDataElements.all().then(async (val) => {
      for (let i = 0; i < val.length; i++) {
        await val[i].click();
        await page.keyboard.press('Backspace');
      }
    });

    await page.click('[data-cy=daily-data-table-actions] [data-cy=save-btn]');

    await page.locator('[data-cy=daily-data-table]').waitFor({ state: 'hidden' });
    await page.locator('[data-cy=data-updater-loader]').waitFor({ state: 'visible' });
    await page.locator('[data-cy=daily-data-table]').waitFor({ state: 'visible' });
  }

  if (hasEditableRow) {
    const otherDirectCostOriginalValue = await otherDirectCostElement.textContent();
    let feedDataOriginalValues = [];

    for (const ele of await feedDataElements.all()) {
      expect(await ele.getAttribute('class')).toContain('htDimmed');
      feedDataOriginalValues.push(await ele.textContent());
    }
    expect(await otherDirectCostElement.getAttribute('class')).toContain('htDimmed');

    // Start editing
    await (await page.$('[data-cy=edit-daily-data-btn]')).click();
    await page.locator('[data-cy=daily-data-table-actions]').waitFor({ state: 'visible' });

    // On edit all feed data cells and other direct cost cell should be editable
    expect(await otherDirectCostElement.getAttribute('class')).not.toContain('htDimmed');
    for (const ele of await feedDataElements.all()) {
      expect(await ele.getAttribute('class')).not.toContain('htDimmed');
    }
    expect(await pondNameElement.getAttribute('class')).toContain('htDimmed');

    // update values of feed type and other direct cost
    await otherDirectCostElement.click();
    await page.keyboard.type('700');

    await feedDataElements.first().click();
    await page.keyboard.type('800');

    // Validate the updated values are actually exist
    await pondNameElement.first().click();
    expect(await otherDirectCostElement.textContent()).toContain('700');
    expect(await feedDataElements.first().textContent()).toContain('800');

    // Cancel editing
    await page.click('[data-cy=daily-data-table-actions] [data-cy=cancel-btn]');
    await page.locator('[data-cy=daily-data-table-actions]').waitFor({ state: 'hidden' });

    // After cancel pressed, all values should be reset to original values
    expect(await otherDirectCostElement.textContent()).toBe(otherDirectCostOriginalValue);
    expect(await feedDataElements.first().textContent()).toBe(feedDataOriginalValues[0]);
  }

  //   Helpers
  const getExactNumber = (val: string) => val.match(/\d/g).join('');
  const generateNumber = (i: number) => `${70 * (i + 1)}`;

  if (hasEditableRow) {
    await (await page.$('[data-cy=edit-daily-data-btn]')).click();
    await page.locator('[data-cy=daily-data-table-actions]').waitFor({ state: 'visible' });

    await otherDirectCostElement.click();
    await page.keyboard.type('700');

    await feedDataElements.all().then(async (val) => {
      for (let i = 0; i < val.length; i++) {
        await val[i].click();
        await page.keyboard.type(generateNumber(i));
      }
    });

    await page.click('[data-cy=daily-data-table-actions] [data-cy=save-btn]');

    await page.locator('[data-cy=daily-data-table]').waitFor({ state: 'hidden' });
    await page.locator('[data-cy=data-updater-loader]').waitFor({ state: 'visible' });
    await page.locator('[data-cy=daily-data-table]').waitFor({ state: 'visible' });

    expect(await otherDirectCostElement.textContent()).toContain('700');

    await feedDataElements.all().then(async (val) => {
      for (let i = 0; i < val.length; i++) {
        expect(getExactNumber(await val[i].textContent())).toBe(generateNumber(i));
      }
    });

    await page.waitForTimeout(300);

    // Clean up data
    await (await page.$('[data-cy=edit-daily-data-btn]')).click();
    await page.locator('[data-cy=daily-data-table-actions]').waitFor({ state: 'visible' });

    await otherDirectCostElement.click();
    await page.keyboard.press('Backspace');
    await feedDataElements.all().then(async (val) => {
      for (let i = 0; i < val.length; i++) {
        await val[i].click();
        await page.keyboard.press('Backspace');
      }
    });

    await page.click('[data-cy=daily-data-table-actions] [data-cy=save-btn]');

    await page.locator('[data-cy=daily-data-table]').waitFor({ state: 'hidden' });
    await page.locator('[data-cy=data-updater-loader]').waitFor({ state: 'visible' });
    await page.locator('[data-cy=daily-data-table]').waitFor({ state: 'visible' });
  }
});
