import { When } from '@cucumber/cucumber';
import { expect } from 'playwright/test';
import { FarmFeedTypes } from '@xpertsea/module-farm-sdk';

When('I fill data for editable nurseries from {string}', async function (storeKey: string) {
  const currentFarm = ctxData.getCtxValue('CURRENT_TESTING_FARM');
  const farmsList = ctxData.getCtxValue(storeKey)?.v1ListFarms?.farms;

  if (!currentFarm || !farmsList?.length) return;

  const getExactNumber = (val: string) => val.match(/\d/g).join('');
  const generateNumber = (i: number) => `${70 * (i + 1)}`;

  const farmData = farmsList.find((f: FarmFeedTypes) => f._id === currentFarm._id);
  const feedTypes: FarmFeedTypes[] = farmData.feedTypes?.filter((ele: FarmFeedTypes) => ele.status === 'active');

  const tableRows = await page.$$(`.ht_master .htCore thead th`);
  expect(tableRows).toHaveLength(feedTypes.length + 2);

  for (let i = 0; i < feedTypes.length; i++) {
    const f = feedTypes[i];
    const tablerowTextContent = await tableRows[i + 1].textContent();
    expect(tablerowTextContent).toContain(f.feedType);
  }

  const tableActionLocator = page.locator('[data-cy=nursery-feed-data-table-actions]');
  const row = page.locator('.ht_master tr', { has: page.locator('td[role=gridcell].htEdit') }).first();
  const pondNameElement = row.locator('td:first-child');
  const feedDataElements = row.locator('td:not(:first-child):not(:last-child)');
  const otherDirectCostElement = row.locator('td:last-child');

  const hasEditableRow = (await row.count()) > 0;

  if (hasEditableRow) {
    const otherDirectCostOriginalValue = await otherDirectCostElement.textContent();
    let feedDataOriginalValues = [];

    for (const ele of await feedDataElements.all()) {
      expect(await ele.getAttribute('class')).toContain('htDimmed');
      feedDataOriginalValues.push(await ele.textContent());
    }
    expect(await otherDirectCostElement.getAttribute('class')).toContain('htDimmed');

    // Start editing
    await (await page.$('[data-cy=edit-nursery-feed-data]')).click();
    await tableActionLocator.waitFor({ state: 'visible' });

    // On edit all feed data cells and other direct cost cell should be editable
    expect(await otherDirectCostElement.getAttribute('class')).not.toContain('htDimmed');
    for (const ele of await feedDataElements.all()) {
      expect(await ele.getAttribute('class')).not.toContain('htDimmed');
    }
    expect(await pondNameElement.getAttribute('class')).toContain('htDimmed');

    // update values of feed type and other direct cost
    await otherDirectCostElement.click();
    await page.keyboard.type('700');

    await feedDataElements.first().click();
    await page.keyboard.type('800');

    // Validate the updated values are actually exist
    await pondNameElement.first().click();
    expect(await otherDirectCostElement.textContent()).toContain('700');
    expect(await feedDataElements.first().textContent()).toContain('800');

    // Cancel editing
    await tableActionLocator.locator('[data-cy=cancel-btn]').click();
    await tableActionLocator.waitFor({ state: 'hidden' });

    // After cancel pressed, all values should be reset to original values
    expect(await otherDirectCostElement.textContent()).toBe(otherDirectCostOriginalValue);
    expect(await feedDataElements.first().textContent()).toBe(feedDataOriginalValues[0]);
  }

  if (hasEditableRow) {
    await (await page.$('[data-cy=edit-nursery-feed-data]')).click();
    await tableActionLocator.waitFor({ state: 'visible' });

    await otherDirectCostElement.click();
    await page.keyboard.type('700');

    await feedDataElements.all().then(async (val) => {
      for (let i = 0; i < val.length; i++) {
        await val[i].click();
        await page.keyboard.type(generateNumber(i));
      }
    });

    await tableActionLocator.locator('[data-cy=save-btn]').click();

    await tableActionLocator.locator('[data-cy=save-btn]').waitFor({ state: 'hidden' });
    await page.locator('[data-cy=edit-nursery-feed-data]').waitFor({ state: 'visible' });

    expect(await otherDirectCostElement.textContent()).toContain('700');
    await feedDataElements.all().then(async (val) => {
      for (let i = 0; i < val.length; i++) {
        expect(getExactNumber(await val[i].textContent())).toBe(generateNumber(i));
      }
    });

    await page.waitForTimeout(300);

    // Clean up data
    await (await page.$('[data-cy=edit-nursery-feed-data]')).click();
    await tableActionLocator.waitFor({ state: 'visible' });

    await otherDirectCostElement.click();
    await page.keyboard.press('Backspace');
    await feedDataElements.all().then(async (val) => {
      for (let i = 0; i < val.length; i++) {
        await val[i].click();
        await page.keyboard.press('Backspace');
      }
    });

    await tableActionLocator.locator('[data-cy=save-btn]').click();

    await tableActionLocator.locator('[data-cy=save-btn]').waitFor({ state: 'hidden' });
    await page.locator('[data-cy=edit-nursery-feed-data]').waitFor({ state: 'visible' });
  }
});
