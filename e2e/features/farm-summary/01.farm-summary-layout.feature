@isExperiment=2025-2-25
Feature: Farm Summary layout

    Background:
        Given I am logged in as an admin
        When I go to current farm page


    Scenario: verify page layout
        When Element '[data-cy=ponds-list-data]' is visible

        Then Elements visibility on the page match the following
            | selector                                     | state   |
            | [data-cy=farm-selector-btn]                  | visible |
            | [data-cy=production-ponds-list]              | visible |
            | [data-cy=production-ponds-options-container] | visible |

        And Elements visibility on the page match the following
            | selector                     | state   |
            | [data-cy=ponds-list-filters] | visible |
            | [data-cy=ponds-filters-form] | hidden  |

        When I press '[data-cy=ponds-list-filters]'
        Then I should see element '[data-cy=ponds-filters-form]'
        And I should see element '[data-cy=cancel-btn]'
        When I press '[data-cy=cancel-btn]'
        Then I should not see element '[data-cy=ponds-filters-form]'

        Then Elements visibility on the page match the following
            | selector                                                      | state   |
            | [data-cy=summary-drawer-desktop] [data-cy=summary-drawer-btn] | visible |
            | [data-cy=summary-drawer-body]                                 | hidden  |

        When I press '[data-cy=summary-drawer-desktop] [data-cy=summary-drawer-btn]'
        Then I should see element '[data-cy=summary-drawer-body]'
        And wait for 1 seconds
        When I press '[data-cy=summary-drawer-body] [data-cy=close-btn]'
        Then I should not see element '[data-cy=summary-drawer-body]'

