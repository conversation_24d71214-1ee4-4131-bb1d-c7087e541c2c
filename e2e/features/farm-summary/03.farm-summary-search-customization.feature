@isExperiment=2025-2-25
Feature: Farm Summary Search and list customization

    Background:
        Given I am logged in as an admin
        When I go to current farm page
        And I save 'v1GetAccountDetails' api response as 'ACCOUNT_DETAILS'


    Scenario: verify search, list and grid view
        When Element '[data-cy=ponds-list-data]' is visible
        Then I should see element '[data-cy=production-ponds-options-container]'
        And Elements visibility on the page match the following
            | selector                                        | state   |
            | [data-cy=production-ponds-search]               | visible |
            | [data-cy=production-insight-indicator-selector] | visible |
            | [data-cy=pond-view-selector]                    | visible |
            | [data-cy=list-view-btn]                         | visible |
            | [data-cy=grid-view-btn]                         | visible |
            | [data-cy=ponds-list-grid-view]                  | hidden  |
            | [data-cy=ponds-list-list-view]                  | visible |
            | [data-cy=ponds-list-no-data]                    | hidden  |

        When I press '[data-cy=grid-view-btn]'
        Then I should see element '[data-cy=ponds-list-grid-view]'
        And I should not see element '[data-cy=ponds-list-list-view]'
        When I fill in '[data-cy=production-ponds-search]' with 'nonsensedata__nonsensedata'
        Then I should see element '[data-cy=ponds-list-no-data]'
        And I should not see element '[data-cy=ponds-list-grid-view]'

        When I clear '[data-cy=production-ponds-search]'
        Then I should not see element '[data-cy=ponds-list-no-data]'
        And I should see element '[data-cy=ponds-list-grid-view]'

        When I press '[data-cy=list-view-btn]'
        And I should not see element '[data-cy=ponds-list-grid-view]'
        And I should see element '[data-cy=ponds-list-list-view]'

        When I press '[data-cy=production-insight-indicator-selector]'
        Then Elements visibility on the page match the following
            | selector                             | state   |
            | [data-cy=menuItem-profitPerHaPerDay] | visible |
            | [data-cy=menuItem-costPerPound]      | visible |
            | [data-cy=menuItem-averageWeight]     | visible |
            | [data-cy=menuItem-growth1WeekAvg]    | visible |
            | [data-cy=menuItem-growthLinear]      | visible |
            | [data-cy=menuItem-survival]          | visible |
            | [data-cy=menuItem-fcrCumulative]     | visible |


    Scenario: verify customize view default values, select and unselect variable
        When Element '[data-cy=ponds-list-data]' is visible
        Then I press '[data-cy=more-options-menu-btn]'
        Then I should see element '[data-cy=customize-view-btn]'
        Then I should see element '[data-cy=create-new-pond-btn]'

        When I press '[data-cy=customize-view-btn]'
        Then I should see element '[data-cy=customize-view-modal]'
        Then I should see element '[data-cy=customize-view-variables-list]'
        Then I should see element '[data-cy=variables-search-input]'

        And Elements visibility on the "[data-cy=customize-view-modal]" match the following
            | selector                                    | state   |
            | [data-cy=customize-view-variables-list]     | visible |
            | [data-cy=variables-search-input]            | visible |
            | input#pondSize                              | visible |
            | input#stockingDate                          | visible |
            | input#animalsStockedHa                      | visible |
            | input#partialHarvestLbsHa                   | visible |
            | input#lastPartialHarvestDate                | visible |
            | input#plannedHarvestDate                    | visible |
            | input#numberOfPartialHarvests               | visible |
            | input#growthLinear                          | visible |
            | input#growthDaily                           | visible |
            | input#growth1WeekAvg                        | visible |
            | input#growth2WeekAvg                        | visible |
            | input#growth3WeekAvg                        | visible |
            | input#growth4w                              | visible |
            | input#fcrCumulative                         | visible |
            | input#fcrWeekly                             | visible |
            | input#adjustedFcr                           | visible |
            | input#kgPerHaPerDayGivenKg                  | visible |
            | input#weeklyFeedGivenKg                     | visible |
            | input#totalFeedGivenKg                      | visible |
            | input#totalFeedGivenLbs                     | visible |
            | input#biomassPercentage                     | visible |
            | input#survival                              | visible |
            | input#survivalFeed                          | visible |
            | input#survivalWithPartialHarvest            | visible |
            | input#biomassLb                             | visible |
            | input#biomassLbTotal                        | visible |
            | input#biomassLbHa                           | visible |
            | input#totalBiomassLbHa                      | visible |
            | input#animalsRemainingM2                    | visible |
            | input#animalsRemainingHa                    | visible |
            | input#dispersion                            | visible |
            | input#symmetry                              | visible |
            | input#profitPerHaPerDay                     | visible |
            | input#profitPerPound                        | visible |
            | input#totalProfit                           | visible |
            | input#revenuePerPound                       | visible |
            | input#totalRevenuePound                     | visible |
            | input#totalRevenue                          | visible |
            | input#totalCosts                            | visible |
            | input#stockingCosts                         | visible |
            | input#stockingCostsMillar                   | visible |
            | input#cumulativeOverheadCosts               | visible |
            | input#cumulativeFeedCosts                   | visible |
            | input#costPerPound                          | visible |
            | input#costPoundHarvest                      | visible |
            | input#feedCostPerKg                         | visible |
            | input#daysUntilHarvest                      | visible |
            | input#daysOffFromOptimal                    | visible |
            | [data-cy=customize-view-selected-variables] | visible |

        Then I match pond selected variables from 'ACCOUNT_DETAILS' with modal

        When I press '[data-cy=customize-view-modal] [data-cy=cancel-btn]'
        Then I should not see element '[data-cy=customize-view-modal]'