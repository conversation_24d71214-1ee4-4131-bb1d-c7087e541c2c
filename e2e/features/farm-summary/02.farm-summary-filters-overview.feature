@isExperiment=2025-2-25
Feature: Farm Summary filters and farm overview

    Background:
        Given I am logged in as an admin
        When I go to current farm page


    Scenario: verify Farm overview
        When Element '[data-cy=ponds-list-data]' is visible
        Then I should see element '[data-cy=summary-drawer-desktop] [data-cy=summary-drawer-btn]'
        And I should not see element '[data-cy=summary-drawer-body]'

        When I press '[data-cy=summary-drawer-desktop] [data-cy=summary-drawer-btn]'
        Then I should see element '[data-cy=summary-drawer-body]'
        And Elements visibility on the "[data-cy=summary-drawer-body]" match the following
            | selector                                             | state   |
            | [data-cy=close-btn]                                  | visible |
            | [data-cy=total-sale]                                 | visible |
            | [data-cy=financial-data]                             | visible |
            | [data-cy=financial-data] [data-cy=costPoundHarvest]  | visible |
            | [data-cy=financial-data] [data-cy=processedCost]     | visible |
            | [data-cy=financial-data] [data-cy=totalCost]         | visible |
            | [data-cy=financial-data] [data-cy=revenuePerUnit]    | visible |
            | [data-cy=financial-data] [data-cy=profitPerUnit]     | visible |
            | [data-cy=financial-data] [data-cy=profitPerHaPerDay] | visible |
            | [data-cy=financial-data] [data-cy=totalProfit]       | visible |



        Then I should see element '[data-cy=summary-drawer-body] [data-cy=production-data]'
        And Elements visibility on the "[data-cy=summary-drawer-body] [data-cy=production-data]" match the following
            | selector                    | state   |
            | [data-cy=averageABW]        | visible |
            | [data-cy=avgBiweeklyGrowth] | visible |
            | [data-cy=avgWeeklyGrowth]   | visible |
            | [data-cy=avgFcr]            | visible |
            | [data-cy=avgSurvival]       | visible |
            | [data-cy=avgDaysOfCulture]  | visible |
            | [data-cy=kgOverHaOverDay]   | visible |
            | [data-cy=totalBiomass]      | visible |

        And wait for 1 seconds
        When I press '[data-cy=summary-drawer-body] [data-cy=close-btn]'
        Then I should not see element '[data-cy=summary-drawer-body]'



    Scenario: verify filters
        When Element '[data-cy=ponds-list-data]' is visible
        Then I should see element '[data-cy=ponds-list-filters]'
        And I should not see element '[data-cy=ponds-filters-form]'

        When I press '[data-cy=ponds-list-filters] [data-cy=trigger-btn]'
        Then I should see element '[data-cy=ponds-filters-form]'
        And Elements visibility on the "[data-cy=ponds-filters-form]" match the following
            | selector                        | state   |
            | input#aboveTarget               | visible |
            | input#onTrack                   | visible |
            | input#offTrack                  | visible |
            | input#flagged                   | visible |
            | input#aboveCarryingCapacityFlag | visible |
            | input#aboveCarryingCapacity     | visible |
            | [id="cycleDays.moreThan"]       | visible |
            | [id="cycleDays.lessThan"]       | visible |
            | [id="abw.moreThan"]             | visible |
            | [id="abw.lessThan"]             | visible |
            | [data-cy=cancel-btn]            | visible |
            | [data-cy=submit-btn]            | visible |

        And Input '[data-cy=ponds-filters-form] input#aboveCarryingCapacity' should be disabled

        When I press '[data-cy=ponds-filters-form] [data-cy=aboveCarryingCapacityFlagInput]'
        Then Input '[data-cy=ponds-filters-form] input#aboveCarryingCapacity' should be enabled

        When I press '[data-cy=ponds-filters-form] [data-cy=submit-btn]'
        Then I should see element '[data-cy=error-aboveCarryingCapacity]'
        When I fill in '[data-cy=ponds-filters-form] input#aboveCarryingCapacity' with '12'
        Then I should not see element '[data-cy=error-aboveCarryingCapacity]'

        # Cycle days
        Then Input '[data-cy=cycleDays-clear-btn]' should be disabled
        When I fill in '[data-cy=ponds-filters-form] [id="cycleDays.moreThan"]' with '30'
        And I fill in '[data-cy=ponds-filters-form] [id="cycleDays.lessThan"]' with '20'
        Then Input '[data-cy=cycleDays-clear-btn]' should be enabled
        And I should see element '[data-cy="error-cycleDays.lessThan"]'

        When I press '[data-cy=cycleDays-clear-btn]'
        Then I should not see element '[data-cy="error-cycleDays.lessThan"]'

        # ABW
        Then Input '[data-cy=abw-clear-btn]' should be disabled
        When I fill in '[data-cy=ponds-filters-form] [id="abw.moreThan"]' with '30'
        And I fill in '[data-cy=ponds-filters-form] [id="abw.lessThan"]' with '20'
        Then Input '[data-cy=abw-clear-btn]' should be enabled
        Then I should see element '[data-cy="error-abw.lessThan"]'

        When I press '[data-cy=abw-clear-btn]'
        Then I should not see element '[data-cy="error-abw.lessThan"]'

        # Cancel
        When I press "[data-cy=ponds-filters-form] [data-cy=cancel-btn]"
        Then I should not see element '[data-cy=ponds-filters-form]'


    Scenario: verify filters tags
        When Element '[data-cy=ponds-list-data]' is visible
        When I press '[data-cy=ponds-list-filters] [data-cy=trigger-btn]'
        Then I should see element '[data-cy=ponds-filters-form]'

        When I press '[data-cy=ponds-filters-form] [data-cy=aboveTargetInput]'
        And I press '[data-cy=ponds-filters-form] [data-cy=FlaggedInput]'
        And I press '[data-cy=ponds-filters-form] [data-cy=aboveCarryingCapacityFlagInput]'
        And I fill in '[data-cy=ponds-filters-form] [id=aboveCarryingCapacity]' with '33'

        And I press '[data-cy=ponds-filters-form] [data-cy=submit-btn]'
        Then Elements visibility on the "[data-cy=filter-tags]" match the following
            | selector                                                            | state   |
            | [data-cy=filterTag-aboveTarget]                                     | visible |
            | [data-cy=filterTag-aboveTarget] + [data-cy=remove-filter]           | visible |
            | [data-cy=filterTag-aboveCarryingCapacity]                           | visible |
            | [data-cy=filterTag-aboveCarryingCapacity] + [data-cy=remove-filter] | visible |
            | [data-cy=filterTag-flagged]                                         | visible |
            | [data-cy=filterTag-flagged] + [data-cy=remove-filter]               | visible |

        When I press '[data-cy=filterTag-aboveTarget] + [data-cy=remove-filter]'
        And I press '[data-cy=filterTag-aboveCarryingCapacity] + [data-cy=remove-filter]'
        Then Elements visibility on the "[data-cy=filter-tags]" match the following
            | selector                                                            | state   |
            | [data-cy=filterTag-aboveTarget]                                     | hidden  |
            | [data-cy=filterTag-aboveTarget] + [data-cy=remove-filter]           | hidden  |
            | [data-cy=filterTag-aboveCarryingCapacity]                           | hidden  |
            | [data-cy=filterTag-aboveCarryingCapacity] + [data-cy=remove-filter] | hidden  |
            | [data-cy=filterTag-flagged]                                         | visible |
            | [data-cy=filterTag-flagged] + [data-cy=remove-filter]               | visible |

        When I press '[data-cy=ponds-list-filters] [data-cy=trigger-btn]'
        Then I should see element '[data-cy=ponds-filters-form]'
        And Input '[data-cy=ponds-filters-form] input#flagged' should be checked
        And Input '[data-cy=ponds-filters-form] input#aboveTarget' should not be checked
        And Input '[data-cy=ponds-filters-form] input#aboveCarryingCapacityFlag' should not be checked

        When I fill in '[data-cy=ponds-filters-form] [id="cycleDays.moreThan"]' with '10'
        And I fill in '[data-cy=ponds-filters-form] [id="abw.moreThan"]' with '2'
        And I fill in '[data-cy=ponds-filters-form] [id="abw.lessThan"]' with '6'
        And I press '[data-cy=ponds-filters-form] [data-cy=submit-btn]'

        Then Elements visibility on the "[data-cy=filter-tags]" match the following
            | selector                           | state   |
            | [data-cy=filterTag-cycleDays]      | visible |
            | [data-cy=cycleDays-moreThan-value] | visible |
            | [data-cy=cycleDays-lessThan-value] | hidden  |
            | [data-cy=filterTag-abw]            | visible |
            | [data-cy=abw-moreThan-value]       | visible |
            | [data-cy=abw-lessThan-value]       | visible |

        And I should see element '[data-cy=cycleDays-moreThan-value]' with text '10'
        And I should see element '[data-cy=abw-moreThan-value]' with text '2'
        And I should see element '[data-cy=abw-lessThan-value]' with text '6'
