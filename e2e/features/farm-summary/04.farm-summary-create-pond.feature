@isExperiment=2025-2-25
Feature: Farm Summary Create pond

    Background:
        Given I am logged in as an admin
        When I go to current farm page

    Scenario: check create new pond form validation
        When Element '[data-cy=ponds-list-data]' is visible
        And I press '[data-cy=more-options-menu-btn]'
        Then I should see element '[data-cy=customize-view-btn]'
        And I should see element '[data-cy=create-new-pond-btn]'

        When I press '[data-cy=create-new-pond-btn]'
        Then I should see element '[data-cy=add-edit-pond-modal]'

        And Elements visibility on the '[data-cy=add-edit-pond-modal]' match the following
            | selector             | state   |
            | input#name           | visible |
            | input#size           | visible |
            | #superVisorId        | visible |
            | [data-cy=cancel-btn] | visible |
            | button[type=submit]  | visible |

        When I press '[data-cy=add-edit-pond-modal] button[type=submit]'
        Then I should see element '[data-cy=add-edit-pond-modal] [data-cy=error-name]'
        And I should see element '[data-cy=add-edit-pond-modal] [data-cy=error-size]'

        When I fill in '[data-cy=add-edit-pond-modal] input#name' with 'pond'
        Then I should not see element '[data-cy=add-edit-pond-modal] [data-cy=error-name]'

        When I fill in '[data-cy=add-edit-pond-modal] input#size' with '3'
        Then I should not see element '[data-cy=add-edit-pond-modal] [data-cy=error-size]'


        When I press '[data-cy=add-edit-pond-modal] [data-cy=cancel-btn]'
        Then I should not see element '[data-cy=add-edit-pond-modal]'

        Then Delete pond with id '677e95dbb2c4d89c34cf7576'

    Scenario: Create new pond
        When Element '[data-cy=ponds-list-data]' is visible
        And I press '[data-cy=more-options-menu-btn]'
        And I should see element '[data-cy=create-new-pond-btn]'

        When I press '[data-cy=create-new-pond-btn]'
        Then I should see element '[data-cy=add-edit-pond-modal]'

        When I fill in '[data-cy=add-edit-pond-modal] input#name' with 'pondww'
        And I fill in '[data-cy=add-edit-pond-modal] input#size' with '2'
        Then I press '[data-cy=add-edit-pond-modal] button[type=submit]'

        And I save 'v1CreatePond' api response as 'POND_DETAILS'


        When Element '[data-cy=pond-view-screen]' is visible
        Then I press '[data-cy=farm-summary-link]'


        When Element '[data-cy=ponds-list-data]' is visible
        Then I should see element '[data-cy=production-ponds-list] [data-cy="pond-{{POND_DETAILS.v1CreatePond.pond._id}}"]'
        And I should see element '[data-cy=production-ponds-list] [data-cy="pond-{{POND_DETAILS.v1CreatePond.pond._id}}"] [data-cy=pond-name]' with text 'pondww'
        And I should see element '[data-cy=production-ponds-list] [data-cy="pond-{{POND_DETAILS.v1CreatePond.pond._id}}"] [data-cy=new-pond-empty-state]'
        And I should see element '[data-cy=production-ponds-list] [data-cy="pond-{{POND_DETAILS.v1CreatePond.pond._id}}"] [data-cy=stock-pond-btn]'

        Then Delete pond with id '{{POND_DETAILS.v1CreatePond.pond._id}}'



