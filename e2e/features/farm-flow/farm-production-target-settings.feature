@skip
Feature: Farm Production target settings flow

  Background:
    Given I am logged in as an admin
    When I go to current farm page
    Then I should see element '[data-cy=farm-overview-container]'

  Scenario: set default values for production target
    When I press '[data-cy=settings-nav-link]'
    Then I should see element '[data-cy=settings-screen]'
    Then  I press '[data-cy=setting-production-tab]'
    And wait for 3 seconds
    When I press '[data-cy=production-targets-settings-card] [data-cy=edit-btn]'
    Then Input '[data-cy=production-targets-settings-card] input#productionDays' should be enabled
    And Input '[data-cy=production-targets-settings-card] input#weight' should be enabled

    When I fill in '[data-cy=production-targets-settings-card] input#productionDays' with faker 'smallNumber' as 'DEFAULT_PRODUCTION_DAYS'
    And I fill in '[data-cy=production-targets-settings-card] input#weight' with faker 'smallNumber' as 'DEFAULT_WEIGHT'

    When I press '[data-cy=harvest-optimization-actions] [data-cy=save-btn]' and wait for 'v1UpdateFarm' api response
    Then I should see input '[data-cy=production-targets-settings-card] input#productionDays' with value 'DEFAULT_PRODUCTION_DAYS' and suffix ' days'
    And I should see number input '[data-cy=production-targets-settings-card] input#weight' with value 'DEFAULT_WEIGHT' and suffix 'g'

  Scenario: Create a pond and stock it to validate production target default values are there in the whole flow
        # create new pond
    When I press '[data-cy=production-insights-link]'
    Then I should see element '[data-cy=create-new-pond-btn]'
    When I create a pond and save data as 'FPTS_POND_DATA'

    Then I should see element '[data-cy=pond-view-screen]'
        # TODO: REMOVE WHEN FIX THE BUG
    When Temp step
        # TODO: END
    When I press '[data-cy=pond-view-actions] [data-cy=action-menu-button]'
    Then I press '[data-cy=pond-view-actions] [role=menu] [data-cy=stock-pond-btn]'
    Then I should see element '[data-cy=stock-pond-modal]'

        # set stock date
    Then I fill in '[data-cy=stock-pond-modal] #cycle' with '1'
    And I select day 10 for field '[data-cy=stock-pond-modal] #stockedAt'
    And I fill in '[data-cy=stock-pond-modal] #seedingQuantity' with '100'
    And I fill in '[data-cy=stock-pond-modal] #seedingAverageWeight' with '15'
    And I press '[data-cy=stock-pond-modal] button[type=submit]'

    Then I should see element '[data-cy=production-target-form]'
    And I should see number input '[data-cy=production-target-form] #weight' with value 'DEFAULT_WEIGHT'
    And I should see input '[data-cy=production-target-form] #productionDays' with value 'DEFAULT_PRODUCTION_DAYS'
    When I press '[data-cy=production-target-form] [data-cy=later-btn]' and wait for 'v1StockPond' api response
    Then I should not see element '[data-cy=stock-pond-modal]'
    Then I should see element '[data-cy=pond-view-screen]'
    Then wait for 1 seconds
    When I press '[data-cy=pond-view-tabs] [data-cy=insights-tab]'
    Then I should see element '[data-cy=production-target-card] [data-cy=set-target-btn]'


        # make sure the production target button is set to default values
    When I press "[data-cy=production-target-card] [data-cy=set-target-btn]"
    Then I should see element '[data-cy=production-target-form]'
    And I should see number input '[data-cy=production-target-form] #weight' with value 'DEFAULT_WEIGHT'
    And I should see input '[data-cy=production-target-form] #productionDays' with value 'DEFAULT_PRODUCTION_DAYS'
    When I press '[data-cy=production-target-form] [data-cy=later-btn]'
    Then I should not see element '[data-cy=stock-pond-modal]'

        # Test production target default values in pond details page
        # make sure the production target button is set to default values
    Then I should see element '[data-cy=production-target-card] [data-cy=set-target-btn]'
    When I press "[data-cy=production-target-card] [data-cy=set-target-btn]"
    Then I should see element '[data-cy=production-target-form]'
    And I should see number input '[data-cy=production-target-form] #weight' with value 'DEFAULT_WEIGHT'
    And I should see input '[data-cy=production-target-form] #productionDays' with value 'DEFAULT_PRODUCTION_DAYS'
    When I press '[data-cy=production-target-form] [data-cy=later-btn]'

        # Test production target default values in edit stocking
    Then I should see element '[data-cy=stocking-details]'
    When I press '[data-cy=stocking-details] [data-cy=edit-stock-btn]'
    Then I should see element '[data-cy=stock-pond-modal]'
    When I press '[data-cy=stock-pond-modal] button[type=submit]'
    Then I should not see element '[data-cy=production-target-form]'


        # Delete the pond
    Then I harvest and archive the pond



