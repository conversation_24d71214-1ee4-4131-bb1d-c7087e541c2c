@skip
Feature: Farm Production target settings flow

    Background:
        Given I am logged in as an admin
        When I go to current farm page
        And I should see element '[data-cy=dashboard-sidebar-link]'
        Then I press '[data-cy=dashboard-sidebar-link]'
        And I save 'v1ListFarms' api response as 'LIST_FARMS'
        And I save 'v1ListPonds' api response as 'LIST_PONDS'
        # Then log store
        Then I should see element '[data-cy=group-summary-screen]'


    Scenario: set default values for production target

        When I press '[data-cy=settings-nav-link]'
# Then I should see element '[data-cy=settings-screen]'
