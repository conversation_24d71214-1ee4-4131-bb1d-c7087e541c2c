@skip
@isExperiment=2023-11-28
Feature: Invite user to farm

  Scenario: I signup as new user
    Given I am on the '/signup' page

    When I fill in '#email' with faker 'email' as 'first-user-email'
    Then I should see element '.chakra-input__right-element'
    Then I fill in '#password' with faker 'password' as 'first-user-password'
    And I fill in '#confirmPassword' with value of 'first-user-password'
    When I press '#submit'
    And wait for page navigation
    Then I should be on page starting with '/verify'
    When I fill in '#code' with '1111'
    Then I press '#submit'
    Then I should be on the '' page

  Scenario: I invite user to farm
    Given I am on the '/logout' page
    Then I should see element '[data-cy=forgot-password-link]'
    When Backoffice admin add farms with permissions for 'first-user-email'
      | farmName          | key         |
      | farm for new user | newUserFarm |

    And wait for 10 seconds

    When I am logged in with a 'first-user-email' and 'first-user-password'
    And I am on the '/' page
    Then wait for 2 seconds
    Then I should see element '[data-cy=farm-overview-container]'
