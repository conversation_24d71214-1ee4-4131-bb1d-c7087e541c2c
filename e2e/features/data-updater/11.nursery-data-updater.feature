@isExperiment=2025-2-25
Feature: Nursery data updater

    Background:
        Given I am logged in as an admin
        When I go to current farm page
        And I save 'v1ListFarms' api response as 'LIST_FARMS'

    Scenario: verify page layout
        When Element '[data-cy=ponds-list-data]' is visible
        Then I should see element '[data-cy=data-updater-link]'

        When I press '[data-cy=data-updater-link]'
        Then I should see element '[data-cy=data-updater-screen]'
        And I should see element '[data-cy=growOut-data-updater-link]'
        And I should see element '[data-cy=nursery-data-updater-link]'

        And I should see element '[data-cy=growOut-data-updater-link]' with attribute 'href' similar to '/data-updater'
        And I should see element '[data-cy=nursery-data-updater-link]' with attribute 'href' similar to '/nursery-data-updater'

        When I press '[data-cy=nursery-data-updater-link]'
        Then Elements visibility on the page match the following
            | selector                                                       | state   |
            | [data-cy=nursery-data-updater-screen]                          | visible |
            | [data-cy=growOut-data-updater-link]                            | visible |
            | [data-cy=nursery-data-updater-link]                            | visible |
            | [data-cy=weekly-range-selector-btn]                            | visible |
            | [data-cy=edit-data-range-btn]                                  | visible |
            | [data-cy=nursery-feed-data-table]                              | visible |
            | [data-cy=edit-nursery-feed-data]                               | visible |
            | [data-cy=nursery-feed-data-table-actions]                      | hidden  |
            | [data-cy=nursery-feed-data-table-actions] [data-cy=cancel-btn] | hidden  |
            | [data-cy=nursery-feed-data-table-actions] [data-cy=save-btn]   | hidden  |

        And I fill data for editable nurseries from 'LIST_FARMS'