@isExperiment=2025-2-25
Feature: Data updater feed tab

    Background:
        Given I am logged in as an admin
        When I go to current farm page
        And I save 'v1List<PERSON>arms' api response as 'LIST_FARMS'
        And I save 'v1ListPonds' api response as 'LIST_PONDS'


    Scenario: Add Data to weekly data table
        When Element '[data-cy=ponds-list-data]' is visible
        Then I should see element '[data-cy=data-updater-link]'

        When I press '[data-cy=data-updater-link]'
        Then I should see element '[data-cy=data-updater-screen]'

        Then Elements visibility on the page match the following
            | selector                            | state   |
            | [data-cy=weekly-data-table]         | visible |
            | [data-cy=feed-tabs-options]         | visible |
            | [data-cy=weekly-range-selector-btn] | visible |
            | [data-cy=edit-data-range-btn]       | visible |
            | [data-cy=edit-weekly-data]          | visible |

        And I fill data for editable pond from farm 'LIST_FARMS'

    Scenario: Add Data to daily data table
        When Element '[data-cy=ponds-list-data]' is visible
        Then I should see element '[data-cy=data-updater-link]'

        When I press '[data-cy=data-updater-link]'
        Then I should see element '[data-cy=data-updater-screen]'

        When I press '[data-cy=feed-tabs-options]'
        Then I should see element '[data-cy=menuItem-dailyData]'

        When I press '[data-cy=menuItem-dailyData]'
        Then Elements visibility on the page match the following
            | selector                      | state   |
            | [data-cy=daily-data-table]    | visible |
            | [data-cy=daily-data-calender] | visible |
            | [data-cy=edit-daily-data-btn] | visible |
            | [data-cy=weekly-data-table]   | hidden  |

        And I fill data for daily feed editable pond from farm 'LIST_FARMS'

