@isExperiment=2025-2-25
Feature: Data updater daily parameters tab

    Background:
        Given I am logged in as an admin
        When I go to current farm page
        And I save 'v1List<PERSON>arms' api response as 'LIST_FARMS'
        And I save 'v1ListPonds' api response as 'LIST_PONDS'

    Scenario: Add Data to daily parameters table
        When Element '[data-cy=ponds-list-data]' is visible
        Then I should see element '[data-cy=data-updater-link]'

        When I press '[data-cy=data-updater-link]'
        Then I should see element '[data-cy=data-updater-screen]'

        When I press '[data-cy=data-updater-selector]'
        Then I press '[data-cy=menuItem-dailyParameter]'

        When Element '[data-cy=daily-param-table]' is visible
        Then Elements visibility on the page match the following
            | selector                                            | state   |
            | [data-cy=daily-parameters-calender]                 | visible |
            | [data-cy=edit-daily-params-btn]                     | visible |
            | [data-cy=daily-params-actions] [data-cy=cancel-btn] | hidden  |
            | [data-cy=daily-params-actions] [data-cy=save-btn]   | hidden  |

        When I press '[data-cy=edit-daily-params-btn]'
        Then I should see element '[data-cy=daily-params-actions] [data-cy=cancel-btn]'
        And I should see element '[data-cy=daily-params-actions] [data-cy=save-btn]'
        And I should not see element '[data-cy=edit-daily-params-btn]'

        When I press '[data-cy=daily-params-actions] [data-cy=cancel-btn]'
        Then I should see element '[data-cy=edit-daily-params-btn]'
        And I should not see element '[data-cy=daily-params-actions] [data-cy=cancel-btn]'
        And I should not see element '[data-cy=daily-params-actions] [data-cy=save-btn]'

        Then I fill data in daily parameters for editable pond from farm 'LIST_FARMS'