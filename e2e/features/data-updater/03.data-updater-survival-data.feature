@isExperiment=2025-2-25
Feature: Weight data tab

    Background:
        Given I am logged in as an admin
        When I go to current farm page
        And I save 'v1List<PERSON>arms' api response as 'LIST_FARMS'
        And I save 'v1ListPonds' api response as 'LIST_PONDS'
        And I save 'v1ListPopulations' api response as 'LIST_POPULATIONS'


    Scenario: Add Data to weekly data table
        When Element '[data-cy=ponds-list-data]' is visible
        Then I should see element '[data-cy=data-updater-link]'

        When I press '[data-cy=data-updater-link]'
        Then I should see element '[data-cy=data-updater-screen]'

        When I press '[data-cy=data-updater-selector]'
        Then I press '[data-cy=menuItem-survivalData]'

        When Element '[data-cy=survival-data-table]' is visible
        Then Elements visibility on the page match the following
            | selector                                             | state   |
            | [data-cy=survival-data-calendar]                     | visible |
            | [data-cy=survival-data]                              | visible |
            | [data-cy=edit-survival-data-btn]                     | visible |
            | [data-cy=survival-data-table-message]                | visible |
            | [data-cy=survival-data-actions]                      | visible |
            | [data-cy=survival-data-actions] [data-cy=cancel-btn] | hidden  |
            | [data-cy=survival-data-actions] [data-cy=save-btn]   | hidden  |

        When I press '[data-cy=edit-survival-data-btn]'
        Then I should see element '[data-cy=survival-data-actions] [data-cy=cancel-btn]'
        And I should see element '[data-cy=survival-data-actions] [data-cy=save-btn]'
        And I should not see element '[data-cy=edit-survival-data-btn]'

        When I press '[data-cy=survival-data-actions] [data-cy=cancel-btn]'
        Then I should not see element '[data-cy=survival-data-actions] [data-cy=cancel-btn]'
        And I should not see element '[data-cy=survival-data-actions] [data-cy=save-btn]'
        And I should see element '[data-cy=edit-survival-data-btn]'

        Then I should see ponds rendered correctly on the survival table from 'LIST_PONDS'
        Then I should be able to edit  in survival data updater


