@isExperiment=2025-2-25
Feature: Weight data tab

    Background:
        Given I am logged in as an admin
        When I go to current farm page
        And I save 'v1ListFarms' api response as 'LIST_FARMS'
        And I save 'v1ListPonds' api response as 'LIST_PONDS'
        And I save 'v1ListPopulations' api response as 'LIST_POPULATIONS'


    Scenario: Add Data to weekly data table
        When Element '[data-cy=ponds-list-data]' is visible
        Then I should see element '[data-cy=data-updater-link]'

        When I press '[data-cy=data-updater-link]'
        Then I should see element '[data-cy=data-updater-screen]'

        When I press '[data-cy=data-updater-selector]'
        Then I press '[data-cy=menuItem-weightData]'

        When Element '[data-cy=weight-data-table]' is visible
        Then Elements visibility on the page match the following
            | selector                                           | state   |
            | [data-cy=weight-data-calendar]                     | visible |
            | [data-cy=weight-data-table-message]                | visible |
            | [data-cy=ponds-weight-data]                        | visible |
            | [data-cy=weight-data-actions]                      | visible |
            | [data-cy=edit-weight-data-btn]                     | visible |
            | [data-cy=weight-data-actions] [data-cy=cancel-btn] | hidden  |
            | [data-cy=weight-data-actions] [data-cy=save-btn]   | hidden  |

        When I press '[data-cy=edit-weight-data-btn]'
        Then I should see element '[data-cy=weight-data-actions] [data-cy=cancel-btn]'
        And I should see element '[data-cy=weight-data-actions] [data-cy=save-btn]'
        And I should not see element '[data-cy=edit-weight-data-btn]'
        And Button '[data-cy=weight-data-actions] [data-cy=save-btn]' should be disabled

        When I press '[data-cy=weight-data-actions] [data-cy=cancel-btn]'
        Then I should not see element '[data-cy=weight-data-actions] [data-cy=cancel-btn]'
        And I should not see element '[data-cy=weight-data-actions] [data-cy=save-btn]'
        And I should see element '[data-cy=edit-weight-data-btn]'

        Then I see weight table data based on pondsList 'LIST_PONDS'


