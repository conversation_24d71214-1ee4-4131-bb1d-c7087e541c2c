Feature: Smoke testing - user registration

  Scenario:
    Given I am on the "/signup" page
    Then I should see element '[data-cy=signup-form]'
    Then Elements visibility on the "[data-cy=signup-form]" match the following
      | selector              | state   |
      | input#email           | visible |
      | input#password        | visible |
      | input#confirmPassword | visible |
      | button#submit         | visible |
    And I should see element '[data-cy=back-to-login]'


        # Form validation
    When I press '[data-cy=signup-form] button#submit'
    Then Elements visibility on the "[data-cy=signup-form]" match the following
      | selector                        | state   |
      | [data-cy=error-email]           | visible |
      | [data-cy=error-password]        | visible |
      | [data-cy=error-confirmPassword] | visible |

        # go to login page
    When I press '[data-cy=back-to-login]'
    Then I should see element '[data-cy=login-form]'

        # go to signup page
    Given I visit '/signup' page
    Then I should see element '[data-cy=signup-form]'

        # fill form with wrong data
    When I fill in '[data-cy=signup-form] input#email' with 'wrongEmail'
    And I fill in '[data-cy=signup-form] input#password' with '123'
    And I fill in '[data-cy=signup-form] input#confirmPassword' with '456'
    And I press '[data-cy=signup-form] button#submit'
    Then Elements visibility on the "[data-cy=signup-form]" match the following
      | selector                        | state   |
      | [data-cy=error-email]           | visible |
      | [data-cy=error-password]        | visible |
      | [data-cy=error-confirmPassword] | visible |

        # Password and confirm password validations
    When I fill in '[data-cy=signup-form] input#password' with '123456'
    And I fill in '[data-cy=signup-form] input#confirmPassword' with '567890'
    Then I should not see element '[data-cy=error-password]'
    And I should see element '[data-cy=error-confirmPassword]'

    When I fill in '[data-cy=signup-form] input#confirmPassword' with '123456'
    Then I should not see element '[data-cy=error-password]'

        # email validataion
    When I fill in '[data-cy=signup-form] input#email' with 'adminUsername'
    Then I should see element '[data-cy=email-input-group] [data-cy=circle-red]'

    When I fill in '[data-cy=signup-form] input#email' with faker 'email' as 'registeredEmail'
    Then I should see element '[data-cy=email-input-group] [data-cy=circle-green]'

        # fill form with correct data
    When I press '[data-cy=signup-form] button#submit'
    Then I should be on page starting with "/verify"
    And Elements visibility on the page match the following
      | selector                                          | state   |
      | [data-cy=verify-form]                             | visible |
      | [data-cy=verify-form] input#code                  | visible |
      | [data-cy=verify-form] [data-cy=verify-your-email] | visible |
      | [data-cy=resend-code]                             | visible |

        # enter invalid code
    Given I close all toasts
    When I fill in "[data-cy=verify-form] input#code" with "aaaa"
    And I press "[data-cy=verify-form] [data-cy=verify-your-email]"
    Then I should see element "[data-cy=toast-error-0]"

        # resend code
    Given I close all toasts
    When I press "[data-cy=resend-code]"
    Then I should see element "[data-cy=toast-success-0]"

        # valid code to complete the register process
    Given I close all toasts
    When I fill in "[data-cy=verify-form] input#code" with "1111"
    And I press "[data-cy=verify-your-email]"
    Then I should see element "[data-cy=toast-success-0]"
    And I should not be on the "/verify" page
