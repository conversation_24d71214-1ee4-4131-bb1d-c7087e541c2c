Feature: Smoke testing - <PERSON><PERSON>:
    Given I am on the "/login" page
    Then I should see element "[data-cy=login-form]"
    And Elements visibility on the "[data-cy=login-form]" match the following
      | selector                           | state   |
      | input#email                        | visible |
      | input#password                     | visible |
      | a[data-cy=forgot-password-link]    | visible |
      | button#login-btn                   | visible |
      | [data-cy=password-visibility-icon] | hidden  |

    When I press '[data-cy=login-form] button#login-btn'
    Then I should see element '[data-cy=error-email]'
    And I should see element '[data-cy=error-password]'

        # fill in email and password fields with valid data
    When I fill in '[data-cy=login-form] input#email' with '<EMAIL>'
    And I fill in '[data-cy=login-form] input#password' with '123456'
    Then Elements visibility on the page match the following
      | selector                                                | state   |
      | [data-cy=error-email]                                   | hidden  |
      | [data-cy=error-password]                                | hidden  |
      | [data-cy=login-form] [data-cy=password-visibility-icon] | visible |

        # fill in email and password fields with not valid data
    When I fill in '[data-cy=login-form] input#email' with 'emaill'
    And I fill in '[data-cy=login-form] input#password' with '1234'
    Then Elements visibility on the page match the following
      | selector                                                | state   |
      | [data-cy=error-email]                                   | visible |
      | [data-cy=error-password]                                | visible |
      | [data-cy=login-form] [data-cy=password-visibility-icon] | visible |

        # Login with invalid data
    When I fill in '[data-cy=login-form] input#email' with '<EMAIL>'
    And I fill in '[data-cy=login-form] input#password' with 'wrondPassword'
    And I press '[data-cy=login-form] button#login-btn'
    Then I should see element '[data-cy*=toast-error]'
    And I should be on the "/login" page

        # Login with valid data
    When I fill in "#email" with "adminUsername"
    And I fill in "#password" with "adminPassword"
    And I press "#login-btn"
    Then I should see element '[data-cy*=toast-success]'
