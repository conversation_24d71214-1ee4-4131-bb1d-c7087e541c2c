@isExperiment=2025-2-25
Feature: Smoke testing - Test pond details page

    Background: go to current farm
        Given I am logged in as an admin
        When I go to current farm page
        When Element '[data-cy=ponds-list-data]' is visible

    Scenario: Go to the pond details page and stock pond if it is empty
        When Element '[data-cy=more-options-menu-btn]' is visible
        When I press '[data-cy=more-options-menu-btn]'
        Then I should see element '[data-cy=create-new-pond-btn]'
        When I create a pond by trigger '[data-cy=create-new-pond-btn]' and save data as 'NEW_POND_DATA'
        Then Element '[data-cy=pond-view-screen]' is visible

        Then I should see element '[data-cy=pond-view-screen]'
        Then Elements visibility on the page match the following
            | selector                                                        | state   |
            | [data-cy=new-pond]                                              | visible |
            | [data-cy=new-pond] [data-cy=stock-pond-btn]                     | visible |
            | [data-cy=pond-view-data]                                        | hidden  |
            | [data-cy=pond-details-tabs]                                     | hidden  |
            | [data-cy=pond-details-tabs] [data-cy=pond-overview-tab-btn]     | hidden  |
            | [data-cy=pond-details-tabs] [data-cy=pond-harvest-plan-tab-btn] | hidden  |
            | [data-cy=pond-details-tabs] [data-cy=pond-data-tab-btn]         | hidden  |
            | [data-cy=pond-view-actions]                                     | hidden  |

        And I should see element '[data-cy=pond-name]' with text '{{NEW_POND_DATA.name}}'


        When I press '[data-cy=new-pond] [data-cy=stock-pond-btn]'
        Then I should see element '[data-cy=pond-stocking-selection-modal]'
        And I should see element '[data-cy=no-nurseries-modal]'
        And I should see element '[data-cy=edit-direct-stocking-btn]'

        When I press '[data-cy=edit-direct-stocking-btn]'
        Then I should see element '[data-cy=stock-pond-modal]'
        And I fill in '[data-cy=stock-pond-modal] #cycle' with '1'
        And I select day 1 for field '[data-cy=stock-pond-modal] #stockedAt'
        And I fill in '[data-cy=stock-pond-modal] #seedingQuantity' with '10000000'
        And I fill in '[data-cy=stock-pond-modal] #seedingAverageWeight' with '2'
        And I fill in '[data-cy=stock-pond-modal] #dryDaysBeforeStocking' with '4'
        And I press '[data-cy=stock-pond-modal] [data-cy=next-btn]'

        Then I should see element '[data-cy=target-form]'
        And I fill in '[data-cy=target-form] [id="cycleInformation.target.biomassToHarvest"]' with '1'
        And I fill in '[data-cy=target-form] [id="cycleInformation.target.fcr"]' with '3'
        When I press '[data-cy=stock-pond-modal] [data-cy=next-btn]'
        Then I should see element '[data-cy=save-btn]'

        And I press '[data-cy=stock-pond-modal] [data-cy=save-btn]'

        Then I should not see element '[data-cy=stock-pond-modal]'
        And Elements visibility on the page match the following
            | selector                            | state   |
            | [data-cy=pond-view-data]            | visible |
            | [data-cy=pond-overview-tab-btn]     | visible |
            | [data-cy=pond-harvest-plan-tab-btn] | visible |
            | [data-cy=pond-data-tab-btn]         | visible |

        When I press '[data-cy=pond-data-tab-btn]'
        Then I should see element '[data-cy=pond-view-tabs]'
        And Elements visibility on the "[data-cy=pond-view-tabs]" match the following
            | selector                        | state   |
            | [data-cy=feed-tab]              | visible |
            | [data-cy=monitoring-tab]        | visible |
            | [data-cy=stocking-tab]          | visible |
            | [data-cy=transfer-history-tab]  | visible |
            | [data-cy=daily-params-tab]      | visible |
            | [data-cy=cycle-information-tab] | visible |
            | [data-cy=harvest-tab]           | visible |

        When I press '[data-cy=pond-view-tabs] [data-cy=stocking-tab]'
        Then I should see element '[data-cy=pond-stocking-details]'
        And I should see element '[data-cy=edit-stocking-btn]'

        When I press '[data-cy=edit-stocking-btn]'
        Then I should see element '[data-cy=stock-pond-modal]'

        And I should see input '[data-cy=stock-pond-modal] input#seedingAverageWeight' with value '2.00' and suffix 'g'
        And I should see input '[data-cy=stock-pond-modal] input#dryDaysBeforeStocking' with value '4'


        Then Delete pond with id '{{NEW_POND_DATA._id}}'

