@skip
Feature: Create new pond

    Background: go to current farm
        Given I am logged in as an admin
        When I go to current farm page

    Scenario:
        When Element '[data-cy=ponds-list-data]' is visible
        Then I should see element '[data-cy=production-ponds-options-container] [data-cy=more-options-menu-btn]'

        When I press '[data-cy=production-ponds-options-container] [data-cy=more-options-menu-btn]'
        Then I should see element '[data-cy=create-new-pond-btn]'

        When I press '[data-cy=create-new-pond-btn]'
        Then I should see element '[data-cy=add-edit-pond-modal]'

        And Elements visibility on the '[data-cy=add-edit-pond-modal]' match the following
            | selector             | state   |
            | input#name           | visible |
            | input#size           | visible |
            | #superVisorId        | visible |
            | [data-cy=cancel-btn] | visible |
            | button[type=submit]  | visible |

        When I fill in '[data-cy=add-edit-pond-modal] input#name' with 'newPond'

# Then Delete pond with id '6786325c9bdf1a0872e04abe'

