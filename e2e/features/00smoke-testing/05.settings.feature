@isExperiment=2025-2-25
Feature: Smoke testing - Settings page

  Background: Background name
    Given I am logged in as an admin
    When I go to current farm page
    When Element '[data-cy=ponds-list-data]' is visible

  Scenario: Validate the inputs on the settings page, and edit the production target
    When I press '[data-cy=settings-nav-link]'
    Then I should see element '[data-cy=settings-screen]'
    Then  I press '[data-cy=setting-production-tab]'
    And wait for 3 seconds
    Then Elements visibility on the "[data-cy=production-targets-settings-card]" match the following
      | selector             | state   |
      | input#productionDays | visible |
      | input#weight         | visible |
      | [data-cy=edit-btn]   | visible |

    And Input '[data-cy=production-targets-settings-card] input#productionDays' should be disabled
    And Input '[data-cy=production-targets-settings-card] input#weight' should be disabled

    When I press '[data-cy=production-targets-settings-card] [data-cy=edit-btn]'

    Then Elements visibility on the "[data-cy=harvest-optimization-actions]" match the following
      | selector             | state   |
      | [data-cy=save-btn]   | visible |
      | [data-cy=cancel-btn] | visible |

    Then Input '[data-cy=production-targets-settings-card] input#productionDays' should be enabled
    And Input '[data-cy=production-targets-settings-card] input#weight' should be enabled

    When I fill in '[data-cy=production-targets-settings-card] input#productionDays' with faker 'smallNumber' as 'SP_PRODUCTION_DAYS'
    And I fill in '[data-cy=production-targets-settings-card] input#weight' with '20'

    # When I press '[data-cy=harvest-optimization-actions] [data-cy=save-btn]' and wait for 'v1UpdateFarm' api response
    Then I should see input '[data-cy=production-targets-settings-card] input#productionDays' with value 'SP_PRODUCTION_DAYS' and suffix ' days'
    And I should see input '[data-cy=production-targets-settings-card] input#weight' with value '20.00' and suffix 'g'

  @skip
  Scenario: Validate the inputs on the production target form
    When I press '[data-cy=production-insights-link]'
    Then I should see element '#ponds-list'
    When I create a pond and save data as 'SP_POND_DATA'
    When Element '[data-cy=ponds-list-skeleton-loader]' is not visible
    Then I should see element "[data-cy=pond-view-screen]"
    # TODO: REMOVE WHEN FIX THE BUG
    When Temp step
    # TODO: END
    When I press "[data-cy=pond-view-actions]"
    Then I press "[data-cy=stock-pond-btn]"
    Then I should see element '[data-cy=stock-pond-modal]'

    # set stock date
    Then I fill in '[data-cy=stock-pond-modal] #cycle' with '1'
    And I select day 10 for field '[data-cy=stock-pond-modal] #stockedAt'
    And I fill in '[data-cy=stock-pond-modal] #seedingQuantity' with '100'
    And I fill in '[data-cy=stock-pond-modal] #seedingAverageWeight' with '15'
    And I press '[data-cy=stock-pond-modal] button[type=submit]'

    Then I should see element '[data-cy=production-target-form]'

    # weight and productionDays fields are required
    When I fill in '[data-cy=production-target-form] #weight' with ''
    And I fill in '[data-cy=production-target-form] #productionDays' with ''
    And I press '[data-cy=production-target-form] [data-cy=set-target-btn]'
    Then I should see element '[data-cy=error-weight]'
    And I should see element '[data-cy=error-productionDays]'

    #validate the production days field
    When I fill in '[data-cy=production-target-form] #productionDays' with '9'
    And I fill in '[data-cy=production-target-form] #weight' with '10'
    And I press '[data-cy=production-target-form] [data-cy=set-target-btn]'
    Then I should see element '[data-cy=error-productionDays]'

    # validate linearGrowth
    When I fill in '[data-cy=production-target-form] #weight' with '35'
    And I fill in '[data-cy=production-target-form] #productionDays' with '30'
    And I press '[data-cy=production-target-form] [data-cy=set-target-btn]'
    Then Input '[data-cy=production-target-form] [data-cy=set-target-btn]' should be disabled

    # Validate change in fields together
    When I fill in '[data-cy=production-target-form] #weight' with '2'
    And I fill in '[data-cy=production-target-form] #productionDays' with '30'
    And I press '[data-cy=production-target-form] [data-cy=set-target-btn]'
    Then I should see element '[data-cy=error-weight]'

    When I fill in '[data-cy=production-target-form] #productionDays' with '10'
    And I press '[data-cy=production-target-form] [data-cy=set-target-btn]'
    Then I should see element '[data-cy=error-weight]'

    # Submit the form
    When I fill in '[data-cy=production-target-form] #weight' with '10'
    And I fill in '[data-cy=production-target-form] #productionDays' with '30'
    Then Input '[data-cy=production-target-form] [data-cy=set-target-btn]' should be enabled
    When I press '[data-cy=production-target-form] [data-cy=set-target-btn]'
    Then I should not see element '[data-cy=stock-pond-modal]'
    When Element '[data-cy=section-loader]' is not visible
    Then I should see element "[data-cy=pond-view-actions]"


    When I press "[data-cy=pond-view-actions]"
    Then I press "[data-cy=harvest-pond-btn]"
    Then I should see element '[data-cy=harvest-population-modal]'
    And I select day 24 for field '[data-cy=harvest-population-modal] #date'
    And I fill in '[data-cy=harvest-population-modal] #weight' with '10'
    And I fill in '[data-cy=harvest-population-modal] #lbsHarvested' with '3000'
    And I press '[data-cy=harvest-population-modal] button[type=submit]'
    Then I should not see element '[data-cy=harvest-population-modal]'

    When I press '[data-cy=pond-view-actions]'
    Then I press '[data-cy=archive-pond-btn]'
    Then I should see element '[data-cy=archive-pond-modal]'
    And I press '[data-cy=archive-pond-modal] [data-cy=archive-pond-submit-btn]'
    Then I should not see element '[data-cy=archive-pond-modal]'
