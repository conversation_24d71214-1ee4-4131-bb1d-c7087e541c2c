Feature: Smoke testing - user account

  Background:
    Given I am logged in as an admin
    Then wait for 5 seconds
    When I am on the "/my-account" page

  Scenario:
    When Element '[data-cy=my-account-screen]' is visible
    Then Elements visibility on the page match the following
      | selector                           | state   |
      | [data-cy=edit-details-btn]         | visible |
      | [data-cy=user-name-label]          | visible |
      | [data-cy=user-contact-phone-label] | visible |
      | [data-cy=user-gender-label]        | visible |
      | [data-cy=user-email-label]         | visible |
      | [data-cy=user-password-label]      | visible |
      | [data-cy=change-password-btn]      | visible |
    And I should see element '[data-cy=user-password-label] .label-value' with text '****'

    When I press '[data-cy=change-password-btn]'
    Then I should see element '[data-cy=change-password-modal]'
    And Elements visibility on the "[data-cy=change-password-modal]" match the following
      | selector                        | state   |
      | input#oldPassword               | visible |
      | input#newPassword               | visible |
      | input#confirmNewPassword        | visible |
      | button[data-cy=submit-btn]      | visible |
      | button[data-cy=close-modal-btn] | visible |

    When I press '[data-cy=change-password-modal] [data-cy=submit-btn]'
    Then Elements visibility on the "[data-cy=change-password-modal]" match the following
      | selector                    | state   |
      | [data-cy=error-oldPassword] | visible |
      | [data-cy=error-newPassword] | visible |

    When I fill in 'input#oldPassword' with '123456'
    And I fill in 'input#newPassword' with '123456'
    And I press '[data-cy=change-password-modal] [data-cy=submit-btn]'
    Then Elements visibility on the "[data-cy=change-password-modal]" match the following
      | selector                           | state   |
      | [data-cy=error-oldPassword]        | hidden  |
      | [data-cy=error-newPassword]        | hidden  |
      | [data-cy=error-confirmNewPassword] | visible |

    When I fill in 'input#confirmNewPassword' with '123456'
    Then I should not see element '[data-cy=error-confirmNewPassword]'
    When I fill in 'input#confirmNewPassword' with '444444'
    Then I should see element '[data-cy=error-confirmNewPassword]'

    When I press 'button[data-cy=close-modal-btn]'
    Then I should not see element "[data-cy=change-password-modal]"

    When I hover over '[data-cy=change-email-popover-trigger]'
    Then I should see element '[data-cy=change-email-popover-body]'

        # Go to edit account details
    When I press '[data-cy=edit-details-btn]'

    And Elements visibility on the "[data-cy=edit-account-form]" match the following
      | selector                     | state   |
      | input#contactPhoneNumber     | visible |
      | input#firstName              | visible |
      | [data-cy=gender-radio-group] | visible |
      | button#submit                | visible |
      | [data-cy=cancel-edit-btn]    | visible |

    And I should see input '[data-cy=gender-radio-group] input >> nth=0' with value 'male'
    And I should see input '[data-cy=gender-radio-group] input >> nth=1' with value 'female'
    And I should see input '[data-cy=gender-radio-group] input >> nth=2' with value 'other'

    When I press '[data-cy=edit-account-form] [data-cy=cancel-edit-btn]'
    Then I should not see element '[data-cy=edit-account-form]'
    And Elements visibility on the page match the following
      | selector                           | state   |
      | [data-cy=edit-details-btn]         | visible |
      | [data-cy=user-name-label]          | visible |
      | [data-cy=user-contact-phone-label] | visible |
      | [data-cy=user-gender-label]        | visible |
      | [data-cy=user-email-label]         | visible |
      | [data-cy=user-password-label]      | visible |
      | [data-cy=change-password-btn]      | visible |



