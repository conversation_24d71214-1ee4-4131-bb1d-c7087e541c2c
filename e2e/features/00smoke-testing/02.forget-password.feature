Feature: Smoke testing - forget password

  Scenario:
    Given I am on the "/login" page
    When I press '[data-cy=login-form] a[data-cy=forgot-password-link]'

    Then I should see element '[data-cy=forget-password-form]'
    And I should see element '[data-cy=forget-password-form] input#email'
    And I should see element '[data-cy=forget-password-form] button#continue'
    And I should see element '[data-cy=back-to-login]'

    # Input validation
    When I fill in '[data-cy=forget-password-form] input#email' with '1'
    And I press '[data-cy=forget-password-form] button#continue'
    Then I should see element '[data-cy=error-email]'

    When I fill in '[data-cy=forget-password-form] input#email' with '<EMAIL>'
    Then I should not see element '[data-cy=error-email]'

    # Add non-existed email
    When I fill in '[data-cy=forget-password-form] input#email' with '<EMAIL>'
    And I press '[data-cy=forget-password-form] button#continue'
    Then I should see element '[data-cy=toast-error-0]'

    # reset password
    When I fill in '[data-cy=forget-password-form] input#email' with 'adminUsername'
    And I press '[data-cy=forget-password-form] button#continue'
    Then I should be on page starting with "/reset-password"
    And I should see element '[data-cy=reset-password-form]'
    Then Elements visibility on the "[data-cy=reset-password-form]" match the following
      | selector                        | state   |
      | [id="pin-input:code:0"]         | visible |
      | [id="pin-input:code:1"]         | visible |
      | [id="pin-input:code:2"]         | visible |
      | [id="pin-input:code:3"]         | visible |
      | input#password                  | visible |
      | input#confirmPassword           | visible |
      | button[data-cy=change-password] | visible |
    And I should see element 'button[data-cy=resend-code]'

    # fields validation
    When I press '[data-cy=reset-password-form] button[data-cy=change-password]'
    Then Elements visibility on the "[data-cy=reset-password-form]" match the following
      | selector                        | state   |
      | [data-cy=error-code]            | visible |
      | [data-cy=error-password]        | visible |
      | [data-cy=error-confirmPassword] | visible |

    When I fill in pinInput '[data-cy=reset-password-form]' with value '1234'
    And I fill in '[data-cy=reset-password-form] input#password' with '123456'
    And I fill in '[data-cy=reset-password-form] input#confirmPassword' with '123456'

    Then Elements visibility on the "[data-cy=reset-password-form]" match the following
      | selector                        | state  |
      | [data-cy=error-code]            | hidden |
      | [data-cy=error-password]        | hidden |
      | [data-cy=error-confirmPassword] | hidden |

    Then I clear pinInput '[data-cy=reset-password-form]' with length 4

    When I fill in pinInput '[data-cy=reset-password-form]' with value '123'
    Then I should see element '[data-cy=reset-password-form] [data-cy=error-code]'
    When I fill in '[data-cy=reset-password-form] input#confirmPassword' with '123455'
    Then I should see element '[data-cy=reset-password-form] [data-cy=error-confirmPassword]'
    And I fill in '[data-cy=reset-password-form] input#password' with '1234'
    And I should see element '[data-cy=reset-password-form] [data-cy=error-password]'
    When I fill in '[data-cy=reset-password-form] input#confirmPassword' with '1234'
    Then I should see element '[data-cy=reset-password-form] [data-cy=error-confirmPassword]'

    # reset password failure due to invalid code
    Then I clear pinInput '[data-cy=reset-password-form]' with length 3
    Given I close all toasts
    When I fill in pinInput '[data-cy=reset-password-form]' with value '1234'
    And I fill in "[data-cy=reset-password-form] input#password" with "adminPassword"
    And I fill in "[data-cy=reset-password-form] input#confirmPassword" with "adminPassword"
    And I press "[data-cy=reset-password-form] button[data-cy=change-password]"
    Then I should see element "[data-cy=toast-error-0]"

    # reset password success
    Then I clear pinInput '[data-cy=reset-password-form]' with length 4
    Given I close all toasts
    When I fill in pinInput '[data-cy=reset-password-form]' with value '1111'
    And I fill in "[data-cy=reset-password-form] input#password" with "adminPassword"
    And I fill in "[data-cy=reset-password-form] input#confirmPassword" with "adminPassword"
    And I press "[data-cy=reset-password-form] button[data-cy=change-password]"
    Then I should see element "[data-cy=toast-success-0]"
    Then I should not be on the "/reset-password" page






