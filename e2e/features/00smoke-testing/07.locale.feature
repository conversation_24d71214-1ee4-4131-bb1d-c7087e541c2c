@isExperiment=2025-2-25
Feature: Smoke testing - Locale

  Background: Background name
    Given I am logged in as an admin
    When I go to current farm page
    When Element '[data-cy=ponds-list-data]' is visible

  Scenario: naviagate to all languages
    When I press '[data-cy=account-nav-link]'
    Then I should see element '[data-cy=my-account-screen]'
    Then I should see element '[data-cy=language-selector]'
    # English
    When I press '[data-cy=language-selector]'
    Then I should see element '[data-cy=language-en]' with text 'English'

    # Spanish
    Then I should see element '[data-cy=language-es]' with text 'Español'
    When I press '[data-cy=language-es]'
    Then I should see element '[data-cy=language-selector]' with text 'Español'

    # Vietnamese
    When I press '[data-cy=language-selector]'
    Then I should see element '[data-cy=language-vi]' with text 'Tiếng Việt'
    When I press '[data-cy=language-vi]'
    Then I should see element '[data-cy=language-selector]' with text 'Tiếng V<PERSON>t'

    # # Thai
    When I press '[data-cy=language-selector]'
    Then I should see element '[data-cy=language-th]' with text 'ภาษาไทย'
    When I press '[data-cy=language-th]'
    Then I should see element '[data-cy=language-selector]' with text 'ภาษาไทย'
