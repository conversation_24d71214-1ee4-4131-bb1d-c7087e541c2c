@isExperiment=2025-2-25
Feature: Home page

    Background: go to current farm
        Given I am logged in as an admin
        When I go to current farm page

    Scenario:
        When Element '[data-cy=ponds-list-data]' is visible
        Then I should see element '[data-cy=farm-selector-btn]'
        And I should see element '[data-cy=production-ponds-list]'
        And I should see element '[data-cy=production-ponds-options-container]'
        And I should see element '[data-cy=ponds-list-filters]'
        And I should see element '[data-cy=summary-drawer-desktop] [data-cy=summary-drawer-btn]'
        And I should not see element '[data-cy=ponds-filters-form]'
        And I should not see element '[data-cy=summary-drawer-body]'

        When I press '[data-cy=ponds-list-filters]'
        Then I should see element '[data-cy=ponds-filters-form]'
        And I should see element '[data-cy=ponds-filters-form] [data-cy=cancel-btn]'

        When I press '[data-cy=ponds-filters-form] [data-cy=cancel-btn]'
        Then I should not see element '[data-cy=ponds-filters-form]'

        When I press '[data-cy=summary-drawer-desktop] [data-cy=summary-drawer-btn]'
        Then I should see element '[data-cy=summary-drawer-body]'
        When I press '[data-cy=summary-drawer-body] [data-cy=close-btn]'
        Then I should not see element '[data-cy=summary-drawer-body]'
