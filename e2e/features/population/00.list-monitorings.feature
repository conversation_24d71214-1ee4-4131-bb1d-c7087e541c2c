@isExperiment=2025-2-25
Feature: Test list of monitoring for a specific pond

  Background: go to current farm
    Given I am logged in as an admin
    When I go to current farm page
    Then I should see element '[data-cy=ponds-list-data]'

  Scenario: Go to the pond details page and stock pond if it is empty
    When I get pond 'e2e-test' data from farm 'CURRENT_TESTING_FARM._id' as 'TESTING_POND'
    When Element '[data-cy=ponds-list-data] [data-cy*="{{TESTING_POND._id}}"]' is visible
    Then I press '[data-cy=ponds-list-data] [data-cy*="{{TESTING_POND._id}}"] [data-cy=pond-name]'

    When Element '[data-cy=pond-view-screen]' is visible
    Then I press '[data-cy=pond-data-tab-btn]'
    And wait for 3 seconds
    And I press '[data-cy=stocking-tab]'
    Given Skip if selector '[data-cy=monitoring-tab]' being 'true'
    

    Then I press "[data-cy=edit-pond-stocking]"
    Then I should see element '[data-cy=stock-pond-modal]'
    Then I fill in '[data-cy=stock-pond-modal] input#cycle' with '1'
    And I select date as today for field 'input#stockedAt'
    And I fill in '[data-cy=stock-pond-modal] input#seedingQuantity' with '100'
    And I fill in '[data-cy=stock-pond-modal] input#seedingAverageWeight' with '15'
    And I press '[data-cy=stock-pond-modal] button[type=submit]'
    Then I should see element '[data-cy=production-target-form]'
    When I press '[data-cy=production-target-form] [data-cy=later-btn]'

    Then I should see element '[data-cy=monitoring-tab]'



