@isExperiment=2025-2-25
Feature: Nursery summary page - nurseries List

    Background:
        Given I am logged in as an admin
        When I go to current farm page

    Scenario: Validate the page structure
        When Element '[data-cy=ponds-list-data]' is visible
        Then I should see element '[data-cy=nursery-link]'

        When I press '[data-cy=nursery-link]'
        Then Elements visibility on the page match the following
            | selector                         | state   |
            | [data-cy=nursery-list-data]      | visible |
            | [data-cy=farm-selector-btn]      | visible |
            | [data-cy=nursery-filters-btn]    | visible |
            | [data-cy=nurseries-search-input] | visible |
            | [data-cy=nurseries-actions-btn]  | visible |
            | [data-cy=nurseries-list]         | visible |

        Then I should see one of these elements
            | eee                              |
            | [data-cy=nurseries-list-data]    |
            | [data-cy=nurseries-list-no-data] |


        When I press '[data-cy=nursery-filters-btn]'
        Then I should see element '[data-cy=nursery-filters-form]'
        And Elements visibility on the "[data-cy=nursery-filters-form]" match the following
            | selector                                        | state   |
            | input#flagged                                   | visible |
            | [id="cycleDays.moreThan"]                       | visible |
            | [id="cycleDays.lessThan"]                       | visible |
            | [data-cy=clear-btn]                             | visible |
            | [data-cy=form-actions-btn]                      | visible |
            | [data-cy=form-actions-btn] [data-cy=cancel-btn] | visible |
            | [data-cy=form-actions-btn]                      | visible |
            | [data-cy=form-actions-btn] [data-cy=apply-btn]  | visible |


        When I press '[data-cy=nursery-filters-form] [data-cy=form-actions-btn] [data-cy=cancel-btn]'
        Then I should not see element '[data-cy=nursery-filters-form]'

        When I press '[data-cy=nurseries-actions-btn]'
        Then I should see element '[data-cy=create-new-nursery-btn]'
        When I press '[data-cy=nurseries-actions-btn]'
        Then I should not see element '[data-cy=create-new-nursery-btn]'

    Scenario: verify filters
        When Element '[data-cy=ponds-list-data]' is visible
        Then I should see element '[data-cy=nursery-link]'

        When I press '[data-cy=nursery-link]'
        Then I should see element '[data-cy=nursery-list-data]'
        And I should see element '[data-cy=nursery-filters-btn]'

        When I press '[data-cy=nursery-filters-btn]'
        Then I should see element '[data-cy=nursery-filters-form]'
        And Elements visibility on the "[data-cy=nursery-filters-form]" match the following
            | selector                                        | state   |
            | input#flagged                                   | visible |
            | [id="cycleDays.moreThan"]                       | visible |
            | [id="cycleDays.lessThan"]                       | visible |
            | [data-cy=clear-btn]                             | visible |
            | [data-cy=form-actions-btn]                      | visible |
            | [data-cy=form-actions-btn] [data-cy=cancel-btn] | visible |
            | [data-cy=form-actions-btn]                      | visible |
            | [data-cy=form-actions-btn] [data-cy=apply-btn]  | visible |

        And Button '[data-cy=clear-btn]' should be disabled

        When I fill in '[data-cy=nursery-filters-form] [id="cycleDays.moreThan"]' with '30'
        Then Button '[data-cy=clear-btn]' should not be disabled

        When I fill in '[data-cy=nursery-filters-form] [id="cycleDays.lessThan"]' with '3'
        Then I should see element '[data-cy="error-cycleDays.lessThan"]'

        When I fill in '[data-cy=nursery-filters-form] [id="cycleDays.lessThan"]' with '29'
        Then I should see element '[data-cy="error-cycleDays.lessThan"]'

        When I fill in '[data-cy=nursery-filters-form] [id="cycleDays.lessThan"]' with '33'
        Then I should not see element '[data-cy="error-cycleDays.lessThan"]'

        When I fill in '[data-cy=nursery-filters-form] [id="cycleDays.lessThan"]' with '10'
        Then I should see element '[data-cy="error-cycleDays.lessThan"]'

        When I press '[data-cy=clear-btn]'
        Then I should not see element '[data-cy="error-cycleDays.lessThan"]'

        # Cancel
        When I press "[data-cy=form-actions-btn] [data-cy=cancel-btn]"
        Then I should not see element '[data-cy=nursery-filters-form]'

    Scenario: verify filters tags
        When Element '[data-cy=ponds-list-data]' is visible
        Then I should see element '[data-cy=nursery-link]'

        When I press '[data-cy=nursery-link]'
        Then I should see element '[data-cy=nursery-list-data]'

        When I press '[data-cy=nursery-filters-btn]'
        Then I should see element '[data-cy=nursery-filters-form]'

        When I press '[data-cy=nursery-filters-form] [data-cy=flaggedInput]'
        And I fill in '[data-cy=nursery-filters-form] [id="cycleDays.moreThan"]' with '33'

        When I press '[data-cy=nursery-filters-form] [data-cy=apply-btn]'
        Then I should not see element '[data-cy=nursery-filters-form]'
        Then Elements visibility on the "[data-cy=filter-tags]" match the following
            | selector                                                | state   |
            | [data-cy=filterTag-flagged]                             | visible |
            | [data-cy=filterTag-flagged] + [data-cy=remove-filter]   | visible |
            | [data-cy=filterTag-cycleDays]                           | visible |
            | [data-cy=filterTag-cycleDays] + [data-cy=remove-filter] | visible |

        When I press '[data-cy=filterTag-flagged] + [data-cy=remove-filter]'
        Then Elements visibility on the "[data-cy=filter-tags]" match the following
            | selector                                                | state   |
            | [data-cy=filterTag-flagged]                             | hidden  |
            | [data-cy=filterTag-flagged] + [data-cy=remove-filter]   | hidden  |
            | [data-cy=filterTag-cycleDays]                           | visible |
            | [data-cy=filterTag-cycleDays] + [data-cy=remove-filter] | visible |

        When I press '[data-cy=filterTag-cycleDays] + [data-cy=remove-filter]'
        Then Elements visibility on the "[data-cy=filter-tags]" match the following
            | selector                                                | state  |
            | [data-cy=filterTag-flagged]                             | hidden |
            | [data-cy=filterTag-flagged] + [data-cy=remove-filter]   | hidden |
            | [data-cy=filterTag-cycleDays]                           | hidden |
            | [data-cy=filterTag-cycleDays] + [data-cy=remove-filter] | hidden |


        When I press '[data-cy=nursery-filters-btn]'
        Then I should see element '[data-cy=nursery-filters-form]'
        And I press '[data-cy=nursery-filters-form] [data-cy=flaggedInput]'
        And I fill in '[data-cy=nursery-filters-form] [id="cycleDays.moreThan"]' with '5'
        And I fill in '[data-cy=nursery-filters-form] [id="cycleDays.lessThan"]' with '10'

        When I press '[data-cy=nursery-filters-form] [data-cy=apply-btn]'
        Then I should not see element '[data-cy=nursery-filters-form]'
        And Elements visibility on the "[data-cy=filter-tags]" match the following
            | selector                                                         | state   |
            | [data-cy=filterTag-flagged]                                      | visible |
            | [data-cy=filterTag-flagged] + [data-cy=remove-filter]            | visible |
            | [data-cy=filterTag-cycleDays]                                    | visible |
            | [data-cy=filterTag-cycleDays] + [data-cy=remove-filter]          | visible |
            | [data-cy=filterTag-cycleDays] [data-cy=cycleDays-moreThan-value] | visible |
            | [data-cy=filterTag-cycleDays] [data-cy=cycleDays-lessThan-value] | visible |

        When I press '[data-cy=filterTag-cycleDays] + [data-cy=remove-filter]'
        Then Elements visibility on the "[data-cy=filter-tags]" match the following
            | selector                                                         | state   |
            | [data-cy=filterTag-flagged]                                      | visible |
            | [data-cy=filterTag-flagged] + [data-cy=remove-filter]            | visible |
            | [data-cy=filterTag-cycleDays]                                    | hidden  |
            | [data-cy=filterTag-cycleDays] + [data-cy=remove-filter]          | hidden  |
            | [data-cy=filterTag-cycleDays] [data-cy=cycleDays-moreThan-value] | hidden  |
            | [data-cy=filterTag-cycleDays] [data-cy=cycleDays-lessThan-value] | hidden  |

        When I press '[data-cy=nursery-filters-btn]'
        Then I should see element '[data-cy=nursery-filters-form]'
        And Input '[data-cy=nursery-filters-form] input#flagged' should be checked
        And I fill in '[data-cy=nursery-filters-form] [id="cycleDays.moreThan"]' with '5'

        When I press '[data-cy=nursery-filters-form] [data-cy=apply-btn]'
        Then Elements visibility on the "[data-cy=filter-tags]" match the following
            | selector                                                         | state   |
            | [data-cy=filterTag-flagged]                                      | visible |
            | [data-cy=filterTag-flagged] + [data-cy=remove-filter]            | visible |
            | [data-cy=filterTag-cycleDays]                                    | visible |
            | [data-cy=filterTag-cycleDays] + [data-cy=remove-filter]          | visible |
            | [data-cy=filterTag-cycleDays] [data-cy=cycleDays-moreThan-value] | visible |
            | [data-cy=filterTag-cycleDays] [data-cy=cycleDays-lessThan-value] | hidden  |

        And I should see element '[data-cy=cycleDays-moreThan-value]' with text '5'


    Scenario: verify search
        When Element '[data-cy=ponds-list-data]' is visible
        Then I should see element '[data-cy=nursery-link]'

        When I press '[data-cy=nursery-link]'
        And I save 'v1ListNurseries' api response as 'NURSERIES_LIST'

        Then I should see element '[data-cy=nursery-list-data]'
        And I should see element '[data-cy=nurseries-search-input]'

        Then Skip if no nurseries in 'NURSERIES_LIST' api response

        When I fill in '[data-cy=nurseries-search-input]' with 'nonsensedata__nonsensedata'
        Then I should see element '[data-cy=nurseries-list-no-data]'

        When I clear '[data-cy=nurseries-search-input]'
        Then I should not see element '[data-cy=nurseries-list-no-data]'
        And I should see element '[data-cy=nurseries-list-data]'
