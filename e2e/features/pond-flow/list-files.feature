@skip
Feature: Test list of files for a specific pond

  Background: go to current farm
    Given I am logged in as an admin
    When I go to current farm page
    Then I should see element '[data-cy=farm-overview-container]'
    When I press '[data-cy=production-insights-link]'
    Then I should see element '[data-cy=create-new-pond-btn]'
    Then I should see element '#ponds-list'

  Scenario: Go to the pond details page and stock pond if it is empty
    When I get pond 'e2e-test' data from farm 'CURRENT_TESTING_FARM._id' as 'TESTING_POND'
    When Element '[data-cy=ponds-list-data] [data-cy*="{{TESTING_POND._id}}"]' is visible
    Then I press '[data-cy=ponds-list-data] [data-cy*="{{TESTING_POND._id}}"] [data-cy=view-pond-btn]'
    When Element '[data-cy=pond-view-screen]' is visible
    Given Skip if selector '[data-cy=files-tab]' being 'true'
    # TODO: REMOVE WHEN FIX THE BUG
    When Temp step
    # TODO: END

    When I press "[data-cy=pond-view-actions]"
    Then I press "[data-cy=stock-pond-btn]"
    Then I should see element '[data-cy=stock-pond-modal]'
    Then I fill in '[data-cy=stock-pond-modal] input#cycle' with '1'
    And I select date as today for field 'input#stockedAt'
    And I fill in '[data-cy=stock-pond-modal] input#seedingQuantity' with '100'
    And I fill in '[data-cy=stock-pond-modal] input#seedingAverageWeight' with '15'
    And I press '[data-cy=stock-pond-modal] button[type=submit]'
    Then I should see element '[data-cy=production-target-form]'
    When I press '[data-cy=production-target-form] [data-cy=later-btn]'

    Then I should see element '[data-cy=files-tab]'

  Scenario: verify list of files
    When Element '[data-cy=ponds-list-data] [data-cy*="{{TESTING_POND._id}}"]' is visible
    Then I press '[data-cy=ponds-list-data] [data-cy*="{{TESTING_POND._id}}"] [data-cy=view-pond-btn]'

    And I press '[data-cy=files-tab]' and save api response as following
      | query       | responsePath | saveAS     |
      | v1ListFiles | v1ListFiles  | LIST_FILES |

    Then I should see file list matches with 'LIST_FILES.files'


