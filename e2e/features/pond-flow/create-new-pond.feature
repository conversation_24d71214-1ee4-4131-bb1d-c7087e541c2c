@skip
Feature: Basic flow for Create, stock, harvest, and archive a pond

  Background:
    Given I am logged in as an admin
    When I go to current farm page
    Then I should see element '[data-cy=farm-overview-container]'
    When I press '[data-cy=production-insights-link]'
    Then I should see element '[data-cy=create-new-pond-btn]'
    Then I should see element '#ponds-list'

  Scenario: Add, stock, harvest and archive pond
    When I press '[data-cy=create-new-pond-btn]'
    Then wait for 1 seconds
    Then I should see element '[data-cy=add-edit-pond-modal]'
    When I press '[data-cy=add-edit-pond-modal] button[type=submit]'
    Then I should see element '[data-cy=error-name]'
    Then I should see element '[data-cy=error-size]'

    # check pond name validation
    When I fill in 'input#name' with 'Pond name more than 15 characters'
    And I press '[data-cy=add-edit-pond-modal] button[type=submit]'
    Then I should see element '[data-cy=error-name]'

    When I fill in 'input#name' with 'pond!_'
    And I press '[data-cy=add-edit-pond-modal] button[type=submit]'
    Then I should see element '[data-cy=error-name]'

    When I fill in 'input#name' with faker 'pondName' as 'new-pond'
    When I press '[data-cy=add-edit-pond-modal] button[type=submit]'
    Then I should see element '[data-cy=error-size]'
    When I react-select random option from '#superVisorId' in '[data-cy=add-edit-pond-modal]'
    And I fill in 'input#size' with faker 'number' as 'pond-size'
    Then I press '[data-cy=add-edit-pond-modal] button[type=submit]'
    And I should not see element '[data-cy=add-edit-pond-modal]'

    # navigate to pond details page
    Then I should see element "[data-cy=pond-view-screen]"

    # TODO: REMOVE WHEN FIX THE BUG
    When Temp step
    # TODO: END


    # Stock the pond
    When I press '[data-cy=pond-view-actions] [data-cy=action-menu-button]'
    Then I press '[data-cy=pond-view-actions] [role=menu] [data-cy=stock-pond-btn]'
    Then I should see element '[data-cy=stock-pond-modal]'

    When I fill in 'input#cycle' with '1'
    And I select date as today for field 'input#stockedAt'
    And I fill in 'input#seedingQuantity' with faker 'number' as 'seeding-quantity'
    And I fill in 'input#seedingAverageWeight' with faker 'number' as 'seeding-average-weight'
    And I press 'button[type=submit]'
    And I should see element '[data-cy=production-target-form]'
    When I press '[data-cy=production-target-form] [data-cy=later-btn]'
    Then I should not see element '[data-cy=stock-pond-modal]'

    # Harvest the pond
    Then wait for 1 seconds
    When I press '[data-cy=pond-view-actions] [data-cy=harvest-action-menu-button]'
    Then I press '[data-cy=pond-view-actions] [role=menu] [data-cy=harvest-pond-btn]'
    Then I should see element '[data-cy=harvest-population-modal]'

    When I select date as today for field 'input#date'
    And I fill in '[data-cy=harvest-population-modal] #weight' with '10'
    And I fill in '[data-cy=harvest-population-modal] #lbsHarvested' with '3000'
    And I press '[data-cy=harvest-population-modal] button[type=submit]'
    Then I should not see element '[data-cy=harvest-population-modal]'
    Then I should see element '[data-cy=pond-view-screen]'

    Then wait for 1 seconds

    # archive the pond
    When I press '[data-cy=action-menu-button]'
    Then I press '[data-cy=archive-pond-btn]'
    Then I should see element '[data-cy=archive-pond-modal]'
    And I should see element '[data-cy=archive-pond-modal]' with text 'new-pond'
    When I press '[data-cy=archive-pond-modal] button[data-cy=archive-pond-submit-btn]'

    And Elements visibility on the page match the following
      | selector                      | state  |
      | [data-cy=add-edit-pond-modal] | hidden |
      | [data-cy=action-menu-button]  | hidden |
