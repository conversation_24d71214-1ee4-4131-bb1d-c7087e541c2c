import { DateTime } from 'luxon';

export function getDaysDiffTillNowBasedOnTimezone(date: string, timezone: string) {
  return parseInt(
    DateTime.local({ zone: timezone })
      .diff(DateTime.fromISO(date).startOf('day').setZone(timezone), 'days')
      .days.toString()
  );
}

export function getDaysDiffBetweenDates({ baseDate, dateToCompare }: { baseDate: string; dateToCompare: string }) {
  const baseDateStart = DateTime.fromISO(baseDate).startOf('day');
  const dateToCompareStart = DateTime.fromISO(dateToCompare).startOf('day');
  return parseInt(baseDateStart.diff(dateToCompareStart, 'days').days.toString());
}
