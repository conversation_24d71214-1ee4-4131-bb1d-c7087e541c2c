import { faker, SexType } from '@faker-js/faker';
import { DateTime } from 'luxon';

export function getFakerValue(fakerType: string) {
  const [valueType, ...rest] = fakerType.split('-');
  let value = '';

  switch (valueType) {
    case 'number':
      value = faker.number.float({ min: 0, max: 1000000, multipleOf: 0.01 }).toString();
      break;

    case 'smallNumber':
      value = faker.number.int({ min: 10, max: 50 }).toString();
      break;

    case 'firstName':
      value = faker.person.firstName(rest[0] as SexType);
      break;

    case 'lastName':
      value = faker.person.lastName(rest[0] as SexType);
      break;

    case 'name':
      value = faker.person.fullName();
      break;

    case 'email':
      value = rest.length
        ? faker.internet.email({ firstName: rest[0], lastName: rest[1], provider: rest[2] })
        : faker.internet.email();
      break;

    case 'phone':
      value = faker.phone.number();
      break;

    case 'streetAddress':
      value = faker.location.streetAddress(!!rest[0]);
      break;

    case 'city':
      value = faker.location.city();
      break;

    case 'state':
      value = faker.location.state();
      break;

    case 'zipCode':
      value = faker.location.zipCode();
      break;

    case 'buildingNumber':
      value = faker.location.buildingNumber();
      break;

    case 'street':
      value = faker.location.street();
      break;

    case 'company':
      value = faker.company.name();
      break;

    case 'catchPhrase':
      value = faker.company.catchPhrase();
      break;

    case 'url':
      value = faker.internet.domainName();
      break;

    case 'bs':
      value = faker.company.buzzPhrase();
      break;

    case 'description':
      value = faker.word.words(5);
      break;

    case 'word':
      value = faker.word.noun();
      break;

    case 'words':
      value = faker.word.words(Number(rest[0]) || 10);
      break;

    case 'coordinates':
      value = `${faker.address.latitude()}, ${faker.address.longitude()}`;
      break;

    case 'latitude':
      value = faker.address.latitude().toString();
      break;

    case 'longitude':
      value = faker.address.longitude().toString();
      break;

    case 'otp':
      value = faker.helpers.replaceSymbols('######');
      break;

    case 'date':
      const randomDate = faker.date.between({ from: new Date(), to: '2030-01-01T00:00:00.000Z' });
      value = DateTime.fromJSDate(randomDate).toFormat('yyyy-MM-dd');
      break;

    case 'password':
      value = faker.internet.password();
      break;

    case 'id':
      value = faker.string.uuid();
      break;

    case 'pondName':
      value = faker.word.noun({ length: { min: 7, max: 15 } });
      break;
  }

  return value;
}
