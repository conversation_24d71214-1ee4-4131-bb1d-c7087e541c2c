import { getConfig } from './get-config';
import { createFarm, createUserMembership } from './module-farm-apis';
import { getUserMany, userLoginByEmail } from './module-user-apis';
import { Farm } from '@xpertsea/module-farm-sdk';

export async function createNewFarm(FARM_NAME: string) {
  let farmData: Partial<Farm>;
  try {
    const { adminUsername: email, adminPassword: password } = getConfig('backoffice');
    const { token: backofficeToken } = await userLoginByEmail(email, password);

    const userManyData = await getUserMany({
      token: backofficeToken,
      filter: { email: getConfig('insights').adminUsername }
    });
    const newFarmData = await createFarm({
      token: backofficeToken,
      record: { status: 'active', name: FARM_NAME }
    });
    await createUserMembership({
      token: backofficeToken,
      record: {
        role: 'admin',
        entity: 'farm',
        userId: userManyData[0]._id,
        entityId: newFarmData._id
      }
    });
    await page.waitForTimeout(5000);

    farmData = newFarmData;
  } catch (e) {
    console.error('Error in createNewFarm', e);
  }

  return farmData;
}
