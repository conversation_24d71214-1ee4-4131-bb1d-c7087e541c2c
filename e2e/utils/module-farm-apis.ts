import {
  CreateOneFarmInput,
  v1FarmListFilterApi,
  v1PondFilterApi,
  v1PondListFilterApi
} from '@xpertsea/module-farm-sdk';
import { CreateOneUserMembershipInput } from '@xpertsea/module-user-sdk';
import { moduleFarm } from '../sdks/module-farm';
import { moduleUser } from '../sdks/module-user';

export const getUserToken = async () => {
  const cookies = await page.context().cookies();
  return cookies?.find((cookie) => cookie.name === 'jwtToken' && cookie.domain === 'localhost').value;
};

export async function getListFarms({ token, filter }: { token: string; filter?: v1FarmListFilterApi }) {
  const { data } = await moduleFarm.query(
    {
      v1ListFarms: [{ filter }, { farms: { _id: 1, name: 1, eid: 1, taskConfig: 1, metadata: 1 } }]
    },
    {
      headers: {
        'Content-Type': 'application/json',
        'x-jwt-token': token
      }
    }
  );

  return data;
}

export async function createFarm({ token, record }: { token: string; record: CreateOneFarmInput }) {
  const { data } = await moduleFarm.mutation(
    {
      farmCreateOne: [
        {
          record
        },
        { record: { _id: true, eid: true, name: true } }
      ]
    },
    {
      headers: {
        'Content-Type': 'application/json',
        'x-jwt-token': token
      }
    }
  );

  return data?.farmCreateOne?.record;
}

export async function createUserMembership({ token, record }: { token: string; record: CreateOneUserMembershipInput }) {
  const { data } = await moduleUser.mutation(
    {
      userMembershipCreateOne: [{ record }, { record: { _id: true } }]
    },
    {
      headers: {
        'Content-Type': 'application/json',
        'x-jwt-token': token
      }
    }
  );

  return data?.userMembershipCreateOne?.record;
}

export async function getListPonds({ token, filter }: { token: string; filter: v1PondListFilterApi }) {
  const { data } = await moduleFarm.query(
    {
      v1ListPonds: [{ filter }, { ponds: { _id: true, name: true, eid: true } }]
    },
    {
      headers: {
        'Content-Type': 'application/json',
        'x-jwt-token': token
      }
    }
  );

  return data?.v1ListPonds;
}

export async function deletePonds({ token, filter }: { token: string; filter: v1PondFilterApi }) {
  const { data } = await moduleFarm.mutation(
    {
      v1DeletePond: [{ filter }, { success: true }]
    },
    {
      headers: {
        'Content-Type': 'application/json',
        'x-jwt-token': token
      }
    }
  );

  return data?.v1DeletePond;
}
