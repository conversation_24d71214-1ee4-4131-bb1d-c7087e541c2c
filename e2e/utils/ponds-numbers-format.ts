import { roundNumberWithDecimal } from './number';
import millify from 'millify';

export function formatAverageWeight(value: number): string {
  return Number(value).toFixed(1);
}

export function formatCV(value: number): number {
  return parseInt(String(roundNumberWithDecimal(Number(value) * 100, 0)), 10);
}

type FormatNumberOptions = {
  compact?: boolean;
  shouldRound?: boolean;
  lang?: string;
  fractionDigits: number;
  asNumber?: boolean;
  shouldAddZeros?: boolean;
};

export function formatNumber(num: number, options: FormatNumberOptions) {
  const {
    lang = 'en',
    compact = false,
    fractionDigits = 0,
    asNumber = false,
    shouldRound = true,
    shouldAddZeros = true
  } = options;
  if (typeof num !== 'number') return num;

  if (compact) return millify(num, { locales: lang });

  let result: string | number;

  if (!shouldRound) {
    const dec = Math.pow(10, fractionDigits);
    result = Math.trunc(num * dec) / dec;
  }

  if (shouldRound) result = num.toFixed(fractionDigits);

  if (asNumber) return Number(result);

  let localeStringOptions: Intl.NumberFormatOptions = {};
  if (shouldAddZeros) {
    localeStringOptions = {
      ...localeStringOptions,
      minimumFractionDigits: fractionDigits,
      maximumFractionDigits: fractionDigits
    };
  }

  return Number(result).toLocaleString(lang, localeStringOptions);
}
