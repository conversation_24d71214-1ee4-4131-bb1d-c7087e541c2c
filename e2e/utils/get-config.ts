const backofficeDevConfig = {
  e2e: {
    baseUrl: 'https://backoffice.xpertseadev.com',
    specPattern: 'cypress/e2e/backoffice/**/*.{feature,features}'
  },
  env: {
    runtimeEnv: 'dev',
    adminUsername: '<EMAIL>',
    adminPassword: 'qa-test-automation-admin12ABC',
    graphqlUrl: '/api/**/graphql',
    baseUrlBackoffice: 'https://backoffice.xpertseadev.com',
    baseUrlInsights: 'https://xpertseadev.com/en'
  }
};

const backofficeLocalConfig = {
  e2e: {
    baseUrl: 'http://localhost:5500',
    specPattern: 'cypress/e2e/backoffice/**/*.{feature,features}'
  },
  env: {
    runtimeEnv: 'local',
    adminUsername: '<EMAIL>',
    adminPassword: 'local123',
    graphqlUrl: '/graphql',
    baseUrlBackoffice: 'http://localhost:5500',
    baseUrlInsights: 'http://localhost:4500/en'
  }
};

const backofficeCIConfig = {
  e2e: {
    baseUrl: 'http://localhost:5500',
    specPattern: 'cypress/e2e/backoffice/**/*.{feature,features}'
  },
  env: {
    runtimeEnv: 'ci',
    adminUsername: '<EMAIL>',
    adminPassword: 'qa-test-automation-admin12ABC',
    graphqlUrl: '/api/**/graphql',
    baseUrlBackoffice: 'http://localhost:5500',
    baseUrlInsights: 'http://localhost:4500/en'
  }
};

const insightsDevConfig = {
  e2e: {
    baseUrl: 'https://xpertseadev.com/en'
  },
  env: {
    runtimeEnv: 'dev',
    adminUsername: '<EMAIL>',
    adminPassword: 'qa-test-automation-insights-dev-12ABC',
    baseUrlBackoffice: 'https://backoffice.xpertseadev.com',
    baseUrlInsights: 'https://xpertseadev.com/en'
  }
};

const insightsLocalConfig = {
  e2e: {
    baseUrl: 'http://localhost:4500/en',
    specPattern: 'cypress/e2e/insights/**/*.{feature,features}'
  },
  env: {
    runtimeEnv: 'local',
    adminUsername: '<EMAIL>',
    adminPassword: 'qa-test-automation-insights-dev-12ABC',
    baseUrlBackoffice: 'http://localhost:5500',
    baseUrlInsights: 'http://localhost:4500/en'
  }
};

const insightsCIConfig = {
  e2e: {
    baseUrl: 'http://localhost:4500/en',
    specPattern: 'cypress/e2e/insights/**/*.{feature,features}'
  },
  env: {
    runtimeEnv: 'ci',
    adminUsername: '<EMAIL>',
    adminPassword: 'qa-test-automation-insights-dev-12ABC',
    baseUrlBackoffice: 'http://localhost:5500',
    baseUrlInsights: 'http://localhost:4500/en'
  }
};

export function getConfig(application: 'backoffice' | 'insights' = 'insights') {
  let currentConfig = insightsLocalConfig.env;
  const runtimeEnv = process.env.TEST_ENV ?? 'local';
  if (runtimeEnv === 'local') {
    if (application === 'backoffice') {
      currentConfig = backofficeLocalConfig.env;
    }
    if (application === 'insights') {
      currentConfig = insightsLocalConfig.env;
    }
  }
  if (runtimeEnv === 'dev') {
    if (application === 'backoffice') {
      currentConfig = backofficeDevConfig.env;
    }
    if (application === 'insights') {
      currentConfig = insightsDevConfig.env;
    }
  }
  if (runtimeEnv === 'ci') {
    if (application === 'insights') {
      currentConfig = insightsCIConfig.env;
    } else {
      currentConfig = backofficeCIConfig.env;
    }
  }

  return currentConfig;
}
