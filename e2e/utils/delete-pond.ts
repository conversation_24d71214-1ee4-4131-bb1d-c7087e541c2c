import { deletePonds, getUserToken } from './module-farm-apis';

export async function deletePondWithId(pondId: string): Promise<boolean> {
  const token = await getUserToken();

  try {
    const res = await deletePonds({
      token,
      filter: {
        pondId
      }
    });
    return res.success;
  } catch (e) {
    console.warn(`Failed to delete pond with ID => ${pondId}`);
    return false;
  }
}

export async function deletePondWithEid(pondEid: number): Promise<boolean> {
  const token = await getUserToken();
  try {
    const res = await deletePonds({
      token,
      filter: {
        pondEid
      }
    });
    return res.success;
  } catch (e) {
    console.warn(`Failed to delete pond with EID => ${pondEid}`);
    return false;
  }
}
