import { moduleUser } from '../sdks/module-user';
import { FilterFindManyUserInput } from '@xpertsea/module-user-sdk';

export const userLoginByEmail = async (email: string, password: string) => {
  const { data } = await moduleUser.query({
    v1LogInByEmail: [
      { email, password, rememberMe: true },
      { token: true, user: { _id: true, firstName: 1, lastName: 1, email: 1, gender: 1 } }
    ]
  });
  return data?.v1LogInByEmail;
};

export const getUserMany = async ({ token, filter }: { token: string; filter: FilterFindManyUserInput }) => {
  const { data } = await moduleUser.query(
    {
      userMany: [{ filter }, { _id: true, email: true, firstName: true, lastName: true }]
    },
    {
      headers: {
        'Content-Type': 'application/json',
        'x-jwt-token': token
      }
    }
  );
  return data?.userMany;
};
