import axios from 'axios';

const axiosInstance = axios.create({
  baseURL: 'https://xpertseadev.com/api/',
  headers: { 'x-secret': 'dev-secret-create-population-test-data' }
});

type CreatePopulationTestDataPropsType = {
  pondId: string;
  numberOfMonitorings: number;
  growthAverageWeight?: number;
  daysBetweenMonitorings?: number;
  weightCv?: number;
  targetWeight?: number;
  conditions?: string[];
};

type CreatePopulationTestDataResponseType = {
  populationId: string;
  webUrl: string;
  backofficeUrl: string;
};

export async function createPopulationTestData(data: CreatePopulationTestDataPropsType) {
  let results: CreatePopulationTestDataResponseType = {} as CreatePopulationTestDataResponseType;
  try {
    const res = await axiosInstance({
      url: 'module-farm/v1-create-population-test-data',
      method: 'POST',
      data
    });

    results = res.data;
  } catch (e) {
    console.error(e.message);
  }

  return results;
}
