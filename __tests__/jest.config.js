const nextJest = require('next/jest');
const { pathsToModuleNameMapper } = require('ts-jest');
const { compilerOptions } = require('./../tsconfig');

process.env.TZ = 'UTC';

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files in your test environment
  dir: './'
});
const paths = compilerOptions.paths || {};

const customJestConfig = {
  preset: 'ts-jest',
  testTimeout: 50000,
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js', '<rootDir>/setup-tests.ts'],
  moduleDirectories: ['node_modules', '<rootDir>/'],
  testEnvironment: 'jsdom',
  testMatch: ['**/__tests__/**/*.(test|spec).(ts|tsx|js|jsx)'],
  collectCoverage: false,
  coverageReporters: ['json', 'html'],
  moduleNameMapper: pathsToModuleNameMapper(paths, {
    prefix: '<rootDir>/../'
  }),
  transform: {
    '^.+\\.ts?$': ['ts-jest', { isolatedModules: true }]
  }
};

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(customJestConfig);
