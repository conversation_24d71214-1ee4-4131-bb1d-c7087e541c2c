import '@testing-library/jest-dom';

Object.defineProperty(window, 'structuredClone', {
  writable: true,
  value: (obj: unknown) => JSON.parse(JSON.stringify(obj))
});

Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn()
  }))
});

window.MessageChannel = jest.fn().mockImplementation(() => {
  let onmessage: (arg0: { data: unknown }) => void;
  return {
    port1: {
      set onmessage(cb: typeof onmessage) {
        onmessage = cb;
      }
    },
    port2: {
      postMessage: (data: unknown) => {
        onmessage?.({ data });
      }
    }
  };
});
