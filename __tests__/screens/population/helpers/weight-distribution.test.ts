import { getRangeFromWeightDistribution } from '@screens/population/helpers/weight-distribution';
import { formatNumber } from '@utils/number';
import { getTrans } from '@i18n/get-trans';

describe('Given getRangeFromWeightDistribution', () => {
  let weights: number[];

  describe('when has a valid weights distribution', () => {
    beforeEach(() => {
      weights = [
        9.624509403569922, 10.12580772720014, 10.203350250327054, 10.386753674338387, 10.787617201449137,
        11.353542671029132, 11.652295795353794, 11.96285780706333, 11.96385426689843, 12.156006804457386,
        12.253743249795143, 12.533964145967165, 12.668713599981977, 12.898931743497275, 13.4194772322508,
        13.488170931527717, 13.755579692978534, 13.916243641645941, 14.102631841293086, 14.348831442121416,
        14.36847791529269, 14.641359063440527, 14.6839032645879, 14.803025618284563, 14.845670611943344,
        14.894011703385594, 15.081255455161834, 15.33068290611705, 15.381103627466802, 15.411451154364338,
        15.45995407852535, 15.466592674876004, 15.521519061889151, 15.625952228779832, 15.679375153286628,
        15.700198653690562, 15.748210487504423, 15.854852260664103, 15.874934334641647, 15.93161225694025,
        15.981101750306706, 15.998197946604094, 16.040428456134443, 16.243570252172457, 16.27987075704641,
        16.397330789834683, 16.445648494744933, 16.45228110610188, 16.590990632869282, 16.704324502619944,
        16.831670644417677, 16.974592525230907, 17.10497554592193, 17.231935692546568, 17.298766198815226,
        17.31092839747323, 17.47862332672978, 17.507217059794797, 17.58948004684392, 17.719596539469894,
        17.731217472233887, 17.866013941222562, 17.90767173856761, 18.009688457373954, 18.06940111150255,
        18.246586310225794, 18.35118685158124, 18.37489982284924, 18.460562967773015, 18.57940082212947,
        18.657267560684257, 18.754552769236152, 18.755522466101866, 18.771339865012745, 18.858791070741294,
        18.93911077503562, 18.96100898288975, 19.01914612713996, 19.15564555361586, 19.206111543943145,
        19.505366154392156, 19.596421037551902, 19.60747332424, 19.788315296006083, 19.820259654153684,
        19.84583422789241, 19.855302619709356, 19.86234760115396, 19.89349628895377, 19.99464718202649,
        19.998180738164873, 20.027528123308397, 20.06331998564, 20.07743196568226, 20.09205918732256,
        20.101813835507535, 20.19397060703597, 20.34636250563124, 20.44062425079642, 20.564353561509307,
        20.783434695630397, 20.806918870527177, 20.949729885319748, 20.951700183751434, 21.052461084589403,
        21.06547259488732, 21.07815203707474, 21.130769820719607, 21.24621908428435, 21.260813542294606,
        21.31453371586592, 21.435492065450333, 21.45486741172226, 21.49971464886718, 21.570399720649156,
        21.578814376301587, 21.634278602146104, 21.670639390824043, 21.68127650755947, 21.740950741049506,
        21.814412396890965, 21.990541606514107, 22.00431023190456, 22.327319815462857, 22.328963299735417,
        22.329834788696974, 22.338057655806765, 23.058823314684087, 23.47999069983299, 24.284088660995934
      ];
    });

    describe.each([
      [0.8, 14.6839032645879, 22.329834788696974],
      [0.95, 10.203350250327054, 22.329834788696974],
      [0.5, 17.298766198815226, 21.68127650755947]
    ])('and using the proportion: %s', (proportion, min, max) => {
      it(`should return the min: ${min} and max: ${max} rage distribution`, () => {
        const { trans } = getTrans();
        const result = getRangeFromWeightDistribution(weights, proportion, 'en');
        const rangeValue =
          !min || !max
            ? ''
            : `${formatNumber(min, { lang: 'en', fractionDigits: 2 })} - ${formatNumber(max, { lang: 'en', fractionDigits: 2 })}`;
        expect(result).toEqual({ min, max, rangeValue });
      });
    });
  });
});
