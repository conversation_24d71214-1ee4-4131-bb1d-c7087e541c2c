/*eslint @typescript-eslint/no-explicit-any: "off"*/

import { syncProductionDaysAndHarvestDate } from '@screens/pond/helpers/validate-production-target-form';

const formState: any = {
  errors: {},
  values: {}
};
const setValue = (name: string, value: any) => {
  formState.values[name] = value;
};

const setup = ({ input, stockedAt }: { input?: { name: string; value: any }; stockedAt: Date }) => {
  const data = input ? { ...formState.values, [input.name]: input.value } : { ...formState.values };
  syncProductionDaysAndHarvestDate({
    data,
    input,
    setValue: (name: string, value: any) => {
      syncProductionDaysAndHarvestDate({
        data,
        input: { name, value, type: undefined },
        setValue,
        stockedAt
      });
      formState.values = input ? { ...formState.values, [name]: value } : { ...formState.values };
    },
    stockedAt
  });
  formState.values = input ? { ...formState.values, [input.name]: input.value } : { ...formState.values };
};

const runValidation = ({ userInputs, stockedAt }: { userInputs: any; stockedAt: Date }) => {
  for (const input of userInputs) {
    setup({ input, stockedAt });
  }
};

describe('validateSyncProductionDaysAndHarvestDate', () => {
  it('should set the harvest date when user change the production days', () => {
    const stockedAt = new Date('2022-01-01');
    runValidation({ userInputs: [{ name: 'productionDays', type: 'change', value: 10 }], stockedAt });
    expect(formState.values.harvestDate?.toISOString()).toEqual('2022-01-11T00:00:00.000Z');

    runValidation({ userInputs: [{ name: 'productionDays', type: 'change', value: 35 }], stockedAt });
    expect(formState.values.harvestDate?.toISOString()).toEqual('2022-02-05T00:00:00.000Z');
  });

  it('should set the harvest date to null if production days is removed', () => {
    const stockedAt = new Date('2022-01-01');
    runValidation({ userInputs: [{ name: 'productionDays', type: 'change', value: 10 }], stockedAt });
    expect(formState.values.harvestDate?.toISOString()).toEqual('2022-01-11T00:00:00.000Z');

    runValidation({ userInputs: [{ name: 'productionDays', type: 'change', value: '' }], stockedAt });
    expect(formState.values.harvestDate).toBeNull();
  });
});
