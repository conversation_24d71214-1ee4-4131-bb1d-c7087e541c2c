import { getGrowthTargetAlert, GetGrowthTargetAlertParams } from '@screens/pond/helpers/helper-growth-target-alert';
import { PopulationGrowthBandsRanges, PopulationHistory, PopulationPrediction } from '@xpertsea/module-farm-sdk';

function createProps({
  history,
  prediction,
  ranges
}: {
  history?: { date: string; averageWeight: number }[];
  prediction?: { date: string; averageWeight: number }[];
  ranges?: { date: string; max: number; min: number }[];
}): GetGrowthTargetAlertParams {
  return {
    population: {
      history: history as PopulationHistory[],
      prediction: prediction as PopulationPrediction[],
      growthBands: {
        ranges: ranges as PopulationGrowthBandsRanges[],
        targetWeightPredictedDate: '2023-04-23',
        targetWeight: 35
      },
      growthTarget: {
        productionDays: 95
      },
      stockedAt: '2022-11-03T00:00:00.000Z',
      lastMonitoringDate: '2023-01-13T11:55:59.756Z'
    },
    farmTimeZone: ''
    // todo fix data to match input from db { alert: { caseNumber: 1, isRed: false, isGreen: true, isYellow: false } }
  } as unknown as GetGrowthTargetAlertParams;
}

// todo fix tests
xdescribe('Growth target alert', () => {
  it('should validate 1st case green alert as true if current monitoring awb is above range, current prediction is above range, last prediction is above range and target date is bellow prediction date', () => {
    const data = createProps({
      history: [{ date: '2022-09-28', averageWeight: 10 }],
      prediction: [
        { date: '2022-09-28', averageWeight: 10 },
        { date: '2022-12-30', averageWeight: 36 }
      ],
      ranges: [
        { date: '2022-09-28', max: 12, min: 9 },
        { date: '2022-12-28', max: 45, min: 36 }
      ]
    });

    const growthTargetAlert = getGrowthTargetAlert(data);

    expect(growthTargetAlert.isGreen).toEqual(true);
    expect(growthTargetAlert.isRed).toEqual(false);
    expect(growthTargetAlert.isYellow).toEqual(false);
    expect(growthTargetAlert.caseNumber).toEqual(1);
  });

  it('should validate 1st case green alert as true if current monitoring awb is above range, current prediction is above range and last prediction is above range', () => {
    const data = createProps({
      history: [{ date: '2022-09-28', averageWeight: 10 }],
      prediction: [
        { date: '2022-09-28', averageWeight: 10 },
        { date: '2022-12-30', averageWeight: 36 }
      ],
      ranges: [
        { date: '2022-09-28', max: 12, min: 9 },
        { date: '2022-12-30', max: 45, min: 36 }
      ]
    });

    const growthTargetAlert = getGrowthTargetAlert(data);

    expect(growthTargetAlert.isGreen).toEqual(true);
    expect(growthTargetAlert.isRed).toEqual(false);
    expect(growthTargetAlert.isYellow).toEqual(false);
    expect(growthTargetAlert.caseNumber).toEqual(1);
  });

  it('should validate 2nd case  red alert as true if current monitoring awb is below range, current prediction is below range and last prediction is below range', () => {
    const data = createProps({
      history: [{ date: '2022-12-28', averageWeight: 10 }],
      prediction: [
        { date: '2022-09-28', averageWeight: 28.75750705901933 },
        { date: '2022-12-28', averageWeight: 28.91180736927895 }
      ],
      ranges: [
        { date: '2022-09-28', max: 34.52549418693781, min: 34.386594044048586 },
        { date: '2022-12-28', max: 34.76468393499251, min: 34.695491111813595 }
      ]
    });

    const growthTargetAlert = getGrowthTargetAlert(data);

    expect(growthTargetAlert.frcHigherEnd).toEqual(16.84624569597883);
    expect(growthTargetAlert.frcLowerEnd).toEqual(10.107747417587298);
    expect(growthTargetAlert.expectedCycleGrowthByWeek).toEqual(2.5789473684210527);
    expect(growthTargetAlert.diffCycleGrowthPercentage).toEqual(21.057807119973535);
    expect(growthTargetAlert.daysTillReachTargetWeight).toEqual(76);
    expect(growthTargetAlert.isGreen).toEqual(false);
    expect(growthTargetAlert.isRed).toEqual(true);
    expect(growthTargetAlert.isYellow).toEqual(false);
    expect(growthTargetAlert.caseNumber).toEqual(2);
  });

  it('should validate 3rd case yellow alert as true if current monitoring awb is above range, current prediction is below range and last prediction is below range', () => {
    const data = createProps({
      history: [{ date: '2022-09-28', averageWeight: 12 }],
      prediction: [
        { date: '2022-09-28', averageWeight: 8 },
        { date: '2022-12-30', averageWeight: 30 }
      ],
      ranges: [
        { date: '2022-09-28', max: 16, min: 11 },
        { date: '2022-12-30', max: 45, min: 36 }
      ]
    });

    const growthTargetAlert = getGrowthTargetAlert(data);

    expect(growthTargetAlert.isRed).toEqual(false);
    expect(growthTargetAlert.isGreen).toEqual(false);
    expect(growthTargetAlert.isYellow).toEqual(true);
    expect(growthTargetAlert.caseNumber).toEqual(3);
  });

  it('should validate 4th yellow alert as true if current monitoring awb is below range, current prediction is above range and last prediction is above range', () => {
    const data = createProps({
      history: [{ date: '2022-09-28', averageWeight: 8 }],
      prediction: [
        { date: '2022-09-28', averageWeight: 16 },
        { date: '2022-12-30', averageWeight: 40 }
      ],
      ranges: [
        { date: '2022-09-28', max: 12, min: 12 },
        { date: '2022-12-30', max: 45, min: 36 }
      ]
    });

    const growthTargetAlert = getGrowthTargetAlert(data);

    expect(growthTargetAlert.isRed).toEqual(false);
    expect(growthTargetAlert.isGreen).toEqual(false);
    expect(growthTargetAlert.isYellow).toEqual(true);
    expect(growthTargetAlert.caseNumber).toEqual(4);
  });

  it('should validate 5th yellow alert as true if current monitoring awb is above range, current prediction is above range and last prediction is below range', () => {
    const data = createProps({
      history: [{ date: '2022-09-28', averageWeight: 12 }],
      prediction: [
        { date: '2022-09-28', averageWeight: 10 },
        { date: '2022-12-30', averageWeight: 2 }
      ],
      ranges: [
        { date: '2022-09-28', max: 12, min: 4 },
        { date: '2022-12-30', max: 45, min: 36 }
      ]
    });

    const growthTargetAlert = getGrowthTargetAlert(data);

    expect(growthTargetAlert.isRed).toEqual(false);
    expect(growthTargetAlert.isGreen).toEqual(false);
    expect(growthTargetAlert.isYellow).toEqual(true);
    expect(growthTargetAlert.caseNumber).toEqual(5);
  });

  it('should validate 6th case yellow alert as true if current monitoring awb is below range, current prediction is above range and last prediction is below range', () => {
    const data = createProps({
      history: [{ date: '2022-09-28', averageWeight: 4 }],
      prediction: [
        { date: '2022-09-28', averageWeight: 44 },
        { date: '2022-12-30', averageWeight: 2 }
      ],
      ranges: [
        { date: '2022-09-28', max: 12, min: 12 },
        { date: '2022-12-30', max: 45, min: 36 }
      ]
    });

    const growthTargetAlert = getGrowthTargetAlert(data);

    expect(growthTargetAlert.isRed).toEqual(false);
    expect(growthTargetAlert.isGreen).toEqual(false);
    expect(growthTargetAlert.isYellow).toEqual(true);
    expect(growthTargetAlert.caseNumber).toEqual(6);
  });

  it('should validate 7th case yellow alert as true if current monitoring awb is above range, current prediction is below range and last prediction is above range', () => {
    const data = createProps({
      history: [{ date: '2022-09-28', averageWeight: 8 }],
      prediction: [
        { date: '2022-09-28', averageWeight: 3 },
        { date: '2022-12-30', averageWeight: 40 }
      ],
      ranges: [
        { date: '2022-09-28', max: 12, min: 4 },
        { date: '2022-12-30', max: 45, min: 36 }
      ]
    });

    const growthTargetAlert = getGrowthTargetAlert(data);

    expect(growthTargetAlert.isRed).toEqual(false);
    expect(growthTargetAlert.isGreen).toEqual(false);
    expect(growthTargetAlert.isYellow).toEqual(true);
    expect(growthTargetAlert.caseNumber).toEqual(7);
  });

  it('should validate 8th case yellow alert as true if current monitoring awb is below range, current prediction is below range and last prediction is above range', () => {
    const data = createProps({
      history: [{ date: '2022-09-28', averageWeight: 4 }],
      prediction: [
        { date: '2022-09-28', averageWeight: 6 },
        { date: '2022-12-30', averageWeight: 35 } // 5% less
      ],
      ranges: [
        { date: '2022-09-28', max: 12, min: 12 },
        { date: '2022-12-30', max: 45, min: 36 }
      ]
    });

    const growthTargetAlert = getGrowthTargetAlert(data);

    expect(growthTargetAlert.isRed).toEqual(false);
    expect(growthTargetAlert.isGreen).toEqual(false);
    expect(growthTargetAlert.isYellow).toEqual(true);
    expect(growthTargetAlert.caseNumber).toEqual(8);
  });
});

xdescribe('Growth target should not fail on missing data', () => {
  it('return all cases as false and do not fail if data is null', () => {
    const data = [
      createProps({
        history: null,
        prediction: [{ date: '2022-09-28', averageWeight: 10 }],
        ranges: [{ date: '2022-09-28', max: 12, min: 9 }]
      }),
      createProps({
        history: [{ date: '2022-09-28', averageWeight: 10 }],
        prediction: [{ date: '2022-09-28', averageWeight: 10 }],
        ranges: null
      }),
      createProps({
        history: [{ date: '2022-09-28', averageWeight: 10 }],
        prediction: null,
        ranges: [{ date: '2022-09-28', max: 12, min: 9 }]
      }),
      createProps({
        history: null,
        prediction: null,
        ranges: null
      })
    ];

    data.forEach((ele) => {
      const growthTargetAlert = getGrowthTargetAlert(ele);

      expect(growthTargetAlert).toBeNull();
    });
  });

  it('return all cases as false and do not fail if data is empty array', () => {
    const data = [
      createProps({
        history: [],
        prediction: [{ date: '2022-09-28', averageWeight: 10 }],
        ranges: [{ date: '2022-09-28', max: 12, min: 9 }]
      }),
      createProps({
        history: [{ date: '2022-09-28', averageWeight: 10 }],
        prediction: [{ date: '2022-09-28', averageWeight: 10 }],
        ranges: []
      }),
      createProps({
        history: [{ date: '2022-09-28', averageWeight: 10 }],
        prediction: [],
        ranges: [{ date: '2022-09-28', max: 12, min: 9 }]
      }),
      createProps({
        history: [],
        prediction: [],
        ranges: []
      })
    ];

    data.forEach((ele) => {
      const growthTargetAlert = getGrowthTargetAlert(ele);
      expect(growthTargetAlert).toBeNull();
    });
  });

  it('should not fail even if no thing matches', () => {
    const data = createProps({
      history: [{ date: '2022-01-28', averageWeight: 10 }],
      prediction: [{ date: '2022-09-28', averageWeight: 10 }],
      ranges: [{ date: '2022-09-30', max: 12, min: 9 }]
    });

    const growthTargetAlert = getGrowthTargetAlert(data);

    expect(growthTargetAlert.isGreen).toEqual(false);
    expect(growthTargetAlert.isRed).toEqual(false);
    expect(growthTargetAlert.isYellow).toEqual(false);
    expect(growthTargetAlert.caseNumber).toEqual(undefined);
  });
});
