/*eslint @typescript-eslint/no-explicit-any: "off"*/

import { validateProductionTargetForm } from '@screens/pond/helpers/validate-production-target-form';

const formState = {
  errors: {} as Record<string, any>
};
const resetForm = () => {
  formState.errors = {};
};
const clearErrorMap = (name: string) => {
  delete formState.errors[name];
};
const setErrorMap = (name: any, value: { message: string }) => {
  formState.errors[name] = value;
};
const trans = (t: string) => {
  return t;
};

const setup = ({ data }: { data?: any }) =>
  validateProductionTargetForm({
    data,
    clearErrorMap,
    setErrorMap,
    defaultMaxTargetWeight: 50,
    defaultMinDaysOfProduction: 10,
    defaultMinTargetWeight: 10,
    minProductionDays: 46,
    minTargetWeight: 12,
    setLinearGrowth: () => {},
    trans
  });

beforeEach(() => {
  resetForm();
});

describe('validateProductionTargetForm', () => {
  it('should validate production target form with max weight error', () => {
    const data = { productionDays: 20, weight: 200 };

    setup({ data });
    expect(formState.errors['weight']).toEqual('t_max_target_weight');
    expect(formState.errors['linearGrowth']).toEqual(undefined);
  });
  it('should validate production target form with min weight error', () => {
    const data = { productionDays: 20, weight: 2 };

    setup({ data });
    expect(formState.errors['weight']).toEqual('t_min_monitored_weight');
    expect(formState.errors['linearGrowth']).toEqual(undefined);
  });
  it('should validate production target form with min productionDays error', () => {
    const data = { productionDays: 2, weight: 22 };

    setup({ data });
    expect(formState.errors['productionDays']).toEqual('t_min_target_stocking_days');
    expect(formState.errors['linearGrowth']).toEqual(undefined);
  });
  it('should validate production target form with linearGrowth error', () => {
    const data = { productionDays: 46, weight: 50 };

    setup({ data });
    expect(formState.errors['linearGrowth']).toEqual('t_growth_warning_prefix');
    expect(formState.errors['productionDays']).toEqual(undefined);
    expect(formState.errors['weight']).toEqual(undefined);
  });

  it('when input correct values should return a valid form', () => {
    const data = { productionDays: 46, weight: 22 };
    expect(setup({ data })).toEqual(true);
  });
});
