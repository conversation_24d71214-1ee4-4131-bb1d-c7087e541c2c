import { getBulkUpdatedNurseries } from '@screens/nursery-data-updater/helper/get-bulk-updated-nurseries';

describe('getBulkUpdatedNurseries', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return correct bulk update data for valid inputs', () => {
    const data: any = [
      {
        feedGiven: [{ feedId: '1', totalLbs: 100 }],
        otherDirectCost: 200,
        populationInfo: {
          nurseryId: '123',
          stockedAt: '2024-01-01',
          feedData: [{ date: '2024-01-02', otherDirectCost: 100, feed: [] as any, totalLbs: 0 }]
        }
      }
    ];
    const weekDates = ['2024-01-01'];

    const result = getBulkUpdatedNurseries(data, weekDates);

    expect(result).toHaveLength(1);
    expect(result[0].filter).toEqual({ nurseryId: '123' });
    expect(result[0].set.feedData).toEqual([
      { date: '2024-01-02', otherDirectCost: 100, feed: [], totalLbs: 0 },
      { date: '2024-01-01', otherDirectCost: 200, feed: [{ feedTypeId: '1', totalLbs: 220.462 }], totalLbs: 220.462 }
    ]);
  });

  it('should return original feed data when no new feedGiven or otherDirectCost edits exist', () => {
    const data: any = [
      {
        feedGiven: undefined, // No feed edits
        otherDirectCost: undefined, // No cost edits
        populationInfo: {
          nurseryId: '123',
          stockedAt: '2024-01-01',
          feedData: [{ date: '2024-01-02', otherDirectCost: 100, feed: [], totalLbs: 0 }],
          originalFeedGiven: [{ feedId: '1', totalLbs: 50 }],
          originalOtherDirectCost: 150
        }
      }
    ];
    const weekDates = ['2024-01-01'];

    const result = getBulkUpdatedNurseries(data, weekDates);

    expect(result).toHaveLength(1);
    expect(result[0].filter).toEqual({ nurseryId: '123' });
    expect(result[0].set.feedData).toEqual([
      { date: '2024-01-02', otherDirectCost: 100, feed: [], totalLbs: 0 },
      { date: '2024-01-01', feed: [] }
    ]);
  });

  it('should return an empty array if no data is provided', () => {
    const result = getBulkUpdatedNurseries([], ['2024-01-01']);
    expect(result).toEqual([]);
  });
});
