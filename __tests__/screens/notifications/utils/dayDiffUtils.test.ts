import * as i18n from '@i18n/get-trans';
import * as farmDates from '@screens/farm/helpers/farm-dates';
import { getDaysUntilDate, getDaysSinceDate } from '@screens/notifications/utils/dayDiffUtils';
import * as numberUtils from '@utils/number';

jest.mock('@i18n/get-trans', () => ({ getTrans: jest.fn() }));

jest.mock('@screens/farm/helpers/farm-dates', () => ({
  getDaysDiffTillNowBasedOnTimezone: jest.fn(),
  getDaysDiffBetweenDates: jest.fn()
}));

jest.mock('@utils/number', () => ({ formatNumber: jest.fn() }));

describe('getDaysSinceDate', () => {
  const mockTrans = jest.fn();
  const lang = 'en';
  const timezone = 'UTC';
  const date = '2024-04-01';

  beforeEach(() => {
    jest.clearAllMocks();
    (i18n.getTrans as jest.Mock).mockReturnValue({ trans: mockTrans });
    (numberUtils.formatNumber as jest.Mock).mockImplementation((num) => String(num));
  });

  it('should return "-" for future dates (negative diff)', () => {
    (farmDates.getDaysDiffTillNowBasedOnTimezone as jest.Mock).mockReturnValue(-1);

    const result = getDaysSinceDate({ date, timezone, lang });

    expect(result).toBe('-');
  });

  it('should return "today" for 0 days difference', () => {
    (farmDates.getDaysDiffTillNowBasedOnTimezone as jest.Mock).mockReturnValue(0);
    mockTrans.mockReturnValue('Today');

    const result = getDaysSinceDate({ date, timezone, lang });

    expect(result).toBe('Today');
    expect(mockTrans).toHaveBeenCalledWith('t_today');
  });

  it('should return "yesterday" for 1 day difference', () => {
    (farmDates.getDaysDiffTillNowBasedOnTimezone as jest.Mock).mockReturnValue(1);
    mockTrans.mockReturnValue('Yesterday');

    const result = getDaysSinceDate({ date, timezone, lang });

    expect(result).toBe('Yesterday');
    expect(mockTrans).toHaveBeenCalledWith('t_yesterday');
  });

  it('should return plural translation for > 1 day', () => {
    (farmDates.getDaysDiffTillNowBasedOnTimezone as jest.Mock).mockReturnValue(5);
    mockTrans.mockReturnValue('5 days ago');

    const result = getDaysSinceDate({ date, timezone, lang });

    expect(result).toBe('5 days ago');
    expect(mockTrans).toHaveBeenCalledWith('t_days_ago_plural', { count: '5' });
  });
});

describe('getDaysUntilDate', () => {
  const mockTrans = jest.fn();
  const lang = 'en';
  const date = '2024-04-20';

  beforeEach(() => {
    jest.clearAllMocks();
    (i18n.getTrans as jest.Mock).mockReturnValue({ trans: mockTrans });
    (numberUtils.formatNumber as jest.Mock).mockImplementation((num) => String(num));
  });

  it('should return "-" for past dates (<= 0)', () => {
    (farmDates.getDaysDiffBetweenDates as jest.Mock).mockReturnValue(-3);

    const result = getDaysUntilDate({ date, lang });

    expect(result).toBe('-');
  });

  it('should return "today" for 0 days left', () => {
    (farmDates.getDaysDiffBetweenDates as jest.Mock).mockReturnValue(0);
    mockTrans.mockReturnValue('Today');

    const result = getDaysUntilDate({ date, lang });

    expect(result).toBe('Today');
    expect(mockTrans).toHaveBeenCalledWith('t_today');
  });

  it('should return "tomorrow" for 1 day left', () => {
    (farmDates.getDaysDiffBetweenDates as jest.Mock).mockReturnValue(1);
    mockTrans.mockReturnValue('Tomorrow');

    const result = getDaysUntilDate({ date, lang });

    expect(result).toBe('Tomorrow');
    expect(mockTrans).toHaveBeenCalledWith('t_tomorrow');
  });

  it('should return count for future dates > 1', () => {
    (farmDates.getDaysDiffBetweenDates as jest.Mock).mockReturnValue(4);
    mockTrans.mockReturnValue('4 days');

    const result = getDaysUntilDate({ date, lang });

    expect(result).toBe('4 days');
    expect(mockTrans).toHaveBeenCalledWith('t_count_days', { count: '4' });
  });
});
