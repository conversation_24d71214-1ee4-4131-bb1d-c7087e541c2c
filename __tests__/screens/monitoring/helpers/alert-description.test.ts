import { getAlertDescription, getSymmetryAlertText } from '@screens/monitoring/helpers/alert-description';

const descriptions = {
  growthAhead: { description: 't_growth_ahead', labelColor: 'black.500' },
  onTrack: { description: 't_on_track_to_hit_target', labelColor: 'black.500' },
  checkStock: { description: 't_check_stock', labelColor: 'red.500' },
  needsAttention: { description: 't_needs_attention', labelColor: 'red.500' },
  checkSample: { description: 't_check_sample', labelColor: 'red.500' },
  targetAtRisk: { description: 't_target_at_risk', labelColor: 'red.500' },
  default: { description: '', labelColor: '' }
};

const mockTrans = jest.fn((x) => x);
jest.mock('@i18n/get-trans', () => ({
  getTrans: () => ({ trans: mockTrans })
}));

describe('getAlertDescription function', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('Should return growth ahead', () => {
    const caseNumbers = [1];
    caseNumbers.forEach((num) => {
      const result = getAlertDescription(num);

      expect(mockTrans).toHaveBeenCalledWith(descriptions.growthAhead.description);
      expect(result).toStrictEqual(descriptions.growthAhead);
    });

    expect(mockTrans).toHaveBeenCalledTimes(1);
  });

  it('Should return onTrack', () => {
    const caseNumbers = [2, 4, 8, 10, 16, 18];
    caseNumbers.forEach((num) => {
      const result = getAlertDescription(num);

      expect(mockTrans).toHaveBeenCalledWith(descriptions.onTrack.description);
      expect(result).toStrictEqual(descriptions.onTrack);
    });

    expect(mockTrans).toHaveBeenCalledTimes(6);
  });

  it('Should return checkStock', () => {
    const caseNumbers = [6, 12];
    caseNumbers.forEach((num) => {
      const result = getAlertDescription(num);

      expect(mockTrans).toHaveBeenCalledWith(descriptions.checkStock.description);
      expect(result).toStrictEqual(descriptions.checkStock);
    });

    expect(mockTrans).toHaveBeenCalledTimes(2);
  });

  it('Should return needsAttention', () => {
    const caseNumbers = [3, 5, 7, 9, 11, 13, 15];
    caseNumbers.forEach((num) => {
      const result = getAlertDescription(num);

      expect(mockTrans).toHaveBeenCalledWith(descriptions.needsAttention.description);
      expect(result).toStrictEqual(descriptions.needsAttention);
    });

    expect(mockTrans).toHaveBeenCalledTimes(7);
  });

  it('Should return checkSample', () => {
    const caseNumbers = [14];
    caseNumbers.forEach((num) => {
      const result = getAlertDescription(num);

      expect(mockTrans).toHaveBeenCalledWith(descriptions.checkSample.description);
      expect(result).toStrictEqual(descriptions.checkSample);
    });

    expect(mockTrans).toHaveBeenCalledTimes(1);
  });

  it('Should return targetAtRisk', () => {
    const caseNumbers = [17, 19];
    caseNumbers.forEach((num) => {
      const result = getAlertDescription(num);

      expect(mockTrans).toHaveBeenCalledWith(descriptions.targetAtRisk.description);
      expect(result).toStrictEqual(descriptions.targetAtRisk);
    });

    expect(mockTrans).toHaveBeenCalledTimes(2);
  });

  it('Should return the default values', () => {
    const caseNumbers = [20, 24, 30, 50, 60];
    caseNumbers.forEach((num) => {
      const result = getAlertDescription(num);

      expect(mockTrans).not.toHaveBeenCalledWith(descriptions.default.description);
      expect(result).toStrictEqual(descriptions.default);
    });

    expect(mockTrans).not.toHaveBeenCalled();
  });
});

const symmetry: any = {
  lessThanHeuristicAvgPercent: 0.5,
  lessThanAvgPercent: 0.3,
  estimatedRevenueLoss: 0.3,
  heuristicGramsBelowABW: 12
};
describe('getAlertDescription function', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('Should return title and info for type left', () => {
    const result = getSymmetryAlertText({ ...symmetry, type: 'left' });

    expect(result).toStrictEqual({ info: 't_symmetry_left_info', title: 't_symmetry_left_title' });

    expect(mockTrans).toHaveBeenCalledTimes(2);
    expect(mockTrans).toHaveBeenCalledWith('t_symmetry_left_title');
    expect(mockTrans).toHaveBeenCalledWith('t_symmetry_left_info', {
      animalsPercentBelowABW: 30,
      animalsPercentBelowHeuristic: 50,
      heuristicGramsBelowABW: 12,
      revenueLoss: 0.3
    });
  });

  it('Should return title and info for type right', () => {
    const result = getSymmetryAlertText({ ...symmetry, type: 'right' });

    expect(result).toStrictEqual({ info: 't_symmetry_right_info', title: 't_symmetry_right_title' });

    expect(mockTrans).toHaveBeenCalledTimes(2);
    expect(mockTrans).toHaveBeenCalledWith('t_symmetry_right_title');
    expect(mockTrans).toHaveBeenCalledWith('t_symmetry_right_info', {
      animalsPercentBelowHeuristic: 50,
      heuristicGramsBelowABW: 12
    });
  });

  it('Should return info only for type normal', () => {
    const result = getSymmetryAlertText({ ...symmetry, type: 'normal' });

    expect(result).toStrictEqual({ info: 't_symmetry_normal_info' });

    expect(mockTrans).toHaveBeenCalledTimes(1);
    expect(mockTrans).toHaveBeenCalledWith('t_symmetry_normal_info');
  });

  it('Should normalize the animalsPercentBelowHeuristic', () => {
    const symmetryValues: any = {
      lessThanAvgPercent: 0.30003003030030303,
      estimatedRevenueLoss: 0.3333982837428374923,
      lessThanHeuristicAvgPercent: 0.55555555555555555,
      heuristicGramsBelowABW: 12
    };
    getSymmetryAlertText({ ...symmetryValues, type: 'left' });

    expect(mockTrans).toHaveBeenCalledWith('t_symmetry_left_info', {
      animalsPercentBelowABW: 30,
      animalsPercentBelowHeuristic: 55.55,
      heuristicGramsBelowABW: 12,
      revenueLoss: 0.33
    });

    getSymmetryAlertText({ ...symmetryValues, type: 'right' });

    expect(mockTrans).toHaveBeenCalledWith('t_symmetry_right_info', {
      animalsPercentBelowHeuristic: 55.55,
      heuristicGramsBelowABW: 12
    });
  });

  it('Should not throw error if symmetry object is missing', () => {
    [undefined, null, {}].forEach((ele) => {
      const result = getSymmetryAlertText(ele as any);
      expect(result).toBeUndefined();
    });
  });
});
