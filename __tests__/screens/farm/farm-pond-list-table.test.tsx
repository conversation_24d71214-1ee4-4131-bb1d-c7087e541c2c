import { useListPondsApi } from '@screens/pond/hooks/use-list-ponds-api';
import { fireEvent, render, RenderResult } from '@testing-library/react';
import { createMockRouter } from '@tests/utils/createMockRouter';
import { getUniDate } from '@utils/date';
import { goToUrl } from '@utils/url';
import { RouterContext } from 'next/dist/shared/lib/router-context.shared-runtime';
import { MOCK_PONDS_LIST_API_RESPONSE } from './mock-ponds-list-api';
import { FarmSummaryScreen } from '@screens/farm-summary/farm-summary-screen';

const expectedSortedArray = {
  byName: {
    asc: ['2E2E-TEST', 'abcd', 'DOORBELL', 'pondWithNoPopulation'],
    desc: ['pondWithNoPopulation', 'DOORBELL', 'abcd', '2E2E-TEST']
  },
  byWeight: {
    asc: ['DOORBELL', 'abcd4eef', '2E2E-TEST', 'pondWithNoPopulation'],
    desc: ['pondWithNoPopulation', '2E2E-TEST', 'abcd4eef', 'DOORBELL']
  },
  byCv: {
    asc: ['DOORBELL', '2E2E-TEST', 'abcd4eef', 'pondWithNoPopulation'],
    desc: ['pondWithNoPopulation', 'abcd4eef', '2E2E-TEST', 'DOORBELL']
  },
  byAnimals: {
    asc: ['DOORBELL', 'abcd4eef', '2E2E-TEST', 'pondWithNoPopulation'],
    desc: ['pondWithNoPopulation', '2E2E-TEST', 'abcd4eef', 'DOORBELL']
  },
  byMonitoredAt: {
    asc: ['DOORBELL', 'abcd4eef', 'pondWithNoPopulation', '2E2E-TEST'],
    desc: ['2E2E-TEST', 'pondWithNoPopulation', 'abcd4eef', 'DOORBELL']
  }
};

jest.mock('react-redux', () => ({
  useSelector: jest.fn().mockImplementation((selector) =>
    selector({
      farm: { currentFarm: { _id: '633a6251f70388a3f592a98a', name: 'e2e test', timezone: 'fsd' } },
      auth: {
        isUserLoggedIn: true,
        userMembership: [{}]
      },
      app: { lang: 'en' }
    })
  ),
  useDispatch: jest.fn()
}));

jest.mock('@screens/pond/hooks/use-list-ponds-api');
jest.mock('@utils/url');
jest.mock('@utils/date');

describe.skip('ProductionInsightsScreen component', () => {
  let query = {};
  let wrapper: RenderResult;
  beforeAll(() => {
    (useListPondsApi as jest.Mock).mockReturnValue([
      {
        data: MOCK_PONDS_LIST_API_RESPONSE,
        isLoading: false,
        isFinishedOnce: true,
        error: null
      },
      jest.fn()
    ]);
    (goToUrl as jest.Mock).mockImplementation((url: { params: [] }) => {
      query = { ...url.params };
    });
    (getUniDate as jest.Mock).mockReturnValue(jest.fn());
  });

  beforeEach(() => {
    wrapper = render(
      <RouterContext.Provider value={createMockRouter({ query })}>
        <FarmSummaryScreen />
      </RouterContext.Provider>
    );
    jest.clearAllMocks();
  });

  it('should initialy sort by pond name and sort direction should be ASC', async () => {
    const { container } = wrapper;

    container.querySelectorAll('[data-cy="pond-name"]').forEach((ele, index) => {
      expect(ele.textContent).toContain(expectedSortedArray.byName.asc[index]);
    });
  });

  it('should sort by name when user click on pond from table header', () => {
    const { container, rerender } = wrapper;

    const pondNameHeader = container.querySelector('[data-cy="th-name"]');
    // sort DESC by name
    fireEvent.click(pondNameHeader);
    expect(goToUrl).toHaveBeenNthCalledWith(1, {
      params: { sortBy: 'name', sortDir: 'desc' },
      route: '/'
    });

    rerender(
      <RouterContext.Provider value={createMockRouter({ query })}>
        <FarmSummaryScreen />
      </RouterContext.Provider>
    );
    container.querySelectorAll('[data-cy="pond-name"]').forEach((ele, index) => {
      expect(ele.textContent).toContain(expectedSortedArray.byName.desc[index]);
    });

    // sort ASC by name
    fireEvent.click(pondNameHeader);
    expect(goToUrl).toHaveBeenNthCalledWith(2, {
      params: { sortBy: 'name', sortDir: 'asc' },
      route: '/'
    });
    rerender(
      <RouterContext.Provider value={createMockRouter({ query })}>
        <FarmSummaryScreen />
      </RouterContext.Provider>
    );
    container.querySelectorAll('[data-cy="pond-name"]').forEach((ele, index) => {
      expect(ele.textContent).toContain(expectedSortedArray.byName.asc[index]);
    });
  });

  it('should sort by Weight when user click on weight from table header', () => {
    const { container, rerender } = wrapper;

    const weigthHeaderCell = container.querySelector('[data-cy="th-weight"]');
    // sort ASC by weight
    fireEvent.click(weigthHeaderCell);
    expect(goToUrl).toHaveBeenNthCalledWith(1, {
      params: { sortBy: 'weight', sortDir: 'asc' },
      route: '/'
    });

    rerender(
      <RouterContext.Provider value={createMockRouter({ query })}>
        <FarmSummaryScreen />
      </RouterContext.Provider>
    );
    container.querySelectorAll('[data-cy="pond-name"]').forEach((ele, index) => {
      expect(ele.textContent).toContain(expectedSortedArray.byWeight.asc[index]);
    });

    // sort DESC by weight
    fireEvent.click(weigthHeaderCell);
    rerender(
      <RouterContext.Provider value={createMockRouter({ query })}>
        <FarmSummaryScreen />
      </RouterContext.Provider>
    );
    container.querySelectorAll('[data-cy="pond-name"]').forEach((ele, index) => {
      expect(ele.textContent).toContain(expectedSortedArray.byWeight.desc[index]);
    });
  });

  it('should sort by CV when user click on CV from table header', () => {
    const { container, rerender } = wrapper;

    const cvHeaderCell = container.querySelector('[data-cy="th-cv"]');
    // sort ASC by cv
    fireEvent.click(cvHeaderCell);
    expect(goToUrl).toHaveBeenNthCalledWith(1, {
      params: { sortBy: 'cv', sortDir: 'asc' },
      route: '/'
    });

    rerender(
      <RouterContext.Provider value={createMockRouter({ query })}>
        <FarmSummaryScreen />
      </RouterContext.Provider>
    );
    container.querySelectorAll('[data-cy="pond-name"]').forEach((ele, index) => {
      expect(ele.textContent).toContain(expectedSortedArray.byCv.asc[index]);
    });

    // sort DESC by cv
    fireEvent.click(cvHeaderCell);
    rerender(
      <RouterContext.Provider value={createMockRouter({ query })}>
        <FarmSummaryScreen />
      </RouterContext.Provider>
    );
    container.querySelectorAll('[data-cy="pond-name"]').forEach((ele, index) => {
      expect(ele.textContent).toContain(expectedSortedArray.byCv.desc[index]);
    });
  });

  it('should sort by number if animals when user click on animals from table header', () => {
    const { container, rerender } = wrapper;

    const animalsHeaderCell = container.querySelector('[data-cy="th-animals"]');
    // sort ASC by animals
    fireEvent.click(animalsHeaderCell);
    expect(goToUrl).toHaveBeenNthCalledWith(1, {
      params: { sortBy: 'animals', sortDir: 'asc' },
      route: '/'
    });

    rerender(
      <RouterContext.Provider value={createMockRouter({ query })}>
        <FarmSummaryScreen />
      </RouterContext.Provider>
    );
    container.querySelectorAll('[data-cy="pond-name"]').forEach((ele, index) => {
      expect(ele.textContent).toContain(expectedSortedArray.byAnimals.asc[index]);
    });

    // sort DESC by animals
    fireEvent.click(animalsHeaderCell);
    rerender(
      <RouterContext.Provider value={createMockRouter({ query })}>
        <FarmSummaryScreen />
      </RouterContext.Provider>
    );
    container.querySelectorAll('[data-cy="pond-name"]').forEach((ele, index) => {
      expect(ele.textContent).toContain(expectedSortedArray.byAnimals.desc[index]);
    });
  });

  it('should sort by monitored when user click on monitored from table header', () => {
    const { container, rerender } = wrapper;

    const monitoredAtHeaderCell = container.querySelector('[data-cy="th-monitoredAt"]');
    // sort ASC by monitored
    fireEvent.click(monitoredAtHeaderCell);
    expect(goToUrl).toHaveBeenNthCalledWith(1, {
      params: { sortBy: 'monitoredAt', sortDir: 'asc' },
      route: '/'
    });

    rerender(
      <RouterContext.Provider value={createMockRouter({ query })}>
        <FarmSummaryScreen />
      </RouterContext.Provider>
    );
    container.querySelectorAll('[data-cy="pond-name"]').forEach((ele, index) => {
      expect(ele.textContent).toContain(expectedSortedArray.byMonitoredAt.asc[index]);
    });

    // sort DESC by monitored
    fireEvent.click(monitoredAtHeaderCell);
    rerender(
      <RouterContext.Provider value={createMockRouter({ query })}>
        <FarmSummaryScreen />
      </RouterContext.Provider>
    );
    container.querySelectorAll('[data-cy="pond-name"]').forEach((ele, index) => {
      expect(ele.textContent).toContain(expectedSortedArray.byMonitoredAt.desc[index]);
    });
  });
});
