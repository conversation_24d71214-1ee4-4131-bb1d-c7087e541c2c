import {
  getLastWeeksFromPlanning,
  GetLastWeeksFromPlanningArgs
} from '@screens/data-updater/helper/get-weeks-from-planning';

const testCases = [
  {
    input: { dayOfWeek: 'thursday', lang: 'en', numberOfWeeks: 4 },
    output: {
      weeksRanges: ['March 01 - 07', 'February 23 - 29', 'February 16 - 22', 'February 09 - 15'],
      weeks: [
        ['2024-03-01', '2024-03-02', '2024-03-03', '2024-03-04', '2024-03-05', '2024-03-06', '2024-03-07'],
        ['2024-02-23', '2024-02-24', '2024-02-25', '2024-02-26', '2024-02-27', '2024-02-28', '2024-02-29'],
        ['2024-02-16', '2024-02-17', '2024-02-18', '2024-02-19', '2024-02-20', '2024-02-21', '2024-02-22'],
        ['2024-02-09', '2024-02-10', '2024-02-11', '2024-02-12', '2024-02-13', '2024-02-14', '2024-02-15']
      ],
      weekDatesMap: {
        'Fri Mar 1 - Thu Mar 7': [
          '2024-03-01',
          '2024-03-02',
          '2024-03-03',
          '2024-03-04',
          '2024-03-05',
          '2024-03-06',
          '2024-03-07'
        ],
        'Fri Feb 23 - Thu Feb 29': [
          '2024-02-23',
          '2024-02-24',
          '2024-02-25',
          '2024-02-26',
          '2024-02-27',
          '2024-02-28',
          '2024-02-29'
        ],
        'Fri Feb 16 - Thu Feb 22': [
          '2024-02-16',
          '2024-02-17',
          '2024-02-18',
          '2024-02-19',
          '2024-02-20',
          '2024-02-21',
          '2024-02-22'
        ],
        'Fri Feb 9 - Thu Feb 15': [
          '2024-02-09',
          '2024-02-10',
          '2024-02-11',
          '2024-02-12',
          '2024-02-13',
          '2024-02-14',
          '2024-02-15'
        ]
      }
    }
  },
  {
    input: { dayOfWeek: 'wednesday', lang: 'en', numberOfWeeks: 4 },
    output: {
      weeksRanges: ['February 29 - March 6', 'February 22 - 28', 'February 15 - 21', 'February 08 - 14'],
      weeks: [
        ['2024-02-29', '2024-03-01', '2024-03-02', '2024-03-03', '2024-03-04', '2024-03-05', '2024-03-06'],
        ['2024-02-22', '2024-02-23', '2024-02-24', '2024-02-25', '2024-02-26', '2024-02-27', '2024-02-28'],
        ['2024-02-15', '2024-02-16', '2024-02-17', '2024-02-18', '2024-02-19', '2024-02-20', '2024-02-21'],
        ['2024-02-08', '2024-02-09', '2024-02-10', '2024-02-11', '2024-02-12', '2024-02-13', '2024-02-14']
      ],
      weekDatesMap: {
        'Thu Feb 29 - Wed Mar 6': [
          '2024-03-01',
          '2024-03-02',
          '2024-03-03',
          '2024-03-04',
          '2024-03-05',
          '2024-03-06',
          '2024-03-07'
        ],
        'Thu Feb 22 - Wed Feb 28': [
          '2024-02-23',
          '2024-02-24',
          '2024-02-25',
          '2024-02-26',
          '2024-02-27',
          '2024-02-28',
          '2024-02-29'
        ],
        'Thu Feb 15 - Wed Feb 21': [
          '2024-02-16',
          '2024-02-17',
          '2024-02-18',
          '2024-02-19',
          '2024-02-20',
          '2024-02-21',
          '2024-02-22'
        ],
        'Thu Feb 8 - Wed Feb 14': [
          '2024-02-09',
          '2024-02-10',
          '2024-02-11',
          '2024-02-12',
          '2024-02-13',
          '2024-02-14',
          '2024-02-15'
        ]
      }
    }
  },
  {
    input: { dayOfWeek: 'friday', lang: 'en', numberOfWeeks: 4 },
    output: {
      weeksRanges: ['February 24 - March 1', 'February 17 - 23', 'February 10 - 16', 'February 03 - 09'],
      weeks: [
        ['2024-02-24', '2024-02-25', '2024-02-26', '2024-02-27', '2024-02-28', '2024-02-29', '2024-03-01'],
        ['2024-02-17', '2024-02-18', '2024-02-19', '2024-02-20', '2024-02-21', '2024-02-22', '2024-02-23'],
        ['2024-02-10', '2024-02-11', '2024-02-12', '2024-02-13', '2024-02-14', '2024-02-15', '2024-02-16'],
        ['2024-02-03', '2024-02-04', '2024-02-05', '2024-02-06', '2024-02-07', '2024-02-08', '2024-02-09']
      ],
      weekDatesMap: {
        'Sat Feb 24 - Fri Mar 1': [
          '2024-03-01',
          '2024-03-02',
          '2024-03-03',
          '2024-03-04',
          '2024-03-05',
          '2024-03-06',
          '2024-03-07'
        ],
        'Sat Feb 17 - Fri Feb 23': [
          '2024-02-23',
          '2024-02-24',
          '2024-02-25',
          '2024-02-26',
          '2024-02-27',
          '2024-02-28',
          '2024-02-29'
        ],
        'Sat Feb 10 - Fri Feb 16': [
          '2024-02-16',
          '2024-02-17',
          '2024-02-18',
          '2024-02-19',
          '2024-02-20',
          '2024-02-21',
          '2024-02-22'
        ],
        'Sat Feb 3 - Fri Feb 9': [
          '2024-02-09',
          '2024-02-10',
          '2024-02-11',
          '2024-02-12',
          '2024-02-13',
          '2024-02-14',
          '2024-02-15'
        ]
      }
    }
  }
];

describe('getLast4WeeksFromPlanning', () => {
  beforeAll(() => {
    jest.useFakeTimers().setSystemTime(new Date('2024-03-01'));
  });
  afterAll(() => {
    jest.clearAllTimers();
  });

  it('should return the correct results', () => {
    testCases.forEach(({ input, output }) => {
      const { weekRanges, weeks } = getLastWeeksFromPlanning(input as GetLastWeeksFromPlanningArgs);
      expect(weekRanges).toStrictEqual(output.weeksRanges);
      expect(weeks).toStrictEqual(output.weeks);
    });
  });
});
