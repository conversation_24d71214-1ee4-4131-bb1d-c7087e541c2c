// import { getNurseryActiveTab, nurseryDataTabs, NurseryDataTabsType } from
import { getNurseryActiveTab } from '@screens/nursery/helpers/nursery-details-tabs';
import { ParsedUrlQuery } from 'querystring';

const route = '/farm/[farmEid]/nursery/[nurseryEid]';

describe('getNurseryActiveTab', () => {
  it('should return "isData" when the route matches and the tab is a valid nursery data tab', () => {
    const query: ParsedUrlQuery = { tab: 'feed' };

    const result = getNurseryActiveTab(route, query);

    expect(result).toBe('isData');
  });

  it('should return "isData" when the route matches and the tab is a valid nursery data tab', () => {
    const query: ParsedUrlQuery = { tab: 'stocking' };

    const result = getNurseryActiveTab(route, query);

    expect(result).toBe('isData');
  });

  it('should return "isData" when the route matches and the tab is a valid nursery data tab', () => {
    const query: ParsedUrlQuery = { tab: 'transferHistory' };

    const result = getNurseryActiveTab(route, query);

    expect(result).toBe('isData');
  });

  it('should return "isOverview" when the route matches and the tab is not a valid nursery data tab', () => {
    const query: ParsedUrlQuery = { tab: 'invalidTab' };

    const result = getNurseryActiveTab(route, query);

    expect(result).toBe('isOverview');
  });

  it('should return "isOverview" when the route matches and the tab is undefined', () => {
    const query: ParsedUrlQuery = {};

    const result = getNurseryActiveTab(route, query);

    expect(result).toBe('isOverview');
  });

  it('should return undefined when the route does not match the nursery path', () => {
    const query: ParsedUrlQuery = { tab: 'feed' };
    const route = '/farm/123/other/456';

    const result = getNurseryActiveTab(route, query);

    expect(result).toBeUndefined();
  });

  it('should handle cases where query.tab is not a string or valid tab', () => {
    const query: ParsedUrlQuery = { tab: undefined };

    const result = getNurseryActiveTab(route, query);

    expect(result).toBe('isOverview');
  });
});
