import { getNurseryFeedData } from '@screens/nursery/helpers/get-nursery-feed-data';
import { convertPoundsToKg, formatNumber } from '@utils/number';

describe('getNurseryFeedData', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return zeros when feedData or feedTypes are empty', () => {
    const result = getNurseryFeedData({ feedData: [], feedTypes: [] });

    expect(result).toEqual({
      totalFeedGiven: 0,
      totalCosts: 0
    });
  });

  it('should calculate total feed given and total costs when valid data is provided', () => {
    const feedData: any = [
      {
        feed: [
          { feedTypeId: '1', totalLbs: 20 },
          { feedTypeId: '2', totalLbs: 30 }
        ],
        otherDirectCost: 200
      }
    ];

    const feedTypes: any = [
      { _id: '1', costPerKg: 5 },
      { _id: '2', costPerKg: 3 }
    ];

    const result = getNurseryFeedData({ feedData, feedTypes });

    expect(result).toEqual({
      totalFeedGiven: 23,
      totalCosts: 286
    });
  });

  it('should handle undefined feedType costPerKg', () => {
    const feedData: any = [
      {
        feed: [{ feedTypeId: '1', totalLbs: 20 }],
        otherDirectCost: 200
      }
    ];

    const feedTypes: any = [{ _id: '1', costPerKg: undefined }];

    const result = getNurseryFeedData({ feedData, feedTypes });

    expect(result).toEqual({
      totalFeedGiven: 0,
      totalCosts: 200
    });
  });

  it('should return zeros when feedData is undefined', () => {
    const result = getNurseryFeedData({ feedData: undefined, feedTypes: [] });

    expect(result).toEqual({
      totalFeedGiven: 0,
      totalCosts: 0
    });
  });

  it('should handle cases with no otherDirectCost gracefully', () => {
    const feedData: any = [
      {
        feed: [{ feedTypeId: '1', totalLbs: 20 }],
        otherDirectCost: undefined
      }
    ];

    const feedTypes: any = [{ _id: '1', costPerKg: 5 }];

    const result = getNurseryFeedData({ feedData, feedTypes });

    expect(result).toEqual({
      totalFeedGiven: 9,
      totalCosts: 45
    });
  });

  it('should ignore feed with no feed types', () => {
    const feedData: any = [
      {
        feed: [
          { feedTypeId: '1', totalLbs: 20 },
          { feedTypeId: '2', totalLbs: 30 }
        ],
        otherDirectCost: 200
      }
    ];

    const feedTypes: any = [{ _id: '1', costPerKg: 5 }];

    const result = getNurseryFeedData({ feedData, feedTypes });

    expect(result).toEqual({
      totalFeedGiven: 9,
      totalCosts: 245
    });
  });
});
