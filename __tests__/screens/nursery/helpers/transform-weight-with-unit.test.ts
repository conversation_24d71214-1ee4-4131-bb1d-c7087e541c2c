import { transformWeightWithUnit } from '@screens/nursery/helpers/transform-weight-with-unit';
import { formatNumber } from '@utils/number';
import { getTrans } from '@i18n/get-trans';

describe('getAlertDescription function', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return undefined weight and unit with "-" when weightInGrams is undefined', () => {
    const result = transformWeightWithUnit({ lang: 'en', weightInGrams: undefined, unit: 'g' });
    expect(result).toEqual({ weight: '-', unit: undefined, weightString: '-' });
  });

  it('should return the correct weight and unit when unit is "g"', () => {
    const { trans } = getTrans();
    const result = transformWeightWithUnit({ lang: 'en', weightInGrams: 100, unit: 'g' });
    expect(result).toEqual({
      weight: formatNumber(100, { fractionDigits: 2 }),
      unit: 'g',
      unitString: trans('t_gram_g'),
      weightString: `${formatNumber(100, { fractionDigits: 2 })} ${trans('t_gram_g')}`
    });
  });

  it('should return the correct weight and unit when unit is "pls"', () => {
    const { trans } = getTrans();
    const result = transformWeightWithUnit({ lang: 'en', weightInGrams: 50, unit: 'pls' });
    expect(result).toEqual({
      weight: formatNumber(0.02, { lang: 'en', fractionDigits: 2 }),
      unit: 'pls',
      unitString: trans('t_pls_per_g'),
      weightString: `${formatNumber(0.02, { lang: 'en', fractionDigits: 2 })} ${trans('t_pls_per_g')}`
    });
  });

  it('should return the correct weightString format with two decimal places', () => {
    const { trans } = getTrans();
    const result = transformWeightWithUnit({ lang: 'en', weightInGrams: 123.456, unit: 'g' });
    expect(result.weightString).toBe(`${formatNumber(123.46, { fractionDigits: 2 })} ${trans('t_gram_g')}`);
  });

  it('should handle zero weightInGrams correctly', () => {
    const result = transformWeightWithUnit({ lang: 'en', weightInGrams: 0, unit: 'g' });
    expect(result).toEqual({ weight: '-', unit: undefined, weightString: '-' });
  });
});
