import { getFeedPalletZones, zoneColors } from '@screens/monitoring/helpers/feed-pallet-zones';

const wd = [
  5.729659504740558, 5.72965950474056, 5.72965950474056, 5.72965950474056, 5.72965950474056, 5.72965950474056,
  5.72965950474056, 5.72965950474056, 5.72965950474056, 5.72965950474056, 5.729659504740561, 5.729659504740561,
  5.729659504740561, 5.729659504740561, 7.153876238949264, 7.153876238949268, 7.153876238949268, 7.153876238949268,
  7.153876238949268, 7.153876238949268, 7.153876238949268, 7.153876238949268, 7.153876238949268, 7.153876238949268,
  7.153876238949269, 7.153876238949269, 7.153876238949269, 7.153876238949269, 7.90800707232737, 7.908007072327374,
  7.908007072327374, 7.908007072327374, 7.908007072327374, 7.908007072327374, 7.908007072327374, 7.908007072327374,
  7.908007072327374, 7.908007072327374, 7.908007072327376, 7.908007072327376, 7.908007072327376, 7.908007072327376,
  8.22982971265286, 8.229829712652863, 8.229829712652863, 8.229829712652863, 8.229829712652863, 8.229829712652863,
  8.229829712652863, 8.229829712652863, 8.229829712652863, 8.229829712652863, 8.229829712652865, 8.229829712652865,
  8.229829712652865, 8.229829712652865, 8.253410263583836, 8.25341026358384, 8.25341026358384, 8.25341026358384,
  8.25341026358384, 8.25341026358384, 8.25341026358384, 8.25341026358384, 8.25341026358384, 8.25341026358384,
  8.253410263583842, 8.253410263583842, 8.253410263583842, 8.253410263583842, 8.336357040166082, 8.336357040166085,
  8.336357040166085, 8.336357040166085, 8.336357040166085, 8.336357040166085, 8.336357040166085, 8.336357040166085,
  8.336357040166085, 8.336357040166085, 8.336357040166087, 8.336357040166087, 8.336357040166087, 8.336357040166087,
  8.60671226229342, 8.606712262293424, 8.606712262293424, 8.606712262293424, 8.606712262293424, 8.606712262293424,
  8.606712262293424, 8.606712262293424, 8.606712262293424, 8.606712262293424, 8.606712262293426, 8.606712262293426,
  8.606712262293426, 8.606712262293426, 8.631293217573123, 8.631293217573127, 8.631293217573127, 8.631293217573127,
  8.631293217573127, 8.631293217573127, 8.631293217573127, 8.631293217573127, 8.631293217573127, 8.631293217573127,
  8.631293217573129, 8.631293217573129, 8.631293217573129, 8.631293217573129, 9.079370415302748, 9.079370415302751,
  9.079370415302751, 9.079370415302751, 9.079370415302751, 9.079370415302751, 9.079370415302751, 9.079370415302751,
  9.079370415302751, 9.079370415302751, 9.079370415302753, 9.079370415302753, 9.079370415302753, 9.079370415302753,
  9.351566228843852, 9.351566228843856, 9.351566228843856, 9.351566228843856, 9.351566228843856, 9.351566228843856,
  9.351566228843856, 9.351566228843856, 9.351566228843856, 9.351566228843856, 9.35156622884386, 9.35156622884386,
  9.35156622884386, 9.35156622884386, 9.38094798058827, 9.380947980588273, 9.380947980588273, 9.380947980588273,
  9.380947980588273, 9.380947980588273, 9.380947980588273, 9.380947980588273, 9.380947980588273, 9.380947980588273,
  9.380947980588276, 9.380947980588276, 9.380947980588276, 9.380947980588276, 9.414521130914812, 9.414521130914816,
  9.414521130914816, 9.414521130914816, 9.414521130914816, 9.414521130914816, 9.414521130914816, 9.414521130914816,
  9.414521130914816, 9.414521130914816, 9.41452113091482, 9.41452113091482, 9.41452113091482, 9.41452113091482,
  9.744495302384948, 9.744495302384951, 9.744495302384951, 9.744495302384951, 9.744495302384951, 9.744495302384951,
  9.744495302384951, 9.744495302384951, 9.744495302384951, 9.744495302384951, 9.744495302384955, 9.744495302384955,
  9.744495302384955, 9.744495302384955, 9.770320802636135, 9.770320802636139, 9.770320802636139, 9.770320802636139,
  9.770320802636139, 9.770320802636139, 9.770320802636139, 9.770320802636139, 9.770320802636139, 9.770320802636139,
  9.770320802636142, 9.770320802636142, 9.770320802636142, 9.770320802636142, 9.817718415726384, 9.817718415726388,
  9.817718415726388, 9.817718415726388, 9.817718415726388, 9.817718415726388, 9.817718415726388, 9.817718415726388,
  9.817718415726388, 9.817718415726388, 9.817718415726391, 9.817718415726391, 9.817718415726391, 9.817718415726391,
  9.849361329254242, 9.849361329254245, 9.849361329254245, 9.849361329254245, 9.849361329254245, 9.849361329254245,
  9.849361329254245, 9.849361329254245, 9.849361329254245, 9.849361329254245, 9.849361329254249, 9.849361329254249,
  9.849361329254249, 9.849361329254249, 10.052513030270038, 10.052513030270044, 10.052513030270044, 10.052513030270044,
  10.052513030270044, 10.052513030270044, 10.052513030270044, 10.052513030270044, 10.052513030270044,
  10.052513030270044, 10.052513030270045, 10.052513030270045, 10.052513030270045, 10.052513030270045,
  10.122880842734368, 10.122880842734373, 10.122880842734373, 10.122880842734373, 10.122880842734373,
  10.122880842734373, 10.122880842734373, 10.122880842734373, 10.122880842734373, 10.122880842734373,
  10.122880842734375, 10.122880842734375, 10.122880842734375, 10.122880842734375, 10.18848886672921, 10.188488866729216,
  10.188488866729216, 10.188488866729216, 10.188488866729216, 10.188488866729216, 10.188488866729216,
  10.188488866729216, 10.188488866729216, 10.188488866729216, 10.188488866729218, 10.188488866729218,
  10.188488866729218, 10.188488866729218, 10.212894345936306, 10.212894345936311, 10.212894345936311,
  10.212894345936311, 10.212894345936311, 10.212894345936311, 10.212894345936311, 10.212894345936311,
  10.212894345936311, 10.212894345936311, 10.212894345936313, 10.212894345936313, 10.212894345936313,
  10.212894345936313, 10.276129628253713, 10.276129628253718, 10.276129628253718, 10.276129628253718,
  10.276129628253718, 10.276129628253718, 10.276129628253718, 10.276129628253718, 10.276129628253718,
  10.276129628253718, 10.27612962825372, 10.27612962825372, 10.27612962825372, 10.27612962825372, 10.463019274975446,
  10.463019274975451, 10.463019274975451, 10.463019274975451, 10.463019274975451, 10.463019274975451,
  10.463019274975451, 10.463019274975451, 10.463019274975451, 10.463019274975451, 10.463019274975453,
  10.463019274975453, 10.463019274975453, 10.463019274975453, 10.689200303172397, 10.689200303172402,
  10.689200303172402, 10.689200303172402, 10.689200303172402, 10.689200303172402, 10.689200303172402,
  10.689200303172402, 10.689200303172402, 10.689200303172402, 10.689200303172404, 10.689200303172404,
  10.689200303172404, 10.689200303172404, 10.964264769030915, 10.96426476903092, 10.96426476903092, 10.96426476903092,
  10.96426476903092, 10.96426476903092, 10.96426476903092, 10.96426476903092, 10.96426476903092, 10.96426476903092,
  10.964264769030923, 10.964264769030923, 10.964264769030923, 10.964264769030923, 11.046080487884288,
  11.046080487884293, 11.046080487884293, 11.046080487884293, 11.046080487884293, 11.046080487884293,
  11.046080487884293, 11.046080487884293, 11.046080487884293, 11.046080487884293, 11.046080487884295,
  11.046080487884295, 11.046080487884295, 11.046080487884295, 11.180817501417822, 11.180817501417827,
  11.180817501417827, 11.180817501417827, 11.180817501417827, 11.180817501417827, 11.180817501417827,
  11.180817501417827, 11.180817501417827, 11.180817501417827, 11.18081750141783, 11.18081750141783, 11.18081750141783,
  11.18081750141783, 11.579910172009265, 11.57991017200927, 11.57991017200927, 11.57991017200927, 11.57991017200927,
  11.57991017200927, 11.57991017200927, 11.57991017200927, 11.57991017200927, 11.57991017200927, 11.579910172009273,
  11.579910172009273, 11.579910172009273, 11.579910172009273, 11.594071281803574, 11.59407128180358, 11.59407128180358,
  11.59407128180358, 11.59407128180358, 11.59407128180358, 11.59407128180358, 11.59407128180358, 11.59407128180358,
  11.59407128180358, 11.594071281803581, 11.594071281803581, 11.594071281803581, 11.594071281803581, 11.610918892122534,
  11.61091889212254, 11.61091889212254, 11.61091889212254, 11.61091889212254, 11.61091889212254, 11.61091889212254,
  11.61091889212254, 11.61091889212254, 11.61091889212254, 11.610918892122541, 11.610918892122541, 11.610918892122541,
  11.610918892122541, 11.673326385140902, 11.673326385140907, 11.673326385140907, 11.673326385140907,
  11.673326385140907, 11.673326385140907, 11.673326385140907, 11.673326385140907, 11.673326385140907,
  11.673326385140907, 11.673326385140909, 11.673326385140909, 11.673326385140909, 11.673326385140909,
  11.730457198215037, 11.730457198215042, 11.730457198215042, 11.730457198215042, 11.730457198215042,
  11.730457198215042, 11.730457198215042, 11.730457198215042, 11.730457198215042, 11.730457198215042,
  11.730457198215044, 11.730457198215044, 11.730457198215044, 11.730457198215044, 12.223711855246698,
  12.223711855246703, 12.223711855246703, 12.223711855246703, 12.223711855246703, 12.223711855246703,
  12.223711855246703, 12.223711855246703, 12.223711855246703, 12.223711855246703, 12.223711855246707,
  12.223711855246707, 12.223711855246707, 12.223711855246707, 12.664050939644042, 12.664050939644047,
  12.664050939644047, 12.664050939644047, 12.664050939644047, 12.664050939644047, 12.664050939644047,
  12.664050939644047, 12.664050939644047, 12.664050939644047, 12.664050939644051, 12.664050939644051,
  12.664050939644051, 12.664050939644051, 12.827889324675544, 12.82788932467555, 12.82788932467555, 12.82788932467555,
  12.82788932467555, 12.82788932467555, 12.82788932467555, 12.82788932467555, 12.82788932467555, 12.82788932467555,
  12.827889324675553, 12.827889324675553, 12.827889324675553, 12.827889324675553, 13.043689608033715, 13.04368960803372,
  13.04368960803372, 13.04368960803372, 13.04368960803372, 13.04368960803372, 13.04368960803372, 13.04368960803372,
  13.04368960803372, 13.04368960803372, 13.043689608033723, 13.043689608033723, 13.043689608033723, 13.043689608033723,
  13.295784828192462, 13.295784828192467, 13.295784828192467, 13.295784828192467, 13.295784828192467,
  13.295784828192467, 13.295784828192467, 13.295784828192467, 13.295784828192467, 13.295784828192467, 13.29578482819247,
  13.29578482819247, 13.29578482819247, 13.29578482819247, 13.801867358336336, 13.801867358336342, 13.801867358336342,
  13.801867358336342, 13.801867358336342, 13.801867358336342, 13.801867358336342, 13.801867358336342,
  13.801867358336342, 13.801867358336342, 13.801867358336345, 13.801867358336345, 13.801867358336345,
  13.801867358336345, 14.35594927005859, 14.355949270058597, 14.355949270058597, 14.355949270058597, 14.355949270058597,
  14.355949270058597, 14.355949270058597, 14.355949270058597, 14.355949270058597, 14.355949270058597,
  14.355949270058598, 14.355949270058598, 14.355949270058598, 14.355949270058598, 20.65979747878676, 20.65979747878677,
  20.65979747878677, 20.65979747878677, 20.65979747878677, 20.65979747878677, 20.65979747878677, 20.65979747878677,
  20.65979747878677, 20.65979747878677, 20.659797478786775, 20.659797478786775, 20.659797478786775, 20.659797478786775
];

const cases = {
  inputOne: [{ weight: 2, palletSize: 2 }],
  outputOne: [{ color: zoneColors[0], title: '2+', palletSize: 2, biomass: 100 }],
  inputTwo: [
    { weight: 2, palletSize: 1.2 },
    { weight: 6, palletSize: 2 }
  ],
  outputTwo: [
    { value: 6, color: zoneColors[0], title: '2-6', palletSize: 1.2, biomass: 1 },
    { color: zoneColors[1], title: '6+', palletSize: 2, biomass: 99 }
  ],
  inputThree: [
    { weight: 2, palletSize: 1 },
    { weight: 6, palletSize: 2 },
    { weight: 10, palletSize: 3 }
  ],
  outputThree: [
    { value: 6, color: zoneColors[0], title: '2-6', palletSize: 1, biomass: 1 },
    { value: 10, color: zoneColors[1], title: '6-10', palletSize: 2, biomass: 32 },
    { color: zoneColors[2], title: '10+', palletSize: 3, biomass: 67 }
  ],
  inputFour: [
    { weight: 2, palletSize: 1 },
    { weight: 6, palletSize: 2 },
    { weight: 10, palletSize: 3 },
    { weight: 14, palletSize: 4 }
  ],
  outputFour: [
    { value: 6, color: zoneColors[0], title: '2-6', palletSize: 1, biomass: 1 },
    { value: 10, color: zoneColors[1], title: '6-10', palletSize: 2, biomass: 32 },
    { value: 14, color: zoneColors[2], title: '10-14', palletSize: 3, biomass: 58 },
    { color: zoneColors[3], title: '14+', palletSize: 4, biomass: 8 }
  ],
  inputFive: [
    { weight: 2, palletSize: 2 },
    { weight: 6, palletSize: 4 },
    { weight: 10, palletSize: 6 },
    { weight: 14, palletSize: 8 },
    { weight: 18, palletSize: 9 }
  ],
  outputFive: [
    { value: 6, color: zoneColors[0], title: '2-6', palletSize: 2, biomass: 1 },
    { value: 10, color: zoneColors[1], title: '6-10', palletSize: 4, biomass: 32 },
    { value: 14, color: zoneColors[2], title: '10-14', palletSize: 6, biomass: 58 },
    { value: 18, color: zoneColors[3], title: '14-18', palletSize: 8, biomass: 3 },
    { color: zoneColors[4], title: '18+', palletSize: 9, biomass: 5 }
  ],
  inputSix: [
    { weight: 2, palletSize: 2 },
    { weight: 6, palletSize: 3 },
    { weight: 10, palletSize: 4.2 },
    { weight: 14, palletSize: 6.2 },
    { weight: 18, palletSize: 7.2 },
    { weight: 22, palletSize: 5 }
  ],
  outputSix: [
    { value: 6, color: zoneColors[0], title: '2-6', palletSize: 2, biomass: 1 },
    { value: 10, color: zoneColors[1], title: '6-10', palletSize: 3, biomass: 32 },
    { value: 14, color: zoneColors[2], title: '10-14', palletSize: 4.2, biomass: 58 },
    { value: 18, color: zoneColors[3], title: '14-18', palletSize: 6.2, biomass: 3 },
    { value: 22, color: zoneColors[4], title: '18-22', palletSize: 7.2, biomass: 5 },
    { color: zoneColors[5], title: '22+', palletSize: 5, biomass: 0 }
  ],
  inputSeven: [
    { weight: 8, palletSize: 2 },
    { weight: 12, palletSize: 1 },
    { weight: 14, palletSize: 3 },
    { weight: 17, palletSize: 4 },
    { weight: 20, palletSize: 5 },
    { weight: 22, palletSize: 7 },
    { weight: 26, palletSize: 6 }
  ],
  outputSeven: [
    { value: 12, color: zoneColors[0], title: '8-12', palletSize: 2, biomass: 68 },
    { value: 14, color: zoneColors[1], title: '12-14', palletSize: 1, biomass: 19 },
    { value: 17, color: zoneColors[2], title: '14-17', palletSize: 3, biomass: 3 },
    { value: 20, color: zoneColors[3], title: '17-20', palletSize: 4, biomass: 0 },
    { value: 22, color: zoneColors[4], title: '20-22', palletSize: 5, biomass: 5 },
    { value: 26, color: zoneColors[5], title: '22-26', palletSize: 7, biomass: 0 },
    { color: zoneColors[5], title: '26+', palletSize: 6, biomass: 0 } //this is the last supported color
  ]
};

describe('Generate feedPallet zones from feedPallets', () => {
  it('Should generate accurate color zones', () => {
    expect(getFeedPalletZones(cases.inputOne, wd).feedPalletZones).toStrictEqual(cases.outputOne);
    expect(getFeedPalletZones(cases.inputTwo, wd).feedPalletZones).toStrictEqual(cases.outputTwo);
    expect(getFeedPalletZones(cases.inputThree, wd).feedPalletZones).toStrictEqual(cases.outputThree);
    expect(getFeedPalletZones(cases.inputFour, wd).feedPalletZones).toStrictEqual(cases.outputFour);
    expect(getFeedPalletZones(cases.inputFive, wd).feedPalletZones).toStrictEqual(cases.outputFive);
    expect(getFeedPalletZones(cases.inputSix, wd).feedPalletZones).toStrictEqual(cases.outputSix);
    expect(getFeedPalletZones(cases.inputSeven, wd).feedPalletZones).toStrictEqual(cases.outputSeven);
  });
});
