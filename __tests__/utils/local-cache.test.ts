import { flattenObject } from '@utils/local-cache';

describe('flatten object', () => {
  it('should flatten 1 level object correctly with sorting', () => {
    expect(
      JSON.stringify(
        flattenObject({
          obj: {
            e: 2,
            c: 1,
            a: 1,
            b: 2
          }
        })
      )
    ).toStrictEqual(JSON.stringify({ a: 1, b: 2, c: 1, e: 2 }));
  });
  it('should flatten 1 level object correctly with sorting disabled', () => {
    expect(
      JSON.stringify(
        flattenObject({
          sort: false,
          obj: {
            e: 2,
            c: 1,
            a: 1,
            b: 2
          }
        })
      )
    ).toStrictEqual(JSON.stringify({ e: 2, c: 1, a: 1, b: 2 }));
  });
  it('should flatten multi level object correctly with sorting', () => {
    expect(
      JSON.stringify(
        flattenObject({
          obj: {
            e: 2,
            c: 1,
            a: 1,
            b: 2,
            d: {
              g: 4,
              z: 1,
              y: {
                o: 1,
                m: 1,
                x: 3
              },
              f: 3
            }
          }
        })
      )
    ).toStrictEqual(
      JSON.stringify({
        a: 1,
        b: 2,
        c: 1,
        'd.f': 3,
        'd.g': 4,
        'd.y.m': 1,
        'd.y.o': 1,
        'd.y.x': 3,
        'd.z': 1,
        e: 2
      })
    );
  });
  it('should flatten multi level object correctly with sorting disabled', () => {
    expect(
      JSON.stringify(
        flattenObject({
          sort: false,
          obj: {
            e: 2,
            c: 1,
            a: 1,
            b: 2,
            d: {
              g: 4,
              z: 1,
              y: {
                o: 1,
                m: 1,
                x: 3
              },
              f: 3
            }
          }
        })
      )
    ).toStrictEqual(
      JSON.stringify({ e: 2, c: 1, a: 1, b: 2, 'd.g': 4, 'd.z': 1, 'd.y.o': 1, 'd.y.m': 1, 'd.y.x': 3, 'd.f': 3 })
    );
  });
});
