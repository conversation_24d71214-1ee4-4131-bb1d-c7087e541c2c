import { sortArray, sortCompareNumber, sortCompareString } from '@utils/sort-array';
import get from 'lodash/get';

const caseOne = {
  input: [
    { id: 1, name: '19' },
    { id: 2, name: '17' },
    { id: 3, name: '16' },
    { id: 4, name: '15' },
    { id: 5, name: 0 },
    { id: 6, name: '2' },
    { id: 7, name: '3' },
    { id: 8, name: null },
    { id: 9, name: '' },
    { id: 10, name: undefined },
    { id: 11, name: 1 },
    { id: 12, name: 4 },
    { id: 13, name: 20 },
    { id: 14, name: undefined },
    { id: 15, name: 20.3 },
    { id: 16, name: 20.2 },
    { id: 17, name: 20.1 }
  ],
  output: [
    { id: 8, name: null },
    { id: 9, name: '' },
    { id: 10, name: undefined },
    { id: 14, name: undefined },
    { id: 5, name: 0 },
    { id: 11, name: 1 },
    { id: 6, name: '2' },
    { id: 7, name: '3' },
    { id: 12, name: 4 },
    { id: 4, name: '15' },
    { id: 3, name: '16' },
    { id: 2, name: '17' },
    { id: 1, name: '19' },
    { id: 13, name: 20 },
    { id: 17, name: 20.1 },
    { id: 16, name: 20.2 },
    { id: 15, name: 20.3 }
  ],
  strCompareOutput: [
    { id: 15, name: 20.3 },
    { id: 16, name: 20.2 },
    { id: 17, name: 20.1 },
    { id: 13, name: 20 },
    { id: 1, name: '19' },
    { id: 2, name: '17' },
    { id: 3, name: '16' },
    { id: 4, name: '15' },
    { id: 12, name: 4 },
    { id: 7, name: '3' },
    { id: 6, name: '2' },
    { id: 11, name: 1 },
    { id: 5, name: 0 },
    { id: 8, name: null },
    { id: 9, name: '' },
    { id: 10, name: undefined },
    { id: 14, name: undefined }
  ],
  descOutput: [
    { id: 8, name: null },
    { id: 9, name: '' },
    { id: 10, name: undefined },
    { id: 14, name: undefined },
    { id: 15, name: 20.3 },
    { id: 16, name: 20.2 },
    { id: 17, name: 20.1 },
    { id: 13, name: 20 },
    { id: 1, name: '19' },
    { id: 2, name: '17' },
    { id: 3, name: '16' },
    { id: 4, name: '15' },
    { id: 12, name: 4 },
    { id: 7, name: '3' },
    { id: 6, name: '2' },
    { id: 11, name: 1 },
    { id: 5, name: 0 }
  ]
};

const caseTwo = {
  input: [
    { id: 1, name: 'A3' },
    { id: 2, name: 'A1' },
    { id: 3, name: 'A2' },
    { id: 4, name: 'B2' },
    { id: 5, name: 'B3' },
    { id: 6, name: 'B1' },
    { id: 7, name: 'C3' },
    { id: 8, name: 'C1' },
    { id: 9, name: 'C2' },
    { id: 10, name: '50' },
    { id: 11, name: '40' }
  ],
  output: [
    { id: 11, name: '40' },
    { id: 10, name: '50' },
    { id: 2, name: 'A1' },
    { id: 3, name: 'A2' },
    { id: 1, name: 'A3' },
    { id: 6, name: 'B1' },
    { id: 4, name: 'B2' },
    { id: 5, name: 'B3' },
    { id: 8, name: 'C1' },
    { id: 9, name: 'C2' },
    { id: 7, name: 'C3' }
  ]
};

const caseThree = {
  input: [
    { id: 1, name: 'GO95' },
    { id: 2, name: 'GO97' },
    { id: 3, name: 'AC5' },
    { id: 4, name: 'AC4' },
    { id: 5, name: 'AD1' },
    { id: 6, name: 'AD3' },
    { id: 7, name: 'GO95A' },
    { id: 8, name: 'GO95B' },
    { id: 9, name: 'GO97A' }
  ],
  output: [
    { id: 4, name: 'AC4' },
    { id: 3, name: 'AC5' },
    { id: 5, name: 'AD1' },
    { id: 6, name: 'AD3' },
    { id: 1, name: 'GO95' },
    { id: 7, name: 'GO95A' },
    { id: 8, name: 'GO95B' },
    { id: 2, name: 'GO97' },
    { id: 9, name: 'GO97A' }
  ]
};

const caseFour = {
  input: [
    { id: 1, name: '100' },
    { id: 2, name: '95A' },
    { id: 3, name: '90' }
  ],
  output: [
    { id: 3, name: '90' },
    { id: 2, name: '95A' },
    { id: 1, name: '100' }
  ]
};

const caseFive = {
  input: [
    { id: 1, name: 'PS-19-IB' },
    { id: 2, name: '19BG' },
    { id: 3, name: 'GO19' },
    { id: 4, name: 'GO19A' }
  ],
  output: [
    { id: 2, name: '19BG' },
    { id: 3, name: 'GO19' },
    { id: 4, name: 'GO19A' },
    { id: 1, name: 'PS-19-IB' }
  ]
};

const caseSix = {
  input: [
    { id: 1, name: '3AB' },
    { id: 2, name: '1AB' },
    { id: 3, name: '2AB' }
  ],
  output: [
    { id: 2, name: '1AB' },
    { id: 3, name: '2AB' },
    { id: 1, name: '3AB' }
  ]
};

const caseSeven = {
  input: [
    { id: 1, name: 'PISCINA2R2' },
    { id: 2, name: 'PISCINA3R2' },
    { id: 3, name: 'PISCINA3R1' },
    { id: 4, name: 'PISCINA2R1A3' },
    { id: 5, name: 'PISCINA2R1A2' }
  ],
  output: [
    { id: 5, name: 'PISCINA2R1A2' },
    { id: 4, name: 'PISCINA2R1A3' },
    { id: 1, name: 'PISCINA2R2' },
    { id: 3, name: 'PISCINA3R1' },
    { id: 2, name: 'PISCINA3R2' }
  ]
};

const caseEight = {
  input: [
    { id: 1, name: '19' },
    { id: 2, name: '17' },
    { id: 3, name: '16' },
    { id: 4, name: '15' },
    { id: 5, name: 0 },
    { id: 6, name: '2' },
    { id: 7, name: '3' },
    { id: 8, name: null },
    { id: 9, name: '' },
    { id: 10, name: undefined },
    { id: 11, name: 1 },
    { id: 12, name: 4 },
    { id: 13, name: 20 },
    { id: 14, name: undefined },
    { id: 15, name: 20.3 },
    { id: 16, name: 20.2 },
    { id: 17, name: 20.1 }
  ],
  output: [
    { id: 5, name: 0 },
    { id: 11, name: 1 },
    { id: 6, name: '2' },
    { id: 7, name: '3' },
    { id: 12, name: 4 },
    { id: 4, name: '15' },
    { id: 3, name: '16' },
    { id: 2, name: '17' },
    { id: 1, name: '19' },
    { id: 13, name: 20 },
    { id: 17, name: 20.1 },
    { id: 16, name: 20.2 },
    { id: 15, name: 20.3 },
    { id: 8, name: null },
    { id: 9, name: '' },
    { id: 10, name: undefined },
    { id: 14, name: undefined }
  ],
  descOutput: [
    { id: 15, name: 20.3 },
    { id: 16, name: 20.2 },
    { id: 17, name: 20.1 },
    { id: 13, name: 20 },
    { id: 1, name: '19' },
    { id: 2, name: '17' },
    { id: 3, name: '16' },
    { id: 4, name: '15' },
    { id: 12, name: 4 },
    { id: 7, name: '3' },
    { id: 6, name: '2' },
    { id: 11, name: 1 },
    { id: 5, name: 0 },
    { id: 8, name: null },
    { id: 9, name: '' },
    { id: 10, name: undefined },
    { id: 14, name: undefined }
  ]
};

const sortByStringSetup = (input: { name: string | number }[], sort: 'asc' | 'desc' = 'asc') => {
  return sortArray({
    list: input,
    sortDir: sort,
    compareFunction: (a, b) => {
      const firstItem = get(a, 'name') as string;
      const secondItem = get(b, 'name') as string;
      return sortCompareString(firstItem, secondItem);
    }
  });
};
const sortByNumberSetup = (
  input: { name: string | number }[],
  sort: 'asc' | 'desc' = 'asc',
  hasEmptyValuePriority = true
) => {
  return sortArray({
    list: input,
    compareFunction: (a, b) => {
      const firstItem = get(a, 'name');
      const secondItem = get(b, 'name');
      return sortCompareNumber({ firstItem, secondItem, hasEmptyValuePriority, sortDir: sort });
    }
  });
};

describe('array sort by string using custom compare', () => {
  it('Should sort array of digits ascending', () => {
    expect(sortByStringSetup(caseOne.input)).toStrictEqual(caseOne.output);
  });

  it('Should sort array of digits descending', () => {
    expect(sortByStringSetup(caseOne.input, 'desc')).toStrictEqual(caseOne.strCompareOutput);
  });

  it('Should sort array of string digits with prefix letters ascending', () => {
    expect(sortByStringSetup(caseTwo.input)).toStrictEqual(caseTwo.output);
  });

  it('Should sort array of string digits with prefix letters descending', () => {
    const reversed = [...caseTwo.output].reverse();
    expect(sortByStringSetup(caseTwo.input, 'desc')).toStrictEqual(reversed);
  });

  it('Should sort array of string digits with prefix and suffix letters ascending', () => {
    expect(sortByStringSetup(caseThree.input)).toStrictEqual(caseThree.output);
  });

  it('Should sort array of string digits with prefix and suffix letters descending', () => {
    const reversed = [...caseThree.output].reverse();
    expect(sortByStringSetup(caseThree.input, 'desc')).toStrictEqual(reversed);
  });

  it('Should sort array of string digits with suffix letters ascending', () => {
    expect(sortByStringSetup(caseFour.input)).toStrictEqual(caseFour.output);
  });

  it('Should sort array of string digits with suffix letters descending', () => {
    const reversed = [...caseFour.output].reverse();
    expect(sortByStringSetup(caseFour.input, 'desc')).toStrictEqual(reversed);
  });

  it('Should sort array of string digits special characters with either suffix/prefix or suffix and prefix letters ascending', () => {
    expect(sortByStringSetup(caseFive.input)).toStrictEqual(caseFive.output);
  });

  it('Should sort array of string digits special characters with either suffix/prefix or suffix and prefix letters descending', () => {
    const reversed = [...caseFive.output].reverse();
    expect(sortByStringSetup(caseFive.input, 'desc')).toStrictEqual(reversed);
  });

  it('Should sort array of string digits with suffix letters ascending', () => {
    expect(sortByStringSetup(caseSix.input)).toStrictEqual(caseSix.output);
  });

  it('Should sort array of string digits with suffix letters descending', () => {
    const reversed = [...caseSix.output].reverse();
    expect(sortByStringSetup(caseSix.input, 'desc')).toStrictEqual(reversed);
  });

  it('Should sort array of string multiple combination of digits and letters ascending', () => {
    expect(sortByStringSetup(caseSeven.input)).toStrictEqual(caseSeven.output);
  });

  it('Should sort array of string multiple combination of digits and letters descending', () => {
    const reversed = [...caseSeven.output].reverse();
    expect(sortByStringSetup(caseSeven.input, 'desc')).toStrictEqual(reversed);
  });
});

describe('array sort by number using custom compare', () => {
  it('Should sort array of digits ascending while empty values has priority', () => {
    const result = sortByNumberSetup(caseOne.input, 'asc');
    expect(result).toStrictEqual(caseOne.output);
  });

  it('Should sort array of digits descending while empty values has priority', () => {
    expect(sortByNumberSetup(caseOne.input, 'desc')).toStrictEqual(caseOne.descOutput);
  });

  it('Should sort array of digits ascending while empty values has low priority', () => {
    expect(sortByNumberSetup(caseEight.input, 'asc', false)).toStrictEqual(caseEight.output);
  });

  it('Should sort array of digits descending while empty values has low priority', () => {
    expect(sortByNumberSetup(caseEight.input, 'desc', false)).toStrictEqual(caseEight.descOutput);
  });
});
