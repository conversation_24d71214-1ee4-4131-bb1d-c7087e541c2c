import { formatNumber } from '@utils/number';
import { isNumber } from 'lodash';

describe('number', () => {
  describe('formatNumber', () => {
    test('should return a number with the specified number of decimal places', () => {
      [
        { value: 0, expected: 0, shouldAddZeros: false },
        { value: 1, expected: 1, shouldAddZeros: false },
        { value: 2.99, expected: 2.9, fractionDigits: 1, shouldRound: false },
        { value: 2.99, expected: 2.99, fractionDigits: 2 },
        { value: 1.123456, expected: 1.1234, fractionDigits: 4, shouldRound: false },
        { value: 0.1875, expected: 0.2, fractionDigits: 1, shouldRound: true }
      ].forEach((data) => {
        const formattedValue = formatNumber(data.value, {
          shouldRound: data.shouldRound,
          fractionDigits: data.fractionDigits,
          shouldAddZeros: data.shouldAddZeros,
          asNumber: true
        });

        expect(formattedValue).toBe(data.expected);
        expect(isNumber(formattedValue)).toBeTruthy();
      });
    });

    test('should return a localed string with the specified number of decimal places', () => {
      [
        { value: 0, expected: '0', shouldAddZeros: false },
        { value: 1, expected: '1', shouldAddZeros: false },
        { value: 1324, expected: '1,324', fractionDigits: 0 },
        { value: 1324, expected: '1,324.0', fractionDigits: 1 },
        { value: 1324, expected: '1,324.00', fractionDigits: 2 },
        { value: 1324.123, expected: '1,324', fractionDigits: 0 },
        { value: 1324.123, expected: '1,324.1', fractionDigits: 1 },
        { value: 1324.123, expected: '1,324.12', fractionDigits: 2 },
        { value: 1324.1, expected: '1,324', fractionDigits: 0 },
        { value: 1324.1, expected: '1,324.1', fractionDigits: 1 },
        { value: 1324.1, expected: '1,324.10', fractionDigits: 2 }
      ].forEach((data) => {
        const formattedValue = formatNumber(data.value, {
          fractionDigits: data.fractionDigits,
          shouldAddZeros: data.shouldAddZeros
        });

        expect(formattedValue).toBe(data.expected);
        expect(isNumber(formattedValue)).toBeFalsy();
      });
    });
  });
});
