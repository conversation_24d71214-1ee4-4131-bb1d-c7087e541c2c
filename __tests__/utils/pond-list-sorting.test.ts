import {
  compareNumberBasedOnPondStatus,
  compareNumberBasedOnPondStatusAndMonitoringStatus
} from '@screens/farm/helpers/table-sort';
import { sortArray } from '@utils/sort-array';
import get from 'lodash/get';

const caseOne = {
  input: [
    { id: 1, weight: '19', stockedAt: '', harvest: { date: '' } },
    { id: 2, weight: '17', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 3, weight: '16', stockedAt: 'aa', harvest: { date: '' } },
    { id: 4, weight: '15', stockedAt: 'aa', harvest: { date: '' } },
    { id: 5, weight: 0, stockedAt: 'aa', harvest: { date: '' } },
    { id: 6, weight: '2', stockedAt: 'aa', harvest: { date: '' } },
    { id: 7, weight: '3', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 8, weight: null, stockedAt: 'aa', harvest: { date: '' } },
    { id: 9, weight: '', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 10, weight: undefined, stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 11, weight: 1, stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 12, weight: 4, stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 13, weight: 20, stockedAt: 'aa', harvest: { date: '' } },
    { id: 14, weight: undefined, stockedAt: 'aa', harvest: { date: '' } },
    { id: 15, weight: 20.3, stockedAt: 'aa', harvest: { date: '' } },
    { id: 16, weight: 20.2, stockedAt: 'aa', harvest: { date: '' } },
    { id: 17, weight: 20.1, stockedAt: 'aa', harvest: { date: '' } }
  ],
  output: [
    { id: 1, weight: '19', stockedAt: '', harvest: { date: '' } },
    { id: 2, weight: '17', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 7, weight: '3', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 9, weight: '', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 10, weight: undefined, stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 11, weight: 1, stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 12, weight: 4, stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 8, weight: null, stockedAt: 'aa', harvest: { date: '' } },
    { id: 14, weight: undefined, stockedAt: 'aa', harvest: { date: '' } },
    { id: 5, weight: 0, stockedAt: 'aa', harvest: { date: '' } },
    { id: 6, weight: '2', stockedAt: 'aa', harvest: { date: '' } },
    { id: 4, weight: '15', stockedAt: 'aa', harvest: { date: '' } },
    { id: 3, weight: '16', stockedAt: 'aa', harvest: { date: '' } },
    { id: 13, weight: 20, stockedAt: 'aa', harvest: { date: '' } },
    { id: 17, weight: 20.1, stockedAt: 'aa', harvest: { date: '' } },
    { id: 16, weight: 20.2, stockedAt: 'aa', harvest: { date: '' } },
    { id: 15, weight: 20.3, stockedAt: 'aa', harvest: { date: '' } }
  ],
  descOutput: [
    { id: 1, weight: '19', stockedAt: '', harvest: { date: '' } },
    { id: 2, weight: '17', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 7, weight: '3', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 9, weight: '', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 10, weight: undefined, stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 11, weight: 1, stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 12, weight: 4, stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 8, weight: null, stockedAt: 'aa', harvest: { date: '' } },
    { id: 14, weight: undefined, stockedAt: 'aa', harvest: { date: '' } },
    { id: 15, weight: 20.3, stockedAt: 'aa', harvest: { date: '' } },
    { id: 16, weight: 20.2, stockedAt: 'aa', harvest: { date: '' } },
    { id: 17, weight: 20.1, stockedAt: 'aa', harvest: { date: '' } },
    { id: 13, weight: 20, stockedAt: 'aa', harvest: { date: '' } },
    { id: 3, weight: '16', stockedAt: 'aa', harvest: { date: '' } },
    { id: 4, weight: '15', stockedAt: 'aa', harvest: { date: '' } },
    { id: 6, weight: '2', stockedAt: 'aa', harvest: { date: '' } },
    { id: 5, weight: 0, stockedAt: 'aa', harvest: { date: '' } }
  ]
};

const caseTwo = {
  input: [
    { id: 1, weight: '19', stockedAt: '', harvest: { date: '' } },
    { id: 2, weight: '17', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 3, weight: '16', stockedAt: 'aa', harvest: { date: '' } },
    { id: 4, weight: '15', stockedAt: 'aa', harvest: { date: '' } },
    { id: 5, weight: 0, stockedAt: 'aa', harvest: { date: '' } },
    { id: 6, weight: '2', stockedAt: 'aa', harvest: { date: '' } },
    { id: 7, weight: '3', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 8, weight: null, stockedAt: 'aa', harvest: { date: '' } },
    { id: 9, weight: '', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 10, weight: undefined, stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 11, weight: 1, stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 12, weight: 4, stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 13, weight: 20, stockedAt: 'aa', harvest: { date: '' } },
    { id: 14, weight: undefined, stockedAt: 'aa', harvest: { date: '' } },
    { id: 15, weight: 20.3, stockedAt: 'aa', harvest: { date: '' } },
    { id: 16, weight: 20.2, stockedAt: 'aa', harvest: { date: '' } },
    { id: 17, weight: 20.1, stockedAt: 'aa', harvest: { date: '' } }
  ],
  output: [
    { id: 8, weight: null, stockedAt: 'aa', harvest: { date: '' } },
    { id: 14, weight: undefined, stockedAt: 'aa', harvest: { date: '' } },
    { id: 5, weight: 0, stockedAt: 'aa', harvest: { date: '' } },
    { id: 6, weight: '2', stockedAt: 'aa', harvest: { date: '' } },
    { id: 4, weight: '15', stockedAt: 'aa', harvest: { date: '' } },
    { id: 3, weight: '16', stockedAt: 'aa', harvest: { date: '' } },
    { id: 13, weight: 20, stockedAt: 'aa', harvest: { date: '' } },
    { id: 17, weight: 20.1, stockedAt: 'aa', harvest: { date: '' } },
    { id: 16, weight: 20.2, stockedAt: 'aa', harvest: { date: '' } },
    { id: 15, weight: 20.3, stockedAt: 'aa', harvest: { date: '' } },
    { id: 1, weight: '19', stockedAt: '', harvest: { date: '' } },
    { id: 2, weight: '17', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 7, weight: '3', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 9, weight: '', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 10, weight: undefined, stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 11, weight: 1, stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 12, weight: 4, stockedAt: 'aa', harvest: { date: 'aa' } }
  ],
  descOutput: [
    { id: 8, weight: null, stockedAt: 'aa', harvest: { date: '' } },
    { id: 14, weight: undefined, stockedAt: 'aa', harvest: { date: '' } },
    { id: 15, weight: 20.3, stockedAt: 'aa', harvest: { date: '' } },
    { id: 16, weight: 20.2, stockedAt: 'aa', harvest: { date: '' } },
    { id: 17, weight: 20.1, stockedAt: 'aa', harvest: { date: '' } },
    { id: 13, weight: 20, stockedAt: 'aa', harvest: { date: '' } },
    { id: 3, weight: '16', stockedAt: 'aa', harvest: { date: '' } },
    { id: 4, weight: '15', stockedAt: 'aa', harvest: { date: '' } },
    { id: 6, weight: '2', stockedAt: 'aa', harvest: { date: '' } },
    { id: 5, weight: 0, stockedAt: 'aa', harvest: { date: '' } },
    { id: 1, weight: '19', stockedAt: '', harvest: { date: '' } },
    { id: 2, weight: '17', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 7, weight: '3', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 9, weight: '', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 10, weight: undefined, stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 11, weight: 1, stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 12, weight: 4, stockedAt: 'aa', harvest: { date: 'aa' } }
  ]
};

const caseThree = {
  input: [
    { id: 1, weight: '19', stockedAt: '', harvest: { date: '' } },
    { id: 2, weight: '17', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 3, weight: '16', stockedAt: 'aa', harvest: { date: '' } },
    { id: 4, weight: '15', stockedAt: 'aa', harvest: { date: '' } },
    { id: 5, weight: 0, stockedAt: 'aa', harvest: { date: '' } },
    { id: 6, weight: '2', stockedAt: 'aa', harvest: { date: '' } },
    { id: 7, weight: '3', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 8, weight: null, stockedAt: 'aa', harvest: { date: '' } },
    { id: 9, weight: '', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 10, weight: undefined, stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 11, weight: 1, stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 12, weight: 4, stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 13, weight: 20, stockedAt: 'aa', harvest: { date: '' } },
    { id: 14, weight: undefined, stockedAt: 'aa', harvest: { date: '' } },
    { id: 15, weight: 20.3, stockedAt: 'aa', harvest: { date: '' } },
    { id: 16, weight: 20.2, stockedAt: 'aa', harvest: { date: '' } },
    { id: 17, weight: 20.1, stockedAt: 'aa', harvest: { date: '' } }
  ],
  output: [
    { id: 5, weight: 0, stockedAt: 'aa', harvest: { date: '' } },
    { id: 6, weight: '2', stockedAt: 'aa', harvest: { date: '' } },
    { id: 4, weight: '15', stockedAt: 'aa', harvest: { date: '' } },
    { id: 3, weight: '16', stockedAt: 'aa', harvest: { date: '' } },
    { id: 13, weight: 20, stockedAt: 'aa', harvest: { date: '' } },
    { id: 17, weight: 20.1, stockedAt: 'aa', harvest: { date: '' } },
    { id: 16, weight: 20.2, stockedAt: 'aa', harvest: { date: '' } },
    { id: 15, weight: 20.3, stockedAt: 'aa', harvest: { date: '' } },
    { id: 8, weight: null, stockedAt: 'aa', harvest: { date: '' } },
    { id: 14, weight: undefined, stockedAt: 'aa', harvest: { date: '' } },
    { id: 1, weight: '19', stockedAt: '', harvest: { date: '' } },
    { id: 2, weight: '17', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 7, weight: '3', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 9, weight: '', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 10, weight: undefined, stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 11, weight: 1, stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 12, weight: 4, stockedAt: 'aa', harvest: { date: 'aa' } }
  ],
  descOutput: [
    { id: 15, weight: 20.3, stockedAt: 'aa', harvest: { date: '' } },
    { id: 16, weight: 20.2, stockedAt: 'aa', harvest: { date: '' } },
    { id: 17, weight: 20.1, stockedAt: 'aa', harvest: { date: '' } },
    { id: 13, weight: 20, stockedAt: 'aa', harvest: { date: '' } },
    { id: 3, weight: '16', stockedAt: 'aa', harvest: { date: '' } },
    { id: 4, weight: '15', stockedAt: 'aa', harvest: { date: '' } },
    { id: 6, weight: '2', stockedAt: 'aa', harvest: { date: '' } },
    { id: 5, weight: 0, stockedAt: 'aa', harvest: { date: '' } },
    { id: 8, weight: null, stockedAt: 'aa', harvest: { date: '' } },
    { id: 14, weight: undefined, stockedAt: 'aa', harvest: { date: '' } },
    { id: 1, weight: '19', stockedAt: '', harvest: { date: '' } },
    { id: 2, weight: '17', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 7, weight: '3', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 9, weight: '', stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 10, weight: undefined, stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 11, weight: 1, stockedAt: 'aa', harvest: { date: 'aa' } },
    { id: 12, weight: 4, stockedAt: 'aa', harvest: { date: 'aa' } }
  ]
};

const caseFour = {
  input: [
    { id: 1, weight: '19', stockedAt: '', harvest: { date: '' }, monitoring: 'aa' },
    { id: 2, weight: '17', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 3, weight: '16', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 4, weight: '15', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 5, weight: 0, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 6, weight: '2', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 7, weight: '3', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 8, weight: null, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 9, weight: '', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 10, weight: undefined, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 11, weight: 1, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 12, weight: 4, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 13, weight: 20, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 14, weight: undefined, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 15, weight: 20.3, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 16, weight: 20.2, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 17, weight: 20.1, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 18, weight: 1, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 19, weight: undefined, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 20, weight: null, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 21, weight: '5', stockedAt: 'aa', harvest: { date: '' }, monitoring: '' }
  ],
  output: [
    { id: 1, weight: '19', stockedAt: '', harvest: { date: '' }, monitoring: 'aa' },
    { id: 2, weight: '17', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 7, weight: '3', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 9, weight: '', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 10, weight: undefined, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 11, weight: 1, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 12, weight: 4, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 18, weight: 1, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 19, weight: undefined, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 20, weight: null, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 21, weight: '5', stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 8, weight: null, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 14, weight: undefined, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 5, weight: 0, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 6, weight: '2', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 4, weight: '15', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 3, weight: '16', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 13, weight: 20, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 17, weight: 20.1, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 16, weight: 20.2, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 15, weight: 20.3, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' }
  ],
  descOutput: [
    { id: 1, weight: '19', stockedAt: '', harvest: { date: '' }, monitoring: 'aa' },
    { id: 2, weight: '17', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 7, weight: '3', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 9, weight: '', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 10, weight: undefined, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 11, weight: 1, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 12, weight: 4, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 18, weight: 1, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 19, weight: undefined, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 20, weight: null, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 21, weight: '5', stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 8, weight: null, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 14, weight: undefined, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 15, weight: 20.3, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 16, weight: 20.2, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 17, weight: 20.1, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 13, weight: 20, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 3, weight: '16', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 4, weight: '15', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 6, weight: '2', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 5, weight: 0, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' }
  ]
};

const caseFive = {
  input: [
    { id: 1, weight: '19', stockedAt: '', harvest: { date: '' }, monitoring: 'aa' },
    { id: 2, weight: '17', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 3, weight: '16', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 4, weight: '15', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 5, weight: 0, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 6, weight: '2', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 7, weight: '3', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 8, weight: null, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 9, weight: '', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 10, weight: undefined, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 11, weight: 1, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 12, weight: 4, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 13, weight: 20, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 14, weight: undefined, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 15, weight: 20.3, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 16, weight: 20.2, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 17, weight: 20.1, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 18, weight: 1, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 19, weight: undefined, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 20, weight: null, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 21, weight: '5', stockedAt: 'aa', harvest: { date: '' }, monitoring: '' }
  ],
  output: [
    { id: 1, weight: '19', stockedAt: '', harvest: { date: '' }, monitoring: 'aa' },
    { id: 2, weight: '17', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 7, weight: '3', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 9, weight: '', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 10, weight: undefined, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 11, weight: 1, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 12, weight: 4, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 18, weight: 1, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 19, weight: undefined, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 20, weight: null, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 21, weight: '5', stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 5, weight: 0, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 6, weight: '2', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 4, weight: '15', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 3, weight: '16', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 13, weight: 20, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 17, weight: 20.1, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 16, weight: 20.2, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 15, weight: 20.3, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 8, weight: null, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 14, weight: undefined, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' }
  ],
  descOutput: [
    { id: 1, weight: '19', stockedAt: '', harvest: { date: '' }, monitoring: 'aa' },
    { id: 2, weight: '17', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 7, weight: '3', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 9, weight: '', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 10, weight: undefined, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 11, weight: 1, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 12, weight: 4, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 18, weight: 1, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 19, weight: undefined, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 20, weight: null, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 21, weight: '5', stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 15, weight: 20.3, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 16, weight: 20.2, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 17, weight: 20.1, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 13, weight: 20, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 3, weight: '16', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 4, weight: '15', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 6, weight: '2', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 5, weight: 0, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 8, weight: null, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 14, weight: undefined, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' }
  ]
};

const caseSix = {
  input: [
    { id: 1, weight: '19', stockedAt: '', harvest: { date: '' }, monitoring: 'aa' },
    { id: 2, weight: '17', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 3, weight: '16', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 4, weight: '15', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 5, weight: 0, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 6, weight: '2', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 7, weight: '3', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 8, weight: null, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 9, weight: '', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 10, weight: undefined, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 11, weight: 1, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 12, weight: 4, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 13, weight: 20, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 14, weight: undefined, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 15, weight: 20.3, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 16, weight: 20.2, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 17, weight: 20.1, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 18, weight: 1, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 19, weight: undefined, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 20, weight: null, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 21, weight: '5', stockedAt: 'aa', harvest: { date: '' }, monitoring: '' }
  ],
  output: [
    { id: 18, weight: 1, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 19, weight: undefined, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 20, weight: null, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 21, weight: '5', stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 8, weight: null, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 14, weight: undefined, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 5, weight: 0, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 6, weight: '2', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 4, weight: '15', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 3, weight: '16', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 13, weight: 20, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 17, weight: 20.1, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 16, weight: 20.2, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 15, weight: 20.3, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 1, weight: '19', stockedAt: '', harvest: { date: '' }, monitoring: 'aa' },
    { id: 2, weight: '17', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 7, weight: '3', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 9, weight: '', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 10, weight: undefined, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 11, weight: 1, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 12, weight: 4, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' }
  ],
  descOutput: [
    { id: 18, weight: 1, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 19, weight: undefined, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 20, weight: null, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 21, weight: '5', stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 8, weight: null, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 14, weight: undefined, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 15, weight: 20.3, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 16, weight: 20.2, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 17, weight: 20.1, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 13, weight: 20, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 3, weight: '16', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 4, weight: '15', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 6, weight: '2', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 5, weight: 0, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 1, weight: '19', stockedAt: '', harvest: { date: '' }, monitoring: 'aa' },
    { id: 2, weight: '17', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 7, weight: '3', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 9, weight: '', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 10, weight: undefined, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 11, weight: 1, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 12, weight: 4, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' }
  ]
};

const caseSeven = {
  input: [
    { id: 1, weight: '19', stockedAt: '', harvest: { date: '' }, monitoring: 'aa' },
    { id: 2, weight: '17', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 3, weight: '16', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 4, weight: '15', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 5, weight: 0, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 6, weight: '2', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 7, weight: '3', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 8, weight: null, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 9, weight: '', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 10, weight: undefined, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 11, weight: 1, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 12, weight: 4, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 13, weight: 20, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 14, weight: undefined, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 15, weight: 20.3, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 16, weight: 20.2, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 17, weight: 20.1, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 18, weight: 1, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 19, weight: undefined, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 20, weight: null, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 21, weight: '5', stockedAt: 'aa', harvest: { date: '' }, monitoring: '' }
  ],
  output: [
    { id: 18, weight: 1, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 19, weight: undefined, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 20, weight: null, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 21, weight: '5', stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 5, weight: 0, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 6, weight: '2', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 4, weight: '15', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 3, weight: '16', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 13, weight: 20, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 17, weight: 20.1, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 16, weight: 20.2, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 15, weight: 20.3, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 8, weight: null, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 14, weight: undefined, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 1, weight: '19', stockedAt: '', harvest: { date: '' }, monitoring: 'aa' },
    { id: 2, weight: '17', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 7, weight: '3', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 9, weight: '', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 10, weight: undefined, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 11, weight: 1, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 12, weight: 4, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' }
  ],
  descOutput: [
    { id: 18, weight: 1, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 19, weight: undefined, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 20, weight: null, stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 21, weight: '5', stockedAt: 'aa', harvest: { date: '' }, monitoring: '' },
    { id: 15, weight: 20.3, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 16, weight: 20.2, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 17, weight: 20.1, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 13, weight: 20, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 3, weight: '16', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 4, weight: '15', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 6, weight: '2', stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 5, weight: 0, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 8, weight: null, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 14, weight: undefined, stockedAt: 'aa', harvest: { date: '' }, monitoring: 'aa' },
    { id: 1, weight: '19', stockedAt: '', harvest: { date: '' }, monitoring: 'aa' },
    { id: 2, weight: '17', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 7, weight: '3', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 9, weight: '', stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 10, weight: undefined, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 11, weight: 1, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' },
    { id: 12, weight: 4, stockedAt: 'aa', harvest: { date: 'aa' }, monitoring: 'aa' }
  ]
};

const sortByNumberSetup = (
  input: typeof caseOne.input,
  sort: 'asc' | 'desc' = 'asc',
  hasEmptyPondPriority = true,
  hasEmptyValuePriority = true
) => {
  return sortArray({
    list: input,
    compareFunction: (a, b) => {
      const firstItemValue = get(a, 'weight');
      const secondItemValue = get(b, 'weight');
      const { stockedAt: firstItemStockedAt, harvest: firstItemHarvest } = a ?? {};
      const { stockedAt: secondItemStockedAt, harvest: secondItemHarvest } = b ?? {};

      return compareNumberBasedOnPondStatus({
        firstItem: firstItemValue,
        secondItem: secondItemValue,
        isFirstItemPondEmpty: !firstItemStockedAt || !!firstItemHarvest?.date,
        isSecondItemPondEmpty: !secondItemStockedAt || !!secondItemHarvest?.date,
        hasEmptyPondPriority,
        hasEmptyValuePriority,
        sortDir: sort
      });
    }
  });
};

const sortByNumberWithMonitoringSetup = (
  input: typeof caseFour.input,
  sort: 'asc' | 'desc' = 'asc',
  hasEmptyValuePriority = true,
  hasEmptyPondPriority = true
) => {
  return sortArray({
    list: input,
    compareFunction: (a, b) => {
      const firstItemValue = get(a, 'weight');
      const secondItemValue = get(b, 'weight');
      const { stockedAt: firstItemStockedAt, harvest: firstItemHarvest, monitoring: firstItemMonitoredAt } = a ?? {};
      const { stockedAt: secondItemStockedAt, harvest: secondItemHarvest, monitoring: secondItemMonitoredAt } = b ?? {};

      return compareNumberBasedOnPondStatusAndMonitoringStatus({
        firstItem: firstItemValue,
        secondItem: secondItemValue,
        isFirstItemPondEmpty: !firstItemStockedAt || !!firstItemHarvest?.date,
        isSecondItemPondEmpty: !secondItemStockedAt || !!secondItemHarvest?.date,
        hasFirstItemMonitoring: !!firstItemMonitoredAt,
        hasSecondItemMonitoring: !!secondItemMonitoredAt,
        hasEmptyValuePriority,
        hasEmptyPondPriority,
        sortDir: sort
      });
    }
  });
};

describe('pond list sort by digits using custom compare', () => {
  it('Should sort array of digits ascending while empty pond has priority with empty values higher priority', () => {
    expect(sortByNumberSetup(caseOne.input, 'asc')).toStrictEqual(caseOne.output);
  });

  it('Should sort array of digits descending while empty pond has priority with empty values higher priority', () => {
    expect(sortByNumberSetup(caseOne.input, 'desc')).toStrictEqual(caseOne.descOutput);
  });

  it('Should sort array of digits ascending while empty pond has low priority with empty values higher priority', () => {
    expect(sortByNumberSetup(caseTwo.input, 'asc', false)).toStrictEqual(caseTwo.output);
  });

  it('Should sort array of digits descending while empty pond has low priority with empty values higher priority', () => {
    expect(sortByNumberSetup(caseTwo.input, 'desc', false)).toStrictEqual(caseTwo.descOutput);
  });

  it('Should sort array of digits ascending while empty pond has low priority , empty values has low priority', () => {
    expect(sortByNumberSetup(caseThree.input, 'asc', false, false)).toStrictEqual(caseThree.output);
  });

  it('Should sort array of digits descending while empty pond has low priority , empty values has low priority', () => {
    expect(sortByNumberSetup(caseThree.input, 'desc', false, false)).toStrictEqual(caseThree.descOutput);
  });

  it('Should sort array of digits ascending while empty pond has priority then empty monitoring then empty values after', () => {
    expect(sortByNumberWithMonitoringSetup(caseFour.input, 'asc')).toStrictEqual(caseFour.output);
  });

  it('Should sort array of digits descending while empty pond has priority then empty monitoring  then empty values after', () => {
    expect(sortByNumberWithMonitoringSetup(caseFour.input, 'desc')).toStrictEqual(caseFour.descOutput);
  });

  it('Should sort array of digits ascending while empty pond has priority then empty monitoring  and empty values has lowest priority', () => {
    expect(sortByNumberWithMonitoringSetup(caseFive.input, 'asc', false)).toStrictEqual(caseFive.output);
  });

  it('Should sort array of digits descending while empty pond has priority then empty monitoring  and empty values has lowest priority', () => {
    expect(sortByNumberWithMonitoringSetup(caseFive.input, 'desc', false)).toStrictEqual(caseFive.descOutput);
  });

  it('Should sort array of digits ascending while empty monitoring has priority then empty values and empty pond has lowest priority', () => {
    expect(sortByNumberWithMonitoringSetup(caseSix.input, 'asc', true, false)).toStrictEqual(caseSix.output);
  });

  it('Should sort array of digits descending while empty monitoring has priority then empty values and empty pond has lowest priority', () => {
    expect(sortByNumberWithMonitoringSetup(caseSix.input, 'desc', true, false)).toStrictEqual(caseSix.descOutput);
  });

  it('Should sort array of digits ascending while values has highest priority then empty values then empty pond has lowest priority', () => {
    expect(sortByNumberWithMonitoringSetup(caseSeven.input, 'asc', false, false)).toStrictEqual(caseSeven.output);
  });

  it('Should sort array of digits descending while values has highest priority then empty values then empty pond has lowest priority', () => {
    expect(sortByNumberWithMonitoringSetup(caseSeven.input, 'desc', false, false)).toStrictEqual(caseSeven.descOutput);
  });
});
