import { errorsMap, getErrorMsg, throwErrorMsg } from '@utils/errors';

const mockDefaultMsg = 't_something_went_wrong';

jest.mock('@i18n/get-trans', () => ({
  getTrans: jest.fn().mockImplementation(() => ({
    trans: jest.fn().mockImplementation((val) => val)
  }))
}));

describe('getErrorMsg function', () => {
  it('Shoud return the default key if error does not exist in the objext', () => {
    [undefined, null, '', 'doesNotExist'].forEach((error) => {
      const result = getErrorMsg(error);
      expect(result).toBe(mockDefaultMsg);
    });
  });

  it('Shoud return the error key if error exist in the object', () => {
    Object.entries(errorsMap).forEach(([key, val]) => {
      const result = getErrorMsg(key);
      expect(result).toBe(val);
    });
  });

  it('should return the default key if error is not a string', () => {
    [1, true, {}, [], () => {}].forEach((error) => {
      const result = getErrorMsg(getErrorMsg(error as string));
      expect(result).toBe(mockDefaultMsg);
    });
  });

  it('Should return the defaultMsg from params', () => {
    const newDefaultMsg = 'new_default_msg';
    [undefined, null, '', 'doesNotExist'].forEach((error) => {
      const result = getErrorMsg(error, newDefaultMsg);
      expect(result).toBe(newDefaultMsg);
    });
  });
});
// '', 'doesNotExist'
const mockErrorArray = [{ message: 'accountNotFound' }, { message: 'emailOrPassowrdInCorrect' }];
const mockErrorObject = { message: 'accountNotFound' };

describe('throwErrorMsg function', () => {
  describe('error is undefined or null', () => {
    it('Should throw an error with the default message', () => {
      [undefined, null, ''].forEach((error) => {
        const result = () => throwErrorMsg(error);
        expect(result).toThrow(mockDefaultMsg);
      });
    });

    it('Should throw an error with the default message from the method params', () => {
      [undefined, null, ''].forEach((error) => {
        const result = () => throwErrorMsg(error, 'new_default_msg');
        expect(result).toThrow('new_default_msg');
      });
    });
  });

  describe('error is a string', () => {
    it('Should throw an error with the message from the object', () => {
      Object.entries(errorsMap).forEach(([key, val]) => {
        const result = () => throwErrorMsg(key);
        expect(result).toThrow(val);
      });
    });

    it('Should throw an error with the default message', () => {
      ['doesNotExist'].forEach((error) => {
        const result = () => throwErrorMsg(error);
        expect(result).toThrow(mockDefaultMsg);
      });
    });

    it('Should throw an error with the default message from method param', () => {
      ['doesNotExist'].forEach((error) => {
        const result = () => throwErrorMsg(error, 'new_default_msg');
        expect(result).toThrow('new_default_msg');
      });
    });
  });

  describe('error is an array', () => {
    it('Should throw an error with the message from the first object in the array', () => {
      const result = () => throwErrorMsg(mockErrorArray);
      expect(result).toThrow(errorsMap[mockErrorArray[0].message]);
    });

    it('Should throw an error with the default message', () => {
      const result = () => throwErrorMsg([]);
      expect(result).toThrow(mockDefaultMsg);
    });

    it('Should throw an error with the default message from method param', () => {
      const result = () => throwErrorMsg([], 'new_default_msg');
      expect(result).toThrow('new_default_msg');
    });
  });

  describe('error is an object', () => {
    it('Should throw an error with the message from the object', () => {
      const result = () => throwErrorMsg(mockErrorObject);
      expect(result).toThrow(errorsMap[mockErrorObject.message]);
    });

    it('Should throw an error with the default message if array does not have a message', () => {
      const result = () => throwErrorMsg({});
      expect(result).toThrow(mockDefaultMsg);
    });

    it('Should throw an error with the default message from method param', () => {
      const result = () => throwErrorMsg({}, 'new_default_msg');
      expect(result).toThrow('new_default_msg');
    });
  });
});
