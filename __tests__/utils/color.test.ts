import { cls } from '@utils/color';

describe('cls function', () => {
  it('should remove the extra spaces between class names', () => {
    const res = cls('class1       class2       class3');
    expect(res).toStrictEqual('class1 class2 class3');
  });

  it('should remove undefined from the string', () => {
    const res = cls('class1 undefined undefined class2 undefined');
    expect(res).toStrictEqual('class1 class2');
  });
});
