import { getOrdinal } from '@utils/get-ordinal';

describe('getOrdinal function', () => {
  it('Should return the value as is if the number is NaN', () => {
    const inputs = ['11d', 'str'];
    inputs.map((input) => {
      expect(getOrdinal(input)).toBe(input);
    });
  });

  it('Should return the value as is if the number is not integer', () => {
    const inputs = [1.2, 2.1, 101.222, '12.2', -1.2];
    inputs.map((input) => {
      expect(getOrdinal(input)).toBe(input);
    });
  });

  it('Should return suffix as "th" if the last two digits is 11, 12 or 13', () => {
    const inputs = [11, 111, 12, 212, 13, 213];
    inputs.map((input) => {
      expect(getOrdinal(input)).toBe(input + 'th');
      expect(getOrdinal(input.toString())).toBe(input + 'th');
    });
  });

  it('Should return suffix as "st" if number ends with 1', () => {
    const input = [1, 21, 41, 101, 221];
    input.map((n) => {
      expect(getOrdinal(n)).toBe(n + 'st');
      //   should perform the same if input is string
      expect(getOrdinal(n.toString())).toBe(n + 'st');
    });
  });

  it('Should return suffix as "nd" if number ends with 2', () => {
    const input = [2, 22, 42, 102, 222];
    input.map((n) => {
      expect(getOrdinal(n)).toBe(n + 'nd');
      //   should perform the same if input is string
      expect(getOrdinal(n.toString())).toBe(n + 'nd');
    });
  });

  it('Should return suffix as "nd" if number ends with 2', () => {
    const input = [3, 23, 43, 103, 223];
    input.map((n) => {
      expect(getOrdinal(n)).toBe(n + 'rd');
      //   should perform the same if input is string
      expect(getOrdinal(n.toString())).toBe(n + 'rd');
    });
  });
});
