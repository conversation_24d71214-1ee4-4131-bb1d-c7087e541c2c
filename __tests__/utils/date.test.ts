import { getUniDate } from '@utils/date';
import { DateTime, Settings } from 'luxon';

function getTimes({
  toTimezone,
  fromTimezone,
  month,
  plusTz = '+04:00'
}: {
  toTimezone: string;
  fromTimezone: string;
  month: number;
  plusTz: string;
}) {
  const hours = [1, 2, 3, 4, 5, 6, 8, 9, 10, 12, 13, 18, 19, 20, 21, 22, 23];
  return [
    ...hours.map((hour) => {
      return {
        hour,
        from: DateTime.fromObject({ year: 2022, month, day: 22, hour: hour }).toJSDate(), // browser time
        to: `2022-${month.toString().padStart(2, '0')}-22T${hour.toString().padStart(2, '0')}:00:00.000Z`, // farm time
        toTimezone, // farm timezone
        fromTimezone // backend timezone
      };
    }),
    ...hours.map((hour) => {
      return {
        hour,
        from: `2022-${month.toString().padStart(2, '0')}-22T${hour.toString().padStart(2, '0')}:00:00Z`, // browser time
        to: `2022-${month.toString().padStart(2, '0')}-22T${hour.toString().padStart(2, '0')}:00:00.000Z`, // farm time
        toTimezone, // farm timezone
        fromTimezone // backend timezone
      };
    }),
    ...hours.map((hour) => {
      return {
        hour,
        from: new Date(
          `2022-${month.toString().padStart(2, '0')}-22T${hour.toString().padStart(2, '0')}:00:00${plusTz}`
        ), // browser time
        to: `2022-${month.toString().padStart(2, '0')}-22T${hour.toString().padStart(2, '0')}:00:00.000Z`, // farm time
        toTimezone, // farm timezone
        fromTimezone // backend timezone
      };
    }),
    ...hours.map((hour) => {
      return {
        hour,
        from: `2022-${month.toString().padStart(2, '0')}-22T${hour.toString().padStart(2, '0')}:00:00${plusTz}`, // browser time
        to: `2022-${month.toString().padStart(2, '0')}-22T${hour.toString().padStart(2, '0')}:00:00.000Z`, // farm time
        toTimezone, // farm timezone
        fromTimezone // backend timezone
      };
    })
  ];
}

describe('test date for frontend', () => {
  it('should test Asia/Dubai --to-> Asia/Dubai', () => {
    const browserTimezone = 'Asia/Dubai';
    Settings.defaultZone = browserTimezone;
    const times = getTimes({ toTimezone: 'Asia/Dubai', fromTimezone: 'UTC', month: 10, plusTz: '+04:00' });
    times.forEach((o, i) => {
      const res = getUniDate({
        date: o.from as unknown as string,
        toTimezone: o.toTimezone,
        fromTimezone: o.fromTimezone,
        format: "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
      });
      expect(res.f).toStrictEqual(o.to);
    });
  });

  it('should test America/New_York --to-> Asia/Dubai', () => {
    const browserTimezone = 'America/New_York';
    Settings.defaultZone = browserTimezone;
    const times = getTimes({ toTimezone: 'Asia/Dubai', fromTimezone: 'UTC', month: 10, plusTz: '-04:00' });
    times.forEach((o, i) => {
      const res = getUniDate({
        date: o.from as unknown as string,
        toTimezone: o.toTimezone,
        fromTimezone: o.fromTimezone,
        format: "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
      });
      expect(res.f).toStrictEqual(o.to);
    });
  });

  it('should test Europe/Berlin --to-> Asia/Dubai', () => {
    const browserTimezone = 'Europe/Berlin';
    Settings.defaultZone = browserTimezone;
    const times = getTimes({ toTimezone: 'Asia/Dubai', fromTimezone: 'UTC', month: 10, plusTz: '+02:00' });
    times.forEach((o, i) => {
      const res = getUniDate({
        date: o.from as unknown as string,
        toTimezone: o.toTimezone,
        fromTimezone: o.fromTimezone,
        format: "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
      });
      expect(res.f).toStrictEqual(o.to);
    });
  });

  it('should test America/New_York --to-> America/New_York', () => {
    const browserTimezone = 'America/New_York';
    Settings.defaultZone = browserTimezone;
    const times = getTimes({ toTimezone: 'America/New_York', fromTimezone: 'UTC', month: 10, plusTz: '-04:00' });
    times.forEach((o, i) => {
      const res = getUniDate({
        date: o.from as unknown as string,
        toTimezone: o.toTimezone,
        fromTimezone: o.fromTimezone,
        format: "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
      });
      expect(res.f).toStrictEqual(o.to);
    });
  });

  it('should test Asia/Dubai --to-> Asia/Dubai', () => {
    const browserTimezone = 'America/New_York';
    Settings.defaultZone = browserTimezone;
    const times = getTimes({ toTimezone: 'Asia/Dubai', fromTimezone: 'UTC', month: 10, plusTz: '-04:00' });
    times.forEach((o, i) => {
      const res = getUniDate({
        date: o.from as unknown as string,
        toTimezone: o.toTimezone,
        fromTimezone: o.fromTimezone,
        format: "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
      });
      expect(res.f).toStrictEqual(o.to);
    });
  });

  it('should test Asia/Dubai --to-> America/New_York', () => {
    const browserTimezone = 'Asia/Dubai';
    Settings.defaultZone = browserTimezone;

    const times = getTimes({ toTimezone: 'America/New_York', fromTimezone: 'UTC', month: 10, plusTz: '+04:00' });
    times.forEach((o, i) => {
      const res = getUniDate({
        date: o.from as unknown as string,
        toTimezone: o.toTimezone,
        fromTimezone: o.fromTimezone,
        format: "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
      });
      expect(res.f).toStrictEqual(o.to);
    });
  });

  it('should test America/Vancouver --to-> America/New_York', () => {
    const browserTimezone = 'America/New_York';
    Settings.defaultZone = browserTimezone;

    const times = getTimes({ toTimezone: 'America/New_York', fromTimezone: 'UTC', month: 10, plusTz: '-04:00' });
    times.forEach((o, i) => {
      const res = getUniDate({
        date: o.from as unknown as string,
        toTimezone: o.toTimezone,
        fromTimezone: o.fromTimezone,
        format: "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
      });
      expect(res.f).toStrictEqual(o.to);
    });
  });
  it('should return hours inside the formatted date', () => {
    const { jsDate } = getUniDate({
      fromTimezone: 'UTC',
      date: '2021-10-22T17:00:00.000Z'
    });
    expect(DateTime.fromJSDate(jsDate).hour).toBe(17);
  });
  it('should return minutes inside the formatted date', () => {
    const { jsDate } = getUniDate({
      fromTimezone: 'UTC',
      date: '2021-10-22T17:33:00.000Z'
    });
    expect(DateTime.fromJSDate(jsDate).minute).toBe(33);
  });
  it('should return seconds inside the formatted date', () => {
    const { jsDate } = getUniDate({
      fromTimezone: 'UTC',
      date: '2021-10-22T17:33:04.000Z'
    });
    expect(DateTime.fromJSDate(jsDate).second).toBe(4);
  });
});
