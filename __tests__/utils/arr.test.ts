import { arrayToHashMap } from '@utils/arr';

const mockArray = [
  { id: 1, name: 'test' },
  { id: 2, name: 'test2' }
];

describe('arrayToHashMap function', () => {
  it('should return empty object if array is undefined of null', () => {
    [undefined, null].forEach((arr) => {
      const res = arrayToHashMap(arr, 'id');
      expect(res).toStrictEqual({});
    });
  });

  it('should return empty object if array is empty', () => {
    const res = arrayToHashMap([], 'id');
    expect(res).toStrictEqual({});
  });

  it('should return empty object if key is not matching anything in the array', () => {
    const res = arrayToHashMap(['hi', 'dfk'], 'id');
    expect(res).toStrictEqual({});
  });

  it('should return object with key and array of items', () => {
    const res = arrayToHashMap(mockArray, 'id');
    expect(res).toStrictEqual({ 1: [{ id: 1, name: 'test' }], 2: [{ id: 2, name: 'test2' }] });
  });

  it('should ignore objects does not have the key and return the rest', () => {
    const res = arrayToHashMap([...mockArray, { name: test }], 'id');
    expect(res).toStrictEqual({ 1: [{ id: 1, name: 'test' }], 2: [{ id: 2, name: 'test2' }] });
  });
});
