import { returnNewObjWithoutNullValues } from '@utils/object';

const dummyData: Record<string, number | string | boolean> = {
  id: 1,
  value: 'test',
  name: null,
  data: undefined,
  isActive: true,
  isDeleted: false,
  age: 0
};

const expectedData: Record<string, number | string | boolean> = {
  id: 1,
  value: 'test',
  data: undefined,
  isActive: true,
  isDeleted: false,
  age: 0
};
describe('Object sanitizer', () => {
  it('it should return new Object without Null values', () => {
    const sanitized = returnNewObjWithoutNullValues(dummyData);
    expect(sanitized).toStrictEqual(expectedData);
  });
});
