import { IKeyValuePair } from '@lib/types/types';
import { render, RenderOptions } from '@testing-library/react';
import { RouterContext } from 'next/dist/shared/lib/router-context.shared-runtime';
import { NextRouter } from 'next/router';
import { ReactNode } from 'react';
import { Provider as ChakraProvider } from '@components/ui/provider';
import { Provider } from 'react-redux';
import { initializeReduxStore } from '@redux/store';

const createReduxStore = (store: IKeyValuePair) =>
  initializeReduxStore({
    auth: { jwtToken: '', isUserLoggedIn: true, isLoadingUserData: false, user: {} },
    app: {
      lang: 'en',
      country: 'ca',
      reqIpAddress: '',
      reqIpCountry: '',
      isMobile: undefined,
      toastMsgList: {}
    },
    ...store
  });
const mockRouter: NextRouter = {
  basePath: '',
  pathname: '/',
  route: '/',
  query: {},
  asPath: '/',
  back: jest.fn(),
  beforePopState: jest.fn(),
  prefetch: jest.fn(),
  push: jest.fn(),
  reload: jest.fn(),
  replace: jest.fn(),
  events: {
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn()
  },
  isFallback: false,
  isLocaleDomain: false,
  isReady: true,
  defaultLocale: 'en',
  domainLocales: [],
  isPreview: false,
  forward: jest.fn()
};

export function ApplicationProvider({
  store = {},
  router = {},
  children
}: {
  store?: IKeyValuePair;
  router?: Partial<NextRouter>;
  children: ReactNode;
}) {
  return (
    <ChakraProvider>
      <Provider store={createReduxStore(store)}>
        <RouterContext.Provider value={{ ...mockRouter, ...router }}>{children}</RouterContext.Provider>
      </Provider>
    </ChakraProvider>
  );
}

export const appRenderer = (ui: ReactNode, options?: RenderOptions) =>
  render(ui, { wrapper: ApplicationProvider, ...options });
