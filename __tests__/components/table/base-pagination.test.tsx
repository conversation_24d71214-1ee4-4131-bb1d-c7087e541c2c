import { BasePagination, IPaginationProps } from '@components/base/base-pagination';
import { render } from '@testing-library/react';
import { ApplicationProvider } from '@tests/test-utils';

const onChange = jest.fn();

const props: IPaginationProps = {
  route: '/farm/test-farm',
  total: 95,
  pageSize: 10,
  current: 1,
  query: { q: 'query' },
  onChange
};

const setup = (_props?: Partial<IPaginationProps>) =>
  render(
    <ApplicationProvider>
      <BasePagination {...props} />
    </ApplicationProvider>
  );

describe('BasePagination component', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('Should render the component', () => {
    const { container } = setup();
    expect(container).toBeTruthy();
  });

  it('Should render page buttons 1-5 and 10 with correct links', () => {
    const { getAllByTestId } = setup();
    const pages = getAllByTestId('pagination-page');

    expect(pages.length).toBe(6);
    expect(pages[0].textContent).toBe('1');
    expect(pages[0]).toHaveAttribute('href', '/farm/test-farm?q=query');

    expect(pages[1].textContent).toBe('2');
    expect(pages[1]).toHaveAttribute('href', '/farm/test-farm?q=query&page=2');

    expect(pages[2].textContent).toBe('3');
    expect(pages[2]).toHaveAttribute('href', '/farm/test-farm?q=query&page=3');

    expect(pages[3].textContent).toBe('4');
    expect(pages[3]).toHaveAttribute('href', '/farm/test-farm?q=query&page=4');

    expect(pages[4].textContent).toBe('5');
    expect(pages[4]).toHaveAttribute('href', '/farm/test-farm?q=query&page=5');

    expect(pages[5].textContent).toBe('10');
    expect(pages[5]).toHaveAttribute('href', '/farm/test-farm?q=query&page=10');
  });

  it('Should rernder pages correctly When current page changes', () => {
    const { rerender, getAllByTestId } = setup();

    rerender(
      <ApplicationProvider>
        <BasePagination {...props} current={9} />
      </ApplicationProvider>
    );

    const pages = getAllByTestId('pagination-page');

    expect(pages.length).toBe(6);
    expect(pages[0].textContent).toBe('1');
    expect(pages[0]).toHaveAttribute('href', '/farm/test-farm?q=query');

    expect(pages[1].textContent).toBe('6');
    expect(pages[1]).toHaveAttribute('href', '/farm/test-farm?q=query&page=6');

    expect(pages[2].textContent).toBe('7');
    expect(pages[2]).toHaveAttribute('href', '/farm/test-farm?q=query&page=7');

    expect(pages[3].textContent).toBe('8');
    expect(pages[3]).toHaveAttribute('href', '/farm/test-farm?q=query&page=8');

    expect(pages[4].textContent).toBe('9');
    expect(pages[4]).toHaveAttribute('href', '/farm/test-farm?q=query&page=9');

    expect(pages[5].textContent).toBe('10');
    expect(pages[5]).toHaveAttribute('href', '/farm/test-farm?q=query&page=10');
  });

  it('Should regenrate pages url when query changes ', () => {
    const { rerender, getAllByTestId } = setup();
    const pages = getAllByTestId('pagination-page');

    expect(pages.length).toBe(6);
    expect(pages[0].textContent).toBe('1');
    expect(pages[0]).toHaveAttribute('href', '/farm/test-farm?q=query');

    expect(pages[1].textContent).toBe('2');
    expect(pages[1]).toHaveAttribute('href', '/farm/test-farm?q=query&page=2');

    expect(pages[2].textContent).toBe('3');
    expect(pages[2]).toHaveAttribute('href', '/farm/test-farm?q=query&page=3');

    expect(pages[3].textContent).toBe('4');
    expect(pages[3]).toHaveAttribute('href', '/farm/test-farm?q=query&page=4');

    expect(pages[4].textContent).toBe('5');
    expect(pages[4]).toHaveAttribute('href', '/farm/test-farm?q=query&page=5');

    expect(pages[5].textContent).toBe('10');
    expect(pages[5]).toHaveAttribute('href', '/farm/test-farm?q=query&page=10');

    rerender(
      <ApplicationProvider>
        <BasePagination {...props} query={{ q: 'new-query', sortBy: 'name', sortDir: 'desc' }} />
      </ApplicationProvider>
    );
    const newPages = getAllByTestId('pagination-page');

    expect(newPages.length).toBe(6);
    expect(newPages[0].textContent).toBe('1');
    expect(newPages[0]).toHaveAttribute('href', '/farm/test-farm?q=new-query&sortBy=name&sortDir=desc');

    expect(newPages[1].textContent).toBe('2');
    expect(newPages[1]).toHaveAttribute('href', '/farm/test-farm?q=new-query&sortBy=name&sortDir=desc&page=2');

    expect(newPages[2].textContent).toBe('3');
    expect(newPages[2]).toHaveAttribute('href', '/farm/test-farm?q=new-query&sortBy=name&sortDir=desc&page=3');

    expect(newPages[3].textContent).toBe('4');
    expect(newPages[3]).toHaveAttribute('href', '/farm/test-farm?q=new-query&sortBy=name&sortDir=desc&page=4');

    expect(newPages[4].textContent).toBe('5');
    expect(newPages[4]).toHaveAttribute('href', '/farm/test-farm?q=new-query&sortBy=name&sortDir=desc&page=5');

    expect(newPages[5].textContent).toBe('10');
    expect(newPages[5]).toHaveAttribute('href', '/farm/test-farm?q=new-query&sortBy=name&sortDir=desc&page=10');
  });

  it('Should render the next button with correct link', () => {
    const { rerender, getByTestId } = setup();

    expect(getByTestId('pagination-next')).toHaveAttribute('href', '/farm/test-farm?q=query&page=2');

    rerender(
      <ApplicationProvider>
        <BasePagination {...props} current={2} />
      </ApplicationProvider>
    );
    expect(getByTestId('pagination-next')).toHaveAttribute('href', '/farm/test-farm?q=query&page=3');

    rerender(
      <ApplicationProvider>
        <BasePagination {...props} current={10} />
      </ApplicationProvider>
    );
    expect(getByTestId('pagination-next')).not.toHaveAttribute('href');
  });

  it('Should render the prev button with correct link', () => {
    const { rerender, getByTestId } = setup();

    expect(getByTestId('pagination-prev')).not.toHaveAttribute('href');

    rerender(
      <ApplicationProvider>
        <BasePagination {...props} current={2} />
      </ApplicationProvider>
    );
    expect(getByTestId('pagination-prev')).toHaveAttribute('href', '/farm/test-farm?q=query');

    rerender(
      <ApplicationProvider>
        <BasePagination {...props} current={10} />
      </ApplicationProvider>
    );
    expect(getByTestId('pagination-prev')).toHaveAttribute('href', '/farm/test-farm?q=query&page=9');
  });

  it('Should render the current page with correct accessible attributes', () => {
    const { container, rerender } = setup();

    let currentPage = container.querySelectorAll('[aria-current=true]');
    expect(currentPage).toHaveLength(1);
    expect(currentPage[0]).toHaveAttribute('aria-label', 'Current Page, Page 1');

    rerender(
      <ApplicationProvider>
        <BasePagination {...props} current={2} />
      </ApplicationProvider>
    );

    currentPage = container.querySelectorAll('[aria-current=true]');
    expect(currentPage).toHaveLength(1);
    expect(currentPage[0]).toHaveAttribute('aria-label', 'Current Page, Page 2');
  });
});
