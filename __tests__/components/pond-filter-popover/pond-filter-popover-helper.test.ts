import {
  applyCurrentProfitPerDayRange,
  applyDaysFromHOptimalHarvestFilter,
  applyOptimalProfitPerDayRange
} from '@components/pond-filter-popover/pond-filter-popover-helpers';

import * as FarmDates from '@screens/farm/helpers/farm-dates';
import { PopulationHarvestPlan } from '@xpertsea/module-farm-sdk';
import { LessThanMoreThan } from '@components/pond-filter-popover/use-generate-filter-tags';

jest.mock('@screens/farm/helpers/farm-dates', () => {
  const original = jest.requireActual('@screens/farm/helpers/farm-dates');
  return {
    __esModule: true,
    ...original
  };
});

const getDaysDiffBetweenDatesSpy = jest.spyOn(FarmDates, 'getDaysDiffBetweenDates');

const optimalProfitProjectionData = {
  date: '2024-11-23',
  harvestType: 'headon',
  headless: {
    biomassKg: 7234.548156851491,
    biomassLbs: 15949.429557557933,
    totalProfit: -13437.926165919878,
    costPerPound: 2.6857862955633287,
    totalRevenue: 29398.833161821905,
    profitPerPound: -0.8425333406079136,
    revenuePerPound: 1.8432529549554162,
    liveTotalRevenue: 29398.833161821905,
    profitPerHaPerDay: -26.17947821141609,
    totalRevenuePound: 1.8432529549554162,
    biomassProcessedLbs: 15949.429557557927
  },
  headon: {
    biomassKg: 8476.298959893164,
    biomassLbs: 18687.018212959665,
    totalProfit: -27487.3896304336,
    costPerPound: 1.936899863330747,
    totalRevenue: 15349.369697308184,
    profitPerPound: -2.4009936249573283,
    revenuePerPound: 1.3407507692016805,
    liveTotalRevenue: 15349.369697308184,
    profitPerHaPerDay: -53.550340211248006,
    totalRevenuePound: 1.3407507692016805,
    biomassProcessedLbs: 11448.339281168279
  }
};

describe('applyDaysFromHOptimalHarvestFilter function', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });
  test('It should return true if flag does not exist or not valid number', () => {
    const harvestPlan = {
      harvestDate: '2024-09-01',
      optimalProfitProjectionData
    } as PopulationHarvestPlan;
    [null, undefined, NaN].forEach((aboveOptimalHarvestDate) => {
      const res = applyDaysFromHOptimalHarvestFilter({
        aboveOptimalHarvestDate,
        harvestPlan,
        aboveOptimalHarvestDateFlag: false
      });
      expect(getDaysDiffBetweenDatesSpy).toHaveBeenCalledTimes(0);
      expect(res).toBe(true);
    });
  });

  test('It should return false if there is no harvest plan', () => {
    const result = applyDaysFromHOptimalHarvestFilter({
      aboveOptimalHarvestDate: 2,
      harvestPlan: undefined,
      aboveOptimalHarvestDateFlag: true
    });
    expect(getDaysDiffBetweenDatesSpy).toHaveBeenCalledTimes(0);
    expect(result).toBe(false);
  });

  test('It should return false if there is no harvest date ', () => {
    const harvestPlan = {
      optimalProfitProjectionData
    } as PopulationHarvestPlan;
    const result = applyDaysFromHOptimalHarvestFilter({
      aboveOptimalHarvestDate: 2,
      harvestPlan,
      aboveOptimalHarvestDateFlag: true
    });
    expect(getDaysDiffBetweenDatesSpy).toHaveBeenCalledTimes(0);
    expect(result).toBe(false);
  });

  test('It should return false if optimalHarvestData is missing', () => {
    const harvestPlan = {
      harvestDate: '2024-08-28'
    } as PopulationHarvestPlan;
    const result = applyDaysFromHOptimalHarvestFilter({
      aboveOptimalHarvestDate: 2,
      harvestPlan,
      aboveOptimalHarvestDateFlag: true
    });
    expect(getDaysDiffBetweenDatesSpy).toHaveBeenCalledTimes(0);
    expect(result).toBe(false);
  });

  test('It should return true if days difference input is correct', () => {
    const harvestPlan = {
      harvestDate: '2024-11-26',
      optimalProfitProjectionData
    } as PopulationHarvestPlan;

    const result = applyDaysFromHOptimalHarvestFilter({
      aboveOptimalHarvestDate: 2,
      harvestPlan,
      aboveOptimalHarvestDateFlag: true
    });
    expect(getDaysDiffBetweenDatesSpy).toHaveBeenCalledTimes(1);
    expect(getDaysDiffBetweenDatesSpy).toHaveBeenCalledWith({ baseDate: '2024-11-26', dateToCompare: '2024-11-23' });
    expect(result).toBe(true);
  });

  test('It should return false if days difference input is not correct', () => {
    const harvestPlan = {
      harvestDate: '2024-11-26',
      optimalProfitProjectionData
    } as PopulationHarvestPlan;

    const result = applyDaysFromHOptimalHarvestFilter({
      aboveOptimalHarvestDate: 4,
      harvestPlan,
      aboveOptimalHarvestDateFlag: true
    });
    expect(getDaysDiffBetweenDatesSpy).toHaveBeenCalledTimes(1);
    expect(getDaysDiffBetweenDatesSpy).toHaveBeenCalledWith({ baseDate: '2024-11-26', dateToCompare: '2024-11-23' });
    expect(result).toBe(false);
  });
});

describe('applyCurrentProfitPerDayRange function', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return true if both lessThan and moreThan are missing', () => {
    const harvestPlan = {
      harvestType: 'headon',
      projection: {
        plannedHarvestIdx: 0,
        profitProjectionData: [{ headon: { profitPerHaPerDay: 3 } }]
      }
    } as PopulationHarvestPlan;
    const range = {} as LessThanMoreThan;
    const results = applyCurrentProfitPerDayRange({ range, harvestPlan });

    expect(results).toBe(true);
  });

  it('should return false if harvestPlan is missing', () => {
    const results = applyCurrentProfitPerDayRange({ range: { lessThan: 10, moreThan: 2 }, harvestPlan: undefined });

    expect(results).toBe(false);
  });

  it('should return false if profitPerHaPerDay is missing', () => {
    const harvestPlans = [
      { harvestType: 'headon', projection: { plannedHarvestIdx: 0, profitProjectionData: [{ headon: {} }] } },
      {
        harvestType: 'headon',
        projection: { plannedHarvestIdx: 1, profitProjectionData: [{ headon: { profitPerHaPerDay: 4 } }] }
      },
      {
        harvestType: 'headless',
        projection: { plannedHarvestIdx: 0, profitProjectionData: [{ headon: { profitPerHaPerDay: 4 } }] }
      }
    ];

    harvestPlans.forEach((harvestPlan) => {
      const results = applyCurrentProfitPerDayRange({
        range: { moreThan: 2, lessThan: 10 },
        harvestPlan: harvestPlan as PopulationHarvestPlan
      });

      expect(results).toBe(false);
    });
  });

  it('should check the profitPerHaPerDay in the correct type (headon or headless)', () => {
    const projection = {
      plannedHarvestIdx: 0,
      profitProjectionData: [{ headon: { profitPerHaPerDay: 5 }, headless: { profitPerHaPerDay: 50 } }]
    };

    const headonHarvestPlan = { harvestType: 'headon', projection };
    const headlessHarvestPlan = { harvestType: 'headless', projection };

    expect(
      applyCurrentProfitPerDayRange({
        range: { moreThan: 2, lessThan: 10 },
        harvestPlan: headonHarvestPlan as PopulationHarvestPlan
      })
    ).toBe(true);

    expect(
      applyCurrentProfitPerDayRange({
        range: { moreThan: 2, lessThan: 10 },
        harvestPlan: headlessHarvestPlan as PopulationHarvestPlan
      })
    ).toBe(false);

    expect(
      applyCurrentProfitPerDayRange({
        range: { moreThan: 20, lessThan: 100 },
        harvestPlan: headlessHarvestPlan as PopulationHarvestPlan
      })
    ).toBe(true);

    expect(
      applyCurrentProfitPerDayRange({
        range: { moreThan: 20, lessThan: 100 },
        harvestPlan: headonHarvestPlan as PopulationHarvestPlan
      })
    ).toBe(false);
  });

  it('should should work if range has at least one value', () => {
    const harvestPlan = {
      harvestType: 'headon',
      projection: {
        plannedHarvestIdx: 0,
        profitProjectionData: [{ headon: { profitPerHaPerDay: 12 } }]
      }
    } as PopulationHarvestPlan;

    expect(
      applyCurrentProfitPerDayRange({
        range: { moreThan: 2, lessThan: undefined },
        harvestPlan
      })
    ).toBe(true);

    expect(
      applyCurrentProfitPerDayRange({
        range: { moreThan: undefined, lessThan: 13 },
        harvestPlan
      })
    ).toBe(true);

    expect(
      applyCurrentProfitPerDayRange({
        range: { moreThan: 2, lessThan: 10 },
        harvestPlan
      })
    ).toBe(false);
  });
});

describe('applyOptimalProfitPerDayRange function', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('Should return true if flag does not exist or not valid number', () => {
    const harvestPlan = {
      harvestType: 'headon',
      projection: {
        optimalHarvestIdx: 0,
        profitProjectionData: [{ headon: { profitPerHaPerDay: 40 } }, { headon: { profitPerHaPerDay: 20 } }]
      }
    } as PopulationHarvestPlan;

    expect(applyOptimalProfitPerDayRange({ range: { lessThan: undefined, moreThan: undefined }, harvestPlan })).toBe(
      true
    );

    expect(applyOptimalProfitPerDayRange({ range: { lessThan: 1, moreThan: undefined }, harvestPlan })).toBe(false);
    expect(applyOptimalProfitPerDayRange({ range: { lessThan: undefined, moreThan: 50 }, harvestPlan })).toBe(false);
  });

  it('should return false if harvestPlan is missing', () => {
    const results = applyOptimalProfitPerDayRange({ range: { lessThan: 10, moreThan: 2 }, harvestPlan: undefined });

    expect(results).toBe(false);
  });

  it('should return false if profitPerHaPerDay is missing', () => {
    const harvestPlans = [
      {
        harvestType: 'headon',
        projection: {
          optimalHarvestIdx: 0,
          profitProjectionData: [{ headon: { profitPerHaPerDay: 40 } }, { headon: { profitPerHaPerDay: 20 } }]
        }
      }
    ];

    harvestPlans.forEach((harvestPlan) => {
      const results = applyOptimalProfitPerDayRange({
        range: { moreThan: 2, lessThan: 10 },
        harvestPlan: harvestPlan as PopulationHarvestPlan
      });

      expect(results).toBe(false);
    });
  });

  it('Should return valid value', () => {
    const harvestPlan = {
      harvestType: 'headon',
      projection: {
        optimalHarvestIdx: 0,
        profitProjectionData: [{ headon: { profitPerHaPerDay: 40 } }, { headon: { profitPerHaPerDay: 20 } }]
      }
    } as PopulationHarvestPlan;

    expect(applyOptimalProfitPerDayRange({ range: { lessThan: 50, moreThan: undefined }, harvestPlan })).toBe(true);
    expect(applyOptimalProfitPerDayRange({ range: { lessThan: 50, moreThan: 10 }, harvestPlan })).toBe(true);
  });
});
