const { TextEncoder } = require('util');
//This to allow publicRuntimeConfig work with tests.
const publicRuntimeConfig = {
  // Will be available on both server and sdk
  webBaseUrl: process.env.WEB_BASE_URL,
  nodeEnv: process.env.NODE_ENV,
  apiEnv: process.env.API_ENV,
  buildNr: process.env.BUILD_NR,
  buildDate: process.env.BUILD_DATE,
  buildBranch: process.env.BUILD_BRANCH,
  buildCommit: process.env.BUILD_COMMIT,
  commitDate: process.env.COMMIT_DATE,
  sentryDsn: process.env.SENTRY_DSN,
  sentryVersion: process.env.SENTRY_VERSION,
  cdnUrl: process.env.ASSET_PREFIX || '',
  imageCdnUrl: process.env.ASSET_PREFIX_IMG || '',
  gaId: process.env.GA_ID,
  gtmId: process.env.GTM_ID
};

// Make sure you can use "publicRuntimeConfig" within tests.
jest.mock('next/config', () => () => ({
  publicRuntimeConfig
}));

global.TextEncoder = TextEncoder;
