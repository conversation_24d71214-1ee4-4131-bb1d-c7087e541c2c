{"extends": ["config:base"], "assigneesFromCodeOwners": true, "packageRules": [{"matchPackagePatterns": ["*"], "matchUpdateTypes": ["minor", "patch"], "groupName": "all non-major dependencies", "groupSlug": "all-minor-patch", "automerge": true, "labels": ["dependencies"]}, {"matchPackagePatterns": ["*"], "matchUpdateTypes": ["major"], "labels": ["dependencies", "breaking"]}], "ignoreDeps": ["string-width", "strip-ansi"]}