---
name: Task Definition
about: Describe the task in a simple and direct way of what must be done.
title: 'chore: '
labels: 'type: chore'
assignees: ''
---

## Description

Add a description to explaining what is the task and how it works.

## Relevant links

Add here any material need to complete the task

- Figma Links
- Documentation links ( if needed )

## Acceptance Criteria

Describe in bullet items the conditions a software product must meet to be accepted.

- **AC 1**: ..
- **AC 2**: ...
- **AC 3**: ....

## Related Issues

Add all related issues to this task.

- Issue #Number
- Issue #Number
