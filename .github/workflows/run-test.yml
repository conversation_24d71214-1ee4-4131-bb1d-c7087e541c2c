name: run-test
on:
  workflow_dispatch:
  workflow_call:
  issue_comment:
    types: [created, edited]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  GH_PAT: ${{ secrets.GH_PAT }}
  DOCKER_PAT: ${{ secrets.DOCKER_PAT }}
  GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  JENKINS_JOB_URL: ${{ secrets.JENKINS_JOB_URL }}
  TS_NODE_TRANSPILE_ONLY: 1
  CI: true
  NEXT_TELEMETRY_DISABLED: 1
  CUCUMBER_PUBLISH_ENABLED: true
  E2E: true
jobs:
  test:
    if: contains(github.event.comment.body, '@xpertsea-bot test')
    runs-on: ubuntu-latest-4-cores
    timeout-minutes: 20

    steps:
      - run: echo "🎉 The job was automatically triggered by a ${{ github.event_name }} event."

      - name: Add reactions to the comment
        uses: peter-evans/create-or-update-comment@v4
        with:
          comment-id: ${{ github.event.comment.id }}
          reactions: eyes

      - name: Get comment branch
        run: echo ::set-output name=branch::$(gh pr view $PR_NO --repo $REPO --json headRefName --jq '.headRefName')
        id: get-branch
        env:
          REPO: ${{ github.repository }}
          PR_NO: ${{ github.event.issue.number }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5
        with:
          fetch-depth: 10
          persist-credentials: false
          ref: ${{ steps.get-branch.outputs.branch }}

      - name: General
        run: |
          # comment
          GIT_BRANCH=${{ github.ref }}
          echo "REPOSITORY_NAME=$(echo "$GITHUB_REPOSITORY" | awk -F / '{print $2}')" >> $GITHUB_ENV
          echo "GIT_BRANCH=$(echo "${GIT_BRANCH/refs\/heads\//}")" >> $GITHUB_ENV
          echo "COMMIT_DATE=$(git log -n 1 --pretty='format:%cd' --date=format:'%y-%m-%d')" >> $GITHUB_ENV
          echo "COMMIT_TIME=$(git log -n 1 --pretty='format:%cd' --date=format:'%H-%M-%S')" >> $GITHUB_ENV
          echo "CURRENT_DATETIME=$(date +'%Y-%m-%d %H:%M:%S')" >> $GITHUB_ENV
          echo "GH_SHA_SHORT=$(echo $GITHUB_SHA | cut -c 1-7)" >> $GITHUB_ENV
          git config --global user.name "xpertsea-bot"
          git config --global url."https://${{ env.GH_PAT }}:<EMAIL>/".insteadOf "https://github.com/"
          git config --global url."https://${{ env.GH_PAT }}:<EMAIL>/".insteadOf "ssh://**************/"

      - name: Checking out comment branch
        run: |
          echo ${{ steps.get-branch.outputs.branch }} - $COMMENT_SHA - $COMMENT_COMMIT_MESSAGE

      - name: Create initial bot comment
        uses: peter-evans/create-or-update-comment@v4
        id: bot-comment
        with:
          issue-number: ${{ github.event.issue.number }}
          body: |
            #### 🤖 Testing ${{ env.COMMENT_SHA }} - ${{ env.COMMENT_COMMIT_MESSAGE }}
            #### 🔗 [View workflow run](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})

            ---

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'yarn'
          cache-dependency-path: |
            yarn.lock
            .e2e/yarn.lock

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Build
        run: yarn build

      - name: Install dependencies for e2e (from e2e directory)
        run: yarn install --cwd ./e2e/ --frozen-lockfile

      - run: sudo apt-get install xvfb

      - name: E2E tests
        run: xvfb-run --auto-servernum yarn e2e:prod-build

      - name: Success comment
        uses: peter-evans/create-or-update-comment@v4
        if: success()
        with:
          issue-number: ${{ github.event.issue.number }}
          comment-id: ${{ steps.bot-comment.outputs.comment-id }}
          body: |
            ✅ @${{ github.event.comment.user.login }} all tests passed 🚀
      - name: Add success reactions
        uses: peter-evans/create-or-update-comment@v4
        if: success()
        with:
          issue-number: ${{ github.event.issue.number }}
          comment-id: ${{ github.event.comment.id }}
          reactions: +1, rocket, hooray

      - name: Fail Comment
        uses: peter-evans/create-or-update-comment@v4
        if: failure()
        with:
          issue-number: ${{ github.event.issue.number }}
          comment-id: ${{ steps.bot-comment.outputs.comment-id }}
          body: |
            ❌ @${{ github.event.comment.user.login }} some tests failed 💥 - [check details here](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})
      - name: Add fail reactions
        uses: peter-evans/create-or-update-comment@v4
        if: failure()
        with:
          issue-number: ${{ github.event.issue.number }}
          comment-id: ${{ github.event.comment.id }}
          reactions: -1, confused
