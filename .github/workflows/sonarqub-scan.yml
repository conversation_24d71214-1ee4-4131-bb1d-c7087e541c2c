name: Sonarq<PERSON> Scan
on:
  workflow_dispatch:
  workflow_call:
  push:
    paths:
      - "*"
    branches:
      - "!*"
      - "develop"
      - "master"

env:
  GH_PAT: ${{ secrets.GH_PAT }}
  DOCKER_PAT: ${{ secrets.DOCKER_PAT }}
  GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  JENKINS_JOB_URL: ${{ secrets.JENKINS_JOB_URL }}
  CI: true

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build:
    runs-on: ubuntu-latest-4-cores
    timeout-minutes: 10

    steps:
      - run: echo "🎉 The job was automatically triggered by a ${{ github.event_name }} event."
      - uses: styfle/cancel-workflow-action@0.12.1
        with:
          workflow_id: build-docker-image-app-drift-detection.yml
          access_token: ${{ github.token }}
      - uses: actions/checkout@v5
        with:
          fetch-depth: 10
          persist-credentials: false
      - uses: sonarsource/sonarqube-scan-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
