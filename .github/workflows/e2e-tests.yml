name: e2e-tests
on:
  workflow_dispatch:
  workflow_call:
  push:
    paths:
      - 'styles/**'
      - 'src/**'
      - 'public/**'
      - 'pages/**'
      - 'yarn.lock'
      - 'Dockerfile'
      - 'e2e/**'
      - '.github/workflows/e2e-tests.yml'
    branches:
      - '!*'
      - 'develop'
      - 'stage'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  GH_PAT: ${{ secrets.GH_PAT }}
  DOCKER_PAT: ${{ secrets.DOCKER_PAT }}
  GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  JENKINS_JOB_URL: ${{ secrets.JENKINS_JOB_URL }}
  CI: true
  TS_NODE_TRANSPILE_ONLY: 1
  NEXT_TELEMETRY_DISABLED: 1
  CUCUMBER_PUBLISH_ENABLED: true
  E2E: true
jobs:
  test:
    runs-on: ubuntu-latest-4-cores
    timeout-minutes: 20

    steps:
      - run: echo "🎉 The job was automatically triggered by a ${{ github.event_name }} event."

      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5
        with:
          fetch-depth: 10
          persist-credentials: false

      - name: General
        run: |
          # comment
          GIT_BRANCH=${{ github.ref }}
          echo "REPOSITORY_NAME=$(echo "$GITHUB_REPOSITORY" | awk -F / '{print $2}')" >> $GITHUB_ENV
          echo "GIT_BRANCH=$(echo "${GIT_BRANCH/refs\/heads\//}")" >> $GITHUB_ENV
          echo "COMMIT_DATE=$(git log -n 1 --pretty='format:%cd' --date=format:'%y-%m-%d')" >> $GITHUB_ENV
          echo "COMMIT_TIME=$(git log -n 1 --pretty='format:%cd' --date=format:'%H-%M-%S')" >> $GITHUB_ENV
          echo "CURRENT_DATETIME=$(date +'%Y-%m-%d %H:%M:%S')" >> $GITHUB_ENV
          echo "GH_SHA_SHORT=$(echo $GITHUB_SHA | cut -c 1-7)" >> $GITHUB_ENV
          git config --global user.name "xpertsea-bot"
          git config --global url."https://${{ env.GH_PAT }}:<EMAIL>/".insteadOf "https://github.com/"
          git config --global url."https://${{ env.GH_PAT }}:<EMAIL>/".insteadOf "ssh://**************/"

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'yarn'
          cache-dependency-path: |
            yarn.lock
            .e2e/yarn.lock

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Build
        run: yarn build

      - name: Install dependencies for e2e (from e2e directory)
        run: yarn install --cwd ./e2e/ --frozen-lockfile

      - run: sudo apt-get install xvfb

      - name: E2E tests
        run: xvfb-run --auto-servernum yarn e2e:prod-build

      - name: Job Summary
        run: |
          echo "Docker Image Tag: $GIT_BRANCH-$COMMIT_DATE-$COMMIT_TIME-$GH_SHA_SHORT" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "- Branch: $GIT_BRANCH" >> $GITHUB_STEP_SUMMARY
          echo "- Commit Date: $COMMIT_DATE" >> $GITHUB_STEP_SUMMARY
          echo "- Commit Time: $COMMIT_TIME" >> $GITHUB_STEP_SUMMARY
          echo "- Commit SHA: $GH_SHA_SHORT" >> $GITHUB_STEP_SUMMARY

      - uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          author_name: ${{ github.repository }}
          fields: repo,message,commit,author,action,eventName,ref,workflow
          mention: 'here'
          if_mention: 'failure'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        if: always()
