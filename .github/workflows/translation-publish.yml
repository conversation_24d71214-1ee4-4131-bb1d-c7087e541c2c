name: Publish Translation
on:
  workflow_dispatch:
  workflow_call:
  push:
    paths:
      - '.git'
      - 'src/i18n/**'
    branches:
      - 'develop'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  GH_PAT: ${{ secrets.GH_PAT }}
  DOCKER_PAT: ${{ secrets.DOCKER_PAT }}
  GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  JENKINS_JOB_URL: ${{ secrets.JENKINS_JOB_URL }}
  TRANSIFEX_TOKEN: ${{ secrets.TRANSIFEX_TOKEN }}
  CI: true
  TS_NODE_TRANSPILE_ONLY: 1
jobs:
  transifex-publish:
    name: Transifex publish
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - run: echo "🎉 The job was automatically triggered by a ${{ github.event_name }} event."

      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5
        with:
          persist-credentials: false
          fetch-depth: 10

      - name: General
        run: |
          # comment
          git config --global user.name "xpertsea-bot"
          git config --global url."https://${{ env.GH_PAT }}:<EMAIL>/".insteadOf "https://github.com/"
          git config --global url."https://${{ env.GH_PAT }}:<EMAIL>/".insteadOf "ssh://**************/"

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'yarn'
          cache-dependency-path: |
            yarn.lock
            .e2e/yarn.lock

      - name: Install dependencies
        run: |
          yarn install --frozen-lockfile
          yarn transifex:install

      - name: Upload Main Translation File
        run: |
          yarn translation:publish

      - uses: 8398a7/action-slack@v3
        if: always() && steps.paths-filter.outputs.code == 'true'
        with:
          status: ${{ job.status }}
          author_name: ${{ github.repository }}
          fields: repo,message,commit,author,action,eventName,ref,workflow
          mention: 'here'
          if_mention: 'failure'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
