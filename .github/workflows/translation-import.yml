name: Import Translation

on:
  workflow_dispatch:
  workflow_call:
  push:
    paths:
      - '.github/workflows/translation-import.yml'
  schedule:
    # Runs "At 04:00 on every day-of-week from Monday through Thursday" (see https://crontab.guru)
    - cron: '0 4 * * 1-4'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  GH_PAT: ${{ secrets.GH_PAT }}
  DOCKER_PAT: ${{ secrets.DOCKER_PAT }}
  GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  JENKINS_JOB_URL: ${{ secrets.JENKINS_JOB_URL }}
  TRANSIFEX_TOKEN: ${{ secrets.TRANSIFEX_TOKEN }}
  CI: true
  TS_NODE_TRANSPILE_ONLY: 1

jobs:
  build:
    name: Import Translation
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - run: echo "🎉 The job was automatically triggered by a ${{ github.event_name }} event."

      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5
        with:
          persist-credentials: true
          fetch-depth: 10
          ref: 'develop'

      - name: General
        run: |
          # comment
          git config --global user.name "xpertsea-bot"
          git config --global user.email "<EMAIL>"

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'yarn'
          cache-dependency-path: |
            yarn.lock
            .e2e/yarn.lock

      - name: Install dependencies
        run: |
          yarn transifex:install

      - name: Importing
        run: |
          yarn translation:import
          git status

      - name: Open Pull request
        id: open-pr
        uses: peter-evans/create-pull-request@v7
        with:
          title: 'chore: Update translations from transifex (auto-generated)'
          body: 'Translations are updated from transifex'
          branch: 'chore/transifex-import'
          base: 'develop'
          commit-message: 'translations are updated from transifex-${{ github.run_number }}'
          labels: 'type: chore, auto-generated'
          assignees: xpertsea-bot
          reviewers: meabed, mendesbarreto
          add-paths: |
            src/i18n/*

      - name: Summary
        run: |
          echo "Pull Request Number: ${{ steps.open-pr.outputs.pull-request-number }}" >> $GITHUB_STEP_SUMMARY
          echo "Pull Request URL: ${{ steps.open-pr.outputs.pull-request-url }}" >> $GITHUB_STEP_SUMMARY
