name: build-docker-image
on:
  workflow_dispatch:
  workflow_call:
  push:
    paths:
      - 'styles/**'
      - 'src/**'
      - 'public/**'
      - 'pages/**'
      - 'yarn.lock'
      - 'Dockerfile'
    branches:
      - '!*'
      - 'develop'
      - 'stage'
      - 'master'
      - 'chore/update-sentry-lib'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  GH_PAT: ${{ secrets.GH_PAT }}
  DOCKER_PAT: ${{ secrets.DOCKER_PAT }}
  GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  JENKINS_JOB_URL: ${{ secrets.JENKINS_JOB_URL }}
  SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
  CI: true
  TS_NODE_TRANSPILE_ONLY: 1
  NEXT_TELEMETRY_DISABLED: 1

jobs:
  build:
    runs-on: ubuntu-latest-4-cores
    timeout-minutes: 20

    steps:
      - run: echo "🎉 The job was automatically triggered by a ${{ github.event_name }} event."

      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5
        with:
          fetch-depth: 10
          persist-credentials: false

      - name: General
        run: |
          # comment
          GIT_BRANCH=${{ github.ref }}
          echo "REPOSITORY_NAME=$(echo "$GITHUB_REPOSITORY" | awk -F / '{print $2}')" >> $GITHUB_ENV
          echo "GIT_BRANCH=$(echo "${GIT_BRANCH/refs\/heads\//}")" >> $GITHUB_ENV
          echo "COMMIT_DATE=$(git log -n 1 --pretty='format:%cd' --date=format:'%y-%m-%d')" >> $GITHUB_ENV
          echo "COMMIT_TIME=$(git log -n 1 --pretty='format:%cd' --date=format:'%H-%M-%S')" >> $GITHUB_ENV
          echo "CURRENT_DATETIME=$(date +'%Y-%m-%d %H:%M:%S')" >> $GITHUB_ENV
          echo "GH_SHA_SHORT=$(echo $GITHUB_SHA | cut -c 1-7)" >> $GITHUB_ENV
          git config --global user.name "xpertsea-bot"
          git config --global url."https://${{ env.GH_PAT }}:<EMAIL>/".insteadOf "https://github.com/"
          git config --global url."https://${{ env.GH_PAT }}:<EMAIL>/".insteadOf "ssh://**************/"

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'yarn'
          cache-dependency-path: |
            yarn.lock
            .e2e/yarn.lock

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Test
        run: |
          yarn test
          yarn lint

      - name: Build
        run: |
          NODE_ENV=production NODE_OPTIONS="--max-old-space-size=8192" yarn build
          yarn sentry:sourcemaps

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Cache Docker layers
        uses: actions/cache@v4
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: xpertseabot
          password: ${{ secrets.DOCKER_PAT }}

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            xpertseacom/${{ env.REPOSITORY_NAME }}
          tags: |
            type=raw,value=latest,enable=${{ github.ref == format('refs/heads/{0}', 'master') }}
            type=raw,value=${{ env.GIT_BRANCH }}-${{ env.COMMIT_DATE }}-${{ env.COMMIT_TIME }}-${{ env.GH_SHA_SHORT }}

      - name: Build and push
        uses: docker/build-push-action@v6
        timeout-minutes: 15
        with:
          context: .
          network: host
          build-args: |
            GH_PAT=${{ secrets.GH_PAT }}
            BUILD_NR=${{ env.GIT_BRANCH }}-${{ env.GH_SHA_SHORT }}
            BUILD_BRANCH=${{ env.GIT_BRANCH }}
            BUILD_COMMIT=${{ env.GH_SHA_SHORT }}
            COMMIT_DATE=${{ env.COMMIT_DATE }}
            BUILD_DATE=${{ env.CURRENT_DATETIME }}
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache-new,mode=max

      - name: Run Jenkins job
        run: |
          curl --write-out '%{http_code}' --silent --output /dev/null \
            -X POST \
            -d "op=build-image" \
            -d "image=xpertseacom/$REPOSITORY_NAME" \
            -d "version=$GIT_BRANCH-$COMMIT_DATE-$COMMIT_TIME-$GH_SHA_SHORT" \
            -d "branch=$GIT_BRANCH" \
            -d "docker_image_version_param=$GIT_BRANCH-$COMMIT_DATE-$COMMIT_TIME-$GH_SHA_SHORT" \
            "$JENKINS_JOB_URL$GIT_BRANCH"

        # Temp fix
        # https://github.com/docker/build-push-action/issues/252
        # https://github.com/moby/buildkit/issues/1896
      - name: Move cache
        run: |
          rm -rf /tmp/.buildx-cache
          mv /tmp/.buildx-cache-new /tmp/.buildx-cache

      - name: Job Summary
        run: |
          echo "Docker Image Tag: $GIT_BRANCH-$COMMIT_DATE-$COMMIT_TIME-$GH_SHA_SHORT" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "- Branch: $GIT_BRANCH" >> $GITHUB_STEP_SUMMARY
          echo "- Commit Date: $COMMIT_DATE" >> $GITHUB_STEP_SUMMARY
          echo "- Commit Time: $COMMIT_TIME" >> $GITHUB_STEP_SUMMARY
          echo "- Commit SHA: $GH_SHA_SHORT" >> $GITHUB_STEP_SUMMARY

      - uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          author_name: ${{ github.repository }}
          fields: repo,message,commit,author,action,eventName,ref,workflow
          mention: 'here'
          if_mention: 'failure'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        if: always()
