import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { HtmlHead } from '@components/html/html-head';
import { LoggedInOnlyGuard } from '@components/general/logged-in-only-guard';
import { BaseScreenWrapper } from '@components/base/base-screen-wrapper';
import { NurseryDataUpdaterScreen } from '@screens/nursery-data-updater/nursery-data-updater-view-screen';
import { VisionOnlyGuard } from '@components/permission-wrappers/vision-only-guard';

function NurseryDataUpdaterPage() {
  const { trans } = getTrans();

  const currentFarmName = useAppSelector((state) => state.farm?.currentFarm?.name);
  const farmName = currentFarmName ?? trans('t_farm');
  return (
    <>
      <HtmlHead
        title={`${trans('t_nursery_data_updater')} | ${farmName}`}
        description={`${trans('t_nursery_data_updater')} | ${farmName}`}
      />
      <LoggedInOnlyGuard>
        <BaseScreenWrapper>
          <VisionOnlyGuard showWarning>
            <NurseryDataUpdaterScreen />
          </VisionOnlyGuard>
        </BaseScreenWrapper>
      </LoggedInOnlyGuard>
    </>
  );
}

export default NurseryDataUpdaterPage;
