import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { HtmlHead } from '@components/html/html-head';
import { LoggedInOnlyGuard } from '@components/general/logged-in-only-guard';
import { BaseScreenWrapper } from '@components/base/base-screen-wrapper';
import { DataUpdaterScreen } from '@screens/data-updater/data-updater-screen';

function DataUpdater() {
  const { trans } = getTrans();

  const currentFarmName = useAppSelector((state) => state.farm?.currentFarm?.name);
  const farmName = currentFarmName ?? trans('t_farm');
  return (
    <>
      <HtmlHead
        title={`${trans('t_data_updater')} | ${farmName}`}
        description={`${trans('t_data_updater')} | ${farmName}`}
      />
      <LoggedInOnlyGuard>
        <BaseScreenWrapper>
          <DataUpdaterScreen />
        </BaseScreenWrapper>
      </LoggedInOnlyGuard>
    </>
  );
}
export default DataUpdater;
