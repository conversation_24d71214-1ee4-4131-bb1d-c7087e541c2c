import { BaseScreenWrapper } from '@components/base/base-screen-wrapper';
import { LoggedInOnlyGuard } from '@components/general/logged-in-only-guard';
import { HtmlHead } from '@components/html/html-head';
import { getTrans } from '@i18n/get-trans';
import { VisionOnlyGuard } from '@components/permission-wrappers/vision-only-guard';
import { GroupPerformanceScreen } from '@screens/group-summary/group-performance-screen';

function DashboardPage() {
  const { trans } = getTrans();

  return (
    <>
      <HtmlHead title={trans('t_group_performance')} description={trans('t_group_performance')} />
      <LoggedInOnlyGuard>
        <BaseScreenWrapper skipApi>
          <VisionOnlyGuard showWarning>
            <GroupPerformanceScreen />
          </VisionOnlyGuard>
        </BaseScreenWrapper>
      </LoggedInOnlyGuard>
    </>
  );
}

export default DashboardPage;
