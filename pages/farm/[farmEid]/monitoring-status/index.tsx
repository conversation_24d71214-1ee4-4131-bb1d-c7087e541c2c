import { BaseScreenWrapper } from '@components/base/base-screen-wrapper';
import { LoggedInOnlyGuard } from '@components/general/logged-in-only-guard';
import { HtmlHead } from '@components/html/html-head';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { FarmMonitoringRecordsScreen } from '@screens/monitoring-status/farm-monitoring-records-screen';

function FarmMonitoringPage() {
  const { trans } = getTrans();

  const name = useAppSelector((state) => state.farm?.currentFarm?.name);

  const farmName = name ?? trans('t_farm');
  const desc = trans('t_monitorings');

  return (
    <>
      <HtmlHead title={farmName} description={`${farmName}-${desc}`} />
      <LoggedInOnlyGuard>
        <BaseScreenWrapper isSummaryData>
          <FarmMonitoringRecordsScreen />
        </BaseScreenWrapper>
      </LoggedInOnlyGuard>
    </>
  );
}
export default FarmMonitoringPage;
