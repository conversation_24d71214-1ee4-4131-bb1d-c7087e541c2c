## WEB INSIGHTS APP

### What is Next.JS?

Next.JS is a framework for building fast, modern web applications. It is a
React-based framework that uses TypeScript for its backend and JavaScript for
its frontend.

- [Next.JS](https://nextjs.org/)
- [Tailwind CSS](https://tailwindcss.com/)

The codebase folder structure is as follows (WIP):

```
├── src
├── tsconfig.json
```

### Installation

```bash
# if you are using nvm
nvm install node
# if you are using brew
brew upgrade node
# Use Yarn or NPM or pnpm
brew install yarn
brew install npm
brew install pnpm

# Install dependencies
yarn

# start server
yarn local # to work on local apis
yarn dev # to work on dev environment
```

## Next.JS

- Run `yarn local`

# Make it work, Make it right, Make it fast

## Translations (i18n)

The project will always update the translation files <lang>.json automatically, That means you don't need to put the keys manually on the json file.
To add a new key just put `trans('<key_you_want_>')` and your key will automatically be generated on the json file, if you remove the key from the code, it will disappear from the generated json as well.

#### Keys standard:

// TODO Put here the key standards

Example:

```
// Auth Login
// TODO: The examples
```

#### Add transifex token to your env

To install transifex run:

````bash
yarn transifex:install
``

To run transifex integration locally each developer should create a private token on transifex web-site and add it to env.

```shell
TRANSIFEX_TOKEN=<your_token>
````

Follow the steps [HERE](https://docs.transifex.com/account/authentication) to create a personal token

### Coding Style and Guidelines (WIP)

- Avoid early abstraction.
- Avoid premature optimization.
- The project is divided in small modules ( which represent an entity with its oppressions ), eg: `user`, `farm` - each module contains all the relevant code, such as: helpers, utils.
- The codebase is co-located in the same folder as the entity to minimize the trip time for the developer to find the related code
- Follow Code co-location - helpers, and utilities belong to the same module should be in the same folder unless they are related to the core logic.
- To avoid large helpers / utils files - split them into smaller files grouped by entity or purpose. eg: `helper-jwt.ts for all jwt related`, `helper-stripe.ts for all stripe related helpers`.
- Favor functions over classes. ( less OOP is better )
- Favor functions over const and let. eg: `expore function doSomething() {} -- is better than -- export const doSomething = () => {}`
- Favor simplicity over complexity. ( functional programming is great but sometimes it's hard to understand )
- Use named exports over default exports.
- Use named parameters over sequential parameters.
- API Versioning in the form of `v1` and `v2` in file name and field name and input / output in the GraphQL schema.
- API Naming `version-verb-name` eg: `v1-reset-password`, `v1-create-user`
- API Input and Output naming follows the same convention as API Naming. eg: `versionApiNameInput`, `versionApiNameOutput` , `v1CreateUserInput`, `v1CreateUserOutput`
- File naming follows the same convention as API Naming. eg: `v1-reset-password.ts`, `v1-create-user.ts`
- Split APIs method from side effects by Using EventEmitter to emit events for side effects, for example, to send email, or to log to a database. eg: `EventEmitter.emit('userCreated', user)` and on listening `EventEmitter.on('userCreated', user => { // do something })`
- Write tests for functions that are complex logic and not just simple functions.
- Write API Flow ( end to end ) tests for group of APIs to make sure that the user flow is correct. foe example, user register, login, reset password, login, change password, update profile.
- Write code TODOs to remind your self and other developers which things need to be done even if it is not urgent.
