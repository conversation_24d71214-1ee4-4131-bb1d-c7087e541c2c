const driverFactory = require('./drivers/factory');
const { spawn, getTranslationKeys, getLanguageList } = require('./common');

const translateApp = async () => {
  await spawn('yarn', ['run', 'translations:extract']);
};

const exportTranslationKeys = async () => {
  const keys = await getTranslationKeys();
  const languageList = await getLanguageList();
  await driverFactory().exportKeys(keys, languageList);
};

(async () => {
  await translateApp();
  await exportTranslationKeys();
})();
