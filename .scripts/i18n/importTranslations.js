const { promises: fs } = require('fs');
const driverFactory = require('./drivers/factory');
const {
  paths: { translationFilePattern }
} = require('./config');
const { getLanguageList } = require('./common');

/**
 * Save all given translations in the project.
 *
 * @param {Object<string, Object<string, string>>|undefined} translationList - Key-value translations.
 * @returns {Promise<void>} The async process.
 */
const saveTranslations = async (translationList) => {
  if (!translationList) {
    return;
  }

  await Promise.all(
    Object.entries(translationList).map(async ([language, translations]) => {
      await fs.writeFile(translationFilePattern.replaceAll('{{lng}}', language), JSON.stringify(translations), 'utf8');
    })
  );
};

const importTranslations = async () => {
  const languageList = await getLanguageList();
  const translationList = await driverFactory().importTranslations(languageList);
  await saveTranslations(translationList);
};

(async () => {
  await importTranslations();
})();
