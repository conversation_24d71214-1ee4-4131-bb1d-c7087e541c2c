const childProcess = require('child_process');
const { promises: fs } = require('fs');
const path = require('path');
const {
  paths: { translationsLocation },
  defaultLanguage
} = require('./config');

const spawn = async (command, args = []) => {
  const child = childProcess.spawn(command, args, { stdio: 'inherit' });
  const exitCode = await new Promise((resolve) => {
    child.on('exit', resolve);
  });
  if (exitCode) {
    throw new Error(`An error occurred while running "${[command, ...args].join(' ')}" [code ${exitCode}]`);
  }
};

// eslint-disable-next-line import/no-dynamic-require, global-require
const getTranslationKeys = async () => Object.keys(require(path.join(translationsLocation, `${defaultLanguage}.json`)));

const getLanguageList = async () => {
  const files = await fs.readdir(translationsLocation);

  return files.filter((file) => file.endsWith('.json')).map((file) => file.replace(/\.json$/, ''));
};

module.exports = {
  spawn,
  getTranslationKeys,
  getLanguageList
};
