#!/bin/bash

#
# Script inspired by
#
# https://raw.githubusercontent.com/transifex/cli/master/install.sh
#

set -e

CURRENT_DIRECTORY="$(cd "$(dirname "${BASH_SOURCE[0]}")" >/dev/null 2>&1 && pwd)"
TEMPORARY_FOLDER_NAME=_tx_temp
TRANSIFEX_BINARY_NAME=tx
TRANSIFEX_BINARY="${CURRENT_DIRECTORY}/bin/${TRANSIFEX_BINARY_NAME}"

# Get the latest version as string from Github API
LATEST_URL="https://api.github.com/repos/transifex/cli/releases/latest"
LATEST_VERSION="$(curl -s "${LATEST_URL}" | grep "tag_name" |
    cut -d : -f 2 | tr -d \"\,\ )"

if test -f "$TRANSIFEX_BINARY"; then
    echo "Transifex CLI is already installed."
    exit 0
fi

# Try to figure out the version needed to download for the specific system
if [ "$(uname)" == "Darwin" ]; then
    OS=darwin
elif [ "$(expr substr $(uname -s) 1 5)" == "Linux" ]; then
    OS=linux
else
    echoerr "This installer is only supported on Linux and MacOS"
    exit 1
fi

ARCH="$(uname -m)"
if [ "${ARCH}" == "x86_64" ]; then
    ARCH=amd64
elif [[ "${ARCH}" == "aarch"* ]] || [[ "${ARCH}" == "arm"* ]]; then
    ARCH=arm64
elif [[ "${ARCH}" == "i386" ]]; then
    ARCH=386
else
    echoerr "unsupported arch: ${ARCH}"
    exit 1
fi

# Try to download the version from github releases
URL="https://github.com/transifex/cli/releases/download/${LATEST_VERSION}/tx-${OS}-${ARCH}.tar.gz"

mkdir "${TEMPORARY_FOLDER_NAME}"
cd "./${TEMPORARY_FOLDER_NAME}"

echo -e "** Installing Transifex CLI from ${URL}\n"
curl -L "${URL}" | tar xz

echo -e "\nMoving Transifex CLI binary to ${TRANSIFEX_BINARY}"
mv "./${TRANSIFEX_BINARY_NAME}" "${TRANSIFEX_BINARY}"
cd ..
rm -rf "./${TEMPORARY_FOLDER_NAME}"

echo -e "\n** If everything went fine you should see the Transifex CLI version in the following line."
$TRANSIFEX_BINARY -v
