const path = require('path');

const rootFolder = path.join(__dirname, '..', '..');
const binaryFolder = path.join(__dirname, 'bin');
const i18nModuleLocation = path.join(rootFolder, 'src', 'i18n');
const translationsLocation = i18nModuleLocation;
const i18nextConfigLocation = i18nModuleLocation;
const translationFilePattern = path.join(translationsLocation, '{{lng}}.json');

module.exports = {
  defaultLanguage: 'en',

  /*
   * Must be one of the available drivers in the "drivers" config.
   * The "drivers[drivers] config must exist.
   * The "./drivers/{driver}.js" file must also exist.
   */
  driver: 'transifex',

  paths: {
    rootFolder,
    binaryFolder,
    i18nModuleLocation,
    i18nextConfigLocation,
    translationsLocation,
    translationFilePattern
  },

  drivers: {
    transifex: {
      token: process.env.TRANSIFEX_TOKEN
    }
  }
};
