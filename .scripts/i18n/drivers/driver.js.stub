/*
 * A translation driver must export a function.
 * This function will receive the driver's configuration.
 * It should return two implemented functions:
 * - exportKeys(keys, languageList)
 * - importTranslations(languageList)
 */
module.exports = (config) => {
  /**
   * Export given keys.
   *
   * @param {Array<string>} keys - The translation keys to export the translation service.
   * @param {Array<string>} languageList - List of all languages that should translate the given keys.
   * @returns {Promise<void>} The async process.
   * @throws {Error} Indicates an export error.
   */
  const exportKeys = async (keys, languageList) => {
    // TODO implement exportKeys
  };

  /**
   * Import all available translations for given language list.
   *
   * @param {Array<string>} languageList - List of languages to import translations from.
   * @returns {Promise<Object<string, Object<string, string>>>} Key-value translations for the given languages.
   * @throws {Error} Indicates an import error.
   */
  const importTranslations = async (languageList) => {
    // TODO implement importTranslations
  };

  return {
    exportKeys,
    importTranslations,
  };
};
