const path = require('path');
const { spawn } = require('../common');

/*
 * A translation driver must export a function.
 * This function will receive the driver's configuration.
 * It should return two implemented functions:
 * - exportKeys(keys, languageList)
 * - importTranslations(languageList)
 */
module.exports = ({ paths: { binaryFolder }, token }) => {
  /**
   * Export given keys.
   *
   * @param {Array<string>} keys - The translation keys to export the translation service.
   * @param {Array<string>} languageList - List of all languages that should translate the given keys.
   * @returns {Promise<void>} The async process.
   * @throws {Error} Indicates an export error.
   */
  const exportKeys = async () => {
    await spawn(path.join(binaryFolder, 'tx'), ['--token', token, 'push', '--source']);
  };

  /**
   * Import all available translations for given language list.
   *
   * @param {Array<string>} languageList - List of languages to import translations from.
   * @returns {Promise<Object<string, Object<string, string>>|undefined>} Key-value translations for the given languages, or void if already handled.
   * @throws {Error} Indicates an import error.
   */
  const importTranslations = async (languageList) => {
    await spawn(path.join(binaryFolder, 'tx'), [
      '--token',
      token,
      'pull',
      '--force',
      '--languages',
      languageList.join(','),
      '--mode',
      'onlyreviewed',
      '--translations'
    ]);
  };

  return {
    exportKeys,
    importTranslations
  };
};
