const { existsSync } = require('fs');
const path = require('path');
const { driver: defaultDriver, drivers, ...config } = require('../config');

const factory = (driver = 'default') => {
  if (driver === 'default') {
    return factory(defaultDriver);
  }

  if (!Object.prototype.hasOwnProperty.call(drivers, driver)) {
    throw new Error(`Translation driver [${driver}] not configured.`);
  }

  const driverPath = path.join(__dirname, `${driver}.js`);

  if (!existsSync(driverPath)) {
    throw new Error(`Translation driver [${driver}] not implemented.`);
  }

  // eslint-disable-next-line import/no-dynamic-require, global-require
  return require(driverPath)({ ...config, ...drivers[driver] });
};

module.exports = factory;
