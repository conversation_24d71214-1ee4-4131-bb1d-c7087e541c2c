#!/bin/bash

CURRENT_DIRECTORY="$(cd "$(dirname "${BASH_SOURCE[0]}")" >/dev/null 2>&1 && pwd)"
ROOT_DIRECTORY=$(realpath "${CURRENT_DIRECTORY}/../..")
DATE=$(date "+%Y-%m-%d")
CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
BRANCH="chores/translations/${DATE}"

git add "${ROOT_DIRECTORY}/src/i18n/*.json"
IS_CLEAN=$(git diff --cached --quiet && echo "1")

if [ "${IS_CLEAN}" == "1" ]
then
  echo "No translation update, aborting..."
  exit 0
fi

git checkout -b "${BRANCH}"
git commit -m "Update translations"
git push origin "${BRANCH}"
git checkout "${CURRENT_BRANCH}"
