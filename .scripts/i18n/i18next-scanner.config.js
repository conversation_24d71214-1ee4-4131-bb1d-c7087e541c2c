/* eslint-disable strict */

'use strict';

const path = require('path');
const {
  paths: { i18nextConfigLocation, translationFilePattern, translationsLocation },
  defaultLanguage
} = require('./config');

require('ts-node').register({
  transpileOnly: true,
  cwd: i18nextConfigLocation
});

const defaultLanguageTranslation = require(`${translationsLocation}/${defaultLanguage}.json`);
const debug = process.env.NODE_ENV === 'development' || process.env.CI === true.toString();

module.exports = {
  input: ['pages/**/*.{ts,tsx}', 'src/**/*.{ts,tsx}', '!src/**/*.{test,spec}.{ts,tsx}', '!**/node_modules/**'],
  output: './',
  options: {
    removeUnusedKeys: true,
    debug,
    func: {
      list: ['trans'],
      extensions: ['.ts', '.tsx']
    },
    lngs: ['en', 'es', 'vi', 'pt', 'th'],
    ns: ['translation'],
    defaultLng: defaultLanguage,
    defaultNs: 'translation',
    defaultValue: (lng, ns, key, options) => {
      return defaultLanguageTranslation[key] ?? '__STRING_NOT_TRANSLATED__';
    },
    nsSeparator: false, // namespace separator
    keySeparator: false, // key separator
    allowDynamicKeys: true,
    plural: false,
    interpolation: {
      prefix: '{{',
      suffix: '}}'
    },
    resource: {
      loadPath: translationFilePattern,
      savePath: translationFilePattern,
      jsonIndent: 2,
      lineEnding: '\n'
    }
  }
};
