#!/usr/bin/env bash

# curl and expect response code 200
code=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080)
# expect response code 200
if [ $code -eq 200 ]; then
  echo "OK"
else
  echo "FAIL"
  exit 1
fi


#test multiple array of urls and expect response code
urls=(
"http://localhost:4500 200"
"http://localhost:4500/ 200"
"http://localhost:4500/login 200"
"http://localhost:8080/forget-password 200"
)
for url in "${urls[@]}"
do
  code=$(curl -s -o /dev/null -w "%{http_code}" ${url%% *})
  if [ $code -eq ${url##* } ]; then
    echo "OK"
  else
    echo "FAIL"
    exit 1
  fi
done
