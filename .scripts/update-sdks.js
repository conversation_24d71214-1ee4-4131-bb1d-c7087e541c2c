let packagesToBeUpdates = [];
const packageJson = require(`${__dirname}/../package.json`);
const { execSync } = require('child_process');
const { writeFileSync } = require('fs');

// usage:
// node .scripts/update-sdks.js sdk-name version
// or
// node /scripts/update-sdks.js all master

const args = process.argv.slice(2);
const repoNameMatch = args[0] ?? 'all';
const versionMatch = args[1] ?? 'master';

function maxVersion(versions) {
  const versionMap = versions.reduce((acc, version) => {
    acc[version] = version
      .split('.')
      .map((v) => parseInt(v))
      .join('.');
    return acc;
  }, {});

  const version = Object.values(versionMap)
    .map((e) => e.split('.'))
    .map((e) => e.map((v) => parseInt(v)))
    .sort((ao, bo) => {
      const a = [...ao];
      const b = [...bo];
      const maxLen = [...a, ...b].reduce((acc, e) => Math.max(acc, e.toString().length), 0);
      for (let i = 0; i < Math.max(a.length, b.length); i++) {
        a[i] = a[i] ?? 0;
        b[i] = b[i] ?? 0;
      }
      const aSum = Number(a.map((e) => String(e).padStart(maxLen, '0')).join(''));
      const bSum = Number(b.map((e) => String(e).padStart(maxLen, '0')).join(''));
      return aSum - bSum > 0 ? 1 : -1;
    })
    .pop()
    ?.join('.');
  return Object.entries(versionMap).find(([key, value]) => value === version)?.[0];
}

Object.entries(packageJson.dependencies).forEach(([name, url]) => {
  if (name.startsWith('@xpertsea/')) {
    if (!args || repoNameMatch === 'all' || repoNameMatch === '-' || (repoNameMatch && name.includes(repoNameMatch))) {
      packagesToBeUpdates.push({ name, url });
    }
  }
});

const pkgJson = packagesToBeUpdates
  .map(({ name, url }) => {
    const urlSplit = url.split('#');
    const tag = execSync(`git ls-remote --tags ${urlSplit[0]} "*${versionMatch}*"`).toString().trim();
    const tagList = tag
      .split('\t')
      .map((e) => e.split('\n')[0])
      .filter((t) => t.includes('refs/tags/'))
      .map((t) => t.replace('refs/tags/', '').replace('^{}', ''));

    let isSemver = false;
    const shaHashes = {};
    let targetTag = tagList
      .filter((t) => {
        return t.match(/-\d+-\d+-\d+-\d+-\d+-\d+/);
      })
      .map((t) => {
        const n = t.split('-');
        const hash = n.pop();
        const tagWithoutHash = n.join('-');
        shaHashes[tagWithoutHash] = hash;
        return tagWithoutHash;
      })
      .sort()
      .reverse()[0];

    if (!targetTag) {
      targetTag = maxVersion(tagList);
      isSemver = true;
    }

    if (!targetTag) {
      console.log(`${name} has no tag matching ${versionMatch}*`);
      return;
    }
    let newUrl = `${urlSplit[0]}#${targetTag}-${shaHashes[targetTag]}`;
    if (isSemver) {
      newUrl = `${urlSplit[0]}#${targetTag}`;
    }

    if (url === newUrl) {
      console.log(`${name} is already up to date`);
      return;
    }
    console.log(`${name} will be updated to ${newUrl}`);
    return { name, url: newUrl };
  })
  .filter((e) => !!e);

Object.entries(pkgJson).forEach(([ket, { name, url }]) => {
  const pkgJson = require(`${__dirname}/../package.json`);
  if (pkgJson.dependencies[name]) {
    pkgJson.dependencies[name] = url;
  }
  if (pkgJson.devDependencies[name]) {
    pkgJson.devDependencies[name] = url;
  }
  writeFileSync(`${__dirname}/../package.json`, JSON.stringify(pkgJson, null, 2));
});

if (Object.keys(pkgJson).length > 0) {
  execSync(`yarn install`);
  console.log(`packages updated`);
} else {
  console.log('All packages are up to date');
}
