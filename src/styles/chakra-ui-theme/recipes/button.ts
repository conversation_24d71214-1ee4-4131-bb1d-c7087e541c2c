import { defineRecipe } from '@chakra-ui/react';

const size = {
  xs: {
    h: '32px',
    w: 'max-content',
    fontSize: 'sm',
    lineHeight: '2',
    fontWeight: 'semibold',
    px: 'xs'
  },
  sm: {
    h: '32px',
    w: 'max-content',
    fontSize: 'md',
    lineHeight: '4',
    fontWeight: 'semibold',
    letterSpacing: 'little',
    px: 'sm-alt'
  },
  md: {
    h: '40px',
    w: 'max-content ',
    fontSize: 'lg',
    lineHeight: '4',
    fontWeight: 'semibold',
    letterSpacing: 'littleWide',
    px: 'md'
  }
};

const variant = {
  primary: {
    flexShrink: 0,
    bgColor: 'colorPalette.600',
    color: 'white',
    _hover: {
      bgColor: 'colorPalette.700',
      _disabled: {
        background: 'bg.gray.strong'
      }
    },
    _focus: {
      bgColor: 'colorPalette.700',
      outline: '2px solid {colors.colorPalette.700}'
    },
    _active: {
      bgColor: 'colorPalette.900',
      outline: 'none',
      _disabled: {
        color: 'text.gray.disabled'
      }
    },
    _expanded: {
      bgColor: 'colorPalette.900',
      outline: 'none',
      _disabled: {
        color: 'text.gray.disabled'
      }
    },
    _disabled: {
      bgColor: 'gray.200',
      color: 'gray.500',
      opacity: 0.9,
      _hover: {
        bgColor: 'gray.200'
      }
    },
    _loading: {
      bgColor: 'button.disabled',
      color: 'text.gray.strong',
      _hover: {
        color: 'text.gray.strong'
      }
    },
    _focusVisible: {
      boxShadow: 'focus'
    }
  },

  link: {
    flexShrink: 0,
    color: 'button.link',
    _active: {
      _disabled: {
        color: 'text.gray.disabled'
      }
    },
    _disabled: {
      color: 'text.gray.disabled',
      opacity: 0.9
    },
    _focusVisible: {
      outline: 'none',
      boxShadow: 'none'
    }
  },

  ghost: {
    flexShrink: 0,
    color: 'text.gray.strong',
    _active: {
      _disabled: {
        color: 'text.gray.disabled'
      }
    },
    _disabled: {
      color: 'text.gray.disabled',
      opacity: 0.9
    },
    _focus: {
      outline: '2px solid',
      outlineColor: 'button.brandBlue.focused'
    },
    _focusVisible: {
      outline: 'none',
      boxShadow: 'none'
    }
  },

  secondary: {
    borderWidth: '1px',
    borderColor: 'border.gray',
    flexShrink: 0,
    bgColor: 'white',
    color: 'gray.800',
    _hover: {
      bgColor: 'gray.200',
      borderColor: 'border.gray.hover',
      _disabled: {
        bgColor: 'bg.gray.strong',
        color: 'text.gray.disabled'
      }
    },
    _focus: {
      bgColor: 'gray.100',
      borderColor: 'border.gray.hover',
      outline: 'none'
    },
    _checked: {
      bgColor: 'gray.200',
      borderColor: 'border.gray.hover',
      outline: 'none'
    },
    _active: {
      bgColor: 'grayDown.300',
      borderColor: 'border.gray.hover',
      outline: 'none'
    },
    _expanded: {
      bgColor: 'grayDown.300',
      borderColor: 'border.gray.hover',
      outline: 'none'
    },
    _selected: {
      bgColor: 'gray.200',
      borderColor: 'border.gray.hover',
      outline: 'none'
    },
    _disabled: {
      bgColor: 'gray.200',
      color: 'gray.500',
      border: 'none',
      opacity: 0.9,
      _hover: {
        bgColor: 'gray.200'
      }
    },
    _loading: {
      bgColor: 'button.disabled',
      color: 'text.gray.strong',
      _hover: {
        color: 'text.gray.strong'
      }
    },
    _focusVisible: {
      boxShadow: 'none'
    }
  }
};

export const button = defineRecipe({
  base: {
    borderRadius: 'full',
    transition: 'unset ',
    colorPalette: 'brandBlue'
  },
  variants: {
    variant,
    size
  },
  defaultVariants: {
    variant: 'primary',
    size: 'md'
  }
});
