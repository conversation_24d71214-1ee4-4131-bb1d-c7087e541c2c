import { defineRecipe } from '@chakra-ui/react';

const base = {
  _disabled: {
    opacity: 1.0
  },
  _placeholder: {
    opacity: 1,
    color: 'text.gray.disabled'
  }
};

const visual = {
  outline: {
    border: 'none',
    outlineOffset: 0,
    outline: '1px solid {colors.border.gray}',
    color: 'text.gray',
    borderRadius: 'lg',
    bg: 'gray.100',
    _invalid: {
      shadow: 'none',
      borderColor: 'unset',
      outlineWidth: '1.5px',
      outlineColor: 'border.error',
      _focus: { borderColor: 'unset', outlineColor: 'border.error' },
      _hover: { borderColor: 'unset', outlineColor: 'border.error' }
    },
    _hover: {
      borderColor: 'unset',
      outlineColor: 'button.link'
    },
    _focusVisible: {
      zIndex: 1,
      shadow: 'none',
      outlineOffset: 0,
      borderColor: 'unset',
      outlineWidth: '1.5px',
      outlineColor: 'button.link'
    },
    _disabled: {
      bg: 'bg.gray.strong',
      color: 'text.gray.disabled'
    }
  }
};

const size = {
  sm: {
    px: 'xs',
    fontSize: 'sm',
    lineHeight: '2',
    fontWeight: 'semibold',
    letterSpacing: 'small'
  },
  md: {
    px: 'xs',
    fontSize: 'md',
    lineHeight: '4',
    fontWeight: 'semibold',
    letterSpacing: 'little'
  }
};

export const textarea = defineRecipe({
  base,
  variants: {
    size,
    visual
  },
  defaultVariants: {
    visual: 'outline',
    size: 'md'
  }
});
