import { defineRecipe } from '@chakra-ui/react';

const size = {
  light100: {
    fontSize: '3xl',
    lineHeight: '10'
  },
  heavy100: {
    fontSize: '3xl',
    lineHeight: '10',
    fontWeight: 'bold'
  },
  light200: {
    fontSize: '2xl',
    lineHeight: '7'
  },
  heavy200: {
    fontSize: '2xl',
    lineHeight: '7',
    fontWeight: 'bold'
  },
  light300: {
    fontSize: 'xl',
    lineHeight: '6'
  },
  heavy300: {
    fontSize: 'xl',
    lineHeight: '6',
    fontWeight: 'bold'
  }
};

export const heading = defineRecipe({
  variants: {
    size
  },
  defaultVariants: {
    size: 'heavy200'
  }
});
