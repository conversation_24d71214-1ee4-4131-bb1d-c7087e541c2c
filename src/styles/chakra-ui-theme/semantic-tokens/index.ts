import type { ThemingConfig } from '@chakra-ui/react/styled-system';

type SemanticTokenDefinition = ThemingConfig['semanticTokens'];

export const semanticTokens: SemanticTokenDefinition = {
  colors: {
    'text.gray': { DEFAULT: { value: '{colors.gray.800}' } },
    'text.gray.inverted': { DEFAULT: { value: '{colors.white}' } },
    'text.squidInkPowder': { DEFAULT: { value: '{colors.squidInkPowder.600}' } },
    text: {
      gray: {
        weak: { value: '{colors.gray.700}' },
        strong: { value: '{colors.gray.900}' },
        disabled: { value: '{colors.gray.500}' },
        inverted: {
          weak: { value: '{colors.gray.400}' }
        }
      },
      squidInkPowder: { weak: { value: '{colors.squidInkPowder.400}' } },
      brandBlue: { value: '{colors.brandBlue.600}' },
      semanticRed: { value: '{colors.semanticRed.800}' },
      brandGreen: { value: '{colors.brandGreen.900}' },
      shrimpyPinky: { value: '{colors.shrimpyPinky.600}' },
      semanticYellow: { value: '{colors.semanticYellow.900}' }
    },
    'bg.gray': { DEFAULT: { value: '{colors.white}' } },
    'bg.squidInkPowder': { DEFAULT: { value: '{colors.squidInkPowder.600}' } },
    'bg.brandBlue': { DEFAULT: { value: '{colors.brandBlue.600}' } },
    'bg.shrimpyPinky': { DEFAULT: { value: '{colors.shrimpyPinky.600}' } },
    'bg.semanticRed': { DEFAULT: { value: '{colors.semanticRed.600}' } },
    'bg.brandGreen': { DEFAULT: { value: '{colors.brandGreen.600}' } },
    'bg.semanticYellow': { DEFAULT: { value: '{colors.semanticYellow.600}' } },
    bg: {
      gray: {
        medium: { value: '{colors.gray.100}' },
        strong: { value: '{colors.gray.200}' }
      },
      squidInkPowder: {
        hover: { value: '{colors.squidInkPowder.700}' },
        down: { value: '{colors.squidInkPowder.800}' },
        weak: { value: '{colors.squidInkPowder.100}' }
      },
      brandBlue: {
        profile: { value: '{colors.graphBrandBlue}' },
        weakShade1: { value: '{colors.brandBlue.100}' },
        weakShade2: { value: '{colors.brandBlue.200}' }
      },
      shrimpyPinky: { weak: { value: '{colors.shrimpyPinky.100}' } },
      semanticRed: { weak: { value: '{colors.semanticRed.100}' } },
      brandGreen: {
        weakShade1: { value: '{colors.brandGreen.100}' },
        weakShade2: { value: '{colors.brandGreen.200}' }
      },
      semanticYellow: { weak: { value: '{colors.semanticYellow.200}' } },
      canvas: {
        value: { base: '{colors.gray.25}', _dark: '{colors.gray.950}' }
      },
      surface: {
        value: { base: '{colors.white}', _dark: '{colors.gray.900}' }
      },
      subtle: {
        value: { base: '{colors.gray.50}', _dark: '{colors.gray.800}' }
      },
      muted: {
        value: { base: '{colors.gray.100}', _dark: '{colors.gray.700}' }
      }
    },
    'icon.gray': { DEFAULT: { value: '{colors.gray.800}' } },
    'icon.gray.inverted': { DEFAULT: { value: '{colors.white}' } },
    'icon.brandBlue': { DEFAULT: { value: '{colors.graphBrandBlue}' } },
    'icon.semanticRed': { DEFAULT: { value: '{colors.semanticRed.600}' } },
    'icon.brandGreen': { DEFAULT: { value: '{colors.brandGreen.600}' } },
    'icon.semanticYellow': { DEFAULT: { value: '{colors.semanticYellow.600}' } },
    'icon.shrimpyPinky': { DEFAULT: { value: '{colors.shrimpyPinky.600}' } },
    'icon.squidInkPowder': { DEFAULT: { value: '{colors.squidInkPowder.600}' } },
    icon: {
      gray: {
        weak: { value: '{colors.gray.700}' },
        disabled: { value: '{colors.gray.500}' },
        inverted: {
          weak: { value: '{colors.gray.400}' }
        }
      },
      brandBlue: {
        weakShade1: { value: '{colors.brandBlue.100}' },
        weakShade2: { value: '{colors.brandBlue.200}' },
        weakShade3: { value: '{colors.brandBlue.300}' }
      },
      semanticRed: { weak: { value: '{colors.semanticRed.100}' } },
      brandGreen: { weak: { value: '{colors.brandGreen.100}' } },
      semanticYellow: { weak: { value: '{colors.semanticYellow.200}' } },
      shrimpyPinky: { weak: { value: '{colors.shrimpyPinky.100}' } },
      squidInkPowder: { weak: { value: '{colors.squidInkPowder.100}' } }
    },
    'border.gray': { DEFAULT: { value: '{colors.gray.400}' } },
    'border.brandBlue': { DEFAULT: { value: '{colors.graphBrandBlue}' } },
    'border.semanticRed': { DEFAULT: { value: '{colors.semanticRed.600}' } },
    'border.brandGreen': { DEFAULT: { value: '{colors.brandGreen.600}' } },
    'border.semanticYellow': { DEFAULT: { value: '{colors.semanticYellow.600}' } },
    'border.shrimpyPinky': { DEFAULT: { value: '{colors.shrimpyPinky.600}' } },
    'border.squidInkPowder': { DEFAULT: { value: '{colors.squidInkPowder.600}' } },
    border: {
      gray: {
        hover: { value: '{colors.gray.700}' },
        down: { value: '{colors.gray.900}' },
        weak: { value: '{colors.gray.200}' },
        negative: { value: '{colors.white}' }
      },
      semanticRed: { weak: { value: '{colors.semanticRed.100}' } },
      brandGreen: { weak: { value: '{colors.brandGreen.100}' } },
      semanticYellow: { weak: { value: '{colors.semanticYellow.200}' } },
      shrimpyPinky: { weak: { value: '{colors.shrimpyPinky.100}' } },
      squidInkPowder: { weak: { value: '{colors.squidInkPowder.100}' } }
    },
    'graph.brandBlue': { DEFAULT: { value: '{colors.graphBrandBlue}' } },
    'graph.shrimpyPinky': { DEFAULT: { value: '{colors.shrimpyPinky.600}' } },
    'graph.squidInkPowder': { DEFAULT: { value: '{colors.squidInkPowder.600}' } },
    graph: {
      brandBlue: {
        weakShade1: { value: '{colors.brandBlue.100}' },
        weakShade2: { value: '{colors.brandBlue.200}' },
        weakShade3: { value: '{colors.brandBlue.300}' },
        weakShade4: { value: '{colors.brandBlue.400}' }
      },
      shrimpyPinky: {
        weakShade1: { value: '{colors.shrimpyPinky.100}' },
        weakShade2: { value: '{colors.shrimpyPinky.200}' },
        weakShade3: { value: '{colors.shrimpyPinky.300}' },
        weakShade4: { value: '{colors.shrimpyPinky.400}' }
      },
      squidInkPowder: {
        weakShade1: { value: '{colors.squidInkPowder.100}' },
        weakShade2: { value: '{colors.squidInkPowder.200}' },
        weakShade3: { value: '{colors.squidInkPowder.300}' },
        weakShade4: { value: '{colors.squidInkPowder.400}' }
      }
    },
    'button.brandBlue': { DEFAULT: { value: '{colors.brandBlue.600}' } },
    'button.squidInkPowder': { DEFAULT: { value: '{colors.squidInkPowder.600}' } },
    button: {
      link: { value: '{colors.linkBlue}' },
      disabled: { value: '{colors.gray.200}' },
      brandBlue: {
        focused: { value: '{colors.brandBlue.700}' },
        hover: { value: '{colors.brandBlue.700}' },
        down: { value: '{colors.brandBlue.900}' }
      },
      squidInkPowder: {
        focused: { value: '{colors.squidInkPowder.500}' },
        hover: { value: '{colors.squidInkPowder.500}' },
        down: { value: '{colors.squidInkPowder.900}' }
      }
    }
  }
};
