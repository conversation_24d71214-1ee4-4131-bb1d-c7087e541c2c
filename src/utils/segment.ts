import { isBrowser } from '@utils/browser';
import { Router } from 'next/router';
import { unSlugfiyUrl } from '@utils/string';
import isMobile from 'ismobilejs';

export type JSONPrimitive = string | number | boolean | null;
export type JSONValue = JSONPrimitive | JSONObject | JSONArray;
export type JSONObject = { [member: string]: JSONValue };
export type JSONArray = JSONValue[];

export const actionsName = {
  // Implemented
  farmFeedTableEdit: 'farm_feed_table_edit',
  farmFeedTableUpload: 'farm_feed_table_upload',
  farmFeedTableDelete: 'farm_feed_table_Delete',
  feedPalletToggleSwitchToggled: 'feed_pallet_toggle_switch_toggled',
  feedPalletSaveClicked: 'feed_pallet_save_clicked',
  feedPalletRemoveClicked: 'feed_pallet_remove_clicked',
  feedPalletSaveFailed: 'feed_pallet_save_failed',
  feedPalletRemoveFailed: 'feed_pallet_remove_failed',
  feedPalletSaveSuccessful: 'feed_pallet_save_successful',
  feedPalletRemoveSuccessful: 'feed_pallet_remove_successful',
  feedPalletAddClicked: 'feed_pallet_add_clicked',
  feedPalletCancelClicked: 'feed_pallet_cancel_clicked',
  loginClicked: 'login_clicked',
  loginAttempted: 'login_attempted',
  loginFailed: 'login_failed',
  loginSuccessful: 'login_successful',
  logoutSuccessful: 'logout_successful',
  switchFarmClicked: 'switch_farm_clicked',
  farmSelected: 'select_farm_clicked',
  languageClicked: 'language_clicked',
  stockPondClicked: 'stock_pond_clicked',
  pondStocked: 'pond_stocked',
  nurseryStocked: 'nursery_stocked',
  pondEditStock: 'pond_edit_stock',
  nurseryEditStock: 'nursery_edit_stock',
  pondListItemClicked: 'pond_list_item_clicked',
  harvestActionButtonClicked: 'harvest_actions_button_clicked',
  harvestPondClicked: 'harvest_pond_clicked',
  dailyParamsListSorted: 'daily_params_list_sorted',
  harvestSummaryDownloadButtonClicked: 'harvest_summary_download_button_clicked',
  harvestSummaryListSorted: 'harvest_summary_list_sorted',
  harvestSummaryCalendarDownloadButtonClicked: 'harvest_summary_calendar_download_button_clicked',
  editHarvestClicked: 'edit_harvest_clicked',
  createManualMonitoringClicked: 'create_manual_monitoring_clicked',
  editManualMonitoringClicked: 'edit_manual_monitoring_clicked',
  manuallyMonitored: 'manually_monitored',
  pondHarvested: 'pond_harvested',
  pondHarvestEdited: 'pond_harvest_edited',
  pondHarvestPlanEdited: 'pond_harvest_plan_edited',
  pondPartialHarvestPlanEdited: 'pond_partial_harvest_plan_edited',
  pondPartialHarvestPlanAdded: 'pond_partial_harvest_plan_added',
  imageCarouselClicked: 'image_carousel_clicked',
  imageCarouselChanged: 'image_carousel_changed',
  cvInfoClicked: 'cv_info_clicked',
  pondListSortColumnClicked: 'column_sort_clicked',
  searchTrayXpc3Clicked: 'search_tray_xpc3_clicked',
  rejectInvitationClicked: 'reject_invitation_clicked',
  accountInvitationClicked: 'accept_invitation_clicked',
  verifyEmailClicked: 'verify_email_clicked',
  resendCodeClicked: 'resend_code_clicked',
  acceptInvitationFormSubmit: 'accept_invitation_form_submit',
  forgotPasswordFormSubmit: 'forgot_password_form_submit',
  changePasswordClicked: 'change_password_clicked',
  createAccountClicked: 'create_account_clicked',
  errorScreenGoToHomeClicked: 'error_screen_go_to_home_clicked',
  notFoundScreenGoToHomeClicked: 'not_found_screen_go_to_home_clicked',
  howCvCalculatedClicked: 'how_cv_calculated_clicked',
  howCvDeterminedClicked: 'how_cv_determined_clicked',
  cvTipsToImproveClicked: 'cv_tips_to_Improve_clicked',
  profitViewDownloadButtonClicked: 'profit_view_download_button_clicked',
  profitViewPrintButtonClicked: 'profit_view_download_button_clicked',
  createNewPondClicked: 'create_new_pond_clicked',
  createNewNurseryClicked: 'create_new_nursery_clicked',
  createNewTrayXpc3Clicked: 'create_new_tray_xpc3_clicked',
  paymentListXpc3Clicked: 'payment_list_xpc3_clicked',
  createAddCreditXpc3Clicked: 'create_add_credit_xpc3_clicked',
  bulkInviteClicked: 'bulk_invite_clicked',
  invitationAddUserClicked: 'invitation_add_user_clicked',
  bulkInviteModalCancelClicked: 'bulk_invite_modal_cancel_clicked',
  bulkInviteModalSaveClicked: 'bulk_invite_modal_save_clicked',
  cancelInvitationClicked: 'cancel_invitation_clicked',
  monitoringListDispersionInfoClicked: 'monitoring_list_dispersion_info_clicked',
  monitoringListItemClicked: 'monitoring_list_item_clicked',
  monitoringDistributionSelected: 'monitoring_distribution_selected',
  monitoringPhotoExpandClicked: 'monitoring_photo_expand_clicked',
  deleteMonitoringClicked: 'delete_monitoring_clicked',
  deleteMonitoringCancelClicked: 'delete_monitoring_cancel_clicked',
  deleteMonitoringConfirmClicked: 'delete_monitoring_confirm_clicked',
  reSubmitMonitoringClicked: 're_submit_monitoring_clicked',
  trayImageModalCloseClicked: 'tray_image_modal_close_clicked',
  editMyAccountCancelClicked: 'edit_my_account_cancel_clicked',
  editMyAccountSaveClicked: 'edit_my_account_save_clicked',
  editPondCancelClicked: 'edit_pond_cancel_clicked',
  editNurseryCancelClicked: 'edit_nursery_cancel_clicked',
  editTrayXpc3CancelClicked: 'edit_tray_xpc3_cancel_clicked',
  editPondSaveClicked: 'edit_pond_save_clicked',
  editTrayXpc3SaveClicked: 'edit_tray_xpc3_save_clicked',
  addPondCancelClicked: 'add_pond_cancel_clicked',
  addNurseryCancelClicked: 'add_nursery_cancel_clicked',
  addNoteCancelClicked: 'add_note_cancel_clicked',
  addTrayXpc3CancelClicked: 'add_tray_xpc3_cancel_clicked',
  addPondSaveClicked: 'add_pond_save_clicked',
  addNoteSaveClicked: 'add_note_save_clicked',
  addTrayXpc3SaveClicked: 'add_tray_xpc3_save_clicked',
  addNotesClicked: 'add_notes_clicked',
  editStockingCancelClicked: 'edit_stocking_cancel_clicked',
  editStockingSaveClicked: 'edit_stocking_save_clicked',
  addStockingCancelClicked: 'add_stocking_cancel_clicked',
  addStockingSaveClicked: 'add_stocking_save_clicked',
  archivePondCancelClicked: 'archive_pond_cancel_clicked',
  archiveNurseryCancelClicked: 'archive_nursery_cancel_clicked',
  archivePondSaveClicked: 'archive_pond_save_clicked',
  archiveNurserySaveClicked: 'archive_nursery_save_clicked',
  harvestTargetClicked: 'harvest_target_clicked',
  harvestTargetCancelClicked: 'harvest_target_cancel_clicked',
  harvestTargetSetLaterClicked: 'harvest_target_set_later_clicked',
  harvestTargetUpdateClicked: 'harvest_target_update_clicked',
  harvestTargetSetTargetClicked: 'harvest_target_set_target_clicked',
  harvestTargetRemoveTargetClicked: 'harvest_target_remove_target_clicked',
  survivalRateClicked: 'survival_rate_clicked',
  survivalRateUpdateClicked: 'survival_rate_update_clicked',
  survivalRateCloseClicked: 'survival_rate_close_clicked',
  populationSelected: 'population_selected',
  downloadFileClicked: 'download_file_clicked',
  viewFileClicked: 'view_file_clicked',
  uploadFileClicked: 'upload_file_clicked',
  uploadFileFailed: 'upload_file_failed',
  uploadFileSuccessful: 'upload_file_successful',
  editFileNameClicked: 'edit_file_name_clicked',
  editFileNameConfirmClicked: 'edit_file_name_confirm_clicked',
  editFileNameCancelClicked: 'edit_file_name_cancel_clicked',
  deleteFileClicked: 'delete_file_clicked',
  deleteFileConfirmClicked: 'delete_file_confirm_clicked',
  deleteFileCancelClicked: 'delete_file_cancel_clicked',
  survivalRateEntered: 'survival_rate_entered',
  survivalRateEdited: 'survival_rate_edited',
  harvestTargetSuccessful: 'harvest_target_successful',
  harvestTargetFailed: 'harvest_target_failed',
  harvestTargetUpdateSuccessful: 'harvest_target_update_successful',
  harvestTargetUpdateFailed: 'harvest_target_update_failed',
  harvestPlannerViewChanged: 'harvest_planner_view_changed',
  addHarvestPlanClicked: 'add_harvest_plan_clicked',
  editHarvestPlanClicked: 'edit_harvest_plan_clicked',
  editPartialHarvestPlanClicked: 'edit_partial_harvest_plan_clicked',
  addPartialHarvestPlanClicked: 'add_partial_harvest_plan_clicked',
  managePartialHarvestClicked: 'manage_partial_harvest_clicked',

  weightChartChanged: 'weight_chart_changed',

  pondActionUpdateTargetClicked: 'pond_action_update_target_clicked',

  harvestOptimizerTabSelected: 'harvest_optimizer_tab_selected',

  harvestOptimizerChartTypeChanged: 'harvest_optimizer_chart_type_changed',
  revenueAtHarvestChartIntervalClicked: 'revenue_at_harvest_chart_interval_clicked',

  keyVariablesSubmitted: 'key_variables_submitted',
  keyVariablesSaveClicked: 'key_variables_save_clicked',
  keyVariablesCancelClicked: 'key_variables_cancel_clicked',

  customizeViewClicked: 'customize_view_clicked',
  customizeViewSaveSubmitted: 'customize_view_save_submitted',
  customizeViewSaveClicked: 'customize_view_save_clicked',
  customizeViewCancelClicked: 'customize_view_cancel_clicked',

  farmSummaryListSorted: 'farm_summary_list_sorted',
  farmSummaryChartDropdownItemSelected: 'farm_summary_chart_dropdown_item_selected',
  changeVariablesClicked: 'change_variables_clicked',
  stockAndProtocolChangeVariablesClicked: 'stock_and_protocol_change_variables_clicked',
  stockAndProtocolCancelClicked: 'stock_and_protocol_cancel_clicked',
  stockAndProtocolSaveClicked: 'stock_and_protocol_save_clicked',
  stockAndProtocolVariablesSubmitted: 'stock_and_protocol_variables_submitted',

  feedDataEditClicked: 'feed_data_edit_clicked',
  feedDataSubmitted: 'feed_data_submitted',
  feedDataEditCancelClicked: 'feed_data_edit_cancel_clicked',
  feedDataEditSaveClicked: 'feed_data_edit_save_clicked',

  ponOverviewDataTabClicked: 'pond_overview_data_tab_clicked',
  pondOverviewTabClicked: 'pond_overview_tab_clicked',
  pondOverviewManageProjectionClicked: 'pond_overview_manage_projection_clicked',

  farmTabSelected: 'farm_tab_selected',
  farmDataUpdaterViewChanged: 'farm_data_updater_view_changed',

  sidebarSettingsClicked: 'sidebar_settings_clicked',
  sidebarFarmClicked: 'sidebar_farm_clicked',
  sidebarHarvestClicked: 'sidebar_harvest_clicked',
  sidebarHomeClicked: 'sidebar_home_clicked',

  settingsTabSelected: 'settings_tab_selected',

  optimizeHarvestClicked: 'optimize_harvest_clicked',

  harvestOptimizerTabClicked: 'harvest_optimizer_tab_clicked',
  harvestCoordinatorTabClicked: 'harvest_coordinator_tab_clicked',

  // farm config
  farmConfigWeeklyProgrammingEditClicked: 'farm_config_weekly_programming_edit_clicked',
  farmConfigWeeklyProgrammingSaveClicked: 'farm_config_weekly_programming_save_clicked',
  farmConfigWeeklyProgrammingCancelClicked: 'farm_config_weekly_programming_cancel_clicked',
  farmConfigProductionTargetsEditClicked: 'farm_config_production_target_edit_clicked',
  farmConfigProductionTargetsSaveClicked: 'farm_config_production_target_save_clicked',
  farmConfigProductionTargetsCancelClicked: 'farm_config_production_target_cancel_clicked',
  farmConfigStockingDefaultsEditClicked: 'farm_config_stocking_defaults_edit_clicked',
  farmConfigStockingDefaultsSaveClicked: 'farm_config_stocking_defaults_save_clicked',
  farmConfigStockingDefaultsCancelClicked: 'farm_config_stocking_defaults_cancel_clicked',

  farmConfigHatcheriesEditClicked: 'farm_config_hatcheries_edit_clicked',
  farmConfigHatcheriesSaveClicked: 'farm_config_hatcheries_save_clicked',
  farmConfigHatcheriesCancelClicked: 'farm_config_hatcheries_cancel_clicked',
  farmConfigGeneticsEditClicked: 'farm_config_genetics_edit_clicked',
  farmConfigGeneticsSaveClicked: 'farm_config_genetics_save_clicked',
  farmConfigGeneticsCancelClicked: 'farm_config_genetics_cancel_clicked',

  farmConfigProcessorsPriceListEditClicked: 'farm_config_processors_price_list_edit_clicked',
  farmConfigProcessorsPriceListSaveClicked: 'farm_config_processors_price_list_save_clicked',
  farmConfigProcessorsPriceListCancelClicked: 'farm_config_processors_price_list_cancel_clicked',
  farmConfigProcessorsPriceListAddItemClicked: 'farm_config_processors_price_list_add_item_clicked',
  farmConfigProcessorsPriceListEditItemClicked: 'farm_config_processors_price_list_edit_item_clicked',
  farmConfigProcessorsPriceListRemoveItemClicked: 'farm_config_processors_price_list_remove_item_clicked',
  farmConfigProcessorsPriceListToggleItemClicked: 'farm_config_processors_price_list_toggle_item_clicked',

  farmConfigAutoFeedersEditClicked: 'farm_config_auto_feeders_edit_clicked',
  farmConfigAutoFeedersSaveClicked: 'farm_config_auto_feeders_save_clicked',
  farmConfigAutoFeedersCancelClicked: 'farm_config_auto_feeders_cancel_clicked',
  farmConfigAutoFeedersAddItemClicked: 'farm_config_auto_feeders_add_item_clicked',
  farmConfigAutoFeedersRemoveItemClicked: 'farm_config_auto_feeders_remove_item_clicked',
  farmConfigAeratorsEditClicked: 'farm_config_auto_feeders_edit_clicked',
  farmConfigAeratorsSaveClicked: 'farm_config_auto_feeders_save_clicked',
  farmConfigAeratorsCancelClicked: 'farm_config_auto_feeders_cancel_clicked',
  farmConfigAeratorsAddItemClicked: 'farm_config_auto_feeders_add_item_clicked',
  farmConfigAeratorsRemoveItemClicked: 'farm_config_auto_feeders_remove_item_clicked',

  farmConfigFeedTablesEditClicked: 'farm_config_feed_tables_edit_clicked',
  farmConfigFeedTablesSaveClicked: 'farm_config_feed_tables_save_clicked',
  farmConfigFeedTablesCancelClicked: 'farm_config_feed_tables_cancel_clicked',
  farmConfigFeedTablesAddItemClicked: 'farm_config_feed_tables_add_item_clicked',
  farmConfigFeedTablesEditItemClicked: 'farm_config_feed_tables_edit_item_clicked',
  farmConfigFeedTablesRemoveItemClicked: 'farm_config_feed_tables_remove_item_clicked',
  farmConfigFeedTablesToggleItemClicked: 'farm_config_feed_tables_toggle_item_clicked',
  farmConfigFeedTypesEditClicked: 'farm_config_feed_types_edit_clicked',
  farmConfigFeedTypesSaveClicked: 'farm_config_feed_types_save_clicked',
  farmConfigFeedTypesCancelClicked: 'farm_config_feed_types_cancel_clicked',
  farmConfigDailyParamsEditClicked: 'farm_config_daily_params_edit_clicked',
  farmConfigDailyParamsCancelClicked: 'farm_config_daily_params_cancel_clicked',
  farmConfigDailyParamsSaveClicked: 'farm_config_daily_params_save_clicked',
  farmConfigDailyParamsRemoveItemSave: 'farm_config_daily_params_remove_item_save',
  farmConfigDailyParamsRemoveItemCancel: 'farm_config_daily_params_remove_item_cancel',
  farmConfigDailyParamsAddItemClicked: 'farm_config_daily_params_add_item_clicked',
  farmConfigFeedTypesAddItemClicked: 'farm_config_feed_types_add_item_clicked',
  farmConfigFeedTypesEditItemClicked: 'farm_config_feed_types_edit_item_clicked',
  farmConfigFeedTypesRemoveItemClicked: 'farm_config_feed_types_remove_item_clicked',
  farmConfigFeedTypesToggleItemClicked: 'farm_config_feed_types_toggle_item_clicked',

  farmConfigProjectionsCancelClicked: 'farm_config_projections_cancel_clicked',
  farmConfigProjectionsSaveClicked: 'farm_config_projections_save_clicked',
  farmConfigProjectionsEditClicked: 'farm_config_projections_edit_clicked',
  // end farm config

  // pond overview
  pondOverviewProductionChartVariableOneChanged: 'pond_overview_production_chart_variable_one_changed',
  pondOverviewProductionChartVariableTwoChanged: 'pond_overview_production_chart_variable_two_changed',
  pondOverviewProductionDailyParamVariableOneChanged: 'pond_overview_production_daily_param_variable_one_changed',
  pondOverviewProductionDailyParamVariableTwoChanged: 'pond_overview_production_daily_param_variable_two_changed',
  pondOverviewRunSimulationClicked: 'pond_overview_run_simulation_clicked',
  pondOverviewRemoveSimulationClicked: 'pond_overview_remove_simulation_clicked',
  pondOverviewProjectionChartSelectVariable1Clicked: 'pond_overview_projection_chart_select_variable1_clicked',
  pondOverviewProjectionChartSelectVariable2Clicked: 'pond_overview_projection_chart_select_variable2_clicked',
  pondOverviewTargetEditClicked: 'pond_overview_target_edit_clicked',
  pondOverviewTargetSaveClicked: 'pond_overview_target_save_clicked',
  pondOverviewTargetCancelClicked: 'pond_overview_target_cancel_clicked',
  pondOverviewPondSetupEditClicked: 'pond_overview_pond_setup_edit_clicked',
  pondOverviewPondSetupSaveClicked: 'pond_overview_pond_setup_save_clicked',
  pondOverviewPondSetupCancelClicked: 'pond_overview_pond_setup_cancel_clicked',
  pondOverviewFeedEditClicked: 'pond_overview_feed_edit_clicked',
  pondOverviewFeedSaveClicked: 'pond_overview_feed_save_clicked',
  pondOverviewFeedCancelClicked: 'pond_overview_feed_cancel_clicked',
  pondOverviewProjectionEditClicked: 'pond_overview_projection_edit_clicked',
  pondOverviewProjectionSaveClicked: 'pond_overview_projection_save_clicked',
  pondOverviewProjectionCancelClicked: 'pond_overview_projection_cancel_clicked',
  pondOverviewPondStockingEditClicked: 'pond_overview_pond_stocking_edit_clicked',
  pondOverviewPartialHarvestRecordClicked: 'pond_overview_partial_harvest_record_clicked',
  pondOverviewPartialHarvestEditClicked: 'pond_overview_partial_harvest_edit_clicked',
  pondOverviewPartialHarvestDeleteClicked: 'pond_overview_partial_harvest_delete_clicked',
  pondOverviewRecordHarvestSaveClicked: 'pond_overview_record_harvest_save_clicked',
  pondOverviewRecordHarvestCancelClicked: 'pond_overview_record_harvest_cancel_clicked',
  pondOverviewEditHarvestPlanSaveClicked: 'pond_overview_edit_harvest_plan_save_clicked',
  pondOverviewEditHarvestPlanCancelClicked: 'pond_overview_edit_harvest_plan_cancel_clicked',
  pondOverviewPartialHarvestPlanSaveClicked: 'pond_overview_partial_harvest_plan_save_clicked',
  pondOverviewPartialHarvestPlanCancelClicked: 'pond_overview_partial_harvest_plan_cancel_clicked',
  pondOverviewUpdateDailyParamsClicked: 'pond_overview_update_daily_params_clicked',
  pondOverviewCancelDailyParamsClicked: 'pond_overview_cancel_daily_params_clicked',
  // end pond overview

  // farm group view (Dashboard & Group Performance)
  farmGroupSummaryClicked: 'farm_group_summary_clicked',
  farmGroupViewChangeVariablesClicked: 'farm_group_view_change_variables_clicked',
  farmGroupViewChangeVariablesSaveClicked: 'farm_group_view_change_variables_save_clicked',
  farmGroupViewChangeVariablesCancelClicked: 'farm_group_view_change_variables_cancel_clicked',
  profitProjectionToViewClicked: 'profit_projection_to_view_clicked',
  profitProjectionToViewSummaryClicked: 'profit_projection_to_view_summary_clicked',
  profitProjectionToViewPondOverviewClicked: 'profit_projection_to_view_pond_overview_clicked',
  farmGroupViewIndicatingStatusClicked: 'farm_group_view_indicating_status_clicked',
  farmGroupViewPondFarmBookClicked: 'farm_group_view_farm_book_clicked',
  farmGroupViewPondFlagClicked: 'farm_group_view_pond_flag_clicked',
  farmSummaryViewPondFlagClicked: 'farm_summary_view_pond_flag_clicked',
  groupPerformanceVariableClicked: 'group_performance_variable_clicked',
  groupPerformancePeriodSelected: 'group_performance_period_selected',
  // end farm group view (Dashboard & Group Performance)

  // pond list view ( farm overview)
  pondListViewUpdateFarmDataClicked: 'pond_list_view_update_farm_data_clicked',
  pondListViewCustomizeViewSaveClicked: 'pond_list_view_customize_view_save_clicked',
  pondListViewCustomizeViewCancelClicked: 'pond_list_view_customize_view_cancel_clicked',
  pondListViewFilterClicked: 'pond_list_view_filter_clicked',
  pondListViewFilterApplyClicked: 'pond_list_view_filter_apply_clicked',
  pondListViewFilterCancelClicked: 'pond_list_view_filter_cancelled_clicked',
  pondListViewChangeVariablesClicked: 'pond_list_view_change_variables_clicked',
  pondListViewChangeVariablesSaveClicked: 'pond_list_view_change_variables_save_clicked',
  pondListViewChangeVariablesCancelClicked: 'pond_list_view_change_variables_cancel_clicked',
  pondListViewNewPondClicked: 'pond_list_view_new_pond_clicked',
  pondListViewSortByClicked: 'pond_list_view_sort_by_clicked',
  pondListViewIndicatingStatusClicked: 'pond_list_view_indicating_status_clicked',
  pondListViewInvalidBiomassOrSurvivalEnterClicked: 'pond_list_view_invalid_biomass_or_survival_enter_clicked',
  pondListViewEditSurvivalClicked: 'pond_list_view_edit_survival_clicked',
  pondListViewFarmBookClicked: 'farm_group_view_farm_book_clicked',
  pondListViewFlagClicked: 'farm_group_view_pond_flag_clicked',
  pondListViewStockPondClicked: 'pond_list_view_stock_pond_clicked',
  pondListViewSortClicked: 'pond_list_view_sort_clicked',
  pondListViewExpandClicked: 'pond_list_view_expand_clicked',
  // end pond list view

  nurseryListViewStockNurseryClicked: 'nursery_list_view_stock_nursery_clicked',
  nurseryListViewNewNurseryClicked: 'nursery_list_view_new_nursery_clicked',

  //Farm Summary
  farmSummaryTabClicked: 'farm_summary_tab_clicked',
  farmSummaryMeatballClicked: 'farm_summary_meatball_clicked',
  farmSummaryMeatballItemClicked: 'farm_summary_meatball_item_clicked'
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function saveFarmProperties(farmProperties: any): void {
  try {
    localStorage.setItem('currentFarm', JSON.stringify(farmProperties));
  } catch (error) {
    console.error('Failed to save farm properties to localStorage:', error);
  }
}

export function getFarmProperties() {
  try {
    const storedData = localStorage.getItem('currentFarm');
    return storedData ? JSON.parse(storedData) : {};
  } catch (error) {
    console.error('Failed to retrieve farm properties from localStorage:', error);
    return {};
  }
}

Router.events.on('routeChangeComplete', (url: string) => {
  const farmProperties = unSlugfiyUrl(url) ?? {};
  const updatedProp = Object.assign(farmProperties, getFarmProperties());

  analyticsTrackPageView(updatedProp);
});

export async function analyticsIdentify({ userId, traits }: { userId: string; traits?: JSONValue }): Promise<void> {
  if (window?.analytics) {
    const { any } = isMobile();

    window?.analytics.identify(
      userId,
      Object.assign(traits ?? {}, {
        source: { mobile: any, web: !any }
      })
    );
  }
}

export async function analyticsGroup({ farmId, traits }: { farmId: string; traits?: JSONValue }): Promise<void> {
  if (window?.analytics) {
    const { any } = isMobile();

    window?.analytics.group(
      farmId,
      Object.assign(traits ?? {}, {
        source: { mobile: any, web: !any }
      })
    );
  }
}

export async function analyticsTrack(event: string, traits?: JSONValue): Promise<void> {
  if (isBrowser && window?.analytics) {
    const { any } = isMobile();

    window?.analytics.track(
      event,
      Object.assign(traits ?? {}, {
        source: { mobile: any, web: !any }
      })
    );
  }
}

export async function analyticsTrackPageView(properties?: JSONValue): Promise<void> {
  if (isBrowser && window?.analytics) {
    const { any } = isMobile();

    window?.analytics.page(
      Object.assign(properties ?? {}, {
        source: { mobile: any, web: !any }
      })
    );
  }
}
