import millify from 'millify';
import { poundPerKg } from '@utils/constants';

// Removes trailing decimals e.g., 34.333333333333 will be changed to 34.33
// 34.89999999999 will be changed to 34.89
export function normalizeAmount(amount: number): number {
  const formattedNum: number = Number(parseFloat(`${amount * 100}`).toPrecision(12)) / 100 || 0;
  // Remove any trailing decimals after `.\d{2}`
  return Number(`${formattedNum}`.replace(/(\.\d{2})\d+/, '$1'));
}

export function convertKgToPounds(kg: number = 0): number {
  return kg * 2.20462;
}

// Function to convert pounds to kilograms
export function convertPoundsToKg(lbs: number = 0): number {
  return lbs / 2.20462;
}

export type FormatNumberOptions = {
  compact?: boolean;
  shouldRound?: boolean;
  lang?: string;
  fractionDigits: number;
  asNumber?: boolean;
  shouldAddZeros?: boolean;
  isCurrency?: boolean;
  currency?: string;
  isPercentage?: boolean;
  notation?: Intl.NumberFormatOptions['notation'];
  signDisplay?: Intl.NumberFormatOptions['signDisplay'];
};

export function formatNumber(num: number, options: FormatNumberOptions) {
  const {
    notation,
    lang = 'en',
    compact = false,
    isPercentage = false,
    fractionDigits = 0,
    asNumber = false,
    shouldRound = true,
    shouldAddZeros = true,
    isCurrency = false,
    currency = 'USD',
    signDisplay = 'auto'
  } = options;

  if (!isNumber(num)) return num;

  if (compact && !isPercentage && !isCurrency) return millify(num, { locales: lang, precision: fractionDigits });

  let result: string | number = num;

  if (!shouldRound) {
    const dec = Math.pow(10, fractionDigits);
    result = Math.trunc(num * dec) / dec;
  }

  if (shouldRound && !isPercentage) result = num.toFixed(fractionDigits);

  if (asNumber) return Number(+result * (isPercentage ? 100 : 1));

  const isCompactNotation = notation === 'compact' || compact;
  const localeStringOptions: Intl.NumberFormatOptions = {
    signDisplay,
    notation: isCompactNotation ? 'compact' : notation
  };

  if (shouldAddZeros) {
    localeStringOptions.minimumFractionDigits = fractionDigits;
    localeStringOptions.maximumFractionDigits = fractionDigits;
  }

  if (isCurrency) {
    Object.assign(localeStringOptions, {
      style: 'currency',
      currency,
      minimumFractionDigits: shouldAddZeros ? fractionDigits : 0,
      maximumFractionDigits: fractionDigits
    });
  }

  if (isPercentage) {
    Object.assign(localeStringOptions, {
      style: 'percent'
    });
  }

  let formattedNumber = Number(result).toLocaleString(isCurrency ? 'en' : lang, localeStringOptions);

  // Convert 1K to 1k
  if (isCompactNotation) formattedNumber = formattedNumber.toLowerCase();

  return formattedNumber;
}

export function isNumber(value: number | string): boolean {
  if (value === '') return false;

  if (typeof value === 'number') {
    return !Number.isNaN(value) && Number.isFinite(value);
  }

  if (typeof value === 'string') {
    const cleanValue = value.replace(/[$,%]/g, '');
    const num = Number(cleanValue);
    return !Number.isNaN(num) && Number.isFinite(num);
  }

  return false;
}

export function getPercentageValue(
  value: number,
  options?: { fallBackValue?: number; fractionDigits?: number }
): number {
  const { fallBackValue, fractionDigits = 2 } = options ?? {};
  if (!isNumber(value)) return fallBackValue ?? value;
  if (value > 1) return value;
  return Number((value * 100).toFixed(fractionDigits));
}

export function getPercentageDivided(value: number) {
  if (!isNumber(value)) return value;
  return Number(value / 100);
}

export function isEqualNumber(params: { num1: number; num2: number; fractionDigits?: number }) {
  const { num1, num2, fractionDigits = 0 } = params;

  const aFormatted = formatNumber(num1, { fractionDigits });
  const bFormatted = formatNumber(num2, { fractionDigits });

  return aFormatted === bFormatted;
}

export function convertUnitByDivision(value: number, unit: 'lbs' | 'kg') {
  if (!isNumber(value)) return value;

  if (unit === 'lbs') return value;

  if (unit === 'kg') return value / poundPerKg;

  return value;
}

export function convertUnitByMultiplication(value: number, unit: 'lbs' | 'kg') {
  if (!isNumber(value)) return value;

  if (unit === 'lbs') return value;

  if (unit === 'kg') return value * poundPerKg;

  return value;
}
