import { isBrowser } from '@utils/browser';
import { parseCookies } from '@utils/cookie';
import router, { Router } from 'next/router';

export function handleRedirect(returnUrl?: string) {
  const currentUrl = isBrowser ? window.location.pathname : undefined;

  if (!currentUrl) return;

  if (returnUrl) {
    setTimeout(() => {
      goToUrl({ route: returnUrl, replace: true });
    }, 100);
  } else {
    setTimeout(() => {
      goToUrl({ route: '/', params: { _li: 1 } });
    }, 100);
  }
}

/**
 * Replaces https://something///url/item  ——> https://something/url/item
 * @param url string
 */
export function normalizeUrlSlashes(url: string) {
  return url.replace(/([^:])(\/{2,})/g, '$1/');
}

export interface IUrlQuery {
  [key: string]: string | string[] | number | undefined;
}

export type goToUrlType = {
  route: string;
  params?: IUrlQuery;
  opt?: { scroll?: boolean; shallow?: boolean; locale?: string };
  replace?: boolean;
  keepParams?: boolean;
  disableAutoScroll?: boolean;
};

export function goToUrl(props: goToUrlType): void {
  const { route = '', params = {}, opt = {}, keepParams, replace = false } = props;
  const { 'x-language': lang } = parseCookies();

  // else sdks side redirect
  if (!router) {
    return;
  }

  // else sdks side redirect
  if (!router.router) {
    return;
  }
  const finalParams = keepParams ? { ...router.router.query, ...params } : params;
  const url =
    Object.values(params).filter((e) => e).length > 0 ? route + '?' + objectToQueryString(finalParams) : route;

  if (replace) {
    router.router.replace(url, undefined, { locale: lang, ...opt }).then();
  } else {
    router.router.push(url, undefined, { locale: lang, ...opt }).then();
  }
}

export function getDomainTld() {
  return window.location.hostname.split('.').slice(-2).join('.');
}

export function objectToQueryString(obj: IUrlQuery): string {
  return Object.entries(obj)
    .filter((e) => e[1])
    .map((i) => [i[0], encodeURIComponent(i[1] as string)].join('='))
    .join('&');
}

Router.events.on('routeChangeComplete', () => {
  window.scroll({
    top: 0,
    left: 0,
    behavior: 'smooth'
  });
});
