import { Box, Flex, FlexProps, Heading, Image, List, Tabs, Text, useDisclosure } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { BackArrowIcon } from '@icons/back-arrow-icon';
import { CloseIcon } from '@icons/close-icon';
import { getTrans } from '@i18n/get-trans';
import { getDistributionTipStrings } from '@screens/farm/helpers/distribution-tips';
import { actionsName } from '@utils/segment';
import { Dispatch, ReactNode, SetStateAction, useRef, useState } from 'react';
import { DrawerBody, DrawerContent, DrawerRoot } from '@components/ui/drawer';

type DrawerPageVariant = 'home' | 'tips' | 'calculated' | 'high';
type DrawerPageProps = {
  setPageVariant: Dispatch<SetStateAction<DrawerPageVariant>>;
};

interface CVLearningDrawerProps extends FlexProps {
  children: ReactNode;
}

export function CVLearningDrawer(props: CVLearningDrawerProps) {
  const { children, ...rest } = props;
  const { open, onOpen, onClose } = useDisclosure();
  const [pageVariant, setPageVariant] = useState<DrawerPageVariant>('home');

  const handleClose = () => {
    setPageVariant('home');
    onClose();
  };
  const ref = useRef<HTMLDivElement>(null);
  return (
    <Box ref={ref}>
      <DrawerRoot
        placement='end'
        size='xl'
        open={open}
        onOpenChange={(e) => (e.open ? onOpen() : handleClose())}
        restoreFocus={false}
      >
        <Flex
          cursor='pointer'
          onClick={(e) => {
            e.stopPropagation();
            onOpen();
          }}
          {...rest}
        >
          {children}
        </Flex>

        <DrawerContent closeTrigger={false} borderTopStartRadius='12px' ps='xl' portalRef={ref}>
          {pageVariant === 'home' && (
            <CloseIcon position='absolute' top='30px' insetStart='24px' cursor='pointer' onClick={handleClose} />
          )}
          {pageVariant !== 'home' && (
            <BackArrowIcon
              position='absolute'
              top='30px'
              insetStart='24px'
              cursor='pointer'
              onClick={() => setPageVariant('home')}
            />
          )}
          <DrawerBody pt='lg'>
            {pageVariant === 'home' && <DrawerHomePage setPageVariant={setPageVariant} />}
            {pageVariant === 'tips' && <DrawerTipsPage />}
            {pageVariant === 'calculated' && <DrawerCalculatedPage />}
            {pageVariant === 'high' && <DrawerHighPage />}
          </DrawerBody>
        </DrawerContent>
      </DrawerRoot>
    </Box>
  );
}

function DrawerHomePage(props: DrawerPageProps) {
  const { setPageVariant } = props;
  const { trans } = getTrans();
  return (
    <>
      <Heading size='heavy300' mb='md'>
        {trans('t_what_is_cv_learning')}
      </Heading>
      <Text mb='md'>{trans('t_cv_learning')}</Text>
      <Text mb='lg'>{trans('t_cv_description_high')}</Text>
      <Text mb='lg'>{trans('t_cv_description_low')}</Text>
      <BaseButton
        onClick={() => setPageVariant('calculated')}
        variant='link'
        color='black'
        textDecoration='underline'
        display='block'
        analyticsId={actionsName.howCvCalculatedClicked}
        analyticsData={{ placement: 'cv-learning-drawer' }}
      >
        {trans('t_cv_how_cv_calculated')}
      </BaseButton>
      <BaseButton
        mb='xl'
        onClick={() => setPageVariant('high')}
        variant='link'
        color='black'
        textDecoration='underline'
        analyticsId={actionsName.howCvDeterminedClicked}
        analyticsData={{ placement: 'cv-learning-drawer' }}
      >
        {trans('t_cv_how_determined')}
      </BaseButton>
      <Heading size='heavy300' mb='md'>
        {trans('t_cv_fixing_distribution')}
      </Heading>
      <Text mb='md'>{trans('t_cv_xpertsea_alert')}</Text>
      <Text>{trans('t_then_you_can')}</Text>
      <List.Root textStyle='paragraph.200.light' ps='md' mb='md'>
        <List.Item>{trans('t_cv_review_pond_histogram')}</List.Item>
        <List.Item>{trans('t_cv_consider_following_our_tips')}</List.Item>
      </List.Root>
      <BaseButton
        mb='lg'
        onClick={() => setPageVariant('tips')}
        analyticsId={actionsName.cvTipsToImproveClicked}
        analyticsData={{ placement: 'cv-learning-drawer' }}
      >
        {trans('t_cv_tips_to_improve')}
      </BaseButton>
    </>
  );
}

function DrawerTipsPage() {
  const { trans } = getTrans();
  const tips = getDistributionTipStrings();

  return (
    <>
      <Heading size='heavy300' mb='md'>
        {trans('t_cv_tips_to_improve')}
      </Heading>
      <Text mb='md'>{trans('t_cv_select_the_average_body_weight')}</Text>
      <Tabs.Root defaultValue='0-5' variant='enclosed'>
        <Tabs.List>
          <Tabs.Trigger
            value='0-5'
            _selected={{ bgColor: 'brand.100', color: 'brand.700' }}
          >{`0-5 ${trans('t_gram_g')}`}</Tabs.Trigger>
          <Tabs.Trigger
            value='5-10'
            _selected={{ bgColor: 'brand.100', color: 'brand.700' }}
          >{`5-10 ${trans('t_gram_g')}`}</Tabs.Trigger>
          <Tabs.Trigger
            value='10-15'
            _selected={{ bgColor: 'brand.100', color: 'brand.700' }}
          >{`10-15 ${trans('t_gram_g')}`}</Tabs.Trigger>
          <Tabs.Trigger
            value='15+'
            _selected={{ bgColor: 'brand.100', color: 'brand.700' }}
          >{`15 ${trans('t_gram_g')}+`}</Tabs.Trigger>
        </Tabs.List>

        {tips.map((tip) => (
          <Tabs.Content value={tip.value} key={tip.grams} px={0}>
            <Text mb='lg'>
              {tip.title}{' '}
              <Text as='span' fontWeight='bold'>
                {tip.grams}{' '}
              </Text>
              {tip.description}
            </Text>
            <Heading size='heavy300' mb='md'>
              {trans('t_consider_the_following')}
            </Heading>
            <List.Root textStyle='paragraph.200.light' ps='md' mb='md'>
              {tip.steps.map((step) => (
                <List.Item key={step} mb='md'>
                  {step}
                </List.Item>
              ))}
            </List.Root>
          </Tabs.Content>
        ))}
      </Tabs.Root>
    </>
  );
}

function DrawerCalculatedPage() {
  const { trans } = getTrans();
  const examples = [
    {
      title: trans('t_cv_low'),
      subTitle: trans('t_cv_sd_low'),
      imgUrl: '/assets/cv-low.png'
    },
    {
      title: trans('t_cv_medium'),
      subTitle: trans('t_cv_sd_medium'),
      imgUrl: '/assets/cv-med.png'
    },
    {
      title: trans('t_cv_high'),
      subTitle: trans('t_cv_sd_high'),
      imgUrl: '/assets/cv-high.png'
    }
  ];
  return (
    <>
      <Heading size='heavy300' mb='md'>
        {trans('t_cv_how_cv_calculated')}
      </Heading>
      <Text mb='md'>{trans('t_cv_calculation_description')}</Text>
      <Text mb='md'>{trans('t_cv_calculation_description_long')}</Text>
      <Text mb='md'>{trans('t_cv_calculation_description_short')}</Text>
      <Heading size='heavy300' mb='md'>
        {trans('t_cv_calculation_examples')}
      </Heading>
      {examples.map((example) => (
        <Flex key={example.title} mb='lg'>
          <Box me='sm' w='30%'>
            <Text>{example.title}</Text>
            <Text fontSize='md'>{example.subTitle}</Text>
          </Box>
          <Image w='217px' src={example.imgUrl} alt={`${example.title}-diagram`} />
        </Flex>
      ))}
    </>
  );
}

function DrawerHighPage() {
  const { trans } = getTrans();

  return (
    <>
      <Heading size='heavy300' mb='md'>
        {trans('t_cv_how_determined')}
      </Heading>
      <Text mb='md'>{trans('t_cv_high_description')}</Text>
      <Text mb='md'>{trans('t_cv_high_description_long')}</Text>
      <List.Root textStyle='paragraph.200.light' ps='md' mb='md'>
        <List.Item mb='md'>
          <Text as='span' fontWeight='bold'>
            {trans('t_cv_high_yellow')}{' '}
          </Text>{' '}
          {trans('t_cv_high_yellow_desc')}
        </List.Item>
        <List.Item>
          <Text as='span' fontWeight='bold'>
            {trans('t_cv_very_high_red')}{' '}
          </Text>{' '}
          {trans('t_cv_very_high_red_desc')}
        </List.Item>
      </List.Root>

      <Image src='/assets/high-diagram.png' alt={`high-cv-diagram`} />
    </>
  );
}
