import { Text } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { isInvalidABW } from '@screens/farm/helpers/abw';
import { formatNumber } from '@utils/number';
import { Population } from '@xpertsea/module-farm-sdk';
import { ReactNode, useState } from 'react';
import { PartialDeep } from 'type-fest';
import { getPondState } from '@screens/pond/helpers/get-pond-state';
import { DateTime } from 'luxon';
import { HoverCardArrow, HoverCardContent, HoverCardRoot, HoverCardTrigger } from '@components/ui/hover-card';

export function ABWInvalidDiv(props: { population?: PartialDeep<Population>; children?: ReactNode; weight?: number }) {
  const { trans } = getTrans();
  const { population, children, weight } = props;
  const { seedingAverageWeight, lastMonitoringMlResult, stockedAt, stockedAtTimezone, metadata } = population ?? {};
  const lang = useAppSelector((state) => state.app?.lang);
  const [open, setOpen] = useState(false);

  const manuallyMonitored = metadata?.manuallyMonitored;

  if (!seedingAverageWeight && !lastMonitoringMlResult?.averageWeight && !weight) {
    return <Text data-cy='pond-average-weight'>-</Text>;
  }

  const { hasMonitoring } = getPondState({ population });

  const averageWeight = weight ?? lastMonitoringMlResult?.averageWeight;
  const { isInvalid, abwLabel } = isInvalidABW(averageWeight);

  if (!isInvalid || manuallyMonitored) {
    return children;
  }

  const stockedAtLuxon = stockedAt ? DateTime.fromISO(stockedAt, { zone: stockedAtTimezone }) : null;
  return (
    <HoverCardRoot
      open={open}
      onOpenChange={(value) => setOpen(value.open)}
      positioning={{ placement: 'bottom' }}
      lazyMount
      unmountOnExit
    >
      <HoverCardTrigger asChild>
        <Text
          w='max-content'
          pos='relative'
          data-cy='pond-average-weight'
          onMouseOver={() => {
            setOpen(true);
          }}
          onMouseLeave={() => {
            setOpen(false);
          }}
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          {hasMonitoring && <>{abwLabel ?? formatNumber(seedingAverageWeight, { fractionDigits: 2, lang })}</>}
          {!hasMonitoring && (
            <>
              {trans('t_weight_stocked_on_date', {
                date: stockedAtLuxon?.toFormat('dd MMM', { locale: lang }),
                weight: formatNumber(seedingAverageWeight, { lang, fractionDigits: 2 })
              })}
            </>
          )}
        </Text>
      </HoverCardTrigger>
      <HoverCardContent overflow='initial' maxW='280px' py='sm-alt' px='md'>
        <HoverCardArrow />

        <Text whiteSpace='initial'>{trans('t_invalid_weight')}</Text>
      </HoverCardContent>
    </HoverCardRoot>
  );
}
