import { Box, BoxProps, Flex, Heading, Input, List, Text, useDisclosure } from '@chakra-ui/react';
import { BaseSpinner } from '@components/base/base-spinner';
import { useAnalytics } from '@hooks/use-analytics';
import { getTrans } from '@i18n/get-trans';
import { setCurrentFarmAction } from '@redux/farm';
import { useAppDispatch, useAppSelector } from '@redux/hooks';
import { actionsName } from '@utils/segment';
import { slugify } from '@utils/string';
import { goToUrl, goToUrlType } from '@utils/url';
import { useRouter } from 'next/router';
import { useMemo, useRef, useState } from 'react';
import { FarmsDataType } from '@redux/farm/set-farms-data';
import { BaseButton } from '@components/base/base-button';
import { ChevronDownFilled } from '@icons/chevron-down/chevron-down-filled';
import { ChevronUpFilled } from '@icons/chevron-up/chevron-up-filled';
import { SearchDuoIcon } from '@icons/search/search-duo-icon';
import { PopoverBody, PopoverContent, PopoverRoot, PopoverTrigger } from '@components/ui/popover';
import { InputGroup } from '@components/ui/input-group';
import { ParsedUrlQuery } from 'querystring';
import { CloseFilled } from '@icons/close/close-filled';

export function FarmSelector(props: BoxProps & { placeholder?: string; extraQuery?: ParsedUrlQuery }) {
  const { placeholder, extraQuery = {}, ...rest } = props;
  const { trans } = getTrans();
  const { route, query } = useRouter();
  const { trackAction } = useAnalytics();
  const dispatch = useAppDispatch();

  const [searchFarm, setSearchFarm] = useState<string>();
  const highlightedFarmRef = useRef<HTMLLIElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [highlightedFarm, setHighlightedFarm] = useState<number>(0);
  const [isGoingDown, setIsGoingDown] = useState<boolean>(false);

  const farms = useAppSelector((state) => state?.farm?.farmsData);
  const currentFarm = useAppSelector((state) => state?.farm?.currentFarm);
  const isFinishedOnceFarmsData = useAppSelector((state) => state?.farm?.isFinishedOnceFarmsData);

  const { onOpen, onClose, open } = useDisclosure();

  const filteredFarms = useMemo(() => {
    if (!searchFarm) return farms;

    return farms?.filter((farm) => farm.name?.toLowerCase().includes(searchFarm.toLowerCase()));
  }, [searchFarm, farms]);

  const isSearchFieldHidden = isFinishedOnceFarmsData && farms?.length < 6;

  const onFarmClick = (farm: FarmsDataType) => {
    const farmSlug = slugify(`${farm.eid}-${farm.name}`);
    const isFarmRoute = route.includes('/farm/[farmEid]');
    const isPondRoute = route.includes('/pond/[pondEid]');

    if (!isFarmRoute && route !== '/users') {
      goToUrl({
        route: '/farm/[farmEid]',
        params: { farmEid: farmSlug }
      });
      return;
    }

    dispatch(
      setCurrentFarmAction({
        currentFarm: farm,
        currentFarmId: farm?._id,
        currentFarmEid: farm?.eid,
        currentFarmName: farm?.name
      })
    );

    const destination: goToUrlType = isPondRoute
      ? {
          route: '/farm/[farmEid]',
          params: { farmEid: farmSlug }
        }
      : {
          route,
          params: { ...query, farmEid: farmSlug, page: undefined, ...extraQuery }
        };

    goToUrl(destination);

    onClose();
    setHighlightedFarm(0);
    setSearchFarm(undefined);
    trackAction(actionsName.farmSelected).then();
  };

  const highlightedFarmCounter = isGoingDown ? highlightedFarm + 1 : highlightedFarm - 1;

  const farmName = placeholder || currentFarm?.name || '...';

  const handleCancel = () => {
    onClose();
    setSearchFarm(undefined);
    setHighlightedFarm(0);
  };

  return (
    <Box {...rest} ref={containerRef}>
      <PopoverRoot
        open={open}
        onOpenChange={(e) => {
          e.open ? onOpen() : handleCancel();
        }}
        lazyMount
        unmountOnExit
        positioning={{ placement: 'bottom-start' }}
      >
        <PopoverTrigger asChild>
          <BaseButton
            _focus={{ outline: 'none' }}
            _active={{ backgroundColor: 'unset' }}
            variant='ghost'
            px={0}
            data-cy='farm-selector-btn'
            onClick={() => {
              trackAction(actionsName.switchFarmClicked).then();
            }}
          >
            <FarmName title={farmName} />{' '}
            {open ? (
              <ChevronUpFilled hasBackground={false} color='icon.gray' />
            ) : (
              <ChevronDownFilled hasBackground={false} color='icon.gray' />
            )}
          </BaseButton>
        </PopoverTrigger>

        <PopoverContent
          border='none'
          overflow='hidden'
          shadow='elevation.400'
          _focusVisible={{ boxShadow: 'none', outline: 'none' }}
          rounded='2xl'
        >
          <PopoverBody p={0}>
            {!isFinishedOnceFarmsData && (
              <Flex align='center' justify='center' gap='sm-alt' py='md'>
                <BaseSpinner size='sm' borderWidth={2} />
                <Text size='label200'>{trans('t_loading')}...</Text>
              </Flex>
            )}
            {isFinishedOnceFarmsData && (
              <>
                {!isSearchFieldHidden && (
                  <Box p='xs' position='sticky' top='0' backgroundColor='white' zIndex={9}>
                    <InputGroup
                      w='full'
                      startElement={<SearchDuoIcon pointerEvents='none' />}
                      endElement={
                        searchFarm && (
                          <CloseFilled hasBackground={false} cursor='pointer' onClick={() => setSearchFarm('')} />
                        )
                      }
                    >
                      <Input
                        type='text'
                        outline='none'
                        textStyle='label.200'
                        id='search-farm'
                        color='text.gray'
                        autoComplete='off'
                        value={searchFarm}
                        borderRadius='full'
                        placeholder={trans('t_search')}
                        _placeholder={{ color: 'text.gray.disabled' }}
                        onChange={(event) => {
                          setSearchFarm(event.target.value);
                          setHighlightedFarm(0);
                        }}
                        onKeyDown={(event) => {
                          if (event.key === 'Enter') {
                            onFarmClick(filteredFarms[highlightedFarm]);
                            return;
                          }

                          let highlightedFarmCopy = highlightedFarm;
                          if (event.key === 'ArrowUp' && highlightedFarmCopy > 0) {
                            setIsGoingDown(false);
                            highlightedFarmCopy = highlightedFarmCopy - 1;
                          } else if (event.key === 'ArrowDown' && highlightedFarmCopy < filteredFarms.length - 1) {
                            setIsGoingDown(true);
                            highlightedFarmCopy = highlightedFarmCopy + 1;
                          }

                          setHighlightedFarm(highlightedFarmCopy);
                          highlightedFarmRef?.current?.scrollIntoView({ block: 'nearest' });
                        }}
                      />
                    </InputGroup>
                  </Box>
                )}
                {!filteredFarms?.length && (
                  <Text size='label200' px='xs' pb='sm' pt='xs' textAlign='center'>
                    {trans('t_no_farms_found')}
                  </Text>
                )}
                {!!filteredFarms?.length && (
                  <List.Root maxHeight='35vh' overflow='scroll' px='xs' pb='xs' pt={isSearchFieldHidden ? 'xs' : 0}>
                    {filteredFarms.map((farm, index) => {
                      const isFirstItem = index === 0;
                      const isHighlighted = farm.eid === currentFarm?.eid || (index === highlightedFarm && searchFarm);

                      return (
                        <List.Item
                          borderRadius='lg-alt'
                          px='xs'
                          minH='40px'
                          maxH='40px'
                          mt={isFirstItem ? 0 : '3xs'}
                          display='flex'
                          alignItems='center'
                          key={farm.eid}
                          cursor='pointer'
                          justifyContent='space-between'
                          _hover={{ backgroundColor: 'bg.brandBlue.weakShade1' }}
                          ref={index === highlightedFarmCounter ? highlightedFarmRef : undefined}
                          {...(isHighlighted && { backgroundColor: 'bg.brandBlue.weakShade1' })}
                          onClick={() => onFarmClick(farm)}
                        >
                          <Text size='label200'>{farm.name}</Text>
                        </List.Item>
                      );
                    })}
                  </List.Root>
                )}
              </>
            )}
          </PopoverBody>
        </PopoverContent>
      </PopoverRoot>
    </Box>
  );
}

function FarmName({ title }: { title: string }) {
  return (
    <Heading
      as='span'
      w='fit-content'
      overflow='hidden'
      whiteSpace='nowrap'
      letterSpacing='unset'
      textOverflow='ellipsis'
      size='heavy300'
      maxW={{ base: '190px', sm: '280px', md: 'max-content' }}
    >
      {title}
    </Heading>
  );
}
