import { Flex, Skeleton } from '@chakra-ui/react';
import { FarmSelector } from '@screens/farm/components/farm-selector';
import { useAppSelector } from '@redux/hooks';
import { CycleComparisonDetails } from './cycle-comparison-details';

export function CycleComparisonScreen() {
  const { isFinishedOnceFarmsData } = useAppSelector((state) => state.farm);

  return (
    <Flex direction='column' py='md' gap='md-alt'>
      <Flex px='md' flexDir='column' justify='center'>
        <Skeleton loading={!isFinishedOnceFarmsData} w='200px'>
          <FarmSelector />
        </Skeleton>
      </Flex>

      <CycleComparisonDetails />
    </Flex>
  );
}
