import { Box, Flex, Input, Text, useDisclosure } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { getTrans } from '@i18n/get-trans';
import { SearchDuoIcon } from '@icons/search/search-duo-icon';
import { ChevronUpFilled } from '@icons/chevron-up/chevron-up-filled';
import { ChevronDownFilled } from '@icons/chevron-down/chevron-down-filled';
import { useEffect, useState } from 'react';
import { useListPopulationsApi } from '@screens/population/hooks/use-list-populations-api';
import { DateTime } from 'luxon';
import { CurrentFarmPonds } from '@redux/farm/set-current-farm-ponds';
import { BaseFormCheckbox } from '@components/form/base-form-checkbox';
import { isEqual } from 'lodash';
import { SelectedCyclesType } from '../hooks/use-selected-cycles';
import { PlusFilled } from '@icons/plus/plus-filled';
import { useAppSelector } from '@redux/hooks';
import { MenuButton, MenuContent, MenuItem, MenuRoot } from '@components/ui/menu';
import { InputGroup } from '@components/ui/input-group';
import { PopoverBody, PopoverContent, PopoverRoot, PopoverTrigger } from '@components/ui/popover';
import { CloseFilled } from '@icons/close/close-filled';

interface Props {
  ponds: CurrentFarmPonds;
  selectedCycles: SelectedCyclesType;
  onSelectCycles: (pondId: string, cycles: string[]) => void;
}

export function PondCyclesSelectors(props: Props) {
  const { ponds, selectedCycles, onSelectCycles } = props;

  const [pond, setPond] = useState('');

  const defaultValues = selectedCycles.reduce((acc, item) => {
    if (item.pondId === pond) acc.push(item.cycleId);
    return acc;
  }, []);

  useEffect(() => {
    setPond(ponds?.[0]?._id);
  }, [ponds]);

  return (
    <Flex gap='sm-alt' align='center'>
      <PondSelector ponds={ponds} pond={pond} onChange={(p: string) => setPond(p)} />

      <CyclesSelector key={pond} pondId={pond} onSelectCycles={onSelectCycles} defaultValues={defaultValues} />
    </Flex>
  );
}

type PondsSelectorProps = { ponds: CurrentFarmPonds; pond: string; onChange: (v: string) => void };

function PondSelector({ ponds, pond, onChange }: PondsSelectorProps) {
  const { trans } = getTrans();

  const [search, setSearch] = useState('');
  const [open, setOpen] = useState(false);

  if (!ponds.length)
    return (
      <BaseButton variant='ghost' bg='bg.gray.medium' textAlign='start' minW={['50px', '350px']} disabled>
        {trans('t_no_ponds_in_your_farm')}
      </BaseButton>
    );

  const filteredPonds = ponds?.filter((p) => p.name.includes(search)) ?? [];
  const selectedPond = ponds?.find((p) => p._id === pond);

  return (
    <MenuRoot onOpenChange={(e) => setOpen(e.open)} highlightedValue={selectedPond?._id}>
      <MenuButton
        variant='ghost'
        bg='bg.gray.medium'
        textAlign='start'
        outline='none !important'
        {...(open && { outline: '1.5px solid !important', outlineColor: 'button.link !important' })}
        _hover={{ outline: `${open ? '1.5px' : '1px'} solid !important`, outlineColor: 'button.link !important' }}
        minW={['50px', '350px']}
        justifyContent='space-between'
      >
        <Text
          textStyle='button.100'
          as='span'
          display='block'
          overflow='hidden'
          textOverflow='ellipsis'
          color={selectedPond?.name ? 'text.gray' : 'text.gray.disabled'}
        >
          {selectedPond?.name ? selectedPond?.name : trans('t_select_pond')}
        </Text>
        {open ? (
          <ChevronUpFilled hasBackground={false} me='-xs' color='icon.gray' boxSize={['20px', '24px']} />
        ) : (
          <ChevronDownFilled hasBackground={false} me='-xs' color='icon.gray' boxSize={['20px', '24px']} />
        )}
      </MenuButton>

      <MenuContent
        p='0'
        maxH='348px'
        minW={['50px', '350px']}
        border='none'
        rounded='2xl'
        overflow='auto'
        shadow='elevation.400'
      >
        <Box p='xs' position='sticky' top='0' backgroundColor='white' zIndex={9}>
          <InputGroup
            w='full'
            startElement={<SearchDuoIcon pointerEvents='none' />}
            endElement={search && <CloseFilled hasBackground={false} cursor='pointer' onClick={() => setSearch('')} />}
          >
            <Input
              value={search}
              borderRadius='full'
              fontSize='lg'
              outline='none'
              autoFocus
              autoComplete='off'
              placeholder={trans('t_search')}
              _placeholder={{ color: 'text.gray.disabled' }}
              onChange={(e) => {
                setSearch(e.target.value);
                e.stopPropagation();
                e.currentTarget.focus();
              }}
            />
          </InputGroup>
        </Box>

        <Flex direction='column' gap='2xs' mx='xs' mb='xs'>
          {filteredPonds.map((pond) => (
            <MenuItem key={pond._id} value={pond._id} autoFocus={false} onClick={() => onChange(pond._id)}>
              {pond.name}
            </MenuItem>
          ))}
        </Flex>
      </MenuContent>
    </MenuRoot>
  );
}

type CyclesSelectorProps = { pondId: string; defaultValues?: string[]; onSelectCycles: Props['onSelectCycles'] };

function CyclesSelector({ pondId, defaultValues, onSelectCycles }: CyclesSelectorProps) {
  const { trans } = getTrans();

  const [{ isLoading, data, isFinishedOnce }, listPopulations] = useListPopulationsApi();
  const { open, onOpen, onClose } = useDisclosure();
  const [selectedCycles, setSelectedCycles] = useState(defaultValues || []);

  const lang = useAppSelector((state) => state.app.lang);

  useEffect(() => {
    if (!pondId) return;
    listPopulations({
      params: {
        filter: { pondId: [pondId] },
        page: { size: 200 },
        sort: [{ field: 'stockedAt', order: 'desc' }]
      },
      fields: {
        populations: {
          _id: 1,
          pondId: 1,
          cycle: 1,
          createdAt: 1,
          stockedAt: 1,
          stockedAtTimezone: 1,
          lastMonitoringDate: 1,
          harvest: {
            date: 1,
            timezone: 1
          }
        }
      }
    });
  }, [pondId]);

  useEffect(() => {
    setSelectedCycles(defaultValues || []);
  }, [defaultValues]);

  const filteredCycles = data?.populations?.filter((ele) => ele.harvest?.date && ele.stockedAt) ?? [];

  const onCycleChange = (cycleId: string, value: boolean) => {
    if (!value) {
      setSelectedCycles((v) => v.filter((ele) => ele !== cycleId));
    } else {
      if (selectedCycles.includes(cycleId)) return;
      setSelectedCycles((v) => [...v, cycleId]);
    }
  };

  if (!isLoading && isFinishedOnce && !filteredCycles?.length) {
    return (
      <BaseButton variant='ghost' bg='bg.gray.medium' textAlign='start' minW={['50px', '350px']} disabled>
        {trans('t_no_cycles')}
      </BaseButton>
    );
  }

  const selectedCyclesNames: string[] = [];
  filteredCycles.forEach((ele) => {
    if (selectedCycles.includes(ele._id)) {
      selectedCyclesNames.push(`${trans('t_cycle')} ${ele.cycle}`);
    }
  });

  const hasChanged = !isEqual(selectedCycles, defaultValues || []);

  return (
    <>
      <PopoverRoot
        open={open}
        onOpenChange={(e) => {
          e.open ? onOpen() : onClose();
        }}
        lazyMount
        unmountOnExit
      >
        <PopoverTrigger asChild>
          <BaseButton
            variant='ghost'
            bg='bg.gray.medium'
            textAlign='start'
            outline='none !important'
            _hover={{ outline: `${open ? '1.5px' : '1px'} solid !important`, outlineColor: 'button.link !important' }}
            {...(open && { outline: '1.5px solid !important', outlineColor: 'button.link !important' })}
            minW={['50px', '350px']}
            justifyContent='space-between'
            loading={isLoading}
          >
            <Text
              textStyle='button.100'
              as='span'
              display='block'
              overflow='hidden'
              textOverflow='ellipsis'
              color={selectedCyclesNames.length ? 'text.gray' : 'text.gray.disabled'}
            >
              {selectedCyclesNames.length ? selectedCyclesNames.join(', ') : trans('t_select_cycle')}
            </Text>
            {open ? (
              <ChevronUpFilled hasBackground={false} me='-xs' color='icon.gray' boxSize={['20px', '24px']} />
            ) : (
              <ChevronDownFilled hasBackground={false} me='-xs' color='icon.gray' boxSize={['20px', '24px']} />
            )}
          </BaseButton>
        </PopoverTrigger>
        <PopoverContent rounded='2xl' minW={['50px', '350px']} shadow='elevation.400' border='none'>
          <PopoverBody p='xs'>
            {filteredCycles.map((population) => {
              const { _id, stockedAt, stockedAtTimezone, cycle, harvest } = population;
              const { date: harvestDate, timezone: harvestTimezone } = harvest ?? {};

              const stockedAtLuxon = DateTime.fromISO(stockedAt).setZone(stockedAtTimezone);
              const stockedAtFormattedDate = stockedAtLuxon.toFormat('MMM dd, yy', { locale: lang });
              const harvestAtFormattedDate = DateTime.fromISO(harvestDate)
                .setZone(harvestTimezone)
                .toFormat('MMM dd, yy', { locale: lang });
              const isChecked = selectedCycles?.includes(_id);

              return (
                <Flex
                  as='label'
                  key={_id}
                  flex={1}
                  justify='space-between'
                  px='xs'
                  h='40px'
                  cursor='pointer'
                  align='center'
                >
                  <Flex gap='xs' align='center'>
                    <BaseFormCheckbox
                      id={_id}
                      display='flex'
                      checked={isChecked}
                      onCheckedChange={(v) => onCycleChange(_id, v.checked as boolean)}
                      w='auto'
                    />
                    <Text pos='relative' size='label200'>
                      {trans('t_cycle')} {cycle}
                    </Text>
                  </Flex>

                  <Text size='label200'>
                    {stockedAtFormattedDate} - {harvestAtFormattedDate}
                  </Text>
                </Flex>
              );
            })}
          </PopoverBody>
        </PopoverContent>
      </PopoverRoot>

      <BaseButton
        size='sm'
        loading={isLoading}
        onClick={() => onSelectCycles(pondId, selectedCycles)}
        disabled={!hasChanged}
      >
        <PlusFilled
          w='16px'
          h='16px'
          me='2xs'
          {...(!hasChanged && { color: 'gray.400', bgColor: 'gray.100' })}
          {...(hasChanged && { color: 'graphBrandBlue', bgColor: 'white' })}
        />
        {trans('t_add')}
      </BaseButton>
    </>
  );
}
