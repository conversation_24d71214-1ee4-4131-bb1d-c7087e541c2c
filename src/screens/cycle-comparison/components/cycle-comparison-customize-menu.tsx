import { Box, IconButton, useDisclosure } from '@chakra-ui/react';
import { ReactNode } from 'react';
import {
  CycleComparisonVariableType,
  useGetCycleComparisonGroupViewVariables
} from '../utils/cycle-comparison-variables';
import { CustomizeViewModal } from '@components/common/customize-view-modal';
import { SettingsAdjustFilled } from '@icons/settings-adjust/settings-adjust-filled';

interface CycleComparisonCustomizeMenuProps {
  selectedVariables: CycleComparisonVariableType[];
  onSelectVariables: (v: CycleComparisonVariableType[]) => void;
}

export function CycleComparisonCustomizeMenu(props: CycleComparisonCustomizeMenuProps) {
  const { selectedVariables = [], onSelectVariables } = props;

  return (
    <CustomizeViewMenuModal defaultValues={selectedVariables} onSubmit={onSelectVariables}>
      <IconButton
        h='fit-content'
        w='fit-content'
        bgColor='white'
        aria-label='filter-button'
        _focus={{ outline: 'none' }}
      >
        <SettingsAdjustFilled w='20px' h='20px' transform='scale(1, -1)' />
      </IconButton>
    </CustomizeViewMenuModal>
  );
}

type CustomizeMenuModalProps = {
  children: ReactNode;
  defaultValues?: CycleComparisonVariableType[];
  onSubmit: CycleComparisonCustomizeMenuProps['onSelectVariables'];
};

function CustomizeViewMenuModal({ children, defaultValues, onSubmit }: CustomizeMenuModalProps) {
  const { open, onOpen, onClose } = useDisclosure();

  const { variablesHashmap, variableLabels } = useGetCycleComparisonGroupViewVariables();

  const handleOnSubmit = (v: CycleComparisonVariableType[]) => {
    onSubmit(v);
    onClose();
  };

  return (
    <CustomizeViewModal
      isOpen={open}
      onClose={onClose}
      onSubmit={(v) => handleOnSubmit(v as CycleComparisonVariableType[])}
      isLoading={false}
      pondVariables={defaultValues}
      variableLabels={variableLabels}
      pondViewVariables={variablesHashmap}
      statusIndicatorValue={'fcr'}
      hasNoLimit
    >
      <Box onClick={onOpen}>{children}</Box>
    </CustomizeViewModal>
  );
}
