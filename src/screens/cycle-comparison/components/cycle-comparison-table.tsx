import { Box, BoxProps, Flex, FlexProps, Grid, GridProps, Text } from '@chakra-ui/react';
import { PartialPageLoader } from '@components/loaders/partial-page-loader';
import { SectionLoader } from '@components/loaders/section-loader';
import { getTrans } from '@i18n/get-trans';
import { CurrentFarmPonds } from '@redux/farm/set-current-farm-ponds';
import { useAppSelector } from '@redux/hooks';
import { useListPopulationsApi } from '@screens/population/hooks/use-list-populations-api';
import { convertUnitByDivision, convertUnitByMultiplication, formatNumber, isNumber } from '@utils/number';
import keyBy from 'lodash/keyBy';
import isUndefined from 'lodash/isUndefined';
import { useEffect, useMemo, useState } from 'react';
import { CycleComparisonVariableType } from '../utils/cycle-comparison-variables';
import { DateTime } from 'luxon';
import { CloseFilled } from '@icons/close/close-filled';
import { harvestCommonGql } from '@screens/population/apis/common-queries';

interface Props {
  ponds: CurrentFarmPonds;
  cycles: string[];
  variables: CycleComparisonVariableType[];
  onDeleteCycle: (pondId: string, cycleId: string) => void;
}

export function CycleComparisonTable(props: Props) {
  const { ponds, cycles: cycleIds, variables, onDeleteCycle } = props;
  const { trans } = getTrans();

  const lang = useAppSelector((state) => state.app?.lang);

  const user = useAppSelector((state) => state.auth.user);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);
  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');

  const [columnIndexToHighlight, setColumnIndexToHighlight] = useState<number>();
  const [{ isLoading, data, isFinishedOnce }, listPopulations] = useListPopulationsApi();

  const existInSelectedVariables = (v: CycleComparisonVariableType) => variables.includes(v);

  useEffect(() => {
    if (!cycleIds?.length) return;

    listPopulations({
      params: {
        filter: { id: cycleIds },
        page: { size: 200 },
        sort: [{ field: 'stockedAt', order: 'desc' }]
      },
      fields: {
        populations: {
          _id: 1,
          pondId: 1,
          cycle: 1,
          stockingCostsMillar: 1,
          stockedAt: 1,
          stockedAtTimezone: 1,
          seedingQuantity: 1,
          seedingAverageWeight: 1,
          geneticName: 1,
          kampiMortality: 1,
          hatcheryName: 1,
          cycleInformation: { target: { cycleLength: 1 }, equipment: { numberOfAutoFeeders: 1 } },
          harvest: harvestCommonGql
        }
      }
    });
  }, [cycleIds]);

  const populations = data?.populations ?? [];
  type PopulationType = (typeof populations)[0];

  const sortedPopulations = cycleIds.reduce((acc, cycleId) => {
    const population: PopulationType = populations.find((ele) => ele._id === cycleId);

    if (population) acc.push(population);
    return acc;
  }, []);

  const pondsHashmap = keyBy(ponds, '_id');

  const columns = useMemo(() => {
    const columnsByVariable = {
      profitPerHaPerDay: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_profit_include_dry_days_ha_day')}
          </BodyCell>
        ),
        show: existInSelectedVariables('profitPerHaPerDay'),
        Cell: (row: PopulationType) => {
          const { hasEstimatedValues, profitPerHaPerDay } = row.harvest ?? {};
          const formattedValue = isNumber(profitPerHaPerDay)
            ? formatNumber(profitPerHaPerDay, { lang, fractionDigits: 2, isCurrency: true })
            : '-';
          return (
            <BodyCell>
              <EstimatedValueWrapper hasEstimatedValues={profitPerHaPerDay && hasEstimatedValues}>
                {formattedValue}
              </EstimatedValueWrapper>
            </BodyCell>
          );
        }
      },

      totalProfit: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_profit')}
          </BodyCell>
        ),
        show: existInSelectedVariables('totalProfit'),
        Cell: (row: PopulationType) => {
          const { hasEstimatedValues, totalProfit } = row.harvest ?? {};
          const formattedValue = isNumber(totalProfit)
            ? formatNumber(totalProfit, { lang, fractionDigits: 0, isCurrency: true })
            : '-';
          return (
            <BodyCell>
              <EstimatedValueWrapper hasEstimatedValues={totalProfit && hasEstimatedValues}>
                {formattedValue}
              </EstimatedValueWrapper>
            </BodyCell>
          );
        }
      },

      profitPoundHarvest: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_profit_per_unit_harvested', { unit: unitLabel })}
          </BodyCell>
        ),
        show: existInSelectedVariables('profitPoundHarvest'),
        Cell: (row: PopulationType) => {
          const { hasEstimatedValues, profitPoundHarvest } = row.harvest ?? {};
          const formattedValue = isNumber(profitPoundHarvest)
            ? formatNumber(convertUnitByMultiplication(profitPoundHarvest, unitsConfig?.biomass), {
                lang,
                fractionDigits: 2,
                isCurrency: true
              })
            : '-';
          return (
            <BodyCell>
              <EstimatedValueWrapper hasEstimatedValues={profitPoundHarvest && hasEstimatedValues}>
                {formattedValue}
              </EstimatedValueWrapper>
            </BodyCell>
          );
        }
      },

      costPerPound: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_cost_per_unit_processed', { unit: unitLabel })}
          </BodyCell>
        ),
        show: existInSelectedVariables('costPerPound'),
        Cell: (row: PopulationType) => {
          const { costPerPound } = row.harvest ?? {};
          const formattedValue = isNumber(costPerPound)
            ? formatNumber(convertUnitByMultiplication(costPerPound, unitsConfig?.biomass), {
                lang,
                fractionDigits: 2,
                isCurrency: true
              })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      costPoundHarvest: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_cost_per_unit_harvested', { unit: unitLabel })}
          </BodyCell>
        ),
        show: existInSelectedVariables('costPoundHarvest'),
        Cell: (row: PopulationType) => {
          const { costPoundHarvest } = row.harvest ?? {};
          const formattedValue = isNumber(costPoundHarvest)
            ? formatNumber(convertUnitByMultiplication(costPoundHarvest, unitsConfig?.biomass), {
                lang,
                fractionDigits: 2,
                isCurrency: true
              })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      totalRevenuePound: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_revenue_per_unit_processed', { unit: unitLabel })}
          </BodyCell>
        ),
        show: existInSelectedVariables('totalRevenuePound'),
        Cell: (row: PopulationType) => {
          const { totalRevenuePound, hasEstimatedValues } = row.harvest ?? {};
          const formattedValue = isNumber(totalRevenuePound)
            ? formatNumber(convertUnitByMultiplication(totalRevenuePound, unitsConfig?.biomass), {
                lang,
                fractionDigits: 2,
                isCurrency: true
              })
            : '-';

          return (
            <BodyCell>
              <EstimatedValueWrapper hasEstimatedValues={totalRevenuePound && hasEstimatedValues}>
                {formattedValue}
              </EstimatedValueWrapper>
            </BodyCell>
          );
        }
      },

      totalRevenuePoundHarvest: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_revenue_per_unit_harvested', { unit: unitLabel })}
          </BodyCell>
        ),
        show: existInSelectedVariables('totalRevenuePoundHarvest'),
        Cell: (row: PopulationType) => {
          const { totalRevenuePoundHarvest, hasEstimatedValues } = row.harvest ?? {};
          const formattedValue = isNumber(totalRevenuePoundHarvest)
            ? formatNumber(convertUnitByMultiplication(totalRevenuePoundHarvest, unitsConfig?.biomass), {
                lang,
                fractionDigits: 2,
                isCurrency: true
              })
            : '-';

          return (
            <BodyCell>
              <EstimatedValueWrapper hasEstimatedValues={totalRevenuePoundHarvest && hasEstimatedValues}>
                {formattedValue}
              </EstimatedValueWrapper>
            </BodyCell>
          );
        }
      },

      totalRevenue: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_revenue')}
          </BodyCell>
        ),
        show: existInSelectedVariables('totalRevenue'),
        Cell: (row: PopulationType) => {
          const { totalRevenue, hasEstimatedValues } = row.harvest ?? {};
          const formattedValue = isNumber(totalRevenue)
            ? formatNumber(totalRevenue, { lang, fractionDigits: 0, isCurrency: true })
            : '-';

          return (
            <BodyCell>
              <EstimatedValueWrapper hasEstimatedValues={totalRevenue && hasEstimatedValues}>
                {formattedValue}
              </EstimatedValueWrapper>
            </BodyCell>
          );
        }
      },

      totalCosts: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_cost_$')}
          </BodyCell>
        ),
        show: existInSelectedVariables('totalCosts'),
        Cell: (row: PopulationType) => {
          const { totalCosts } = row.harvest ?? {};
          const formattedValue = isNumber(totalCosts)
            ? formatNumber(totalCosts, { lang, fractionDigits: 0, isCurrency: true })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      stockingCosts: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_stocking_cost')}
          </BodyCell>
        ),
        show: existInSelectedVariables('stockingCosts'),
        Cell: (row: PopulationType) => {
          const { stockingCosts } = row.harvest ?? {};
          const formattedValue = isNumber(stockingCosts)
            ? formatNumber(stockingCosts, { lang, fractionDigits: 0, isCurrency: true })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      cumulativeFeedCosts: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_feed_cost_$')}
          </BodyCell>
        ),
        show: existInSelectedVariables('cumulativeFeedCosts'),
        Cell: (row: PopulationType) => {
          const { cumulativeFeedCosts } = row.harvest ?? {};
          const formattedValue = isNumber(cumulativeFeedCosts)
            ? formatNumber(cumulativeFeedCosts, { lang, fractionDigits: 0, isCurrency: true })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      cumulativeOverheadCosts: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_overhead_cost_cumulative')}
          </BodyCell>
        ),
        show: existInSelectedVariables('cumulativeOverheadCosts'),
        Cell: (row: PopulationType) => {
          const { cumulativeOverheadCosts } = row.harvest ?? {};
          const formattedValue = isNumber(cumulativeOverheadCosts)
            ? formatNumber(cumulativeOverheadCosts, { lang, fractionDigits: 0, isCurrency: true })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      overheadCostDryDays: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_overhead_cost_dry_days')}
          </BodyCell>
        ),
        show: existInSelectedVariables('overheadCostDryDays'),
        Cell: (row: PopulationType) => {
          const { overheadCostDryDays } = row.harvest ?? {};
          const formattedValue = isNumber(overheadCostDryDays)
            ? formatNumber(overheadCostDryDays, { lang, fractionDigits: 0, isCurrency: true })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      overheadCostProductiveDays: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_overhead_cost_days_of_culture')}
          </BodyCell>
        ),
        show: existInSelectedVariables('overheadCostProductiveDays'),
        Cell: (row: PopulationType) => {
          const { overheadCostProductiveDays } = row.harvest ?? {};
          const formattedValue = isNumber(overheadCostProductiveDays)
            ? formatNumber(overheadCostProductiveDays, { lang, fractionDigits: 0, isCurrency: true })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      accumulatedOtherDirectCosts: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_other_direct_costs')}
          </BodyCell>
        ),
        show: existInSelectedVariables('accumulatedOtherDirectCosts'),
        Cell: (row: PopulationType) => {
          const { accumulatedOtherDirectCosts } = row.harvest ?? {};
          const formattedValue = isNumber(accumulatedOtherDirectCosts)
            ? formatNumber(accumulatedOtherDirectCosts, { lang, fractionDigits: 0, isCurrency: true })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      averageWeight: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_abw_g')}
          </BodyCell>
        ),
        show: existInSelectedVariables('averageWeight'),
        Cell: (row: PopulationType) => {
          const { averageWeight } = row.harvest ?? {};
          const formattedValue = isNumber(averageWeight)
            ? formatNumber(averageWeight, { lang, fractionDigits: 1 })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      growthLinear: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_growth_g_wk')}
          </BodyCell>
        ),
        show: existInSelectedVariables('growthLinear'),
        Cell: (row: PopulationType) => {
          const { growthLinear } = row.harvest ?? {};
          const formattedValue = isNumber(growthLinear) ? formatNumber(growthLinear, { lang, fractionDigits: 2 }) : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      growthDaily: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_growth_g_day')}
          </BodyCell>
        ),
        show: existInSelectedVariables('growthDaily'),
        Cell: (row: PopulationType) => {
          const { growthDaily } = row.harvest ?? {};
          const formattedValue = isNumber(growthDaily) ? formatNumber(growthDaily, { lang, fractionDigits: 2 }) : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      survival: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_survival_include_harvests')}
          </BodyCell>
        ),
        show: existInSelectedVariables('survival'),
        Cell: (row: PopulationType) => {
          const { survival } = row.harvest ?? {};
          const formattedValue = isNumber(survival)
            ? formatNumber(survival, { lang, isPercentage: true, fractionDigits: 0 })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      rofi: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_rofi')}
          </BodyCell>
        ),
        show: existInSelectedVariables('rofi'),
        Cell: (row: PopulationType) => {
          const { rofi } = row.harvest ?? {};
          const formattedValue = isNumber(rofi)
            ? formatNumber(rofi, { lang, isPercentage: true, fractionDigits: 0 })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      totalBiomassLbs: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_biomass_include_harvests_unit', { unit: unitLabel })}
          </BodyCell>
        ),
        show: existInSelectedVariables('totalBiomassLbs'),
        Cell: (row: PopulationType) => {
          const { totalBiomassLbs } = row.harvest ?? {};

          const formattedValue = isNumber(totalBiomassLbs)
            ? formatNumber(convertUnitByDivision(totalBiomassLbs, unitsConfig?.biomass), { lang, fractionDigits: 0 })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      totalBiomassLbsByHa: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_biomass_include_harvests_unit_ha', { unit: unitLabel })}
          </BodyCell>
        ),
        show: existInSelectedVariables('totalBiomassLbsByHa'),
        Cell: (row: PopulationType) => {
          const { totalBiomassLbsByHa } = row.harvest ?? {};
          const formattedValue = isNumber(totalBiomassLbsByHa)
            ? formatNumber(convertUnitByDivision(totalBiomassLbsByHa, unitsConfig?.biomass), {
                lang,
                fractionDigits: 0
              })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      totalAnimalsHarvested: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_animals_harvested')}
          </BodyCell>
        ),
        show: existInSelectedVariables('totalAnimalsHarvested'),
        Cell: (row: PopulationType) => {
          const { totalAnimalsHarvested } = row.harvest ?? {};
          const formattedValue = isNumber(totalAnimalsHarvested)
            ? formatNumber(totalAnimalsHarvested, { lang, fractionDigits: 0 })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      animalsHarvestedM2: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_animals_harvested_per_m2')}
          </BodyCell>
        ),
        show: existInSelectedVariables('animalsHarvestedM2'),
        Cell: (row: PopulationType) => {
          const { animalsHarvestedM2 } = row.harvest ?? {};
          const formattedValue = isNumber(animalsHarvestedM2)
            ? formatNumber(animalsHarvestedM2, { lang, fractionDigits: 2 })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      animalsHarvestedHa: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_animals_harvested_ha')}
          </BodyCell>
        ),
        show: existInSelectedVariables('animalsHarvestedHa'),
        Cell: (row: PopulationType) => {
          const { animalsHarvestedHa } = row.harvest ?? {};
          const formattedValue = isNumber(animalsHarvestedHa)
            ? formatNumber(animalsHarvestedHa, { lang, fractionDigits: 0 })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      totalPartialHarvestBiomassLbs: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_biomass_from_partials_unit', { unit: unitLabel })}
          </BodyCell>
        ),
        show: existInSelectedVariables('totalPartialHarvestBiomassLbs'),
        Cell: (row: PopulationType) => {
          const { totalPartialHarvestBiomassLbs } = row.harvest ?? {};
          const formattedValue = isNumber(totalPartialHarvestBiomassLbs)
            ? formatNumber(convertUnitByDivision(totalPartialHarvestBiomassLbs, unitsConfig?.biomass), {
                lang,
                fractionDigits: 0
              })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      lbsHarvested: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_biomass_from_final_unit', { unit: unitLabel })}
          </BodyCell>
        ),
        show: existInSelectedVariables('lbsHarvested'),
        Cell: (row: PopulationType) => {
          const { lbsHarvested } = row.harvest ?? {};
          const formattedValue = isNumber(lbsHarvested)
            ? formatNumber(convertUnitByDivision(lbsHarvested, unitsConfig?.biomass), { lang, fractionDigits: 0 })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      numberOfPartialHarvest: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_partial_harvest_done')}
          </BodyCell>
        ),
        show: existInSelectedVariables('numberOfPartialHarvest'),
        Cell: (row: PopulationType) => {
          const { numberOfPartialHarvest } = row.harvest ?? {};

          return <BodyCell>{isNumber(numberOfPartialHarvest) ? numberOfPartialHarvest : '-'}</BodyCell>;
        }
      },

      cumulativeFcr: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_fcr')}
          </BodyCell>
        ),
        show: existInSelectedVariables('cumulativeFcr'),
        Cell: (row: PopulationType) => {
          const { cumulativeFcr } = row.harvest ?? {};
          const formattedValue = isNumber(cumulativeFcr)
            ? formatNumber(cumulativeFcr, { lang, fractionDigits: 2 })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      adjustedFcr: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_fcr_adjusted')}
          </BodyCell>
        ),
        show: existInSelectedVariables('adjustedFcr'),
        Cell: (row: PopulationType) => {
          const { adjustedFcr } = row.harvest ?? {};
          const formattedValue = isNumber(adjustedFcr) ? formatNumber(adjustedFcr, { lang, fractionDigits: 2 }) : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      totalFeedGivenKg: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_feed_given_kg')}
          </BodyCell>
        ),
        show: existInSelectedVariables('totalFeedGivenKg'),
        Cell: (row: PopulationType) => {
          const { totalFeedGivenKg } = row.harvest ?? {};
          const formattedValue = isNumber(totalFeedGivenKg)
            ? formatNumber(totalFeedGivenKg, { lang, fractionDigits: 0 })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      feedKgHaDay: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_feed_given_daily_kg_ha')}
          </BodyCell>
        ),
        show: existInSelectedVariables('feedKgHaDay'),
        Cell: (row: PopulationType) => {
          const { feedKgHaDay } = row.harvest ?? {};
          const formattedValue = isNumber(feedKgHaDay) ? formatNumber(feedKgHaDay, { lang, fractionDigits: 0 }) : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      feedCostPerKg: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_feed_cost_per_kg')}
          </BodyCell>
        ),
        show: existInSelectedVariables('feedCostPerKg'),
        Cell: (row: PopulationType) => {
          const { feedCostPerKg } = row.harvest ?? {};
          const formattedValue = isNumber(feedCostPerKg)
            ? formatNumber(feedCostPerKg, { lang, fractionDigits: 2 })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      costMillar: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_cost_millar')}
          </BodyCell>
        ),
        show: existInSelectedVariables('costMillar'),
        Cell: (row: PopulationType) => {
          const { stockingCostsMillar } = row;
          const formattedValue = isNumber(stockingCostsMillar)
            ? formatNumber(stockingCostsMillar, { fractionDigits: 2, lang, isCurrency: true })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      stockingDate: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_stocking_date')}
          </BodyCell>
        ),
        show: existInSelectedVariables('stockingDate'),
        Cell: (row: PopulationType) => {
          const { stockedAt, stockedAtTimezone } = row;
          const formattedValue = stockedAt
            ? DateTime.fromISO(stockedAt, { zone: stockedAtTimezone }).toFormat('LLL dd, yyyy', {
                locale: lang
              })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      harvestDate: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_harvest_date')}
          </BodyCell>
        ),
        show: existInSelectedVariables('harvestDate'),
        Cell: (row: PopulationType) => {
          const { date } = row.harvest ?? {};
          const harvestDate = date?.includes('GMT') ? new Date(date).toISOString() : date;
          const formattedValue = harvestDate
            ? DateTime.fromISO(harvestDate).toFormat('LLL dd, yyyy', { locale: lang })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      stockingQuantity: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_animals_stocked')}
          </BodyCell>
        ),
        show: existInSelectedVariables('stockingQuantity'),
        Cell: (row: PopulationType) => {
          const { seedingQuantity } = row ?? {};

          const formattedValue = isNumber(seedingQuantity)
            ? formatNumber(seedingQuantity, { lang, fractionDigits: 0 })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      stockingQuantityByHa: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_animals_stocked_ha')}
          </BodyCell>
        ),
        show: existInSelectedVariables('stockingQuantityByHa'),
        Cell: (row: PopulationType) => {
          const { seedingQuantity, pondId } = row ?? {};
          const { size } = pondsHashmap[pondId] ?? {};

          const formattedValue =
            isNumber(seedingQuantity) && size ? formatNumber(seedingQuantity / size, { lang, fractionDigits: 0 }) : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      stockingWeight: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_stocking_weight_g')}
          </BodyCell>
        ),
        show: existInSelectedVariables('stockingWeight'),
        Cell: (row: PopulationType) => {
          const { seedingAverageWeight } = row ?? {};

          const formattedValue = isNumber(seedingAverageWeight)
            ? formatNumber(seedingAverageWeight, { lang, fractionDigits: 2 })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      pondSize: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_pond_size')}
          </BodyCell>
        ),
        show: existInSelectedVariables('pondSize'),
        Cell: (row: PopulationType) => {
          const { pondId } = row ?? {};
          const { size } = pondsHashmap[pondId] ?? {};

          const formattedValue = size ? `${formatNumber(size, { lang, fractionDigits: 2 })} ${trans('t_ha')}` : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      cycleDays: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_days_of_culture')}
          </BodyCell>
        ),
        show: existInSelectedVariables('cycleDays'),
        Cell: (row: PopulationType) => {
          const { daysOfCulture } = row.harvest ?? {};
          const formattedValue = isNumber(daysOfCulture)
            ? trans('t_days_d', { count: formatNumber(daysOfCulture, { lang, fractionDigits: 0 }) })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      productionDays: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_production_days')}
          </BodyCell>
        ),
        show: existInSelectedVariables('productionDays'),
        Cell: (row: PopulationType) => {
          const { cycleLength } = row.cycleInformation?.target ?? {};
          const dryDays = row?.dryDaysBeforeStocking ?? 0;

          const formattedValue = isNumber(cycleLength)
            ? trans('t_days_d', { count: formatNumber(cycleLength + dryDays, { lang, fractionDigits: 0 }) })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      numberOfAutofeeders: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_#_of_autofeeders')}
          </BodyCell>
        ),
        show: existInSelectedVariables('numberOfAutofeeders'),
        Cell: (row: PopulationType) => {
          const { cycleInformation } = row;
          const numberOfAutoFeeders = cycleInformation?.equipment?.numberOfAutoFeeders;

          const formattedValue = isNumber(numberOfAutoFeeders)
            ? formatNumber(numberOfAutoFeeders, { lang, fractionDigits: 0 })
            : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      },

      nauplii: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_nauplii_source')}
          </BodyCell>
        ),
        show: existInSelectedVariables('nauplii'),
        Cell: (row: PopulationType) => {
          const { geneticName } = row;

          return <BodyCell>{geneticName || '-'}</BodyCell>;
        }
      },

      hatcheries: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_lab_source')}
          </BodyCell>
        ),
        show: existInSelectedVariables('hatcheries'),
        Cell: (row: PopulationType) => {
          const { hatcheryName } = row;

          return <BodyCell>{hatcheryName || '-'}</BodyCell>;
        }
      },

      autoFeederPerHa: {
        Header: (
          <BodyCell px='2lg' justifyContent='flex-start'>
            {trans('t_autofeeders_ha')}
          </BodyCell>
        ),
        show: existInSelectedVariables('autoFeederPerHa'),
        Cell: (row: PopulationType) => {
          const { cycleInformation, pondId } = row;
          const numberOfAutoFeeders = cycleInformation?.equipment?.numberOfAutoFeeders;
          const { size } = pondsHashmap[pondId] ?? {};
          const formattedValue =
            isNumber(numberOfAutoFeeders) && size
              ? formatNumber(numberOfAutoFeeders / size, { lang, fractionDigits: 2 })
              : '-';

          return <BodyCell>{formattedValue}</BodyCell>;
        }
      }
    };

    return [
      {
        Header: <HeadCell>{trans('t_variables')}</HeadCell>,
        show: true,
        Cell: (row: PopulationType) => {
          const pondName = pondsHashmap[row.pondId]?.name;

          return (
            <HeadCell
              css={{
                '& > p:not(:last-child):after': {
                  h: '16px',
                  w: '0.5px',
                  mx: '10px',
                  content: '""',
                  bgColor: 'gray.400',
                  verticalAlign: 'sub',
                  display: 'inline-block'
                }
              }}
            >
              <Text>{pondName}</Text>
              <Text>
                {trans('t_cycle')} {row.cycle}
              </Text>
            </HeadCell>
          );
        }
      },

      ...variables.map((variable) => columnsByVariable[variable])
    ];
  }, [lang, existInSelectedVariables]);

  if (isLoading && !isFinishedOnce) {
    return <PartialPageLoader />;
  }

  if (!cycleIds?.length) {
    return null;
  }

  return (
    <Box
      flex={1}
      pos='relative'
      css={{
        '& .table-container .data-row:nth-child(2)': {
          pt: 'md-alt',
          top: '16px',
          zIndex: 100,
          position: 'sticky',
          backgroundColor: 'white',
          w: 'calc-size(calc-size(max-content, size), size + 6px)'
        },
        '& .table-container .data-row:nth-child(2) > .header-cell, .table-container .data-row > .body-cell': {
          left: 0,
          zIndex: 10,
          position: 'sticky'
        },
        '& .table-container .data-row > .body-cell': {
          bgColor: 'white'
        },
        [`& .table-container .data-row:nth-child(2) [data-column-index="${columnIndexToHighlight}"]`]: {
          borderTopRadius: 'xl',
          backgroundColor: 'gray.100'
        },
        '& .table-container .data-row:last-child .body-cell': {
          borderBottomRadius: 'xl'
        }
      }}
    >
      <SectionLoader isLoading={isLoading} />
      {!!sortedPopulations?.length && (
        <Box
          maxW='calc-size(calc-size(max-content, size), size + 6px)'
          overflowY='auto'
          overflowX='auto'
          maxH='calc(100vh - 276px)'
          pe='2xs'
          className='table-container'
        >
          <Text
            mr='-4px'
            mb='md-alt'
            size='label200'
            color='text.gray.disabled'
            position='sticky'
            top={0}
            left={0}
            bgColor='white'
            zIndex={100}
          >
            {trans('t_using_imputed_values_for_calculations')}
          </Text>
          {columns.map((column, i) => {
            const { Header, Cell, show } = column ?? {};
            if (!show) return null;

            const headProps: GridProps = i === 0 ? { zIndex: 10 } : { pos: 'relative' };
            return (
              <Grid
                h={i === 0 ? '60px' : '44px'}
                className='data-row'
                key={i}
                bgColor='white'
                templateColumns={`260px repeat(${sortedPopulations.length}, 160px)`}
                gap='sm-alt'
                css={{
                  [`& [data-column-index="${columnIndexToHighlight}"] .header-cell`]: {
                    backgroundColor: 'gray.200'
                  },
                  [`& [data-column-index="${columnIndexToHighlight}"] .body-cell`]: {
                    backgroundColor: 'gray.100'
                  }
                }}
                {...headProps}
              >
                {Header}
                {sortedPopulations.map((population, index) => {
                  return (
                    <Box
                      key={population._id}
                      pos='relative'
                      data-column-index={index + 1}
                      onMouseLeave={() => setColumnIndexToHighlight(undefined)}
                      _hover={{
                        '& .removeCycle': { display: 'block' },
                        '& .header-cell': { bgColor: 'gray.200' }
                      }}
                      onMouseOver={(element) => {
                        const { currentTarget } = element;

                        // If not header cell return
                        if (!currentTarget.querySelector('.header-cell')) return;

                        setColumnIndexToHighlight(Number(currentTarget.getAttribute('data-column-index')));
                      }}
                    >
                      {Cell(population)}
                      {i === 0 && (
                        <CloseFilled
                          pos='absolute'
                          top='-12px'
                          right='-5px'
                          className='removeCycle'
                          display='none'
                          cursor='pointer'
                          onClick={() => {
                            onDeleteCycle(population.pondId, population._id);
                          }}
                        />
                      )}
                    </Box>
                  );
                })}
              </Grid>
            );
          })}
        </Box>
      )}
    </Box>
  );
}

function HeadCell(props: FlexProps) {
  return (
    <Flex
      align='center'
      justify='center'
      bg='bg.muted'
      h='40px'
      rounded='xl'
      textStyle='label.200'
      className='header-cell'
      {...props}
    />
  );
}

function BodyCell(props: FlexProps) {
  return (
    <Flex
      py='sm'
      align='center'
      justify='center'
      height='100%'
      textStyle='label.200'
      className='body-cell'
      {...props}
    />
  );
}

function EstimatedValueWrapper({ children, hasEstimatedValues }: BoxProps & { hasEstimatedValues?: boolean }) {
  if (!hasEstimatedValues) return children;

  return (
    <Flex
      w='max-content'
      align='center'
      justify='center'
      bg={hasEstimatedValues ? 'bg.gray.strong' : 'transparent'}
      px='md'
      h='24px'
      rounded='full'
    >
      <Text size='label200'>{children}*</Text>
    </Flex>
  );
}
