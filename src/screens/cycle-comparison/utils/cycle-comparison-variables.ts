import { usePermission } from '@hooks/use-permission';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import isUndefined from 'lodash/isUndefined';

export type CycleComparisonVariableType =
  | 'profitPerHaPerDay'
  | 'totalProfit'
  | 'rofi'
  | 'profitPoundHarvest'
  | 'costPerPound'
  | 'costPoundHarvest'
  | 'totalRevenuePound'
  | 'totalRevenuePoundHarvest'
  | 'costMillar'
  | 'totalRevenue'
  | 'totalCosts'
  | 'stockingCosts'
  | 'cumulativeFeedCosts'
  | 'cumulativeOverheadCosts'
  | 'overheadCostDryDays'
  | 'overheadCostProductiveDays'
  | 'accumulatedOtherDirectCosts'
  | 'averageWeight'
  | 'growthLinear'
  | 'growthDaily'
  | 'survival'
  | 'totalBiomassLbs'
  | 'totalBiomassLbsByHa'
  | 'totalAnimalsHarvested'
  | 'animalsHarvestedM2'
  | 'animalsHarvestedHa'
  | 'totalPartialHarvestBiomassLbs'
  | 'lbsHarvested'
  | 'numberOfPartialHarvest'
  | 'cumulativeFcr'
  | 'adjustedFcr'
  | 'totalFeedGivenKg'
  | 'feedKgHaDay'
  | 'feedCostPerKg'
  | 'stockingDate'
  | 'harvestDate'
  | 'stockingQuantity'
  | 'stockingQuantityByHa'
  | 'stockingWeight'
  | 'pondSize'
  | 'cycleDays'
  | 'productionDays'
  | 'numberOfAutofeeders'
  | 'nauplii'
  | 'hatcheries'
  | 'autoFeederPerHa';

type VariableType = { value: CycleComparisonVariableType; label: string };

export const useGetCycleVariableLabels = (): Record<CycleComparisonVariableType, string> => {
  const { trans } = getTrans();
  const user = useAppSelector((state) => state.auth.user);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};

  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);
  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');

  return {
    profitPerHaPerDay: trans('t_profit_include_dry_days_ha_day'),
    totalProfit: trans('t_profit'),
    rofi: trans('t_rofi'),
    profitPoundHarvest: trans('t_profit_per_unit_harvested', { unit: unitLabel }),
    costPerPound: trans('t_cost_per_unit_processed', { unit: unitLabel }),
    costPoundHarvest: trans('t_cost_per_unit_harvested', { unit: unitLabel }),
    totalRevenuePound: trans('t_revenue_per_unit_processed', { unit: unitLabel }),
    totalRevenuePoundHarvest: trans('t_revenue_per_unit_harvested', { unit: unitLabel }),
    totalRevenue: trans('t_revenue'),
    totalCosts: trans('t_cost_$'),
    stockingCosts: trans('t_stocking_cost'),
    cumulativeFeedCosts: trans('t_feed_cost_$'),
    cumulativeOverheadCosts: trans('t_overhead_cost_cumulative'),
    overheadCostProductiveDays: trans('t_overhead_cost_days_of_culture'),
    overheadCostDryDays: trans('t_overhead_cost_dry_days'),
    accumulatedOtherDirectCosts: trans('t_other_direct_costs'),
    averageWeight: trans('t_abw_g'),
    growthLinear: trans('t_growth_g_wk'),
    growthDaily: trans('t_growth_g_day'),
    survival: trans('t_survival_include_harvests'),
    totalBiomassLbs: trans('t_biomass_include_harvests_unit', { unit: unitLabel }),
    totalBiomassLbsByHa: trans('t_biomass_include_harvests_unit_ha', { unit: unitLabel }),
    totalAnimalsHarvested: trans('t_animals_harvested'),
    animalsHarvestedM2: trans('t_animals_harvested_per_m2'),
    animalsHarvestedHa: trans('t_animals_harvested_ha'),
    totalPartialHarvestBiomassLbs: trans('t_biomass_from_partials_unit', { unit: unitLabel }),
    lbsHarvested: trans('t_biomass_from_final_unit', { unit: unitLabel }),
    numberOfPartialHarvest: trans('t_partial_harvest_done'),
    cumulativeFcr: trans('t_fcr'),
    adjustedFcr: trans('t_fcr_adjusted'),
    totalFeedGivenKg: trans('t_feed_given_kg'),
    feedKgHaDay: trans('t_feed_given_daily_kg_ha'),
    feedCostPerKg: trans('t_feed_cost_per_kg'),
    costMillar: trans('t_cost_millar'),
    stockingDate: trans('t_stocking_date'),
    harvestDate: trans('t_harvest_date'),
    stockingQuantity: trans('t_animals_stocked'),
    stockingQuantityByHa: trans('t_animals_stocked_ha'),
    stockingWeight: trans('t_stocking_weight_g'),
    pondSize: trans('t_pond_size'),
    cycleDays: trans('t_days_of_culture'),
    productionDays: trans('t_production_days'),
    numberOfAutofeeders: trans('t_#_of_autofeeders'),
    nauplii: trans('t_nauplii_source'),
    hatcheries: trans('t_lab_source'),
    autoFeederPerHa: trans('t_autofeeders_ha')
  };
};

type useGetCycleComparisonGroupViewVariablesReturnType = {
  variablesHashmap: Record<string, { title: string; variables: VariableType[] }>;
  variableLabels: Record<CycleComparisonVariableType, string>;
};

export const useGetCycleComparisonGroupViewVariables = (): useGetCycleComparisonGroupViewVariablesReturnType => {
  const currentFarmId = useAppSelector((state) => state.farm?.currentFarm?._id);
  const { trans } = getTrans();
  const variableLabels = useGetCycleVariableLabels();

  const isAdmin = usePermission({
    role: 'admin',
    entity: 'farm',
    entityId: currentFarmId
  });

  const isSupervisor = usePermission({
    role: 'supervisor',
    entity: 'farm',
    entityId: currentFarmId
  });

  const financialAdmin: VariableType[] = isAdmin
    ? [
        { value: 'rofi', label: variableLabels['rofi'] },
        { value: 'profitPerHaPerDay', label: variableLabels['profitPerHaPerDay'] },
        { value: 'totalProfit', label: variableLabels['totalProfit'] },
        { value: 'profitPoundHarvest', label: variableLabels['profitPoundHarvest'] },
        { value: 'totalRevenuePound', label: variableLabels['totalRevenuePound'] },
        { value: 'totalRevenuePoundHarvest', label: variableLabels['totalRevenuePoundHarvest'] },
        { value: 'totalRevenue', label: variableLabels['totalRevenue'] }
      ]
    : [];

  const financialSupervisor: VariableType[] =
    isSupervisor || isAdmin
      ? [
          { value: 'costPerPound', label: variableLabels['costPerPound'] },
          { value: 'costPoundHarvest', label: variableLabels['costPoundHarvest'] },
          { value: 'costMillar', label: variableLabels['costMillar'] },
          { value: 'totalCosts', label: variableLabels['totalCosts'] },
          { value: 'stockingCosts', label: variableLabels['stockingCosts'] },
          { value: 'feedCostPerKg', label: variableLabels['feedCostPerKg'] },
          { value: 'cumulativeFeedCosts', label: variableLabels['cumulativeFeedCosts'] },
          { value: 'cumulativeOverheadCosts', label: variableLabels['cumulativeOverheadCosts'] },
          { value: 'overheadCostProductiveDays', label: variableLabels['overheadCostProductiveDays'] },
          { value: 'overheadCostDryDays', label: variableLabels['overheadCostDryDays'] },
          { value: 'accumulatedOtherDirectCosts', label: variableLabels['accumulatedOtherDirectCosts'] }
        ]
      : [];

  return {
    variablesHashmap: {
      financials: {
        title: trans('t_financials'),
        variables: [...financialAdmin, ...financialSupervisor]
      },
      weights: {
        title: trans('t_weights'),
        variables: [
          { value: 'averageWeight', label: variableLabels['averageWeight'] },
          { value: 'growthLinear', label: variableLabels['growthLinear'] },
          { value: 'growthDaily', label: variableLabels['growthDaily'] }
        ]
      },

      biomass: {
        title: trans('t_biomass'),
        variables: [
          { value: 'survival', label: variableLabels['survival'] },
          { value: 'totalBiomassLbs', label: variableLabels['totalBiomassLbs'] },
          { value: 'totalBiomassLbsByHa', label: variableLabels['totalBiomassLbsByHa'] },
          { value: 'totalAnimalsHarvested', label: variableLabels['totalAnimalsHarvested'] },
          { value: 'animalsHarvestedM2', label: variableLabels['animalsHarvestedM2'] },
          { value: 'animalsHarvestedHa', label: variableLabels['animalsHarvestedHa'] },
          { value: 'totalPartialHarvestBiomassLbs', label: variableLabels['totalPartialHarvestBiomassLbs'] },
          { value: 'lbsHarvested', label: variableLabels['lbsHarvested'] },
          { value: 'numberOfPartialHarvest', label: variableLabels['numberOfPartialHarvest'] }
        ]
      },

      feed: {
        title: trans('t_feed'),
        variables: [
          { value: 'cumulativeFcr', label: variableLabels['cumulativeFcr'] },
          { value: 'adjustedFcr', label: variableLabels['adjustedFcr'] },
          { value: 'totalFeedGivenKg', label: variableLabels['totalFeedGivenKg'] },
          { value: 'feedKgHaDay', label: variableLabels['feedKgHaDay'] }
        ]
      },

      'Cycle info': {
        title: trans('t_cycle_info'),
        variables: [
          { value: 'stockingDate', label: variableLabels['stockingDate'] },
          { value: 'harvestDate', label: variableLabels['harvestDate'] },
          { value: 'stockingQuantity', label: variableLabels['stockingQuantity'] },
          { value: 'stockingQuantityByHa', label: variableLabels['stockingQuantityByHa'] },
          { value: 'stockingWeight', label: variableLabels['stockingWeight'] },
          { value: 'pondSize', label: variableLabels['pondSize'] },
          { value: 'cycleDays', label: variableLabels['cycleDays'] },
          { value: 'productionDays', label: variableLabels['productionDays'] },
          { value: 'numberOfAutofeeders', label: variableLabels['numberOfAutofeeders'] },
          { value: 'nauplii', label: variableLabels['nauplii'] },
          { value: 'hatcheries', label: variableLabels['hatcheries'] },
          { value: 'autoFeederPerHa', label: variableLabels['autoFeederPerHa'] }
        ]
      }
    },
    variableLabels
  };
};
