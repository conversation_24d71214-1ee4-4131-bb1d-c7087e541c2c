import { useEffect, useState } from 'react';
import { getCacheItem, setCacheItem, sevenDaysCacheTime } from '@utils/local-cache';
import { CurrentFarmPonds } from '@redux/farm/set-current-farm-ponds';
import keyBy from 'lodash/keyBy';
import { useListPopulationsApi } from '@screens/population/hooks/use-list-populations-api';
import { useAppSelector } from '@redux/hooks';

const CYCLE_COMPARISON_SELECTED_CYCLES_KEY = 'cycleComparisonSelectedCycles';

export type SelectedCyclesType = { pondId: string; cycleId: string }[];
type ReturnType = [
  SelectedCyclesType,
  (pondId: string, cycles: string[]) => void,
  (pondId: string, cycleId: string) => void
];

export const useCycleComparisonSelectedCycles = ({ ponds }: { ponds: CurrentFarmPonds }): ReturnType => {
  const [value, setValue] = useState<SelectedCyclesType>([]);
  const currentFarmId = useAppSelector((state) => state.farm?.currentFarm?._id);

  const [_, listPopulations] = useListPopulationsApi();

  const pondsHashmap = keyBy(ponds, '_id');

  useEffect(() => {
    getCacheItem<SelectedCyclesType>(CYCLE_COMPARISON_SELECTED_CYCLES_KEY).then((res) => {
      if (!res?.length) {
        return getRecentCompletedCycles();
      }

      const filteredPonds: SelectedCyclesType = [];
      res.forEach((ele) => {
        if (!!pondsHashmap[ele.pondId]) {
          filteredPonds.push(ele);
        }
      });

      setValue(filteredPonds);
    });
  }, []);

  const getRecentCompletedCycles = () => {
    listPopulations({
      params: {
        filter: { farmId: currentFarmId },
        page: { size: 5 },
        sort: [{ field: 'harvest.date', order: 'desc' }]
      },
      fields: {
        populations: {
          _id: 1,
          pondId: 1
        }
      },
      successCallback: (res) => {
        const recentCompletedCycles: SelectedCyclesType =
          res.populations?.map((ele) => ({ pondId: ele.pondId, cycleId: ele._id })) || [];
        setValue(recentCompletedCycles);
      },
      failureCallback: () => setValue([])
    });
  };

  const onChange = (pondId: string, cycles: string[]) => {
    const newCycles: SelectedCyclesType = [];
    cycles.forEach((cycleId) => {
      const isExist = value.find((v) => v.cycleId === cycleId);
      if (!isExist) {
        newCycles.push({ pondId, cycleId });
      }
    });

    const newValues: SelectedCyclesType = [...value, ...newCycles];

    setCacheItem(CYCLE_COMPARISON_SELECTED_CYCLES_KEY, newValues, sevenDaysCacheTime).then();
    setValue(newValues);
  };

  const onDeleteCycle = (pondId: string, cycleId: string) => {
    const newValues = value.filter((ele) => ele.cycleId !== cycleId);

    setCacheItem(CYCLE_COMPARISON_SELECTED_CYCLES_KEY, newValues, sevenDaysCacheTime).then();
    setValue(newValues);
  };

  return [value, onChange, onDeleteCycle];
};
