import { useEffect, useState } from 'react';
import { getCacheItem, setCacheItem, sevenDaysCacheTime } from '@utils/local-cache';
import { useAppSelector } from '@redux/hooks';
import { usePermission } from '@hooks/use-permission';
import { CycleComparisonVariableType } from '@screens/cycle-comparison/utils/cycle-comparison-variables';

const CYCLE_COMPARISON_VARIABLES_KEY = 'cycleComparisonVariables';

const adminValues: CycleComparisonVariableType[] = [
  'harvestDate',
  'cycleDays',
  'stockingQuantity',
  'stockingQuantityByHa',
  'averageWeight',
  'profitPerHaPerDay',
  'totalProfit',
  'totalRevenue',
  'totalRevenuePoundHarvest',
  'rofi',
  'costPoundHarvest',
  'adjustedFcr',
  'totalBiomassLbs',
  'totalBiomassLbsByHa',
  'totalAnimalsHarvested',
  'animalsHarvestedHa',
  'survival',
  'growthLinear'
];

const supervisorValues: CycleComparisonVariableType[] = [
  'harvestDate',
  'cycleDays',
  'stockingQuantity',
  'stockingQuantityByHa',
  'averageWeight',
  'costPoundHarvest',
  'adjustedFcr',
  'totalBiomassLbs',
  'totalBiomassLbsByHa',
  'totalAnimalsHarvested',
  'animalsHarvestedHa',
  'survival',
  'growthLinear'
];

const nonAdminOrSupervisorValues: CycleComparisonVariableType[] = [
  'harvestDate',
  'cycleDays',
  'stockingQuantity',
  'stockingQuantityByHa',
  'averageWeight',
  'adjustedFcr',
  'totalBiomassLbs',
  'totalBiomassLbsByHa',
  'totalAnimalsHarvested',
  'animalsHarvestedHa',
  'survival',
  'growthLinear'
];

const adminOnlyAllowedVariables: CycleComparisonVariableType[] = [
  'profitPerHaPerDay',
  'totalProfit',
  'rofi',
  'profitPoundHarvest',
  'totalRevenuePound',
  'totalRevenuePoundHarvest',
  'totalRevenue'
];

const supervisorOrAdminOnlyAllowedVariables: CycleComparisonVariableType[] = [
  ...adminOnlyAllowedVariables,
  'costPerPound',
  'costPoundHarvest',
  'costMillar',
  'totalCosts',
  'stockingCosts',
  'cumulativeFeedCosts',
  'cumulativeOverheadCosts',
  'overheadCostDryDays',
  'overheadCostProductiveDays',
  'accumulatedOtherDirectCosts',
  'feedCostPerKg'
];

type ReturnType = [CycleComparisonVariableType[], (v: CycleComparisonVariableType[]) => void];

export function useCycleComparisonSelectedVariables(localStorageKey?: string): ReturnType {
  const farmId = useAppSelector((state) => state.farm.currentFarm?._id);

  const isAdmin = usePermission({
    role: 'admin',
    entity: 'farm',
    entityId: farmId
  });

  const isSupervisor = usePermission({
    role: 'supervisor',
    entity: 'farm',
    entityId: farmId
  });

  const [value, setValue] = useState<CycleComparisonVariableType[]>([]);
  const supervisorDefaults = isSupervisor ? supervisorValues : nonAdminOrSupervisorValues;
  const defaultValues = isAdmin ? adminValues : supervisorDefaults;

  useEffect(() => {
    getCacheItem<CycleComparisonVariableType[]>(localStorageKey ?? CYCLE_COMPARISON_VARIABLES_KEY).then((res) => {
      if (!res) return setValue(defaultValues);
      if (isAdmin) return setValue(res);

      if (isSupervisor) {
        const filteredValues = res.filter((value) => {
          return !adminOnlyAllowedVariables.includes(value);
        });
        setValue(filteredValues);
        return;
      }

      const filteredValues = res.filter((value) => {
        return !supervisorOrAdminOnlyAllowedVariables.includes(value);
      });
      setValue(filteredValues);
    });
  }, [isAdmin, isSupervisor]);

  const onChange = (v: CycleComparisonVariableType[]) => {
    setCacheItem(localStorageKey ?? CYCLE_COMPARISON_VARIABLES_KEY, v, sevenDaysCacheTime).then();

    setValue(v);
  };

  return [value, onChange];
}
