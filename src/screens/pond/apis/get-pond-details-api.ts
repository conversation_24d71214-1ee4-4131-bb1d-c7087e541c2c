import { moduleFarm } from '@sdks/module-farm';
import { Head, QueryRequest, v1GetPondDetailsOutputRequest } from '@xpertsea/module-farm-sdk';

export async function getPondDetailsApi({
  params,
  fields = {
    pond: {
      _id: 1,
      eid: 1,
      name: 1,
      farmId: 1,
      size: 1,
      currentPopulationId: 1,
      superVisorId: 1,
      geoLocation: { type: 1, coordinates: 1 },
      geoPolygon: { type: 1, coordinates: 1 },
      metadata: 1,
      updatedAt: 1,
      createdAt: 1,
      isDeleted: 1,
      deletedAt: 1,
      deletedBy: 1,
      createdBy: 1,
      isArchived: 1
    }
  },
  addFields, // todo handle addFields with merge deep
  removeFields // todo handle removeFields with remove deep
}: {
  params: Head<QueryRequest['v1GetPondDetails']>;
  fields?: v1GetPondDetailsOutputRequest;
  addFields?: v1GetPondDetailsOutputRequest;
  removeFields?: v1GetPondDetailsOutputRequest;
}) {
  // fields = mergeDeep(fields, addFields);
  // fields = removeDeep(fields, removeFields);
  const { data, errors } = await moduleFarm.query({
    v1GetPondDetails: [params, fields]
  });

  return { data, errors };
}
