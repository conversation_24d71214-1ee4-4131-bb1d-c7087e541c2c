import { getTrans } from '@i18n/get-trans';
import { OptionType } from '@components/form/form-control-react-select';
import { formatNumber, isNumber } from '@utils/number';

export function getHarvestTypeToLabel(harvestType: string, shouldReturnAbbreviation = false) {
  const { trans } = getTrans();

  const harvestTypeMap: Record<typeof harvestType, string> = {
    headon: shouldReturnAbbreviation ? trans('t_head_on_abbreviation') : trans('t_head_on'),
    headless: shouldReturnAbbreviation ? trans('t_head_less_abbreviation') : trans('t_headless')
  };

  return harvestTypeMap?.[harvestType] ?? '';
}

type GetHarvestAmountPercentTextParams = {
  percentHarvestAsHeadsOn: number;
  percentHeadlessAsClassA: number;
  harvestType: string;
};

export function getHarvestAmountPercentText(params: GetHarvestAmountPercentTextParams) {
  const { harvestType, percentHarvestAsHeadsOn, percentHeadlessAsClassA } = params;

  let harvestAmountPercentText;

  if (harvestType === 'headon')
    harvestAmountPercentText = isNumber(percentHarvestAsHeadsOn)
      ? `: A ${formatNumber(percentHarvestAsHeadsOn, { fractionDigits: 0, isPercentage: true })}`
      : '-';
  if (harvestType === 'headless')
    harvestAmountPercentText = isNumber(percentHeadlessAsClassA)
      ? `: A ${formatNumber(percentHeadlessAsClassA, { fractionDigits: 0, isPercentage: true })}`
      : '-';

  return harvestAmountPercentText;
}

type GetHarvestTypeTextParams = {
  processorId: string;
  processorOptions: OptionType[];
  percentHarvestAsHeadsOn: number;
  percentHeadlessAsClassA: number;
  harvestType: string;
};

export function getHarvestTypeText(params: GetHarvestTypeTextParams) {
  const { processorOptions, harvestType, processorId, percentHarvestAsHeadsOn, percentHeadlessAsClassA } = params;

  if (!processorId) return '-';

  const harvestAmountPercentText = getHarvestAmountPercentText({
    harvestType,
    percentHarvestAsHeadsOn,
    percentHeadlessAsClassA
  });
  const processorName = processorOptions?.find((item) => item.value === processorId)?.label;

  return `${processorName ?? '-'} / ${getHarvestTypeToLabel(harvestType, true)}${harvestAmountPercentText ?? '-'}`;
}
