import { PopulationPrediction } from '@xpertsea/module-farm-sdk';
import { PopulationType } from '@redux/farm/set-current-population';
import { useAppSelector } from '@redux/hooks';
import { generateSurvivalCycleDays, getLastDateRecordValue } from '@screens/population/helpers/population-values';
import { convertToIsoDate } from '@utils/date';
import { formatNumber, isNumber } from '@utils/number';
import { getDaysDiffBetweenDates } from '@screens/farm/helpers/farm-dates';

interface FindProfitProjectionGrowthParams {
  prediction: PopulationPrediction[];
  lastHistoryDate: string;
  plannedHarvestDate: string;
}

export function findPredictionGrowthLastHistoryToHarvestDate(args: FindProfitProjectionGrowthParams) {
  const { prediction, lastHistoryDate, plannedHarvestDate } = args;
  const lastHistoryPrediction = prediction?.find((ele) => ele.date === lastHistoryDate);
  const predictionOnHarvestDate = prediction?.find((ele) => ele.date === plannedHarvestDate);

  const lastHistoryPredictionABW = lastHistoryPrediction?.averageWeight ?? 0;
  const predictionOnHarvestDateABW = predictionOnHarvestDate?.averageWeight ?? 0;

  const abwDiff = predictionOnHarvestDateABW - lastHistoryPredictionABW;

  if (!abwDiff) return 0;

  const daysDiff = getDaysDiffBetweenDates({
    dateToCompare: lastHistoryDate,
    baseDate: plannedHarvestDate
  });
  const weekDays = 7;

  return (abwDiff / daysDiff) * weekDays;
}

type CheckHasInfoForRecentMonitoringParams = {
  lastMonitoringDate: string;
  lastSurvivalRateDate: string;
  lastCumulativeDate: string;
};

export function checkHasInfoForRecentMonitoring({
  lastMonitoringDate,
  lastSurvivalRateDate,
  lastCumulativeDate
}: CheckHasInfoForRecentMonitoringParams) {
  return lastMonitoringDate === lastSurvivalRateDate && lastMonitoringDate === lastCumulativeDate;
}

export function useProfitProjectionVariableFormData({ population }: { population: PopulationType }) {
  const { lang } = useAppSelector((state) => state.app);
  const { currentFarm } = useAppSelector((state) => state.farm);
  const { timezone, growthTarget, stockingConfig } = currentFarm ?? {};

  const { productionDays, survivalRate: defaultSurvival } = growthTarget ?? {};

  const {
    survivalRate,
    stockedAt,
    stockedAtTimezone,
    lastMonitoringDate,
    dryDaysBeforeStocking,
    cycleInformation,
    feedData,
    productionPrediction
  } = population ?? {};

  const { summary } = stockingConfig ?? {};
  const { dryDays: costDryDays } = summary ?? {};

  const { projection } = cycleInformation ?? {};
  const { projectedGrowth, projectedFeed, projectedSurvival } = projection ?? {};
  const {
    projectedSurvivalType,
    dailyMortalityPercent,
    expectedTargetSurvival: projectionSurvival,
    expectedTargetSurvivalDays: projectionSurvivalDays
  } = projectedSurvival ?? {};

  const { days: estimatedGrowthDays, grams: estimatedGrowthGrams, type: estimateGrowthType } = projectedGrowth ?? {};

  const { value: survival, latestDate: lastSurvivalRateDate } = getLastDateRecordValue(survivalRate);
  const lastSurvivalCycleDays = generateSurvivalCycleDays({
    timezone,
    stockedAt,
    stockedAtTimezone,
    survivalDate: lastSurvivalRateDate
  });
  const lastCumulativeDate = feedData?.[feedData?.length - 1]?.date;
  const cumulativeFeed = productionPrediction?.find((e) => e.date === lastCumulativeDate)?.cumulativeFeed;

  const lastMonitoringDateISOFormatted = convertToIsoDate(lastMonitoringDate, timezone);

  const isLastMonitoringHasAllInfo = checkHasInfoForRecentMonitoring({
    lastCumulativeDate: lastCumulativeDate,
    lastMonitoringDate: lastMonitoringDateISOFormatted,
    lastSurvivalRateDate: lastSurvivalRateDate
  });

  const survivalValue = survival ? survival * 100 - 5 : undefined;
  const dryDaysOfCycle = dryDaysBeforeStocking || costDryDays;
  const expectedTargetSurvival = projectionSurvival || defaultSurvival;
  const expectedTargetSurvivalDays = projectionSurvivalDays || productionDays;

  const defaultValues = {
    dryDaysBeforeStocking: dryDaysOfCycle,
    cumulativeFeed: { value: cumulativeFeed || NaN, date: lastCumulativeDate },
    survival: { value: survival ? survival * 100 : NaN, date: lastSurvivalRateDate },
    projectionVariables: {
      expectedTargetSurvivalDays,
      expectedTargetSurvival: isNumber(expectedTargetSurvival)
        ? (formatNumber(expectedTargetSurvival, { isPercentage: true, fractionDigits: 0, asNumber: true }) as number)
        : survivalValue,
      feedProjection: {
        type: projectedFeed?.type ?? 'fcr',
        expectedFeed: projectedFeed?.expectedFeed
      },
      growthProjection: {
        type: estimateGrowthType ?? 'xpertsea',
        estimatedGrowthDays,
        estimatedGrowthGrams
      },
      dailyMortalityPercent: isNumber(dailyMortalityPercent)
        ? (formatNumber(dailyMortalityPercent, { isPercentage: true, fractionDigits: 0, asNumber: true }) as number)
        : NaN,
      projectedSurvivalType: projectedSurvivalType ?? 'endOfCycle'
    }
  };

  return {
    survival,
    isLastMonitoringHasAllInfo,
    defaultValues,
    lastSurvivalCycleDays: formatNumber(lastSurvivalCycleDays, {
      lang,
      fractionDigits: 0,
      asNumber: true
    }) as number
  };
}
