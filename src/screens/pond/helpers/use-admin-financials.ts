import { convertUnitByMultiplication, formatNumber } from '@utils/number';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { usePermission } from '@hooks/use-permission';
import { PopulationHarvestPlanIndicators } from '@xpertsea/module-farm-sdk';
import { indicatorsColorMap } from '@components/pond-card/helper';
import { UserPreferences } from '@xpertsea/module-user-sdk';

interface AdminFinancialsProps {
  profitPerHaPerDay: number;
  costPerPound?: number;
  costPoundHarvest?: number;
  stockingCostsMillar?: number;
  totalRevenue: number;
  totalProfit: number;
  totalCosts?: number;
  stockingCosts?: number;
  feedCostPerKg?: number;
  cumulativeOverheadCosts?: number;
  cumulativeFeedCosts?: number;
  profitPerPound?: number;
  profitPoundHarvest?: number;
  rofi?: number;
  revenuePerPound: number;
  revenuePoundHarvest?: number;
  totalRevenuePound?: number;
  totalRevenuePoundHarvest?: number;
  indicators: PopulationHarvestPlanIndicators;
  profitProjectionView?: 'current' | 'harvest';
  farmId: string;
}

type GetAdminFinancialParams = Omit<AdminFinancialsProps, 'farmId'> & {
  isAdmin: boolean;
  isSupervisor: boolean;
  lang: string;
  unitsConfig: UserPreferences['unitsConfig'];
};

export const getAdminFinancial = (params: GetAdminFinancialParams) => {
  const {
    profitPerHaPerDay,
    costPerPound,
    costPoundHarvest,
    stockingCostsMillar,
    profitPerPound,
    profitPoundHarvest,
    totalRevenue,
    totalProfit,
    rofi,
    totalCosts,
    stockingCosts,
    feedCostPerKg,
    cumulativeOverheadCosts,
    cumulativeFeedCosts,
    revenuePerPound,
    revenuePoundHarvest,
    totalRevenuePound,
    totalRevenuePoundHarvest,
    indicators,
    profitProjectionView,
    isAdmin,
    lang,
    unitsConfig,
    isSupervisor
  } = params;
  const { trans } = getTrans();

  const indicatingColorLabelMap = {
    red: trans('t_worse_than_target'),
    green: trans('t_better_than_target'),
    blue: trans('t_on_track')
  };
  const isAdminOrSupervisor = isAdmin || isSupervisor;

  const { profitPerHaPerDay: indicatorProfitPerHaPerDay, costPerPound: indicatorCostPerPound } = indicators ?? {};

  const profitPerHaPerDayIndicator = indicatorProfitPerHaPerDay?.[profitProjectionView]?.color;
  const costPerPoundIndicator = indicatorCostPerPound?.[profitProjectionView]?.color;

  const profitPerHaPerDayTextColor = !isAdmin ? indicatorsColorMap[profitPerHaPerDayIndicator] : '';
  const profitPerHaPerDayUserTargetText = indicatingColorLabelMap[profitPerHaPerDayIndicator];
  const profitPerHaPerDayUserText = profitPerHaPerDayUserTargetText ?? 'N/A';

  const costPerPoundTextColor = !isAdminOrSupervisor ? indicatorsColorMap[costPerPoundIndicator] : '';
  const costPerPoundUserTargetText = indicatingColorLabelMap[costPerPoundIndicator];
  const costPerPoundUserText = costPerPoundUserTargetText ?? 'N/A';

  const profitPerHaPerDayFormatted = formatNumber(profitPerHaPerDay, { fractionDigits: 2, lang, isCurrency: true });
  const profitPerHaPerDayAdminValue = isAdmin ? profitPerHaPerDayFormatted : profitPerHaPerDayUserText;

  const costPerPoundFormatted = formatNumber(convertUnitByMultiplication(costPerPound, unitsConfig?.biomass), {
    fractionDigits: 2,
    lang,
    isCurrency: true
  });
  const costPerPoundAdminValue = isAdminOrSupervisor ? costPerPoundFormatted : costPerPoundUserText;

  const costPoundHarvestFormatted = formatNumber(convertUnitByMultiplication(costPoundHarvest, unitsConfig?.biomass), {
    fractionDigits: 2,
    lang,
    isCurrency: true
  });
  const costPoundHarvestAdminValue = isAdminOrSupervisor ? costPoundHarvestFormatted : costPerPoundUserText;

  const profitPerPoundFormatted = formatNumber(convertUnitByMultiplication(profitPerPound, unitsConfig?.biomass), {
    fractionDigits: 2,
    lang,
    isCurrency: true
  });
  const profitPerPoundAdminValue = isAdmin ? profitPerPoundFormatted : 'N/A';

  const profitPoundHarvestFormatted = formatNumber(
    convertUnitByMultiplication(profitPoundHarvest, unitsConfig?.biomass),
    {
      fractionDigits: 2,
      lang,
      isCurrency: true
    }
  );
  const profitPoundHarvestAdminValue = isAdmin ? profitPoundHarvestFormatted : 'N/A';

  const totalRevenueFormatted = formatNumber(totalRevenue, { fractionDigits: 0, lang, isCurrency: true });
  const totalRevenueAdminValue = isAdmin ? totalRevenueFormatted : 'N/A';

  const totalProfitFormatted = formatNumber(totalProfit, { fractionDigits: 0, lang, isCurrency: true });
  const totalProfitAdminValue = isAdmin ? totalProfitFormatted : 'N/A';

  const rofiFormatted = formatNumber(rofi, { fractionDigits: 1, lang, isPercentage: true });
  const rofiAdminValue = isAdmin ? rofiFormatted : 'N/A';

  const totalCostsFormatted = formatNumber(totalCosts, { fractionDigits: 0, lang, isCurrency: true });
  const totalCostsAdminValue = isAdminOrSupervisor ? totalCostsFormatted : 'N/A';

  const stockingCostsFormatted = formatNumber(stockingCosts, { fractionDigits: 0, lang, isCurrency: true });
  const stockingCostsAdminValue = isAdminOrSupervisor ? stockingCostsFormatted : 'N/A';

  const stockingCostsMillarFormatted = formatNumber(stockingCostsMillar, {
    fractionDigits: 2,
    lang,
    isCurrency: true
  });
  const stockingCostsMillarAdminValue = isAdminOrSupervisor ? stockingCostsMillarFormatted : 'N/A';

  const feedCostPerKgFormatted = formatNumber(feedCostPerKg, { fractionDigits: 3, lang, isCurrency: true });
  const feedCostPerKgAdminValue = isAdminOrSupervisor ? feedCostPerKgFormatted : 'N/A';

  const cumulativeOverheadCostsFormatted = formatNumber(cumulativeOverheadCosts, {
    fractionDigits: 0,
    lang,
    isCurrency: true
  });
  const cumulativeOverheadCostsAdminValue = isAdminOrSupervisor ? cumulativeOverheadCostsFormatted : 'N/A';

  const cumulativeFeedCostsFormatted = formatNumber(cumulativeFeedCosts, {
    fractionDigits: 0,
    lang,
    isCurrency: true
  });
  const cumulativeFeedCostsAdminValue = isAdminOrSupervisor ? cumulativeFeedCostsFormatted : 'N/A';

  const revenuePoundHarvestFormatted = formatNumber(
    convertUnitByMultiplication(revenuePoundHarvest, unitsConfig?.biomass),
    {
      fractionDigits: 2,
      lang,
      isCurrency: true
    }
  );
  const revenuePoundHarvestAdminValue = isAdmin ? revenuePoundHarvestFormatted : 'N/A';

  const revenuePerPoundFormatted = formatNumber(convertUnitByMultiplication(revenuePerPound, unitsConfig?.biomass), {
    fractionDigits: 2,
    lang,
    isCurrency: true
  });
  const revenuePerPoundAdminValue = isAdmin ? revenuePerPoundFormatted : 'N/A';

  const totalRevenuePerPoundFormatted = formatNumber(
    convertUnitByMultiplication(totalRevenuePound, unitsConfig?.biomass),
    { fractionDigits: 2, lang, isCurrency: true }
  );
  const totalRevenuePerPoundAdminValue = isAdmin ? totalRevenuePerPoundFormatted : 'N/A';

  const totalRevenuePoundHarvestFormatted = formatNumber(
    convertUnitByMultiplication(totalRevenuePoundHarvest, unitsConfig?.biomass),
    { fractionDigits: 2, lang, isCurrency: true }
  );
  const totalRevenuePoundHarvestAdminValue = isAdmin ? totalRevenuePoundHarvestFormatted : 'N/A';

  return {
    profitPerHaPerDayTextColor,
    costPerPoundTextColor,
    profitPerHaPerDayAdminValue,
    costPerPoundAdminValue,
    costPoundHarvestAdminValue,
    profitPerPoundAdminValue,
    profitPoundHarvestAdminValue,
    totalRevenueAdminValue,
    totalProfitAdminValue,
    rofiAdminValue,
    totalCostsAdminValue,
    revenuePerPoundAdminValue,
    revenuePoundHarvestAdminValue,
    totalRevenuePerPoundAdminValue,
    totalRevenuePoundHarvestAdminValue,
    stockingCostsAdminValue,
    stockingCostsMillarAdminValue,
    feedCostPerKgAdminValue,
    cumulativeOverheadCostsAdminValue,
    cumulativeFeedCostsAdminValue,
    isSupervisor,
    isAdmin
  };
};

export function useGetAdminFinancials(props: AdminFinancialsProps) {
  const {
    profitPerHaPerDay,
    costPerPound,
    costPoundHarvest,
    stockingCostsMillar,
    profitPerPound,
    profitPoundHarvest,
    totalRevenue,
    totalProfit,
    rofi,
    totalCosts,
    stockingCosts,
    feedCostPerKg,
    cumulativeOverheadCosts,
    cumulativeFeedCosts,
    revenuePerPound,
    revenuePoundHarvest,
    totalRevenuePound,
    totalRevenuePoundHarvest,
    indicators,
    profitProjectionView = 'current',
    farmId
  } = props;

  const user = useAppSelector((state) => state.auth.user);
  const lang = useAppSelector((state) => state.app?.lang);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};

  const isAdmin = usePermission({
    role: 'admin',
    entity: 'farm',
    entityId: farmId
  });

  const isSupervisor = usePermission({
    role: 'supervisor',
    entity: 'farm',
    entityId: farmId
  });

  const financialData = getAdminFinancial({
    profitPerHaPerDay,
    costPerPound,
    costPoundHarvest,
    stockingCostsMillar,
    profitPerPound,
    profitPoundHarvest,
    totalRevenue,
    totalProfit,
    rofi,
    totalCosts,
    stockingCosts,
    feedCostPerKg,
    cumulativeOverheadCosts,
    cumulativeFeedCosts,
    revenuePerPound,
    revenuePoundHarvest,
    totalRevenuePound,
    totalRevenuePoundHarvest,
    indicators,
    profitProjectionView,
    isAdmin,
    lang,
    unitsConfig,
    isSupervisor
  });

  return financialData;
}

export type calculateAdminFinancialsFnType = (
  params: Omit<AdminFinancialsProps, 'farmId'>
) => ReturnType<typeof useGetAdminFinancials>;

export function useCalculateAdminFinancials({ farmId }: { farmId: string }) {
  const user = useAppSelector((state) => state.auth.user);
  const lang = useAppSelector((state) => state.app?.lang);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};

  const isAdmin = usePermission({
    role: 'admin',
    entity: 'farm',
    entityId: farmId
  });

  const isSupervisor = usePermission({
    role: 'supervisor',
    entity: 'farm',
    entityId: farmId
  });

  const calculateAdminFinancials: calculateAdminFinancialsFnType = (params: Omit<AdminFinancialsProps, 'farmId'>) => {
    return getAdminFinancial({
      ...params,
      isAdmin,
      isSupervisor,
      lang,
      unitsConfig
    });
  };

  return { calculateAdminFinancials };
}
