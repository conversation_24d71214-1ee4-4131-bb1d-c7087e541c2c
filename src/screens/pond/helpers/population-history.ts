import { PopulationHistory } from '@xpertsea/module-farm-sdk';
import { DateTime } from 'luxon';
import { WeekDays } from '@components/form/form-days-picker';
import isEmpty from 'lodash/isEmpty';

export function getHistoryWeeklyData(history: PopulationHistory[], monitoringDays: WeekDays[], feedingDay: WeekDays) {
  if (!history?.length) {
    return [];
  }

  const daysOfWeek: WeekDays[] = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

  const historySorted = [...history].sort((a, b) => a.date.localeCompare(b.date));

  const weekData: Record<string, PopulationHistory> = {};
  const feedingDayList: WeekDays[] = feedingDay ? [feedingDay] : ['monday'];
  // first try to get the history data based on the monitoring days, if not available, try to get it based on the start of the week day or monday
  const baseDays: WeekDays[] = monitoringDays.length ? monitoringDays : feedingDayList;
  const monitoringDaysIndexes = baseDays.map((day: WeekDays) => daysOfWeek.indexOf(day) + 1);

  const generateWeekData = (daysIndexes: number[] = monitoringDaysIndexes) => {
    historySorted.forEach((item) => {
      const dateLuxon = DateTime.fromISO(item.date).startOf('day');
      const weekNumber = dateLuxon.weekNumber;
      const year = dateLuxon.year;
      const weekYearKey = `${year}-W${weekNumber}`;

      if (
        daysIndexes.includes(dateLuxon.weekday) &&
        (!weekData[weekYearKey] || DateTime.fromISO(weekData[weekYearKey].date).startOf('day') < dateLuxon)
      ) {
        weekData[weekYearKey] = item;
      }
    });
  };

  generateWeekData();

  if (isEmpty(weekData)) {
    const startOfWeekIndexes = feedingDayList.map((day: WeekDays) => daysOfWeek.indexOf(day) + 1);
    generateWeekData(startOfWeekIndexes);
  }

  return Object.values(weekData);
}
