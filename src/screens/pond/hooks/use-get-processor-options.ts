import { useAppSelector } from '@redux/hooks';

export function useGetProcessorOptions() {
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);
  const { processorPriceLists } = currentFarm ?? {};
  const activeProcessorPriceLists = processorPriceLists?.filter((item) => item.status === 'active');

  const processorOptions =
    activeProcessorPriceLists?.map((item) => ({ value: item._id as string, label: item.name })) ?? [];

  return { processorOptions, activeProcessorPriceLists };
}
