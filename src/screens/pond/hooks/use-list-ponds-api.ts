import { IApiHookInput, ICallbackParams, IHookInputType, useApiHookWrapper } from '@hooks/use-api-hook-wrapper';
import { getTrans } from '@i18n/get-trans';
import { addToastAction } from '@redux/app';
import {
  setCurrentFarmPondsAction,
  setErrorCurrentFarmPondsAction,
  setIsFinishedOnceLoadingCurrentFarmPondsAction,
  setIsLoadingCurrentFarmPondsAction,
  setIsLoadingPondsDataAction,
  setPondsDataAction
} from '@redux/farm';
import { listPondsApi } from '@screens/pond/apis/list-ponds-api';
import { moduleFarm } from '@sdks/module-farm';
import { moduleUser } from '@sdks/module-user';
import { throwErrorMsg } from '@utils/errors';
import { Head, PopulationRequest, QueryRequest, v1ListPondsOutputRequest } from '@xpertsea/module-farm-sdk';
import {
  harvestCommonGql,
  harvestPlanCommonGql,
  historyGqlData,
  partialHarvestCommonGql,
  partialHarvestPlannedCommonGql,
  productionPredictionCommonGql
} from '@screens/population/apis/common-queries';
import keyBy from 'lodash/keyBy';

async function mountFn(props: ICallbackParams<Input>) {
  const { input, dispatch, successCallback } = props;

  const { loadRelated, params, fields, extraFields, shouldListAllPonds = false, isSummaryData = false } = input;
  const setIsLoadingPondsAction = shouldListAllPonds ? setIsLoadingPondsDataAction : setIsLoadingCurrentFarmPondsAction;
  const setPondsAction = shouldListAllPonds ? setPondsDataAction : setCurrentFarmPondsAction;

  dispatch(setIsLoadingPondsAction(true));

  const { data, errors } = await listPondsApi({ params, fields });

  if (errors) {
    throwErrorMsg(errors);
  }

  const pondsRes = data.v1ListPonds;

  if (!loadRelated) {
    dispatch(
      setPondsAction({
        ponds: pondsRes.ponds,
        totalCurrentFarmPonds: pondsRes.totalRecords
      })
    );
    dispatch(setIsLoadingPondsAction(false));
    dispatch(setIsFinishedOnceLoadingCurrentFarmPondsAction(true));
    successCallback?.();
    return { pondsRes };
  }

  const populationIds: string[] = [];
  const userIds: string[] = [];

  pondsRes?.ponds?.forEach((pond) => {
    if (pond.currentPopulationId) populationIds.push(pond.currentPopulationId);
    if (pond.superVisorId && !userIds.includes(pond.superVisorId)) userIds.push(pond.superVisorId);
  });

  if (!populationIds.length) {
    dispatch(
      setPondsAction({
        ponds: pondsRes.ponds,
        totalCurrentFarmPonds: pondsRes.totalRecords
      })
    );
    dispatch(setIsLoadingPondsAction(false));
    dispatch(setIsFinishedOnceLoadingCurrentFarmPondsAction(true));
    successCallback?.();
    return { pondsRes };
  }

  const { data: population, errors: populationErrors } = await moduleFarm.query({
    v1ListPopulations: [
      {
        filter: {
          farmId: params.filter.farmId,
          id: populationIds,
          isSummaryData
        },
        page: {
          current: 1,
          size: 10000
        }
      },
      {
        populations: {
          _id: 1,
          createdAt: 1,
          cycle: 1,
          stockingCostsMillar: 1,
          dryDaysBeforeStocking: 1,
          stockedAt: 1,
          stockedAtTimezone: 1,
          hatcheryName: 1,
          geneticName: 1,
          kampiMortality: 1,
          averageHatcheryWeight: 1,
          hatcheryHarvestDate: 1,
          seedingAverageWeight: 1,
          seedingQuantity: 1,
          stockingAreaDensity: 1,
          farmId: 1,
          lastMonitoringDate: 1,
          lastMonitoringDateTimezone: 1,
          lastMonitoringIds: 1,
          isMonitoringStarted: 1,
          lastMonitoringDistance: 1,
          growthTarget: {
            productionDays: 1,
            weight: 1,
            updatedAt: 1,
            updatedBy: 1
          },
          lastFcr: 1,
          lastMonitoringMlResult: {
            averageWeight: 1,
            total: 1,
            weightCv: 1,
            weightDistribution: 1
          },
          lastMonitoringStatus: 1,
          lastWeekGrowth: 1,
          lastWeeklyGrowthDays: 1,
          pondId: 1,
          growthBandsStatus: 1,
          predictionStatus: 1,
          notes: {
            note: 1,
            createdAt: 1
          },
          growthBands: {
            alert: {
              isRed: 1,
              isYellow: 1,
              isGreen: 1,
              caseNumber: 1,
              lastPrediction: {
                date: 1,
                averageWeight: 1
              }
            },
            targetWeightPredictedDate: 1,
            growthBandsError: 1,
            targetWeightPredictedDateError: 1,
            startDate: 1,
            targetDate: 1,
            targetWeight: 1
          },
          survivalRate: 1,
          symmetry: {
            alert: {
              isGreen: 1,
              isYellow: 1,
              isRed: 1
            },
            averageWeight: 1,
            estimatedRevenueLoss: 1,
            heuristicAverageWeight: 1,
            lessThanAvgPercent: 1,
            lessThanHeuristicAvgPercent: 1,
            heuristicGramsBelowABW: 1,
            type: 1
          },
          partialHarvest: partialHarvestCommonGql,
          partialHarvestPlanned: partialHarvestPlannedCommonGql,
          harvest: harvestCommonGql,
          harvestPlan: harvestPlanCommonGql,
          manualAverageWeights: {
            date: 1,
            updatedBy: 1,
            averageWeight: 1
          },
          history: historyGqlData,
          cycleInformation: {
            carryingCapacity: 1,
            target: {
              fcr: 1,
              cycleLength: 1,
              harvestWeight: 1,
              biomassToHarvest: 1
            },
            projection: {
              overheadCostsPerHaPerDay: 1,
              projectedGrowth: { type: 1, days: 1, grams: 1 },
              projectedFeed: {
                type: 1,
                expectedFeed: 1,
                farmFeedTypes: {
                  id: 1,
                  percentage: 1
                },
                farmFeedTableId: 1,
                feedAggressionMultiplier: 1
              },
              projectedSurvival: {
                dailyMortalityPercent: 1,
                projectedSurvivalType: 1,
                expectedTargetSurvival: 1,
                expectedTargetSurvivalDays: 1
              }
            }
          },
          prediction: { date: 1, averageWeight: 1 },
          productionPrediction: productionPredictionCommonGql,
          metadata: 1,
          estimatedSurvival: 1,
          estimatedFeed: 1,
          feedData: { date: 1, totalLbs: 1, otherDirectCost: 1, feed: { totalLbs: 1, feedTypeId: 1 } },
          dailyParameters: {
            _id: 1,
            date: 1,
            params: {
              _id: 1,
              paramId: 1,
              value: 1
            }
          },
          ...(extraFields?.population || {})
        }
      }
    ]
  });

  if (populationErrors) {
    throwErrorMsg(populationErrors);
  }

  population?.v1ListPopulations?.populations?.forEach((population) => {
    if (population?.harvestPlan?.approvedBy && !userIds.includes(population?.harvestPlan?.approvedBy)) {
      userIds.push(population.harvestPlan.approvedBy);
    }

    if (population?.harvestPlan?.createdBy && !userIds.includes(population?.harvestPlan?.createdBy)) {
      userIds.push(population.harvestPlan.createdBy);
    }
  });

  const populationsMap = keyBy(population?.v1ListPopulations?.populations, '_id');

  const pondsList = pondsRes?.ponds?.map((pond) => {
    return {
      ...pond,
      currentPopulation: populationsMap[pond.currentPopulationId]
    };
  });

  const pondsResWithPopulation = {
    ...pondsRes,
    ponds: pondsList
  };

  if (!userIds.length) {
    dispatch(
      setPondsAction({
        ponds: pondsRes.ponds,
        populations: population.v1ListPopulations.populations,
        totalCurrentFarmPonds: pondsRes.totalRecords
      })
    );
    dispatch(setIsLoadingPondsAction(false));
    dispatch(setIsFinishedOnceLoadingCurrentFarmPondsAction(true));
    successCallback?.();
    return { pondsRes: pondsResWithPopulation };
  }

  const { data: usersRes, errors: userErrors } = await moduleUser.query({
    v1ListUsers: [{ filter: { id: userIds } }, { users: { _id: 1, email: 1, firstName: 1, lastName: 1 } }]
  });

  if (userErrors) {
    throwErrorMsg(userErrors);
  }

  const { data: v1ListUserMembershipData, errors: v1ListUserMembershipDataErrors } = await moduleUser.query({
    v1ListUserMembership: [
      {
        page: { size: 10000 },
        filter: { userId: userIds, entityId: params.filter.farmId }
      },
      { userMemberships: { _id: true, userId: true, role: true, isDeactivated: true, metadata: true } }
    ]
  });

  if (v1ListUserMembershipDataErrors) {
    throwErrorMsg(v1ListUserMembershipDataErrors[0].message);
  }

  const activeMemberships = v1ListUserMembershipData.v1ListUserMembership.userMemberships?.filter(
    (item) => !item.isDeactivated
  );

  const userMembershipsMap = keyBy(activeMemberships, 'userId');

  dispatch(
    setPondsAction({
      users: usersRes.v1ListUsers.users,
      userMembershipsMap,
      ponds: pondsRes.ponds,
      populations: population.v1ListPopulations.populations,
      totalCurrentFarmPonds: pondsRes.totalRecords
    })
  );
  dispatch(setIsLoadingPondsAction(false));
  dispatch(setIsFinishedOnceLoadingCurrentFarmPondsAction(true));

  successCallback?.();

  return { pondsRes, pondsResWithPopulation, userMembershipsMap };
}

async function errorFn(props: ICallbackParams<Input>) {
  const { dispatch, error } = props;
  dispatch(setErrorCurrentFarmPondsAction(error));
  dispatch(setIsLoadingCurrentFarmPondsAction(false));
  dispatch(setIsLoadingPondsDataAction(false));
  dispatch(setIsFinishedOnceLoadingCurrentFarmPondsAction(true));
  const { trans } = getTrans();
  dispatch(addToastAction({ type: 'error', title: trans('t_error'), msg: error }));
}

export type ExtraFieldsType = { population: Partial<PopulationRequest> };

type Input = IApiHookInput<Head<QueryRequest['v1ListPonds']>, v1ListPondsOutputRequest> & {
  extraFields?: ExtraFieldsType;
  shouldListAllPonds?: boolean;
  isSummaryData?: boolean;
};
type Output = Awaited<ReturnType<typeof mountFn>>;

export function useListPondsApi(props?: IHookInputType<Input, Output>) {
  return useApiHookWrapper({
    mountFn,
    errorFn,
    initialInput: props,
    initialIsLoading: !!props,
    skipInitialApiCallOnEmptyInputs: true,
    unmountFn: undefined,
    __t: props?.__t, // cache buster key
    __cachePrefix: props?.__cachePrefix, // cache key prefix
    __cacheTTL: props?.__cacheTTL // cache ttl in seconds
  });
}
