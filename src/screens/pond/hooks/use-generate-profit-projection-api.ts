import { IApiHookInput, ICallbackParams, IHookInputType, useApiHookWrapper } from '@hooks/use-api-hook-wrapper';
import { getTrans } from '@i18n/get-trans';
import { addToastAction } from '@redux/app';
import { moduleFarm } from '@sdks/module-farm';
import { throwErrorMsg } from '@utils/errors';
import {
  Head,
  MutationRequest,
  PopulationHarvestPlanProjectionProfitProjectionDataRequest,
  v1GenerateProfitProjectionOutputRequest
} from '@xpertsea/module-farm-sdk';
import { setIsLoadingScenarioProfitProjectionAction, setScenarioProfitProjectionAction } from '@redux/farm';
import {
  commercialClassBreakdownCommonGql,
  productionPredictionCommonGql
} from '@screens/population/apis/common-queries';

const harvestDateFields: PopulationHarvestPlanProjectionProfitProjectionDataRequest = {
  date: 1,
  headon: {
    biomassLbs: 1,
    revenuePerPound: 1,
    revenuePoundHarvest: 1,
    profitPerHaPerDay: 1,
    totalProfit: 1,
    rofi: 1,
    totalRevenue: 1,
    liveTotalRevenue: 1,
    totalRevenuePound: 1,
    totalRevenuePoundHarvest: 1,
    costPerPound: 1,
    profitPerPound: 1,
    profitPoundHarvest: 1,
    biomassKg: 1,
    biomassProcessedLbs: 1
  },
  headless: {
    biomassLbs: 1,
    revenuePerPound: 1,
    revenuePoundHarvest: 1,
    profitPerHaPerDay: 1,
    totalProfit: 1,
    rofi: 1,
    totalRevenue: 1,
    liveTotalRevenue: 1,
    totalRevenuePound: 1,
    totalRevenuePoundHarvest: 1,
    costPerPound: 1,
    profitPerPound: 1,
    profitPoundHarvest: 1,
    biomassKg: 1,
    biomassProcessedLbs: 1
  }
};

async function mountFn(props: ICallbackParams<Input>) {
  const { input, successCallback, dispatch } = props;

  const {
    params,
    fields = {
      distributionPrediction: { date: 1, distribution: 1 },
      productionCommercialClasses: {
        processorId: 1,
        commercialClassesBreakdown: {
          date: 1,
          headless: commercialClassBreakdownCommonGql,
          headon: commercialClassBreakdownCommonGql
        }
      },
      productionPrediction: productionPredictionCommonGql,
      projections: {
        optimalHarvestIdx: 1,
        plannedHarvestIdx: 1,
        processingStatus: 1,
        processingStatusReasons: 1,
        profitProjectionData: harvestDateFields,
        processorId: 1,
        updatedAt: 1
      },
      indicators: {
        abw: {
          harvest: {
            color: 1,
            val: 1
          },
          current: {
            color: 1,
            val: 1
          }
        },
        growth: {
          harvest: {
            color: 1,
            val: 1
          },
          current: {
            color: 1,
            val: 1
          }
        },
        biomassLbsByHa: {
          harvest: {
            color: 1,
            val: 1
          },
          current: {
            color: 1,
            val: 1
          }
        },
        costPerPound: {
          harvest: {
            color: 1,
            val: 1
          },
          current: {
            color: 1,
            val: 1
          }
        },
        fcr: {
          harvest: {
            color: 1,
            val: 1
          },
          current: {
            color: 1,
            val: 1
          }
        },
        growthLinear: {
          harvest: {
            color: 1,
            val: 1
          },
          current: {
            color: 1,
            val: 1
          }
        },
        profitPerHaPerDay: {
          harvest: {
            color: 1,
            val: 1
          },
          current: {
            color: 1,
            val: 1
          }
        },
        survivalRate: {
          harvest: {
            color: 1,
            val: 1
          },
          current: {
            color: 1,
            val: 1
          }
        }
      }
    } as v1GenerateProfitProjectionOutputRequest
  } = input;
  const { shouldUpdateRedux = true, ...rest } = params;

  if (shouldUpdateRedux) {
    dispatch(setIsLoadingScenarioProfitProjectionAction(true));
  }

  const { data, errors } = await moduleFarm.mutation({
    v1GenerateProfitProjection: [rest, fields]
  });

  if (errors) {
    dispatch(setIsLoadingScenarioProfitProjectionAction(false));
    throwErrorMsg(errors);
  }

  const generateProfitProjectionRes = data?.v1GenerateProfitProjection;
  if (shouldUpdateRedux) {
    dispatch(setScenarioProfitProjectionAction(generateProfitProjectionRes));
    dispatch(setIsLoadingScenarioProfitProjectionAction(false));
  }
  successCallback?.(generateProfitProjectionRes);

  return generateProfitProjectionRes;
}

async function errorFn(props: ICallbackParams<Input>) {
  const { dispatch, error } = props;
  const { trans } = getTrans();
  dispatch(addToastAction({ type: 'error', title: trans('t_error'), msg: error }));
}

type Input = IApiHookInput<
  Head<MutationRequest['v1GenerateProfitProjection']> & { shouldUpdateRedux?: boolean },
  v1GenerateProfitProjectionOutputRequest
>;
type Output = Awaited<ReturnType<typeof mountFn>>;

export function useGenerateProfitProjection(props?: IHookInputType<Input, Output>) {
  return useApiHookWrapper({
    mountFn,
    errorFn,
    initialInput: props,
    initialIsLoading: !!props,
    skipInitialApiCallOnEmptyInputs: true,
    unmountFn: undefined,
    __t: props?.__t, // cache buster key
    __cachePrefix: props?.__cachePrefix, // cache key prefix
    __cacheTTL: props?.__cacheTTL // cache ttl in seconds
  });
}
