import { Box, Flex, FlexProps, Text } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { HotTableDynamicComponent } from '@components/hot-table/hot-table-dynamic';
import { HotColumn, type HotTableProps } from '@handsontable/react-wrapper';
import { getTrans } from '@i18n/get-trans';
import { ReactNode, useState } from 'react';
import '@handsontable/pikaday/css/pikaday.css';
import { numberTransform, useYupSchema, Yup, yupFormResolver, YupResolverType } from '@utils/yup';
import { useFieldArray, useForm } from 'react-hook-form';
import { AddRemoveFields } from '@screens/settings/components/add-form-fields';
import { DateTime } from 'luxon';
import { useBulkCreateManualMonitoringApi } from '@screens/farm/hooks/use-bulk-create-manual-monitoring';
import { useReloadCurrentPond } from '@screens/pond/hooks/use-reload-current-pond';
import { formatNumber } from '@utils/number';
import { useAppSelector } from '@redux/hooks';
import Handsontable from 'handsontable';
import { ValidationError } from 'yup';
import { HotTableStylesWrapper } from '@components/hot-table/hot-table-styles-wrapper';
import { FormControlDateInput } from '@components/form/form-control-date-input';
import { AddEditPondProjectionModal } from '@screens/pond/components/modals/add-edit-pond-projection-modal';
import { AdminOrSupervisorOrOperationWrapper } from '@components/permission-wrappers/admin-or-supervisor-or-operation-wrapper';

type GrowthFormSchemaType = { manualWeeklyGrowth: { date: string; averageWeight: number; survival?: number }[] };

interface ManualWeeklyGrowthProps {
  stockedAt: string;
  stockedAtTimezone: string;
  populationId: string;
}
export function ManualWeeklyGrowth(props: ManualWeeklyGrowthProps) {
  const { stockedAt, stockedAtTimezone, populationId } = props;
  const { trans } = getTrans();
  const [isFormActive, setIsFormActive] = useState(false);
  const [{ isLoading }, createManualMonitoring] = useBulkCreateManualMonitoringApi();
  const { reloadCurrentPond } = useReloadCurrentPond();
  const productOffering = useAppSelector((state) => state.farm?.currentFarm?.metadata?.productOffering);

  const isVisionOnly = productOffering === 'visionOnly';

  const handleOnSubmit = (data: GrowthFormSchemaType['manualWeeklyGrowth']) => {
    createManualMonitoring({
      params: {
        monitorings: data.map((ele) => ({
          set: {
            populationId,
            startedAt: ele.date,
            survivalRate: ele.survival ? ele.survival / 100 : undefined,
            averageWeight: ele.averageWeight
          }
        }))
      },
      successCallback: () => {
        reloadCurrentPond();
      }
    });
  };

  if (!isFormActive) {
    return (
      <Box
        bg='white'
        py='md'
        px='sm-alt'
        borderRadius='xl'
        shadow='0px 1px 3px rgba(0, 0, 0, 0.1),0px 1px 2px rgba(0, 0, 0, 0.06)'
        w='500px'
      >
        <Text fontWeight={500} mb='sm-alt'>
          {trans('t_no_growth_data')}
        </Text>
        <Text fontSize='md' mb='sm-alt' w='436px'>
          {trans('t_enter_growth_data_or_monitor_pond')}
        </Text>
        <Flex gap='md' align='center'>
          <BaseButton minW='179px' size='sm' onClick={() => setIsFormActive(true)} fontWeight={500} w='fit-content'>
            {trans('t_enter_weekly_growth_data')}
          </BaseButton>

          {!isVisionOnly && (
            <AdminOrSupervisorOrOperationWrapper>
              <AddEditPondProjectionModal>
                <BaseButton size='sm'>{trans('t_manage_projections')}</BaseButton>
              </AddEditPondProjectionModal>
            </AdminOrSupervisorOrOperationWrapper>
          )}
        </Flex>
      </Box>
    );
  }

  return (
    <WeeklyGrowthForm
      onCancel={() => setIsFormActive(false)}
      stockedAt={stockedAt}
      stockedAtTimezone={stockedAtTimezone}
      onSubmit={handleOnSubmit}
      isLoading={isLoading}
      isVisionOnly={isVisionOnly}
    />
  );
}

type FormPropsType = {
  stockedAt: string;
  stockedAtTimezone: string;
  onCancel?: () => void;
  onSubmit: (val: GrowthFormSchemaType['manualWeeklyGrowth']) => void;
  isLoading?: boolean;
  defaultValues?: GrowthFormSchemaType['manualWeeklyGrowth'];
  isVisionOnly?: boolean;
};

function WeeklyGrowthForm(props: FormPropsType) {
  const { stockedAt, stockedAtTimezone, isLoading, onSubmit, onCancel, defaultValues, isVisionOnly } = props;
  const { trans } = getTrans();
  const [isSubmittedOnce, setIsSubmittedOnce] = useState(false);
  const lang = useAppSelector((state) => state.app.lang);
  const stockedAtDate = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone }).startOf('day');
  const todayDateLuxon = DateTime.local().startOf('day');

  const { schema } = useYupSchema({
    manualWeeklyGrowth: Yup.array().of(
      Yup.object().shape({
        date: Yup.string()
          .test((value, { path }) => {
            if (!value) {
              throw new ValidationError(trans('yup_is_required', { label: trans('t_date') }), value, path);
            }

            const valueLuxon = DateTime.fromISO(value).startOf('day');

            if (valueLuxon > todayDateLuxon) {
              throw new ValidationError(
                trans('yup_date_must_not_be_later_than', {
                  label: trans('t_date'),
                  date: todayDateLuxon.toFormat('MMM dd, yyyy', { locale: lang })
                }),
                value,
                path
              );
            }

            if (valueLuxon < stockedAtDate) {
              throw new ValidationError(
                trans('yup_date_must_not_be_earlier_than', {
                  label: trans('t_date'),
                  date: stockedAtDate.toFormat('MMM dd, yyyy', { locale: lang })
                }),
                value,
                path
              );
            }

            return true;
          })
          .required()
          .label('t_date'),
        averageWeight: Yup.number().transform(numberTransform).required().label('t_abw_g').positive(),
        survival: Yup.number().transform(numberTransform).nullable().min(0).max(150).label('t_survival')
      })
    )
  });

  const {
    control,
    handleSubmit,
    formState: { errors },
    trigger,
    setValue,
    watch
  } = useForm<GrowthFormSchemaType>({
    mode: 'onChange',
    resolver: yupFormResolver(schema) as YupResolverType<GrowthFormSchemaType>,
    defaultValues: {
      manualWeeklyGrowth: defaultValues ?? [{}, {}, {}]
    }
  });

  const { fields, append, remove, replace } = useFieldArray({
    control,
    name: 'manualWeeklyGrowth'
  });

  const gridHeaders = [trans('t_date'), `${trans('t_abw_g')}`, `${trans('t_survival')} (${trans('t_optional')})`];

  const cellsErrors = Array.isArray(errors?.manualWeeklyGrowth)
    ? errors?.manualWeeklyGrowth?.flatMap((error, index) => {
        if (!error?.date && !error?.averageWeight && !error?.survival) return [];
        return [error?.date?.message, error?.averageWeight?.message, error?.survival?.message]
          .flatMap((errorMsg: string, colIndex): HotTableProps['cell'][0] => {
            const isRequiredError = errorMsg?.includes('required');
            if (!errorMsg) return null;
            return {
              row: index,
              col: colIndex,
              valid: false,
              comment: {
                value: isRequiredError ? ' ' : errorMsg,
                style: isRequiredError ? { width: 1, height: 1 } : { width: 180, height: 80 },
                readOnly: true
              }
            };
          })
          .filter((val) => !!val);
      })
    : [];

  const handleOnSubmit = handleSubmit((data: GrowthFormSchemaType) => {
    onSubmit(data.manualWeeklyGrowth);
  });

  return (
    <Flex
      direction='column'
      gap='sm-alt'
      as='form'
      onSubmit={handleOnSubmit}
      position='relative'
      p='md'
      rounded='xl'
      bgColor='white'
      shadow='0px 1px 3px rgba(0, 0, 0, 0.1),0px 1px 2px rgba(0, 0, 0, 0.06)'
      w='600px'
    >
      <Flex direction='column' gap='xs-alt'>
        <Text fontSize='md' fontWeight={500} color='gray.700'>
          {trans('t_hint_you_can_copy_and_paste_from_excel')}
        </Text>
        {!!errors?.manualWeeklyGrowth?.length && (
          <Text color='shrimpyPinky.600' fontSize='md' fontWeight={500}>
            {trans('t_manual_weight_generic_validation')}
          </Text>
        )}
      </Flex>

      <HotTableStylesWrapper
        pos='static'
        display='flex'
        css={{
          '& .handsontable tr td:first-of-type': {
            position: 'relative',
            bgColor: 'white',
            borderStart: '1px solid'
          },
          '& .handsontable td ': {
            overflow: 'visible !important'
          },
          '& .ht-wrapper.handsontable.htColumnHeaders ': {
            overflow: 'visible !important'
          },
          '& .ht_master.handsontable ': {
            overflow: 'visible !important'
          },
          '& .handsontable.ht-wrapper:before ': {
            overflow: 'visible !important'
          },
          '& .ht_master .wtHolder': {
            overflow: 'visible !important'
          },
          '& .handsontable .htCommentCell': {
            border: '0.5px solid !important',
            borderColor: 'red.500 !important'
          },
          '& .handsontable .htCommentCell:after': {
            borderTopColor: 'red.500 !important'
          }
        }}
      >
        <HotTableDynamicComponent
          width='80%'
          preventOverflow={false}
          data={fields}
          colHeaders={gridHeaders}
          onChange={(data) => {
            const watchedFields = watch('manualWeeklyGrowth');
            const fieldsData = data.map((field, index) => {
              const currentField = watchedFields[index];
              return { ...currentField, averageWeight: +field[1] || null, survival: +field[2] || null };
            });
            replace(fieldsData);
            isSubmittedOnce && trigger('manualWeeklyGrowth');
          }}
          cell={cellsErrors}
          rowHeights={36}
          columnHeaderHeight={36}
          comments={true}
          contextMenu={{
            items: {
              copy: {},
              cut: {},
              undo: {},
              redo: {}
            }
          }}
        >
          <HotColumn
            data='date'
            readOnly
            width={170}
            allowInvalid={true}
            renderer={(props) => {
              const { row } = props ?? {};

              return (
                <ValueWrapper position='static'>
                  <FormControlDateInput
                    control={control}
                    displayAsDropdown
                    hideDropdownIcon
                    placement='right'
                    dateFormat='MMMM dd, yyyy'
                    id='date'
                    placeholder={trans('t_select_date')}
                    name={`manualWeeklyGrowth.${row}.date`}
                    inputProps={{ textStyle: 'paragraph.200.light', color: 'text.gray' }}
                    minDate={stockedAtDate.toJSDate()}
                    maxDate={todayDateLuxon.toJSDate()}
                    onChange={(value) => {
                      const isoDate = DateTime.fromJSDate(value).toISO();
                      setValue(`manualWeeklyGrowth.${row}.date`, isoDate);
                    }}
                    error={errors?.manualWeeklyGrowth?.[row]?.message?.toString()}
                  />
                </ValueWrapper>
              );
            }}
          />

          <HotColumn data='averageWeight' allowInvalid={true} width={120} renderer={AverageWeightRenderer} />

          {!isVisionOnly && <HotColumn data='survival' allowInvalid={true} width={150} renderer={SurvivalRenderer} />}
        </HotTableDynamicComponent>

        <Box w='20%'>
          <Box height='36px' />
          {fields.map((field, index) => {
            const isLastElement = index === fields.length - 1;
            const showDelete = fields.length > 1;
            return (
              <Flex height='36px' align='center' px='sm-alt' key={field.id}>
                <AddRemoveFields
                  appendFields={() => append({ date: null, averageWeight: null })}
                  addButtonProps={{
                    left: '10px',
                    bottom: '16px',
                    variant: 'link',
                    showLeftIcon: true,
                    minW: 'fit-content',
                    position: 'absolute',
                    addButtonText: trans('t_add_weight')
                  }}
                  removeFields={() => remove(index)}
                  control={control}
                  name={[`manualWeeklyGrowth.${index}.date`, `manualWeeklyGrowth.${index}.averageWeight`]}
                  showAdd={isLastElement}
                  showDelete={showDelete}
                />
              </Flex>
            );
          })}
        </Box>
      </HotTableStylesWrapper>

      <Flex justify='flex-end' gap='sm-alt' me='101px' align='center'>
        <BaseButton
          variant='link'
          size='sm'
          colorPalette='black'
          onClick={onCancel}
          disabled={isLoading}
          fontWeight={500}
        >
          {trans('t_cancel')}
        </BaseButton>
        <BaseButton
          size='sm'
          type='submit'
          px='xs'
          minW='44px'
          loading={isLoading}
          onClick={() => setIsSubmittedOnce(true)}
        >
          {trans('t_save_changes')}
        </BaseButton>
      </Flex>
    </Flex>
  );
}

type CellRendererProps = { value?: Handsontable.CellValue; cellProperties?: Handsontable.CellProperties };

function AverageWeightRenderer(props: CellRendererProps) {
  const { value } = props;

  const lang = useAppSelector((state) => state.app?.lang);

  return <ValueWrapper>{value ? formatNumber(+value, { fractionDigits: 2, lang }) : '-'}</ValueWrapper>;
}

function SurvivalRenderer(props: CellRendererProps) {
  const { value } = props;

  const lang = useAppSelector((state) => state.app?.lang);

  return <ValueWrapper>{value ? `${formatNumber(+value, { fractionDigits: 0, lang })}%` : '-'}</ValueWrapper>;
}

type ValueWrapperProps = FlexProps & {
  children: ReactNode;
};

function ValueWrapper(props: ValueWrapperProps) {
  const { children, ...rest } = props;

  return (
    <Flex align='center' justify='center' overflow='visible' direction='column' {...rest}>
      {children}
    </Flex>
  );
}
