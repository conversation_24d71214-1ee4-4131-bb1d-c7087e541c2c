import { useReloadCurrentPond } from '@screens/pond/hooks/use-reload-current-pond';
import { useAppSelector } from '@redux/hooks';
import { getPondState } from '@screens/pond/helpers/get-pond-state';
import { getTrans } from '@i18n/get-trans';
import { useUnharvestPondApi } from '@screens/pond/hooks/use-unharvest-pond-api';
import { Flex } from '@chakra-ui/react';
import { AddEditFinalHarvestModal } from '@screens/population/components/harvest-modals/add-edit-final-harvest-modal';
import { isDevApp } from '@utils/app-links';
import { BaseLink } from '@components/base/base-link';
import { CreateManualMonitoringModal } from '@screens/population/components/create-manual-monitoring-modal';
import { AddEditPondModal } from '@screens/pond/components/modals/add-edit-pond-modal';
import { AddNotesModal } from '@screens/pond/components/modals/add-notes-modal';
import { ArchivePondModal } from '@screens/pond/components/modals/archive-pond-modal';
import { AddPondStockingModal } from '@screens/pond/components/modals/add-edit-pond-stocking/add-pond-stocking-modal';
import { DevGenerateHistoryButton } from '@screens/pond/components/pond-overview/dev-generate-history-button';
import { DataCheckButton } from '@screens/pond/components/pond-overview/data-check-button';
import { ChevronDownFilled } from '@icons/chevron-down/chevron-down-filled';
import { MenuButton, MenuContent, MenuItem, MenuRoot } from '@components/ui/menu';
import { useRouter } from 'next/router';
import { getPondActiveTab } from '@screens/pond/helpers/pond-links-bar';
import { useAnalytics } from '@hooks/use-analytics';
import { actionsName } from '@utils/segment';
import { ManagePartialHarvestModal } from '@screens/population/components/harvest-modals/manage-partial-harvest-modal';
import { EditFinalHarvestPlanModal } from '@screens/population/components/harvest-modals/edit-final-harvest-plan-modal';
import { VisionOnlyGuard } from '@components/permission-wrappers/vision-only-guard';
import { VisionOnlyAccess } from '@components/permission-wrappers/vision-only-access';
import { VisionOnlyAddEditPondStockingModal } from '@screens/pond/components/modals/add-edit-pond-stocking/vision-only-add-edit-pond-stocking-modal';

type PondActionsProps = {
  isInPondView?: boolean;
};

export function PondActions(props: PondActionsProps) {
  const { isInPondView } = props;

  const { route, query } = useRouter();

  const { reloadCurrentPond } = useReloadCurrentPond();

  const { currentPond: pond, currentPopulation: population } = useAppSelector((state) => state.farm);

  const { name: pondName, _id: pondId, farmId, size: pondSize, superVisorId } = pond ?? {};
  const { _id: populationId, history, harvestPlan, productionPrediction } = population ?? {};

  let harvestPlanDefaultValues = undefined;

  const { canStock, canHarvest, isEmpty, canUnHarvest, isHarvested, hasMonitoring } = getPondState({ population });

  if (canHarvest) {
    const { harvestDate, selectedProcessorId, processorPriceList, projection } = harvestPlan ?? {};
    const { profitProjectionData, plannedHarvestIdx } = projection ?? {};

    const profitOnPlannedHarvestDate =
      profitProjectionData?.[plannedHarvestIdx]?.[processorPriceList?.defaultHarvestType as 'headon'];
    const predictionOnPlannedHarvest = productionPrediction?.find((item) => item.date === harvestDate);

    harvestPlanDefaultValues = {
      date: harvestDate,
      lbsHarvested: predictionOnPlannedHarvest?.biomassLbs,
      weight: predictionOnPlannedHarvest?.averageWeight,
      revenue: profitOnPlannedHarvestDate?.totalRevenue,
      processorId: selectedProcessorId,
      enablesEstimation: true,
      priceList: processorPriceList
    };
  }

  const { trans } = getTrans();
  const [_, unHarvestPond] = useUnharvestPondApi();

  const handleUnHarvest = () => {
    unHarvestPond({
      params: {
        pondId,
        populationId
      },
      successCallback: () => {
        reloadCurrentPond();
      }
    });
  };

  const isDev = isDevApp();
  const activeTab = getPondActiveTab(route, query);
  const { trackAction } = useAnalytics();
  return (
    <Flex align='center' gap='sm-alt' data-cy='pond-view-actions' flexWrap='wrap'>
      {activeTab === 'isOverview' && (
        <>
          <DevGenerateHistoryButton />
          <DataCheckButton />
        </>
      )}
      <MenuRoot closeOnSelect={false}>
        <MenuButton
          ps='md'
          pe='xs-alt'
          border='1px solid'
          borderColor='gray.400'
          analyticsId={actionsName.harvestActionButtonClicked}
          analyticsData={{ addCurrentPopulation: true }}
          selectVariant='secondary'
          _hover={{ borderColor: 'gray.700' }}
          _active={{ borderColor: 'gray.700' }}
          data-cy='harvest-action-menu-button'
          size={{ base: 'sm', lg: 'md' }}
        >
          {trans('t_harvest_actions')} <ChevronDownFilled hasBackground={false} color='icon.gray' />
        </MenuButton>
        <MenuContent>
          <AddEditFinalHarvestModal
            pondId={pondId}
            pondName={pondName}
            isInPondView={isInPondView}
            triggerContainerProps={{ width: 'unset' }}
            isEdit={!canHarvest}
            defaultValues={harvestPlanDefaultValues}
          >
            <MenuItem
              value='harvest-pond-btn'
              data-cy='harvest-pond-btn'
              onClick={() => {
                trackAction(actionsName.harvestPondClicked, {
                  addCurrentPopulation: true,
                  trigger: 'harvest-action-button',
                  state: canHarvest ? 'record final harvest' : 'edit recorded final harvest'
                }).then();
              }}
            >
              {canHarvest ? trans('t_record_final_harvest') : trans('t_edit_final_harvest')}
            </MenuItem>
          </AddEditFinalHarvestModal>
          <VisionOnlyGuard>
            {canHarvest && (
              <EditFinalHarvestPlanModal
                pondId={pondId}
                pondName={pondName}
                pondSize={pondSize}
                isInPondView={isInPondView}
                triggerContainerProps={{ width: 'unset' }}
              >
                <MenuItem
                  value='edit-harvest-pond-btn'
                  data-cy='edit-harvest-pond-btn'
                  onClick={() => {
                    trackAction(actionsName.editHarvestPlanClicked, {
                      addCurrentPopulation: true,
                      trigger: 'harvest-action-button',
                      state: 'edit final harvest plan'
                    }).then();
                  }}
                >
                  {trans('t_edit_final_harvest_plan')}
                </MenuItem>
              </EditFinalHarvestPlanModal>
            )}

            {hasMonitoring && canHarvest && (
              <ManagePartialHarvestModal>
                <MenuItem
                  value='edit-partial-harvest-pond-btn'
                  data-cy='edit-partial-harvest-pond-btn'
                  onClick={() => {
                    trackAction(actionsName.managePartialHarvestClicked, {
                      addCurrentPopulation: true,
                      trigger: 'harvest-action-button',
                      state: 'manage partial harvests'
                    }).then();
                  }}
                >
                  {trans('t_manage_partial_harvests')}
                </MenuItem>
              </ManagePartialHarvestModal>
            )}
          </VisionOnlyGuard>
          {canUnHarvest && isDev && (
            <MenuItem value='unharvest-pond-btn' onClick={handleUnHarvest}>
              {trans('t_unharvest_pond')}
            </MenuItem>
          )}
        </MenuContent>
      </MenuRoot>
      <MenuRoot closeOnSelect={false}>
        <MenuButton
          ps='md'
          pe='xs-alt'
          border='1px solid'
          borderColor='gray.400'
          selectVariant='secondary'
          _hover={{ borderColor: 'gray.700' }}
          _active={{ borderColor: 'gray.700' }}
          data-cy='action-menu-button'
          size={{ base: 'sm', lg: 'md' }}
        >
          {trans('t_pond_actions')} <ChevronDownFilled hasBackground={false} color='icon.gray' />
        </MenuButton>
        <MenuContent>
          <VisionOnlyAccess>
            <VisionOnlyAddEditPondStockingModal
              pondId={pondId}
              pondName={pondName}
              pondSize={pondSize}
              isHarvested={isHarvested}
              firstMonitoring={history?.[history.length - 1]}
              isInPondView={isInPondView}
              isEdit={!canStock}
            >
              <MenuItem
                value='stock-pond-btn'
                data-cy='stock-pond-btn'
                onClick={() => {
                  trackAction(actionsName.stockPondClicked, { source: 'pond-actions-button' }).then();
                }}
              >
                {canStock ? trans('t_stock_pond') : trans('t_edit_stocking')}
              </MenuItem>
            </VisionOnlyAddEditPondStockingModal>
          </VisionOnlyAccess>
          {canStock && (
            <VisionOnlyGuard>
              <AddPondStockingModal
                pondId={pondId}
                pondName={pondName}
                pondSize={pondSize}
                isHarvested={isHarvested}
                firstMonitoring={history?.[history.length - 1]}
                isInPondView={isInPondView}
              >
                <MenuItem
                  value='stock-pond-btn'
                  data-cy='stock-pond-btn'
                  onClick={() => {
                    trackAction(actionsName.stockPondClicked, { source: 'pond-actions-button' }).then();
                  }}
                >
                  {trans('t_stock_pond')}
                </MenuItem>
              </AddPondStockingModal>
            </VisionOnlyGuard>
          )}

          {canHarvest && isDev && (
            <MenuItem asChild value='start_monitoring_btn' data-cy='start_monitoring_btn'>
              <BaseLink route='/manage-monitoring' params={{ populationId }}>
                {trans('t_start_monitoring')}
              </BaseLink>
            </MenuItem>
          )}
          {canHarvest && (
            <CreateManualMonitoringModal
              pondId={pondId}
              pondName={pondName}
              populationId={populationId}
              buttonProps={{ width: 'unset' }}
            >
              <MenuItem value='create_manual_monitoring_btn' data-cy='create_manual_monitoring_btn'>
                {trans('t_add_manual_weight_entry')}
              </MenuItem>
            </CreateManualMonitoringModal>
          )}

          <AddEditPondModal
            farmId={farmId}
            pondId={pondId}
            pondName={pondName}
            defaultValues={{ name: pondName, size: pondSize, superVisorId }}
            isInPondView={isInPondView}
            buttonProps={{ width: 'unset' }}
          >
            <MenuItem value='edit-pond-btn' data-cy='edit-pond-btn'>
              {trans('t_edit_pond_details')}
            </MenuItem>
          </AddEditPondModal>

          <VisionOnlyGuard>
            {!isEmpty && (
              <AddNotesModal
                w='unset'
                pondId={pondId}
                populationId={populationId}
                pondName={pondName}
                shouldUseCurrentPopulation
              >
                <MenuItem value='add-notes-btn' data-cy='add-notes-btn'>
                  {trans('t_add_notes')}
                </MenuItem>
              </AddNotesModal>
            )}
          </VisionOnlyGuard>

          {!!isEmpty && isDev && (
            <ArchivePondModal pondId={pond._id} pondName={pond.name} isInPondView={isInPondView}>
              <MenuItem value='archive-pond-btn' data-cy='archive-pond-btn'>
                {trans('t_archive_pond')} (DEV)
              </MenuItem>
            </ArchivePondModal>
          )}
        </MenuContent>
      </MenuRoot>
    </Flex>
  );
}
