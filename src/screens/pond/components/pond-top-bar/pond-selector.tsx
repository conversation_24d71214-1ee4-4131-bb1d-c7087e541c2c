import { Box, Flex, Heading, Input, List, Text, useDisclosure } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { CurrentFarmPonds } from '@redux/farm/set-current-farm-ponds';
import { useAppSelector } from '@redux/hooks';
import { sortArray, sortCompareString } from '@utils/sort-array';
import get from 'lodash/get';
import { useRouter } from 'next/router';
import { useMemo, useState } from 'react';
import { ChevronUpFilled } from '@icons/chevron-up/chevron-up-filled';
import { ChevronDownFilled } from '@icons/chevron-down/chevron-down-filled';
import { SearchDuoIcon } from '@icons/search/search-duo-icon';
import { BaseButton } from '@components/base/base-button';
import { BaseSpinner } from '@components/base/base-spinner';
import { slugify } from '@utils/string';
import { BaseLink } from '@components/base/base-link';
import { PopoverBody, PopoverContent, PopoverRoot, PopoverTrigger } from '@components/ui/popover';
import { InputGroup } from '@components/ui/input-group';
import { CloseFilled } from '@icons/close/close-filled';

type PondSelectorProps = { currentPond: CurrentFarmPonds[0] };

export function PondSelector(props: PondSelectorProps) {
  const { currentPond } = props;

  const { onOpen, onClose, open } = useDisclosure();

  return (
    <PopoverRoot
      open={open}
      onOpenChange={(e) => (e.open ? onOpen() : onClose())}
      lazyMount
      unmountOnExit
      positioning={{ placement: 'bottom-start' }}
    >
      <PopoverTrigger asChild>
        <BaseButton
          px={0}
          variant='ghost'
          data-cy='pond-name'
          h='fit-content'
          _focus={{ outline: 'none' }}
          _active={{ backgroundColor: 'unset' }}
        >
          <Heading
            size={{ base: 'heavy300', md: 'heavy200' }}
            as='span'
            w='fit-content'
            overflow='hidden'
            whiteSpace='nowrap'
            letterSpacing='unset'
            textOverflow='ellipsis'
            maxW={{ base: '190px', sm: '280px', md: 'max-content' }}
          >
            {currentPond.name}
          </Heading>
          {open ? (
            <ChevronUpFilled hasBackground={false} color='icon.gray' />
          ) : (
            <ChevronDownFilled hasBackground={false} color='icon.gray' />
          )}
        </BaseButton>
      </PopoverTrigger>
      <PopoverContent
        rounded='2xl'
        border='none'
        overflow='hidden'
        shadow='elevation.400'
        _focusVisible={{ boxShadow: 'none', outline: 'none' }}
      >
        <PopoverBody p={0}>
          <PondsMenuList onPondClick={onClose} />
        </PopoverBody>
      </PopoverContent>
    </PopoverRoot>
  );
}

type PondsMenuListProps = {
  onPondClick: () => void;
};

function PondsMenuList(props: PondsMenuListProps) {
  const { onPondClick } = props;

  const { trans } = getTrans();
  const { query } = useRouter();
  const { pondEid } = query;
  const currentPondEid = pondEid ? Number(pondEid.toString().split('-')[0]) : 0;
  const tab = query?.tab ? query?.tab.toString() : undefined;
  const { currentFarmPonds, isFinishedOnceLoadingCurrentFarmPonds } = useAppSelector((state) => state.farm);

  const [search, setSearch] = useState(undefined);

  const sortedPondList = useMemo(() => {
    if (!currentFarmPonds?.length) return [];

    const ponds = search
      ? currentFarmPonds.filter((ele) => ele.name.toLowerCase().includes(search.toLowerCase()))
      : currentFarmPonds;

    return sortArray({
      list: ponds,
      sortDir: 'asc',
      compareFunction: (a, b) => {
        const firstItemValue = get(a, 'name');
        const secondItemValue = get(b, 'name');
        return sortCompareString(firstItemValue, secondItemValue);
      }
    });
  }, [currentFarmPonds, search]);

  return (
    <>
      {!isFinishedOnceLoadingCurrentFarmPonds && (
        <Flex align='center' justify='center' gap='sm-alt' py='md'>
          <BaseSpinner size='sm' borderWidth={2} />
          <Text size='label200'>{trans('t_loading')}...</Text>
        </Flex>
      )}
      {isFinishedOnceLoadingCurrentFarmPonds && (
        <>
          <Box p='xs' position='sticky' top='0' backgroundColor='white' zIndex={9}>
            <InputGroup
              w='full'
              startElement={<SearchDuoIcon pointerEvents='none' />}
              endElement={
                search && <CloseFilled hasBackground={false} cursor='pointer' onClick={() => setSearch('')} />
              }
            >
              <Input
                value={search}
                borderRadius='full'
                textStyle='label.200'
                outline='none'
                autoFocus
                autoComplete='off'
                placeholder={trans('t_search')}
                _placeholder={{ color: 'text.gray.disabled' }}
                onChange={(e) => {
                  setSearch(e.target.value);
                  e.stopPropagation();
                  e.currentTarget.focus();
                }}
              />
            </InputGroup>
          </Box>
          {!sortedPondList?.length && (
            <Text size='label200' px='xs' pb='md' pt='xs' textAlign='center'>
              {trans('t_no_ponds_found')}
            </Text>
          )}
          {!!sortedPondList.length && (
            <List.Root maxHeight='35vh' overflow='scroll' px='xs' pb='xs'>
              {sortedPondList.map((pond) => {
                const isHighlighted = pond.eid === currentPondEid;

                return (
                  <List.Item
                    mt='3xs'
                    minH='40px'
                    maxH='40px'
                    key={pond.eid}
                    display='flex'
                    cursor='pointer'
                    alignItems='center'
                    borderRadius='lg-alt'
                    onClick={onPondClick}
                    justifyContent='space-between'
                    _hover={{ backgroundColor: 'bg.brandBlue.weakShade1' }}
                    {...(isHighlighted && { backgroundColor: 'bg.brandBlue.weakShade1' })}
                    asChild
                  >
                    <BaseLink
                      px='xs'
                      display='block'
                      boxSize='100%'
                      route='/farm/[farmEid]/pond/[pondEid]'
                      params={{ ...query, tab, populationId: undefined, pondEid: slugify(`${pond.eid}-${pond.name}`) }}
                    >
                      <Text size='label200'>{pond.name}</Text>
                    </BaseLink>
                  </List.Item>
                );
              })}
            </List.Root>
          )}
        </>
      )}
    </>
  );
}
