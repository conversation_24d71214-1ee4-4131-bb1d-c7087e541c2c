import { useAppSelector } from '@redux/hooks';
import { getTrans } from '@i18n/get-trans';
import { UIEvent, useEffect, useState } from 'react';
import { Box, Flex, Heading, List, Text, useDisclosure } from '@chakra-ui/react';
import { useListPopulationsApi } from '@screens/population/hooks/use-list-populations-api';
import { DateTime } from 'luxon';
import { BaseButton } from '@components/base/base-button';
import { BaseLink } from '@components/base/base-link';
import { slugify } from '@utils/string';
import { getPondState } from '@screens/pond/helpers/get-pond-state';
import { ChevronUpFilled } from '@icons/chevron-up/chevron-up-filled';
import { ChevronDownFilled } from '@icons/chevron-down/chevron-down-filled';
import { BaseSpinner } from '@components/base/base-spinner';
import { PopoverBody, PopoverContent, PopoverRoot, PopoverTrigger } from '@components/ui/popover';

export function CycleMenu() {
  const lang = useAppSelector((state) => state.app.lang);
  const pond = useAppSelector((state) => state.farm.currentPond);
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);
  const population = useAppSelector((state) => state.farm.currentPopulation);
  const currentFarmSlug = useAppSelector((state) => state.farm.currentFarmSlug);

  const { timezone: farmTimezone } = currentFarm ?? {};
  const { _id: pondId, currentPopulationId } = pond ?? {};
  const { stockedAt, stockedAtTimezone, createdAt, cycle } = population ?? {};
  const routePath = '/farm/[farmEid]/pond/[pondEid]/population/[populationId]';

  const { isNewPond } = getPondState({ population });
  const { trans } = getTrans();
  const [pageSize, setPageSize] = useState(25);
  const { open, onOpen, onClose } = useDisclosure();
  const [{ data: populationsData, isFinishedOnce }, listPopulations] = useListPopulationsApi();

  const { populations } = populationsData ?? ({} as typeof populationsData);

  const sortedPopulations = [...(populations ?? [])].sort((a, b) => {
    // If a doesn't have a date but b does, return 1 (so a comes after b)
    if (!a.lastMonitoringDate && b.lastMonitoringDate) return 1;

    // If b doesn't have a date but a does, return -1 (so b comes after a)
    if (a.lastMonitoringDate && !b.lastMonitoringDate) return -1;

    // If neither a nor b have a date, keep their order unchanged
    if (!a.lastMonitoringDate && !b.lastMonitoringDate) return 0;
    return DateTime.fromISO(b.lastMonitoringDate).toMillis() - DateTime.fromISO(a.lastMonitoringDate).toMillis();
  });

  useEffect(() => {
    if (!open) return;
    listPopulations({
      params: {
        filter: { pondId: [pondId] },
        page: { size: pageSize },
        sort: [{ field: 'stockedAt', order: 'desc' }]
      },
      fields: {
        populations: {
          _id: 1,
          pondId: 1,
          cycle: 1,
          createdAt: 1,
          stockedAt: 1,
          stockedAtTimezone: 1,
          lastMonitoringDate: 1,
          harvest: {
            date: 1,
            timezone: 1
          }
        }
      }
    });
  }, [open, pageSize]);

  const handleScroll = (event: UIEvent<HTMLUListElement>) => {
    const target = event.currentTarget;
    if (target.scrollHeight - target.scrollTop === target.clientHeight) {
      setPageSize((prev) => prev + 25);
    }
  };

  if (isNewPond)
    return (
      <BaseButton
        ps='xs'
        pe='2xs'
        size='sm'
        height={['20px', '32px']}
        fontWeight={500}
        disabled={true}
        color='grey.400'
        data-cy='cycle-menu-button'
        variant='ghost'
      >
        <Heading size={['sm', 'md']} as='span' display='block' overflow='hidden' textOverflow='ellipsis'>
          {trans('t_no_past_cycles')}
        </Heading>
      </BaseButton>
    );

  const cycleTitle = cycle
    ? `${trans('t_cycle')} ${cycle}`
    : DateTime.fromISO(stockedAt || createdAt)
        .setZone(stockedAtTimezone || farmTimezone)
        .toFormat('MMMyyyy');

  return (
    <Box data-cy='pond-view-cycle-menu'>
      <PopoverRoot
        open={open}
        onOpenChange={(e) => (e.open ? onOpen() : onClose())}
        lazyMount
        unmountOnExit
        positioning={{ placement: 'bottom-start' }}
      >
        <PopoverTrigger asChild>
          <BaseButton
            px={0}
            variant='ghost'
            data-cy='cycle-menu-button'
            h='fit-content'
            _focus={{ outline: 'none' }}
            _active={{ backgroundColor: 'unset' }}
          >
            <Heading
              size={{ base: 'heavy300', md: 'heavy200' }}
              as='span'
              w='fit-content'
              overflow='hidden'
              whiteSpace='nowrap'
              letterSpacing='unset'
              textOverflow='ellipsis'
              maxW={{ base: '190px', sm: '280px', md: 'max-content' }}
            >
              {cycleTitle}
            </Heading>
            {open ? (
              <ChevronUpFilled hasBackground={false} color='icon.gray' />
            ) : (
              <ChevronDownFilled hasBackground={false} color='icon.gray' />
            )}
          </BaseButton>
        </PopoverTrigger>
        <PopoverContent
          rounded='2xl'
          border='none'
          overflow='hidden'
          shadow='elevation.400'
          _focusVisible={{ boxShadow: 'none', outline: 'none' }}
        >
          <PopoverBody p={0}>
            {!PopoverBody && (
              <Flex align='center' justify='center' gap='sm-alt' py='md'>
                <BaseSpinner size='sm' borderWidth={2} />
                <Text size='label200'>{trans('t_loading')}...</Text>
              </Flex>
            )}
            {isFinishedOnce && (
              <>
                {!sortedPopulations?.length && (
                  <Text size='label200' p='md' textAlign='center'>
                    {trans('t_no_cycles_found')}
                  </Text>
                )}
                {!!sortedPopulations?.length && (
                  <List.Root maxHeight='35vh' overflow='scroll' p='xs' onScroll={handleScroll}>
                    {sortedPopulations.map((population) => {
                      const {
                        _id: populationId,
                        harvest,
                        stockedAt,
                        stockedAtTimezone,
                        createdAt,
                        cycle
                      } = population ?? {};
                      const { date: harvestDate } = harvest ?? {};

                      const isCurrentPopulation = populationId === currentPopulationId;
                      const stockedAtLuxon = DateTime.fromISO(stockedAt).setZone(stockedAtTimezone);
                      const stockedAtFormattedDate = stockedAt
                        ? stockedAtLuxon.toFormat('MMM dd, yy', { locale: lang })
                        : trans('t_empty_cycle');
                      const createdAtLuxon = DateTime.fromISO(createdAt).setZone(farmTimezone);

                      const harvestDateTitle = isCurrentPopulation ? trans('t_present') : '';
                      const harvestAtFormattedDate = harvestDate
                        ? DateTime.fromISO(harvestDate).toFormat('MMM dd, yy', { locale: lang })
                        : harvestDateTitle;
                      const cycleDate = stockedAt
                        ? stockedAtLuxon.toFormat('MMM yyyy', { locale: lang })
                        : createdAtLuxon.toFormat('MMM yyyy', { locale: lang });
                      const cycleTitle = cycle ? `${trans('t_cycle')} ${cycle}` : cycleDate;

                      return (
                        <List.Item
                          px='xs'
                          mt='3xs'
                          h='40px'
                          cursor='pointer'
                          onClick={onClose}
                          key={populationId}
                          borderRadius='lg-alt'
                          data-cy='harvest-pond-btn'
                          _hover={{ backgroundColor: 'bg.brandBlue.weakShade1' }}
                        >
                          <BaseLink
                            boxSize='100%'
                            display='flex'
                            alignItems='center'
                            justifyContent='space-between'
                            route={routePath}
                            params={{
                              farmEid: currentFarmSlug,
                              pondEid: slugify(`${pond.eid}-${pond.name}`),
                              populationId: slugify(`${populationId}-${cycle ?? cycleDate}`)
                            }}
                          >
                            <Text size='label200'>{cycleTitle}</Text>
                            <Text size='label200'>
                              {`${stockedAtFormattedDate}${harvestAtFormattedDate ? ' - ' : ''}${harvestAtFormattedDate}`}
                            </Text>
                          </BaseLink>
                        </List.Item>
                      );
                    })}
                  </List.Root>
                )}
              </>
            )}
          </PopoverBody>
        </PopoverContent>
      </PopoverRoot>
    </Box>
  );
}
