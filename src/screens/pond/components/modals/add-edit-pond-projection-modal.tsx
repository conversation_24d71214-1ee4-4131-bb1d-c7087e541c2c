import {
  Box,
  BoxProps,
  chakra,
  Field,
  Flex,
  FlexProps,
  Separator,
  Stack,
  Table,
  Text,
  TextProps,
  useDisclosure
} from '@chakra-ui/react';
import { BaseFormNumberInput } from '@components/form/base-form-number-input';
import { getTrans } from '@i18n/get-trans';
import { ComponentProps, ReactNode, useEffect, useState } from 'react';
import { numberTransform, useYupSchema, Yup, yupFormResolver, YupResolverType } from '@utils/yup';
import { Controller, FieldErrors, useFieldArray, useForm } from 'react-hook-form';
import { convertUnitByDivision, formatNumber, getPercentageDivided, getPercentageValue, isNumber } from '@utils/number';
import { useAppSelector } from '@redux/hooks';
import { BaseButton } from '@components/base/base-button';
import { PopulationType } from '@redux/farm/set-current-population';
import { useUpdatePopulationApi } from '@screens/population/hooks/use-update-population-api';
import { DateTime } from 'luxon';
import { InfoIconToolTip } from '@components/tooltip/info-icon-tool-tip';
import { WarningIcon } from '@screens/farm/icons/warning-icon';
import {
  EnumPopulationCycleInformationProjectionProjectedFeedType,
  EnumPopulationCycleInformationProjectionProjectedGrowthType,
  EnumPopulationCycleInformationProjectionProjectedSurvivalProjectedSurvivalType,
  PopulationPrediction,
  PopulationProductionPrediction
} from '@xpertsea/module-farm-sdk';
import {
  findPredictionGrowthLastHistoryToHarvestDate,
  useProfitProjectionVariableFormData
} from '@screens/pond/helpers/profit-projection';
import { PlusFilled } from '@icons/plus/plus-filled';
import { getDaysDiffBetweenDates } from '@screens/farm/helpers/farm-dates';
import { AdminOrSupervisorWrapper } from '@components/permission-wrappers/admin-or-supervisor-wrapper';
import { NumberSlider } from '@components/common/number-slider';
import { ChevronDownFilled } from '@icons/chevron-down/chevron-down-filled';
import {
  DialogActionTrigger,
  DialogBody,
  DialogContent,
  DialogHeader,
  DialogRoot,
  DialogTitle,
  DialogTrigger
} from '@components/ui/dialog';
import { NativeSelectField, NativeSelectRoot } from '@components/ui/native-select';
import { CloseButton } from '@components/ui/close-button';
import { TrashcanXIcon } from '@icons/trash-can/trash-can-x-icon';
import { useReloadCurrentFarmPonds } from '@screens/farm/hooks/use-reload-current-farm-ponds';
import { useGetPopulationDetailsApi } from '@screens/population/hooks/use-get-population-details-api';
import { actionsName } from '@utils/segment';
import isEmpty from 'lodash/isEmpty';
import { RefreshIcon } from '@icons/refresh/refresh-icon';
import { TableContainer } from '@components/ui/table-container';
import isUndefined from 'lodash/isUndefined';
import { SectionLoader } from '@components/loaders/section-loader';
import { useV2GenerateProfitProjection } from '@screens/pond/components/modals/hooks/use-v2-generate-profit-projection';
import { PopulationCycleInformationInput } from '@xpertsea/module-farm-sdk/dist/schema';
import isEqual from 'lodash/isEqual';

type AddEditPondProjectionModalProps = {
  children: ReactNode;
  containerProps?: BoxProps;
  population?: PopulationType;
  pondName?: string;
  isInPondView?: boolean;
  isInPondListView?: boolean;
};

export function AddEditPondProjectionModal(props: AddEditPondProjectionModalProps) {
  const { children, containerProps, population, isInPondView = true, isInPondListView, pondName } = props;
  const currentPopulation = useAppSelector((state) => state.farm?.currentPopulation);
  const currentPondName = useAppSelector((state) => state.farm?.currentPond?.name);
  const { open, onOpen, onClose } = useDisclosure();

  const populationToUse = population ?? currentPopulation;
  const pondNameToUse = pondName ?? currentPondName;

  return (
    <DialogRoot
      open={open}
      onOpenChange={(e) => {
        e.open ? onOpen() : onClose();
      }}
      size={{ base: 'full', lg: 'cover' }}
      scrollBehavior='inside'
    >
      <Box {...containerProps} onClick={onOpen}>
        {children}
      </Box>

      <DialogContent closeTrigger={false} p='lg' rounded='2xl' bgColor='bg.muted'>
        <DialogBody p={0}>
          <Form
            population={populationToUse}
            pondName={pondNameToUse}
            onClose={onClose}
            isInPondView={isInPondView}
            isInPondListView={isInPondListView}
          />
        </DialogBody>
      </DialogContent>
    </DialogRoot>
  );
}

type FormType = {
  projectedFeed: {
    feedType: EnumPopulationCycleInformationProjectionProjectedFeedType;
    expectedFeed: number;
    farmFeedTableId: string;
    feedAggressionMultiplier: number;
  };
  projectedSurvival: {
    projectedSurvivalType: EnumPopulationCycleInformationProjectionProjectedSurvivalProjectedSurvivalType;
    dailyMortalityPercent: number;
    expectedTargetSurvival: number;
    expectedTargetSurvivalDays: number;
  };
  projectedGrowth: {
    growthType: EnumPopulationCycleInformationProjectionProjectedGrowthType;
    days: number;
    grams: number;
  };
  feedTypes: { feedType: string; percentage: number }[];
  costVariables: { overheadCosts: number };
};

type FormProps = {
  population: PopulationType;
  pondName: string;
  onClose: () => void;
  isInPondView?: boolean;
  isInPondListView?: boolean;
};

function Form(props: FormProps) {
  const { population, onClose, isInPondView, isInPondListView, pondName } = props;

  const { trans } = getTrans();
  const lang = useAppSelector((state) => state.app?.lang);
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);

  const [{ isLoading }, updatePopulationApi] = useUpdatePopulationApi();
  const { reloadCurrentFarmPonds } = useReloadCurrentFarmPonds();
  const [_, getPopulationDetails] = useGetPopulationDetailsApi();
  const [{ isLoading: isLoadingProjections, data }, generateProfitProjection] = useV2GenerateProfitProjection();
  const [cycleInfoSubmittedToProjection, setCycleInfoSubmittedToProjection] =
    useState<PopulationCycleInformationInput>(null);

  const { survival, lastSurvivalCycleDays } = useProfitProjectionVariableFormData({
    population
  });

  const generatedProductionPrediction = data?.productionPrediction?.[0];

  const { feedTypes, feedTable, projectionsConfig } = currentFarm ?? {};
  const farmProjection = projectionsConfig?.projection;

  const { cycleInformation } = population ?? {};
  const { projectedFeed, projectedSurvival, projectedGrowth, overheadCostsPerHaPerDay } =
    cycleInformation?.projection ?? {};

  const {
    projectedFeed: farmProjectedFeed,
    projectedSurvival: farmProjectedSurvival,
    projectedGrowth: farmProjectedGrowth
  } = farmProjection ?? {};

  const populationFeedTypes = projectedFeed?.farmFeedTypes?.map((ele) => ({
    feedType: ele.id,
    percentage: getPercentageValue(ele.percentage)
  }));
  const farmFeedTypes = farmProjectedFeed?.farmFeedTypes?.map((ele) => ({
    feedType: ele.id,
    percentage: getPercentageValue(ele.percentage)
  }));

  const defaultValues = {
    projectedFeed: {
      feedType: projectedFeed?.type ?? farmProjectedFeed?.type ?? 'feedTable',
      expectedFeed: projectedFeed?.expectedFeed ?? farmProjectedFeed?.expectedFeed,
      farmFeedTableId: projectedFeed?.farmFeedTableId ?? farmProjection?.projectedFeed?.farmFeedTableId,
      feedAggressionMultiplier:
        getPercentageValue(projectedFeed?.feedAggressionMultiplier ?? farmProjectedFeed?.feedAggressionMultiplier) ?? 0
    },
    projectedSurvival: {
      projectedSurvivalType:
        projectedSurvival?.projectedSurvivalType ?? farmProjectedSurvival?.projectedSurvivalType ?? 'kampiMortality',
      dailyMortalityPercent: getPercentageValue(
        projectedSurvival?.dailyMortalityPercent ?? farmProjectedSurvival?.dailyMortalityPercent
      ),
      expectedTargetSurvival: getPercentageValue(
        projectedSurvival?.expectedTargetSurvival ?? farmProjectedSurvival?.expectedTargetSurvival
      ),
      expectedTargetSurvivalDays:
        projectedSurvival?.expectedTargetSurvivalDays ?? farmProjectedSurvival?.expectedTargetSurvivalDays
    },
    projectedGrowth: {
      growthType: projectedGrowth?.type ?? farmProjectedGrowth?.type ?? 'xpertsea',
      days: projectedGrowth?.days ?? farmProjectedGrowth?.days ?? 7,
      grams: projectedGrowth?.grams ?? farmProjectedGrowth?.grams
    },
    feedTypes: populationFeedTypes?.length ? populationFeedTypes : farmFeedTypes,
    costVariables: { overheadCosts: overheadCostsPerHaPerDay }
  };

  const feedTableOptions = feedTypes
    ?.map((ele) => {
      if (ele?.status === 'inactive') return;
      return { value: ele._id, label: `${ele.feedType} ${ele.brand}` };
    })
    ?.filter(Boolean);

  const farmFeedTables = feedTable?.filter((item) => item.status === 'active') ?? [];

  const { schema } = useYupSchema({
    projectedFeed: Yup.object().shape({
      feedType: Yup.string().required(trans('t_required')),
      expectedFeed: Yup.number().when('feedType', {
        is: 'kgPerHaPerDay',
        then: () =>
          Yup.number()
            .transform(numberTransform)
            .min(0)
            .max(500)
            .required(trans('t_required'))
            .label('t_expected_feed'),
        otherwise: () => Yup.number().transform(numberTransform).nullable()
      }),
      farmFeedTableId: Yup.string().when('feedType', {
        is: 'feedTable',
        then: () => Yup.string().required(trans('t_required')),
        otherwise: () => Yup.string().nullable()
      }),
      feedAggressionMultiplier: Yup.number().when('feedType', {
        is: 'feedTable',
        then: () =>
          Yup.number()
            .transform(numberTransform)
            .min(-100)
            .max(100)
            .required(trans('t_required'))
            .label('t_feed_aggression_multiplier'),
        otherwise: () => Yup.number().transform(numberTransform).nullable()
      })
    }),

    projectedSurvival: Yup.object().shape({
      projectedSurvivalType: Yup.string().required(trans('t_required')),
      dailyMortalityPercent: Yup.number().when('projectedSurvivalType', {
        is: (type: string) => {
          return !!survival && type === 'mortality';
        },
        then: () =>
          Yup.number()
            .transform(numberTransform)
            .min(0)
            .max(5)
            .required(trans('t_required'))
            .label('t_daily_mortality'),
        otherwise: () => Yup.number().transform(numberTransform).nullable()
      }),
      expectedTargetSurvival: Yup.number().when('projectedSurvivalType', {
        is: (type: string) => {
          return !!survival && type === 'endOfCycle';
        },
        then: () =>
          Yup.number()
            .transform(numberTransform)
            .min(0)
            .max(survival ? survival * 100 : 100)
            .required(trans('t_required'))
            .label('t_survival'),
        otherwise: () => Yup.number().transform(numberTransform).nullable()
      }),
      expectedTargetSurvivalDays: Yup.number().when('projectedSurvivalType', {
        is: (type: string) => {
          return !!survival && type === 'endOfCycle';
        },
        then: () =>
          Yup.number()
            .transform(numberTransform)
            .min(lastSurvivalCycleDays ? lastSurvivalCycleDays + 1 : 0)
            .max(200)
            .required(trans('t_required'))
            .label('t_days'),
        otherwise: () => Yup.number().transform(numberTransform).nullable()
      })
    }),

    projectedGrowth: Yup.object().shape({
      growthType: Yup.string().required().label('t_required'),
      days: Yup.number().when('growthType', {
        is: 'custom',
        then: () =>
          Yup.number().transform(numberTransform).min(0).max(30).required(trans('t_required')).label('t_days'),
        otherwise: () => Yup.number().transform(numberTransform).nullable()
      }),
      grams: Yup.number().when('growthType', {
        is: 'custom',
        then: () =>
          Yup.number().transform(numberTransform).min(0).max(10).required(trans('t_required')).label('t_grams'),
        otherwise: () => Yup.number().transform(numberTransform).nullable()
      })
    }),

    feedTypes: Yup.array()
      .of(
        Yup.object().shape({
          feedType: Yup.string().required(trans('t_required')),
          percentage: Yup.number()
            .transform(numberTransform)
            .min(0)
            .max(100)
            .required(trans('t_required'))
            .label('t_percentage')
        })
      )
      .test('sum-100', trans('t_feed_type_percentage_sum_error'), (values) => {
        const sum = values?.reduce((acc, current) => acc + (current.percentage || 0), 0);
        return sum <= 100;
      }),

    costVariables: Yup.object().shape({
      overheadCosts: Yup.number()
        .transform(numberTransform)
        .min(0)
        .max(10_000)
        .nullable()
        .label('t_overhead_cost_ha_day')
    })
  });

  const {
    control,
    watch,
    register,
    handleSubmit,
    resetField,
    reset,
    trigger,
    formState: { errors, dirtyFields }
  } = useForm<FormType>({
    mode: 'onChange',
    resolver: yupFormResolver(schema) as YupResolverType<FormType>,
    defaultValues
  });

  const onlyRootFeedTypeMessage =
    errors?.feedTypes && Object.keys(errors.feedTypes).length === 1 && errors.feedTypes.root?.message;

  const watchedFormData = watch();
  const feedProjectionTypeWatch = watchedFormData.projectedFeed.feedType;
  const growthProjectionDaysWatch = watchedFormData.projectedGrowth.days;
  const growthProjectionTypeWatch = watchedFormData.projectedGrowth.growthType;
  const growthProjectionGramsWatch = watchedFormData.projectedGrowth.grams;
  const survivalTypeWatch = watchedFormData.projectedSurvival.projectedSurvivalType;

  const { fields, append, remove } = useFieldArray<FormType>({ control, name: 'feedTypes' });

  const { _id: populationId, history, prediction, harvestPlan, kampiMortality } = population ?? {};
  const { harvestDate } = harvestPlan ?? {};

  const lastHistory = history?.[0];

  const { date: lastHistoryDate } = lastHistory ?? {};

  const isFormDirty = !isEmpty(dirtyFields);

  const weeklyGrowth = findPredictionGrowthLastHistoryToHarvestDate({
    prediction,
    lastHistoryDate,
    plannedHarvestDate: harvestDate
  });

  const gramsDiff = growthProjectionGramsWatch - weeklyGrowth;
  const gramsDiffColor = gramsDiff < 0 ? 'text.semanticRed' : 'text.brandGreen';

  const selectProps = {
    w: '188px',
    iconSize: '24px',
    borderRadius: 'lg',
    borderColor: 'border.gray',
    bgColor: 'bg.gray.medium',
    _hover: { borderColor: 'button.link' },
    icon: <ChevronDownFilled color='text.gray' hasBackground={false} />
  };

  const cycleInfoProjectionInput = generateCycleInfoProjectionInput(watchedFormData);

  useEffect(() => {
    reset(defaultValues);
  }, [JSON.stringify(defaultValues)]);

  //Effects
  useEffect(() => {
    if (!survivalTypeWatch) return;

    if (survivalTypeWatch === 'kampiMortality') {
      resetField('projectedSurvival.expectedTargetSurvival');
      resetField('projectedSurvival.expectedTargetSurvivalDays');
      resetField('projectedSurvival.dailyMortalityPercent');
    } else if (survivalTypeWatch === 'mortality') {
      resetField('projectedSurvival.expectedTargetSurvival');
      resetField('projectedSurvival.expectedTargetSurvivalDays');
    } else {
      resetField('projectedSurvival.dailyMortalityPercent');
    }
  }, [survivalTypeWatch]);

  useEffect(() => {
    if (!growthProjectionTypeWatch) return;

    if (growthProjectionTypeWatch === 'xpertsea') {
      resetField('projectedGrowth.grams');
      resetField('projectedGrowth.days');
    }
  }, [growthProjectionTypeWatch]);

  //Handlers
  const onReload = () => {
    if (isInPondView) return getPopulationDetails({ params: { filter: { populationId } }, loadRelated: true });
    if (isInPondListView) return reloadCurrentFarmPonds();
  };

  const handleOnSubmit = handleSubmit((data) => {
    const { costVariables, projectedFeed, projectedGrowth, projectedSurvival, feedTypes } = data;
    const { dailyMortalityPercent, expectedTargetSurvival } = projectedSurvival;

    const expectedTargetSurvivalFormatted = isNumber(expectedTargetSurvival)
      ? getPercentageDivided(expectedTargetSurvival)
      : undefined;
    const dailyMortalityPercentFormatted = isNumber(dailyMortalityPercent)
      ? getPercentageDivided(dailyMortalityPercent)
      : undefined;

    const feedAggressionMultiplierFormatted = isNumber(projectedFeed.feedAggressionMultiplier)
      ? getPercentageDivided(projectedFeed.feedAggressionMultiplier)
      : undefined;

    const feedTypesNormalized = feedTypes?.map((f) => ({
      id: f.feedType,
      percentage: getPercentageDivided(f.percentage)
    }));

    updatePopulationApi({
      params: {
        filter: { populationId },
        set: {
          cycleInformation: {
            ...population?.cycleInformation,
            projection: {
              ...population?.cycleInformation?.projection,
              overheadCostsPerHaPerDay: costVariables.overheadCosts,
              projectedFeed: {
                ...population?.cycleInformation?.projection?.projectedFeed,
                type: projectedFeed.feedType,
                expectedFeed: projectedFeed.expectedFeed || undefined,
                farmFeedTableId: projectedFeed.farmFeedTableId || undefined,
                feedAggressionMultiplier: feedAggressionMultiplierFormatted,
                farmFeedTypes: feedTypesNormalized?.length ? feedTypesNormalized : undefined
              },
              projectedSurvival: {
                ...population?.cycleInformation?.projection?.projectedSurvival,
                projectedSurvivalType: projectedSurvival.projectedSurvivalType,
                expectedTargetSurvivalDays: projectedSurvival.expectedTargetSurvivalDays,
                dailyMortalityPercent: dailyMortalityPercentFormatted,
                expectedTargetSurvival: expectedTargetSurvivalFormatted
              },
              projectedGrowth: {
                ...population?.cycleInformation?.projection?.projectedGrowth,
                type: projectedGrowth.growthType,
                days: projectedGrowth.days,
                grams: projectedGrowth.grams
              }
            }
          }
        }
      },
      successCallback: () => {
        onClose();
        onReload();
      }
    });
  });

  const handleGenerateProfitProjection = async (defaultData?: PopulationCycleInformationInput) => {
    const isValid = await trigger();
    if (!isValid) return;
    const cycleInfoInput = defaultData ?? cycleInfoProjectionInput;
    generateProfitProjection({
      params: {
        populationId,
        date: harvestDate,
        cycleInformation: cycleInfoInput
      },
      successCallback: () => {
        setCycleInfoSubmittedToProjection(cycleInfoInput);
      }
    });
  };

  return (
    <chakra.form
      display='flex'
      flexDirection='column'
      gap='lg'
      onSubmit={(event) => {
        event.stopPropagation();
        handleOnSubmit(event).then();
      }}
      noValidate
    >
      <Flex justify='space-between' align='center'>
        <Text size='label100'>
          {pondName ? `${pondName} - ` : ''}
          {trans('t_manage_projections')}
        </Text>
        <Flex align='center' gap='md'>
          <BaseButton type='submit' loading={isLoading} analyticsId={actionsName.pondOverviewProjectionEditClicked}>
            {trans('t_save_changes')}
          </BaseButton>
          <CloseButton onClick={onClose} />
        </Flex>
      </Flex>

      <ProjectionDataTable
        errors={errors}
        population={population}
        canGenerateProjections={isFormDirty}
        generatedProductionPrediction={generatedProductionPrediction}
        isLoading={isLoadingProjections}
        isSameDataSubmitted={isEqual(cycleInfoSubmittedToProjection, cycleInfoProjectionInput)}
        onGenerateProfitProjection={handleGenerateProfitProjection}
      />

      <Flex direction='column' gap='sm-alt' p='md' bg='white' rounded='xl'>
        <Flex flexWrap='wrap' align='center' gap='xl-alt'>
          <Text size='label200' w='150px'>
            {trans('t_growth')}
          </Text>

          <Flex flexWrap='wrap' gap={growthProjectionTypeWatch === 'custom' ? 'sm-alt' : 'lg'} align='center'>
            <SelectContainer error={errors?.projectedGrowth?.growthType?.message}>
              <NativeSelectField {...register('projectedGrowth.growthType')}>
                <option value='xpertsea'>{trans('t_kampi_growth')}</option>
                <option value='custom'>{trans('t_custom')}</option>
              </NativeSelectField>
            </SelectContainer>

            {growthProjectionTypeWatch === 'xpertsea' && (
              <PredictionGrowth
                lastHistoryDate={lastHistoryDate}
                plannedHarvestDate={harvestPlan?.harvestDate}
                prediction={prediction}
                lang={lang}
              />
            )}

            {growthProjectionTypeWatch === 'custom' && (
              <Flex gap='sm-alt' align='center' flexWrap='wrap'>
                <SelectContainer error={errors?.projectedGrowth?.days?.message}>
                  <NativeSelectField {...register('projectedGrowth.days')}>
                    <option value={1}>{trans('t_daily')}</option>
                    <option value={7}>{trans('t_weekly')}</option>
                  </NativeSelectField>
                </SelectContainer>

                <BaseFormNumberInput
                  width='124px'
                  control={control}
                  name='projectedGrowth.grams'
                  inputRightElement={
                    +growthProjectionDaysWatch === 7 ? trans('t_g_over_wk') : trans('t_growth_g_over_d')
                  }
                  inputRightElementProps={{ color: 'text.gray', textStyle: 'label.200' }}
                  numericFormatProps={{ decimalScale: 2 }}
                />
                <Text ms='sm' size='label200' color={!isNumber(gramsDiff) || gramsDiff === 0 ? '' : gramsDiffColor}>
                  {isNumber(gramsDiff)
                    ? trans('t_grams_change', {
                        grams: formatNumber(gramsDiff, { lang, fractionDigits: 2, signDisplay: 'always' })
                      })
                    : ''}
                </Text>
              </Flex>
            )}
          </Flex>
        </Flex>

        <Separator borderWidth='0.5px' borderColor='border.gray.weak' />

        {!['mortality', 'kampiMortality'].includes(growthProjectionTypeWatch) && (
          <ProjectedSurvivalWarnings
            lastSurvivalCycleDays={lastSurvivalCycleDays}
            lastSurvival={survival * 100}
            survival={defaultValues?.projectedSurvival?.expectedTargetSurvival}
            days={defaultValues?.projectedSurvival?.expectedTargetSurvivalDays}
          />
        )}

        <Flex flexWrap='wrap' align='center' gap='xl-alt'>
          <Text size='label200' w='150px'>
            {trans('t_mortality')}
          </Text>

          <Flex flexWrap='wrap' gap='sm-alt' align='center'>
            <SelectContainer error={errors?.projectedSurvival?.projectedSurvivalType?.message}>
              <NativeSelectField {...register('projectedSurvival.projectedSurvivalType')}>
                <option value='endOfCycle'>{trans('t_end_of_cycle')}</option>
                <option value='mortality'>{trans('t_daily_mortality')}</option>
                <option value='kampiMortality'>{trans('t_kampi_mortality')}</option>
              </NativeSelectField>
            </SelectContainer>

            {survivalTypeWatch === 'endOfCycle' && (
              <Flex gap='sm-alt' align='center'>
                <BaseFormNumberInput
                  width='96px'
                  control={control}
                  name='projectedSurvival.expectedTargetSurvival'
                  inputRightElement='%'
                  inputRightElementProps={{ color: 'text.gray', textStyle: 'label.200' }}
                  numericFormatProps={{ decimalScale: 1 }}
                />

                <Text size='label200'>{trans('t_at')}</Text>

                <BaseFormNumberInput
                  width='96px'
                  control={control}
                  name='projectedSurvival.expectedTargetSurvivalDays'
                  inputRightElement={trans('t_days')}
                  inputRightElementProps={{ color: 'text.gray', textStyle: 'label.200' }}
                  numericFormatProps={{ decimalScale: 0 }}
                />
              </Flex>
            )}

            {survivalTypeWatch === 'mortality' && (
              <BaseFormNumberInput
                width='136px'
                control={control}
                name='projectedSurvival.dailyMortalityPercent'
                inputRightElement={`% / ${trans('t_day')}`}
                inputRightElementProps={{ color: 'text.gray', textStyle: 'label.200' }}
                numericFormatProps={{
                  decimalScale: 2,
                  fixedDecimalScale: true
                }}
              />
            )}

            {survivalTypeWatch === 'kampiMortality' && (
              <Text size='label200'>
                {isNumber(kampiMortality)
                  ? trans('t_count_percent_day', {
                      count: formatNumber(kampiMortality, {
                        lang,
                        fractionDigits: 2,
                        isPercentage: true
                      })
                    })
                  : '-'}
              </Text>
            )}
          </Flex>
        </Flex>

        <Separator borderWidth='0.5px' borderColor='border.gray.weak' />

        <Flex flexWrap='wrap' align='center' gap='xl-alt'>
          <Text size='label200' w='150px'>
            {trans('t_feed_given')}
          </Text>

          <Flex align='center' gap='sm-alt' flexWrap='wrap'>
            <SelectContainer error={errors?.projectedFeed?.feedType?.message}>
              <NativeSelectField {...register('projectedFeed.feedType')}>
                <option value='fcr'>{trans('t_fcr')}</option>
                <option value='kgPerHaPerDay'>{trans('t_feed_given_daily_kg_ha')}</option>
                <option value='feedTable'>{trans('t_feed_table')}</option>
              </NativeSelectField>
            </SelectContainer>
            {feedProjectionTypeWatch !== 'feedTable' && (
              <InfoIconToolTip triggerComponentProps={{ ms: 0 }} iconProps={{ w: '16px', h: '16px' }}>
                {feedProjectionTypeWatch === 'fcr' ? trans('t_fcr_tooltip') : trans('t_kg_ha_tooltip')}
              </InfoIconToolTip>
            )}

            {feedProjectionTypeWatch === 'kgPerHaPerDay' && (
              <BaseFormNumberInput
                width='136px'
                control={control}
                name='projectedFeed.expectedFeed'
                inputRightElement={trans('t_kg_over_ha')}
                inputRightElementProps={{ color: 'text.gray', textStyle: 'label.200' }}
                numericFormatProps={{ decimalScale: 2 }}
              />
            )}

            {feedProjectionTypeWatch === 'feedTable' && (
              <>
                <SelectContainer error={errors?.projectedFeed?.farmFeedTableId?.message}>
                  <NativeSelectField {...register('projectedFeed.farmFeedTableId')}>
                    {farmFeedTables.map((table) => (
                      <option key={table._id} value={table._id}>
                        {table.name}
                      </option>
                    ))}
                  </NativeSelectField>
                </SelectContainer>
                <Controller
                  name='projectedFeed.feedAggressionMultiplier'
                  control={control}
                  render={({ field: { value, onChange } }) => (
                    <NumberSlider
                      w='234px'
                      max={100}
                      min={-100}
                      value={value}
                      onValueChange={onChange}
                      labelText={trans('t_percent_compared_to_feed_table', {
                        value: formatNumber(value ?? 0, { lang, fractionDigits: 0, signDisplay: 'always' })
                      })}
                    />
                  )}
                />
              </>
            )}
          </Flex>
        </Flex>

        <Separator borderWidth='0.5px' borderColor='border.gray.weak' />

        <Flex flexDir='column' gap='md'>
          <Flex align='center' gap='sm-alt'>
            <Text size='label200'>{trans('t_feed_type')}</Text>
            <PlusFilled
              hasBackground
              bgColor='bg.brandBlue'
              color='white'
              onClick={() => append({ feedType: '', percentage: undefined })}
              cursor='pointer'
            />
          </Flex>
          {onlyRootFeedTypeMessage && (
            <Text color='red.500' my='sm-alt' size='label200'>
              {errors?.feedTypes?.root?.message}
            </Text>
          )}
          <Stack gap='sm-alt' py='xs'>
            {fields.map((field, i) => {
              const feedError = errors.feedTypes?.[i]?.feedType?.message;
              return (
                <Flex key={field.id} flexWrap='wrap' align='center' gap={{ base: 'md', md: 'xl-alt' }}>
                  <Text size='label200' w='150px'>
                    #{i + 1}
                  </Text>

                  <Flex gap='sm-alt' align='center' flexWrap='wrap'>
                    <SelectContainer {...selectProps} error={feedError}>
                      <NativeSelectField {...register(`feedTypes.${i}.feedType`)}>
                        {feedTableOptions?.map(({ label, value }) => (
                          <option key={value} value={value}>
                            {label}
                          </option>
                        ))}
                      </NativeSelectField>
                    </SelectContainer>

                    <BaseFormNumberInput
                      width='124px'
                      control={control}
                      name={`feedTypes.${i}.percentage`}
                      numericFormatProps={{ decimalScale: 0 }}
                      inputRightElement='%'
                    />

                    {fields.length > 1 && <TrashcanXIcon cursor='pointer' onClick={() => remove(i)} />}
                  </Flex>
                </Flex>
              );
            })}
          </Stack>
        </Flex>
      </Flex>

      <AdminOrSupervisorWrapper>
        <Flex direction='column' gap='md' p='md' bg='white' rounded='xl'>
          <Text size='label200'>{trans('t_cost')}</Text>
          <Flex align='center' gap='md-alt' flexWrap='wrap'>
            <Flex align='center' gap='xs-alt'>
              <Text whiteSpace='nowrap'>{trans('t_overhead_cost_ha_day')}</Text>
              <InfoIconToolTip triggerComponentProps={{ boxSize: '16px' }} iconProps={{ boxSize: '16px' }}>
                {trans('t_overhead_cost_tooltip')}
              </InfoIconToolTip>
            </Flex>

            <BaseFormNumberInput
              width='156px'
              control={control}
              name='costVariables.overheadCosts'
              numericFormatProps={{ fixedDecimalScale: true, decimalScale: 2, prefix: '$' }}
            />
          </Flex>
        </Flex>
      </AdminOrSupervisorWrapper>

      <ResetDataConfirmationModal
        onConfirm={() => {
          reset(defaultValues);
          handleGenerateProfitProjection(generateCycleInfoProjectionInput(defaultValues)).then();
        }}
      />
    </chakra.form>
  );
}

function generateCycleInfoProjectionInput(data: FormType) {
  const cycleInfoProjectionInput: PopulationCycleInformationInput = {
    projection: {
      overheadCostsPerHaPerDay: data.costVariables.overheadCosts,
      projectedFeed: {
        type: data.projectedFeed.feedType,
        expectedFeed: data.projectedFeed.expectedFeed || undefined,
        farmFeedTableId: data.projectedFeed.farmFeedTableId || undefined,
        feedAggressionMultiplier: isNumber(data.projectedFeed.feedAggressionMultiplier)
          ? getPercentageDivided(data.projectedFeed.feedAggressionMultiplier)
          : undefined,
        farmFeedTypes: data.feedTypes?.length
          ? data.feedTypes.map((f) => ({
              id: f.feedType,
              percentage: getPercentageDivided(f.percentage)
            }))
          : undefined
      },
      projectedSurvival: {
        projectedSurvivalType: data.projectedSurvival.projectedSurvivalType,
        expectedTargetSurvivalDays: data.projectedSurvival.expectedTargetSurvivalDays,
        dailyMortalityPercent: isNumber(data.projectedSurvival.dailyMortalityPercent)
          ? getPercentageDivided(data.projectedSurvival.dailyMortalityPercent)
          : undefined,
        expectedTargetSurvival: isNumber(data.projectedSurvival.expectedTargetSurvival)
          ? getPercentageDivided(data.projectedSurvival.expectedTargetSurvival)
          : undefined
      },
      projectedGrowth: {
        type: data.projectedGrowth.growthType,
        days: +data.projectedGrowth.days,
        grams: data.projectedGrowth.grams
      }
    }
  };
  return cycleInfoProjectionInput;
}

interface ProjectionDataTableProps {
  population: PopulationType;
  canGenerateProjections: boolean;
  errors: FieldErrors<FormType>;
  generatedProductionPrediction: PopulationProductionPrediction;
  isLoading: boolean;
  isSameDataSubmitted: boolean;
  onGenerateProfitProjection: () => void;
}
function ProjectionDataTable(props: ProjectionDataTableProps) {
  const {
    population,
    canGenerateProjections,
    isSameDataSubmitted,
    errors,
    generatedProductionPrediction,
    isLoading,
    onGenerateProfitProjection
  } = props;
  const { trans } = getTrans();

  const lang = useAppSelector((state) => state.app?.lang);
  const user = useAppSelector((state) => state.auth.user);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);

  const { stockedAtTimezone, stockedAt, history, harvestPlan, productionPrediction, harvest } = population ?? {};

  const hasProjection = !!harvestPlan?.projection?.profitProjectionData?.length && !harvest?.date;
  const lastHistory = history?.[0];
  const harvestPlanDate = harvestPlan?.harvestDate;

  const isGenerateButtonEnabled = canGenerateProjections || !!generatedProductionPrediction;
  const productionPredictionOnHarvestDate = productionPrediction?.find((item) => item.date === harvestPlanDate);
  const productionPredictionData = generatedProductionPrediction ?? productionPredictionOnHarvestDate;

  const {
    averageWeight: lastHistoryAbw,
    date: lastHistoryDate,
    weeklyGrowth: lastHistoryWeeklyGrowth,
    biomassLbs: lastHistoryBiomassLbs,
    totalBiomassLbsByHa: lastHistoryTotalBiomassLbsByHa,
    survivalWithPartialHarvest: lastHistorySurvivalWithPartialHarvest,
    adjustedFcr: lastHistoryAdjustedFcr,
    totalFeedGivenKg: lastHistoryTotalFeedGivenKg
  } = lastHistory ?? {};

  const {
    daysOfCulture: harvestPlanDaysOfCulture,
    averageWeight: harvestPlanAbw,
    weeklyGrowth: harvestPlanWeeklyGrowth,
    biomassLbs: harvestPlanBiomassLbs,
    totalBiomassLbsByHa: harvestPlanTotalBiomassLbsByHa,
    survivalWithPartialHarvest: harvestPlanSurvivalWithPartialHarvest,
    adjustedFcr: harvestPlanAdjustedFcr,
    totalFeedGivenKg: harvestPlanTotalFeedGivenKg
  } = productionPredictionData ?? {};

  const harvestPlanDateFormatted = harvestPlanDate ? DateTime.fromISO(harvestPlanDate).toFormat('MMMM dd, yyyy') : null;
  const stockedAtFormatted = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone })
    .startOf('day')
    .toFormat('yyyy-MM-dd');
  const lastHistoryDateLuxon = DateTime.fromISO(lastHistoryDate).startOf('day');

  const daysOfCulture = getDaysDiffBetweenDates({
    baseDate: lastHistoryDateLuxon.toFormat('yyyy-MM-dd'),
    dateToCompare: stockedAtFormatted
  });
  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');

  const thTextProps: TextProps = {
    size: 'label300',
    color: 'text.gray.disabled',
    whiteSpace: 'normal'
  };

  const canGenerate = isGenerateButtonEnabled && isEmpty(errors) && !isSameDataSubmitted && hasProjection;

  const handleSimulateClick = () => {
    onGenerateProfitProjection();
  };

  return (
    <Flex direction='column' gap='sm-alt'>
      <TableContainer p='md' bg='white' rounded='xl' minH='168px' pos='relative'>
        <SectionLoader isLoading={isLoading} />
        <Table.Root>
          <Table.Header>
            <Table.Row>
              <Table.ColumnHeader />

              <Table.ColumnHeader>
                <Text {...thTextProps}>{trans('t_date')}</Text>
              </Table.ColumnHeader>
              <Table.ColumnHeader>
                <Text {...thTextProps}>{trans('t_days_of_culture')}</Text>
              </Table.ColumnHeader>
              <Table.ColumnHeader>
                <Text {...thTextProps}>{trans('t_abw_g')}</Text>
              </Table.ColumnHeader>
              <Table.ColumnHeader>
                <Text {...thTextProps}>{trans('t_growth_g_wk')}</Text>
              </Table.ColumnHeader>
              <Table.ColumnHeader>
                <Text {...thTextProps}>{trans('t_biomass_in_pond_unit', { unit: unitLabel })}</Text>
              </Table.ColumnHeader>
              <Table.ColumnHeader>
                <Text {...thTextProps}>{trans('t_biomass_include_harvests_unit_ha', { unit: unitLabel })}</Text>
              </Table.ColumnHeader>
              <Table.ColumnHeader>
                <Text {...thTextProps}>{trans('t_survival_include_harvests')}</Text>
              </Table.ColumnHeader>
              <Table.ColumnHeader>
                <Text {...thTextProps}>{trans('t_fcr_adjusted')}</Text>
              </Table.ColumnHeader>
              <Table.ColumnHeader>
                <Text {...thTextProps}>{trans('t_feed_given_kg')}</Text>
              </Table.ColumnHeader>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            <Table.Row>
              <Table.Cell>
                <Text size='label200'>{trans('t_latest_monitoring')}</Text>
              </Table.Cell>
              <Table.Cell>
                <Text size='label200'>
                  {lastHistoryDateLuxon.isValid ? lastHistoryDateLuxon.toFormat('MMMM dd, yyyy') : '-'}
                </Text>
              </Table.Cell>
              <Table.Cell>
                <Text size='label200'>{isNumber(daysOfCulture) ? daysOfCulture : '-'}</Text>
              </Table.Cell>
              <Table.Cell>
                <Text size='label200'>
                  {isNumber(lastHistoryAbw) ? formatNumber(lastHistoryAbw, { lang, fractionDigits: 2 }) : '-'}
                </Text>
              </Table.Cell>
              <Table.Cell>
                <Text size='label200'>
                  {isNumber(lastHistoryWeeklyGrowth)
                    ? formatNumber(lastHistoryWeeklyGrowth, { fractionDigits: 2, lang })
                    : '-'}
                </Text>
              </Table.Cell>
              <Table.Cell>
                <Text size='label200'>
                  {isNumber(lastHistoryBiomassLbs)
                    ? formatNumber(convertUnitByDivision(lastHistoryBiomassLbs, unitsConfig?.biomass), {
                        lang,
                        fractionDigits: 0
                      })
                    : '-'}
                </Text>
              </Table.Cell>
              <Table.Cell>
                <Text size='label200'>
                  {isNumber(lastHistoryTotalBiomassLbsByHa)
                    ? formatNumber(convertUnitByDivision(lastHistoryTotalBiomassLbsByHa, unitsConfig?.biomass), {
                        lang,
                        fractionDigits: 0
                      })
                    : '-'}
                </Text>
              </Table.Cell>
              <Table.Cell>
                <Text size='label200'>
                  {isNumber(lastHistorySurvivalWithPartialHarvest)
                    ? formatNumber(lastHistorySurvivalWithPartialHarvest, {
                        lang,
                        fractionDigits: 0,
                        isPercentage: true
                      })
                    : '-'}
                </Text>
              </Table.Cell>
              <Table.Cell>
                <Text size='label200'>
                  {isNumber(lastHistoryAdjustedFcr)
                    ? formatNumber(lastHistoryAdjustedFcr, { fractionDigits: 2, lang })
                    : '-'}
                </Text>
              </Table.Cell>
              <Table.Cell>
                <Text size='label200'>
                  {isNumber(lastHistoryTotalFeedGivenKg)
                    ? formatNumber(lastHistoryTotalFeedGivenKg, { fractionDigits: 0, lang })
                    : '-'}
                </Text>
              </Table.Cell>
            </Table.Row>
            <Table.Row>
              <Table.Cell>
                <Text size='label200'>{trans('t_projected_at_final_harvest')}</Text>
              </Table.Cell>
              <Table.Cell>
                <Text size='label200'>{harvestPlanDateFormatted ?? '-'}</Text>
              </Table.Cell>
              <Table.Cell>
                <Text size='label200'>{isNumber(harvestPlanDaysOfCulture) ? harvestPlanDaysOfCulture : '-'}</Text>
              </Table.Cell>
              {!hasProjection && (
                <Table.Cell colSpan={7}>
                  <Flex align='center'>
                    <Text size='label200'>{trans('t_projections_not_available')}</Text>

                    <InfoIconToolTip>
                      <Text>{trans('t_projections_not_available_for_pond')}</Text>
                    </InfoIconToolTip>
                  </Flex>
                </Table.Cell>
              )}
              {hasProjection && (
                <>
                  <Table.Cell>
                    <Text size='label200'>
                      {isNumber(harvestPlanAbw) ? formatNumber(harvestPlanAbw, { lang, fractionDigits: 2 }) : '-'}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text size='label200'>
                      {isNumber(harvestPlanWeeklyGrowth)
                        ? formatNumber(harvestPlanWeeklyGrowth, { fractionDigits: 2, lang })
                        : '-'}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text size='label200'>
                      {isNumber(harvestPlanBiomassLbs)
                        ? formatNumber(convertUnitByDivision(harvestPlanBiomassLbs, unitsConfig?.biomass), {
                            lang,
                            fractionDigits: 0
                          })
                        : '-'}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text size='label200'>
                      {isNumber(harvestPlanTotalBiomassLbsByHa)
                        ? formatNumber(convertUnitByDivision(harvestPlanTotalBiomassLbsByHa, unitsConfig?.biomass), {
                            lang,
                            fractionDigits: 0
                          })
                        : '-'}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text size='label200'>
                      {isNumber(harvestPlanSurvivalWithPartialHarvest)
                        ? formatNumber(harvestPlanSurvivalWithPartialHarvest, {
                            lang,
                            fractionDigits: 0,
                            isPercentage: true
                          })
                        : '-'}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text size='label200'>
                      {isNumber(harvestPlanAdjustedFcr)
                        ? formatNumber(harvestPlanAdjustedFcr, { fractionDigits: 2, lang })
                        : '-'}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text size='label200'>
                      {isNumber(harvestPlanTotalFeedGivenKg)
                        ? formatNumber(harvestPlanTotalFeedGivenKg, { fractionDigits: 0, lang })
                        : '-'}
                    </Text>
                  </Table.Cell>
                </>
              )}
            </Table.Row>
          </Table.Body>
        </Table.Root>
      </TableContainer>

      <BaseButton
        ms='auto'
        p={0}
        size='sm'
        variant='link'
        disabled={!canGenerate || isLoading}
        onClick={handleSimulateClick}
      >
        <RefreshIcon color={!canGenerate || isLoading ? 'text.gray.disabled' : 'button.link'} />
        {trans('t_simulate_projections')}
      </BaseButton>
    </Flex>
  );
}

interface ResetDataConfirmationModalProps {
  onConfirm: () => void;
}
function ResetDataConfirmationModal(props: ResetDataConfirmationModalProps) {
  const { onConfirm } = props;
  const { trans } = getTrans();
  const handleSubmit = () => {
    onConfirm();
  };
  return (
    <DialogRoot size='md' motionPreset='slide-in-top'>
      <DialogTrigger asChild>
        <BaseButton p={0} variant='link' color={'text.gray'} size='sm' ms='auto'>
          <RefreshIcon color={'text.gray'} /> {trans('t_reset_to_last_saved_values')}
        </BaseButton>
      </DialogTrigger>

      <DialogContent portalled={false}>
        <DialogHeader>
          <DialogTitle>{trans('t_reset_to_last_saved_values')}</DialogTitle>
        </DialogHeader>
        <DialogBody gap='md'>
          <Text size='label100' color='text.gray.weak' mb='2lg' lineHeight='20px'>
            {trans('t_reset_data_confirmation')}
          </Text>
          <Flex justify='flex-end' gap='sm-alt'>
            <DialogActionTrigger asChild>
              <BaseButton w='fit-content' variant='secondary'>
                {trans('t_cancel')}
              </BaseButton>
            </DialogActionTrigger>
            <DialogActionTrigger asChild>
              <BaseButton w='fit-content' onClick={handleSubmit}>
                {trans('t_reset')}
              </BaseButton>
            </DialogActionTrigger>
          </Flex>
        </DialogBody>
      </DialogContent>
    </DialogRoot>
  );
}

function ProjectedSurvivalWarnings({
  survival,
  days,
  lastSurvivalCycleDays,
  lastSurvival
}: {
  survival: number;
  days: number;
  lastSurvivalCycleDays: number;
  lastSurvival: number;
}) {
  const { trans } = getTrans();

  const isPast = days && lastSurvivalCycleDays ? days < lastSurvivalCycleDays + 1 : false;
  const isGreaterThanCurrent = survival && lastSurvival ? survival > lastSurvival : false;

  if (!isPast && !isGreaterThanCurrent) return null;

  return (
    <>
      {isPast && <WarningBox text={trans('t_projected_survival_must_be_future')} />}
      {isGreaterThanCurrent && <WarningBox text={trans('t_projected_survival_must_be_lower_than_current')} />}
    </>
  );
}

interface PredictionGrowthProps extends FlexProps {
  prediction: PopulationPrediction[];
  lang: string;
  lastHistoryDate: string;
  plannedHarvestDate: string;
}

function PredictionGrowth(props: PredictionGrowthProps) {
  const { prediction, lang, lastHistoryDate, plannedHarvestDate, ...rest } = props;
  const { trans } = getTrans();
  const weeklyGrowth = findPredictionGrowthLastHistoryToHarvestDate({
    prediction,
    lastHistoryDate,
    plannedHarvestDate
  });

  if (!weeklyGrowth) return <Text>-</Text>;

  const weeklyGrowthNumber = `${formatNumber(weeklyGrowth, { fractionDigits: 2, lang, signDisplay: 'exceptZero' })} ${trans('t_g_over_wk')}`;

  return (
    <Flex gap='xs-alt' align='center' flexWrap='wrap' {...rest}>
      <Text textAlign='center'>{weeklyGrowthNumber}</Text>
      <Text textTransform='lowercase'>{trans('t_until_harvest')}</Text>
    </Flex>
  );
}

function WarningBox({ text }: { text: string }) {
  return (
    <Flex
      gap='sm'
      backgroundColor='red.50'
      border='1px solid'
      borderColor='shrimpyPinky.600'
      borderRadius='md'
      py='sm-alt'
      px='md'
    >
      <WarningIcon color='shrimpyPinky.600' />
      <Text>{text}</Text>
    </Flex>
  );
}

function SelectContainer(props: ComponentProps<typeof NativeSelectRoot> & { error: string }) {
  const { error, disabled, ...rest } = props;
  const selectProps = {
    h: '40px',
    w: '188px'
  };

  return (
    <Field.Root w='max-content' invalid={!!error} disabled={disabled}>
      <NativeSelectRoot {...selectProps} {...rest} />
      {error && <Field.ErrorText textStyle='label.300'>{error}</Field.ErrorText>}
    </Field.Root>
  );
}
