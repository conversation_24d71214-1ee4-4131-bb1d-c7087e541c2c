import { Box, chakra, Flex, Text } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { BaseFormNumberInput } from '@components/form/base-form-number-input';
import { FormControlDateInput } from '@components/form/form-control-date-input';
import { yupResolver } from '@hookform/resolvers/yup';
import { useAnalytics } from '@hooks/use-analytics';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { useReloadCurrentFarmPonds } from '@screens/farm/hooks/use-reload-current-farm-ponds';
import { useReloadCurrentPond } from '@screens/pond/hooks/use-reload-current-pond';
import { formatNumber } from '@utils/number';
import { actionsName } from '@utils/segment';
import { numberTransform, useYupSchema, Yup, YupResolverType } from '@utils/yup';
import { EnumPopulationStockingType, Nursery, PopulationHistory } from '@xpertsea/module-farm-sdk';
import { Control, FieldErrors, useForm, UseFormWatch } from 'react-hook-form';
import { useUpdatePopulationApi } from '@screens/population/hooks/use-update-population-api';
import {
  GeneticSelectContainer,
  HatcherySelectContainer,
  NurseriesTransferInformation,
  StockFormValues
} from '@screens/pond/components/modals/add-edit-pond-stocking/pond-stocking-shared';
import {
  formContainerStyles,
  inputStyles,
  labelStyles
} from '@screens/pond/components/modals/helpers/chakra-form-styles';
import { useMemo } from 'react';
import { DateTime } from 'luxon';
import { AdminOrSupervisorWrapper } from '@components/permission-wrappers/admin-or-supervisor-wrapper';
import { DialogBody, DialogFooter, DialogHeader, DialogTitle } from '@components/ui/dialog';

const dateToBeTwoYearsLess = new Date();
dateToBeTwoYearsLess.setFullYear(dateToBeTwoYearsLess.getFullYear() - 2);

interface DirectPondStockingFormModalContentProps {
  pondId: string;
  pondName: string;
  pondSize: number;
  isHarvested: boolean;
  firstMonitoring: PopulationHistory;
  isInPondView?: boolean;
  isInPondListView?: boolean;
  onClose: () => void;
}

export function DirectPondStockingFormModalContent(props: DirectPondStockingFormModalContentProps) {
  const { pondId, pondName, pondSize, isHarvested, firstMonitoring, isInPondView, isInPondListView, onClose } = props;

  const { trans } = getTrans();

  /// Hooks
  const { trackAction } = useAnalytics();
  const { reloadCurrentPond } = useReloadCurrentPond();
  const { reloadCurrentFarmPonds } = useReloadCurrentFarmPonds();

  const [{ isLoading: isUpdating }, updatePopulation] = useUpdatePopulationApi();

  /// Redux
  const lang = useAppSelector((state) => state.app.lang);
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);
  const currentPopulation = useAppSelector((state) => state.farm.currentPopulation);
  const pondNurseries = useAppSelector((state) => state.farm.currentPondNurseries);

  /// FORM DATA
  const { timezone } = currentFarm ?? {};
  const {
    _id: populationId,
    seedingAverageWeight,
    seedingQuantity,
    stockedAt,
    stockedAtTimezone,
    stockingCostsMillar,
    dryDaysBeforeStocking,
    stockingType,
    nurserySources
  } = currentPopulation ?? {};

  const nurseriesSources = useMemo(() => {
    if (stockingType !== 'transfer') return [];
    const results: Nursery[] = [];
    nurserySources.forEach((source) => {
      const nursery = pondNurseries?.find((nursery) => nursery._id === source.nurseryId);
      if (nursery) results.push(nursery);
    });
    return results;
  }, [stockingType, nurserySources, pondNurseries]);

  const defaultValues: StockFormValues = {
    stockingCostsMillar,
    stockedAt: stockedAt ? DateTime.fromISO(stockedAt).setZone(stockedAtTimezone).toJSDate() : new Date(),
    seedingAverageWeight,
    seedingQuantity,
    dryDaysBeforeStocking
  };

  const defaultMinValue = 0;
  const defaultMaxCostMillar = 50_000;

  const { schema } = useYupSchema({
    stockedAt: Yup.date()
      .transform(numberTransform)
      .min(dateToBeTwoYearsLess, trans('t_min_allowed_stocked'))
      .required(trans('t_required'))
      .label('t_stocked_at'),
    seedingAverageWeight: Yup.number()
      .transform(numberTransform)
      .positive()
      .nullable()
      .test((seedingAverageWeight, { createError, path }) => {
        if (isHarvested) {
          return true;
        }
        const firstMonitoringAverageWeight = firstMonitoring?.averageWeight;

        if (!firstMonitoringAverageWeight) {
          return true;
        }

        if (seedingAverageWeight > firstMonitoringAverageWeight) {
          return createError({
            path,
            message: trans('t_average_weight_should_be_less_than_first_monitoring_average_weight', {
              firstMonitoringAverageWeight: formatNumber(firstMonitoringAverageWeight, { lang, fractionDigits: 2 })
            })
          });
        }

        return true;
      })
      .required(trans('t_required'))
      .label('t_stocking_average_weight'),
    seedingQuantity: Yup.number()
      .transform(numberTransform)
      .positive()
      .nullable()
      .required(trans('t_required'))
      .label('t_animals_stocked'),
    dryDaysBeforeStocking: Yup.number()
      .transform(numberTransform)
      .min(defaultMinValue, trans('t_min_x', { min: defaultMinValue }))
      .nullable()
      .required(trans('t_required'))
      .label('t_dry_days'),
    stockingCostsMillar: Yup.number()
      .transform(numberTransform)
      .min(defaultMinValue, trans('t_min_x', { min: defaultMinValue }))
      .max(defaultMaxCostMillar, trans('t_max_x', { max: defaultMaxCostMillar }))
      .nullable()
      .label('t_cost_millar'),
    hatcheryName: Yup.string().nullable().label('t_hatchery'),
    geneticName: Yup.string().nullable().label('t_genetic')
  });

  const {
    handleSubmit,
    control,
    formState: { errors },
    watch,
    reset
  } = useForm<StockFormValues>({
    resolver: yupResolver(schema) as YupResolverType<StockFormValues>,
    defaultValues
  });

  /// Handlers
  const handleReload = () => {
    if (isInPondView) return reloadCurrentPond();
    if (isInPondListView) return reloadCurrentFarmPonds();
  };

  const handleModalClose = () => {
    reset(defaultValues);
    onClose();
  };

  const onSubmit = (data: StockFormValues) => {
    trackAction(actionsName.pondEditStock, { pondName, pondId }).then();
    updatePopulation({
      params: {
        filter: { populationId },
        set: {
          ...data,
          stockedAtTimezone: timezone
        }
      },
      successCallback: () => {
        onClose();
        handleReload();
      }
    });
  };

  return (
    <chakra.form data-cy='stock-pond-modal' pos='relative' onSubmit={handleSubmit(onSubmit)} noValidate>
      <DialogHeader
        p='sm-alt'
        flex={1}
        borderBottom='0.5px solid'
        borderColor='border.gray'
        mb='sm-alt'
        display='flex'
        alignItems='center'
      >
        <DialogTitle>{trans('t_stocking')}</DialogTitle>
        <Text mt='2xs'>
          {pondName} ({pondSize} {trans('t_ha')})
        </Text>
      </DialogHeader>

      <DialogBody px='sm-alt' py='sm-alt'>
        <StockingFormFields
          errors={errors}
          control={control}
          watch={watch}
          pondSize={pondSize}
          stockingType={stockingType}
          pondId={pondId}
          nurseriesSources={nurseriesSources}
        />
      </DialogBody>

      <DialogFooter gap='sm-alt' p='sm-alt' pos='sticky' bottom={0} bgColor='white'>
        <BaseButton
          color='gray.700'
          variant='link'
          size='sm'
          fontWeight={500}
          onClick={handleModalClose}
          analyticsId={actionsName.addStockingCancelClicked}
          analyticsData={{ pondId, pondName }}
          data-cy='cancel-btn'
        >
          {trans('t_cancel')}
        </BaseButton>

        <BaseButton
          type='submit'
          size='sm'
          loading={isUpdating}
          analyticsId={actionsName.addStockingSaveClicked}
          analyticsData={{ pondId, pondName }}
          data-cy='save-btn'
        >
          {trans('t_save_changes')}
        </BaseButton>
      </DialogFooter>
    </chakra.form>
  );
}

interface StockingFormFields {
  control: Control<StockFormValues>;
  errors: FieldErrors<StockFormValues>;
  watch: UseFormWatch<StockFormValues>;
  pondSize: number;
  stockingType: EnumPopulationStockingType;
  pondId: string;
  nurseriesSources?: Nursery[];
}

function StockingFormFields(props: StockingFormFields) {
  const { control, errors, watch, pondSize, stockingType, nurseriesSources, pondId } = props;

  const { trans } = getTrans();
  const lang = useAppSelector((state) => state.app.lang);
  const watchedSeedingQuantity = watch('seedingQuantity');
  const stockingDensity = watchedSeedingQuantity
    ? formatNumber(watchedSeedingQuantity / pondSize, { fractionDigits: 0, lang })
    : null;

  return (
    <Flex direction='column' gap='xs-alt'>
      <Box {...formContainerStyles}>
        <Text asChild>
          <label htmlFor='stockedAt'>{trans('t_stocking_date')}</label>
        </Text>
        <FormControlDateInput
          id='stockedAt'
          name='stockedAt'
          error={errors?.stockedAt?.message}
          control={control}
          inputProps={inputStyles}
          labelProps={labelStyles}
          maxW='180px'
          maxDate={new Date()}
          dateFormat='MMM dd, yyyy'
          placement='left-end'
        />
      </Box>

      <Box {...formContainerStyles}>
        <Text>{trans('t_type_of_stocking')}</Text>
        <Text>
          {stockingType === 'transfer' ? trans('t_transfer') : stockingType === 'direct' ? trans('t_direct') : '-'}
        </Text>
      </Box>

      {!!nurseriesSources?.length && <NurseriesTransferInformation nurseries={nurseriesSources} pondId={pondId} />}

      <BaseFormNumberInput
        required
        control={control}
        name='seedingAverageWeight'
        label={trans('t_stocking_weight_g')}
        {...formContainerStyles}
        inputProps={inputStyles}
        formLabelProps={labelStyles}
        inputContainerProps={{ w: 'auto' }}
        numericFormatProps={{ decimalScale: 2, fixedDecimalScale: true }}
      />

      <BaseFormNumberInput
        control={control}
        name='seedingQuantity'
        required
        {...formContainerStyles}
        inputProps={inputStyles}
        formLabelProps={labelStyles}
        inputContainerProps={{ w: 'auto' }}
        label={trans('t_animals_stocked')}
        numericFormatProps={{ decimalScale: 0 }}
      />

      <Box {...formContainerStyles}>
        <Text>{trans('t_stocking_density_ha')}</Text>
        <Text w='100px' textAlign='center'>
          {stockingDensity ?? '-'}
        </Text>
      </Box>

      <BaseFormNumberInput
        control={control}
        name='dryDaysBeforeStocking'
        required
        {...formContainerStyles}
        inputProps={inputStyles}
        formLabelProps={labelStyles}
        label={trans('t_dry_days')}
        inputContainerProps={{ w: 'auto' }}
        numericFormatProps={{ decimalScale: 0 }}
      />

      <AdminOrSupervisorWrapper>
        <BaseFormNumberInput
          control={control}
          name='stockingCostsMillar'
          required
          {...formContainerStyles}
          inputProps={inputStyles}
          formLabelProps={labelStyles}
          inputContainerProps={{ w: 'auto' }}
          label={trans('t_cost_millar')}
          numericFormatProps={{ decimalScale: 2, prefix: '$', fixedDecimalScale: true }}
        />
      </AdminOrSupervisorWrapper>
      <HatcherySelectContainer control={control} error={errors?.hatcheryName?.message} />

      <GeneticSelectContainer control={control} error={errors?.geneticName?.message} />
    </Flex>
  );
}
