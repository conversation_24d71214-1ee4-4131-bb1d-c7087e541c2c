import { Box, BoxProps, Flex, Heading, Text, useDisclosure } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { BaseFormNumberInput } from '@components/form/base-form-number-input';
import { FormControlDateInput } from '@components/form/form-control-date-input';
import { yupResolver } from '@hookform/resolvers/yup';
import { useAnalytics } from '@hooks/use-analytics';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { useReloadCurrentFarmPonds } from '@screens/farm/hooks/use-reload-current-farm-ponds';
import { useReloadCurrentPond } from '@screens/pond/hooks/use-reload-current-pond';
import { useStockPondApi } from '@screens/pond/hooks/use-stock-pond-api';
import { convertUnitByMultiplication, formatNumber } from '@utils/number';
import { actionsName } from '@utils/segment';
import { numberTransform, useYupSchema, Yup, YupResolverType } from '@utils/yup';
import { PopulationHistory } from '@xpertsea/module-farm-sdk';
import { DateTime } from 'luxon';
import { ReactNode, useEffect } from 'react';
import { Control, FieldErrors, useForm, UseFormSetValue, UseFormWatch } from 'react-hook-form';
import {
  formContainerStyles,
  inputStyles,
  labelStyles
} from '@screens/pond/components/modals/helpers/chakra-form-styles';
import { StockFormValues } from '@screens/pond/components/modals/target-form-fields';
import { DialogContent, DialogRoot } from '@components/ui/dialog';
import { sqPerHectare } from '@utils/constants';
import { useUpdatePopulationApi } from '@screens/population/hooks/use-update-population-api';

const dateToBeTwoYearsLess = new Date();
dateToBeTwoYearsLess.setFullYear(dateToBeTwoYearsLess.getFullYear() - 2);

interface VisionOnlyAddPondStockingModalProps {
  pondId: string;
  pondName: string;
  pondSize: number;
  isHarvested: boolean;
  firstMonitoring: PopulationHistory;
  children: ReactNode;
  isInPondView?: boolean;
  isInPondListView?: boolean;
  containerProps?: BoxProps;
  isEdit?: boolean;
}

export function VisionOnlyAddEditPondStockingModal(props: VisionOnlyAddPondStockingModalProps) {
  const { children, pondId, pondName, containerProps = {} } = props;

  const { onOpen, onClose, open } = useDisclosure();

  const { trackAction } = useAnalytics();

  const handleModalOpen = () => {
    trackAction(actionsName.stockPondClicked, { pondName, pondId, isVisionOnly: true }).then();
    onOpen();
  };

  const handleClose = () => {
    onClose();
  };

  return (
    <DialogRoot
      open={open}
      size='lg'
      restoreFocus={false}
      onOpenChange={(e) => {
        e.open ? handleModalOpen() : handleClose();
      }}
    >
      <Box
        onClick={(e) => {
          e.stopPropagation();
          handleModalOpen();
        }}
        {...containerProps}
      >
        {children}
      </Box>
      <DialogContent rounded='2xl' overflow='normal' bg='white' px='lg' py='md' data-cy='stock-pond-modal'>
        <Form {...props} onClose={handleClose} />
      </DialogContent>
    </DialogRoot>
  );
}

interface FormProps extends VisionOnlyAddPondStockingModalProps {
  onClose: () => void;
}

function Form(props: FormProps) {
  const { pondId, pondName, pondSize, isHarvested, firstMonitoring, isInPondView, isInPondListView, onClose, isEdit } =
    props;

  const { trans } = getTrans();

  /// Hooks
  const { trackAction } = useAnalytics();
  const { reloadCurrentPond, isLoading: isLoadingPond } = useReloadCurrentPond();
  const { reloadCurrentFarmPonds, isLoading: isLoadingPonds } = useReloadCurrentFarmPonds();

  const [{ isLoading: isCreating }, stockPond] = useStockPondApi();
  const [{ isLoading: isUpdating }, updatePopulation] = useUpdatePopulationApi();

  /// Redux
  const lang = useAppSelector((state) => state.app.lang);
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);
  const currentPopulation = useAppSelector((state) => state.farm.currentPopulation);
  const user = useAppSelector((state) => state.auth.user);

  /// FORM DATA
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};

  const { stockingConfig, growthTarget, feedTable, feedTypes, timezone } = currentFarm ?? {};

  const {
    _id: populationId,
    seedingAverageWeight,
    seedingQuantity,
    stockedAt,
    stockedAtTimezone,
    dryDaysBeforeStocking,
    cycleInformation,
    harvest,
    cycle: populationCycle
  } = currentPopulation ?? {};

  const { carryingCapacity, equipment, projection } = cycleInformation ?? {};
  const {
    aeratorType,
    numberOfAutoFeeders,
    autoFeederBrand,
    autoFeederProtocol = 'timer',
    numberOfAerators,
    feedBrand
  } = equipment ?? {};

  const { projectedFeed } = projection ?? {};
  const { farmFeedTypes } = projectedFeed ?? {};
  const defaultFarmFeedTableId = feedTable?.find((item) => item.isDefault)?._id;
  const defaultFarmFeedTypeId = feedTypes?.find((item) => item.isDefault)?._id;

  const { weight: harvestWeight, productionDays: cycleLength, fcr, biomassLbsHa } = growthTarget ?? {};
  const { summary } = stockingConfig ?? {};
  const { dryDays, growthDensity, avgStockingWeight } = summary ?? {};

  const totalSQ = pondSize * sqPerHectare;
  const defaultQuantity = growthDensity ? growthDensity * totalSQ : null;

  const defaultValues: Partial<StockFormValues> = {
    cycle: isEdit ? +populationCycle : null,
    stockedAt: stockedAt
      ? DateTime.fromISO(stockedAt).setZone(stockedAtTimezone).toJSDate()
      : DateTime.local().toJSDate(),
    seedingAverageWeight: isEdit ? seedingAverageWeight : avgStockingWeight,
    seedingQuantity: isEdit ? seedingQuantity : defaultQuantity,
    dryDaysBeforeStocking: isEdit ? dryDaysBeforeStocking : dryDays
  };

  const defaultMinValue = 0;

  const { schema } = useYupSchema({
    cycle: isEdit ? Yup.string() : Yup.string().required(trans('t_required')).label('t_cycle_number'),
    stockedAt: Yup.date()
      .transform(numberTransform)
      .min(dateToBeTwoYearsLess, trans('t_min_allowed_stocked'))
      .required(trans('t_required'))
      .label('t_stocked_at'),
    seedingAverageWeight: Yup.number()
      .transform(numberTransform)
      .positive()
      .nullable()
      .test((seedingAverageWeight, { createError, path }) => {
        if (isHarvested) {
          return true;
        }
        const firstMonitoringAverageWeight = firstMonitoring?.averageWeight;

        if (!firstMonitoringAverageWeight) {
          return true;
        }

        if (seedingAverageWeight > firstMonitoringAverageWeight) {
          return createError({
            path,
            message: trans('t_average_weight_should_be_less_than_first_monitoring_average_weight', {
              firstMonitoringAverageWeight: formatNumber(firstMonitoringAverageWeight, { lang, fractionDigits: 2 })
            })
          });
        }

        return true;
      })
      .required(trans('t_required'))
      .label('t_stocking_average_weight'),
    seedingQuantity: Yup.number()
      .transform(numberTransform)
      .positive()
      .nullable()
      .required(trans('t_required'))
      .label('t_animals_stocked'),
    dryDaysBeforeStocking: Yup.number()
      .transform(numberTransform)
      .min(defaultMinValue, trans('t_min_x', { min: defaultMinValue }))
      .nullable()
      .required(trans('t_required'))
      .label('t_dry_days')
  });

  const {
    handleSubmit,
    control,
    formState: { errors },
    watch,
    setValue,
    reset
  } = useForm<StockFormValues>({
    resolver: yupResolver(schema) as YupResolverType<StockFormValues>,
    defaultValues
  });

  /// Handlers
  const handleModalClose = () => {
    onClose();
  };
  const handleReload = () => {
    if (isInPondView) return reloadCurrentPond(handleModalClose);
    if (isInPondListView) return reloadCurrentFarmPonds({ successCallback: handleModalClose });
  };

  const handleModalCancel = () => {
    reset(defaultValues);
    handleModalClose();
  };

  const onSubmit = (data: StockFormValues) => {
    const { cycle, cycleInformation } = data;

    if (isEdit) {
      trackAction(actionsName.pondEditStock, { pondName, pondId }).then();

      updatePopulation({
        params: {
          filter: { populationId },
          set: {
            ...data,
            cycle: populationCycle,
            stockedAtTimezone: timezone
          }
        },
        successCallback: () => {
          handleReload();
        }
      });
      return;
    }

    trackAction(actionsName.pondStocked, { pondName, pondId }).then();
    const farmFeedTypeId = farmFeedTypes?.[0]?.id ?? defaultFarmFeedTypeId;
    stockPond({
      params: {
        pondId,
        ...data,
        cycle: cycle.toString(),
        stockedAtTimezone: timezone,
        stockingType: 'direct',
        cycleInformation: {
          ...cycleInformation,
          carryingCapacity: convertUnitByMultiplication(carryingCapacity, unitsConfig?.biomass),
          target: {
            harvestWeight,
            cycleLength,
            biomassToHarvest: biomassLbsHa,
            type: 'biomassToHarvest',
            fcr
          },
          equipment: {
            aeratorType,
            numberOfAutoFeeders,
            autoFeederBrand,
            autoFeederProtocol: autoFeederProtocol ?? 'timer',
            numberOfAerators,
            feedBrand
          },
          projection: {
            projectedFeed: {
              farmFeedTableId: defaultFarmFeedTableId,
              ...(farmFeedTypeId && {
                farmFeedTypes: [{ id: farmFeedTypeId, percentage: 1 }]
              })
            }
          }
        }
      },
      successCallback: () => {
        handleReload();
      }
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} noValidate>
      <Flex align='center' gap='sm-alt' mb='2lg'>
        <Heading size='heavy300'>{trans('t_confirm_stocking_details')}</Heading>
        <Text>
          {pondName} ({pondSize} {trans('t_ha')})
        </Text>
      </Flex>

      <StockingFormFields
        errors={errors}
        control={control}
        watch={watch}
        pondSize={pondSize}
        harvestDate={harvest?.date}
        setValue={setValue}
        isEdit={isEdit}
      />

      <Flex align='center' justify='flex-end' gap='md' mt='2lg'>
        <BaseButton
          variant='secondary'
          size='sm'
          onClick={handleModalCancel}
          analyticsId={actionsName.addStockingCancelClicked}
          analyticsData={{ pondId, pondName }}
          data-cy='cancel-btn'
        >
          {trans('t_cancel')}
        </BaseButton>

        <BaseButton
          type='submit'
          size='sm'
          loading={isCreating || isLoadingPond || isLoadingPonds}
          analyticsId={actionsName.addStockingSaveClicked}
          analyticsData={{ pondId, pondName }}
          data-cy='save-btn'
        >
          {trans('t_save_changes')}
        </BaseButton>
      </Flex>
    </form>
  );
}

interface StockingFormFields {
  control: Control<StockFormValues>;
  errors: FieldErrors<StockFormValues>;
  watch: UseFormWatch<StockFormValues>;
  setValue: UseFormSetValue<StockFormValues>;
  pondSize: number;
  harvestDate: string;
  isEdit?: boolean;
}

function StockingFormFields(props: StockingFormFields) {
  const { control, errors, watch, pondSize, harvestDate, setValue, isEdit } = props;
  const { trans } = getTrans();
  const lang = useAppSelector((state) => state.app.lang);
  const watchedSeedingQuantity = watch('seedingQuantity');
  const watchedStockingDate = watch('stockedAt');
  const stockingDensity = watchedSeedingQuantity
    ? formatNumber(watchedSeedingQuantity / pondSize, { fractionDigits: 0, lang })
    : null;

  const harvestDateLuxon = DateTime.fromISO(harvestDate).startOf('day');

  useEffect(() => {
    if (!watchedStockingDate || !harvestDate) return;
    const stockingDateLuxon = DateTime.fromJSDate(watchedStockingDate).startOf('day');
    const diff = stockingDateLuxon.diff(harvestDateLuxon, 'days').days;
    if (diff < 0) return;
    setValue('dryDaysBeforeStocking', diff);
  }, [watchedStockingDate, harvestDate]);

  return (
    <Flex direction='column' gap='xs-alt'>
      {!isEdit && (
        <BaseFormNumberInput
          name='cycle'
          required
          control={control}
          label={trans('t_cycle_number')}
          inputProps={{ autoFocus: true, ...inputStyles }}
          numericFormatProps={{ decimalScale: 0 }}
          formLabelProps={labelStyles}
          {...formContainerStyles}
          inputContainerProps={{ w: 'auto' }}
        />
      )}
      <Box {...formContainerStyles}>
        <Text asChild>
          <label htmlFor='stockedAt'>{trans('t_stocking_date')}</label>
        </Text>
        <FormControlDateInput
          id='stockedAt'
          name='stockedAt'
          error={errors?.stockedAt?.message}
          control={control}
          inputProps={inputStyles}
          labelProps={labelStyles}
          maxW='180px'
          minDate={harvestDateLuxon.toJSDate()}
          maxDate={new Date()}
          dateFormat='MMM dd, yyyy'
          placement='left-end'
        />
      </Box>

      <BaseFormNumberInput
        name='seedingAverageWeight'
        required
        control={control}
        label={trans('t_stocking_weight_g')}
        inputProps={inputStyles}
        formLabelProps={labelStyles}
        numericFormatProps={{ decimalScale: 2, fixedDecimalScale: true }}
        inputContainerProps={{ w: 'auto' }}
        {...formContainerStyles}
      />

      <BaseFormNumberInput
        control={control}
        name='seedingQuantity'
        required
        {...formContainerStyles}
        inputProps={inputStyles}
        formLabelProps={labelStyles}
        label={trans('t_animals_stocked')}
        numericFormatProps={{ decimalScale: 0 }}
        inputContainerProps={{ w: 'auto' }}
      />

      <Box {...formContainerStyles}>
        <Text fontSize='md' fontWeight={500}>
          {trans('t_stocking_density_ha')}
        </Text>
        <Text w='180px' textAlign='center' fontSize='md'>
          {stockingDensity ?? '-'}
        </Text>
      </Box>

      <BaseFormNumberInput
        control={control}
        name='dryDaysBeforeStocking'
        required
        {...formContainerStyles}
        inputProps={inputStyles}
        formLabelProps={labelStyles}
        label={trans('t_dry_days')}
        numericFormatProps={{ decimalScale: 0 }}
        inputContainerProps={{ w: 'auto' }}
      />
    </Flex>
  );
}
