import { Box, Flex, Text } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { useState } from 'react';
import { MdKeyboardArrowDown, MdKeyboardArrowUp } from 'react-icons/md';
import { formatNurseriesNames } from '@screens/pond/components/modals/helpers/nurseries-helpers';
import { CurrentPondNurseriesType } from '@redux/farm/set-current-pond-nurseries';
import { DateTime } from 'luxon';
import { formatNumber, isNumber } from '@utils/number';
import { Control } from 'react-hook-form';
import { useAppSelector } from '@redux/hooks';
import { FormControlReactSelect } from '@components/form/form-control-react-select';
import {
  formContainerStyles,
  labelStyles,
  selectControlStyles
} from '@screens/pond/components/modals/helpers/chakra-form-styles';
import { transformWeightWithUnit } from '@screens/nursery/helpers/transform-weight-with-unit';
import { AdminOrSupervisorWrapper } from '@components/permission-wrappers/admin-or-supervisor-wrapper';

interface NurseriesTransferInformationProps {
  nurseries: CurrentPondNurseriesType;
  pondId: string;
}

export function NurseriesTransferInformation({ nurseries, pondId }: NurseriesTransferInformationProps) {
  const { trans } = getTrans();

  const lang = useAppSelector((state) => state.app.lang);

  const [isOpen, setIsOpen] = useState(true);

  return (
    <Box px='md' bg='gray.100'>
      <Flex justify='space-between' py='sm-alt'>
        <Flex align='center' gap='md' onClick={() => setIsOpen((val) => !val)} cursor='pointer'>
          <Text>{trans('t_source')}</Text>
          {isOpen ? <MdKeyboardArrowUp /> : <MdKeyboardArrowDown />}
        </Flex>
        <Text>{formatNurseriesNames({ nurseries })}</Text>
      </Flex>

      {isOpen && (
        <Box>
          {nurseries.map((nursery) => {
            const { population, previousPopulations, name } = nursery;

            // let's merge the current population with the previous ones
            const populations = [];
            if (population) populations.push(population);
            if (previousPopulations) populations.push(...previousPopulations);

            let harvest = null;
            let transfers = null;
            for (const population of populations) {
              harvest = population?.harvest;
              transfers = harvest?.transfers || [];

              const hasTransferForCurrentPond = transfers?.find((ele) => ele.pondId === pondId);
              if (hasTransferForCurrentPond) break;
            }

            const { avgWeight, totalWeight, value, costsMillar, avgWeightUnit } =
              transfers?.find((ele) => ele.pondId === pondId) ?? {};

            const { weight, unitString } = transformWeightWithUnit({
              lang,
              weightInGrams: avgWeight,
              unit: avgWeightUnit
            });

            return (
              <Box key={nursery._id}>
                <Flex justify='space-between' bg='white' px='md' py='sm-alt' rounded='lg'>
                  <Text>{trans('t_source')}</Text>
                  <Text>{name}</Text>
                </Flex>

                <Flex flexDir='column' gap='sm-alt' ps='lg' pe='md' py='sm-alt'>
                  <Flex align='center' justify='space-between'>
                    <Text>{trans('t_transfer_date')}</Text>
                    <Text>
                      {harvest?.date ? DateTime.fromISO(harvest.date).toFormat('LLL dd, yyyy', { locale: lang }) : '-'}
                    </Text>
                  </Flex>

                  <Flex align='center' justify='space-between'>
                    <Text>
                      {trans('t_abw')} {unitString}
                    </Text>
                    <Text>{weight}</Text>
                  </Flex>

                  <Flex align='center' justify='space-between'>
                    <Text>{trans('t_Total_biomass_transferred')}</Text>
                    <Text>
                      {isNumber(totalWeight)
                        ? `${formatNumber(totalWeight, { lang, fractionDigits: 0 })} ${trans('t_kg')}`
                        : '-'}
                    </Text>
                  </Flex>

                  <Flex align='center' justify='space-between'>
                    <Text>{trans('t_transfer_amount')}</Text>
                    <Text>{isNumber(value) ? formatNumber(value, { lang, fractionDigits: 0 }) : '-'}</Text>
                  </Flex>

                  <AdminOrSupervisorWrapper>
                    <Flex align='center' justify='space-between'>
                      <Text>{trans('t_cost_millar')}</Text>
                      <Text>
                        {isNumber(costsMillar) ? `${formatNumber(costsMillar, { lang, fractionDigits: 2 })}` : '-'}
                      </Text>
                    </Flex>
                  </AdminOrSupervisorWrapper>
                </Flex>
              </Box>
            );
          })}
        </Box>
      )}
    </Box>
  );
}

export type StockFormValues = {
  stockedAt: Date;
  seedingAverageWeight: number;
  seedingQuantity: number;
  dryDaysBeforeStocking: number;
  stockingCostsMillar: number;
  hatcheryName?: string;
  geneticName?: string;
};

interface SelectContainerProps {
  control: Control<StockFormValues>;
  error: string;
}

export function HatcherySelectContainer(props: SelectContainerProps) {
  const { control, error } = props;
  const { trans } = getTrans();

  const currentFarm = useAppSelector((state) => state.farm?.currentFarm);

  const { otherConfig } = currentFarm ?? {};

  const { hatchery } = otherConfig ?? {};
  const hatcheryOptions = hatchery?.map((item: { name: string }) => ({
    label: item.name,
    value: item.name
  }));

  return (
    <Box {...formContainerStyles}>
      <Text asChild>
        <label htmlFor='hatcheryName'>{trans('t_lab_source')}</label>
      </Text>

      <FormControlReactSelect
        isOptional
        id='hatcheryName'
        name='hatcheryName'
        control={control}
        placeholder=' '
        options={hatcheryOptions}
        error={error}
        isSearchable={false}
        labelProps={labelStyles}
        formControlProps={{ w: selectControlStyles.minWidth }}
        selectControlStyles={selectControlStyles}
        menuPlacement='top'
      />
    </Box>
  );
}

export function GeneticSelectContainer(props: SelectContainerProps) {
  const { control, error } = props;
  const { trans } = getTrans();

  const currentFarm = useAppSelector((state) => state.farm?.currentFarm);

  const { otherConfig } = currentFarm ?? {};

  const { genetic } = otherConfig ?? {};
  const geneticOptions = genetic?.map((item: { name: string }) => ({
    label: item.name,
    value: item.name
  }));

  return (
    <Box {...formContainerStyles}>
      <Text asChild>
        <label htmlFor='geneticName'> {trans('t_nauplii_source')}</label>
      </Text>

      <FormControlReactSelect
        isOptional
        id='geneticName'
        name='geneticName'
        control={control}
        placeholder=' '
        options={geneticOptions}
        error={error}
        isSearchable={false}
        labelProps={labelStyles}
        formControlProps={{ w: selectControlStyles.minWidth }}
        selectControlStyles={selectControlStyles}
        menuPlacement='top'
      />
    </Box>
  );
}
