import { Box, BoxProps, Flex, Heading, Text, useDisclosure } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { BaseFormNumberInput } from '@components/form/base-form-number-input';
import { FormControlDateInput } from '@components/form/form-control-date-input';
import { yupResolver } from '@hookform/resolvers/yup';
import { useAnalytics } from '@hooks/use-analytics';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { useReloadCurrentFarmPonds } from '@screens/farm/hooks/use-reload-current-farm-ponds';
import { useReloadCurrentPond } from '@screens/pond/hooks/use-reload-current-pond';
import { useStockPondApi } from '@screens/pond/hooks/use-stock-pond-api';
import { convertUnitByDivision, convertUnitByMultiplication, formatNumber } from '@utils/number';
import { actionsName } from '@utils/segment';
import { numberTransform, useYupSchema, Yup, YupResolverType } from '@utils/yup';
import { EnumPopulationStockingType, PopulationHistory } from '@xpertsea/module-farm-sdk';
import { DateTime } from 'luxon';
import { ReactNode, useEffect, useState } from 'react';
import { Control, FieldErrors, useForm, UseFormSetValue, UseFormWatch } from 'react-hook-form';
import { FormControlReactSelect } from '@components/form/form-control-react-select';
import {
  formContainerStyles,
  inputStyles,
  labelStyles,
  selectControlStyles
} from '@screens/pond/components/modals/helpers/chakra-form-styles';
import { StockFormValues, TargetFormFields } from '@screens/pond/components/modals/target-form-fields';
import {
  CycleProtocolFormFields,
  SelectContainerProps
} from '@screens/pond/components/modals/cycle-protocol-form-fields';
import { AddPondStockingContainer } from '@screens/pond/components/modals/add-edit-pond-stocking/add-pond-stocking-container';
import { NurseriesTransferInformation } from '@screens/pond/components/modals/add-edit-pond-stocking/pond-stocking-shared';
import { CurrentPondNurseriesType } from '@redux/farm/set-current-pond-nurseries';
import { getStockingDefaultValuesFromNurseries } from '@screens/pond/components/modals/helpers/get-stocking-default-values-from-nurseries';
import { AdminOrSupervisorWrapper } from '@components/permission-wrappers/admin-or-supervisor-wrapper';
import isUndefined from 'lodash/isUndefined';
import { DialogContent, DialogRoot } from '@components/ui/dialog';
import { sqPerHectare } from '@utils/constants';

const dateToBeTwoYearsLess = new Date();
dateToBeTwoYearsLess.setFullYear(dateToBeTwoYearsLess.getFullYear() - 2);

interface AddStockingModalProps {
  pondId: string;
  pondName: string;
  pondSize: number;
  isHarvested: boolean;
  firstMonitoring: PopulationHistory;
  children: ReactNode;
  isInPondView?: boolean;
  isInPondListView?: boolean;
  containerProps?: BoxProps;
}

export function AddPondStockingModal(props: AddStockingModalProps) {
  const { children, pondId, pondName, containerProps = {} } = props;

  const { onOpen, onClose, open } = useDisclosure();

  const { trackAction } = useAnalytics();

  const handleModalOpen = () => {
    trackAction(actionsName.stockPondClicked, { pondName, pondId }).then();
    onOpen();
  };

  const [stockingType, setStockingType] = useState<'direct' | 'transfer'>();
  const [nurseries, setNurseries] = useState<CurrentPondNurseriesType>();

  const handleClose = () => {
    onClose();
    setStockingType(undefined);
  };

  const handleOnSelect = (stockingType: 'direct' | 'transfer', nurseries: CurrentPondNurseriesType) => {
    setStockingType(stockingType);
    if (stockingType === 'transfer') setNurseries(nurseries);
  };

  return (
    <DialogRoot
      open={open}
      size={stockingType ? 'lg' : 'xl'}
      restoreFocus={false}
      onOpenChange={(e) => {
        e.open ? handleModalOpen() : handleClose();
      }}
    >
      <Box
        onClick={(e) => {
          e.stopPropagation();
          handleModalOpen();
        }}
        {...containerProps}
      >
        {children}
      </Box>
      <DialogContent
        rounded='2xl'
        overflow='normal'
        bg={!stockingType ? 'bg.gray.medium' : 'white'}
        px='lg'
        py='md'
        data-cy={!stockingType ? 'pond-stocking-selection-modal' : 'stock-pond-modal'}
      >
        {!stockingType && <AddPondStockingContainer pondId={pondId} pondName={pondName} onSelect={handleOnSelect} />}
        {stockingType === 'direct' && <Form {...props} onClose={handleClose} />}
        {stockingType === 'transfer' && <Form {...props} onClose={handleClose} nurseries={nurseries} />}
      </DialogContent>
    </DialogRoot>
  );
}

interface FormProps {
  pondId: string;
  pondName: string;
  pondSize: number;
  isHarvested: boolean;
  firstMonitoring: PopulationHistory;
  isInPondView?: boolean;
  isInPondListView?: boolean;
  onClose: () => void;
  nurseries?: CurrentPondNurseriesType;
}

function Form(props: FormProps) {
  const {
    pondId,
    pondName,
    pondSize,
    isHarvested,
    firstMonitoring,
    isInPondView,
    isInPondListView,
    onClose,
    nurseries
  } = props;

  const { trans } = getTrans();

  /// Hooks
  const [step, setStep] = useState(1);
  const { trackAction } = useAnalytics();
  const { reloadCurrentPond, isLoading: isLoadingPond } = useReloadCurrentPond();
  const { reloadCurrentFarmPonds, isLoading: isLoadingPonds } = useReloadCurrentFarmPonds();

  const [{ isLoading: isCreating }, stockPond] = useStockPondApi();

  /// Redux
  const lang = useAppSelector((state) => state.app.lang);
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);
  const currentPopulation = useAppSelector((state) => state.farm.currentPopulation);
  const user = useAppSelector((state) => state.auth.user);

  /// FORM DATA
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);

  const { stockingConfig, growthTarget, feedTable, feedTypes, timezone } = currentFarm ?? {};
  const { cycleInformation, harvest } = currentPopulation ?? {};

  const { carryingCapacity, equipment, projection } = cycleInformation ?? {};
  const {
    aeratorType,
    numberOfAutoFeeders,
    autoFeederBrand,
    autoFeederProtocol = 'timer',
    numberOfAerators,
    feedBrand
  } = equipment ?? {};

  const { projectedFeed } = projection ?? {};
  const { farmFeedTypes } = projectedFeed ?? {};
  const defaultFarmFeedTableId = feedTable?.find((item) => item.isDefault)?._id;
  const defaultFarmFeedTypeId = feedTypes?.find((item) => item.isDefault)?._id;

  const { weight: harvestWeight, productionDays: cycleLength, fcr, biomassLbsHa } = growthTarget ?? {};
  const { summary } = stockingConfig ?? {};
  const { dryDays, growthDensity, avgStockingWeight } = summary ?? {};

  const totalSQ = pondSize * sqPerHectare;

  const isTransfer = nurseries?.length;
  const dv: Partial<ReturnType<typeof getStockingDefaultValuesFromNurseries>> = isTransfer
    ? getStockingDefaultValuesFromNurseries({ nurseries, pondId })
    : {
        seedingAverageWeight: avgStockingWeight,
        seedingQuantity: growthDensity ? growthDensity * totalSQ : null
      };

  const defaultValues: StockFormValues = {
    cycle: null,
    stockingCostsMillar: dv.costMillar,
    stockedAt: dv.stockedAt ? DateTime.fromISO(dv.stockedAt).toJSDate() : DateTime.local().toJSDate(),
    seedingAverageWeight: dv.seedingAverageWeight,
    seedingQuantity: dv.seedingQuantity,
    dryDaysBeforeStocking: dryDays,
    hatcheryName: dv.labSource,
    cycleInformation: {
      carryingCapacity: convertUnitByDivision(carryingCapacity, unitsConfig?.biomass),
      target: {
        harvestWeight,
        cycleLength,
        biomassToHarvest: biomassLbsHa,
        type: 'biomassToHarvest',
        survival: null,
        fcr
      },
      equipment: {
        aeratorType,
        numberOfAutoFeeders,
        autoFeederBrand,
        autoFeederProtocol: autoFeederProtocol ?? 'timer',
        numberOfAerators,
        feedBrand
      },
      projection: {
        projectedFeed: {
          farmFeedTypeId: farmFeedTypes?.[0]?.id ?? defaultFarmFeedTypeId,
          farmFeedTableId: defaultFarmFeedTableId
        }
      }
    }
  };

  const defaultMinValue = 0;
  const defaultMaxValue = 200;
  const defaultMaxCostMillar = 50_000;

  const { schema } = useYupSchema({
    cycle: Yup.string().required(trans('t_required')).label('t_cycle_number'),
    stockedAt: Yup.date()
      .transform(numberTransform)
      .min(dateToBeTwoYearsLess, trans('t_min_allowed_stocked'))
      .required(trans('t_required'))
      .label('t_stocked_at'),
    seedingAverageWeight: Yup.number()
      .transform(numberTransform)
      .positive()
      .nullable()
      .test((seedingAverageWeight, { createError, path }) => {
        if (isHarvested) {
          return true;
        }
        const firstMonitoringAverageWeight = firstMonitoring?.averageWeight;

        if (!firstMonitoringAverageWeight) {
          return true;
        }

        if (seedingAverageWeight > firstMonitoringAverageWeight) {
          return createError({
            path,
            message: trans('t_average_weight_should_be_less_than_first_monitoring_average_weight', {
              firstMonitoringAverageWeight: formatNumber(firstMonitoringAverageWeight, { lang, fractionDigits: 2 })
            })
          });
        }

        return true;
      })
      .required(trans('t_required'))
      .label('t_stocking_average_weight'),
    seedingQuantity: Yup.number()
      .transform(numberTransform)
      .positive()
      .nullable()
      .required(trans('t_required'))
      .label('t_animals_stocked'),
    dryDaysBeforeStocking: Yup.number()
      .transform(numberTransform)
      .min(defaultMinValue, trans('t_min_x', { min: defaultMinValue }))
      .nullable()
      .required(trans('t_required'))
      .label('t_dry_days'),
    stockingCostsMillar: Yup.number()
      .transform(numberTransform)
      .min(defaultMinValue, trans('t_min_x', { min: defaultMinValue }))
      .max(defaultMaxCostMillar, trans('t_max_x', { max: defaultMaxCostMillar }))
      .nullable()
      .label('t_cost_millar'),
    hatcheryName: Yup.string().nullable().label('t_hatchery'),
    geneticName: Yup.string().nullable().label('t_genetic'),
    cycleInformation: Yup.object().shape({
      carryingCapacity: Yup.number()
        .transform(numberTransform)
        .min(defaultMinValue, trans('t_min_x', { min: defaultMinValue }))
        .nullable()
        .label(isBiomassUnitLbs ? 't_carrying_capacity_lb_ha' : 't_carrying_capacity_kg_ha'),
      target: Yup.object().shape({
        harvestWeight: Yup.number()
          .transform(numberTransform)
          .min(defaultMinValue, trans('t_min_x', { min: defaultMinValue }))
          .max(defaultMaxValue, trans('t_max_x', { max: defaultMaxValue }))
          .nullable()
          .required(trans('t_required'))
          .label('t_harvest_weight_g'),
        cycleLength: Yup.number()
          .transform(numberTransform)
          .min(defaultMinValue, trans('t_min_x', { min: defaultMinValue }))
          .max(365, trans('t_max_x', { max: 365 }))
          .nullable()
          .required(trans('t_required'))
          .label('t_cycle_length'),
        type: Yup.string().required(trans('t_required')).label('t_cycle_type'),
        biomassToHarvest: Yup.number()
          .transform(numberTransform)
          .positive()
          .nullable()
          .required(trans('t_required'))
          .label(isBiomassUnitLbs ? 't_biomass_lb_ha' : 't_biomass_kg_ha'),
        survival: Yup.number()
          .transform(numberTransform)
          .nullable()
          .when('type', {
            is: 'biomassToHarvest',
            then: (schema) => schema,
            otherwise: (schema) =>
              schema
                .min(defaultMinValue, trans('t_min_x', { min: defaultMinValue }))
                .max(defaultMaxValue, trans('t_max_x', { max: defaultMaxValue }))
                .required(trans('t_required'))
          })
          .label('t_survival'),
        fcr: Yup.number()
          .transform(numberTransform)
          .min(defaultMinValue, trans('t_min_x', { min: defaultMinValue }))
          .max(5, trans('t_max_x', { max: 5 }))
          .nullable()
          .required(trans('t_required'))
          .label('t_fcr')
      }),
      equipment: Yup.object().shape({
        numberOfAutoFeeders: Yup.number()
          .transform(numberTransform)
          .min(defaultMinValue, trans('t_min_x', { max: defaultMinValue }))
          .max(defaultMaxValue, trans('t_max_x', { max: defaultMaxValue }))
          .nullable()
          .label('t_#_of_autofeeders'),
        aeratorType: Yup.string().nullable().label('t_type_of_aerator'),
        autoFeederBrand: Yup.string().nullable().label('t_autofeeder_brand'),
        autoFeederProtocol: Yup.string().nullable().label('t_autofeeder_protocol'),
        numberOfAerators: Yup.number()
          .transform(numberTransform)
          .min(defaultMinValue, trans('t_min_x', { max: defaultMinValue }))
          .max(defaultMaxValue, trans('t_max_x', { max: defaultMaxValue }))
          .nullable()
          .label('t_#_of_aerators'),
        feedBrand: Yup.string().nullable().label('t_feed_brand')
      }),
      projection: Yup.object().shape({
        projectedFeed: Yup.object().shape({
          farmFeedTypeId: Yup.string().nullable().label('t_autofeeder_brand'),
          farmFeedTableId: Yup.string().nullable().required(trans('t_required')).label('t_autofeeder_protocol')
        })
      })
    })
  });

  const {
    trigger,
    handleSubmit,
    control,
    formState: { errors },
    watch,
    setValue,
    reset,
    clearErrors
  } = useForm<StockFormValues>({
    resolver: yupResolver(schema) as YupResolverType<StockFormValues>,
    defaultValues
  });

  /// Handlers
  const handleModalClose = () => {
    setStep(1);
    onClose();
  };
  const handleReload = () => {
    if (isInPondView) return reloadCurrentPond(handleModalClose);
    if (isInPondListView) return reloadCurrentFarmPonds({ successCallback: handleModalClose });
  };

  const handleModalCancel = () => {
    reset(defaultValues);
    handleModalClose();
  };

  const handleGoBack = () => {
    clearErrors();
    setStep((prev) => prev - 1);
  };

  const handleGoNext = async () => {
    if (step === 1) {
      const isValid = await trigger([
        'cycle',
        'stockedAt',
        'seedingAverageWeight',
        'seedingQuantity',
        'dryDaysBeforeStocking',
        'stockingCostsMillar'
      ]);
      if (!isValid) return;
    }

    if (step === 2) {
      const isValid = await trigger([
        'cycleInformation.target.harvestWeight',
        'cycleInformation.target.cycleLength',
        'cycleInformation.target.biomassToHarvest',
        'cycleInformation.target.survival',
        'cycleInformation.target.fcr'
      ]);
      if (!isValid) return;
    }

    setStep((prev) => prev + 1);
  };

  const onSubmit = (data: StockFormValues) => {
    const { cycle, cycleInformation } = data;
    const { target, projection, carryingCapacity } = cycleInformation;
    const convertedTargetSurvival = target.survival >= 0 ? target.survival / 100 : undefined;

    const transferData: {
      stockingType: EnumPopulationStockingType;
      nurserySources?: { nurseryId: string; nurseryPopulationId: string }[];
    } = isTransfer
      ? {
          stockingType: 'transfer',
          nurserySources: nurseries.map((ele) => ({ nurseryId: ele._id, nurseryPopulationId: ele.population._id }))
        }
      : {
          stockingType: 'direct'
        };

    const { projectedFeed } = projection ?? {};
    const { farmFeedTypeId, ...farmFeedTableIdLessData } = projectedFeed ?? {};

    trackAction(actionsName.pondStocked, { pondName, pondId }).then();
    stockPond({
      params: {
        pondId,
        ...data,
        cycle: cycle.toString(),
        stockedAtTimezone: timezone,
        ...transferData,
        cycleInformation: {
          ...cycleInformation,
          carryingCapacity: convertUnitByMultiplication(carryingCapacity, unitsConfig?.biomass),
          target: {
            ...target,
            survival: convertedTargetSurvival
          },
          projection: {
            ...(projection || {}),
            projectedFeed: {
              ...(farmFeedTableIdLessData || {}),
              ...(farmFeedTypeId && {
                farmFeedTypes: [{ id: farmFeedTypeId, percentage: 1 }]
              })
            }
          }
        }
      },
      successCallback: () => {
        handleReload();
      }
    });
  };

  const titleMap: Record<number, string> = {
    1: trans('t_confirm_stocking_details'),
    2: trans('t_confirm_targets'),
    3: trans('t_confirm_cycle_protocol')
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} noValidate>
      <Flex align='center' gap='sm-alt' mb='2lg'>
        <Heading size='heavy300'>{titleMap[step]}</Heading>
        <Text>
          {pondName} ({pondSize} {trans('t_ha')})
        </Text>
      </Flex>

      {step === 1 && (
        <StockingFormFields
          errors={errors}
          control={control}
          watch={watch}
          pondSize={pondSize}
          nurseries={nurseries}
          pondId={pondId}
          harvestDate={harvest?.date}
          setValue={setValue}
        />
      )}
      {step === 2 && <TargetFormFields control={control} setValue={setValue} watch={watch} pondSize={pondSize} />}
      {step === 3 && <CycleProtocolFormFields control={control} errors={errors} />}

      <Flex align='center' justify='flex-end' gap='md' mt='2lg'>
        <BaseButton
          variant='secondary'
          size='sm'
          onClick={handleModalCancel}
          analyticsId={actionsName.addStockingCancelClicked}
          analyticsData={{ pondId, pondName }}
          data-cy='cancel-btn'
        >
          {trans('t_cancel')}
        </BaseButton>

        {step > 1 && (
          <BaseButton
            variant='secondary'
            size='sm'
            onClick={handleGoBack}
            analyticsId={actionsName.addStockingCancelClicked}
            analyticsData={{ pondId, pondName }}
            data-cy='back-btn'
          >
            {trans('t_back')}
          </BaseButton>
        )}

        {step < 3 && (
          <BaseButton
            size='sm'
            onClick={handleGoNext}
            analyticsId={actionsName.addStockingCancelClicked}
            analyticsData={{ pondId, pondName }}
            data-cy='next-btn'
          >
            {trans('t_next')}
          </BaseButton>
        )}

        {step === 3 && (
          <BaseButton
            type='submit'
            size='sm'
            loading={isCreating || isLoadingPond || isLoadingPonds}
            analyticsId={actionsName.addStockingSaveClicked}
            analyticsData={{ pondId, pondName }}
            data-cy='save-btn'
          >
            {trans('t_save_changes')}
          </BaseButton>
        )}
      </Flex>
    </form>
  );
}

interface StockingFormFields {
  control: Control<StockFormValues>;
  errors: FieldErrors<StockFormValues>;
  watch: UseFormWatch<StockFormValues>;
  setValue: UseFormSetValue<StockFormValues>;
  pondSize: number;
  pondId: string;
  harvestDate: string;
  nurseries?: CurrentPondNurseriesType;
}

function StockingFormFields(props: StockingFormFields) {
  const { control, errors, watch, pondSize, nurseries, pondId, harvestDate, setValue } = props;
  const { trans } = getTrans();
  const lang = useAppSelector((state) => state.app.lang);
  const watchedSeedingQuantity = watch('seedingQuantity');
  const watchedStockingDate = watch('stockedAt');
  const stockingDensity = watchedSeedingQuantity
    ? formatNumber(watchedSeedingQuantity / pondSize, { fractionDigits: 0, lang })
    : null;

  const harvestDateLuxon = DateTime.fromISO(harvestDate).startOf('day');

  useEffect(() => {
    if (!watchedStockingDate || !harvestDate) return;
    const stockingDateLuxon = DateTime.fromJSDate(watchedStockingDate).startOf('day');
    const diff = stockingDateLuxon.diff(harvestDateLuxon, 'days').days;
    if (diff < 0) return;
    setValue('dryDaysBeforeStocking', diff);
  }, [watchedStockingDate, harvestDate]);

  return (
    <Flex direction='column' gap='xs-alt'>
      <BaseFormNumberInput
        name='cycle'
        required
        control={control}
        label={trans('t_cycle_number')}
        inputProps={{ autoFocus: true, ...inputStyles }}
        numericFormatProps={{ decimalScale: 0 }}
        formLabelProps={labelStyles}
        {...formContainerStyles}
        inputContainerProps={{ w: 'auto' }}
      />
      <Box {...formContainerStyles}>
        <Text asChild>
          <label htmlFor='stockedAt'>{trans('t_stocking_date')}</label>
        </Text>
        <FormControlDateInput
          id='stockedAt'
          name='stockedAt'
          error={errors?.stockedAt?.message}
          control={control}
          inputProps={inputStyles}
          labelProps={labelStyles}
          maxW='180px'
          minDate={harvestDateLuxon.toJSDate()}
          maxDate={new Date()}
          dateFormat='MMM dd, yyyy'
          placement='left-end'
        />
      </Box>

      <Box {...formContainerStyles}>
        <Text>{trans('t_type_of_stocking')}</Text>
        <Text>{nurseries?.length ? trans('t_transfer') : trans('t_direct')}</Text>
      </Box>

      {nurseries?.length && <NurseriesTransferInformation nurseries={nurseries} pondId={pondId} />}

      <BaseFormNumberInput
        required
        control={control}
        name='seedingAverageWeight'
        label={trans('t_stocking_weight_g')}
        {...formContainerStyles}
        inputProps={inputStyles}
        formLabelProps={labelStyles}
        numericFormatProps={{ decimalScale: 2, fixedDecimalScale: true }}
        inputContainerProps={{ w: 'auto' }}
      />

      <BaseFormNumberInput
        control={control}
        name='seedingQuantity'
        required
        {...formContainerStyles}
        inputProps={inputStyles}
        formLabelProps={labelStyles}
        label={trans('t_animals_stocked')}
        numericFormatProps={{ decimalScale: 0 }}
        inputContainerProps={{ w: 'auto' }}
      />

      <Box {...formContainerStyles}>
        <Text fontSize='md' fontWeight={500}>
          {trans('t_stocking_density_ha')}
        </Text>
        <Text w='180px' textAlign='center' fontSize='md'>
          {stockingDensity ?? '-'}
        </Text>
      </Box>

      <BaseFormNumberInput
        control={control}
        name='dryDaysBeforeStocking'
        required
        {...formContainerStyles}
        inputProps={inputStyles}
        formLabelProps={labelStyles}
        label={trans('t_dry_days')}
        numericFormatProps={{ decimalScale: 0 }}
        inputContainerProps={{ w: 'auto' }}
      />

      <AdminOrSupervisorWrapper>
        <BaseFormNumberInput
          control={control}
          name='stockingCostsMillar'
          required
          {...formContainerStyles}
          inputProps={inputStyles}
          formLabelProps={labelStyles}
          label={trans('t_cost_millar')}
          numericFormatProps={{ decimalScale: 2, prefix: '$', fixedDecimalScale: true }}
          inputContainerProps={{ w: 'auto' }}
        />
      </AdminOrSupervisorWrapper>

      <HatcherySelectContainer control={control} error={errors?.hatcheryName?.message} />

      <GeneticSelectContainer control={control} error={errors?.geneticName?.message} />
    </Flex>
  );
}

function HatcherySelectContainer(props: SelectContainerProps) {
  const { control, error } = props;
  const { trans } = getTrans();

  const currentFarm = useAppSelector((state) => state.farm?.currentFarm);

  const { otherConfig } = currentFarm ?? {};

  const { hatchery } = otherConfig ?? {};
  const hatcheryOptions = hatchery?.map((item: { name: string }) => ({
    label: item.name,
    value: item.name
  }));

  return (
    <Box {...formContainerStyles}>
      <Text asChild>
        <label htmlFor='hatcheryName'>{trans('t_lab_source')}</label>
      </Text>

      <FormControlReactSelect
        isOptional
        id='hatcheryName'
        name='hatcheryName'
        control={control}
        placeholder=' '
        options={hatcheryOptions}
        error={error}
        isSearchable={false}
        labelProps={labelStyles}
        formControlProps={{ w: selectControlStyles.minWidth }}
        selectControlStyles={selectControlStyles}
      />
    </Box>
  );
}

function GeneticSelectContainer(props: SelectContainerProps) {
  const { control, error } = props;
  const { trans } = getTrans();

  const currentFarm = useAppSelector((state) => state.farm?.currentFarm);

  const { otherConfig } = currentFarm ?? {};

  const { genetic } = otherConfig ?? {};
  const geneticOptions = genetic?.map((item: { name: string }) => ({
    label: item.name,
    value: item.name
  }));

  return (
    <Box {...formContainerStyles}>
      <Text asChild>
        <label htmlFor='geneticName'>{trans('t_nauplii_source')}</label>
      </Text>

      <FormControlReactSelect
        isOptional
        id='geneticName'
        name='geneticName'
        control={control}
        placeholder=' '
        options={geneticOptions}
        error={error}
        isSearchable={false}
        labelProps={labelStyles}
        formControlProps={{ w: selectControlStyles.minWidth }}
        selectControlStyles={selectControlStyles}
      />
    </Box>
  );
}
