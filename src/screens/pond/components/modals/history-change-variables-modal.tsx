import { Box, chakra, Flex, Heading, Text, useDisclosure } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { BaseFormCheckbox } from '@components/form/base-form-checkbox';
import { getTrans } from '@i18n/get-trans';
import { ReactNode, useEffect } from 'react';
import { useUpdateUserPreferencesApi } from '@screens/my-account/hooks/use-update-user-preferences';
import { useAppSelector } from '@redux/hooks';
import { Controller, useForm } from 'react-hook-form';
import {
  HistoryVariable,
  useGetHistoryViewVariables
} from '@screens/pond/components/modals/hooks/use-get-history-view-variables';
import { useAnalytics } from '@hooks/use-analytics';
import { actionsName } from '@utils/segment';
import { DialogBody, DialogContent, DialogFooter, DialogHeader, DialogRoot, DialogTitle } from '@components/ui/dialog';
import { VisionOnlyGuard } from '@components/permission-wrappers/vision-only-guard';

type HistoryChangeVariablesModalProps = {
  children: ReactNode;
  selectedVariables: HistoryVariable[];
  isProductionDataTable?: boolean;
};

export function HistoryChangeVariablesModal(props: HistoryChangeVariablesModalProps) {
  const { children, selectedVariables, isProductionDataTable } = props;
  const { onOpen, onClose, open } = useDisclosure();
  return (
    <DialogRoot
      open={open}
      onOpenChange={(e) => {
        e.open ? onOpen() : onClose();
      }}
      scrollBehavior='inside'
    >
      <Box onClick={onOpen}>{children}</Box>

      <Form onCancel={onClose} selectedVariables={selectedVariables} isProductionDataTable={isProductionDataTable} />
    </DialogRoot>
  );
}

type FormType = Record<HistoryVariable, boolean>;

type FormProps = {
  selectedVariables: HistoryVariable[];
  isProductionDataTable?: boolean;
  onCancel: () => void;
};

function Form({ selectedVariables, onCancel, isProductionDataTable }: FormProps) {
  const { trans } = getTrans();
  const { trackAction } = useAnalytics();
  const historyViewVariables = useGetHistoryViewVariables();
  const maxSelected = 9;
  const { isUpdating, updatePondDetailsPreferences } = usePreferences(() => {
    onCancel();
    trackAction(actionsName.keyVariablesSubmitted);
  }, isProductionDataTable);

  const { reset, control, handleSubmit, watch } = useForm<FormType>();

  const handleResetForm = () => {
    const defaultValues = selectedVariables.reduce((acc: FormType, key: HistoryVariable) => {
      acc[key] = true;
      return acc;
    }, {} as FormType);
    reset(defaultValues);
  };

  const handleCancelForm = () => {
    handleResetForm();
    onCancel();
  };

  useEffect(() => {
    handleResetForm();
  }, [JSON.stringify(selectedVariables ?? [])]);

  const variables = watch();
  const selectedWatchVariables = Object.values(variables).filter((value) => value);
  const isSubmitDisabled = selectedWatchVariables.length < 1;

  const isVariableDisabled = (variable: HistoryVariable) => {
    return !isProductionDataTable && selectedWatchVariables.length >= maxSelected && !variables[variable];
  };

  const onSubmit = handleSubmit((data: FormType) => {
    if (isSubmitDisabled) return;
    const selectedKeys = Object.keys(data).filter((key) => data[key as HistoryVariable]);
    updatePondDetailsPreferences(selectedKeys as HistoryVariable[]);
  });

  const historyViewVariablesEntries = Object.entries(historyViewVariables);

  return (
    <DialogContent data-cy='select-variables-modal' borderRadius='2xl' boxShadow='elevation.400'>
      <chakra.form
        display='contents'
        onSubmit={(event) => {
          event.stopPropagation();
          onSubmit(event).then();
        }}
        noValidate
      >
        <DialogHeader px='md' pt='md' pb='2lg'>
          <DialogTitle
            display='flex'
            gap={{ base: 'xs-alt', md: 'xl' }}
            flexDirection={{ base: 'column', md: 'row' }}
            minW={{ base: 'auto', md: '370px', lg: '423px' }}
            justifyContent={{ base: 'initial', md: 'space-between' }}
            alignItems={!isProductionDataTable ? 'flex-start' : 'center'}
          >
            <Box>
              <Heading size='heavy300'>{trans('t_customize_view')}</Heading>
              <VisionOnlyGuard>
                {!isProductionDataTable && (
                  <Text size='light300' mt='xs' color='text.gray.weak'>
                    {trans('t_select_up_to_x_variables_you_want', { count: maxSelected })}
                  </Text>
                )}
              </VisionOnlyGuard>
            </Box>
            <VisionOnlyGuard>
              <Text size='heavy300' alignSelf='flex-end'>
                {isProductionDataTable
                  ? trans('t_selected_x', { count: selectedWatchVariables.length })
                  : trans('t_selected', { selected: selectedWatchVariables.length, total: maxSelected })}
              </Text>
            </VisionOnlyGuard>
          </DialogTitle>
        </DialogHeader>

        <DialogBody display='flex' flexDirection='column' px='md' pt={0} pb={0}>
          {historyViewVariablesEntries.map(([key, viewVariable], index) => {
            const { title, variables } = viewVariable;

            const isFirstElement = index === 0;
            const isLast = index === historyViewVariablesEntries.length - 1;

            return (
              <Flex
                key={key}
                flexDir='column'
                gap='md'
                py='2lg'
                borderBottom={!isLast ? '1px solid' : 'none'}
                borderColor='border.gray'
                {...(isFirstElement && { pt: '2sm' })}
              >
                <Text size='label200'>{title}</Text>

                {variables.map((variable) => (
                  <Controller
                    name={variable.value}
                    key={variable.value}
                    control={control}
                    render={({ field: { value, onChange, ...rest } }) => {
                      return (
                        <BaseFormCheckbox
                          {...rest}
                          id={variable.value}
                          label={variable.label}
                          formControlProps={{
                            w: '100%'
                          }}
                          checked={value}
                          onCheckedChange={({ checked }) => onChange(checked)}
                          disabled={isVariableDisabled(variable.value)}
                        />
                      );
                    }}
                  />
                ))}
              </Flex>
            );
          })}
        </DialogBody>
        <DialogFooter gap='sm-alt' py='sm-alt' px='md' shadow='elevation.200' roundedBottom='2xl'>
          <BaseButton
            analyticsId={actionsName.keyVariablesCancelClicked}
            variant='secondary'
            w='max-content'
            onClick={handleCancelForm}
            disabled={isUpdating}
          >
            {trans('t_cancel')}
          </BaseButton>
          <BaseButton
            analyticsId={actionsName.keyVariablesSaveClicked}
            type='submit'
            w='max-content'
            loading={isUpdating}
            disabled={isSubmitDisabled}
          >
            {trans('t_apply')}
          </BaseButton>
        </DialogFooter>
      </chakra.form>
    </DialogContent>
  );
}

const usePreferences = (onSuccess: () => void, isProductionDataTable: boolean) => {
  const user = useAppSelector((state) => state.auth?.user);
  const { preferences } = user ?? {};
  const { viewFields, __typename, ...restPreferences } = preferences ?? {};
  const [{ isLoading }, updateUser] = useUpdateUserPreferencesApi();

  const updatePondDetailsPreferences = (historyVariables: HistoryVariable[]) => {
    const fieldsToEdit = isProductionDataTable
      ? { pondProductionData: historyVariables }
      : { pondLastTwoHistory: historyVariables };
    updateUser({
      params: {
        preferences: {
          ...restPreferences,
          viewFields: { ...viewFields, ...fieldsToEdit }
        }
      },
      successCallback: onSuccess
    });
  };

  return {
    isUpdating: isLoading,
    updatePondDetailsPreferences
  };
};
