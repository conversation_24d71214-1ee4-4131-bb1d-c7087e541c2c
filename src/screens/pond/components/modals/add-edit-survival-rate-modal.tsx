import { Box, BoxProps, Flex, Text, useDisclosure } from '@chakra-ui/react';
import { useAnalytics } from '@hooks/use-analytics';
import { getTrans } from '@i18n/get-trans';
import { actionsName } from '@utils/segment';
import { PopulationPartialHarvest, v1PopulationSetApi } from '@xpertsea/module-farm-sdk';
import { ReactNode, useEffect } from 'react';
import { useAppSelector } from '@redux/hooks';
import { numberTransform, useYupSchema, Yup, yupFormResolver } from '@utils/yup';
import { useForm } from 'react-hook-form';
import { getUniDate } from '@utils/date';
import { BaseFormNumberInput } from '@components/form/base-form-number-input';
import { BaseButton } from '@components/base/base-button';
import { parsePercent } from '@screens/monitoring/helpers/weight-distribution';
import { DateTime } from 'luxon';
import { InfoIconToolTip } from '@components/tooltip/info-icon-tool-tip';
import { useUpdatePopulationApi } from '@screens/population/hooks/use-update-population-api';
import { useReloadCurrentPond } from '@screens/pond/hooks/use-reload-current-pond';
import { useReloadCurrentFarmPonds } from '@screens/farm/hooks/use-reload-current-farm-ponds';
import { DialogBody, DialogContent, DialogHeader, DialogRoot, DialogTitle } from '@components/ui/dialog';

interface AddEditSurvivalRateModalProps {
  isEdit?: boolean;
  populationId: string;
  pondName: string;
  pondId: string;
  seedingQuantity: number;
  partialHarvest: PopulationPartialHarvest[];
  children?: ReactNode;
  isOpen?: boolean;
  onClose?: () => void;
  lastMonitoringDate: string;
  survivalRate?: number;
  isInPondView?: boolean;
  isInPondListView?: boolean;
  isInHarvestPlannerView?: boolean;
}

export function AddEditSurvivalRateModal(props: AddEditSurvivalRateModalProps) {
  const { children, pondName, pondId, isEdit = false, ...rest } = props;

  const { trans } = getTrans();
  const { onOpen, onClose, open } = useDisclosure();
  const { trackAction } = useAnalytics();

  useEffect(() => {
    if (!props?.isOpen) return;
    onOpen();
  }, [props?.isOpen]);

  const handleModalClose = () => {
    trackAction(actionsName.survivalRateCloseClicked, {
      pondId,
      pondName,
      isEdit,
      addCurrentPopulation: true
    }).then();
    onClose();
    props?.onClose?.();
  };

  const handleModalOpen = () => {
    trackAction(actionsName.survivalRateClicked, {
      pondId,
      pondName,
      isEdit,
      addCurrentPopulation: true
    }).then();
    onOpen();
  };

  return (
    <DialogRoot
      open={open}
      onOpenChange={(e) => {
        e.open ? handleModalOpen() : handleModalClose();
      }}
      restoreFocus={false}
    >
      <Box
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
          handleModalOpen();
        }}
      >
        {children}
      </Box>

      <DialogContent portalled={false} data-cy='production-target-modal' rounded='2xl'>
        <DialogHeader px='md' pt='md' pb={0}>
          <DialogTitle mb='md'>{isEdit ? trans('t_edit_survival') : trans('t_enter_survival')}</DialogTitle>
          <Text>{pondName}</Text>
        </DialogHeader>
        <DialogBody px='md' py='md' fontSize='md'>
          <SetSurvivalRateForm
            {...rest}
            isEdit={isEdit}
            onSuccess={handleModalClose}
            handleOnCancel={handleModalClose}
            pondName={pondName}
            pondId={pondId}
          />
        </DialogBody>
      </DialogContent>
    </DialogRoot>
  );
}

type FormType = v1PopulationSetApi['survivalRate'];

interface SetSurvivalRateProps
  extends BoxProps,
    Omit<AddEditSurvivalRateModalProps, 'children' | 'isOpen' | 'onClose'> {
  canCancel?: boolean;
  onSuccess: () => void;
  handleOnCancel: () => void;
}

export function SetSurvivalRateForm(props: SetSurvivalRateProps) {
  const {
    survivalRate,
    handleOnCancel,
    isEdit = false,
    canCancel = true,
    pondName,
    pondId,
    isInPondView,
    populationId,
    partialHarvest,
    seedingQuantity,
    isInPondListView,
    lastMonitoringDate,
    isInHarvestPlannerView,
    onSuccess,
    ...rest
  } = props;

  const { reloadCurrentPond } = useReloadCurrentPond();
  const { reloadCurrentFarmPonds } = useReloadCurrentFarmPonds();

  const [{ isLoading }, updatePopulation] = useUpdatePopulationApi();
  const appLanguage = useAppSelector((state) => state.app.lang);
  const { trans } = getTrans();
  const { trackAction } = useAnalytics();

  const min = 0;
  const max = 100;

  const { schema } = useYupSchema({
    survivalRate: Yup.number().transform(numberTransform).required().min(min).max(max).positive().label('t_survival')
  });

  const {
    handleSubmit,
    control,
    formState: { errors }
  } = useForm<FormType>({
    mode: 'onChange',
    resolver: yupFormResolver(schema),
    defaultValues: { survivalRate }
  });

  const onReload = () => {
    if (isInPondView) return reloadCurrentPond();
    if (isInPondListView) return reloadCurrentFarmPonds();
    if (isInHarvestPlannerView) {
      return reloadCurrentFarmPonds({
        extraFields: {
          population: {
            prediction: { date: 1, averageWeight: 1 },
            harvestPlan: { harvestDate: 1, harvestType: 1 }
          }
        }
      });
    }
  };

  const handleOnSubmit = handleSubmit((formData: FormType) => {
    updatePopulation({
      params: {
        filter: { populationId },
        set: {
          survivalRate: {
            [lastMonitoringDate]: formData.survivalRate / 100
          }
        }
      },
      successCallback: () => {
        onSuccess();
        onReload();
      }
    });
  });

  const { f: formattedTitleDate } = getUniDate({
    date: lastMonitoringDate,
    format: 'EEE, LLL dd',
    locale: appLanguage
  });

  return (
    <Box {...rest}>
      {!!partialHarvest?.length && (
        <>
          <Text mb='sm-alt'>{trans('t_partial_harvest')}:</Text>
          <Flex direction='column' gap='xs-alt' mb='sm-alt'>
            {partialHarvest
              ?.slice()
              .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
              ?.map((harvest, index) => {
                const percent = parsePercent(harvest.quantity / seedingQuantity);
                const dateFormatted = DateTime.fromISO(harvest.date).toFormat('LLL dd', { locale: appLanguage });
                return (
                  <Text key={`${dateFormatted}-${index}`}>
                    {trans('t_x_harvested_on', { date: dateFormatted, percent })}
                  </Text>
                );
              })}
          </Flex>
        </>
      )}
      <Flex mb='md' gap='xs' justify='space-between' align='center' className='survival-info-container'>
        <Text>{trans('t_ensure_survival_included')}</Text>
        <InfoIconToolTip>{trans('t_ensure_survival_included_tooltip')}</InfoIconToolTip>
      </Flex>
      <form
        onSubmit={(event) => {
          event.preventDefault();
          event.stopPropagation();
          trackAction(actionsName.survivalRateClicked, {
            pondId,
            pondName,
            isEdit,
            addCurrentPopulation: true
          }).then();

          handleOnSubmit(event).then();
        }}
        noValidate
        data-cy='survival-rate-form'
      >
        <BaseFormNumberInput
          name='survivalRate'
          control={control}
          display='flex'
          flexDirection='row'
          alignItems='center'
          justifyContent='space-between'
          px='sm-alt'
          py='2xs'
          borderRadius='base'
          bgColor='gray.100'
          inputContainerProps={{ w: 'fit-content' }}
          formLabelProps={{ m: 0, fontWeight: 600, whiteSpace: 'nowrap' }}
          label={trans('t_survival_form_title', { date: formattedTitleDate })}
          required
          error={errors?.survivalRate?.message?.toString()}
          inputProps={{ autoFocus: true, textAlign: 'center', w: '80px', size: 'sm', rounded: 'base' }}
          numericFormatProps={{
            min,
            max,
            fixedDecimalScale: true
          }}
          inputRightElement={
            <Text as='span' fontSize='md' color='gray.400'>
              %
            </Text>
          }
        />

        <Flex mt='lg' gap='sm-alt' justify='flex-end' className='form-action-container'>
          {canCancel && (
            <BaseButton
              variant='ghost'
              onClick={handleOnCancel}
              data-cy='cancel-survival-btn'
              size='sm'
              className='cancel-btn'
              analyticsId={isEdit ? actionsName.harvestTargetCancelClicked : actionsName.harvestTargetSetLaterClicked}
              analyticsData={{ pondName, pondId }}
            >
              {trans('t_cancel')}
            </BaseButton>
          )}

          <BaseButton
            type='submit'
            color='white'
            loading={isLoading}
            onClick={handleOnSubmit}
            data-cy='set-target-btn'
            size='sm'
            className='submit-btn'
            analyticsId={isEdit ? actionsName.survivalRateEdited : actionsName.survivalRateEntered}
            analyticsData={{ pondName, pondId }}
          >
            {isEdit ? trans('t_update') : trans('t_save')}
          </BaseButton>
        </Flex>
      </form>
    </Box>
  );
}
