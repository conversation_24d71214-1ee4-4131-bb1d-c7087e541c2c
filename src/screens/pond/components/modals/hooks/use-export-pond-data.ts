import { IndicatorOption, PondCardViewType } from '@components/pond-card/helper';
import { useCalculatePondCardData } from '@components/pond-card/hooks/use-get-pond-card-data';
import { getTrans } from '@i18n/get-trans';
import { CurrentFarmPonds } from '@redux/farm/set-current-farm-ponds';
import { useAppSelector } from '@redux/hooks';
import { getDaysDiffBetweenDates } from '@screens/farm/helpers/farm-dates';
import { getPondState } from '@screens/pond/helpers/get-pond-state';
import { getLastDateRecordValue } from '@screens/population/helpers/population-values';
import { getGrowthSeries } from '@screens/population/hooks/get-growth-series';
import { FarmSummaryViewVariable, getPondViewVariableLabels } from '@screens/farm-summary/helpers/pond-list';
import { formatDate } from '@utils/date';
import { exportAsXLSX, exportAsXLSXTabs } from '@utils/files';
import { convertUnitByDivision, convertUnitByMultiplication, formatNumber } from '@utils/number';
import { sortArray, sortCompareString } from '@utils/sort-array';
import { UserPreferences } from '@xpertsea/module-user-sdk';
import { get, isNumber, isString, isUndefined, snakeCase } from 'lodash';
import { DateTime } from 'luxon';
import { useMemo } from 'react';
import { usePermission } from '@hooks/use-permission';

type PropsType = {
  ponds: CurrentFarmPonds;
  viewVariables: FarmSummaryViewVariable[];
  selectedIndicatingStatus: IndicatorOption;
};

type GetPondOverviewDataProps = {
  ponds: CurrentFarmPonds;
  farmTimezone: string;
  unitsConfig: UserPreferences['unitsConfig'];
  lang: string;
  isAdmin: boolean;
  isSupervisor: boolean;
  isVisionOnly: boolean;
};
function getPondOverviewData(props: GetPondOverviewDataProps) {
  const { ponds, farmTimezone, unitsConfig, lang, isAdmin, isVisionOnly, isSupervisor } = props;
  const { trans } = getTrans();

  const data = ponds?.reduce(
    (acc, pond) => {
      const { currentPopulation, size: pondSize } = pond;

      const { isHarvested, isEmpty } = getPondState({ population: currentPopulation });
      if (isHarvested || isEmpty) return acc;

      const {
        stockedAt,
        survivalRate,
        lastWeekGrowth,
        stockedAtTimezone,
        lastMonitoringDate,
        seedingAverageWeight,
        lastMonitoringMlResult,
        lastMonitoringDateTimezone,
        history
      } = currentPopulation;

      const {
        cumulativeFcr,
        feedKgByHa,
        profitPerHaPerDay,
        totalProfit,
        revenuePerPound,
        profitPerPound,
        totalRevenue,
        totalCosts,
        biomassLbs,
        totalBiomassLbs,
        totalProcessedBiomassLbs,
        date
      } = history?.[0] ?? {};

      if (isString(date) && isString(stockedAt)) {
        const historyDateLuxon = DateTime.fromISO(date, { zone: farmTimezone });
        const stockedAtLuxon = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone ?? farmTimezone });

        const cycleDays = getDaysDiffBetweenDates({
          baseDate: historyDateLuxon.toFormat('yyyy-MM-dd'),
          dateToCompare: stockedAtLuxon.toFormat('yyyy-MM-dd')
        });

        acc.cycleDaysSum += cycleDays;
      }

      if (pondSize) {
        acc.pondSizeSum += pondSize;
      }

      if (totalCosts) {
        acc.totalCostsSum = acc.totalCostsSum + totalCosts;
      }

      if (feedKgByHa) {
        acc.feedKgByHaCount += 1;
        acc.feedKgByHaSum = acc.feedKgByHaSum + feedKgByHa;
      }

      if (totalRevenue) {
        acc.totalRevenueSum = acc.totalRevenueSum + totalRevenue;
      }

      if (totalProfit) {
        acc.totalProfitSum = acc.totalProfitSum + totalProfit;
      }

      if (profitPerHaPerDay) {
        acc.profitPerHaPerDaySum = acc.profitPerHaPerDaySum + profitPerHaPerDay;
      }

      if (revenuePerPound) {
        acc.revenuePerPoundCount += 1;
        acc.revenuePerPoundSum = acc.revenuePerPoundSum + revenuePerPound;
      }

      if (profitPerPound) {
        acc.profitPerPoundCount += 1;
        acc.profitPerPoundSum = acc.profitPerPoundSum + profitPerPound;
      }

      const { value: lastSurvivalRate } = getLastDateRecordValue(survivalRate);
      const averageWeight = lastMonitoringMlResult?.averageWeight ?? seedingAverageWeight;

      // live biomass
      if (biomassLbs) {
        acc.biomassLbsSum = acc.biomassLbsSum + biomassLbs;
      }

      // total biomass
      if (totalBiomassLbs) {
        acc.totalBiomassLbsSum = acc.totalBiomassLbsSum + totalBiomassLbs;
      }

      // total processed biomass
      if (totalProcessedBiomassLbs) {
        acc.totalProcessedBiomassLbsSum = acc.totalProcessedBiomassLbsSum + totalProcessedBiomassLbs;
      }

      const monitoredAtLuxon = DateTime.fromISO(lastMonitoringDate, {
        zone: lastMonitoringDateTimezone ?? farmTimezone
      });

      if (history?.length) {
        const { series: growthSeries } = getGrowthSeries(history ?? []) ?? {};
        const twoWeekAvgGrowthData = growthSeries?.find((growthSeriesItem) => {
          return growthSeriesItem?.date === monitoredAtLuxon.toFormat('yyyy-MM-dd');
        });
        const twoWeekAvgGrowth = twoWeekAvgGrowthData?.y ?? 0;
        acc.avg2wkGrowth = twoWeekAvgGrowth;
        acc.maxAvg2wkGrowth = acc.maxAvg2wkGrowth ? Math.max(acc.maxAvg2wkGrowth, twoWeekAvgGrowth) : twoWeekAvgGrowth;
        acc.minAvg2wkGrowth = acc.minAvg2wkGrowth ? Math.min(acc.minAvg2wkGrowth, twoWeekAvgGrowth) : twoWeekAvgGrowth;
      }

      if (lastWeekGrowth) {
        acc.pondsWithGrowthCount += 1;
        acc.growthSum = acc.growthSum + lastWeekGrowth;
        acc.maxGrowth = acc.maxGrowth ? Math.max(acc.maxGrowth, lastWeekGrowth) : lastWeekGrowth;
        acc.minGrowth = acc.minGrowth ? Math.min(acc.minGrowth, lastWeekGrowth) : lastWeekGrowth;
      }

      const stockedAtLuxon = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone ?? farmTimezone });
      const stockedDaysDiff = getDaysDiffBetweenDates({
        baseDate: monitoredAtLuxon.toFormat('yyyy-MM-dd'),
        dateToCompare: stockedAtLuxon.toFormat('yyyy-MM-dd')
      });

      if (stockedDaysDiff) {
        acc.pondsWithDaysOfCultureCount += 1;
        acc.daysOfCultureSum = acc.daysOfCultureSum + stockedDaysDiff;
      }

      if (averageWeight) {
        acc.pondsWithAbwCount += 1;
        acc.abwSum = acc.abwSum + averageWeight;
        acc.maxAvgAbw = acc.maxAvgAbw ? Math.max(acc.maxAvgAbw, averageWeight) : averageWeight;
        acc.minAvgAbw = acc.minAvgAbw ? Math.min(acc.minAvgAbw, averageWeight) : averageWeight;
      }

      if (lastSurvivalRate) {
        acc.pondsWithSurvivalRateCount += 1;
        acc.survivalRateSum = acc.survivalRateSum + lastSurvivalRate;
      }

      if (cumulativeFcr) {
        acc.pondsWithFcrCount += 1;
        acc.fcrSum = acc.fcrSum + cumulativeFcr;
        acc.maxFcr = acc.maxFcr ? Math.max(acc.maxFcr, cumulativeFcr) : cumulativeFcr;
        acc.minFcr = acc.minFcr ? Math.min(acc.minFcr, cumulativeFcr) : cumulativeFcr;
      }

      return acc;
    },
    {
      pondSizeSum: 0,
      cycleDaysSum: 0,
      totalCostsSum: 0,
      feedKgByHaSum: 0,
      feedKgByHaCount: 0,
      totalRevenueSum: 0,
      totalProfitSum: 0,
      profitPerHaPerDaySum: 0,
      revenuePerPoundSum: 0,
      revenuePerPoundCount: 0,
      profitPerPoundSum: 0,
      profitPerPoundCount: 0,
      costPerLbSum: 0,
      biomassLbsSum: 0,
      totalBiomassLbsSum: 0,
      totalProcessedBiomassLbsSum: 0,
      avg2wkGrowth: 0,
      minAvg2wkGrowth: undefined,
      maxAvg2wkGrowth: undefined,

      fcrSum: 0,
      maxFcr: undefined,
      minFcr: undefined,
      pondsWithFcrCount: 0,

      abwSum: 0,
      maxAvgAbw: undefined,
      minAvgAbw: undefined,
      pondsWithAbwCount: 0,

      growthSum: 0,
      maxGrowth: undefined,
      minGrowth: undefined,
      pondsWithGrowthCount: 0,

      daysOfCultureSum: 0,
      pondsWithDaysOfCultureCount: 0,

      survivalRateSum: 0,
      pondsWithSurvivalRateCount: 0
    }
  );

  const cardValues = {
    ...data,
    costPoundHarvest: data?.totalCostsSum ? data.totalCostsSum / data.totalBiomassLbsSum : undefined,
    costPerPound: data?.totalCostsSum ? data.totalCostsSum / data.totalProcessedBiomassLbsSum : undefined,
    avgProfitPerPound: data.profitPerPoundSum ? data.profitPerPoundSum / data.profitPerPoundCount : undefined,
    avgRevenuePerPound: data.revenuePerPoundSum ? data.revenuePerPoundSum / data.revenuePerPoundCount : undefined,
    profitPerHaPerDay: data?.totalProfitSum ? data.totalProfitSum / data.cycleDaysSum / data.pondSizeSum : undefined,
    avgFeedKgByHa: data.feedKgByHaSum / data.feedKgByHaCount,
    avgFcr: data.fcrSum / data.pondsWithFcrCount,
    avgAbw: data.abwSum / data.pondsWithAbwCount,
    avgGrowth: data.growthSum / data.pondsWithGrowthCount,
    avgSurvivalRate: data.survivalRateSum / data.pondsWithSurvivalRateCount,
    avgDaysOfCulture: data.daysOfCultureSum / data.pondsWithDaysOfCultureCount
  };

  const {
    costPoundHarvest,
    costPerPound,
    totalCostsSum,
    avgRevenuePerPound,
    avgProfitPerPound,
    profitPerHaPerDay,
    totalProfitSum,
    avgAbw,
    minAvgAbw,
    maxAvgAbw,
    avg2wkGrowth,
    minAvg2wkGrowth,
    maxAvg2wkGrowth,
    avgGrowth,
    minGrowth,
    maxGrowth,
    avgFcr,
    minFcr,
    maxFcr,
    avgSurvivalRate,
    avgDaysOfCulture,
    avgFeedKgByHa,
    biomassLbsSum
  } = cardValues;
  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);
  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');
  const avgDaysOfCultureUnit = avgDaysOfCulture > 1 ? ` ${trans('t_days')}` : ` ${trans('t_day')}`;
  const costFinancials =
    isAdmin || isSupervisor
      ? [
          {
            col1: trans('t_cost_per_unit_harvested', { unit: unitLabel }),
            col2: costPoundHarvest
              ? `${formatNumber(convertUnitByMultiplication(costPoundHarvest, unitsConfig?.biomass), {
                  lang,
                  fractionDigits: 2,
                  isCurrency: true
                })}`
              : '-'
          },
          {
            col1: trans('t_cost_per_unit_processed', { unit: unitLabel }),
            col2: costPerPound
              ? `${formatNumber(convertUnitByMultiplication(costPerPound, unitsConfig?.biomass), {
                  lang,
                  fractionDigits: 2,
                  isCurrency: true
                })}`
              : '-'
          },
          {
            col1: trans('t_cost_$'),
            col2: totalCostsSum
              ? (formatNumber(totalCostsSum, { lang, fractionDigits: 0, isCurrency: true }) as string)
              : '-'
          }
        ]
      : [];
  const revenueFinancials = isAdmin
    ? [
        {
          col1: isBiomassUnitLbs ? trans('t_revenue_per_pound') : trans('t_revenue_per_kg'),
          col2: avgRevenuePerPound
            ? `${formatNumber(convertUnitByMultiplication(avgRevenuePerPound, unitsConfig?.biomass), {
                lang,
                fractionDigits: 2,
                isCurrency: true
              })}`
            : '-'
        },
        {
          col1: isBiomassUnitLbs ? trans('t_profit_lb') : trans('t_profit_kg'),
          col2: avgProfitPerPound
            ? `${formatNumber(convertUnitByMultiplication(avgProfitPerPound, unitsConfig?.biomass), {
                lang,
                fractionDigits: 2,
                isCurrency: true
              })}`
            : '-'
        },
        {
          col1: trans('t_profit_include_dry_days_ha_day'),
          col2: profitPerHaPerDay
            ? `${formatNumber(profitPerHaPerDay, { lang, fractionDigits: 2, isCurrency: true })}`
            : '-'
        },
        {
          col1: trans('t_profit'),
          col2: totalProfitSum ? `${formatNumber(totalProfitSum, { lang, fractionDigits: 0, isCurrency: true })}` : '-'
        }
      ]
    : [];

  const financials =
    (costFinancials.length || revenueFinancials.length) && !isVisionOnly
      ? [{ col1: trans('t_financial'), col2: '' }, ...costFinancials, ...revenueFinancials, { col1: '', col2: '' }]
      : [];

  const production = isVisionOnly
    ? [
        {
          col1: trans('t_avg_abw_g'),
          col2: avgAbw
            ? `${formatNumber(avgAbw, { lang, fractionDigits: 2 })} ( ${formatNumber(minAvgAbw, { lang, fractionDigits: 2 })} - ${formatNumber(maxAvgAbw, { lang, fractionDigits: 2 })} )`
            : '-'
        },
        {
          col1: trans('t_avg_2wk_growth'),
          col2: avg2wkGrowth
            ? `${formatNumber(avg2wkGrowth, { lang, fractionDigits: 2 })} ( ${formatNumber(minAvg2wkGrowth, { lang, fractionDigits: 2 })} - ${formatNumber(maxAvg2wkGrowth, { lang, fractionDigits: 2 })} )`
            : '-'
        },
        {
          col1: trans('t_avg_weekly_growth'),
          col2: avgGrowth
            ? `${formatNumber(avgGrowth, { lang, fractionDigits: 2 })} ( ${formatNumber(minGrowth, { lang, fractionDigits: 2 })} - ${formatNumber(maxGrowth, { lang, fractionDigits: 2 })} )`
            : '-'
        },
        {
          col1: trans('t_avg_days_of_culture'),
          col2: avgDaysOfCulture
            ? `${formatNumber(avgDaysOfCulture, { lang, fractionDigits: 0 })} ${avgDaysOfCultureUnit}`
            : '-'
        }
      ]
    : [
        {
          col1: trans('t_avg_abw_g'),
          col2: avgAbw
            ? `${formatNumber(avgAbw, { lang, fractionDigits: 2 })} ( ${formatNumber(minAvgAbw, { lang, fractionDigits: 2 })} - ${formatNumber(maxAvgAbw, { lang, fractionDigits: 2 })} )`
            : '-'
        },
        {
          col1: trans('t_avg_2wk_growth'),
          col2: avg2wkGrowth
            ? `${formatNumber(avg2wkGrowth, { lang, fractionDigits: 2 })} ( ${formatNumber(minAvg2wkGrowth, { lang, fractionDigits: 2 })} - ${formatNumber(maxAvg2wkGrowth, { lang, fractionDigits: 2 })} )`
            : '-'
        },
        {
          col1: trans('t_avg_weekly_growth'),
          col2: avgGrowth
            ? `${formatNumber(avgGrowth, { lang, fractionDigits: 2 })} ( ${formatNumber(minGrowth, { lang, fractionDigits: 2 })} - ${formatNumber(maxGrowth, { lang, fractionDigits: 2 })} )`
            : '-'
        },
        {
          col1: trans('t_avg_fcr'),
          col2: avgFcr
            ? `${formatNumber(avgFcr, { lang, fractionDigits: 1 })} ( ${formatNumber(minFcr, { lang, fractionDigits: 1 })} - ${formatNumber(maxFcr, { lang, fractionDigits: 1 })} )`
            : '-'
        },
        {
          col1: trans('t_avg_survival'),
          col2: avgSurvivalRate
            ? `${formatNumber(avgSurvivalRate, { lang, fractionDigits: 0, isPercentage: true })}`
            : '-'
        },
        {
          col1: trans('t_avg_days_of_culture'),
          col2: avgDaysOfCulture
            ? `${formatNumber(avgDaysOfCulture, { lang, fractionDigits: 0 })} ${avgDaysOfCultureUnit}`
            : '-'
        },
        {
          col1: trans('t_kg_over_ha_over_day'),
          col2: avgFeedKgByHa
            ? `${formatNumber(avgFeedKgByHa, { lang, fractionDigits: 0 })}  ${trans('t_kg_over_ha_over_day')}`
            : '-'
        },
        {
          col1: isBiomassUnitLbs ? trans('t_biomass_lb') : trans('t_biomass_kg'),
          col2: biomassLbsSum
            ? `${formatNumber(convertUnitByDivision(biomassLbsSum, unitsConfig?.biomass), { lang, fractionDigits: 0 })}  ${unitLabel}`
            : '-'
        }
      ];

  return [...financials, { col1: trans('t_production'), col2: '' }, ...production];
}

export function UseExportPondData(props: PropsType) {
  const { ponds, viewVariables, selectedIndicatingStatus } = props;
  const { trans } = getTrans();
  const currentFarm = useAppSelector((state) => state.farm?.currentFarm);
  const { _id: farmId, metadata, timezone: farmTimezone, name: farmName } = currentFarm ?? {};

  const isVisionOnly = metadata?.productOffering === 'visionOnly';
  const isAdmin = usePermission({ role: 'admin', entity: 'farm', entityId: farmId });
  const isSupervisor = usePermission({ role: 'supervisor', entity: 'farm', entityId: farmId });

  const lang = useAppSelector((state) => state.app.lang);
  const user = useAppSelector((state) => state.auth.user);

  const { calculatePondCardData } = useCalculatePondCardData({ farmId });

  const sortedPonds = useMemo(() => {
    return sortArray({
      list: ponds,
      sortDir: 'asc',
      compareFunction: (a, b) => {
        const firstItemValue = get(a, 'name');
        const secondItemValue = get(b, 'name');
        return sortCompareString(firstItemValue, secondItemValue);
      }
    });
  }, [ponds]);

  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);
  const pondViewVariableLabels = getPondViewVariableLabels({ unitsConfig });

  const getList = (profitProjectionView: PondCardViewType) => {
    return sortedPonds.map((pond) => {
      const { currentPopulation, name: pondName, size: pondSize } = pond;
      const { isNewPond, hasMonitoring } = getPondState({ population: currentPopulation });
      const {
        stockedAt,
        stockedAtTimezone,
        seedingAverageWeight,
        lastMonitoringDate,
        history,
        symmetry,
        harvestPlan,
        metadata,
        cycleInformation,
        productionPrediction,
        stockingCostsMillar,
        manualAverageWeights,
        partialHarvest,
        seedingQuantity,
        lastMonitoringDistance
      } = currentPopulation ?? {};
      const {
        projection,
        indicators,
        harvestType: oldHarvestType,
        processorPriceList,
        harvestDate
      } = harvestPlan ?? {};
      const harvestType = processorPriceList?.defaultHarvestType ?? oldHarvestType;

      const lastMonitoringDateLuxon = DateTime.fromISO(lastMonitoringDate, { zone: farmTimezone });
      const lastMonitoringDateFormatted = lastMonitoringDateLuxon.toFormat('yyyy-MM-dd');
      const manuallyMonitored = metadata?.manuallyMonitored;

      const lastHistoryData = history?.[0];

      const stockedAtLuxon = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone ?? farmTimezone });

      const cycleDays = getDaysDiffBetweenDates({
        baseDate: lastMonitoringDateFormatted,
        dateToCompare: stockedAtLuxon.toFormat('yyyy-MM-dd')
      });

      const { plannedHarvestIdx, optimalHarvestIdx, profitProjectionData } = projection ?? {};
      const plannedOptimalHarvestDate = profitProjectionData?.[optimalHarvestIdx]?.date;

      const productionPredictionHarvestDate = productionPrediction?.find((p) => p.date === harvestDate);
      const plannedProfitProjection = profitProjectionData?.[plannedHarvestIdx]?.[harvestType as 'headon'];

      const pondSizeFormatted = formatNumber(pondSize, { lang, fractionDigits: 0 });
      const pondSizeValue = pondSize ? `${pondSizeFormatted} ${trans('t_ha')}` : null;
      const seedingQuantityHa = seedingQuantity && pondSize ? seedingQuantity / pondSize : null;
      const seedingQuantityHaFormatted = seedingQuantityHa
        ? formatNumber(seedingQuantityHa, { lang, fractionDigits: 0 })
        : null;
      const sumOfLbsHarvested = partialHarvest?.reduce((acc, curr) => acc + (curr?.lbsHarvested ?? 0), 0);
      const partialHarvestLbsHa = sumOfLbsHarvested && pondSize ? sumOfLbsHarvested / pondSize : null;
      const unitTitle = isBiomassUnitLbs ? trans('t_lb_over_ha') : trans('t_kg_over_ha');
      const partialHarvestHaValue = partialHarvestLbsHa
        ? `${formatNumber(convertUnitByDivision(partialHarvestLbsHa, unitsConfig?.biomass), { lang, fractionDigits: 2 })} ${unitTitle}`
        : null;
      const numberOfPartialHarvests = partialHarvest?.length;
      const lastPartialHarvestDate = partialHarvest?.[numberOfPartialHarvests - 1]?.date;
      const lastMonitoringDistanceFormatted = isNumber(lastMonitoringDistance)
        ? formatNumber(lastMonitoringDistance, { lang, fractionDigits: 2 })
        : null;

      const data = calculatePondCardData({
        indicators,
        selectedIndicatingStatus,
        profitProjectionView,
        optimalHarvestDate: plannedOptimalHarvestDate,
        lastHistory: { ...lastHistoryData, cycleDays },
        plannedProfitProjection,
        productionPredictionHarvestDate,
        farmTimezone,
        cycleInformation,
        stockingCostsMillar,
        manualAverageWeights
      });

      const {
        growthLinearFormatted,
        growthDailyFormatted,
        weeklyGrowthFormatted,
        growthTwoWeeksFormatted,
        growthThreeWeeksFormatted,
        growthFourWeeksFormatted,
        cumulativeFcrFormatted,
        weeklyFcrFormatted,
        adjustedFcrFormatted,
        feedKgByHaFormatted,
        weeklyFeedGivenFormatted,
        totalFeedGivenKgFormatted,
        totalFeedGivenLbsFormatted,
        feedAsBiomassPercentFormatted,
        survivalRateFormatted,
        survivalFeedFormatted,
        survivalRateWithPartialHarvestFormatted,
        biomassLbsFormatted,
        totalBiomassLbsFormatted,
        biomassLbsByHaFormatted,
        totalBiomassLbsByHaFormatted,
        animalsRemainingM2Formatted,
        animalsRemainingHaFormatted,
        weightCvFormatted,
        profitPerHaPerDayAdminValue,
        profitPerPoundAdminValue,
        totalProfitAdminValue,
        revenuePerPoundAdminValue,
        totalRevenuePerPoundAdminValue,
        totalRevenueAdminValue,
        stockingCostsAdminValue,
        stockingCostsMillarAdminValue,
        cumulativeOverheadCostsAdminValue,
        cumulativeFeedCostsAdminValue,
        costPerPoundAdminValue,
        costPoundHarvestAdminValue,
        feedCostPerKgAdminValue,
        daysUntilHarvestFormatted,
        daysOffFromOptimalTitle,
        isAverageWeightInvalid,
        averageWeightFormatted,
        totalCostsAdminValue
      } = data ?? {};

      const typeText = { left: trans('t_left'), right: trans('t_right'), normal: trans('t_normal') };

      const isCurrentView = profitProjectionView === 'current';

      const seedingAverageWeightFormatted = isNumber(seedingAverageWeight)
        ? formatNumber(seedingAverageWeight, { lang, fractionDigits: 2 })
        : '-';

      const abw = isCurrentView ? averageWeightFormatted || seedingAverageWeightFormatted : averageWeightFormatted;

      const getValue = (variable: FarmSummaryViewVariable) => {
        if (isNewPond || !hasMonitoring) return '-';
        const symmetryValue = !symmetry ? '-' : (typeText[symmetry.type] ?? symmetry.type);
        const abwValue = abw || '-';

        const valuesHash: Record<FarmSummaryViewVariable, string | number> = {
          pondSize: pondSizeValue ?? '-',
          stockingDate: stockedAt ?? '-',
          animalsStockedHa: seedingQuantityHaFormatted ?? '-',
          partialHarvestLbsHa: partialHarvestHaValue ?? '-',
          lastPartialHarvestDate: lastPartialHarvestDate
            ? formatDate({ locale: lang, date: lastPartialHarvestDate, format: 'LLL dd, yyyy' })
            : '-',
          plannedHarvestDate: harvestDate
            ? formatDate({ locale: lang, date: harvestDate, format: 'LLL dd, yyyy' })
            : '-',
          numberOfPartialHarvests: numberOfPartialHarvests ?? '-',
          averageWeight: isAverageWeightInvalid ? averageWeightFormatted : abwValue,
          growthLinear: growthLinearFormatted || '-',
          growthDaily: growthDailyFormatted || '-',
          growth1WeekAvg: weeklyGrowthFormatted || '-',
          growth2WeekAvg: growthTwoWeeksFormatted || '-',
          growth3WeekAvg: growthThreeWeeksFormatted || '-',
          growth4w: growthFourWeeksFormatted || '-',
          fcrCumulative: cumulativeFcrFormatted ?? '-',
          fcrWeekly: weeklyFcrFormatted ?? '-',
          adjustedFcr: adjustedFcrFormatted ?? '-',
          kgPerHaPerDayGivenKg: feedKgByHaFormatted || '-',
          weeklyFeedGivenKg: weeklyFeedGivenFormatted || '-',
          totalFeedGivenKg: totalFeedGivenKgFormatted || '-',
          totalFeedGivenLbs: totalFeedGivenLbsFormatted || '-',
          biomassPercentage: feedAsBiomassPercentFormatted || '-',
          survival: survivalRateFormatted || '-',
          survivalFeed: survivalFeedFormatted || '-',
          survivalWithPartialHarvest: survivalRateWithPartialHarvestFormatted || '-',
          biomassLb: biomassLbsFormatted ?? '-',
          biomassLbTotal: totalBiomassLbsFormatted ?? '-',
          biomassLbHa: biomassLbsByHaFormatted ?? '-',
          totalBiomassLbHa: totalBiomassLbsByHaFormatted ?? '-',
          animalsRemainingM2: animalsRemainingM2Formatted || '-',
          animalsRemainingHa: animalsRemainingHaFormatted || '-',
          dispersion: manuallyMonitored ? 'N/A' : `${weightCvFormatted} ${trans('t_cv')}`,
          symmetry: manuallyMonitored ? 'N/A' : symmetryValue,
          profitPerHaPerDay: profitPerHaPerDayAdminValue ?? '-',
          profitPerPound: profitPerPoundAdminValue ?? '-',
          totalProfit: totalProfitAdminValue ?? '-',
          revenuePerPound: revenuePerPoundAdminValue ?? '-',
          totalRevenuePound: totalRevenuePerPoundAdminValue ?? '-',
          totalRevenue: totalRevenueAdminValue ?? '-',
          totalCosts: totalCostsAdminValue ?? '-',
          stockingCosts: stockingCostsAdminValue ?? '-',
          stockingCostsMillar: stockingCostsMillarAdminValue ?? '-',
          cumulativeOverheadCosts: cumulativeOverheadCostsAdminValue ?? '-',
          cumulativeFeedCosts: cumulativeFeedCostsAdminValue ?? '-',
          costPerPound: costPerPoundAdminValue ?? '-',
          costPoundHarvest: costPoundHarvestAdminValue ?? '-',
          feedCostPerKg: feedCostPerKgAdminValue ?? '-',
          daysUntilHarvest: daysUntilHarvestFormatted ?? '-',
          daysOffFromOptimal: daysOffFromOptimalTitle ?? '-',
          lastMonitoringDistance: lastMonitoringDistanceFormatted ?? '-'
        };

        return valuesHash[variable] ?? '-';
      };

      const row: Record<string, string | number> = { [trans('t_pond')]: pondName };

      viewVariables.forEach((variable) => {
        let variableLabel = pondViewVariableLabels[variable];
        if (/^\d/.test(variableLabel)) {
          variableLabel = `xlsx_prefix_${variableLabel}`;
        }
        row[variableLabel] = getValue(variable);
      });

      return row;
    });
  };

  const exportData = ({
    profitProjectionView,
    includeFarmSummary
  }: {
    profitProjectionView: PondCardViewType | 'all';
    includeFarmSummary: boolean;
  }) => {
    const columnHeaders = [trans('t_pond')].concat(viewVariables.map((variable) => pondViewVariableLabels[variable]));

    const todayDate = DateTime.local({ zone: farmTimezone }).toFormat('yyyy-MM-dd');
    const fileName = snakeCase(`${farmName?.toLowerCase()}_${trans('t_farm_summary')}_${todayDate}`.slice(0, 30));

    if (profitProjectionView === 'all') {
      const farmSummaryCurrent = getList('current');
      const farmSummaryAtHarvest = getList('harvest');

      return exportAsXLSXTabs({
        fileName,
        tabs: [
          { list: farmSummaryCurrent, columnHeaders, tabName: `${trans('t_current')}` },
          { list: farmSummaryAtHarvest, columnHeaders, tabName: `${trans('t_expected_at_harvest')}` }
        ]
      });
    }

    const farmSummaryList = getList(profitProjectionView);

    if (profitProjectionView === 'current') {
      const pondOverviewListData = getPondOverviewData({
        ponds: sortedPonds,
        farmTimezone,
        unitsConfig,
        lang,
        isAdmin,
        isSupervisor,
        isVisionOnly
      });
      const farmSummaryTab = includeFarmSummary
        ? [{ list: pondOverviewListData, columnHeaders: ['', ''], tabName: trans('t_farm_summary') }]
        : [];
      exportAsXLSXTabs({
        fileName,
        tabs: [{ list: farmSummaryList, columnHeaders, tabName: `${trans('t_farm_summary')}_` }, ...farmSummaryTab]
      });
    } else {
      exportAsXLSX({ list: farmSummaryList, columnHeaders, fileName });
    }
  };

  return { exportData };
}
