import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { usePermission } from '@hooks/use-permission';
import isUndefined from 'lodash/isUndefined';

export const harvestVariableValues = [
  'profitPerHaPerDay',
  'averageWeight',
  'fcr',
  'adjustedFcr',
  'survivalAllHarvests',
  'survivalFinalHarvest',
  'animalHarvested',
  'totalBiomass',
  'biomassPartialHarvest',
  'biomassFinalHarvest',
  'totalProfit',
  'totalCosts',
  'totalFeedCosts',
  'totalRevenue',
  'revenuePartial',
  'revenueFinal',
  'rofi'
] as const;

export type HarvestVariable = (typeof harvestVariableValues)[number];

const harvestVariableSet = new Set<HarvestVariable>(harvestVariableValues);

export function isHarvestVariable(name: string): name is HarvestVariable {
  return harvestVariableSet.has(name as HarvestVariable);
}
type OneHarvestVariableListItem = {
  label: string;
  value: HarvestVariable;
  options?: { label: string; value: string }[];
};

type UseGetHarvestViewVariablesReturnType = {
  title: string;
  items: OneHarvestVariableListItem[];
};
export function useGetHarvestViewVariables(): UseGetHarvestViewVariablesReturnType[] {
  const { trans } = getTrans();

  const currentFarmId = useAppSelector((state) => state.farm.currentFarm?._id);
  const user = useAppSelector((state) => state.auth.user);
  const productOffering = useAppSelector((state) => state.farm?.currentFarm?.metadata?.productOffering);

  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const isVisionOnly = productOffering === 'visionOnly';

  const isAdmin = usePermission({
    role: 'admin',
    entity: 'farm',
    entityId: currentFarmId
  });

  const isSupervisor = usePermission({
    role: 'supervisor',
    entity: 'farm',
    entityId: currentFarmId
  });

  const isAdminOrSupervisor = isAdmin || isSupervisor;

  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);
  const biomassUnit = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');
  const biomassTitle = isBiomassUnitLbs ? trans('t_biomass_lb') : trans('t_biomass_kg');

  if (isVisionOnly) {
    return [
      {
        title: trans('t_weights'),
        items: [
          {
            value: 'averageWeight',
            label: trans('t_abw_g'),
            options: [
              { value: 'daily', label: trans('t_daily_linear_growth') },
              { value: 'weekly', label: trans('t_weekly_linear_growth') }
            ]
          }
        ]
      }
    ];
  }

  const financialVariables: UseGetHarvestViewVariablesReturnType[] = isAdminOrSupervisor
    ? [
        {
          title: trans('t_financials'),
          items: [
            ...(isAdmin
              ? ([
                  { value: 'profitPerHaPerDay', label: trans('t_profit_include_dry_days_ha_day') },
                  { value: 'totalProfit', label: trans('t_total_profit_profit_over_x', { unit: biomassUnit }) }
                ] as OneHarvestVariableListItem[])
              : []),
            {
              value: 'totalCosts',
              label: trans('t_cost_$'),
              options: [
                { value: 'processed', label: trans('t_cost_per_unit_processed', { unit: biomassUnit }) },
                { value: 'harvested', label: trans('t_cost_per_unit_harvested', { unit: biomassUnit }) }
              ]
            },
            { value: 'rofi', label: trans('t_rofi') },
            { value: 'totalFeedCosts', label: trans('t_feed_cost_$') },
            ...(isAdmin
              ? [
                  {
                    value: 'totalRevenue',
                    label: trans('t_revenue', { unit: biomassUnit }),
                    options: [
                      { value: 'processed', label: trans('t_revenue_per_unit_processed', { unit: biomassUnit }) },
                      { value: 'harvested', label: trans('t_revenue_per_unit_harvested', { unit: biomassUnit }) }
                    ]
                  } as OneHarvestVariableListItem,
                  {
                    value: 'revenuePartial',
                    label: `${trans('t_revenue')} - ${trans('t_partial_harvest')}`,
                    options: [
                      { value: 'processed', label: trans('t_revenue_per_unit_processed', { unit: biomassUnit }) },
                      { value: 'harvested', label: trans('t_revenue_per_unit_harvested', { unit: biomassUnit }) }
                    ]
                  } as OneHarvestVariableListItem,
                  {
                    value: 'revenueFinal',
                    label: `${trans('t_revenue')} - ${trans('t_final_harvest')}`,
                    options: [
                      { value: 'processed', label: trans('t_revenue_per_unit_processed', { unit: biomassUnit }) },
                      { value: 'harvested', label: trans('t_revenue_per_unit_harvested', { unit: biomassUnit }) }
                    ]
                  } as OneHarvestVariableListItem
                ]
              : [])
          ]
        }
      ]
    : [];

  return [
    {
      title: trans('t_weights'),
      items: [
        {
          value: 'averageWeight',
          label: trans('t_abw_g'),
          options: [
            { value: 'daily', label: trans('t_daily_linear_growth') },
            { value: 'weekly', label: trans('t_weekly_linear_growth') }
          ]
        }
      ]
    },
    {
      title: trans('t_feed'),
      items: [
        { value: 'fcr', label: trans('t_fcr') },
        { value: 'adjustedFcr', label: trans('t_fcr_adjusted') }
      ]
    },
    {
      title: biomassTitle,
      items: [
        { value: 'survivalAllHarvests', label: trans('t_survival_include_harvests') },
        { value: 'survivalFinalHarvest', label: trans('t_survival_final_harvest') },

        {
          value: 'animalHarvested',
          label: trans('t_animals_harvested'),
          options: [
            { value: 'animalHarvestedHa', label: trans('t_over_ha') },
            { value: 'animalHarvestedM2', label: trans('t_over_m2') }
          ]
        },
        { value: 'totalBiomass', label: trans('t_total_biomass_biomass_over_ha') },
        { value: 'biomassPartialHarvest', label: trans('t_biomass_lb_partial_harvest') },
        { value: 'biomassFinalHarvest', label: trans('t_biomass_lb_final_harvest') }
      ]
    },
    ...financialVariables
  ];
}
