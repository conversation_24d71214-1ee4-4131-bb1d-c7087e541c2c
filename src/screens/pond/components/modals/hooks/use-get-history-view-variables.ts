import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { usePermission } from '@hooks/use-permission';
import isUndefined from 'lodash/isUndefined';

export type HistoryVariable =
  | 'averageWeight'
  | 'growthLinear'
  | 'growthDaily'
  | 'growth1WeekAvg'
  | 'growth2WeekAvg'
  | 'growthThreeWeeks'
  | 'growth4w'
  | 'fcrCumulative'
  | 'fcrWeekly'
  | 'adjustedFcr'
  | 'kgPerHaPerDayGivenKg'
  | 'weeklyFeedGiven'
  | 'totalFeedGivenKg'
  | 'totalFeedGivenLbs'
  | 'biomassPercentage'
  | 'survival'
  | 'survivalFeed'
  | 'survivalWithPartialHarvest'
  | 'biomassLb'
  | 'biomassLbTotal'
  | 'biomassLbHa'
  | 'totalBiomassLbHa'
  | 'animalsRemainingM2'
  | 'animalsRemainingHa'
  | 'dispersion'
  | 'profitPerHaPerDay'
  | 'totalProfit'
  | 'rofi'
  | 'totalRevenue'
  | 'revenuePerPound'
  | 'totalRevenuePound'
  | 'totalCosts'
  | 'stockingCosts'
  | 'cumulativeOverheadCosts'
  | 'cumulativeFeedCosts'
  | 'costPerPound'
  | 'costPoundHarvest'
  | 'feedCostPerKg'
  | 'stockingCostsMillar';

type OneHistoryVariableListItem = { label: string; value: HistoryVariable };
type OneHistoryVariable = {
  title: string;
  variables: OneHistoryVariableListItem[];
};

type GetHistoryVariablesLabelMapParams = {
  unitsConfig: { biomass: 'kg' | 'lbs' };
};

export function getHistoryVariablesLabelMap(
  params: GetHistoryVariablesLabelMapParams
): Record<HistoryVariable, string> {
  const { unitsConfig } = params;

  const { trans } = getTrans();
  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);
  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');
  return {
    averageWeight: trans('t_abw_g'),
    growthLinear: trans('t_growth_g_wk'),
    growthDaily: trans('t_growth_g_day'),
    growth1WeekAvg: trans('t_growth_monitored_x_week_g_wk', { weeks: 1 }),
    growth2WeekAvg: trans('t_growth_monitored_x_week_g_wk', { weeks: 2 }),
    growthThreeWeeks: trans('t_growth_monitored_x_week_g_wk', { weeks: 3 }),
    growth4w: trans('t_growth_monitored_x_week_g_wk', { weeks: 4 }),
    fcrCumulative: trans('t_fcr_cumulative'),
    fcrWeekly: trans('t_fcr_weekly'),
    adjustedFcr: trans('t_fcr_adjusted'),
    kgPerHaPerDayGivenKg: trans('t_feed_given_daily_kg_ha'),
    weeklyFeedGiven: trans('t_feed_given_weekly_kg'),
    totalFeedGivenKg: trans('t_feed_given_kg'),
    totalFeedGivenLbs: trans('t_feed_given_lg'),
    biomassPercentage: trans('t_feed_%_of_biomass'),
    survival: trans('t_survival_live'),
    survivalWithPartialHarvest: trans('t_survival_include_harvests'),
    biomassLb: trans('t_biomass_in_pond_unit', { unit: unitLabel }),
    biomassLbTotal: trans('t_biomass_include_harvests_unit', { unit: unitLabel }),
    biomassLbHa: trans('t_biomass_in_pond_unit_ha', { unit: unitLabel }),
    totalBiomassLbHa: trans('t_biomass_include_harvests_unit_ha', { unit: unitLabel }),
    animalsRemainingM2: trans('t_animals_in_pond_m2'),
    animalsRemainingHa: trans('t_animals_in_pond_ha'),
    dispersion: trans('t_dispersion_cv'),
    profitPerHaPerDay: trans('t_profit_include_dry_days_ha_day'),
    totalProfit: trans('t_profit'),
    rofi: trans('t_rofi'),
    totalRevenue: trans('t_revenue'),
    totalCosts: trans('t_cost_$'),
    stockingCosts: trans('t_stocking_cost'),
    cumulativeOverheadCosts: trans('t_overhead_cost_cumulative'),
    cumulativeFeedCosts: trans('t_feed_cost_$'),
    costPerPound: trans('t_cost_per_unit_processed', { unit: unitLabel }),
    costPoundHarvest: trans('t_cost_per_unit_harvested', { unit: unitLabel }),
    feedCostPerKg: trans('t_feed_cost_per_kg'),
    stockingCostsMillar: trans('t_cost_millar'),
    survivalFeed: trans('t_kampi_survival'),
    revenuePerPound: isBiomassUnitLbs ? trans('t_revenue_per_lb_live') : trans('t_revenue_per_kg_live'),
    totalRevenuePound: isBiomassUnitLbs
      ? trans('t_revenue_per_lb_include_harvest')
      : trans('t_revenue_per_kg_include_harvest')
  };
}

type HistorySections = 'weights' | 'feed' | 'biomass' | 'dispersion' | 'financials';
type HistoryViewSection = Record<HistorySections, OneHistoryVariable>;
export function useGetHistoryViewVariables(): HistoryViewSection {
  const { trans } = getTrans();

  const currentFarmId = useAppSelector((state) => state.farm.currentFarm?._id);
  const productOffering = useAppSelector((state) => state.farm?.currentFarm?.metadata?.productOffering);
  const user = useAppSelector((state) => state.auth.user);

  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const isVisionOnly = productOffering === 'visionOnly';

  const isAdmin = usePermission({
    role: 'admin',
    entity: 'farm',
    entityId: currentFarmId
  });

  const isSupervisor = usePermission({
    role: 'supervisor',
    entity: 'farm',
    entityId: currentFarmId
  });

  const isUser = !isAdmin && !isSupervisor;

  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);
  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');

  if (isVisionOnly) {
    return {
      weights: {
        title: trans('t_weights'),
        variables: [
          { label: trans('t_abw_g'), value: 'averageWeight' },
          { label: trans('t_growth_g_wk'), value: 'growthLinear' },
          { label: trans('t_growth_g_day'), value: 'growthDaily' },
          { label: trans('t_growth_monitored_x_week_g_wk', { weeks: 1 }), value: 'growth1WeekAvg' },
          { label: trans('t_growth_monitored_x_week_g_wk', { weeks: 2 }), value: 'growth2WeekAvg' }
        ]
      },
      dispersion: {
        title: trans('t_dispersion'),
        variables: [{ value: 'dispersion', label: trans('t_dispersion_cv') }]
      }
    } as HistoryViewSection;
  }

  const adminFinancials: OneHistoryVariableListItem[] = isAdmin
    ? [
        { label: trans('t_profit_include_dry_days_ha_day'), value: 'profitPerHaPerDay' },
        {
          label: isBiomassUnitLbs ? trans('t_revenue_per_lb_live') : trans('t_revenue_per_kg_live'),
          value: 'revenuePerPound'
        },
        {
          label: isBiomassUnitLbs
            ? trans('t_revenue_per_lb_include_harvest')
            : trans('t_revenue_per_kg_include_harvest'),
          value: 'totalRevenuePound'
        },
        { label: trans('t_rofi'), value: 'rofi' },
        { label: trans('t_profit'), value: 'totalProfit' },
        { label: trans('t_revenue'), value: 'totalRevenue' }
      ]
    : [];
  const financials = isUser
    ? ({} as HistoryViewSection)
    : ({
        financials: {
          title: trans('t_financials'),
          variables: [
            ...adminFinancials,
            { label: trans('t_cost_$'), value: 'totalCosts' },
            { label: trans('t_stocking_cost'), value: 'stockingCosts' },
            { label: trans('t_cost_millar'), value: 'stockingCostsMillar' },
            { label: trans('t_overhead_cost_cumulative'), value: 'cumulativeOverheadCosts' },
            { label: trans('t_feed_cost_$'), value: 'cumulativeFeedCosts' },
            { label: trans('t_feed_cost_per_kg'), value: 'feedCostPerKg' },
            {
              label: trans('t_cost_per_unit_processed', { unit: unitLabel }),
              value: 'costPerPound'
            },
            {
              label: trans('t_cost_per_unit_harvested', { unit: unitLabel }),
              value: 'costPoundHarvest'
            }
          ]
        }
      } as HistoryViewSection);
  return {
    weights: {
      title: trans('t_weights'),
      variables: [
        { label: trans('t_abw_g'), value: 'averageWeight' },
        { label: trans('t_growth_g_wk'), value: 'growthLinear' },
        { label: trans('t_growth_g_day'), value: 'growthDaily' },
        { label: trans('t_growth_monitored_x_week_g_wk', { weeks: 1 }), value: 'growth1WeekAvg' },
        { label: trans('t_growth_monitored_x_week_g_wk', { weeks: 2 }), value: 'growth2WeekAvg' },
        { label: trans('t_growth_monitored_x_week_g_wk', { weeks: 3 }), value: 'growthThreeWeeks' },
        { label: trans('t_growth_monitored_x_week_g_wk', { weeks: 4 }), value: 'growth4w' }
      ]
    },
    feed: {
      title: trans('t_feed'),
      variables: [
        { label: trans('t_fcr_cumulative'), value: 'fcrCumulative' },
        { label: trans('t_fcr_weekly'), value: 'fcrWeekly' },
        { label: trans('t_fcr_adjusted'), value: 'adjustedFcr' },
        { label: trans('t_feed_given_daily_kg_ha'), value: 'kgPerHaPerDayGivenKg' },
        { label: trans('t_feed_given_weekly_kg'), value: 'weeklyFeedGiven' },
        { label: trans('t_feed_given_kg'), value: 'totalFeedGivenKg' },
        { label: trans('t_feed_given_lg'), value: 'totalFeedGivenLbs' },
        { label: trans('t_feed_%_of_biomass'), value: 'biomassPercentage' }
      ]
    },
    biomass: {
      title: trans('t_biomass'),
      variables: [
        { value: 'survival', label: trans('t_survival_live') },
        { value: 'survivalFeed', label: trans('t_kampi_survival') },
        { value: 'survivalWithPartialHarvest', label: trans('t_survival_include_harvests') },
        { value: 'biomassLb', label: trans('t_biomass_in_pond_unit', { unit: unitLabel }) },
        {
          value: 'biomassLbTotal',
          label: trans('t_biomass_include_harvests_unit', { unit: unitLabel })
        },
        {
          value: 'biomassLbHa',
          label: trans('t_biomass_in_pond_unit_ha', { unit: unitLabel })
        },
        {
          value: 'totalBiomassLbHa',
          label: trans('t_biomass_include_harvests_unit_ha', { unit: unitLabel })
        },
        { value: 'animalsRemainingM2', label: trans('t_animals_in_pond_m2') },
        { value: 'animalsRemainingHa', label: trans('t_animals_in_pond_ha') }
      ]
    },
    dispersion: {
      title: trans('t_dispersion'),
      variables: [{ value: 'dispersion', label: trans('t_dispersion_cv') }]
    },
    ...financials
  };
}
