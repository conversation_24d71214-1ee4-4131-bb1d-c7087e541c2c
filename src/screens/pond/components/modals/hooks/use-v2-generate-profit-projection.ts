import { IApiHookInput, ICallbackParams, IHookInputType, useApiHookWrapper } from '@hooks/use-api-hook-wrapper';
import { getTrans } from '@i18n/get-trans';
import { addToastAction } from '@redux/app';
import { moduleFarm } from '@sdks/module-farm';
import { throwErrorMsg } from '@utils/errors';
import { Head, MutationRequest, v2GenerateProfitProjectionOutputRequest } from '@xpertsea/module-farm-sdk';

async function mountFn(props: ICallbackParams<Input>) {
  const { input, successCallback } = props;
  const {
    params,
    fields = {
      productionPrediction: {
        daysOfCulture: 1,
        averageWeight: 1,
        weeklyGrowth: 1,
        biomassLbs: 1,
        totalBiomassLbsByHa: 1,
        survivalWithPartialHarvest: 1,
        adjustedFcr: 1,
        totalFeedGivenKg: 1
      }
    }
  } = input;

  const { data, errors } = await moduleFarm.mutation({
    v2GenerateProfitProjection: [params, fields]
  });

  if (errors) {
    throwErrorMsg(errors);
  }

  const generateProfitProjectionRes = data?.v2GenerateProfitProjection;

  successCallback?.(generateProfitProjectionRes);

  return generateProfitProjectionRes;
}

async function errorFn(props: ICallbackParams<Input>) {
  const { dispatch, error } = props;
  const { trans } = getTrans();
  dispatch(addToastAction({ type: 'error', title: trans('t_error'), msg: error }));
}

type Input = IApiHookInput<
  Head<MutationRequest['v2GenerateProfitProjection']>,
  v2GenerateProfitProjectionOutputRequest
>;
type Output = Awaited<ReturnType<typeof mountFn>>;

export function useV2GenerateProfitProjection(props?: IHookInputType<Input, Output>) {
  return useApiHookWrapper({
    mountFn,
    errorFn,
    initialInput: props,
    initialIsLoading: !!props,
    skipInitialApiCallOnEmptyInputs: true,
    unmountFn: undefined,
    __t: props?.__t, // cache buster key
    __cachePrefix: props?.__cachePrefix, // cache key prefix
    __cacheTTL: props?.__cacheTTL // cache ttl in seconds
  });
}
