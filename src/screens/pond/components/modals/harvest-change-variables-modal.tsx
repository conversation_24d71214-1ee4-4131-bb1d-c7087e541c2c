import { Box, chakra, Flex, Heading, Stack, Text, useDisclosure } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { BaseFormCheckbox } from '@components/form/base-form-checkbox';
import { getTrans } from '@i18n/get-trans';
import { ReactNode, useEffect } from 'react';
import { useUpdateUserPreferencesApi } from '@screens/my-account/hooks/use-update-user-preferences';
import { useAppSelector } from '@redux/hooks';
import { Controller, useForm } from 'react-hook-form';
import { useAnalytics } from '@hooks/use-analytics';
import { actionsName } from '@utils/segment';
import {
  HarvestVariable,
  useGetHarvestViewVariables
} from '@screens/pond/components/modals/hooks/use-get-harvest-view-variables';
import { DialogBody, DialogContent, DialogFooter, DialogHeader, DialogRoot, DialogTitle } from '@components/ui/dialog';
import { Radio, RadioGroup } from '@components/ui/radio';
import { HarvestVariablesType } from '@screens/pond/components/pond-overview/helpers/get-harvest-variables';

type HarvestChangeVariablesModalProps = {
  children: ReactNode;
  selectedVariables: HarvestVariablesType[];
};

export function HarvestChangeVariablesModal(props: HarvestChangeVariablesModalProps) {
  const { children, selectedVariables } = props;
  const { onOpen, onClose, open } = useDisclosure();

  return (
    <DialogRoot
      open={open}
      onOpenChange={(e) => {
        e.open ? onOpen() : onClose();
      }}
      scrollBehavior='inside'
    >
      <Box onClick={onOpen}>{children}</Box>

      <Form onCancel={onClose} selectedVariables={selectedVariables} />
    </DialogRoot>
  );
}

const usePreferences = (onSuccess: () => void) => {
  const user = useAppSelector((state) => state.auth?.user);
  const { preferences } = user ?? {};
  const { viewFields, __typename, ...restPreferences } = preferences ?? {};
  const [{ isLoading }, updateUser] = useUpdateUserPreferencesApi();

  const updatePondDetailsPreferences = (harvestVariables: HarvestVariablesType[]) => {
    const fieldsToEdit = { harvestVariables };
    updateUser({
      params: {
        preferences: {
          ...restPreferences,
          viewFields: { ...viewFields, ...fieldsToEdit }
        }
      },
      successCallback: onSuccess
    });
  };

  return {
    isUpdating: isLoading,
    updatePondDetailsPreferences
  };
};

type FormType = Record<HarvestVariable, { isSelected: boolean; subOptions?: string }>;

type FormProps = {
  selectedVariables: HarvestVariablesType[];
  onCancel: () => void;
};

function Form(props: FormProps) {
  const { selectedVariables, onCancel } = props;
  const { trans } = getTrans();
  const { trackAction } = useAnalytics();
  const harvestViewVariables = useGetHarvestViewVariables();

  const variablesCount = harvestViewVariables.reduce((acc, item) => {
    acc += item.items.length;
    return acc;
  }, 0);
  const maxSelected = variablesCount >= 8 ? 8 : variablesCount;

  const { isUpdating, updatePondDetailsPreferences } = usePreferences(() => {
    onCancel();
    trackAction(actionsName.keyVariablesSubmitted).then();
  });

  const { reset, control, handleSubmit, watch } = useForm<FormType>();

  const handleResetForm = () => {
    const defaultValues = selectedVariables.reduce((acc, variable) => {
      const { name, subOptions } = variable;
      acc[name] = { isSelected: true, ...(subOptions ? { subOptions } : {}) };
      return acc;
    }, {} as FormType);
    reset(defaultValues);
  };

  const handleCancel = () => {
    handleResetForm();
    onCancel();
  };

  useEffect(() => {
    handleResetForm();
  }, [JSON.stringify(selectedVariables ?? [])]);

  const variables = watch();
  const selectedWatchVariables = Object.values(variables).filter((value) => value.isSelected);
  const isSubmitDisabled = selectedWatchVariables.length < 1;

  const isVariableDisabled = (variable: HarvestVariable) =>
    selectedWatchVariables.length >= maxSelected && !variables[variable]?.isSelected;

  const onSubmit = handleSubmit((data: FormType) => {
    if (isSubmitDisabled) return;
    const formattedData: HarvestVariablesType[] = [];

    Object.entries(data).forEach(([key, value]) => {
      if (value.isSelected) {
        formattedData.push({ name: key as HarvestVariable, subOptions: value.subOptions });
      }
    });

    updatePondDetailsPreferences(formattedData);
  });

  return (
    <DialogContent data-cy='select-variables-modal' borderRadius='2xl' boxShadow='elevation.400'>
      <chakra.form
        onSubmit={(event) => {
          event.stopPropagation();
          onSubmit(event).then();
        }}
        display='contents'
        noValidate
      >
        <DialogHeader px='md' pt='md' pb='2lg'>
          <DialogTitle
            display='flex'
            gap={{ base: 'xs-alt', md: 'xl' }}
            flexDirection={{ base: 'column', md: 'row' }}
            minW={{ base: 'auto', md: '370px', lg: '423px' }}
            justifyContent={{ base: 'initial', md: 'space-between' }}
          >
            <Box>
              <Heading size='heavy300'>{trans('t_customize_view')}</Heading>
              <Text size='light300' mt='xs' color='text.gray.weak'>
                {trans('t_select_up_to_x_variables_you_want', { count: maxSelected })}
              </Text>
            </Box>

            <Text size='heavy300' alignSelf='flex-end'>
              {trans('t_selected', { selected: selectedWatchVariables.length, total: maxSelected })}
            </Text>
          </DialogTitle>
        </DialogHeader>

        <DialogBody px='md' pt={0} pb={0}>
          {harvestViewVariables.map(({ title, items }, i) => {
            const isFirstElement = i === 0;
            const isLastElement = i !== harvestViewVariables.length - 1;

            return (
              <Box
                key={i}
                py='lg'
                borderBottom={isLastElement && '1px solid'}
                borderColor='border.gray'
                {...(isFirstElement && { pt: '2sm' })}
              >
                <Box textStyle='label.200' mb='md'>
                  {title}
                </Box>

                <Flex flexDir='column' gap='md'>
                  {items.map((viewVariable) => {
                    return (
                      <>
                        <Controller
                          name={`${viewVariable.value}.isSelected`}
                          control={control}
                          render={({ field: { value, onChange, ...rest } }) => {
                            return (
                              <BaseFormCheckbox
                                {...rest}
                                id={`${viewVariable.value}.isSelected`}
                                label={viewVariable.label}
                                formControlProps={{
                                  w: '100%'
                                }}
                                disabled={isVariableDisabled(viewVariable.value)}
                                checked={value}
                                onCheckedChange={({ checked }) => onChange(checked)}
                              />
                            );
                          }}
                        />

                        {viewVariable.options?.length && (
                          <Box ps='lg'>
                            <Controller
                              name={`${viewVariable.value}.subOptions`}
                              control={control}
                              render={({ field: { name, value, onChange } }) => {
                                return (
                                  <RadioGroup
                                    name={name}
                                    value={value}
                                    onValueChange={(v) => onChange(v.value)}
                                    disabled={!variables[viewVariable.value]?.isSelected}
                                  >
                                    <Stack>
                                      {viewVariable.options.map(({ label, value }) => (
                                        <Radio key={value} value={value}>
                                          {label}
                                        </Radio>
                                      ))}
                                    </Stack>
                                  </RadioGroup>
                                );
                              }}
                            />
                          </Box>
                        )}
                      </>
                    );
                  })}
                </Flex>
              </Box>
            );
          })}
        </DialogBody>

        <DialogFooter gap='sm-alt' py='sm-alt' px='md' shadow='elevation.200' roundedBottom='2xl'>
          <BaseButton
            analyticsId={actionsName.keyVariablesCancelClicked}
            variant='secondary'
            w='max-content'
            onClick={handleCancel}
            disabled={isUpdating}
          >
            {trans('t_cancel')}
          </BaseButton>
          <BaseButton
            analyticsId={actionsName.keyVariablesSaveClicked}
            type='submit'
            w='max-content'
            loading={isUpdating}
            disabled={isSubmitDisabled}
          >
            {trans('t_apply')}
          </BaseButton>
        </DialogFooter>
      </chakra.form>
    </DialogContent>
  );
}
