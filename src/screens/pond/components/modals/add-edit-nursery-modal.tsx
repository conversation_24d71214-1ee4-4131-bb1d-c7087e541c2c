import { Box, BoxProps, chakra, Flex, Stack, Text, useDisclosure } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { BaseFormInput } from '@components/form/base-form-input';
import { BaseFormNumberInput } from '@components/form/base-form-number-input';
import { yupResolver } from '@hookform/resolvers/yup';
import { getTrans } from '@i18n/get-trans';
import { useCreateNurseryApi } from '@screens/nursery/hooks/use-create-nursery-api';
import { useUpdateNurseryApi } from '@screens/nursery/hooks/use-update-nursery-api';
import { actionsName } from '@utils/segment';
import { numberTransform, useYupSchema, Yup, YupResolverType } from '@utils/yup';
import { v1PondSetApi } from '@xpertsea/module-farm-sdk';
import { ReactNode, useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/router';
import { goToUrl } from '@utils/url';
import { slugify } from '@utils/string';
import { useListUsersApi } from '@screens/user/hooks/use-list-users-api';
import { FormControlReactSelect } from '@components/form/form-control-react-select';
import { DialogBody, DialogContent, DialogFooter, DialogHeader, DialogRoot, DialogTitle } from '@components/ui/dialog';

interface AddEditNurseryModalProps {
  farmId: string;
  nurseryId?: string;
  children: ReactNode;
  defaultValues?: v1PondSetApi;
  nurseryName?: string;
  buttonProps?: BoxProps;
}

export function AddEditNurseryModal(props: AddEditNurseryModalProps) {
  const { farmId, nurseryId, children, defaultValues, nurseryName, buttonProps = {} } = props;

  const { query, reload } = useRouter();
  const { farmEid } = query as { farmEid: string };

  const { trans } = getTrans();

  const nameRegex = /^[A-Z0-9\\-]{1,15}$/;

  const { schema } = useYupSchema({
    name: Yup.string()
      .required()
      .label('t_name')
      .transform((value) => value.toUpperCase())
      .matches(nameRegex, trans('t_nursery_name_validation_msg')),
    size: Yup.number().transform(numberTransform).nullable().moreThan(0).label('t_nursery_size').required(),
    superVisorId: Yup.string().nullable()
  });

  const { onOpen, onClose, open } = useDisclosure();

  const [{ isLoading: isCreating }, createNursery] = useCreateNurseryApi();
  const [{ isLoading: isUpdating }, updateNursery] = useUpdateNurseryApi();

  type UpdateFormValues = Parameters<typeof updateNursery>[0]['params']['set'];
  type CreateFormValues = Parameters<typeof createNursery>[0]['params']['record'];
  const {
    reset,
    register,
    handleSubmit,
    control,
    formState: { errors }
  } = useForm<CreateFormValues | UpdateFormValues>({
    resolver: yupResolver(schema) as YupResolverType<CreateFormValues | UpdateFormValues>,
    defaultValues
  });

  const [{ data: userListRes, isLoading: isUserLoading }, listUsers] = useListUsersApi();
  const { users = [], userMembershipsMap } = userListRes ?? {};

  useEffect(() => {
    if (!open) return;
    listUsers({ params: { page: { size: 1000, current: 1 }, filter: { entityId: [farmId] } } });
  }, [open, farmId]);

  const handleModalClose = useCallback(() => {
    onClose();
  }, [onClose]);

  const handleModalOpen = () => {
    onOpen();
    reset(defaultValues);
  };

  const onReload = ({ nurseryEid, name }: { nurseryEid: number; name: string }) => {
    goToUrl({
      route: '/farm/[farmEid]/nursery/[nurseryEid]',
      params: { farmEid, nurseryEid: slugify(`${nurseryEid}-${name}`) }
    });
  };

  const onSubmit = useCallback(
    handleSubmit((data: CreateFormValues | UpdateFormValues) => {
      if (nurseryId) {
        updateNursery({
          params: {
            filter: { nurseryId },
            set: data as UpdateFormValues
          },
          successCallback: () => {
            handleModalClose();
            reload();
          }
        });
      } else {
        createNursery({
          params: { record: { farmId, ...(data as CreateFormValues) } },
          successCallback: (nurseryRes) => {
            const { nursery } = nurseryRes;
            const { eid: nurseryEid, name } = nursery;

            handleModalClose();
            onReload({ nurseryEid, name });
          }
        });
      }
    }),
    [nurseryId, farmId]
  );

  return (
    <DialogRoot
      open={open}
      onOpenChange={(e) => {
        e.open ? handleModalOpen() : handleModalClose();
      }}
      size='md'
      restoreFocus={false}
    >
      <Box
        onClick={(e) => {
          e.stopPropagation();
          handleModalOpen();
        }}
        {...buttonProps}
      >
        {children}
      </Box>

      <DialogContent overflow='visible' data-cy='add-edit-nursery-modal' rounded='2xl' bg='bg.gray.medium'>
        <chakra.form onSubmit={onSubmit} display='contents' noValidate>
          {!nurseryId && (
            <DialogHeader py='lg'>
              <DialogTitle>{trans('t_add_nursery_pond')}</DialogTitle>
            </DialogHeader>
          )}
          {nurseryId && (
            <DialogHeader>
              <DialogTitle>{trans('t_edit_nursery')}</DialogTitle>
              <Text fontSize='2xl' fontWeight={500}>
                {defaultValues.name}
              </Text>
            </DialogHeader>
          )}

          <DialogBody py='xs-alt'>
            <Stack gap='sm-alt' bg='white' p='md' rounded='xl'>
              <Flex
                justify='space-between'
                align='center'
                borderBottom='0.5px solid'
                borderColor='border.gray.weak'
                pb='sm-alt'
              >
                <Text size='label200'>{trans('t_name')}</Text>
                <BaseFormInput
                  id='name'
                  autoFocus={true}
                  error={errors?.name?.message}
                  required
                  formControlProps={{ w: '124px' }}
                  bg='bg.gray.medium'
                  placeholder={trans('t_enter')}
                  {...register('name')}
                />
              </Flex>
              <Flex
                justify='space-between'
                align='center'
                borderBottom='0.5px solid'
                borderColor='border.gray.weak'
                pb='sm-alt'
              >
                <Text size='label200'>{trans('t_size')}</Text>
                <BaseFormNumberInput
                  name='size'
                  control={control}
                  required
                  w='124px'
                  numericFormatProps={{ decimalScale: 2, placeholder: trans('t_enter') }}
                  inputRightElement={
                    <Text as='span' size='label200'>
                      {trans('t_ha')}
                    </Text>
                  }
                />
              </Flex>
              <Flex justify='space-between' align='center' pb='sm-alt'>
                <Text size='label200'>{trans('t_supervisor_optional')}</Text>
                <Box w='200px' justifyItems='flex-end'>
                  <FormControlReactSelect
                    isOptional
                    id='superVisorId'
                    isClearable={true}
                    error={errors?.superVisorId?.message}
                    control={control}
                    placeholder={`${trans('t_select')}...`}
                    formControlProps={{ w: '188px', h: '40px' }}
                    isLoading={isUserLoading}
                    options={users
                      .filter(
                        (user) =>
                          !userMembershipsMap[user?._id]?.isDeactivated &&
                          !userMembershipsMap[user?._id]?.metadata?.hideInUserList
                      )
                      .map((e) => {
                        return { value: e._id, label: e.email || e.logInPhoneNumber || `${e.firstName} ${e.lastName}` };
                      })}
                  />
                </Box>
              </Flex>
            </Stack>
          </DialogBody>
          <DialogFooter py='lg'>
            <BaseButton
              type='submit'
              loading={isUpdating || isCreating}
              color='white'
              analyticsId={nurseryId ? actionsName.editPondSaveClicked : actionsName.addPondSaveClicked}
              analyticsData={{ nurseryId, nurseryName }}
            >
              {nurseryId ? trans('t_update') : trans('t_create')}
            </BaseButton>
          </DialogFooter>
        </chakra.form>
      </DialogContent>
    </DialogRoot>
  );
}
