import { Box, Flex, Text, useDisclosure } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { BaseFormCheckbox } from '@components/form/base-form-checkbox';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { useYupSchema, Yup, yupFormResolver, YupResolverType } from '@utils/yup';
import { ReactNode } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { IndicatorOption, PondCardViewType } from '@components/pond-card/helper';
import { ChevronDownFilled } from '@icons/chevron-down/chevron-down-filled';
import { DialogBody, DialogContent, DialogHeader, DialogRoot, DialogTitle } from '@components/ui/dialog';
import { NativeSelectField, NativeSelectRoot } from '@components/ui/native-select';
import { useGetFarmSummaryVariables } from '@screens/farm-summary/hooks/use-get-farm-summary-variables';
import { usePermission } from '@hooks/use-permission';
import { VisionOnlyGuard } from '@components/permission-wrappers/vision-only-guard';
import { UseExportPondData } from '@screens/pond/components/modals/hooks/use-export-pond-data';
import { FarmSummaryViewVariable } from '@screens/farm-summary/helpers/pond-list';

type ExportPondDataModalPropsType = {
  children: ReactNode;
  selectedIndicatingStatus: IndicatorOption;
};
export function ExportPondDataModal(props: ExportPondDataModalPropsType) {
  const { selectedIndicatingStatus, children } = props;
  const { trans } = getTrans();
  const { open, onOpen, onClose } = useDisclosure();

  return (
    <DialogRoot
      open={open}
      onOpenChange={(e) => {
        e.open ? onOpen() : onClose();
      }}
      size='xl'
      placement='center'
    >
      <Box onClick={onOpen}>{children}</Box>

      <DialogContent rounded='2xl' bg='bg.gray.medium' p='lg'>
        <DialogHeader p='0' mb='2lg'>
          <DialogTitle>{trans('t_export_farm_summary_table')}</DialogTitle>
        </DialogHeader>

        <DialogBody p='0'>
          <Form onCancel={onClose} selectedIndicatingStatus={selectedIndicatingStatus} />
        </DialogBody>
      </DialogContent>
    </DialogRoot>
  );
}

type FormValues = { viewType: string; farmOverviewData?: boolean };

type FormProps = {
  onCancel: () => void;
  selectedIndicatingStatus: IndicatorOption;
};

function Form(props: FormProps) {
  const { selectedIndicatingStatus, onCancel } = props;
  const { trans } = getTrans();

  const ponds = useAppSelector((state) => state.farm?.currentFarmPonds);
  const farmId = useAppSelector((state) => state.farm?.currentFarm?._id);

  const isAdmin = usePermission({ role: 'admin', entity: 'farm', entityId: farmId });
  const isSupervisor = usePermission({ role: 'supervisor', entity: 'farm', entityId: farmId });

  const pondViewVariables = useGetFarmSummaryVariables({ isAdmin, isSupervisor });

  const allPondVariables = Object.values(pondViewVariables).reduce((agg, ele) => {
    ele.variables.forEach((el) => agg.push(el.value));
    return agg;
  }, [] as FarmSummaryViewVariable[]);

  const viewTypeOptions = [
    { label: trans('t_current_view'), value: 'current' },
    { label: trans('t_expected_at_harvest'), value: 'harvest' },
    { label: trans('t_all_data'), value: 'all' }
  ];

  const { schema } = useYupSchema({
    viewType: Yup.string().label('t_primary_feed_brand'),
    farmOverviewData: Yup.boolean().label('t_add_farm_overview_data')
  });

  const { register, control, handleSubmit, watch } = useForm<FormValues>({
    resolver: yupFormResolver(schema) as YupResolverType<FormValues>,
    defaultValues: { viewType: viewTypeOptions[0].value }
  });

  const viewTypeWatch = watch('viewType');

  const { exportData } = UseExportPondData({
    ponds,
    viewVariables: allPondVariables,
    selectedIndicatingStatus
  });

  const onSubmit = handleSubmit((data: FormValues) => {
    const viewType = data.viewType as PondCardViewType;
    exportData({ profitProjectionView: viewType, includeFarmSummary: data.farmOverviewData });

    onCancel();
  });

  return (
    <form onSubmit={onSubmit}>
      <Flex flexDirection='column' p='md' bg='white' rounded='2xl' mb='2lg' gap='sm-alt'>
        <VisionOnlyGuard>
          <Flex borderBottom='0.5px solid' borderColor='gray.200' justify='space-between' align='center'>
            <Text size='label200'>{trans('t_select_a_view')}</Text>
            <Box>
              <NativeSelectRoot
                w='188px'
                rounded='lg'
                bg='gray.100'
                fontSize='md'
                textStyle='label.200'
                borderColor='gray.400'
                icon={<ChevronDownFilled color='text.gray' hasBackground={false} />}
              >
                <NativeSelectField {...register('viewType')} items={viewTypeOptions} />
              </NativeSelectRoot>
            </Box>
          </Flex>
        </VisionOnlyGuard>

        {viewTypeWatch === 'current' && (
          <Box py='sm-alt'>
            <Controller
              name='farmOverviewData'
              control={control}
              render={({ field: { value, onChange, ...rest } }) => {
                return (
                  <BaseFormCheckbox
                    {...rest}
                    id='farmOverviewData'
                    label={trans('t_add_farm_overview_data')}
                    formControlProps={{ w: '100%' }}
                    checked={value}
                    onCheckedChange={({ checked }) => onChange(checked)}
                  />
                );
              }}
            />
          </Box>
        )}
        <Text size='label200' borderTop='0.5px solid' borderColor='gray.200' py='sm-alt'>
          {trans('t_your_data_exported_in_xlsx')}
        </Text>
      </Flex>

      <Flex justify='flex-end' gap='sm-alt'>
        <BaseButton onClick={onCancel} variant='secondary'>
          {trans('t_cancel')}
        </BaseButton>

        <BaseButton type='submit'>{trans('t_export')}</BaseButton>
      </Flex>
    </form>
  );
}
