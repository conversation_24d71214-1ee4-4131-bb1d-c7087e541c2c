import { Box, BoxProps, chakra, Flex, Stack, Text, useDisclosure } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { BaseFormInput } from '@components/form/base-form-input';
import { BaseFormNumberInput } from '@components/form/base-form-number-input';
import { FormControlReactSelect } from '@components/form/form-control-react-select';
import { yupResolver } from '@hookform/resolvers/yup';
import { getTrans } from '@i18n/get-trans';
import {
  ReloadCurrentFarmPondsParams,
  useReloadCurrentFarmPonds
} from '@screens/farm/hooks/use-reload-current-farm-ponds';
import { useCreatePondApi } from '@screens/pond/hooks/use-create-pond-api';
import { useReloadCurrentPond } from '@screens/pond/hooks/use-reload-current-pond';
import { useUpdatePondApi } from '@screens/pond/hooks/use-update-pond-api';
import { useListUsersApi } from '@screens/user/hooks/use-list-users-api';
import { actionsName } from '@utils/segment';
import { numberTransform, useYupSchema, Yup, YupResolverType } from '@utils/yup';
import { v1PondSetApi } from '@xpertsea/module-farm-sdk';
import { ReactNode, useCallback, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/router';
import { goToUrl } from '@utils/url';
import { slugify } from '@utils/string';
import { DialogBody, DialogContent, DialogFooter, DialogHeader, DialogRoot, DialogTitle } from '@components/ui/dialog';

interface AddEditPondModalProps {
  farmId: string;
  pondId?: string;
  children: ReactNode;
  defaultValues?: v1PondSetApi;
  pondName?: string;
  isInPondView?: boolean;
  isInPondListView?: boolean;
  buttonProps?: BoxProps;
}

export function AddEditPondModal(props: AddEditPondModalProps) {
  const { farmId, pondId, children, defaultValues, pondName, isInPondView, isInPondListView, buttonProps = {} } = props;

  const { reloadCurrentPond } = useReloadCurrentPond();
  const { reloadCurrentFarmPonds } = useReloadCurrentFarmPonds();
  const { query } = useRouter();
  const { farmEid } = query as { farmEid: string };

  const { trans } = getTrans();

  const nameRegex = /^[A-Z0-9\\-]{1,15}$/;

  const { schema } = useYupSchema({
    name: Yup.string()
      .required()
      .label('t_name')
      .transform((value) => value.toUpperCase())
      .matches(nameRegex, trans('t_pond_name_validation_msg')),
    size: Yup.number().transform(numberTransform).nullable().moreThan(0).label('t_pond_size').required(),
    superVisorId: Yup.string().nullable()
  });

  const { onOpen, onClose, open } = useDisclosure();

  const [{ isLoading: isCreating }, createPond] = useCreatePondApi();
  const [{ isLoading: isUpdating }, updatePond] = useUpdatePondApi();
  const [{ data: userListRes, isLoading: isUserLoading }, listUsers] = useListUsersApi();

  type UpdateFormValues = Parameters<typeof updatePond>[0]['params']['set'];
  type CreateFormValues = Parameters<typeof createPond>[0]['params']['record'];

  const {
    reset,
    register,
    handleSubmit,
    control,
    formState: { errors }
  } = useForm<CreateFormValues | UpdateFormValues>({
    resolver: yupResolver(schema) as YupResolverType<CreateFormValues | UpdateFormValues>,
    defaultValues
  });

  const { users = [], userMembershipsMap } = userListRes ?? {};

  useEffect(() => {
    if (!open) return;

    listUsers({
      params: {
        page: {
          size: 1000,
          current: 1
        },
        filter: { entityId: [farmId] }
      }
    });
  }, [open, farmId]);

  const handleModalClose = () => {
    onClose();
  };

  const handleModalOpen = () => {
    onOpen();
    reset(defaultValues);
  };

  const onReload = (successCallback?: ReloadCurrentFarmPondsParams['successCallback']) => {
    if (isInPondView) return reloadCurrentPond();
    if (isInPondListView) return reloadCurrentFarmPonds({ successCallback });
  };

  const onSubmit = useCallback(
    handleSubmit((data: CreateFormValues | UpdateFormValues) => {
      if (pondId) {
        updatePond({
          params: {
            filter: { pondId },
            set: data as UpdateFormValues
          },
          successCallback: () => {
            handleModalClose();
            onReload();
          }
        });
      } else {
        createPond({
          params: { record: { farmId, ...(data as CreateFormValues) } },
          successCallback: (pondRes) => {
            const { pond } = pondRes;
            const { eid: pondEid, name } = pond;

            handleModalClose();
            onReload(() => {
              goToUrl({
                route: '/farm/[farmEid]/pond/[pondEid]',
                params: { farmEid, pondEid: slugify(`${pondEid}-${name}`) }
              });
            });
          }
        });
      }
    }),
    [pondId, farmId]
  );

  return (
    <DialogRoot
      open={open}
      onOpenChange={(e) => {
        e.open ? handleModalOpen() : handleModalClose();
      }}
      restoreFocus={false}
      size='md'
    >
      <Box
        onClick={(e) => {
          e.stopPropagation();
          handleModalOpen();
        }}
        {...buttonProps}
      >
        {children}
      </Box>

      <DialogContent overflow='visible' data-cy='add-edit-pond-modal' rounded='2xl' bg='bg.gray.medium'>
        <chakra.form display='contents' onSubmit={onSubmit} noValidate>
          {!pondId && (
            <DialogHeader>
              <DialogTitle>{trans('t_add_pond')}</DialogTitle>
            </DialogHeader>
          )}
          {pondId && (
            <DialogHeader>
              <DialogTitle mb='sm' display='flex' alignItems='center' gap='sm-alt'>
                {trans('t_edit_pond')}
                <Text as='span' size='label100'>
                  {defaultValues.name}
                </Text>
              </DialogTitle>
            </DialogHeader>
          )}
          <DialogBody display='flex' flexDirection='column' gap='md'>
            <Stack gap='sm-alt' bg='white' p='md' rounded='xl'>
              <Flex
                justify='space-between'
                align='center'
                borderBottom='0.5px solid'
                borderColor='border.gray.weak'
                pb='sm-alt'
              >
                <Text size='label200'>{trans('t_name')}</Text>
                <BaseFormInput
                  id='name'
                  autoFocus={true}
                  error={errors?.name?.message}
                  required
                  formControlProps={{ w: '124px' }}
                  bg='bg.gray.medium'
                  placeholder={trans('t_enter')}
                  {...register('name')}
                />
              </Flex>
              <Flex
                justify='space-between'
                align='center'
                borderBottom='0.5px solid'
                borderColor='border.gray.weak'
                pb='sm-alt'
              >
                <Text size='label200'>{trans('t_size')}</Text>
                <BaseFormNumberInput
                  name='size'
                  control={control}
                  required
                  w='124px'
                  numericFormatProps={{ decimalScale: 2, placeholder: trans('t_enter') }}
                  inputRightElement={
                    <Text as='span' size='label200'>
                      {trans('t_ha')}
                    </Text>
                  }
                />
              </Flex>
              <Flex justify='space-between' align='center' pb='sm-alt'>
                <Text size='label200'>{trans('t_supervisor_optional')}</Text>
                <Box w='200px' justifyItems='flex-end'>
                  <FormControlReactSelect
                    isOptional
                    id='superVisorId'
                    isClearable={true}
                    error={errors?.superVisorId?.message}
                    control={control}
                    placeholder={`${trans('t_select')}...`}
                    formControlProps={{ w: '188px', h: '40px' }}
                    isLoading={isUserLoading}
                    options={users
                      .filter(
                        (user) =>
                          !userMembershipsMap[user?._id]?.isDeactivated &&
                          !userMembershipsMap[user?._id]?.metadata?.hideInUserList
                      )
                      .map((e) => {
                        return { value: e._id, label: e.email || e.logInPhoneNumber || `${e.firstName} ${e.lastName}` };
                      })}
                  />
                </Box>
              </Flex>
            </Stack>
          </DialogBody>
          <DialogFooter>
            <BaseButton
              size='sm'
              variant='secondary'
              me='md'
              onClick={handleModalClose}
              data-cy='cancel-btn'
              analyticsId={pondId ? actionsName.editPondCancelClicked : actionsName.addPondCancelClicked}
              analyticsData={{ pondId, pondName }}
            >
              {trans('t_cancel')}
            </BaseButton>
            <BaseButton
              size='sm'
              type='submit'
              loading={isUpdating || isCreating}
              analyticsId={pondId ? actionsName.editPondSaveClicked : actionsName.addPondSaveClicked}
              analyticsData={{ pondId, pondName }}
            >
              {pondId ? trans('t_update') : trans('t_create')}
            </BaseButton>
          </DialogFooter>
        </chakra.form>
      </DialogContent>
    </DialogRoot>
  );
}
