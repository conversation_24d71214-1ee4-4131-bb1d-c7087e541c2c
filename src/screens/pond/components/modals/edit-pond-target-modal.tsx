import { Box, Flex, useDisclosure } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { getTrans } from '@i18n/get-trans';
import { setCurrentPopulationAction } from '@redux/farm';
import { useAppDispatch, useAppSelector } from '@redux/hooks';
import { useUpdatePopulationApi } from '@screens/population/hooks/use-update-population-api';
import { numberTransform, useYupSchema, Yup, yupFormResolver, YupResolverType } from '@utils/yup';
import { ReactNode } from 'react';
import { useForm } from 'react-hook-form';
import { StockFormValues, TargetFormFields } from '@screens/pond/components/modals/target-form-fields';
import { actionsName } from '@utils/segment';
import isUndefined from 'lodash/isUndefined';
import { DialogBody, DialogContent, DialogHeader, DialogRoot, DialogTitle } from '@components/ui/dialog';

type EditPondTargetModalPropsType = { children: ReactNode };

export function EditPondTargetModal(props: EditPondTargetModalPropsType) {
  const { children } = props;
  const { trans } = getTrans();
  const dispatch = useAppDispatch();
  const { open, onOpen, onClose } = useDisclosure();
  const currentPopulation = useAppSelector((state) => state.farm?.currentPopulation);
  const { _id: populationId, cycleInformation, seedingQuantity } = currentPopulation ?? {};
  const { target } = cycleInformation ?? {};
  const { harvestWeight, cycleLength, fcr, biomassToHarvest, survival, type } = target ?? {};
  const [{ isLoading: isUpdating }, updatePopulation] = useUpdatePopulationApi();

  const defaultValues: StockFormValues = {
    cycleInformation: {
      target: {
        harvestWeight,
        cycleLength,
        fcr,
        biomassToHarvest,
        survival: survival ? survival * 100 : 0,
        type: type ?? 'biomassToHarvest'
      }
    }
  };

  const onSubmit = (value: StockFormValues) => {
    const { cycleInformation: formCycleInformation } = value;
    const { target } = formCycleInformation;
    const { survival, ...restTarget } = target;
    const finalTarget = { ...restTarget, survival: survival / 100 };
    updatePopulation({
      params: {
        filter: { populationId },
        set: {
          cycleInformation: {
            ...cycleInformation,
            target: finalTarget
          }
        }
      },
      successCallback: () => {
        dispatch(
          setCurrentPopulationAction({
            ...currentPopulation,
            cycleInformation: { ...cycleInformation, target: finalTarget }
          })
        );
        onClose();
      }
    });
  };

  return (
    <DialogRoot
      open={open}
      onOpenChange={(e) => {
        e.open ? onOpen() : onClose();
      }}
      restoreFocus={false}
    >
      <Box onClick={onOpen}>{children}</Box>

      <DialogContent rounded='2xl' closeTriggerProps={{ top: '4px' }}>
        <DialogHeader p='sm-alt' borderBottom='0.5px solid' borderColor='border.gray'>
          <DialogTitle> {trans('t_targets')}</DialogTitle>
        </DialogHeader>
        <DialogBody p='sm-alt'>
          <Form
            onClose={onClose}
            onSubmit={onSubmit}
            defaultValues={defaultValues}
            isLoading={isUpdating}
            seedingQuantity={seedingQuantity}
          />
        </DialogBody>
      </DialogContent>
    </DialogRoot>
  );
}

type FormPropsType = {
  defaultValues?: StockFormValues;
  onClose: () => void;
  isLoading?: boolean;
  onSubmit: (value: StockFormValues) => void;
  seedingQuantity: number;
};

function Form(props: FormPropsType) {
  const { defaultValues = {}, onClose, onSubmit, isLoading: isUpdating, seedingQuantity } = props;
  const { trans } = getTrans();

  const user = useAppSelector((state) => state.auth.user);
  const currentPond = useAppSelector((state) => state.farm?.currentPond);

  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);

  const defaultMinValue = 0;
  const defaultMaxValue = 100;

  const { schema } = useYupSchema({
    cycleInformation: Yup.object().shape({
      target: Yup.object().shape({
        harvestWeight: Yup.number()
          .transform(numberTransform)
          .min(defaultMinValue, trans('t_min_x', { min: defaultMinValue }))
          .max(defaultMaxValue, trans('t_max_x', { max: defaultMaxValue }))
          .nullable()
          .required(trans('t_required'))
          .label('t_harvest_weight_g'),
        cycleLength: Yup.number()
          .transform(numberTransform)
          .min(defaultMinValue, trans('t_min_x', { min: defaultMinValue }))
          .max(200, trans('t_max_x', { max: 200 }))
          .nullable()
          .required(trans('t_required'))
          .label('t_cycle_length'),
        type: Yup.string().required(trans('t_required')).label('t_cycle_type'),
        biomassToHarvest: Yup.number()
          .transform(numberTransform)
          .positive()
          .nullable()
          .required(trans('t_required'))
          .label(isBiomassUnitLbs ? 't_biomass_lb_ha' : 't_biomass_kg_ha'),
        survival: Yup.number()
          .transform(numberTransform)
          .min(defaultMinValue, trans('t_min_x', { min: defaultMinValue }))
          .max(defaultMaxValue, trans('t_max_x', { max: defaultMaxValue }))
          .nullable()
          .required(trans('t_required'))
          .label('t_survival'),
        fcr: Yup.number()
          .transform(numberTransform)
          .min(defaultMinValue, trans('t_min_x', { min: defaultMinValue }))
          .max(5, trans('t_max_x', { max: 5 }))
          .nullable()
          .required(trans('t_required'))
          .label('t_fcr')
      })
    })
  });

  const { watch, setValue, handleSubmit, control } = useForm<StockFormValues>({
    resolver: yupFormResolver(schema) as YupResolverType<StockFormValues>,
    defaultValues
  });

  const onHandleSubmit = handleSubmit((data: StockFormValues) => {
    onSubmit(data);
  });

  return (
    <form onSubmit={onHandleSubmit}>
      <TargetFormFields
        watch={watch}
        setValue={setValue}
        control={control}
        pondSize={currentPond?.size}
        seedingQuantity={seedingQuantity}
      />

      <Flex justify='flex-end' gap='sm-alt' mt='lg'>
        <BaseButton
          size='sm'
          variant='link'
          onClick={onClose}
          color='gray.700'
          analyticsId={actionsName.pondOverviewTargetSaveClicked}
        >
          {trans('t_cancel')}
        </BaseButton>
        <BaseButton
          loading={isUpdating}
          type='submit'
          size='sm'
          analyticsId={actionsName.pondOverviewTargetCancelClicked}
        >
          {trans('t_save_changes')}
        </BaseButton>
      </Flex>
    </form>
  );
}
