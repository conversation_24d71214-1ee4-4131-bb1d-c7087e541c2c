import { Control, Controller, UseFormSetValue, UseFormWatch } from 'react-hook-form';
import { getTrans } from '@i18n/get-trans';
import { useEffect } from 'react';
import { Box, Flex } from '@chakra-ui/react';
import { BaseFormNumberInput } from '@components/form/base-form-number-input';
import {
  formContainerStyles,
  inputStyles,
  labelStyles
} from '@screens/pond/components/modals/helpers/chakra-form-styles';
import { EnumPopulationCycleInformationTargetType } from '@xpertsea/module-farm-sdk';
import { gramsInPound } from '@utils/constants';
import { useAppSelector } from '@redux/hooks';
import isUndefined from 'lodash/isUndefined';
import { Radio, RadioGroup } from '@components/ui/radio';

export type StockFormValues = {
  cycle?: number;
  stockedAt?: Date;
  seedingAverageWeight?: number;
  seedingQuantity?: number;
  dryDaysBeforeStocking?: number;
  stockingCostsMillar?: number;
  hatcheryName?: string;
  geneticName?: string;
  cycleInformation: {
    carryingCapacity?: number;
    target: {
      harvestWeight: number;
      cycleLength: number;
      biomassToHarvest: number;
      type: EnumPopulationCycleInformationTargetType;
      survival: number;
      fcr: number;
    };
    equipment?: {
      numberOfAutoFeeders?: number;
      autoFeederBrand?: string;
      autoFeederProtocol?: string;
      numberOfAerators?: number;
      feedBrand?: string;
      aeratorType?: string;
    };
    projection?: {
      projectedFeed?: {
        farmFeedTypeId?: string;
        farmFeedTableId?: string;
      };
    };
  };
};

interface TargetFormFieldsProps {
  control: Control<StockFormValues>;
  setValue: UseFormSetValue<StockFormValues>;
  watch: UseFormWatch<StockFormValues>;
  pondSize: number;
  seedingQuantity?: number;
}

export function TargetFormFields(props: TargetFormFieldsProps) {
  const { control, watch, setValue, pondSize, seedingQuantity } = props;

  const { trans } = getTrans();
  const user = useAppSelector((state) => state.auth.user);

  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);

  const targetType = watch('cycleInformation.target.type');
  const biomassToHarvest = watch('cycleInformation.target.biomassToHarvest');
  const harvestWeight = watch('cycleInformation.target.harvestWeight');
  const harvestSurvival = watch('cycleInformation.target.survival');
  const stockingDensity = watch('seedingQuantity') ?? seedingQuantity;
  const isBiomassToHarvest = targetType === 'biomassToHarvest';

  useEffect(() => {
    if (!harvestWeight || !pondSize || !stockingDensity) return;
    if (!biomassToHarvest && !harvestSurvival) return;
    const harvestWeightLbs = harvestWeight / gramsInPound;
    const stockingDensityOverHa = stockingDensity / pondSize;
    if (isBiomassToHarvest) {
      const animalsHarvestedPerHa = biomassToHarvest / harvestWeightLbs;
      const survival = animalsHarvestedPerHa / stockingDensityOverHa;
      setValue('cycleInformation.target.survival', survival * 100);
    } else {
      const survivalValue = harvestSurvival / 100;
      const animalsHarvestedPerHa = survivalValue * stockingDensityOverHa;
      const biomassToHarvest = animalsHarvestedPerHa * harvestWeightLbs;
      setValue('cycleInformation.target.biomassToHarvest', biomassToHarvest);
    }
  }, [targetType, biomassToHarvest, harvestWeight, pondSize, stockingDensity, harvestSurvival]);

  return (
    <Flex direction='column' gap='xs-alt' data-cy='target-form'>
      <BaseFormNumberInput
        name='cycleInformation.target.harvestWeight'
        required
        control={control}
        label={trans('t_harvest_weight_g')}
        inputProps={{ autoFocus: true, ...inputStyles }}
        numericFormatProps={{ decimalScale: 1, fixedDecimalScale: true }}
        formLabelProps={labelStyles}
        inputContainerProps={{ w: 'auto' }}
        {...formContainerStyles}
      />

      <BaseFormNumberInput
        required
        control={control}
        name='cycleInformation.target.cycleLength'
        label={trans('t_cycle_length')}
        inputContainerProps={{ w: 'auto' }}
        {...formContainerStyles}
        inputProps={inputStyles}
        formLabelProps={labelStyles}
        numericFormatProps={{ decimalScale: 0, fixedDecimalScale: true, suffix: ` ${trans('t_day_s')}` }}
      />

      <Controller
        name='cycleInformation.target.type'
        control={control}
        render={({ field: { name, value, onChange } }) => {
          return (
            <RadioGroup size='sm' name={name} value={value} onValueChange={(e) => onChange(e.value)}>
              <Flex direction='column' gap='xs-alt'>
                <Box {...formContainerStyles}>
                  <Radio w='100%' value='biomassToHarvest'>
                    {isBiomassUnitLbs ? trans('t_biomass_lb_ha') : trans('t_biomass_kg_ha')}
                  </Radio>
                  <BaseFormNumberInput
                    required
                    disabled={!isBiomassToHarvest}
                    control={control}
                    name='cycleInformation.target.biomassToHarvest'
                    w='max-content'
                    inputProps={{ ...inputStyles, _disabled: { border: 'none' } }}
                    formLabelProps={labelStyles}
                    numericFormatProps={{
                      decimalScale: 0,
                      fixedDecimalScale: true,
                      suffix: ` ${isBiomassUnitLbs ? trans('t_lb_over_ha') : trans('t_kg_over_ha')}`
                    }}
                  />
                </Box>

                <Box {...formContainerStyles}>
                  <Radio w='100%' value='survival'>
                    {trans('t_survival')}
                  </Radio>

                  <BaseFormNumberInput
                    required
                    disabled={isBiomassToHarvest}
                    control={control}
                    name='cycleInformation.target.survival'
                    w='max-content'
                    inputProps={{ ...inputStyles, _disabled: { border: 'none' } }}
                    formLabelProps={labelStyles}
                    numericFormatProps={{ decimalScale: 0, fixedDecimalScale: true, suffix: '%' }}
                  />
                </Box>
              </Flex>
            </RadioGroup>
          );
        }}
      />

      <BaseFormNumberInput
        control={control}
        name='cycleInformation.target.fcr'
        required
        {...formContainerStyles}
        inputContainerProps={{ w: 'auto' }}
        inputProps={inputStyles}
        formLabelProps={labelStyles}
        label={trans('t_fcr')}
        numericFormatProps={{ decimalScale: 2, fixedDecimalScale: true }}
      />
    </Flex>
  );
}
