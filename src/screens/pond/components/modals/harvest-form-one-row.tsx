import { Box, Flex, FlexProps, Text, TextProps } from '@chakra-ui/react';

interface HarvestFormOneRowProps extends FlexProps {
  label: string;
  subTitle?: string;
  subTitleProps?: TextProps;
}

export function HarvestFormOneRow(props: HarvestFormOneRowProps) {
  const { label, children, subTitle, subTitleProps, ...rest } = props;

  return (
    <Flex
      pb='sm-alt'
      alignItems='center'
      borderBottom='0.5px solid {colors.border.gray.weak}'
      justify='space-between'
      {...rest}
    >
      <Box>
        <Text size='label200'>{label}</Text>
        {subTitle && (
          <Text size='label300' maxW='201px' mt='sm-alt' color='text.gray.weak' {...subTitleProps}>
            {subTitle}
          </Text>
        )}
      </Box>

      <Box>{children}</Box>
    </Flex>
  );
}
