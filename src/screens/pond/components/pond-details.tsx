import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { useRouter } from 'next/router';
import { goToUrl } from '@utils/url';
import { Flex, Tabs, Text } from '@chakra-ui/react';
import { PondHarvestedAlertBar } from '@screens/pond/components/pond-harvested-alert-bar';
import { HtmlHead } from '@components/html/html-head';
import { MonitoringListTableWrapper } from '@screens/monitoring/components/monitoring-list-table-wrapper';
import { PondOverview } from '@screens/pond/components/pond-overview/pond-overview';
import { PassedPlansAlerts } from '@screens/pond/components/alerts/past-plans-alerts';
import { dataTabs, DataTabsType, isDataTab, visionOnlyTabs } from '@screens/pond/helpers/pond-details-tabs';
import { PondFeed } from '@screens/pond/components/pond-feed-data/pond-feed';
import { PondCycleInformation } from '@screens/pond/components/pond-cycle-information';
import { PondStocking } from '@screens/pond/components/pond-stocking';
import { PondHarvests } from '@screens/pond/components/pond-harvests-tab/pond-harvests';
import { useAnalytics } from '@hooks/use-analytics';
import { actionsName } from '@utils/segment';
import { PondLinksBar } from '@screens/pond/components/pond-links-bar';
import { PondTransferHistory } from '@screens/pond/components/pond-transfer-history';
import { PondDailyParams } from '@screens/pond/components/pond-daily-params';
import { HarvestPlan } from '@screens/pond/components/harvest-plan/harvest-plan';
import { SandboxTab } from './sandbox/sandbox-tab';
import { useEnableTechnicalFeatures } from '@hooks/use-is-xpertsea-user';
import { VisionOnlyGuard } from '@components/permission-wrappers/vision-only-guard';

export function PondDetails({ isInPondView }: { isInPondView?: boolean }) {
  const { trans } = getTrans();

  const { query } = useRouter();
  const { tab } = query;

  const currentPond = useAppSelector((state) => state.farm.currentPond);
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);
  const { name: pondName } = currentPond ?? {};
  const { name: farmName } = currentFarm ?? {};

  const enableTechnicalFeatures = useEnableTechnicalFeatures();

  const titleMap = {
    overview: `${trans('t_overview')} | ${pondName ?? trans('t_pond')} | ${farmName ?? trans('t_farm')}`,
    harvestPlan: `${trans('t_harvest_plan')} | ${pondName ?? trans('t_pond')} | ${farmName ?? trans('t_farm')}`,
    data: `${trans('t_data')} | ${pondName ?? trans('t_pond')} | ${farmName ?? trans('t_farm')}`,
    sandbox: `${trans('t_sandbox')} | ${pondName ?? trans('t_pond')} | ${farmName ?? trans('t_farm')}`
  };

  const isValidDataTab = isDataTab(tab as DataTabsType);
  const isOverviewTab = !tab || (!isValidDataTab && tab !== 'plan' && tab !== 'sandbox');

  return (
    <Flex direction='column' gap='md-alt' data-cy='pond-view-data'>
      <PassedPlansAlerts />

      <PondLinksBar isInPondView={isInPondView} />

      <PondHarvestedAlertBar isInPondView={isInPondView} />

      {isOverviewTab && (
        <>
          <HtmlHead title={titleMap.overview} description={titleMap.overview} />
          <PondOverview />
        </>
      )}

      <VisionOnlyGuard>
        {tab === 'plan' && (
          <>
            <HtmlHead title={titleMap.harvestPlan} description={titleMap.harvestPlan} />
            <HarvestPlan />
          </>
        )}
      </VisionOnlyGuard>

      {isValidDataTab && (
        <>
          <HtmlHead title={titleMap.data} description={titleMap.data} />
          <DataTabs tab={tab as DataTabsType} />
        </>
      )}
      <VisionOnlyGuard>
        {tab === 'sandbox' && enableTechnicalFeatures && (
          <>
            <HtmlHead title={titleMap.sandbox} description={titleMap.sandbox} />
            <SandboxTab />
          </>
        )}
      </VisionOnlyGuard>
    </Flex>
  );
}

function DataTabs({ tab }: { tab: DataTabsType }) {
  const { trans } = getTrans();
  const { query, route } = useRouter();

  const currentFarm = useAppSelector((state) => state.farm?.currentFarm);
  const pond = useAppSelector((state) => state.farm?.currentPond);
  const { name: pondName } = pond ?? {};
  const { metadata, name: farmName } = currentFarm ?? {};

  const isVisionOnly = metadata?.productOffering === 'visionOnly';

  const dataTabsInfo: Record<DataTabsType, { title: string; 'data-cy'?: string; query?: Record<string, unknown> }> = {
    feed: { title: trans('t_feed'), 'data-cy': 'feed-tab' },
    monitoring: { title: trans('t_monitoring'), 'data-cy': 'monitoring-tab', query: { size: 500 } },
    cycleInformation: { title: trans('t_cycle_info'), 'data-cy': 'cycle-information-tab' },
    stocking: { title: trans('t_stocking'), 'data-cy': 'stocking-tab' },
    harvest: { title: trans('t_harvest'), 'data-cy': 'harvest-tab' },
    transferHistory: { title: trans('t_transfer_history'), 'data-cy': 'transfer-history-tab' },
    dailyParams: { title: trans('t_daily_parameters'), 'data-cy': 'daily-params-tab' }
  };

  const { trackAction } = useAnalytics();
  const selectedStyles = {
    position: 'relative',
    color: 'white',
    bgColor: 'bg.squidInkPowder.down',
    _before: { h: 0 }
  };
  const actualTabs = isVisionOnly ? visionOnlyTabs : dataTabs;
  const isValidTab = actualTabs.includes(tab);
  const titleMap = {
    feed: `${trans('t_feed')} | ${trans('t_data')} | ${pondName ?? trans('t_pond')} | ${farmName ?? trans('t_farm')}`,
    stocking: `${trans('t_stocking')} | ${trans('t_data')} | ${pondName ?? trans('t_pond')} | ${farmName ?? trans('t_farm')}`,
    monitoring: `${trans('t_monitoring')} | ${trans('t_data')} | ${pondName ?? trans('t_pond')} | ${farmName ?? trans('t_farm')}`,
    transferHistory: `${trans('t_transfer_history')} | ${trans('t_data')} | ${pondName ?? trans('t_pond')} | ${farmName ?? trans('t_farm')}`,
    dailyParams: `${trans('t_daily_parameters')} | ${trans('t_data')} | ${pondName ?? trans('t_pond')} | ${farmName ?? trans('t_farm')}`,
    cycleInfo: `${trans('t_cycle_info')} | ${trans('t_data')} | ${pondName ?? trans('t_pond')} | ${farmName ?? trans('t_farm')}`,
    harvest: `${trans('t_harvest')} | ${trans('t_data')} | ${pondName ?? trans('t_pond')} | ${farmName ?? trans('t_farm')}`
  };

  return (
    <Tabs.Root
      pos='relative'
      value={isValidTab ? tab : actualTabs[0]}
      onValueChange={(e) => {
        const tab = e.value;
        trackAction(actionsName.ponOverviewDataTabClicked, { tab, addCurrentPopulation: true }).then();
        goToUrl({
          route,
          params: { ...query, page: undefined, tab }
        });
      }}
      data-cy='pond-view-tabs'
    >
      <Tabs.List
        alignItems='flex-end'
        mb={{ base: '2xs', lg: 0 }}
        ms={{ base: 'xs', lg: 'lg' }}
        flexWrap='wrap'
        borderBottomWidth='0'
        gap='sm-alt'
      >
        {actualTabs.map((tab, index) => {
          const tabInfo = dataTabsInfo[tab];
          return (
            <Tabs.Trigger
              value={tab}
              key={index}
              display='flex'
              color='text.gray'
              transition='none'
              alignItems='center'
              bg='bg.gray.strong'
              borderTopRadius='2xl'
              borderBottomRadius={{ base: '2xl', lg: 'none' }}
              py={0}
              px='2lg'
              h='32px'
              data-cy={tabInfo?.['data-cy']}
              _hover={selectedStyles}
              _selected={selectedStyles}
            >
              <Text size='label100' as='span' lineHeight='20px'>
                {tabInfo?.title}
              </Text>
            </Tabs.Trigger>
          );
        })}
      </Tabs.List>
      <Tabs.Content value='feed' px={0} pb='2xl-alt' pt={0}>
        <>
          <HtmlHead title={titleMap.feed} description={titleMap.feed} />
          <PondFeed />
        </>
      </Tabs.Content>
      <Tabs.Content value='monitoring' px={0} pb='2xl-alt' pt={0}>
        <>
          <HtmlHead title={titleMap.monitoring} description={titleMap.monitoring} />
          <MonitoringListTableWrapper />
        </>
      </Tabs.Content>
      <Tabs.Content value='stocking' px={0} pb='2xl-alt' pt={0}>
        <>
          <HtmlHead title={titleMap.stocking} description={titleMap.stocking} />
          <PondStocking />
        </>
      </Tabs.Content>

      <Tabs.Content value='transferHistory' px={0} pb='2xl-alt' pt={0}>
        <>
          <HtmlHead title={titleMap.transferHistory} description={titleMap.transferHistory} />
          <PondTransferHistory />
        </>
      </Tabs.Content>

      <Tabs.Content value='dailyParams' px={0} pb='2xl-alt' pt={0}>
        <>
          <HtmlHead title={titleMap.dailyParams} description={titleMap.dailyParams} />
          <PondDailyParams />
        </>
      </Tabs.Content>

      <Tabs.Content value='cycleInformation' px={0} pb='2xl-alt' pt={0}>
        <>
          <HtmlHead title={titleMap.cycleInfo} description={titleMap.cycleInfo} />
          <PondCycleInformation />
        </>
      </Tabs.Content>
      <Tabs.Content value='harvest' px={0} pb='2xl-alt' pt={0}>
        <>
          <HtmlHead title={titleMap.harvest} description={titleMap.harvest} />
          <PondHarvests />
        </>
      </Tabs.Content>
    </Tabs.Root>
  );
}
