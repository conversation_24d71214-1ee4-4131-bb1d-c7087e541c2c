import { WeekDays } from '@components/form/form-days-picker';
import { FarmFeedTypes, Population } from '@xpertsea/module-farm-sdk';
import keyBy from 'lodash/keyBy';
import { DateTime } from 'luxon';
import { DeepPartial } from 'react-hook-form';

function getStartOfWeek(date: string, startDay: string) {
  const daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

  const dateIso = DateTime.fromISO(date, {
    locale: 'en-US'
  });
  const currentIndex = daysOfWeek.indexOf(dateIso.weekdayLong?.toLowerCase());
  const startIndex = daysOfWeek.indexOf(startDay);
  let diffDays = currentIndex - startIndex - 1;
  if (diffDays < 0) {
    diffDays += 7;
  }

  return dateIso.minus({ days: diffDays });
}

type FeedDataType = DeepPartial<Population['feedData']>;

export type WeeksType = {
  start: DateTime;
  end: DateTime;
  weekDays: string[];
}[];

export function getStartAndEndOfWeeks(startDate: string, dayOfWeek: WeekDays): WeeksType {
  const dateIso = DateTime.fromISO(startDate).endOf('day');
  const daysOfWeek: WeekDays[] = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
  const endDayIndex = daysOfWeek.indexOf(dayOfWeek);
  if (endDayIndex === -1) throw new Error('Invalid day of the week');

  const weeks = [];
  let currentDate = DateTime.now().endOf('day');
  const currentDayIndex = currentDate.weekday % 7;

  if (endDayIndex !== currentDayIndex) {
    const daysToSubtract =
      currentDayIndex > endDayIndex ? currentDayIndex - endDayIndex - 7 : 7 + currentDayIndex - endDayIndex;
    currentDate = currentDate.minus({ days: daysToSubtract });
  }

  if (currentDate < dateIso) {
    return [];
  }

  while (currentDate > dateIso) {
    const startOfWeek = currentDate.minus({ days: 6 });
    weeks.push({
      start: startOfWeek,
      end: currentDate,
      weekDays: getWeekDaysArray(startOfWeek)
    });
    currentDate = currentDate.minus({ weeks: 1 });
  }

  return weeks;
}

function getWeekDaysArray(startOfWeek: DateTime) {
  const days = [];
  for (let i = 0; i < 7; i++) {
    days.push(startOfWeek.plus({ days: i }).toFormat('yyyy-MM-dd'));
  }
  return days;
}

function mergeFeeds(feeds: FeedDataType[0]['feed'], newFeeds: FeedDataType[0]['feed']): FeedDataType[0]['feed'] {
  if (!feeds?.length && !newFeeds?.length) return [];
  if (!feeds?.length) return newFeeds;
  if (!newFeeds?.length) return feeds;

  const feedsHashmap = keyBy(feeds, 'feedTypeId');
  newFeeds.forEach((feed) => {
    const { totalLbs, feedTypeId } = feed;
    const existingFeed = feedsHashmap[feedTypeId];

    if (existingFeed) {
      feedsHashmap[feedTypeId] = { ...existingFeed, totalLbs: existingFeed.totalLbs + totalLbs };
    } else {
      feedsHashmap[feedTypeId] = feed;
    }
  });
  return Object.values(feedsHashmap);
}

export function groupFeedDataByWeek(feedData: FeedDataType, startDate: string): Record<string, FeedDataType[0]> {
  if (!feedData?.length) return {};
  return feedData.reduce(
    (agg, ele) => {
      const monday = getStartOfWeek(ele.date, startDate);
      const formattedMonday = monday.toFormat('yyyy-MM-dd');
      if (agg[formattedMonday]) {
        const { feed, otherDirectCost, totalLbs = 0 } = agg[formattedMonday];
        agg[formattedMonday] = {
          otherDirectCost: otherDirectCost ? otherDirectCost + (ele.otherDirectCost || 0) : ele.otherDirectCost,
          totalLbs: totalLbs + ele.totalLbs,
          feed: mergeFeeds(feed, ele.feed)
        };
      } else {
        agg[formattedMonday] = ele;
      }

      return agg;
    },
    {} as Record<string, FeedDataType[0]>
  );
}

function getAvailableWeekDays({ date, earliestPossibleDay }: { date: string; earliestPossibleDay: string }) {
  const startDate = DateTime.fromISO(date).startOf('day');
  const todayDate = DateTime.now().startOf('day');
  const earliestPossibleDate = DateTime.fromISO(earliestPossibleDay).startOf('day');

  const availableWeekDays: string[] = [];

  for (let i = 0; i < 7; i++) {
    const currentDate = startDate.plus({ days: i });

    if (currentDate.diff(earliestPossibleDate, ['days', 'hours']).days < 0) continue;
    if (todayDate.diff(currentDate, ['days', 'hours']).days < 0) break;
    availableWeekDays.push(currentDate.toISO());
  }

  return availableWeekDays;
}

export function convertFeedDataToDailyFromWeekly({ data, stockedAt }: { data: FeedDataType; stockedAt: string }) {
  const results: FeedDataType = [];

  data.forEach((weekInfo) => {
    const { totalLbs: weeklyTotalLbs, otherDirectCost: weeklyOtherDirectCost, feed: weeklyFeeds } = weekInfo;

    const availableWeekDays: string[] = getAvailableWeekDays({ date: weekInfo.date, earliestPossibleDay: stockedAt });

    const numberOfAvailableDays = availableWeekDays.length;
    availableWeekDays.forEach((date) => {
      results.push({
        date: DateTime.fromISO(date).toFormat('yyyy-MM-dd'),
        totalLbs: weeklyTotalLbs / numberOfAvailableDays || undefined,
        otherDirectCost: weeklyOtherDirectCost / numberOfAvailableDays || undefined,
        feed: weeklyFeeds.map(({ feedTypeId, totalLbs }) => ({
          feedTypeId,
          totalLbs: totalLbs / numberOfAvailableDays || undefined
        }))
      });
    });
  });

  return results;
}

export function getFeedTypesIncludingInactiveWithValues({
  feedTypes,
  feedData
}: {
  feedTypes: FarmFeedTypes[];
  feedData: Record<string, FeedDataType[0]>;
}) {
  const activeFeedType = feedTypes?.filter((ele) => ele.status === 'active') ?? [];
  if (!feedData) return activeFeedType;

  const feedDataValues = Object.values(feedData);

  if (!feedDataValues?.length) return activeFeedType;

  try {
    const feedTypesHashmap = keyBy(feedTypes, '_id');
    const inActiveIds = feedTypes?.filter((ele) => ele.status === 'inactive').map((ele) => ele._id);

    const { inactiveWithValues } = Object.values(feedData).reduce(
      (agg, ele) => {
        const { feed } = ele;
        feed.forEach((f) => {
          if (agg.foundIds.includes(f.feedTypeId)) return agg;

          if (f.totalLbs && inActiveIds.includes(f.feedTypeId)) {
            agg.foundIds.push(f.feedTypeId);
            agg.inactiveWithValues.push(feedTypesHashmap[f.feedTypeId]);
          }
        });
        return agg;
      },
      { inactiveWithValues: [], foundIds: [] }
    );

    return activeFeedType.concat(inactiveWithValues);
  } catch (e: unknown) {
    return activeFeedType;
  }
}
