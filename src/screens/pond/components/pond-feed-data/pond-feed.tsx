import { Box, Flex, Table, TableCellProps, Text } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { ReactNode, useMemo, useState } from 'react';
import {
  convertFeedDataToDailyFromWeekly,
  getFeedTypesIncludingInactiveWithValues,
  getStartAndEndOfWeeks,
  groupFeedDataByWeek,
  WeeksType
} from '@screens/pond/components/pond-feed-data/feed-data-helpers';
import { EmptyState } from '@components/errors/empty-state';
import { FarmFeedTypes, Population } from '@xpertsea/module-farm-sdk';
import { useYupSchema, Yup, YupResolverType } from '@utils/yup';
import { DeepPartial, useFieldArray, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { BaseFormNumberInput } from '@components/form/base-form-number-input';
import { useUpdatePopulationApi } from '@screens/population/hooks/use-update-population-api';
import { useReloadCurrentPond } from '@screens/pond/hooks/use-reload-current-pond';
import { convertKgToPounds, convertPoundsToKg, formatNumber, isNumber } from '@utils/number';
import { useSetFarmBookRefererInRedux } from '@components/farm-book/hooks/use-set-farm-book-referer-in-redux';
import { actionsName } from '@utils/segment';
import { useAnalytics } from '@hooks/use-analytics';
import { AdminOrSupervisorWrapper } from '@components/permission-wrappers/admin-or-supervisor-wrapper';
import { AlertRowContainer } from '@components/alert/alert-row-container';
import { ClearNightSolid } from '@icons/clear-night/clear-night-solid';
import { ArrowBigRight } from '@icons/arrow-big-right/arrow-big-right';
import { useEnableTechnicalFeatures } from '@hooks/use-is-xpertsea-user';
import { BaseLink } from '@components/base/base-link';
import { useRouter } from 'next/router';
import { EditOutlinedIcon } from '@icons/edit/edit-outlined-icon';
import { BaseIconButton } from '@components/base/base-icon-button';
import { TableContainer } from '@components/ui/table-container';

export function PondFeed() {
  const { trans } = getTrans();
  useSetFarmBookRefererInRedux('feed');
  const { currentPopulation, currentFarm } = useAppSelector((state) => state.farm);
  const weekDay = currentFarm?.otherConfig?.startOfWeek?.day ?? 'monday';
  const weeks = getStartAndEndOfWeeks(currentPopulation?.stockedAt, weekDay);

  if (!weeks?.length) return <EmptyState minH='300px' bgColor='white' />;

  if (!currentFarm?.feedTypes?.length) {
    return <EmptyState minH='300px' bgColor='white' description={trans('t_no_feed_types_available')} />;
  }

  return <PondFeedWrapper weeks={[...weeks].reverse()} />;
}

function PondFeedWrapper({ weeks }: { weeks: WeeksType }) {
  const { trans } = getTrans();
  const { currentPopulation, currentFarm } = useAppSelector((state) => state.farm);
  const { feedData, estimatedFeed } = currentPopulation ?? {};
  const [isEditing, setIsEditing] = useState(false);
  const { query } = useRouter();
  const { farmEid } = query;

  const enableTechnicalFeatures = useEnableTechnicalFeatures();

  const formattedFeedData = groupFeedDataByWeek(feedData, currentFarm?.otherConfig?.startOfWeek?.day ?? 'monday');

  const hasEstimatedValues = !!Object.values(estimatedFeed ?? {}).length;

  const activeFeedTypes = getFeedTypesIncludingInactiveWithValues({
    feedData: formattedFeedData,
    feedTypes: currentFarm?.feedTypes
  });

  const isHarvested = !!currentPopulation?.harvest?.date;

  const filteredWeeks = useMemo(() => {
    if (!isHarvested) return weeks;
    return weeks.filter(({ weekDays }) => {
      const { otherDirectCost, feed } = formattedFeedData[weekDays?.[0]] ?? {};
      return otherDirectCost || feed?.length;
    });
  }, [weeks, formattedFeedData, isHarvested]);

  if (!filteredWeeks?.length) return <EmptyState minH='300px' bgColor='white' />;

  return (
    <Box bgColor='white' p='md' rounded='2xl'>
      <Flex justify='space-between' align='center' mb='md-alt' mx='2xs'>
        <Box>
          <Text size='label100'>{trans('t_feed_data')}</Text>
          {hasEstimatedValues && (
            <Text mt='sm-alt' size='label300' color='text.gray.disabled'>
              {trans('t_using_imputed_values')}
            </Text>
          )}
        </Box>

        {!isEditing && (
          <BaseIconButton
            analyticsId={actionsName.feedDataEditClicked}
            aria-label='edit-pond-feed'
            onClick={() => setIsEditing((v) => !v)}
          >
            <EditOutlinedIcon />
          </BaseIconButton>
        )}
      </Flex>

      {isEditing ? (
        <>
          {enableTechnicalFeatures && (
            <AlertRowContainer status='info' mb='md'>
              <ClearNightSolid color='icon.brandBlue' />
              <Text size={{ base: 'light300', md: 'light200' }}>{trans('t_edit_daily_feed_msg')}</Text>

              <BaseLink route='/farm/[farmEid]/data-updater' params={{ farmEid, view: 'dailyData' }}>
                <BaseButton variant='link' size='sm'>
                  {trans('t_data_updater')} <ArrowBigRight color='button.link' boxSize='20px' />
                </BaseButton>
              </BaseLink>
            </AlertRowContainer>
          )}

          <Form
            weeks={filteredWeeks}
            feedTypes={activeFeedTypes}
            feedData={formattedFeedData}
            onCancel={() => setIsEditing(false)}
            estimatedFeed={estimatedFeed}
          />
        </>
      ) : (
        <TableContent
          weeks={filteredWeeks}
          feedTypes={activeFeedTypes}
          feedData={formattedFeedData}
          estimatedFeed={estimatedFeed}
        />
      )}
    </Box>
  );
}

type TableContentProps = {
  weeks: WeeksType;
  feedTypes: FarmFeedTypes[];
  feedData: ReturnType<typeof groupFeedDataByWeek>;
  estimatedFeed: Population['estimatedFeed'];
};

function TableContent(props: TableContentProps) {
  const { weeks, feedTypes, feedData, estimatedFeed } = props;
  const { trans } = getTrans();
  const lang = useAppSelector((state) => state.app.lang);
  return (
    <FeedTableContainer feedTypes={feedTypes}>
      <Table.Body>
        {weeks.map((week, i) => {
          const { start: startOfWeek, end: endOfWeek, weekDays } = week;
          const isLastItem = i === weeks.length - 1;

          const { totalLbs, otherDirectCost, feed } = feedData[startOfWeek.toFormat('yyyy-MM-dd')] ?? {};

          const tdProps: TableCellProps = {
            py: 'md',
            ...(isLastItem && { paddingBottom: 'none' })
          };

          const estimatedValues = weekDays?.some((day) => !!estimatedFeed?.[day]);

          return (
            <Table.Row
              key={i}
              borderBottom='0.5px solid'
              borderBottomColor='gray.400'
              {...(isLastItem && { borderBottom: 'none' })}
            >
              <Table.Cell textAlign='center' {...tdProps}>
                <Text size='label200'>
                  {startOfWeek.toFormat('MMM dd', { locale: lang })} - {endOfWeek.toFormat('MMM dd', { locale: lang })}
                  {estimatedValues ? ' *' : ''}
                </Text>
              </Table.Cell>

              {feedTypes?.map(({ _id, status }) => {
                const fd = feed?.find(({ feedTypeId }) => feedTypeId === _id);
                return (
                  <Table.Cell key={_id} textAlign='center' {...tdProps}>
                    <Text size='label200' color={status === 'active' ? 'text.gray' : 'text.gray.disabled'}>
                      {fd
                        ? `${formatNumber(convertPoundsToKg(fd.totalLbs), { lang, fractionDigits: 0 })} ${trans('t_kg')}`
                        : '-'}
                    </Text>
                  </Table.Cell>
                );
              })}

              <Table.Cell textAlign='center' {...tdProps}>
                <Text size='label200'>
                  {totalLbs
                    ? `${formatNumber(convertPoundsToKg(totalLbs), { lang, fractionDigits: 0 })} ${trans('t_kg')}`
                    : '-'}
                </Text>
              </Table.Cell>
              <AdminOrSupervisorWrapper>
                <Table.Cell textAlign='center' {...tdProps}>
                  <Text size='label200'>
                    {isNumber(otherDirectCost)
                      ? formatNumber(otherDirectCost, { lang, fractionDigits: 2, isCurrency: true })
                      : '-'}
                  </Text>
                </Table.Cell>
              </AdminOrSupervisorWrapper>
            </Table.Row>
          );
        })}
      </Table.Body>
    </FeedTableContainer>
  );
}

type FormValues = {
  weeks: {
    date: string;
    dateString: string;
    otherDirectCost: number;
    totalLbs: number;
    feed: { totalLbs: number; feedTypeId: string }[];
  }[];
};

interface FormProps {
  weeks: WeeksType;
  feedTypes: FarmFeedTypes[];
  feedData: ReturnType<typeof groupFeedDataByWeek>;
  onCancel: () => void;
  estimatedFeed: Population['estimatedFeed'];
}

function Form(props: FormProps) {
  const { weeks, feedTypes, feedData, onCancel, estimatedFeed } = props;
  const lang = useAppSelector((state) => state.app.lang);
  const { currentPopulation } = useAppSelector((state) => state.farm);
  const { reloadCurrentPond } = useReloadCurrentPond();
  const { trackAction } = useAnalytics();
  const { trans } = getTrans();
  const [{ isLoading }, updatePopulation] = useUpdatePopulationApi();

  const { schema } = useYupSchema({
    test: Yup.array().of(
      Yup.object().shape({
        otherDirectCost: Yup.number().label('t_other_direct_costs'),
        totalLbs: Yup.number().label('t_total'),
        feed: Yup.array().of(
          Yup.object().shape({
            totalLbs: Yup.number(),
            feedTypeId: Yup.string()
          })
        )
      })
    )
  });

  const {
    control,
    handleSubmit,
    formState: { errors, dirtyFields }
  } = useForm<FormValues>({
    resolver: yupResolver(schema) as YupResolverType<FormValues>,
    defaultValues: {
      weeks: weeks.map((week) => {
        const { start: startOfWeek, end: endOfWeek, weekDays } = week;

        const { otherDirectCost, feed, totalLbs } = feedData[startOfWeek.toFormat('yyyy-MM-dd')] ?? {};
        const estimatedValues = weekDays?.some((day) => !!estimatedFeed?.[day]);

        return {
          date: startOfWeek.toISO(),
          dateString: `${startOfWeek.toFormat('MMM dd', { locale: lang })} - ${endOfWeek.toFormat('MMM dd', { locale: lang })}${estimatedValues ? ' *' : ''}`,
          otherDirectCost,
          totalLbs: totalLbs ? convertPoundsToKg(totalLbs) : totalLbs,
          feed: feedTypes.map((f) => {
            const feedTotalLbs = feed?.find((ele) => ele.feedTypeId === f._id)?.totalLbs;
            return { feedTypeId: f._id, totalLbs: feedTotalLbs ? convertPoundsToKg(feedTotalLbs) : feedTotalLbs };
          })
        };
      })
    }
  });

  const { fields } = useFieldArray({ control, name: 'weeks' });

  const onSubmit = handleSubmit((data: FormValues) => {
    const filteredWeeklyData = data.weeks.reduce(
      (agg, weekData, currentIndex) => {
        const dirtyWeek = dirtyFields?.weeks?.[currentIndex];

        if (!dirtyWeek) return agg;

        const filteredFeed = weekData.feed.filter((f) => f.totalLbs);
        if (filteredFeed?.length || weekData.otherDirectCost) {
          const filteredFeedInLbs = filteredFeed.map((ele) => ({ ...ele, totalLbs: convertKgToPounds(ele.totalLbs) }));
          const totalLbs = filteredFeedInLbs.reduce((agg, ele) => agg + ele.totalLbs, 0);
          agg.push({
            ...weekData,
            totalLbs,
            feed: filteredFeedInLbs,
            otherDirectCost: weekData.otherDirectCost || undefined
          });
        }
        return agg;
      },
      [] as DeepPartial<Population['feedData']>
    );

    updatePopulation({
      params: {
        filter: { populationId: currentPopulation._id },
        set: {
          feedData: convertFeedDataToDailyFromWeekly({
            data: filteredWeeklyData,
            stockedAt: currentPopulation.stockedAt
          })
        }
      },
      successCallback: () => {
        trackAction(actionsName.feedDataSubmitted).then();
        reloadCurrentPond(onCancel);
      }
    });
  });

  return (
    <form onSubmit={onSubmit}>
      <FeedTableContainer feedTypes={feedTypes}>
        <Table.Body>
          {fields.map((field, i) => {
            const isLastItem = i === fields.length - 1;
            const tdProps: TableCellProps = {
              py: 'md',
              textAlign: 'center',
              ...(isLastItem && { paddingBottom: 'none' })
            };
            const feeds: { totalLbs: number }[] = field['feed'];

            return (
              <Table.Row
                key={i}
                borderBottom='0.5px solid'
                borderBottomColor='gray.400'
                {...(isLastItem && { borderBottom: 'none' })}
              >
                <Table.Cell {...tdProps}>
                  <Text size='label200'>{field['dateString']}</Text>
                </Table.Cell>
                {feeds.map((_, index) => (
                  <Table.Cell key={index} {...tdProps}>
                    <BaseFormNumberInput
                      display='flex'
                      flexDir='column'
                      alignItems='center'
                      justifyContent='center'
                      name={`weeks.${i}.feed.${index}.totalLbs`}
                      control={control}
                      error={errors?.weeks?.[i]?.feed?.[index]?.message}
                      inputProps={{
                        w: '80px',
                        size: 'sm',
                        rounded: 'base',
                        textAlign: 'center',
                        borderWidth: '0.5px',
                        borderColor: 'gray.700',
                        _hover: { borderWidth: '0.5px' }
                      }}
                      numericFormatProps={{ decimalScale: 0 }}
                    />
                  </Table.Cell>
                ))}
                <Table.Cell {...tdProps}>
                  <Text size='label200'>
                    {field['totalLbs']
                      ? `${formatNumber(field['totalLbs'], { lang, fractionDigits: 0 })} ${trans('t_kg')}`
                      : '-'}
                  </Text>
                </Table.Cell>
                <AdminOrSupervisorWrapper>
                  <Table.Cell {...tdProps}>
                    <BaseFormNumberInput
                      display='flex'
                      flexDir='column'
                      alignItems='center'
                      justifyContent='center'
                      name={`weeks.${i}.otherDirectCost`}
                      control={control}
                      numericFormatProps={{ decimalScale: 2 }}
                      error={errors?.weeks?.[i]?.otherDirectCost?.message}
                      inputProps={{
                        w: '80px',
                        size: 'sm',
                        textAlign: 'center',
                        rounded: 'base',
                        borderWidth: '0.5px',
                        borderColor: 'gray.700',
                        _hover: { borderWidth: '0.5px' }
                      }}
                    />
                  </Table.Cell>
                </AdminOrSupervisorWrapper>
              </Table.Row>
            );
          })}
        </Table.Body>
      </FeedTableContainer>

      <Flex justify='flex-end' gap='sm-alt' mt='md'>
        <BaseButton
          analyticsId={actionsName.feedDataEditCancelClicked}
          variant='link'
          onClick={onCancel}
          disabled={isLoading}
          fontWeight={500}
          color='gray.700'
        >
          {trans('t_cancel')}
        </BaseButton>
        <BaseButton analyticsId={actionsName.feedDataEditSaveClicked} type='submit' loading={isLoading} size='sm'>
          {trans('t_save_changes')}
        </BaseButton>
      </Flex>
    </form>
  );
}

function FeedTableContainer({ feedTypes, children }: { feedTypes: FarmFeedTypes[]; children: ReactNode }) {
  const { trans } = getTrans();

  const width = feedTypes.length + 3 / 100;

  return (
    <TableContainer>
      <Table.Root>
        <Table.Header>
          <Table.Row border='none'>
            <TableHeaderCell title={trans('t_weeks')} width={width} />
            {feedTypes?.map(({ _id, feedType }) => (
              <TableHeaderCell key={_id} title={` ${feedType}`} width={width} />
            ))}
            <TableHeaderCell title={trans('t_total')} width={width} />
            <AdminOrSupervisorWrapper>
              <TableHeaderCell title={trans('t_other_direct_costs')} width={width} />
            </AdminOrSupervisorWrapper>
          </Table.Row>
        </Table.Header>
        {children}
      </Table.Root>
    </TableContainer>
  );
}

function TableHeaderCell({ title, width }: { title: string; width: number }) {
  return (
    <Table.ColumnHeader border='none' textAlign='center' p='0' w={`${width}%`}>
      <Text bgColor='gray.100' py='sm' borderRadius='xl' mx='2xs' size='label200'>
        {title}
      </Text>
    </Table.ColumnHeader>
  );
}
