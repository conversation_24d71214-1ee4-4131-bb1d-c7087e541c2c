import { useAppSelector } from '@redux/hooks';
import { getTrans } from '@i18n/get-trans';
import { Box, Flex, Text } from '@chakra-ui/react';
import { getPondState } from '@screens/pond/helpers/get-pond-state';
import { BaseButton } from '@components/base/base-button';
import { AddPondStockingModal } from '@screens/pond/components/modals/add-edit-pond-stocking/add-pond-stocking-modal';
import { HideOnMobileView } from '@components/mobile-view/hide-on-mobile-view';
import { ArrowReverseLineIcon } from '@icons/arrow-reverse/arrow-reverse-line-icon';
import { actionsName } from '@utils/segment';
import { VisionOnlyGuard } from '@components/permission-wrappers/vision-only-guard';
import { VisionOnlyAccess } from '@components/permission-wrappers/vision-only-access';
import { VisionOnlyAddEditPondStockingModal } from '@screens/pond/components/modals/add-edit-pond-stocking/vision-only-add-edit-pond-stocking-modal';

export function PondHarvestedAlertBar({ isInPondView }: { isInPondView?: boolean }) {
  const population = useAppSelector((state) => state.farm.currentPopulation);
  const pond = useAppSelector((state) => state.farm.currentPond);

  const { name: pondName, _id: pondId, size: pondSize } = pond ?? {};
  const { history } = population ?? {};
  const { isHarvested } = getPondState({ population });

  const { trans } = getTrans();
  if (!population?.harvest?.date) return null;

  return (
    <Flex
      data-cy='empty-pond'
      bg='bg.gray.strong'
      p='sm-alt'
      rounded='xl'
      gap='sm-alt'
      justify='space-between'
      flexWrap='wrap'
      align='center'
    >
      <Flex align='flex-start' gap='sm-alt'>
        <ArrowReverseLineIcon />

        <Box>
          <Text size='label100'>{trans('t_past_cycle')}</Text>
          <Text whiteSpace='normal'>{trans('t_data_from_previous_cycle')}</Text>
        </Box>
      </Flex>
      <HideOnMobileView>
        <VisionOnlyAccess>
          <VisionOnlyAddEditPondStockingModal
            pondId={pondId}
            pondName={pondName}
            pondSize={pondSize}
            isHarvested={isHarvested}
            firstMonitoring={history?.[history.length - 1]}
            isInPondView={isInPondView}
            containerProps={{ ms: 'auto' }}
          >
            <BaseButton
              size='sm'
              px='sm'
              data-cy='stock-pond-btn'
              analyticsId={actionsName.stockPondClicked}
              analyticsData={{ source: 'pond details harvested alert bar stock pond - vision only' }}
            >
              {trans('t_stock_pond')}
            </BaseButton>
          </VisionOnlyAddEditPondStockingModal>
        </VisionOnlyAccess>
        <VisionOnlyGuard>
          <AddPondStockingModal
            pondId={pondId}
            pondName={pondName}
            pondSize={pondSize}
            isHarvested={isHarvested}
            firstMonitoring={history?.[history.length - 1]}
            isInPondView={isInPondView}
            containerProps={{ ms: 'auto' }}
          >
            <BaseButton
              size='sm'
              px='sm'
              data-cy='stock-pond-btn'
              analyticsId={actionsName.stockPondClicked}
              analyticsData={{ source: 'pond details harvested alert bar stock pond' }}
            >
              {trans('t_stock_pond')}
            </BaseButton>
          </AddPondStockingModal>
        </VisionOnlyGuard>
      </HideOnMobileView>
    </Flex>
  );
}
