import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { headlessSizesLookup, headonSizesLookup } from '@screens/settings/constants/default-price-list';
import { BoxProps, Flex, Table, Text, TextProps } from '@chakra-ui/react';
import { useRef } from 'react';
import { convertUnitByDivision, convertUnitByMultiplication, formatNumber, isNumber } from '@utils/number';
import { TableContainer } from '@components/ui/table-container';
import { AdminOnlyWrapper } from '@components/permission-wrappers/admin-only-wrapper';
import { UserPreferences } from '@xpertsea/module-user-sdk';
import {
  PopulationHarvestCommercialClassBreakdown,
  PopulationHarvestCommercialClassBreakdownHeadonBucketBins
} from '@xpertsea/module-farm-sdk';
import { BiomassProcessedWithoutPrice } from '@screens/pond/components/harvest-plan/biomass-processed-without-price';

type CommercialClassBreakdownUnit = 'kg' | 'lbs';

export type ClassificationType = 'headon' | 'headless';

export type ClassificationBreakdownTableProps = {
  unit: CommercialClassBreakdownUnit;
  type: ClassificationType;
  unitsConfig: UserPreferences['unitsConfig'];
  isUnitSwitchVisible?: boolean;
  setUnit?: (unit: CommercialClassBreakdownUnit) => void;
  commercialClassBreakdownData?: PopulationHarvestCommercialClassBreakdown;
};

export function ClassificationBreakdownTable(props: ClassificationBreakdownTableProps) {
  const { unit, unitsConfig, type, setUnit, commercialClassBreakdownData, isUnitSwitchVisible = true } = props;

  const { trans } = getTrans();
  const lang = useAppSelector((state) => state.app.lang);

  const unitLabel = unit === 'lbs' ? trans('t_lb') : trans('t_kg');

  const commercialClassBreakdown =
    type === 'headon' ? commercialClassBreakdownData?.headon : commercialClassBreakdownData?.headless;

  const sortByCategory = (
    bucketBins: PopulationHarvestCommercialClassBreakdownHeadonBucketBins[],
    type: 'headon' | 'headless'
  ) => {
    const lookup = type === 'headless' ? headlessSizesLookup : headonSizesLookup;

    return [...bucketBins].sort((a, b) => {
      const categoryA = a.category;
      const categoryB = b.category;

      if (!lookup[categoryA] || !lookup[categoryB]) {
        return 0;
      }

      const weightA = lookup[categoryA]?.startWeight || 0;
      const weightB = lookup[categoryB]?.startWeight || 0;

      return weightB - weightA;
    });
  };

  const unitConversionTabs = [
    { title: trans('t_kg'), isActive: unit === 'kg', onClick: () => setUnit('kg') },
    { title: trans('t_lb'), isActive: unit === 'lbs', onClick: () => setUnit('lbs') }
  ];

  const thTextStyles: TextProps = {
    color: 'text.gray',
    borderRadius: 'xl',
    backgroundColor: 'bg.gray.medium',
    px: 'md',
    py: 'xs',
    h: '40px',
    size: 'label200',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center'
  };

  const thStyles: BoxProps = {
    background: 'white',
    border: 'none',
    borderRight: '6px solid white',
    textAlign: 'center',
    p: 0,
    position: 'sticky',
    top: 0,
    zIndex: 4,
    color: 'text.gray'
  };

  const tdStyles: BoxProps = {
    textAlign: 'center',
    borderBottomWidth: '0.5px',
    borderColor: 'border.gray',
    px: 'md',
    py: 'xs',
    textStyle: 'label.200'
  };

  const expectedTdStyles: BoxProps = {
    ...tdStyles,
    borderBottom: 'none',
    textStyle: 'paragraph.200.heavy'
  };

  const containerRef = useRef<HTMLDivElement>(null);

  const commercialClassesWithoutPrice = commercialClassBreakdown?.bucketBins?.filter(
    (bin) => isNumber(bin.biomassProcessedLbs) && bin.biomassProcessedLbs === 0
  );

  const commercialClassesWithPrice = commercialClassBreakdown?.bucketBins?.filter(
    (bin) => isNumber(bin.biomassProcessedLbs) && bin.biomassProcessedLbs > 0
  );

  return (
    <>
      {isUnitSwitchVisible && (
        <Flex mb='xs' p='3xs' h='40px' w='115px' rounded='4xl' align='center' justify='center' bgColor='bg.gray.strong'>
          {unitConversionTabs.map((item, index) => (
            <Flex
              h='36px'
              flex={1}
              key={index}
              rounded='4xl'
              align='center'
              justify='center'
              cursor='pointer'
              userSelect='none'
              onClick={item.onClick}
              {...(item.isActive && { bgColor: 'white' })}
            >
              <Text fontSize='lg' fontWeight={600} lineHeight='16px'>
                {item.title}
              </Text>
            </Flex>
          ))}
        </Flex>
      )}
      <TableContainer ref={containerRef}>
        <Table.Root size='lg' borderCollapse='collapse'>
          <Table.Header>
            <Table.Row>
              <Table.ColumnHeader {...thStyles}>
                <Text {...thTextStyles}>{trans('t_class_size')}</Text>
              </Table.ColumnHeader>
              <Table.ColumnHeader {...thStyles}>
                <Text {...thTextStyles}>{trans('t_biomass_processed', { unit: unitLabel })}</Text>
              </Table.ColumnHeader>
              <AdminOnlyWrapper>
                <Table.ColumnHeader {...thStyles}>
                  <Flex
                    borderRadius={thTextStyles.borderRadius}
                    bgColor={thTextStyles.backgroundColor}
                    px={thTextStyles.px}
                    py={thTextStyles.py}
                    h={thTextStyles.h}
                    justifyContent='center'
                    alignItems='center'
                    gap='2xs'
                  >
                    <Text size={thTextStyles.size} color={thTextStyles.color}>
                      {trans('t_revenue_processed_unit', { unit: unitLabel })}
                    </Text>
                    {isUnitSwitchVisible && <BiomassProcessedWithoutPrice containerRef={containerRef} />}
                  </Flex>
                </Table.ColumnHeader>
                <Table.ColumnHeader {...thStyles}>
                  <Text {...thTextStyles}> {trans('t_revenue')}</Text>
                </Table.ColumnHeader>
              </AdminOnlyWrapper>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {sortByCategory([...(commercialClassesWithPrice ?? [])], type)
              ?.reverse()
              ?.map((bucketBin, index) => {
                return (
                  <Table.Row key={index}>
                    <Table.Cell {...tdStyles}>{bucketBin?.category}</Table.Cell>
                    <Table.Cell {...tdStyles}>
                      {' '}
                      {isNumber(bucketBin?.biomassProcessedLbs)
                        ? formatNumber(
                            convertUnitByDivision(bucketBin.biomassProcessedLbs, unit ?? unitsConfig?.biomass),
                            {
                              lang,
                              fractionDigits: 0
                            }
                          )
                        : ''}
                    </Table.Cell>
                    <AdminOnlyWrapper>
                      <Table.Cell {...tdStyles}>
                        {isNumber(bucketBin?.revenuePerPound)
                          ? formatNumber(
                              convertUnitByMultiplication(bucketBin.revenuePerPound, unit ?? unitsConfig?.biomass),
                              { lang, fractionDigits: 2, isCurrency: true }
                            )
                          : ''}
                      </Table.Cell>

                      <Table.Cell {...tdStyles}>
                        {isNumber(bucketBin?.revenue)
                          ? formatNumber(bucketBin.revenue, { lang, fractionDigits: 0, isCurrency: true })
                          : ''}
                      </Table.Cell>
                    </AdminOnlyWrapper>
                  </Table.Row>
                );
              })}
            <Table.Row>
              <Table.Cell {...expectedTdStyles}>{trans('t_expected')}</Table.Cell>
              <Table.Cell {...expectedTdStyles}>
                {isNumber(commercialClassBreakdown?.totalBiomassProcessedLbs)
                  ? formatNumber(
                      convertUnitByDivision(
                        commercialClassBreakdown.totalBiomassProcessedLbs,
                        unit ?? unitsConfig?.biomass
                      ),
                      { lang, fractionDigits: 0 }
                    )
                  : 0}
              </Table.Cell>
              <AdminOnlyWrapper>
                <Table.Cell {...expectedTdStyles}>
                  {isNumber(commercialClassBreakdown?.totalRevenuePerPound)
                    ? trans('t_amount_unit', {
                        unit: unitLabel,
                        amount: formatNumber(
                          convertUnitByMultiplication(
                            commercialClassBreakdown.totalRevenuePerPound,
                            unit ?? unitsConfig?.biomass
                          ),
                          { lang, fractionDigits: 2, isCurrency: true }
                        )
                      })
                    : 0}
                </Table.Cell>
                <Table.Cell {...expectedTdStyles}>
                  {isNumber(commercialClassBreakdown?.totalRevenue)
                    ? formatNumber(commercialClassBreakdown.totalRevenue, { lang, fractionDigits: 0, isCurrency: true })
                    : 0}
                </Table.Cell>
              </AdminOnlyWrapper>
            </Table.Row>
          </Table.Body>
        </Table.Root>
      </TableContainer>

      {commercialClassesWithoutPrice?.length > 0 && (
        <>
          <Text mt='md' mb='md' size='label100'>
            {trans('t_class_size_without_price_set')}
          </Text>

          <TableContainer ref={containerRef}>
            <Table.Root size='lg' borderCollapse='collapse'>
              <Table.Header>
                <Table.Row>
                  <Table.ColumnHeader {...thStyles}>
                    <Text {...thTextStyles}>{trans('t_class_size')}</Text>
                  </Table.ColumnHeader>
                  <Table.ColumnHeader {...thStyles}>
                    <Text {...thTextStyles}>{trans('t_biomass_unpriced', { unit: unitLabel })}</Text>
                  </Table.ColumnHeader>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {sortByCategory([...(commercialClassesWithoutPrice ?? [])], type)
                  ?.reverse()
                  ?.map((bucketBin, index) => {
                    return (
                      <Table.Row key={index}>
                        <Table.Cell {...tdStyles}>{bucketBin?.category}</Table.Cell>
                        <Table.Cell {...tdStyles}>
                          {' '}
                          {isNumber(bucketBin?.biomassNotProcessedLbs)
                            ? formatNumber(
                                convertUnitByDivision(bucketBin.biomassNotProcessedLbs, unit ?? unitsConfig?.biomass),
                                {
                                  lang,
                                  fractionDigits: 0
                                }
                              )
                            : ''}
                        </Table.Cell>
                      </Table.Row>
                    );
                  })}
              </Table.Body>
            </Table.Root>
          </TableContainer>
        </>
      )}
    </>
  );
}
