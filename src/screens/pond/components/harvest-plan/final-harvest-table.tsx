import { Box, Collapsible, Flex, Grid, GridItem, GridProps, Text, TextProps } from '@chakra-ui/react';
import { useGetHarvestCustomizeViewVariables } from '@screens/pond/components/harvest-plan/hooks/use-get-harvest-customize-view-variables';
import { getTrans } from '@i18n/get-trans';
import { HarvestViewVariable } from '@screens/pond/components/harvest-plan/hooks/use-get-harvest-view-variables';
import { ReactNode, useLayoutEffect, useRef, useState } from 'react';
import { useAppSelector } from '@redux/hooks';
import isUndefined from 'lodash/isUndefined';
import { GridDropDownRow } from '@screens/pond/components/grid-drop-down-row';
import { DateTime } from 'luxon';
import { getDaysDiffBetweenDates } from '@screens/farm/helpers/farm-dates';
import { convertUnitByDivision, convertUnitByMultiplication, formatNumber, isNumber } from '@utils/number';
import { isLastItemInSection } from './helpers/summary-section';
import { Tooltip } from '@components/ui/tooltip';
import { InfoFilledIcon } from '@icons/info/info-filled-icon';
import { gramsInPound, sqPerHectare } from '@utils/constants';

export function FinalHarvestTable(props: { harvestToShow: string }) {
  const { harvestToShow } = props;
  const population = useAppSelector((state) => state.farm?.currentPopulation);

  const { generalVariables, financialVariables, biomassVariables, viewVariables } =
    useGetHarvestCustomizeViewVariables();
  const { trans } = getTrans();
  const [isGeneralOpen, setIsGeneralOpen] = useState(true);
  const [isFinancialsOpen, setIsFinancialsOpen] = useState(true);
  const [isBiomassOpen, setIsBiomassOpen] = useState(true);
  const [containerHeight, setContainerHeight] = useState<number | null>(null);

  const hasGeneralVariables = viewVariables.some((variable) => generalVariables.has(variable));
  const hasFinancialVariables = viewVariables.some((variable) => financialVariables.has(variable));
  const hasBiomassVariables = viewVariables.some((variable) => biomassVariables.has(variable));

  const { harvest } = population ?? {};
  const hasEstimatedValues = harvest?.hasEstimatedValues;
  const containerRef = useRef<HTMLDivElement>(null);

  useLayoutEffect(() => {
    if (containerRef.current) {
      setContainerHeight(containerRef.current.clientHeight);
    }
  }, []);

  return (
    <Flex flexDir='column' gap='md' ref={containerRef}>
      {hasEstimatedValues && (
        <Text size='label300' color='text.gray.disabled' mt='-md'>
          {trans('t_using_imputed_values_for_calculations')}
        </Text>
      )}
      <Grid
        templateColumns='230px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        mb='-xs-alt'
        alignItems='center'
        pos='relative'
        zIndex={5}
      >
        <GridItem>
          <Text size='label300' />
        </GridItem>
        <GridItem pos='relative'>
          {harvestToShow === 'finalHarvestRecorded' && (
            <Box
              borderTopRadius='lg'
              pos='absolute'
              opacity='0.5'
              left='-20px'
              top='-10px'
              w='112px'
              h={`${containerHeight}px`}
              bgColor='bg.brandBlue.weakShade2'
              zIndex={1}
            />
          )}
          <Flex align='center' gap='xs' pos='relative' zIndex={5}>
            <Text size='label300' color='text.gray.weak'>
              {trans('t_recorded')}
            </Text>
            <Tooltip content={trans('t_harvest_plan_recorded_msg')} showArrow>
              <InfoFilledIcon firstSectionColor='icon.gray.weak' secondSectionColor='white' boxSize='16px' />
            </Tooltip>
          </Flex>
        </GridItem>
        <GridItem pos='relative'>
          {harvestToShow === 'finalHarvestPlanned' && (
            <Box
              borderTopRadius='lg'
              pos='absolute'
              opacity='0.12'
              left='-25px'
              top='-10px'
              w='112px'
              h={`${containerHeight}px`}
              bgColor='bg.shrimpyPinky'
              zIndex={1}
            />
          )}
          <Flex align='center' gap='xs' pos='relative' zIndex={5}>
            <Text size='label300' color='text.gray.weak'>
              {trans('t_plan')}
            </Text>
            <Tooltip content={trans('t_harvest_plan_planned_msg')} showArrow>
              <InfoFilledIcon firstSectionColor='icon.gray.weak' secondSectionColor='white' boxSize='16px' />
            </Tooltip>
          </Flex>
        </GridItem>
        <GridItem>
          <Text size='label300' color='text.gray.weak'>
            {trans('t_target')}
          </Text>
        </GridItem>
      </Grid>

      {hasGeneralVariables && (
        <>
          <GridDropDownRow
            title={trans('t_general')}
            isOpen={isGeneralOpen}
            onClick={() => setIsGeneralOpen((prev) => !prev)}
          />
          <Box mt='-md' pos='relative' zIndex={5}>
            <Collapsible.Root open={isGeneralOpen}>
              <Collapsible.Content>
                {viewVariables.map((variable, index) => {
                  if (!generalVariables.has(variable)) return null;
                  const isLastItem = isLastItemInSection(index, generalVariables, viewVariables);
                  return (
                    <GeneralSectionRowMapper
                      key={variable}
                      variable={variable}
                      containerProps={{ ...(isLastItem && { borderBottom: 'none' }) }}
                    />
                  );
                })}
              </Collapsible.Content>
            </Collapsible.Root>
          </Box>
        </>
      )}

      {hasFinancialVariables && (
        <>
          <GridDropDownRow
            title={trans('t_financials')}
            isOpen={isFinancialsOpen}
            onClick={() => setIsFinancialsOpen((prev) => !prev)}
          />
          <Box mt='-md' pos='relative' zIndex={5}>
            <Collapsible.Root open={isFinancialsOpen}>
              <Collapsible.Content>
                {viewVariables.map((variable, index) => {
                  if (!financialVariables.has(variable)) return null;
                  const isLastItem = isLastItemInSection(index, financialVariables, viewVariables);
                  return (
                    <FinancialSectionRowMapper
                      key={variable}
                      variable={variable}
                      containerProps={{ ...(isLastItem && { borderBottom: 'none' }) }}
                    />
                  );
                })}
              </Collapsible.Content>
            </Collapsible.Root>
          </Box>
        </>
      )}

      {hasBiomassVariables && (
        <>
          <GridDropDownRow
            title={trans('t_biomass')}
            isOpen={isBiomassOpen}
            onClick={() => setIsBiomassOpen((prev) => !prev)}
          />
          <Box mt='-md' pos='relative' zIndex={5}>
            <Collapsible.Root open={isBiomassOpen}>
              <Collapsible.Content>
                {viewVariables.map((variable, index) => {
                  if (!biomassVariables.has(variable)) return null;
                  const isLastItem = isLastItemInSection(index, biomassVariables, viewVariables);
                  return (
                    <BiomassSectionRowMapper
                      key={variable}
                      variable={variable}
                      containerProps={{ ...(isLastItem && { borderBottom: 'none' }) }}
                    />
                  );
                })}
              </Collapsible.Content>
            </Collapsible.Root>
          </Box>
        </>
      )}
    </Flex>
  );
}

type GeneralValues = Extract<HarvestViewVariable, 'date' | 'cycleDays' | 'averageWeight'>;
function GeneralSectionRowMapper(props: { variable: HarvestViewVariable; containerProps?: GridProps }) {
  const { variable, containerProps } = props;
  const { trans } = getTrans();
  const viewVariable = variable as GeneralValues;
  const lang = useAppSelector((state) => state.app.lang);
  const currentPopulation = useAppSelector((state) => state.farm?.currentPopulation);
  const timezone = useAppSelector((state) => state.farm?.currentFarm?.timezone);

  const {
    harvest,
    harvestPlan,
    cycleInformation,
    productionCycleComparison,
    stockedAt,
    stockedAtTimezone,
    productionPrediction,
    history
  } = currentPopulation ?? {};

  const { date: harvestDate, averageWeight: harvestAbw } = harvest ?? {};
  const { harvestDate: harvestPlanDate } = harvestPlan ?? {};
  const { target } = cycleInformation ?? {};
  const { cycleLength } = target ?? {};

  const stockedAtDateLuxon = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone ?? timezone });
  const stockedAtFormatted = stockedAtDateLuxon.toFormat('yyyy-MM-dd');
  const targetHarvestDateLuxon = stockedAtDateLuxon.plus({ days: cycleLength });
  const targetHarvestDateFormatted = targetHarvestDateLuxon.isValid
    ? targetHarvestDateLuxon.toFormat('MMM dd', { locale: lang })
    : '-';

  const targetCycleComparison = productionCycleComparison?.find((item) => {
    return item.date === targetHarvestDateLuxon.toFormat('yyyy-MM-dd');
  });
  const productionPredictionOnPlannedHarvest = productionPrediction?.find((item) => {
    return item.date === harvestPlanDate;
  });
  const lastHistoryOnPlanDate = history?.[0]?.date === harvestPlan?.harvestDate ? history?.[0] : undefined;

  const { averageWeight: targetAbw } = targetCycleComparison ?? {};
  const { averageWeight: harvestPlanAbw } = productionPredictionOnPlannedHarvest ?? {};
  const { averageWeight: historyAbw } = lastHistoryOnPlanDate ?? {};

  const harvestDateLuxon = DateTime.fromISO(harvestDate);
  const harvestDateFormatted = harvestDate ? harvestDateLuxon.toFormat('MMM dd', { locale: lang }) : '-';
  const harvestPlanDateLuxon = DateTime.fromISO(harvestPlanDate);
  const harvestPlanDateFormatted = harvestPlanDate ? harvestPlanDateLuxon.toFormat('MMM dd', { locale: lang }) : '-';

  const actualCycleDays = getDaysDiffBetweenDates({
    baseDate: harvestDateLuxon.toFormat('yyyy-MM-dd'),
    dateToCompare: stockedAtFormatted
  });

  const plannedCycleDays = getDaysDiffBetweenDates({
    baseDate: harvestPlanDateLuxon.toFormat('yyyy-MM-dd'),
    dateToCompare: stockedAtFormatted
  });

  const harvestAbwFormatted = isNumber(harvestAbw) ? formatNumber(harvestAbw, { fractionDigits: 2, lang }) : '-';
  const harvestPlanAbwFormatted = isNumber(harvestPlanAbw ?? historyAbw)
    ? formatNumber(harvestPlanAbw ?? historyAbw, { fractionDigits: 2, lang })
    : '-';

  const targetAbwFormatted = isNumber(targetAbw) ? formatNumber(targetAbw, { fractionDigits: 2, lang }) : '-';

  const actualCycleDaysFormatted = isNumber(actualCycleDays) ? actualCycleDays : '-';
  const plannedCycleDaysFormatted = isNumber(plannedCycleDays) ? plannedCycleDays : '-';
  const cycleLengthFormatted = isNumber(cycleLength) ? cycleLength : '-';

  const values: Record<GeneralValues, ReactNode> = {
    date: (
      <Grid
        templateColumns='230px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_date')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{harvestDateFormatted}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{harvestPlanDateFormatted}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{targetHarvestDateFormatted}</Text>
        </GridItem>
      </Grid>
    ),
    cycleDays: (
      <Grid
        templateColumns='230px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_days_of_culture')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{actualCycleDaysFormatted}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{plannedCycleDaysFormatted}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{cycleLengthFormatted}</Text>
        </GridItem>
      </Grid>
    ),
    averageWeight: (
      <Grid
        templateColumns='230px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_abw_g')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{harvestAbwFormatted}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{harvestPlanAbwFormatted}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{targetAbwFormatted}</Text>
        </GridItem>
      </Grid>
    )
  };

  return values[viewVariable];
}

type FinancialValues = Extract<HarvestViewVariable, 'totalRevenue' | 'revenuePerPound' | 'revenuePoundHarvest'>;
function FinancialSectionRowMapper(props: { variable: HarvestViewVariable; containerProps?: GridProps }) {
  const { variable, containerProps } = props;
  const { trans } = getTrans();
  const user = useAppSelector((state) => state.auth.user);
  const lang = useAppSelector((state) => state.app.lang);
  const currentPopulation = useAppSelector((state) => state.farm?.currentPopulation);
  const timezone = useAppSelector((state) => state.farm?.currentFarm?.timezone);

  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const unitConfigBiomass = unitsConfig?.biomass;
  const isBiomassUnitLbs = unitConfigBiomass === 'lbs' || isUndefined(unitConfigBiomass);

  const { harvest, harvestPlan, cycleInformation, productionCycleComparison, stockedAt, stockedAtTimezone, history } =
    currentPopulation ?? {};
  const { target } = cycleInformation ?? {};
  const { cycleLength } = target ?? {};

  const {
    revenue: harvestRevenue,
    revenuePerPound: harvestRevenuePerPound,
    revenuePoundHarvest: harvestRevenuePoundHarvest,
    hasEstimatedValues
  } = harvest ?? {};
  const { projection, harvestType: oldHarvestType, processorPriceList } = harvestPlan ?? {};
  const harvestType = processorPriceList?.defaultHarvestType ?? oldHarvestType;

  const { profitProjectionData, plannedHarvestIdx } = projection ?? {};
  const plannedProfitProjection = profitProjectionData?.[plannedHarvestIdx]?.[harvestType as 'headon'];

  const {
    liveTotalRevenue: plannedRevenue,
    revenuePerPound: plannedRevenuePerPound,
    revenuePoundHarvest: plannedRevenuePoundHarvest
  } = plannedProfitProjection ?? {};

  const lastHistoryOnPlanDate = history?.[0]?.date === harvestPlan?.harvestDate ? history?.[0] : undefined;

  const {
    totalRevenue: historyRevenue,
    revenuePerPound: historyRevenuePerPound,
    revenuePoundHarvest: historyRevenuePoundHarvest
  } = lastHistoryOnPlanDate ?? {};

  const stockedAtDateLuxon = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone ?? timezone });
  const targetHarvestDateFormatted = stockedAtDateLuxon.plus({ days: cycleLength }).toFormat('yyyy-MM-dd');

  const targetCycleComparison = productionCycleComparison?.find((item) => {
    return item.date === targetHarvestDateFormatted;
  });

  const {
    liveTotalRevenue: targetRevenue,
    revenuePerPound: targetRevenuePerPound,
    revenuePoundHarvest: targetRevenuePoundHarvest
  } = targetCycleComparison ?? {};

  const formatValue = (value: number, options: { isConverted?: boolean; fractionDigits?: number } = {}) => {
    const { isConverted = false, fractionDigits = 2 } = options;
    if (!isNumber(value)) return '-';
    if (!isConverted) return formatNumber(value, { fractionDigits, isCurrency: true, lang });
    return formatNumber(convertUnitByMultiplication(value, unitConfigBiomass), {
      fractionDigits,
      isCurrency: true,
      lang
    });
  };

  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');

  const estimatedValuesStyles: TextProps = hasEstimatedValues
    ? { py: '2xs', px: 'xs', bgColor: 'bg.gray.strong', borderRadius: 'full' }
    : {};

  const viewVariable = variable as FinancialValues;
  const values: Record<FinancialValues, ReactNode> = {
    totalRevenue: (
      <Grid
        templateColumns='230px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_revenue')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>
            <Box as='span' {...estimatedValuesStyles}>
              {formatValue(harvestRevenue, { fractionDigits: 0 })}
            </Box>
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(plannedRevenue ?? historyRevenue, { fractionDigits: 0 })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetRevenue, { fractionDigits: 0 })}</Text>
        </GridItem>
      </Grid>
    ),
    revenuePerPound: (
      <Grid
        templateColumns='230px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_revenue_per_unit_processed', { unit: unitLabel })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>
            <Box as='span' {...estimatedValuesStyles}>
              {formatValue(harvestRevenuePerPound, { isConverted: true })}
            </Box>
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>
            {formatValue(plannedRevenuePerPound ?? historyRevenuePerPound, { isConverted: true })}
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetRevenuePerPound, { isConverted: true })}</Text>
        </GridItem>
      </Grid>
    ),
    revenuePoundHarvest: (
      <Grid
        templateColumns='230px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_revenue_per_unit_harvested', { unit: unitLabel })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>
            <Box as='span' {...estimatedValuesStyles}>
              {formatValue(harvestRevenuePoundHarvest, { isConverted: true })}
            </Box>
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>
            {formatValue(plannedRevenuePoundHarvest ?? historyRevenuePoundHarvest, { isConverted: true })}
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetRevenuePoundHarvest, { isConverted: true })}</Text>
        </GridItem>
      </Grid>
    )
  };

  return values[viewVariable];
}

type BiomassValues = Extract<
  HarvestViewVariable,
  'totalBiomassLbs' | 'biomassLbsByHa' | 'animalsHarvested' | 'animalsHarvestedM2' | 'animalsHarvestedHa' | 'survival'
>;
function BiomassSectionRowMapper(props: { variable: HarvestViewVariable; containerProps?: GridProps }) {
  const { variable, containerProps } = props;
  const { trans } = getTrans();
  const user = useAppSelector((state) => state.auth.user);
  const lang = useAppSelector((state) => state.app.lang);
  const currentPopulation = useAppSelector((state) => state.farm?.currentPopulation);
  const pondSize = useAppSelector((state) => state.farm?.currentPond?.size);
  const timezone = useAppSelector((state) => state.farm?.currentFarm?.timezone);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const unitConfigBiomass = unitsConfig?.biomass;
  const isBiomassUnitLbs = unitConfigBiomass === 'lbs' || isUndefined(unitConfigBiomass);

  const {
    harvest,
    harvestPlan,
    cycleInformation,
    productionPrediction,
    productionCycleComparison,
    stockedAt,
    stockedAtTimezone,
    seedingQuantity,
    history
  } = currentPopulation ?? {};
  const { target } = cycleInformation ?? {};
  const { cycleLength } = target ?? {};

  const { lbsHarvested: harvestLbsHarvested, averageWeight } = harvest ?? {};
  const harvestBiomassLbsByHa = isNumber(harvestLbsHarvested) ? harvestLbsHarvested / pondSize : null;
  const harvestTotalAnimalsHarvested = (harvestLbsHarvested * gramsInPound) / averageWeight; // only from final harvest
  const harvestAnimalsHarvestedHa = harvestTotalAnimalsHarvested / pondSize; // only from final harvest
  const harvestAnimalsHarvestedM2 = harvestAnimalsHarvestedHa / sqPerHectare; // only from final harvest
  const harvestSurvival = seedingQuantity > 0 ? harvestTotalAnimalsHarvested / seedingQuantity : 0; // only from final harvest

  const { harvestDate: harvestPlanDate } = harvestPlan ?? {};

  const productionPredictionOnPlannedHarvest = productionPrediction?.find((item) => {
    return item.date === harvestPlanDate;
  });

  const {
    biomassLbs: plannedLbsHarvested,
    biomassLbsByHa: plannedBiomassLbsByHa,
    animalsRemainingM2: plannedAnimalsHarvestedM2,
    animalsRemainingHa: plannedAnimalsHarvestedHa,
    survival
  } = productionPredictionOnPlannedHarvest ?? {};

  const plannedTotalAnimalsHarvested = productionPredictionOnPlannedHarvest
    ? plannedAnimalsHarvestedHa * pondSize
    : undefined;

  const plannedSurvival = survival ?? undefined;

  const lastHistoryOnPlanDate = history?.[0]?.date === harvestPlan?.harvestDate ? history?.[0] : undefined;

  const {
    biomassLbs: historyLbsHarvested,
    biomassLbsByHa: historyBiomassLbsByHa,
    animalsRemainingM2: historyAnimalsHarvestedM2,
    animalsRemainingHa: historyAnimalsHarvestedHa
  } = lastHistoryOnPlanDate ?? {};

  const historyTotalAnimalsHarvested = historyAnimalsHarvestedHa * pondSize;
  const historySurvival = lastHistoryOnPlanDate?.survival ?? 0;

  const stockedAtDateLuxon = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone ?? timezone });
  const targetHarvestDateFormatted = stockedAtDateLuxon.plus({ days: cycleLength }).toFormat('yyyy-MM-dd');

  const targetCycleComparison = productionCycleComparison?.find((item) => {
    return item.date === targetHarvestDateFormatted;
  });

  const {
    biomassLbs: targetLbsHarvested,
    biomassLbsByHa: targetBiomassLbsByHa,
    animalsRemainingM2: targetAnimalsHarvestedM2,
    animalsRemainingHa: targetAnimalsHarvestedHa
  } = targetCycleComparison ?? {};

  const targetTotalAnimalsHarvested = isNumber(targetAnimalsHarvestedHa) ? targetAnimalsHarvestedHa * pondSize : null;

  const targetSurvival = seedingQuantity > 0 ? targetTotalAnimalsHarvested / seedingQuantity : 0;

  const formatValue = (
    value: number,
    options: { isConverted?: boolean; isPercentage?: boolean; fractionDigits?: number } = {}
  ) => {
    const { isConverted = false, isPercentage = false, fractionDigits = 0 } = options;
    if (!isNumber(value)) return '-';
    if (!isConverted) return formatNumber(value, { fractionDigits, isPercentage, lang });
    return formatNumber(convertUnitByDivision(value, unitConfigBiomass), { fractionDigits, lang });
  };

  const viewVariable = variable as BiomassValues;
  const values: Record<BiomassValues, ReactNode> = {
    totalBiomassLbs: (
      <Grid
        templateColumns='230px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{isBiomassUnitLbs ? trans('t_biomass_lb') : trans('t_biomass_kg')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestLbsHarvested, { isConverted: true })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(plannedLbsHarvested ?? historyLbsHarvested, { isConverted: true })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetLbsHarvested, { isConverted: true })}</Text>
        </GridItem>
      </Grid>
    ),
    biomassLbsByHa: (
      <Grid
        templateColumns='230px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{isBiomassUnitLbs ? trans('t_biomass_lb_ha') : trans('t_biomass_kg_ha')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestBiomassLbsByHa, { isConverted: true })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>
            {formatValue(plannedBiomassLbsByHa ?? historyBiomassLbsByHa, { isConverted: true })}
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetBiomassLbsByHa, { isConverted: true })}</Text>
        </GridItem>
      </Grid>
    ),
    animalsHarvested: (
      <Grid
        templateColumns='230px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_animals_harvested')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestTotalAnimalsHarvested)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(plannedTotalAnimalsHarvested ?? historyTotalAnimalsHarvested)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetTotalAnimalsHarvested)}</Text>
        </GridItem>
      </Grid>
    ),
    animalsHarvestedM2: (
      <Grid
        templateColumns='230px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_animals_harvested_per_m2')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestAnimalsHarvestedM2, { fractionDigits: 2 })} </Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>
            {formatValue(plannedAnimalsHarvestedM2 ?? historyAnimalsHarvestedM2, { fractionDigits: 2 })}
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetAnimalsHarvestedM2, { fractionDigits: 2 })}</Text>
        </GridItem>
      </Grid>
    ),
    animalsHarvestedHa: (
      <Grid
        templateColumns='230px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_animals_harvested_ha')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestAnimalsHarvestedHa)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(plannedAnimalsHarvestedHa ?? historyAnimalsHarvestedHa)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetAnimalsHarvestedHa)}</Text>
        </GridItem>
      </Grid>
    ),
    survival: (
      <Grid
        templateColumns='230px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_survival')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestSurvival, { isPercentage: true, fractionDigits: 1 })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>
            {formatValue(plannedSurvival ?? historySurvival, { isPercentage: true, fractionDigits: 1 })}
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetSurvival, { isPercentage: true, fractionDigits: 1 })}</Text>
        </GridItem>
      </Grid>
    )
  };

  return values[viewVariable];
}
