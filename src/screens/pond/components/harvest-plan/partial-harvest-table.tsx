import { useGetHarvestCustomizeViewVariables } from '@screens/pond/components/harvest-plan/hooks/use-get-harvest-customize-view-variables';
import { Box, Table, Text } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { HarvestViewVariable } from '@screens/pond/components/harvest-plan/hooks/use-get-harvest-view-variables';
import { ReactNode, useLayoutEffect, useRef, useState } from 'react';
import { useAppSelector } from '@redux/hooks';
import isUndefined from 'lodash/isUndefined';
import {
  harvestTableCellStyles,
  harvestTableFirstCellStyles,
  TableDropDownRow
} from '@screens/pond/components/table-drop-down-row';
import { PopulationPartialHarvest } from '@xpertsea/module-farm-sdk';
import { convertUnitByDivision, convertUnitByMultiplication, formatNumber, isNumber } from '@utils/number';
import { DateTime } from 'luxon';
import { getDaysDiffBetweenDates } from '@screens/farm/helpers/farm-dates';
import { TableContainer } from '@components/ui/table-container';
import { sqPerHectare } from '@utils/constants';

type PopulationPartialHarvestWithPlanned = PopulationPartialHarvest & { isPlanned: boolean };

export function PartialHarvestTable(props: { harvestToShow: string }) {
  const { harvestToShow } = props;
  const splitString = harvestToShow.split('-');
  const partialHarvestId = splitString[0];
  const status = splitString[1];
  const isRecorded = status === 'recorded';
  const { trans } = getTrans();

  const { generalVariables, biomassVariables, financialVariables, viewVariables } =
    useGetHarvestCustomizeViewVariables(true);

  const [isGeneralOpen, setIsGeneralOpen] = useState(true);
  const [isFinancialsOpen, setIsFinancialsOpen] = useState(true);
  const [isBiomassOpen, setIsBiomassOpen] = useState(true);
  const [containerHeight, setContainerHeight] = useState<number | null>(null);

  const currentPopulation = useAppSelector((state) => state.farm?.currentPopulation);
  const pondSize = useAppSelector((state) => state.farm?.currentPond?.size);

  const { partialHarvest, partialHarvestPlanned, seedingQuantity } = currentPopulation ?? {};
  let abwSum: number;
  let revenueSum: number;
  let lbsProcessedSum: number;
  let lbsHarvestedSum: number;
  let lbsHarvestedHaSum: number;
  let animalsHarvestedSum: number;
  let animalsHarvestedM2Sum: number;
  let animalsHarvestedHaSum: number;

  const processHarvests = (harvests: PopulationPartialHarvest[], isPlanned: boolean) => {
    if (!harvests?.length) return [];
    return harvests
      .filter((harvest) => harvest?.date)
      .map((harvest) => {
        const quantity = harvest.quantity;

        if (isNumber(harvest.weight)) abwSum = (abwSum ?? 0) + harvest.weight;
        if (isNumber(harvest.revenue)) revenueSum = (revenueSum ?? 0) + harvest.revenue;
        if (isNumber(harvest.lbsProcessed)) lbsProcessedSum = (lbsProcessedSum ?? 0) + harvest.lbsProcessed;
        if (isNumber(harvest.lbsHarvested)) lbsHarvestedSum = (lbsHarvestedSum ?? 0) + harvest.lbsHarvested;

        if (isNumber(harvest.lbsHarvested) && isNumber(pondSize)) {
          lbsHarvestedHaSum = (lbsHarvestedHaSum ?? 0) + harvest.lbsHarvested / pondSize;
        }

        if (isNumber(quantity)) {
          animalsHarvestedSum = (animalsHarvestedSum ?? 0) + quantity;

          if (pondSize) {
            animalsHarvestedM2Sum = (animalsHarvestedM2Sum ?? 0) + quantity / pondSize / sqPerHectare;
            animalsHarvestedHaSum = (animalsHarvestedHaSum ?? 0) + quantity / pondSize;
          }
        }

        return {
          ...harvest,
          isPlanned
        };
      });
  };

  const validPartialHarvest = processHarvests(partialHarvest, false);
  const validPartialHarvestPlanned = processHarvests(partialHarvestPlanned, true);

  const harvests = validPartialHarvest.concat(validPartialHarvestPlanned).sort((first, second) => {
    const firstDate = DateTime.fromISO(first.date);
    const secondDate = DateTime.fromISO(second.date);
    return firstDate.toMillis() - secondDate.toMillis();
  });
  const unitConversionValue = 454;
  const totalAverageWeight =
    lbsHarvestedSum && animalsHarvestedSum ? (lbsHarvestedSum / animalsHarvestedSum) * unitConversionValue : undefined;
  const totalRevenuePerPound = isNumber(revenueSum) && lbsProcessedSum ? revenueSum / lbsProcessedSum : undefined;
  const totalRevenuePerPoundHarvested =
    isNumber(revenueSum) && lbsHarvestedSum ? revenueSum / lbsHarvestedSum : undefined;

  const containerRef = useRef<HTMLDivElement>(null);

  useLayoutEffect(() => {
    if (containerRef.current) {
      setContainerHeight(containerRef.current.clientHeight);
    }
  }, []);

  // EARLY UI RETURN
  if (!harvests.length) {
    return <Text>{trans('t_no_partial_harvest_alert')}</Text>;
  }
  const hasFinancialVariables = viewVariables.some((variable) => financialVariables.has(variable));
  const hasBiomassVariables = viewVariables.some((variable) => biomassVariables.has(variable));

  return (
    <TableContainer ref={containerRef}>
      <Table.Root borderCollapse='separate' borderSpacing='0 10px'>
        <Table.Header>
          <Table.Row>
            <Table.ColumnHeader {...harvestTableFirstCellStyles} />
            {harvests.map((harvest, index) => {
              const harvestNumber = index + 1;
              return (
                <Table.ColumnHeader key={`${harvest.date}-${harvest.harvestId}`} {...harvestTableCellStyles}>
                  {harvest.harvestId === partialHarvestId && (
                    <>
                      {isRecorded && (
                        <Box
                          borderTopRadius='lg'
                          pos='absolute'
                          opacity='0.5'
                          left='-12px'
                          top='-10px'
                          w='112px'
                          h={`${containerHeight}px`}
                          bgColor='bg.brandBlue.weakShade2'
                          zIndex={1}
                        />
                      )}

                      {!isRecorded && (
                        <Box
                          borderTopRadius='lg'
                          pos='absolute'
                          opacity='0.12'
                          left='-12px'
                          top='-10px'
                          w='112px'
                          h={`${containerHeight}px`}
                          bgColor='bg.shrimpyPinky'
                          zIndex={1}
                        />
                      )}
                    </>
                  )}

                  <Text size='label300' color='text.gray.weak' pos='relative' zIndex={5}>
                    {trans('t_partial_harvest_x', { count: harvestNumber })}
                  </Text>
                </Table.ColumnHeader>
              );
            })}
            <Table.ColumnHeader {...harvestTableCellStyles}>
              <Text size='label300' color='text.gray.weak'>
                {trans('t_total')}
              </Text>
            </Table.ColumnHeader>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          <TableDropDownRow
            title={trans('t_general')}
            isOpen={isGeneralOpen}
            onClick={() => setIsGeneralOpen((prev) => !prev)}
          />
          {isGeneralOpen && (
            <>
              <Table.Row>
                <Table.Cell {...harvestTableCellStyles} ps='sm-alt'>
                  <Text size='label200'>{trans('t_status')}</Text>
                </Table.Cell>
                {harvests.map((harvest) => {
                  const harvestState = harvest.isPlanned ? trans('t_planned') : trans('t_recorded');
                  return (
                    <Table.Cell key={`${harvest.date}-${harvest.harvestId}`} {...harvestTableCellStyles}>
                      <Text size='label200'>{harvestState}</Text>
                    </Table.Cell>
                  );
                })}
                <Table.Cell {...harvestTableCellStyles}>
                  <Text size='label200'>-</Text>
                </Table.Cell>
              </Table.Row>
              {viewVariables.map((variable) => {
                if (!generalVariables.has(variable)) return null;
                return (
                  <GeneralSectionRowMapper
                    key={variable}
                    variable={variable}
                    totalAverageWeight={totalAverageWeight}
                    harvests={harvests}
                  />
                );
              })}
            </>
          )}

          {hasFinancialVariables && (
            <>
              <TableDropDownRow
                title={trans('t_financials')}
                isOpen={isFinancialsOpen}
                onClick={() => setIsFinancialsOpen((prev) => !prev)}
              />
              {isFinancialsOpen && (
                <>
                  {viewVariables.map((variable) => {
                    if (!financialVariables.has(variable)) return null;
                    return (
                      <FinancialSectionRowMapper
                        key={variable}
                        variable={variable}
                        totalRevenue={revenueSum}
                        totalRevenuePerPound={totalRevenuePerPound}
                        totalRevenuePerPoundHarvested={totalRevenuePerPoundHarvested}
                        harvests={harvests}
                      />
                    );
                  })}
                </>
              )}
            </>
          )}
          {hasBiomassVariables && (
            <>
              <TableDropDownRow
                title={trans('t_biomass')}
                isOpen={isBiomassOpen}
                onClick={() => setIsBiomassOpen((prev) => !prev)}
              />
              {isBiomassOpen && (
                <>
                  {viewVariables.map((variable) => {
                    if (!biomassVariables.has(variable)) return null;
                    return (
                      <BiomassSectionRowMapper
                        key={variable}
                        variable={variable}
                        totalBiomassHarvested={lbsHarvestedSum}
                        totalBiomassHarvestedHa={lbsHarvestedHaSum}
                        totalAnimalsHarvested={animalsHarvestedSum}
                        totalAnimalsHarvestedM2={animalsHarvestedM2Sum}
                        totalAnimalsHarvestedHa={animalsHarvestedHaSum}
                        harvests={harvests}
                        seedingQuantity={seedingQuantity}
                      />
                    );
                  })}
                </>
              )}
            </>
          )}
        </Table.Body>
      </Table.Root>
    </TableContainer>
  );
}

type GeneralValues = Extract<HarvestViewVariable, 'date' | 'cycleDays' | 'averageWeight'>;
type GeneralSectionRowMapperProps = {
  variable: HarvestViewVariable;
  totalAverageWeight: number;
  harvests: PopulationPartialHarvestWithPlanned[];
};
function GeneralSectionRowMapper(props: GeneralSectionRowMapperProps) {
  const { variable, totalAverageWeight, harvests } = props;
  const { trans } = getTrans();
  const currentPopulation = useAppSelector((state) => state.farm?.currentPopulation);
  const timezone = useAppSelector((state) => state.farm?.currentFarm?.timezone);
  const lang = useAppSelector((state) => state.app.lang);

  const { stockedAt, stockedAtTimezone } = currentPopulation ?? {};
  const viewVariable = variable as GeneralValues;
  const values: Record<GeneralValues, ReactNode> = {
    date: (
      <>
        <Table.Cell {...harvestTableCellStyles} ps='sm-alt'>
          <Text size='label200'>{trans('t_date')}</Text>
        </Table.Cell>
        {harvests.map((harvest) => (
          <Table.Cell key={`${harvest.date}-${harvest.harvestId}`} {...harvestTableCellStyles}>
            <Text size='label200'>{DateTime.fromISO(harvest.date).toFormat('MMM dd')}</Text>
          </Table.Cell>
        ))}
        <Table.Cell {...harvestTableCellStyles}>
          <Text size='label200'>-</Text>
        </Table.Cell>
      </>
    ),
    cycleDays: (
      <>
        <Table.Cell {...harvestTableCellStyles} ps='sm-alt'>
          <Text size='label200'>{trans('t_days_of_culture')}</Text>
        </Table.Cell>
        {harvests.map((harvest) => {
          const stockedAtDateLuxon = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone ?? timezone });
          const stockedAtFormatted = stockedAtDateLuxon.toFormat('yyyy-MM-dd');
          const cycleDays = getDaysDiffBetweenDates({
            baseDate: harvest.date,
            dateToCompare: stockedAtFormatted
          });
          return (
            <Table.Cell key={`${harvest.date}-${harvest.harvestId}`} {...harvestTableCellStyles}>
              <Text size='label200'>{isNumber(cycleDays) ? cycleDays : '-'}</Text>
            </Table.Cell>
          );
        })}
        <Table.Cell {...harvestTableCellStyles}>
          <Text size='label200'>-</Text>
        </Table.Cell>
      </>
    ),
    averageWeight: (
      <>
        <Table.Cell {...harvestTableCellStyles} ps='sm-alt'>
          <Text size='label200'>{trans('t_abw_g')}</Text>
        </Table.Cell>
        {harvests.map((harvest) => {
          const abw = harvest.weight;
          const abwFormatted = isNumber(abw) ? formatNumber(abw, { fractionDigits: 2, lang }) : '-';
          return (
            <Table.Cell key={`${harvest.date}-${harvest.harvestId}`} {...harvestTableCellStyles}>
              <Text size='label200'>{abwFormatted}</Text>
            </Table.Cell>
          );
        })}
        <Table.Cell {...harvestTableCellStyles}>
          <Text size='label200'>
            {isNumber(totalAverageWeight)
              ? formatNumber(totalAverageWeight, { fractionDigits: 2, lang, shouldRound: false })
              : '-'}
          </Text>
        </Table.Cell>
      </>
    )
  };

  return <Table.Row>{values[viewVariable]}</Table.Row>;
}

type FinancialValues = Extract<HarvestViewVariable, 'totalRevenue' | 'revenuePerPound' | 'revenuePoundHarvest'>;

type FinancialSectionRowMapperProps = {
  variable: HarvestViewVariable;
  totalRevenue: number;
  totalRevenuePerPound: number;
  totalRevenuePerPoundHarvested: number;
  harvests: PopulationPartialHarvestWithPlanned[];
};
function FinancialSectionRowMapper(props: FinancialSectionRowMapperProps) {
  const { variable, totalRevenue, totalRevenuePerPound, totalRevenuePerPoundHarvested, harvests } = props;
  const { trans } = getTrans();
  const lang = useAppSelector((state) => state.app.lang);

  const user = useAppSelector((state) => state.auth.user);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const unitConfigBiomass = unitsConfig?.biomass;
  const isBiomassUnitLbs = unitConfigBiomass === 'lbs' || isUndefined(unitConfigBiomass);
  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');

  const totalRevenueFormatted = isNumber(totalRevenue)
    ? formatNumber(totalRevenue, { fractionDigits: 0, lang, isCurrency: true })
    : '-';
  const totalRevenuePerPoundFormatted = isNumber(totalRevenuePerPound)
    ? formatNumber(convertUnitByMultiplication(totalRevenuePerPound, unitConfigBiomass), {
        fractionDigits: 2,
        isCurrency: true,
        lang
      })
    : '-';

  const totalRevenuePerPoundHarvestedFormatted = isNumber(totalRevenuePerPoundHarvested)
    ? formatNumber(convertUnitByMultiplication(totalRevenuePerPoundHarvested, unitConfigBiomass), {
        fractionDigits: 2,
        isCurrency: true,
        lang
      })
    : '-';

  const viewVariable = variable as FinancialValues;
  const values: Record<FinancialValues, ReactNode> = {
    totalRevenue: (
      <>
        <Table.Cell {...harvestTableCellStyles} ps='sm-alt'>
          <Text size='label200'>{trans('t_revenue')}</Text>
        </Table.Cell>
        {harvests.map((harvest) => {
          const revenue = harvest.revenue;
          const revenueFormatted = isNumber(revenue)
            ? formatNumber(revenue, { fractionDigits: 0, isCurrency: true, lang })
            : '-';
          return (
            <Table.Cell key={`${harvest.date}-${harvest.harvestId}`} {...harvestTableCellStyles}>
              <Text size='label200'>{revenueFormatted}</Text>
            </Table.Cell>
          );
        })}
        <Table.Cell {...harvestTableCellStyles}>
          <Text size='label200'>{totalRevenueFormatted}</Text>
        </Table.Cell>
      </>
    ),
    revenuePerPound: (
      <>
        <Table.Cell {...harvestTableCellStyles} ps='sm-alt'>
          <Text size='label200'>{trans('t_revenue_per_unit_processed', { unit: unitLabel })}</Text>
        </Table.Cell>
        {harvests.map((harvest) => {
          const revenuePerPound = harvest.revenuePerPound;
          const revenuePerPoundFormatted = isNumber(revenuePerPound)
            ? formatNumber(convertUnitByMultiplication(revenuePerPound, unitConfigBiomass), {
                fractionDigits: 2,
                isCurrency: true,
                lang
              })
            : '-';
          return (
            <Table.Cell key={`${harvest.date}-${harvest.harvestId}`} {...harvestTableCellStyles}>
              <Text size='label200'>{revenuePerPoundFormatted}</Text>
            </Table.Cell>
          );
        })}
        <Table.Cell {...harvestTableCellStyles}>
          <Text size='label200'>{totalRevenuePerPoundFormatted}</Text>
        </Table.Cell>
      </>
    ),
    revenuePoundHarvest: (
      <>
        <Table.Cell {...harvestTableCellStyles} ps='sm-alt'>
          <Text size='label200'>{trans('t_revenue_per_unit_harvested', { unit: unitLabel })}</Text>
        </Table.Cell>
        {harvests.map((harvest) => {
          const revenuePoundHarvest = harvest.revenuePoundHarvest;
          const revenuePoundHarvestFormatted = isNumber(revenuePoundHarvest)
            ? formatNumber(convertUnitByMultiplication(revenuePoundHarvest, unitConfigBiomass), {
                fractionDigits: 2,
                isCurrency: true,
                lang
              })
            : '-';
          return (
            <Table.Cell key={`${harvest.date}-${harvest.harvestId}`} {...harvestTableCellStyles}>
              <Text size='label200'>{revenuePoundHarvestFormatted}</Text>
            </Table.Cell>
          );
        })}
        <Table.Cell {...harvestTableCellStyles}>
          <Text size='label200'>{totalRevenuePerPoundHarvestedFormatted}</Text>
        </Table.Cell>
      </>
    )
  };

  return <Table.Row>{values[viewVariable]}</Table.Row>;
}

type BiomassValues = Extract<
  HarvestViewVariable,
  | 'totalBiomassLbs'
  | 'biomassLbsByHa'
  | 'animalsHarvested'
  | 'animalsHarvestedM2'
  | 'animalsHarvestedHa'
  | 'percStockedAnimals'
>;
type BiomassSectionRowMapperProps = {
  variable: HarvestViewVariable;
  totalBiomassHarvested: number;
  totalBiomassHarvestedHa: number;
  totalAnimalsHarvested: number;
  totalAnimalsHarvestedM2: number;
  totalAnimalsHarvestedHa: number;
  harvests: PopulationPartialHarvestWithPlanned[];
  seedingQuantity: number;
};
function BiomassSectionRowMapper(props: BiomassSectionRowMapperProps) {
  const {
    variable,
    totalBiomassHarvested,
    totalBiomassHarvestedHa,
    totalAnimalsHarvested,
    totalAnimalsHarvestedM2,
    totalAnimalsHarvestedHa,
    harvests,
    seedingQuantity
  } = props;
  const { trans } = getTrans();

  const pondSize = useAppSelector((state) => state.farm?.currentPond?.size);
  const lang = useAppSelector((state) => state.app.lang);
  const user = useAppSelector((state) => state.auth.user);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const unitConfigBiomass = unitsConfig?.biomass;

  const isBiomassUnitLbs = unitConfigBiomass === 'lbs' || isUndefined(unitConfigBiomass);

  const totalBiomassHarvestedFormatted = isNumber(totalBiomassHarvested)
    ? formatNumber(convertUnitByDivision(totalBiomassHarvested, unitConfigBiomass), { fractionDigits: 0, lang })
    : '-';

  const totalBiomassHarvestedHaFormatted = isNumber(totalBiomassHarvestedHa)
    ? formatNumber(convertUnitByDivision(totalBiomassHarvestedHa, unitConfigBiomass), { fractionDigits: 0, lang })
    : '-';

  const totalAnimalsHarvestedFormatted = isNumber(totalAnimalsHarvested)
    ? formatNumber(totalAnimalsHarvested, { fractionDigits: 0, lang })
    : '-';
  const totalAnimalsHarvestedM2Formatted = isNumber(totalAnimalsHarvestedM2)
    ? formatNumber(totalAnimalsHarvestedM2, { fractionDigits: 2, lang })
    : '-';
  const totalAnimalsHarvestedHaFormatted = isNumber(totalAnimalsHarvestedHa)
    ? formatNumber(totalAnimalsHarvestedHa, { fractionDigits: 0, lang })
    : '-';

  const viewVariable = variable as BiomassValues;
  const values: Record<BiomassValues, ReactNode> = {
    totalBiomassLbs: (
      <>
        <Table.Cell {...harvestTableFirstCellStyles}>
          <Text size='label200'>{isBiomassUnitLbs ? trans('t_biomass_lb') : trans('t_biomass_kg')}</Text>
        </Table.Cell>
        {harvests.map((harvest) => {
          const lbsHarvested = harvest.lbsHarvested;
          const lbsHarvestedFormatted = isNumber(lbsHarvested)
            ? formatNumber(convertUnitByDivision(lbsHarvested, unitConfigBiomass), { fractionDigits: 0, lang })
            : '-';
          return (
            <Table.Cell key={`${harvest.date}-${harvest.harvestId}`} {...harvestTableCellStyles}>
              <Text size='label200'>{lbsHarvestedFormatted}</Text>
            </Table.Cell>
          );
        })}
        <Table.Cell {...harvestTableCellStyles}>
          <Text size='label200'>{totalBiomassHarvestedFormatted}</Text>
        </Table.Cell>
      </>
    ),
    biomassLbsByHa: (
      <>
        <Table.Cell {...harvestTableFirstCellStyles}>
          <Text size='label200'>{isBiomassUnitLbs ? trans('t_biomass_lb_ha') : trans('t_biomass_kg_ha')}</Text>
        </Table.Cell>
        {harvests.map((harvest) => {
          const lbsHarvested = harvest.lbsHarvested;
          const lbsHarvestedHa = isNumber(lbsHarvested) && pondSize ? lbsHarvested / pondSize : undefined;
          const lbsHarvestedHaFormatted = isNumber(lbsHarvestedHa)
            ? formatNumber(convertUnitByDivision(lbsHarvestedHa, unitConfigBiomass), { fractionDigits: 0, lang })
            : '-';
          return (
            <Table.Cell key={`${harvest.date}-${harvest.harvestId}`} {...harvestTableCellStyles}>
              <Text size='label200'>{lbsHarvestedHaFormatted}</Text>
            </Table.Cell>
          );
        })}
        <Table.Cell {...harvestTableCellStyles}>
          <Text size='label200'>{totalBiomassHarvestedHaFormatted}</Text>
        </Table.Cell>
      </>
    ),
    animalsHarvested: (
      <>
        <Table.Cell {...harvestTableFirstCellStyles}>
          <Text size='label200'>{trans('t_animals_harvested')}</Text>
        </Table.Cell>
        {harvests.map((harvest) => {
          const animalsHarvested = harvest.quantity;
          const animalsHarvestedFormatted = isNumber(animalsHarvested)
            ? formatNumber(animalsHarvested, { fractionDigits: 0, lang })
            : '-';
          return (
            <Table.Cell key={`${harvest.date}-${harvest.harvestId}`} {...harvestTableCellStyles}>
              <Text size='label200'>{animalsHarvestedFormatted}</Text>
            </Table.Cell>
          );
        })}
        <Table.Cell {...harvestTableCellStyles}>
          <Text size='label200'>{totalAnimalsHarvestedFormatted}</Text>
        </Table.Cell>
      </>
    ),
    animalsHarvestedM2: (
      <>
        <Table.Cell {...harvestTableFirstCellStyles}>
          <Text size='label200'>{trans('t_animals_harvested_per_m2')}</Text>
        </Table.Cell>
        {harvests.map((harvest) => {
          const animalsHarvested = harvest.quantity;
          const animalsHarvestedM2 =
            isNumber(animalsHarvested) && pondSize ? animalsHarvested / pondSize / sqPerHectare : undefined;
          const animalsHarvestedM2Formatted = isNumber(animalsHarvestedM2)
            ? formatNumber(animalsHarvestedM2, { fractionDigits: 2, lang })
            : '-';
          return (
            <Table.Cell key={`${harvest.date}-${harvest.harvestId}`} {...harvestTableCellStyles}>
              <Text size='label200'>{animalsHarvestedM2Formatted}</Text>
            </Table.Cell>
          );
        })}
        <Table.Cell {...harvestTableCellStyles}>
          <Text size='label200'>{totalAnimalsHarvestedM2Formatted}</Text>
        </Table.Cell>
      </>
    ),
    animalsHarvestedHa: (
      <>
        <Table.Cell {...harvestTableFirstCellStyles}>
          <Text size='label200'>{trans('t_animals_harvested_ha')}</Text>
        </Table.Cell>
        {harvests.map((harvest) => {
          const animalsHarvested = harvest.quantity;
          const animalsHarvestedHa = isNumber(animalsHarvested) && pondSize ? animalsHarvested / pondSize : undefined;
          const animalsHarvestedHaFormatted = isNumber(animalsHarvestedHa)
            ? formatNumber(animalsHarvestedHa, { fractionDigits: 0, lang })
            : '-';
          return (
            <Table.Cell key={`${harvest.date}-${harvest.harvestId}`} {...harvestTableCellStyles}>
              <Text size='label200'>{animalsHarvestedHaFormatted}</Text>
            </Table.Cell>
          );
        })}
        <Table.Cell {...harvestTableCellStyles}>
          <Text size='label200'>{totalAnimalsHarvestedHaFormatted}</Text>
        </Table.Cell>
      </>
    ),
    percStockedAnimals: (
      <>
        <Table.Cell {...harvestTableFirstCellStyles}>
          <Text size='label200'>{trans('t_percentage_of_stocked_animals')}</Text>
        </Table.Cell>
        {harvests.map((harvest) => {
          const animalsHarvested = harvest.quantity;
          const percStockedAnimals =
            isNumber(animalsHarvested) && seedingQuantity ? animalsHarvested / seedingQuantity : undefined;
          const percStockedAnimalsFormatted = isNumber(percStockedAnimals)
            ? formatNumber(percStockedAnimals, { fractionDigits: 2, lang, isPercentage: true })
            : '-';
          return (
            <Table.Cell key={`${harvest.date}-${harvest.harvestId}`} {...harvestTableCellStyles}>
              <Text size='label200'>{percStockedAnimalsFormatted}</Text>
            </Table.Cell>
          );
        })}
        <Table.Cell {...harvestTableCellStyles}>
          <Text size='label200'>
            {isNumber(totalAnimalsHarvested) && seedingQuantity
              ? formatNumber(totalAnimalsHarvested / seedingQuantity, { fractionDigits: 0, lang, isPercentage: true })
              : '-'}
          </Text>
        </Table.Cell>
      </>
    )
  };

  return <Table.Row>{values[viewVariable]}</Table.Row>;
}
