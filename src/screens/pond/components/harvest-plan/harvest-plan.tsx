import { ReactNode, useState } from 'react';
import { Box, Flex, FlexProps, Heading, useMediaQuery } from '@chakra-ui/react';
import { CardContainer } from '@components/card-container/card-container';
import { getTrans } from '@i18n/get-trans';
import { TabCardHeader } from '@screens/pond/components/harvest-plan/tab-card-header';
import { LiquidationReport } from '@screens/pond/components/harvest-plan/liquidation-report';
import { CycleSummaryTable } from '@screens/pond/components/harvest-plan/cycle-summary-table';
import { FinalHarvestTable } from '@screens/pond/components/harvest-plan/final-harvest-table';
import { PartialHarvestTable } from '@screens/pond/components/harvest-plan/partial-harvest-table';
import { BaseMenu } from '@components/base/base-menu';
import { RepeatFilledIcon } from '@icons/repeat/repeat-filled-icon';

export function HarvestPlan() {
  const [harvestToShow, setHarvestToShow] = useState('finalHarvestPlanned');

  return (
    <Flex gap='sm-alt' align='stretch' direction={{ base: 'column', xl: 'row' }} mb='2xl-alt'>
      <LiquidationReport harvestToShow={harvestToShow} setHarvestToShow={setHarvestToShow} />
      <HarvestPlanTabs harvestToShow={harvestToShow} />
    </Flex>
  );
}

type HarvestPlanTabs = 'finalHarvest' | 'cycleSummary' | 'partialHarvest';
function HarvestPlanTabs(props: { harvestToShow: string }) {
  const { harvestToShow } = props;
  const { trans } = getTrans();
  const [activeTab, setActiveTab] = useState<HarvestPlanTabs>('cycleSummary');
  const tabOptions: { label: string; value: HarvestPlanTabs }[] = [
    { label: trans('t_cycle_summary'), value: 'cycleSummary' },
    { label: trans('t_final_harvest'), value: 'finalHarvest' },
    { label: trans('t_partial_harvest'), value: 'partialHarvest' }
  ];
  const selectedOption = tabOptions.find((option) => option.value === activeTab);
  return (
    <Flex direction='column' flex='1' w={{ base: '100%', xl: '50%' }}>
      <BaseMenu
        onItemSelect={(item) => {
          setActiveTab(item.value as HarvestPlanTabs);
        }}
        options={tabOptions}
        selectedOption={selectedOption}
        display={{ base: 'flex', md: 'none' }}
        mb='md'
      />
      <Flex px='md' gap='sm-alt' display={{ base: 'none', md: 'flex' }}>
        <HarvestPlanTabBox
          isActive={activeTab === 'cycleSummary'}
          onClick={() => {
            setActiveTab('cycleSummary');
          }}
        >
          {trans('t_cycle_summary')}
        </HarvestPlanTabBox>
        <HarvestPlanTabBox
          isActive={activeTab === 'finalHarvest'}
          onClick={() => {
            setActiveTab('finalHarvest');
          }}
        >
          {trans('t_final_harvest')}
        </HarvestPlanTabBox>

        <HarvestPlanTabBox
          isActive={activeTab === 'partialHarvest'}
          onClick={() => {
            setActiveTab('partialHarvest');
          }}
        >
          {trans('t_partial_harvest')}
        </HarvestPlanTabBox>
      </Flex>
      {activeTab === 'cycleSummary' && (
        <CardContainer display='flex' flexDirection='column' gap='md' flex='1'>
          <TabCardHeader isCycleSummary />
          <LandScapeOnlyCard>
            <CycleSummaryTable harvestToShow={harvestToShow} />
          </LandScapeOnlyCard>
        </CardContainer>
      )}
      {activeTab === 'finalHarvest' && (
        <CardContainer display='flex' flexDirection='column' gap='md' flex='1'>
          <TabCardHeader />
          <LandScapeOnlyCard>
            <FinalHarvestTable harvestToShow={harvestToShow} />
          </LandScapeOnlyCard>
        </CardContainer>
      )}

      {activeTab === 'partialHarvest' && (
        <CardContainer display='flex' flexDirection='column' gap='md' flex='1'>
          <TabCardHeader isPartialHarvest />
          <LandScapeOnlyCard>
            <PartialHarvestTable harvestToShow={harvestToShow} />
          </LandScapeOnlyCard>
        </CardContainer>
      )}
    </Flex>
  );
}

function LandScapeOnlyCard(props: { children: ReactNode }) {
  const { children } = props;
  const { trans } = getTrans();
  const [isLandscape, isLargerThan580] = useMediaQuery(['(orientation: landscape)', '(min-width: 580px)'], {
    ssr: false
  });

  const isContentVisible = isLandscape || isLargerThan580;

  if (!isContentVisible) {
    return (
      <Box m='auto' textAlign='center' maxW='300px'>
        <RepeatFilledIcon mb='sm-alt' />
        <Heading>{trans('t_rotate_your_phone_content_msg')}</Heading>
      </Box>
    );
  }

  return children;
}

function HarvestPlanTabBox(props: FlexProps & { isActive?: boolean }) {
  const { isActive, ...rest } = props;
  const isActiveStyles = isActive ? { bgColor: 'bg.squidInkPowder', color: 'white' } : {};
  return (
    <Flex
      justify='center'
      align='center'
      py='xs-alt'
      px='2lg'
      borderTopRadius='2xl'
      bgColor='bg.gray.strong'
      cursor='pointer'
      whiteSpace='nowrap'
      flex={1}
      textStyle='label.100'
      {...isActiveStyles}
      {...rest}
    />
  );
}
