import { Flex, Text, useDisclosure } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { ReactNode, RefObject } from 'react';
import { PopoverArrow, PopoverBody, PopoverContent, PopoverRoot, PopoverTrigger } from '@components/ui/popover';
import { InfoFilledIcon } from '@icons/info/info-filled-icon';

type BiomassProcessedWithoutPriceProps = {
  children?: ReactNode;
  containerRef: RefObject<HTMLDivElement>;
};

export function BiomassProcessedWithoutPrice(props: BiomassProcessedWithoutPriceProps) {
  const { children, containerRef } = props;

  const { trans } = getTrans();

  const { open, onOpen, onClose } = useDisclosure();

  return (
    <Flex align='center'>
      {!!children && <Text size='label200'>{children}</Text>}
      <PopoverRoot open={open} onOpenChange={(e) => (e.open ? onOpen() : onClose())}>
        <PopoverTrigger asChild>
          <InfoFilledIcon userSelect='none' cursor='pointer' _hover={{ opacity: 0.8 }} />
        </PopoverTrigger>

        <PopoverContent w='179px' bgColor='bg.gray.strong' shadow='none' outline='none' portalRef={containerRef}>
          <PopoverArrow bgColor='bg.gray.strong' />
          <PopoverBody py='sm-alt' px='md'>
            <Text size='label300'>{trans('t_biomass_processed_without_price')}</Text>
          </PopoverBody>
        </PopoverContent>
      </PopoverRoot>
    </Flex>
  );
}
