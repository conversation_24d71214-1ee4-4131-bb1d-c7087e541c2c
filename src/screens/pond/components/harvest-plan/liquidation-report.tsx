import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { getTrans } from '@i18n/get-trans';
import { Box, Flex, Text } from '@chakra-ui/react';
import { CardContainer } from '@components/card-container/card-container';
import { useAppSelector } from '@redux/hooks';
import { DateTime } from 'luxon';
import { PartialHarvestBreakdown } from '@screens/pond/components/harvest-plan/partial-harvest-breakdown';
import { FinalHarvestBreakdown } from '@screens/pond/components/harvest-plan/final-harvest-breakdown';
import { PopulationType } from '@redux/farm/set-current-population';
import { BaseMenu } from '@components/base/base-menu';

const getDefaultClassification = (population: PopulationType, partialHarvestId: string): 'headon' | 'headless' => {
  const { harvestPlan, partialHarvestPlanned = [] } = population ?? {};

  if (partialHarvestId === 'finalHarvest') {
    const harvestType = harvestPlan?.processorPriceList?.defaultHarvestType as 'headon' | 'headless';
    return harvestType || 'headon';
  }
  const partialHarvest = partialHarvestPlanned?.find((item) => item.harvestId === partialHarvestId);

  return (partialHarvest?.processorPriceList?.defaultHarvestType as 'headon' | 'headless') || 'headon';
};

type LiquidationReportProps = {
  harvestToShow: string;
  setHarvestToShow: Dispatch<SetStateAction<string>>;
};
export function LiquidationReport(props: LiquidationReportProps) {
  const { harvestToShow, setHarvestToShow } = props;
  const { trans } = getTrans();

  const lang = useAppSelector((state) => state.app.lang);
  const currentPopulation = useAppSelector((state) => state.farm.currentPopulation);
  const { partialHarvestPlanned, partialHarvest, _id: populationId, harvest } = currentPopulation ?? {};
  const [classification, setClassification] = useState<'headon' | 'headless'>('headon');

  const partialHarvestPlannedArr =
    partialHarvestPlanned?.map((item) => {
      return { ...item, status: 'planned' };
    }) ?? [];

  const partialHarvestArr =
    partialHarvest?.map((item) => {
      return { ...item, status: 'recorded' };
    }) ?? [];
  const partialHarvestList = [...partialHarvestPlannedArr, ...partialHarvestArr].sort((first, second) => {
    const firstDate = DateTime.fromISO(first.date);
    const secondDate = DateTime.fromISO(second.date);
    return firstDate.toMillis() - secondDate.toMillis();
  });

  const shouldShowRecordedHarvest = !!harvest?.date;
  const finalHarvestRecordedOption = shouldShowRecordedHarvest
    ? [
        {
          label: trans('t_final_harvest_recorded'),
          value: 'finalHarvestRecorded'
        }
      ]
    : [];
  const options = [
    {
      label: trans('t_final_harvest_plan'),
      value: 'finalHarvestPlanned'
    },
    ...finalHarvestRecordedOption,
    ...partialHarvestList.map((partialHarvest, index) => ({
      value: `${partialHarvest.harvestId}-${partialHarvest.status}`,
      label: (
        <>
          <Text as='span' me='xs'>
            {partialHarvest.status === 'recorded' && trans('t_partial_harvest_count_recorded', { count: index + 1 })}
            {partialHarvest.status === 'planned' && trans('t_partial_harvest_count_plan', { count: index + 1 })}
          </Text>
          <Text as='span'>{DateTime.fromISO(partialHarvest.date).toFormat('LLL dd, yyyy', { locale: lang })}</Text>
        </>
      )
    }))
  ];

  const selectedOption = options.find((item) => item.value === harvestToShow);

  useEffect(() => {
    if (selectedOption?.value) return;
    setHarvestToShow('finalHarvestPlanned');
  }, [selectedOption?.value]);

  useEffect(() => {
    const defaultClassification = getDefaultClassification(currentPopulation, harvestToShow);

    setClassification(defaultClassification);
  }, [harvestToShow, populationId]);
  const tabs = [
    {
      width: '90px',
      title: trans('t_head_on'),
      isActive: classification === 'headon',
      onClick: () => setClassification('headon')
    },
    {
      width: '96px',
      title: trans('t_headless'),
      isActive: classification === 'headless',
      onClick: () => setClassification('headless')
    }
  ];
  return (
    <CardContainer display='flex' flexDirection='column' gap='sm-alt' flex='1' w={{ base: '100%', xl: '50%' }}>
      <Flex align='center' justify='space-between' py='sm-alt' flexWrap='wrap'>
        <BaseMenu
          menuOffset={{ mainAxis: -2, crossAxis: 6 }}
          selectedOption={selectedOption}
          options={options}
          onItemSelect={(item) => setHarvestToShow(item.value)}
        />

        <Flex
          p='3xs'
          h='40px'
          rounded='4xl'
          align='center'
          justify='center'
          bgColor='bg.gray.strong'
          position='relative'
        >
          {tabs.map((item, index) => (
            <Flex
              h='36px'
              flex={1}
              key={index}
              rounded='4xl'
              align='center'
              w={item.width}
              justify='center'
              cursor='pointer'
              userSelect='none'
              onClick={item.onClick}
              position='relative'
              zIndex='1'
              bgColor={item.isActive ? 'white' : 'bg.gray.strong'}
            >
              <Text size='label100'>{item.title}</Text>
            </Flex>
          ))}
        </Flex>
      </Flex>
      <Box flex={1}>
        {harvestToShow === 'finalHarvestPlanned' || harvestToShow === 'finalHarvestRecorded' ? (
          <FinalHarvestBreakdown classification={classification} harvestToShow={harvestToShow} />
        ) : (
          <PartialHarvestBreakdown harvestToShow={harvestToShow} classification={classification} />
        )}
      </Box>
    </CardContainer>
  );
}
