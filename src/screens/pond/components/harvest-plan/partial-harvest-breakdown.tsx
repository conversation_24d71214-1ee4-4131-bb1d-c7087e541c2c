import { useGenerateProfitProjection } from '@screens/pond/hooks/use-generate-profit-projection-api';
import { useEffect, useMemo } from 'react';
import { ClassificationBreakdown } from '@screens/pond/components/harvest-plan/classification-breakdown';
import { useGetProfitCardsData } from '@screens/pond/hooks/use-get-profit-cards-data';
import { useAppSelector } from '@redux/hooks';
import { poundPerKg } from '@utils/constants';
import { getGenerateProfitProjectionRequestBody } from '@screens/pond/components/harvest-plan/helpers/projection';

type PartialHarvestBreakdownProps = {
  classification: string;
  harvestToShow: string;
};

export function PartialHarvestBreakdown(props: PartialHarvestBreakdownProps) {
  const { classification, harvestToShow } = props;
  const splitString = harvestToShow.split('-');
  const partialHarvestId = splitString[0];
  const status = splitString[1];
  const isRecorded = status === 'recorded';

  const currentPopulation = useAppSelector((state) => state.farm.currentPopulation);
  const {
    _id: currentPopulationId,
    partialHarvestPlanned,
    partialHarvest: currentPartialHarvest
  } = currentPopulation ?? {};

  const [{ isLoading: isLoadingGenerateProfitProjection }, generateProfitProjectionApi] = useGenerateProfitProjection();

  const profitData = useGetProfitCardsData();

  const allHarvests = [...(partialHarvestPlanned ?? []), ...(currentPartialHarvest ?? [])];
  const partialHarvest = allHarvests.find((item) => item.harvestId === partialHarvestId);

  const requestBody = useMemo(() => {
    if (isRecorded) {
      return null;
    }
    const {
      fcrValue,
      projectedFeed,
      farmFeedTypes,
      projectedGrowth,
      projectedSurvival,
      dailyMortalityPercentValue,
      expectedTargetSurvivalValue,
      feedAggressionMultiplierValue
    } = profitData ?? {};

    const partialHarvestPlannedWithoutCurrent = partialHarvestPlanned?.filter(
      (item) => item.harvestId !== partialHarvestId
    );

    return getGenerateProfitProjectionRequestBody({
      projectedGrowth,
      populationId: currentPopulationId,
      partialHarvestPlanned: partialHarvestPlannedWithoutCurrent,
      simulatedPartialHarvest: {
        processorId: partialHarvest?.processorId,
        percentTail: partialHarvest?.processorPriceList?.percentTail,
        harvestType: partialHarvest?.processorPriceList?.defaultHarvestType,
        processLeftoverAs: partialHarvest?.processorPriceList?.processLeftoverAs,
        percentHarvestAsHeadsOn: partialHarvest?.processorPriceList?.percentHarvestAsHeadsOn,
        percentHeadlessAsClassA: partialHarvest?.processorPriceList?.percentHeadlessAsClassA,
        percentHeadlessAsClassB: partialHarvest?.processorPriceList?.percentHeadlessAsClassB,
        percentHarvestAsLeftover: partialHarvest?.processorPriceList?.percentHarvestAsLeftover,
        percentHarvestAsHeadsOnClassA: partialHarvest?.processorPriceList?.percentHarvestAsHeadsOnClassA,
        percentHarvestAsHeadsOnClassB: partialHarvest?.processorPriceList?.percentHarvestAsHeadsOnClassB,
        percentLeftoverAsHeadlessClassB: partialHarvest?.processorPriceList?.percentLeftoverAsHeadlessClassB,
        percentLeftoverAsHeadlessClassA: partialHarvest?.processorPriceList?.percentLeftoverAsHeadlessClassA
      },
      projectedSurvival: {
        ...projectedSurvival,
        dailyMortalityPercent: dailyMortalityPercentValue,
        expectedTargetSurvival: expectedTargetSurvivalValue
      },
      projectedFeed: {
        ...projectedFeed,
        fcr: fcrValue,
        farmFeedTypes,
        type: projectedFeed?.type || 'fcr',
        feedAggressionMultiplier: feedAggressionMultiplierValue
      }
    });
  }, [profitData, JSON.stringify(partialHarvest), status, currentPopulationId, partialHarvestId]);

  useEffect(() => {
    if (!requestBody) return;
    generateProfitProjectionApi({ params: { ...requestBody, shouldUpdateRedux: false } });
  }, [JSON.stringify(requestBody), partialHarvest]);

  const weightDistribution = partialHarvest?.weightDistribution;

  const lbsHarvestedKg = partialHarvest?.lbsHarvested ? partialHarvest?.lbsHarvested / poundPerKg : 0;

  return (
    <ClassificationBreakdown
      isPartialHarvest={true}
      isRecordedPartialHarvest={isRecorded}
      partialHarvest={partialHarvest}
      classification={classification}
      isLoading={isLoadingGenerateProfitProjection}
      biomassKg={lbsHarvestedKg}
      biomassLbs={partialHarvest?.lbsHarvested}
      weightDistribution={weightDistribution}
    />
  );
}
