import { MutationRequest } from '@xpertsea/module-farm-sdk';
import omit from 'lodash/omit';

type SimulateProductionVariablesFormType = {
  projectedGrowth: { grams?: number; days?: number; type?: 'xpertsea' | 'custom' };
  projectedSurvival: {
    dailyMortalityPercent?: number;
    expectedTargetSurvival?: number;
    expectedTargetSurvivalDays?: number;
    projectedSurvivalType?: 'endOfCycle' | 'mortality' | 'kampiMortality';
  };
  projectedFeed: {
    fcr?: number;
    expectedFeed?: number;
    farmFeedTableId?: string;
    feedAggressionMultiplier?: number;
    type?: 'fcr' | 'kgPerHaPerDay' | 'feedTable';
    farmFeedTypes?: { id?: string; percentage?: number }[];
  };
};

type GetGenerateProfitProjectionRequestBodyParams = SimulateProductionVariablesFormType &
  MutationRequest['v1GenerateProfitProjection'][0];
export function getGenerateProfitProjectionRequestBody(
  params: GetGenerateProfitProjectionRequestBodyParams
): MutationRequest['v1GenerateProfitProjection'][0] {
  const { projectedFeed, projectedSurvival } = params;

  const mortalityPercentValue = projectedSurvival?.dailyMortalityPercent / 100;
  const expectedTargetSurvivalValue = projectedSurvival?.expectedTargetSurvival / 100;

  return {
    ...params,
    fcr: projectedFeed?.type === 'fcr' ? projectedFeed?.fcr : undefined,
    projectedSurvival: {
      ...projectedSurvival,
      dailyMortalityPercent: mortalityPercentValue,
      expectedTargetSurvival: expectedTargetSurvivalValue
    },
    projectedFeed: {
      ...omit(projectedFeed, 'fcr'),
      farmFeedTypes: projectedFeed?.farmFeedTypes?.length
        ? projectedFeed?.farmFeedTypes?.map((item) => ({
            ...item,
            percentage: item?.percentage ? item.percentage / 100 : undefined
          }))
        : undefined,
      expectedFeed: projectedFeed?.type === 'kgPerHaPerDay' ? projectedFeed.expectedFeed : undefined,
      feedAggressionMultiplier:
        projectedFeed?.type === 'feedTable' ? projectedFeed.feedAggressionMultiplier / 100 : undefined
    }
  };
}
