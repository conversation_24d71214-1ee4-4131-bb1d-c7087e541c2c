import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { usePermission } from '@hooks/use-permission';
import isUndefined from 'lodash/isUndefined';

export type HarvestViewVariable =
  | 'date'
  | 'cycleDays'
  | 'averageWeight'
  | 'totalRevenue'
  | 'revenuePerPound'
  | 'revenuePoundHarvest'
  | 'totalBiomassLbs'
  | 'biomassLbsByHa'
  | 'animalsHarvested'
  | 'animalsHarvestedM2'
  | 'animalsHarvestedHa'
  | 'survival'
  | 'percStockedAnimals';

type HarvestViewCustomizeVariableOption = { value: HarvestViewVariable; label: string };
type HarvestViewSections = 'general' | 'financials' | 'biomass';

type GetHarvestViewVariablesReturn = Record<
  HarvestViewSections,
  { title: string; subtitle?: string; variables: HarvestViewCustomizeVariableOption[] }
>;

export function useGetHarvestViewVariables(isPartial: boolean = false): GetHarvestViewVariablesReturn {
  const { trans } = getTrans();
  const user = useAppSelector((state) => state.auth.user);
  const farmData = useAppSelector((state) => state.farm.farmsData);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const farmIds = farmData?.map((farm) => farm._id);

  const isAdmin = usePermission({
    role: 'admin',
    entity: 'farm',
    entityIds: farmIds
  });

  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);

  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');
  const biomassHaTitle = isBiomassUnitLbs ? trans('t_biomass_lb_ha') : trans('t_biomass_kg_ha');

  const financials = !isAdmin
    ? ({} as GetHarvestViewVariablesReturn)
    : ({
        financials: {
          title: trans('t_financials'),
          variables: [
            { label: trans('t_revenue'), value: 'totalRevenue' },
            {
              label: trans('t_revenue_per_unit_processed', { unit: unitLabel }),
              value: 'revenuePerPound'
            },
            {
              label: trans('t_revenue_per_unit_harvested', { unit: unitLabel }),
              value: 'revenuePoundHarvest'
            }
          ]
        }
      } as GetHarvestViewVariablesReturn);

  const survivalOption: HarvestViewCustomizeVariableOption[] = isPartial
    ? []
    : [{ label: trans('t_survival'), value: 'survival' }];
  const percStockedAnimalsOption: HarvestViewCustomizeVariableOption[] = isPartial
    ? [{ label: trans('t_percentage_of_stocked_animals'), value: 'percStockedAnimals' }]
    : [];
  return {
    general: {
      title: trans('t_general'),
      variables: [
        { label: trans('t_date'), value: 'date' },
        { label: trans('t_days_of_culture'), value: 'cycleDays' },
        { label: trans('t_abw_g'), value: 'averageWeight' }
      ]
    },
    ...financials,
    biomass: {
      title: trans('t_biomass'),
      variables: [
        { label: trans('t_biomass_include_harvests_unit', { unit: unitLabel }), value: 'totalBiomassLbs' },
        { label: biomassHaTitle, value: 'biomassLbsByHa' },
        { label: trans('t_animals_harvested'), value: 'animalsHarvested' },
        { label: trans('t_animals_harvested_per_m2'), value: 'animalsHarvestedM2' },
        { label: trans('t_animals_harvested_ha'), value: 'animalsHarvestedHa' },
        ...survivalOption,
        ...percStockedAnimalsOption
      ]
    }
  };
}
