import { useAppSelector } from '@redux/hooks';
import { usePermission } from '@hooks/use-permission';
import { CycleSummaryViewVariable } from '@screens/pond/components/harvest-plan/hooks/use-get-cycle-summary-view-variables';

function sortVariables(variables: CycleSummaryViewVariable[]) {
  const variableOrder: Record<CycleSummaryViewVariable, number> = {
    totalProfit: 1,
    profitPerHaPerDay: 2,
    profitPoundHarvest: 3,
    profitPerPound: 4,
    totalRevenue: 5,
    rofi: 6,
    revenuePoundHarvest: 7,
    revenuePerPound: 8,
    totalCosts: 9,
    costPoundHarvest: 10,
    costPerPound: 11,
    stockingCosts: 12,
    cumulativeFeedCosts: 13,
    feedCostPerKg: 14,
    cumulativeOverheadCosts: 15,
    overheadCostProductiveDays: 16,
    overheadCostDryDays: 17,
    overheadCostsPerHaPerDay: 18,
    accumulatedOtherDirectCosts: 19,
    totalFeedGivenKg: 20,
    fcr: 21,
    adjustedFcr: 22,
    growthLinear: 23,
    totalBiomassLbs: 24,
    biomassLbsByHa: 25,
    animalsHarvested: 26,
    animalsHarvestedM2: 27,
    animalsHarvestedHa: 28,
    survival: 29
  };
  return [...variables].sort((a, b) => variableOrder[a] - variableOrder[b]);
}

export function useGetCycleSummaryCustomizeViewVariables() {
  const user = useAppSelector((state) => state.auth.user);
  const currentFarmId = useAppSelector((state) => state.farm.currentFarm?._id);

  const isAdmin = usePermission({
    role: 'admin',
    entity: 'farm',
    entityId: currentFarmId
  });

  const isSupervisor = usePermission({
    role: 'supervisor',
    entity: 'farm',
    entityId: currentFarmId
  });

  const { preferences } = user ?? {};
  const { viewFields } = preferences ?? {};

  const cycleSummaryViewVariables: CycleSummaryViewVariable[] = viewFields?.cycleSummaryViewVariables ?? [];

  const profitAndRevenueVariables = new Set<CycleSummaryViewVariable>([
    'rofi',
    'totalProfit',
    'profitPerHaPerDay',
    'profitPerPound',
    'profitPoundHarvest',
    'totalRevenue',
    'revenuePerPound',
    'revenuePoundHarvest'
  ]);
  const costVariables = new Set<CycleSummaryViewVariable>([
    'totalCosts',
    'costPerPound',
    'costPoundHarvest',
    'stockingCosts',
    'cumulativeFeedCosts',
    'feedCostPerKg',
    'cumulativeOverheadCosts',
    'overheadCostProductiveDays',
    'overheadCostDryDays',
    'overheadCostsPerHaPerDay',
    'accumulatedOtherDirectCosts'
  ]);
  const feedVariables = new Set<CycleSummaryViewVariable>(['totalFeedGivenKg', 'fcr', 'adjustedFcr']);

  const biomassVariables = new Set<CycleSummaryViewVariable>([
    'growthLinear',
    'totalBiomassLbs',
    'biomassLbsByHa',
    'animalsHarvested',
    'animalsHarvestedM2',
    'animalsHarvestedHa',
    'survival'
  ]);

  if (isAdmin) {
    const adminDefaultCustomizeViewVariables: CycleSummaryViewVariable[] = [
      'totalProfit',
      'totalRevenue',
      'rofi',
      'stockingCosts',
      'cumulativeOverheadCosts',
      'overheadCostProductiveDays',
      'overheadCostDryDays',
      'totalFeedGivenKg',
      'fcr',
      'growthLinear',
      'totalBiomassLbs',
      'biomassLbsByHa'
    ];
    const viewVariables = !cycleSummaryViewVariables.length
      ? adminDefaultCustomizeViewVariables
      : sortVariables(cycleSummaryViewVariables);
    return { profitAndRevenueVariables, costVariables, feedVariables, biomassVariables, viewVariables };
  }

  if (isSupervisor) {
    const supervisorDefaultCustomizeViewVariables: CycleSummaryViewVariable[] = [
      'stockingCosts',
      'cumulativeOverheadCosts',
      'overheadCostProductiveDays',
      'overheadCostDryDays',
      'totalFeedGivenKg',
      'fcr',
      'growthLinear',
      'totalBiomassLbs',
      'biomassLbsByHa'
    ];
    const viewVariables = !cycleSummaryViewVariables?.length
      ? supervisorDefaultCustomizeViewVariables
      : sortVariables(
          cycleSummaryViewVariables.filter((variable) => {
            return !profitAndRevenueVariables.has(variable);
          })
        );
    return {
      profitAndRevenueVariables,
      costVariables,
      feedVariables,
      biomassVariables,
      viewVariables
    };
  }

  const userDefaultVariables: CycleSummaryViewVariable[] = [
    'totalFeedGivenKg',
    'fcr',
    'growthLinear',
    'totalBiomassLbs',
    'biomassLbsByHa'
  ];
  const viewVariables = !cycleSummaryViewVariables?.length
    ? userDefaultVariables
    : sortVariables(
        cycleSummaryViewVariables.filter((variable) => {
          return !profitAndRevenueVariables.has(variable) && !costVariables.has(variable);
        })
      );
  return {
    profitAndRevenueVariables,
    costVariables,
    feedVariables,
    biomassVariables,
    viewVariables
  };
}
