import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { usePermission } from '@hooks/use-permission';
import isUndefined from 'lodash/isUndefined';

export type CycleSummaryViewVariable =
  | 'totalProfit'
  | 'profitPerHaPerDay'
  | 'profitPerPound'
  | 'profitPoundHarvest'
  | 'totalRevenue'
  | 'rofi'
  | 'revenuePerPound'
  | 'revenuePoundHarvest'
  | 'totalCosts'
  | 'costPerPound'
  | 'costPoundHarvest'
  | 'stockingCosts'
  | 'cumulativeFeedCosts'
  | 'feedCostPerKg'
  | 'cumulativeOverheadCosts'
  | 'overheadCostsPerHaPerDay'
  | 'overheadCostDryDays'
  | 'overheadCostProductiveDays'
  | 'accumulatedOtherDirectCosts'
  | 'totalFeedGivenKg'
  | 'fcr'
  | 'adjustedFcr'
  | 'growthLinear'
  | 'totalBiomassLbs'
  | 'biomassLbsByHa'
  | 'animalsHarvested'
  | 'animalsHarvestedM2'
  | 'animalsHarvestedHa'
  | 'survival';

type CycleSummaryViewCustomizeVariableOption = { value: CycleSummaryViewVariable; label: string };
type CycleSummaryViewSections = 'profit' | 'costs' | 'feed' | 'biomass';

type GetCycleSummaryViewVariablesReturn = Record<
  CycleSummaryViewSections,
  { title: string; subtitle?: string; variables: CycleSummaryViewCustomizeVariableOption[] }
>;

export function useGetCycleSummaryViewVariables(): GetCycleSummaryViewVariablesReturn {
  const { trans } = getTrans();

  const farmData = useAppSelector((state) => state.farm.farmsData);
  const user = useAppSelector((state) => state.auth.user);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const farmIds = farmData?.map((farm) => farm._id);

  const isAdmin = usePermission({
    role: 'admin',
    entity: 'farm',
    entityIds: farmIds
  });

  const isSupervisor = usePermission({
    role: 'supervisor',
    entity: 'farm',
    entityIds: farmIds
  });

  const isUser = !isAdmin && !isSupervisor;
  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);

  const unitTitle = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');
  const biomassHaTitle = isBiomassUnitLbs ? trans('t_biomass_lb_ha') : trans('t_biomass_kg_ha');
  const biomassTitle = isBiomassUnitLbs ? trans('t_biomass_lb') : trans('t_biomass_kg');

  const profit = !isAdmin
    ? ({} as GetCycleSummaryViewVariablesReturn)
    : ({
        profit: {
          title: trans('t_profit_revenue'),
          variables: [
            { label: trans('t_rofi'), value: 'rofi' },
            { label: trans('t_profit'), value: 'totalProfit' },
            { label: trans('t_profit_include_dry_days_ha_day'), value: 'profitPerHaPerDay' },
            {
              label: trans('t_profit_per_unit_processed', { unit: unitTitle }),
              value: 'profitPerPound'
            },
            {
              label: trans('t_profit_per_unit_harvested', { unit: unitTitle }),
              value: 'profitPoundHarvest'
            },
            { label: trans('t_revenue'), value: 'totalRevenue' },
            {
              label: trans('t_revenue_per_unit_processed', { unit: unitTitle }),
              value: 'revenuePerPound'
            },
            {
              label: trans('t_revenue_per_unit_harvested', { unit: unitTitle }),
              value: 'revenuePoundHarvest'
            }
          ]
        }
      } as GetCycleSummaryViewVariablesReturn);

  const costs = isUser
    ? ({} as GetCycleSummaryViewVariablesReturn)
    : ({
        costs: {
          title: trans('t_cost'),
          variables: [
            { label: trans('t_cost_$'), value: 'totalCosts' },
            {
              label: trans('t_cost_per_unit_processed', { unit: unitTitle }),
              value: 'costPerPound'
            },
            {
              label: trans('t_cost_per_unit_harvested', { unit: unitTitle }),
              value: 'costPoundHarvest'
            },
            { label: trans('t_stocking_cost'), value: 'stockingCosts' },
            { label: trans('t_feed_cost_$'), value: 'cumulativeFeedCosts' },
            { label: trans('t_feed_cost_per_kg'), value: 'feedCostPerKg' },
            { label: trans('t_overhead_cost_cumulative'), value: 'cumulativeOverheadCosts' },
            { label: trans('t_overhead_cost_days_of_culture'), value: 'overheadCostProductiveDays' },
            { label: trans('t_overhead_cost_dry_days'), value: 'overheadCostDryDays' },
            {
              label: trans('t_overhead_cost_ha_day'),
              value: 'overheadCostsPerHaPerDay'
            },
            { label: trans('t_other_direct_costs'), value: 'accumulatedOtherDirectCosts' }
          ]
        }
      } as GetCycleSummaryViewVariablesReturn);

  return {
    ...profit,
    ...costs,
    feed: {
      title: trans('t_feed'),
      variables: [
        { label: trans('t_feed_given_kg'), value: 'totalFeedGivenKg' },
        { label: trans('t_fcr'), value: 'fcr' },
        { label: trans('t_fcr_adjusted'), value: 'adjustedFcr' }
      ]
    },
    biomass: {
      title: trans('t_biomass'),
      variables: [
        { label: trans('t_growth_g_wk'), value: 'growthLinear' },
        { label: biomassTitle, value: 'totalBiomassLbs' },
        { label: biomassHaTitle, value: 'biomassLbsByHa' },
        { label: trans('t_animals_harvested'), value: 'animalsHarvested' },
        { label: trans('t_animals_harvested_per_m2'), value: 'animalsHarvestedM2' },
        { label: trans('t_animals_harvested_ha'), value: 'animalsHarvestedHa' },
        { label: trans('t_survival_include_harvests'), value: 'survival' }
      ]
    }
  };
}
