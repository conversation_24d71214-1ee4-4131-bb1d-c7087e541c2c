import { getTrans } from '@i18n/get-trans';
import { useRef } from 'react';
import { Box, Flex, IconButton, Text } from '@chakra-ui/react';
import { CustomizeHarvestViewModal } from '@screens/pond/components/harvest-plan/customize-harvest-view-modal';
import { CustomizeCycleSummaryViewModal } from '@screens/pond/components/harvest-plan/customize-cycle-summary-view-modal';
import { SettingsAdjustFilled } from '@icons/settings-adjust/settings-adjust-filled';

export function TabCardHeader(props: { isPartialHarvest?: boolean; isCycleSummary?: boolean }) {
  const { isPartialHarvest, isCycleSummary } = props;
  const { trans } = getTrans();
  const buttonRef = useRef<HTMLDivElement>(null);
  const harvestTitle = isPartialHarvest ? trans('t_partial_harvest') : trans('t_final_harvest');
  const title = isCycleSummary ? trans('t_cycle_summary') : harvestTitle;
  return (
    <Flex align='center' justify='space-between' h='24px'>
      <Text size='label100'>{title}</Text>
      <Box>
        {!isCycleSummary && (
          <CustomizeHarvestViewModal isPartial={isPartialHarvest}>
            <Box ref={buttonRef} display='none'>
              {trans('t_customize_view')}
            </Box>
          </CustomizeHarvestViewModal>
        )}
        {isCycleSummary && (
          <CustomizeCycleSummaryViewModal>
            <Box ref={buttonRef} display='none'>
              {trans('t_customize_view')}
            </Box>
          </CustomizeCycleSummaryViewModal>
        )}
        <IconButton
          h='fit-content'
          w='fit-content'
          bgColor='white'
          aria-label='filter-button'
          _focus={{ outline: 'none' }}
          onClick={() => buttonRef.current?.click()}
          css={{
            '&:hover svg.first-path': {
              fill: 'brandBlue.300'
            },
            '&:hover svg.second-path': {
              fill: 'graphBrandBlue'
            }
          }}
        >
          <SettingsAdjustFilled w='20px' h='20px' transform='scale(1, -1)' />{' '}
        </IconButton>
      </Box>
    </Flex>
  );
}
