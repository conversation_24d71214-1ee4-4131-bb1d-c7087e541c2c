import { Box, Collapsible, Flex, Grid, GridItem, GridProps, Text } from '@chakra-ui/react';
import { useGetCycleSummaryCustomizeViewVariables } from '@screens/pond/components/harvest-plan/hooks/use-get-cycle-summary-customize-view-variables';
import { getTrans } from '@i18n/get-trans';
import { ReactNode, useLayoutEffect, useRef, useState } from 'react';
import { useAppSelector } from '@redux/hooks';
import isUndefined from 'lodash/isUndefined';
import { GridDropDownRow } from '@screens/pond/components/grid-drop-down-row';
import { CycleSummaryViewVariable } from '@screens/pond/components/harvest-plan/hooks/use-get-cycle-summary-view-variables';
import { DateTime } from 'luxon';
import { convertUnitByDivision, convertUnitByMultiplication, formatNumber, isNumber } from '@utils/number';
import { isLastItemInSection } from './helpers/summary-section';
import { Tooltip } from '@components/ui/tooltip';
import { InfoFilledIcon } from '@icons/info/info-filled-icon';

export function CycleSummaryTable(props: { harvestToShow: string }) {
  const { harvestToShow } = props;

  const { profitAndRevenueVariables, costVariables, biomassVariables, feedVariables, viewVariables } =
    useGetCycleSummaryCustomizeViewVariables();
  const { trans } = getTrans();
  const [isProfitAndRevenueOpen, setIsProfitAndRevenueOpen] = useState(true);
  const [isCostsOpen, setIsCostsOpen] = useState(true);
  const [isFeedOpen, setIsFeedOpen] = useState(true);
  const [isBiomassOpen, setIsBiomassOpen] = useState(true);
  const [containerHeight, setContainerHeight] = useState<number | null>(null);

  const hasProfitRevenueVariables = viewVariables.some((variable) => profitAndRevenueVariables.has(variable));

  const hasCostVariables = viewVariables.some((variable) => costVariables.has(variable));

  const hasFeedVariables = viewVariables.some((variable) => feedVariables.has(variable));

  const hasBiomassVariables = viewVariables.some((variable) => biomassVariables.has(variable));
  const containerRef = useRef<HTMLDivElement>(null);

  useLayoutEffect(() => {
    if (containerRef.current) {
      setContainerHeight(containerRef.current.clientHeight);
    }
  }, []);

  return (
    <Flex flexDir='column' gap='md' ref={containerRef}>
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        mb='-xs-alt'
        pos='relative'
        zIndex={5}
      >
        <GridItem>
          <Text size='label300' />
        </GridItem>
        <GridItem pos='relative'>
          {harvestToShow === 'finalHarvestRecorded' && (
            <Box
              borderTopRadius='lg'
              pos='absolute'
              opacity='0.5'
              left='-20px'
              top='-10px'
              w='112px'
              h={`${containerHeight}px`}
              bgColor='bg.brandBlue.weakShade2'
              zIndex={1}
            />
          )}
          <Flex pos='relative' zIndex={5} align='center' gap='xs'>
            <Text size='label300' color='text.gray.weak'>
              {trans('t_recorded')}
            </Text>
            <Tooltip content={trans('t_harvest_plan_recorded_msg')}>
              <InfoFilledIcon firstSectionColor='icon.gray.weak' secondSectionColor='white' boxSize='16px' />
            </Tooltip>
          </Flex>
        </GridItem>
        <GridItem pos='relative'>
          {harvestToShow === 'finalHarvestPlanned' && (
            <Box
              borderTopRadius='lg'
              pos='absolute'
              opacity='0.12'
              left='-25px'
              top='-10px'
              w='112px'
              h={`${containerHeight}px`}
              bgColor='bg.shrimpyPinky'
              zIndex={1}
            />
          )}
          <Flex pos='relative' zIndex={5} align='center' gap='xs'>
            <Text size='label300' color='text.gray.weak'>
              {trans('t_plan')}
            </Text>
            <Tooltip content={trans('t_harvest_plan_planned_msg')}>
              <InfoFilledIcon firstSectionColor='icon.gray.weak' secondSectionColor='white' boxSize='16px' />
            </Tooltip>
          </Flex>
        </GridItem>
        <GridItem>
          <Text size='label300' color='text.gray.disabled'>
            {trans('t_target')}
          </Text>
        </GridItem>
      </Grid>

      {hasProfitRevenueVariables && (
        <>
          <GridDropDownRow
            title={trans('t_profit_revenue')}
            isOpen={isProfitAndRevenueOpen}
            onClick={() => setIsProfitAndRevenueOpen((prev) => !prev)}
          />
          <Box mt='-md' pos='relative' zIndex={5}>
            <Collapsible.Root open={isProfitAndRevenueOpen}>
              <Collapsible.Content>
                {viewVariables.map((variable, index) => {
                  if (!profitAndRevenueVariables.has(variable)) return null;
                  const isLastItem = isLastItemInSection(index, profitAndRevenueVariables, viewVariables);
                  return (
                    <ProfitAndRevenueSectionRowMapper
                      key={variable}
                      variable={variable}
                      containerProps={{ ...(isLastItem && { borderBottom: 'none' }) }}
                    />
                  );
                })}
              </Collapsible.Content>
            </Collapsible.Root>
          </Box>
        </>
      )}
      {hasCostVariables && (
        <>
          <GridDropDownRow
            title={trans('t_cost')}
            isOpen={isCostsOpen}
            onClick={() => setIsCostsOpen((prev) => !prev)}
          />
          <Box mt='-md' pos='relative' zIndex={5}>
            <Collapsible.Root open={isCostsOpen}>
              <Collapsible.Content>
                {viewVariables.map((variable, index) => {
                  if (!costVariables.has(variable)) return null;
                  const isLastItem = isLastItemInSection(index, costVariables, viewVariables);
                  return (
                    <CostsSectionRowMapper
                      key={variable}
                      variable={variable}
                      containerProps={{ ...(isLastItem && { borderBottom: 'none' }) }}
                    />
                  );
                })}
              </Collapsible.Content>
            </Collapsible.Root>
          </Box>
        </>
      )}

      {hasFeedVariables && (
        <>
          <GridDropDownRow title={trans('t_feed')} isOpen={isFeedOpen} onClick={() => setIsFeedOpen((prev) => !prev)} />
          <Box mt='-md' pos='relative' zIndex={5}>
            <Collapsible.Root open={isFeedOpen}>
              <Collapsible.Content>
                {viewVariables.map((variable, index) => {
                  if (!feedVariables.has(variable)) return null;
                  const isLastItem = isLastItemInSection(index, feedVariables, viewVariables);
                  return (
                    <FeedSectionRowMapper
                      key={variable}
                      variable={variable}
                      containerProps={{ ...(isLastItem && { borderBottom: 'none' }) }}
                    />
                  );
                })}
              </Collapsible.Content>
            </Collapsible.Root>
          </Box>
        </>
      )}
      {hasBiomassVariables && (
        <>
          <GridDropDownRow
            title={trans('t_biomass')}
            isOpen={isBiomassOpen}
            onClick={() => setIsBiomassOpen((prev) => !prev)}
          />
          <Box mt='-md' pos='relative' zIndex={5}>
            <Collapsible.Root open={isBiomassOpen}>
              <Collapsible.Content>
                {viewVariables.map((variable, index) => {
                  if (!biomassVariables.has(variable)) return null;
                  const isLastItem = isLastItemInSection(index, biomassVariables, viewVariables);
                  return (
                    <BiomassSectionRowMapper
                      key={variable}
                      variable={variable}
                      containerProps={{ ...(isLastItem && { borderBottom: 'none' }) }}
                    />
                  );
                })}
              </Collapsible.Content>
            </Collapsible.Root>
          </Box>
        </>
      )}
    </Flex>
  );
}

type ProfitAndRevenueValues = Extract<
  CycleSummaryViewVariable,
  | 'totalProfit'
  | 'profitPerHaPerDay'
  | 'profitPerPound'
  | 'profitPoundHarvest'
  | 'totalRevenue'
  | 'revenuePerPound'
  | 'revenuePoundHarvest'
  | 'rofi'
>;
function ProfitAndRevenueSectionRowMapper(props: { variable: CycleSummaryViewVariable; containerProps?: GridProps }) {
  const { variable, containerProps } = props;
  const { trans } = getTrans();
  const user = useAppSelector((state) => state.auth.user);
  const lang = useAppSelector((state) => state.app.lang);
  const currentPopulation = useAppSelector((state) => state.farm?.currentPopulation);

  const timezone = useAppSelector((state) => state.farm?.currentFarm?.timezone);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const unitConfigBiomass = unitsConfig?.biomass;
  const isBiomassUnitLbs = unitConfigBiomass === 'lbs' || isUndefined(unitConfigBiomass);

  const { harvest, harvestPlan, cycleInformation, productionCycleComparison, stockedAt, stockedAtTimezone, history } =
    currentPopulation ?? {};
  const { target } = cycleInformation ?? {};
  const { cycleLength } = target ?? {};

  const {
    totalProfit: harvestTotalProfit,
    profitPerHaPerDay: harvestProfitPerHaPerDay,
    profitPerPound: harvestProfitPerPound,
    profitPoundHarvest: harvestProfitPoundHarvest,
    totalRevenue: harvestTotalRevenue,
    totalRevenuePound: harvestTotalRevenuePound,
    totalRevenuePoundHarvest: harvestTotalRevenuePoundHarvest,
    rofi: harvestRofi
  } = harvest ?? {};

  const { projection, harvestType: oldHarvestType, processorPriceList } = harvestPlan ?? {};
  const { profitProjectionData, plannedHarvestIdx } = projection ?? {};
  const harvestType = processorPriceList?.defaultHarvestType ?? oldHarvestType;
  const plannedProfitProjection = profitProjectionData?.[plannedHarvestIdx]?.[harvestType as 'headon'];

  const {
    totalProfit: plannedTotalProfit,
    profitPerHaPerDay: plannedProfitPerHaPerDay,
    profitPerPound: plannedProfitPerPound,
    profitPoundHarvest: plannedProfitPoundHarvest,
    totalRevenue: plannedTotalRevenue,
    totalRevenuePound: plannedTotalRevenuePound,
    totalRevenuePoundHarvest: plannedTotalRevenuePoundHarvest,
    rofi: plannedRofi
  } = plannedProfitProjection ?? {};

  const lastHistoryOnPlanDate = history?.[0]?.date === harvestPlan?.harvestDate ? history?.[0] : undefined;

  const {
    totalProfit: historyTotalProfit,
    profitPerHaPerDay: historyProfitPerHaPerDay,
    profitPerPound: historyProfitPerPound,
    profitPoundHarvest: historyProfitPoundHarvest,
    totalRevenue: historyTotalRevenue,
    totalRevenuePound: historyTotalRevenuePound,
    totalRevenuePoundHarvest: historyTotalRevenuePoundHarvest,
    rofi: historyRofi
  } = lastHistoryOnPlanDate ?? {};

  const stockedAtDateLuxon = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone ?? timezone });
  const targetHarvestDateFormatted = stockedAtDateLuxon.plus({ days: cycleLength }).toFormat('yyyy-MM-dd');

  const targetCycleComparison = productionCycleComparison?.find((item) => {
    return item.date === targetHarvestDateFormatted;
  });

  const {
    totalProfit: targetTotalProfit,
    profitPerHaPerDay: targetProfitPerHaPerDay,
    profitPerPound: targetProfitPerPound,
    profitPoundHarvest: targetProfitPoundHarvest,
    totalRevenue: targetTotalRevenue,
    totalRevenuePound: targetTotalRevenuePound,
    totalRevenuePoundHarvest: targetTotalRevenuePoundHarvest,
    rofi: targetRofi
  } = targetCycleComparison ?? {};

  const formatValue = (
    value: number,
    options: { isConverted?: boolean; isPercentage?: boolean; isCurrency?: boolean; fractionDigits?: number } = {}
  ) => {
    const { isConverted = false, isPercentage = false, isCurrency = false, fractionDigits = 2 } = options;
    if (!isNumber(value)) return '-';
    if (!isConverted) return formatNumber(value, { fractionDigits, isPercentage, isCurrency, lang });
    return formatNumber(convertUnitByMultiplication(value, unitConfigBiomass), {
      fractionDigits,
      isCurrency: true,
      lang
    });
  };

  const unitTitle = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');

  const viewVariable = variable as ProfitAndRevenueValues;
  const values: Record<ProfitAndRevenueValues, ReactNode> = {
    rofi: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200' color='text.gray'>
            {trans('t_rofi')}
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200' color='text.gray'>
            {formatValue(harvestRofi, { isPercentage: true, fractionDigits: 1 })}
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200' color='text.gray'>
            {formatValue(plannedRofi ?? historyRofi, { isPercentage: true, fractionDigits: 1 })}
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200' color='text.gray'>
            {formatValue(targetRofi, { isPercentage: true, fractionDigits: 1 })}
          </Text>
        </GridItem>
      </Grid>
    ),
    totalProfit: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_profit')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestTotalProfit, { fractionDigits: 0, isCurrency: true })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>
            {formatValue(plannedTotalProfit ?? historyTotalProfit, { fractionDigits: 0, isCurrency: true })}
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetTotalProfit, { fractionDigits: 0, isCurrency: true })}</Text>
        </GridItem>
      </Grid>
    ),
    profitPerHaPerDay: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_profit_include_dry_days_ha_day')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestProfitPerHaPerDay, { isCurrency: true })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>
            {formatValue(plannedProfitPerHaPerDay ?? historyProfitPerHaPerDay, { isCurrency: true })}
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetProfitPerHaPerDay, { isCurrency: true })}</Text>
        </GridItem>
      </Grid>
    ),
    profitPerPound: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_profit_per_unit_processed', { unit: unitTitle })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestProfitPerPound, { isConverted: true, isCurrency: true })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>
            {formatValue(plannedProfitPerPound ?? historyProfitPerPound, { isConverted: true, isCurrency: true })}
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetProfitPerPound, { isConverted: true, isCurrency: true })}</Text>
        </GridItem>
      </Grid>
    ),
    profitPoundHarvest: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_profit_per_unit_harvested', { unit: unitTitle })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestProfitPoundHarvest, { isConverted: true, isCurrency: true })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>
            {formatValue(plannedProfitPoundHarvest ?? historyProfitPoundHarvest, {
              isConverted: true,
              isCurrency: true
            })}
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetProfitPoundHarvest, { isConverted: true, isCurrency: true })}</Text>
        </GridItem>
      </Grid>
    ),
    totalRevenue: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_revenue')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestTotalRevenue, { fractionDigits: 0, isCurrency: true })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>
            {formatValue(plannedTotalRevenue ?? historyTotalRevenue, { fractionDigits: 0, isCurrency: true })}
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetTotalRevenue, { fractionDigits: 0, isCurrency: true })}</Text>
        </GridItem>
      </Grid>
    ),
    revenuePerPound: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_revenue_per_unit_processed', { unit: unitTitle })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestTotalRevenuePound, { isConverted: true, isCurrency: true })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>
            {formatValue(plannedTotalRevenuePound ?? historyTotalRevenuePound, { isConverted: true, isCurrency: true })}
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetTotalRevenuePound, { isConverted: true, isCurrency: true })}</Text>
        </GridItem>
      </Grid>
    ),
    revenuePoundHarvest: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_revenue_per_unit_harvested', { unit: unitTitle })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>
            {formatValue(harvestTotalRevenuePoundHarvest, { isConverted: true, isCurrency: true })}
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>
            {formatValue(plannedTotalRevenuePoundHarvest ?? historyTotalRevenuePoundHarvest, {
              isConverted: true,
              isCurrency: true
            })}
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>
            {formatValue(targetTotalRevenuePoundHarvest, { isConverted: true, isCurrency: true })}
          </Text>
        </GridItem>
      </Grid>
    )
  };

  return values[viewVariable];
}

type CostValues = Extract<
  CycleSummaryViewVariable,
  | 'totalCosts'
  | 'costPerPound'
  | 'costPoundHarvest'
  | 'stockingCosts'
  | 'cumulativeFeedCosts'
  | 'feedCostPerKg'
  | 'cumulativeOverheadCosts'
  | 'overheadCostDryDays'
  | 'overheadCostProductiveDays'
  | 'overheadCostsPerHaPerDay'
  | 'accumulatedOtherDirectCosts'
>;
function CostsSectionRowMapper(props: { variable: CycleSummaryViewVariable; containerProps?: GridProps }) {
  const { variable, containerProps } = props;
  const { trans } = getTrans();

  const lang = useAppSelector((state) => state.app.lang);
  const timezone = useAppSelector((state) => state.farm?.currentFarm?.timezone);
  const currentPopulation = useAppSelector((state) => state.farm?.currentPopulation);
  const user = useAppSelector((state) => state.auth.user);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const unitConfigBiomass = unitsConfig?.biomass;
  const isBiomassUnitLbs = unitConfigBiomass === 'lbs' || isUndefined(unitConfigBiomass);
  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');
  const {
    harvest,
    productionCycleComparison,
    cycleInformation,
    harvestPlan,
    productionPrediction,
    stockedAt,
    stockedAtTimezone,
    history
  } = currentPopulation ?? {};

  const {
    harvestDate: harvestPlanDate,
    projection: harvestProjection,
    harvestType: oldHarvestType,
    processorPriceList
  } = harvestPlan ?? {};
  const harvestType = processorPriceList?.defaultHarvestType ?? oldHarvestType;

  const { target, projection } = cycleInformation ?? {};
  const { cycleLength } = target ?? {};
  const { overheadCostsPerHaPerDay: targetOverheadCostsPerHaPerDay } = projection ?? {};
  const { profitProjectionData, plannedHarvestIdx } = harvestProjection ?? {};

  const plannedProfitProjection = profitProjectionData?.[plannedHarvestIdx]?.[harvestType as 'headon'];

  const {
    totalCosts: harvestTotalCosts,
    costPerPound: harvestCostPerPound,
    costPoundHarvest: harvestCostPoundHarvest,
    stockingCosts: harvestStockingCosts,
    cumulativeFeedCosts: harvestCumulativeFeedCosts,
    feedCostPerKg: harvestFeedCostPerKg,
    cumulativeOverheadCosts: harvestCumulativeOverheadCosts,
    overheadCostDryDays: harvestOverheadCostsDryDays,
    overheadCostProductiveDays: harvestOverheadCostsProductiveDays,
    accumulatedOtherDirectCosts: harvestAccumulatedOtherDirectCosts
  } = harvest ?? {};

  const productionPredictionOnPlannedHarvest = productionPrediction?.find((item) => {
    return item.date === harvestPlanDate;
  });

  const {
    totalCosts: plannedTotalCosts,
    costPoundHarvest: plannedCostPoundHarvest,
    stockingCosts: plannedStockingCosts,
    cumulativeFeedCosts: plannedCumulativeFeedCosts,
    feedCostPerKg: plannedFeedCostPerKg,
    cumulativeOverheadCosts: plannedCumulativeOverheadCosts,
    overheadCostDryDays: plannedOverheadCostsDryDays,
    overheadCostProductiveDays: plannedOverheadCostsProductiveDays,
    accumulatedOtherDirectCosts: plannedAccumulatedOtherDirectCosts
  } = productionPredictionOnPlannedHarvest ?? {};

  const plannedCostPerPound = plannedProfitProjection?.costPerPound;

  const lastHistoryOnPlanDate = history?.[0]?.date === harvestPlan?.harvestDate ? history?.[0] : undefined;

  const {
    totalCosts: historyTotalCosts,
    costPoundHarvest: historyCostPoundHarvest,
    costPerPound: historyCostPerPound,
    stockingCosts: historyStockingCosts,
    cumulativeFeedCosts: historyCumulativeFeedCosts,
    feedCostPerKg: historyFeedCostPerKg,
    cumulativeOverheadCosts: historyCumulativeOverheadCosts,
    overheadCostDryDays: historyOverheadCostsDryDays,
    overheadCostProductiveDays: historyOverheadCostsProductiveDays,
    accumulatedOtherDirectCosts: historyAccumulatedOtherDirectCosts
  } = lastHistoryOnPlanDate ?? {};

  const stockedAtDateLuxon = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone ?? timezone });
  const targetHarvestDateFormatted = stockedAtDateLuxon.plus({ days: cycleLength }).toFormat('yyyy-MM-dd');

  const targetCycleComparison = productionCycleComparison?.find((item) => {
    return item.date === targetHarvestDateFormatted;
  });

  const {
    totalCosts: targetTotalCosts,
    costPerPound: targetCostPerPound,
    costPoundHarvest: targetCostPoundHarvest,
    stockingCosts: targetStockingCosts,
    cumulativeFeedCosts: targetCumulativeFeedCosts,
    overheadCostDryDays: targetOverheadCostsDryDays,
    overheadCostProductiveDays: targetOverheadCostsProductiveDays,
    feedCostPerKg: targetFeedCostPerKg,
    cumulativeOverheadCosts: targetCumulativeOverheadCosts
  } = targetCycleComparison ?? {};

  const formatValue = (value: number, options: { isConverted?: boolean; fractionDigits?: number } = {}) => {
    const { isConverted = false, fractionDigits = 0 } = options;
    if (!isNumber(value)) return '-';
    if (!isConverted) return formatNumber(value, { fractionDigits, isCurrency: true, lang });
    return formatNumber(convertUnitByMultiplication(value, unitConfigBiomass), {
      fractionDigits,
      isCurrency: true,
      lang
    });
  };

  const values: Record<CostValues, ReactNode> = {
    totalCosts: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_cost_$')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestTotalCosts)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(plannedTotalCosts ?? historyTotalCosts)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetTotalCosts)}</Text>
        </GridItem>
      </Grid>
    ),
    costPerPound: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_cost_per_unit_processed', { unit: unitLabel })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestCostPerPound, { isConverted: true, fractionDigits: 2 })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>
            {formatValue(plannedCostPerPound ?? historyCostPerPound, { isConverted: true, fractionDigits: 2 })}
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetCostPerPound, { isConverted: true, fractionDigits: 2 })}</Text>
        </GridItem>
      </Grid>
    ),
    costPoundHarvest: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_cost_per_unit_harvested', { unit: unitLabel })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestCostPoundHarvest, { isConverted: true, fractionDigits: 2 })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>
            {formatValue(plannedCostPoundHarvest ?? historyCostPoundHarvest, { isConverted: true, fractionDigits: 2 })}
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetCostPoundHarvest, { isConverted: true, fractionDigits: 2 })}</Text>
        </GridItem>
      </Grid>
    ),
    stockingCosts: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_stocking_cost')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestStockingCosts)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(plannedStockingCosts ?? historyStockingCosts)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetStockingCosts)}</Text>
        </GridItem>
      </Grid>
    ),
    cumulativeFeedCosts: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_feed_cost_$')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestCumulativeFeedCosts)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(plannedCumulativeFeedCosts ?? historyCumulativeFeedCosts)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetCumulativeFeedCosts)}</Text>
        </GridItem>
      </Grid>
    ),
    feedCostPerKg: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_feed_cost_per_kg')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestFeedCostPerKg, { fractionDigits: 2 })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>
            {formatValue(plannedFeedCostPerKg ?? historyFeedCostPerKg, { fractionDigits: 2 })}
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetFeedCostPerKg, { fractionDigits: 2 })}</Text>
        </GridItem>
      </Grid>
    ),
    cumulativeOverheadCosts: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_overhead_cost_cumulative')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestCumulativeOverheadCosts)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(plannedCumulativeOverheadCosts ?? historyCumulativeOverheadCosts)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetCumulativeOverheadCosts)}</Text>
        </GridItem>
      </Grid>
    ),
    overheadCostDryDays: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_overhead_cost_dry_days')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestOverheadCostsDryDays)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(plannedOverheadCostsDryDays ?? historyOverheadCostsDryDays)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetOverheadCostsDryDays)}</Text>
        </GridItem>
      </Grid>
    ),
    overheadCostProductiveDays: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_overhead_cost_days_of_culture')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestOverheadCostsProductiveDays)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>
            {formatValue(plannedOverheadCostsProductiveDays ?? historyOverheadCostsProductiveDays)}
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetOverheadCostsProductiveDays)}</Text>
        </GridItem>
      </Grid>
    ),
    overheadCostsPerHaPerDay: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_overhead_cost_ha_day')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetOverheadCostsPerHaPerDay)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetOverheadCostsPerHaPerDay)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetOverheadCostsPerHaPerDay)}</Text>
        </GridItem>
      </Grid>
    ),
    accumulatedOtherDirectCosts: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_other_direct_costs')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestAccumulatedOtherDirectCosts)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>
            {formatValue(plannedAccumulatedOtherDirectCosts ?? historyAccumulatedOtherDirectCosts)}
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>-</Text>
        </GridItem>
      </Grid>
    )
  };

  return values[variable as CostValues];
}

type FeedValues = Extract<CycleSummaryViewVariable, 'totalFeedGivenKg' | 'fcr' | 'adjustedFcr'>;
function FeedSectionRowMapper(props: { variable: CycleSummaryViewVariable; containerProps?: GridProps }) {
  const { variable, containerProps } = props;
  const { trans } = getTrans();

  const lang = useAppSelector((state) => state.app.lang);
  const timezone = useAppSelector((state) => state.farm?.currentFarm?.timezone);
  const currentPopulation = useAppSelector((state) => state.farm?.currentPopulation);
  const {
    harvest,
    productionCycleComparison,
    cycleInformation,
    harvestPlan,
    productionPrediction,
    stockedAt,
    stockedAtTimezone,
    history
  } = currentPopulation ?? {};
  const { harvestDate: harvestPlanDate } = harvestPlan ?? {};
  const { target } = cycleInformation ?? {};
  const { cycleLength } = target ?? {};

  const {
    totalFeedGivenKg: harvestTotalFeedGivenKg,
    cumulativeFcr: harvestCumulativeFcr,
    adjustedFcr: harvestAdjustedFcr
  } = harvest ?? {};

  const productionPredictionOnPlannedHarvest = productionPrediction?.find((item) => {
    return item.date === harvestPlanDate;
  });

  const {
    totalFeedGivenKg: plannedTotalFeedGivenKg,
    cumulativeFcr: plannedCumulativeFcr,
    adjustedFcr: plannedAdjustedFcr
  } = productionPredictionOnPlannedHarvest ?? {};

  const lastHistoryOnPlanDate = history?.[0]?.date === harvestPlan?.harvestDate ? history?.[0] : undefined;

  const {
    totalFeedGivenKg: historyTotalFeedGivenKg,
    cumulativeFcr: historyCumulativeFcr,
    adjustedFcr: historyAdjustedFcr
  } = lastHistoryOnPlanDate ?? {};

  const stockedAtDateLuxon = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone ?? timezone });
  const targetHarvestDateFormatted = stockedAtDateLuxon.plus({ days: cycleLength }).toFormat('yyyy-MM-dd');

  const targetCycleComparison = productionCycleComparison?.find((item) => {
    return item.date === targetHarvestDateFormatted;
  });

  const {
    totalFeedGivenKg: targetTotalFeedGivenKg,
    cumulativeFcr: targetCumulativeFcr,
    adjustedFcr: targetAdjustedFcr
  } = targetCycleComparison ?? {};

  const formatValue = (value: number, fractionDigits = 2) =>
    isNumber(value) ? formatNumber(value, { fractionDigits, lang }) : '-';

  const values: Record<FeedValues, ReactNode> = {
    totalFeedGivenKg: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_feed_given_kg')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestTotalFeedGivenKg, 0)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(plannedTotalFeedGivenKg ?? historyTotalFeedGivenKg, 0)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetTotalFeedGivenKg, 0)}</Text>
        </GridItem>
      </Grid>
    ),
    fcr: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_fcr')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestCumulativeFcr)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(plannedCumulativeFcr ?? historyCumulativeFcr)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetCumulativeFcr)}</Text>
        </GridItem>
      </Grid>
    ),
    adjustedFcr: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_fcr_adjusted')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestAdjustedFcr)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(plannedAdjustedFcr ?? historyAdjustedFcr)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetAdjustedFcr)}</Text>
        </GridItem>
      </Grid>
    )
  };

  return values[variable as FeedValues];
}

type BiomassValues = Extract<
  CycleSummaryViewVariable,
  | 'growthLinear'
  | 'totalBiomassLbs'
  | 'biomassLbsByHa'
  | 'animalsHarvested'
  | 'animalsHarvestedM2'
  | 'animalsHarvestedHa'
  | 'survival'
>;
function BiomassSectionRowMapper(props: { variable: CycleSummaryViewVariable; containerProps?: GridProps }) {
  const { variable, containerProps } = props;
  const { trans } = getTrans();

  const user = useAppSelector((state) => state.auth.user);
  const lang = useAppSelector((state) => state.app.lang);
  const currentPopulation = useAppSelector((state) => state.farm?.currentPopulation);
  const pondSize = useAppSelector((state) => state.farm?.currentPond?.size);
  const timezone = useAppSelector((state) => state.farm?.currentFarm?.timezone);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const unitConfigBiomass = unitsConfig?.biomass;
  const isBiomassUnitLbs = unitConfigBiomass === 'lbs' || isUndefined(unitConfigBiomass);

  const {
    harvest,
    harvestPlan,
    cycleInformation,
    productionPrediction,
    productionCycleComparison,
    stockedAt,
    stockedAtTimezone,
    seedingQuantity,
    history
  } = currentPopulation ?? {};
  const { target } = cycleInformation ?? {};
  const { cycleLength } = target ?? {};

  const {
    growthLinear: harvestGrowthLinear,
    totalBiomassLbs: harvestTotalBiomassLbs,
    totalBiomassLbsByHa: harvestTotalBiomassLbsByHa,
    totalAnimalsHarvested: harvestTotalAnimalsHarvested,
    animalsHarvestedM2: harvestAnimalsHarvestedM2,
    animalsHarvestedHa: harvestAnimalsHarvestedHa,
    survival: harvestSurvival
  } = harvest ?? {};

  const { harvestDate: harvestPlanDate } = harvestPlan ?? {};

  const productionPredictionOnPlannedHarvest = productionPrediction?.find((item) => {
    return item.date === harvestPlanDate;
  });

  const {
    growthLinear: plannedGrowthLinear,
    totalBiomassLbs: plannedTotalBiomassLbs,
    totalBiomassLbsByHa: plannedTotalBiomassLbsByHa,
    totalAnimalsHarvested,
    animalsRemainingM2,
    animalsRemainingHa,
    animalsHarvestedM2,
    animalsHarvestedHa
  } = productionPredictionOnPlannedHarvest ?? {};

  const plannedAnimalsHarvestedM2 = productionPredictionOnPlannedHarvest
    ? (animalsHarvestedM2 ?? 0) + (animalsRemainingM2 ?? 0)
    : undefined;
  const plannedAnimalsHarvestedHa = productionPredictionOnPlannedHarvest
    ? (animalsHarvestedHa ?? 0) + (animalsRemainingHa ?? 0)
    : undefined;

  const totalAnimalsRemaining = animalsRemainingHa && pondSize ? animalsRemainingHa * pondSize : undefined;
  const plannedTotalAnimalsHarvested = productionPredictionOnPlannedHarvest
    ? totalAnimalsHarvested + totalAnimalsRemaining
    : undefined;

  // planned survival is calculated as the total animals harvested divided by the seeding quantity including harvested animals
  const plannedSurvival =
    seedingQuantity > 0 && plannedTotalAnimalsHarvested > 0
      ? plannedTotalAnimalsHarvested / seedingQuantity
      : undefined;

  const lastHistoryOnPlanDate = history?.[0]?.date === harvestPlan?.harvestDate ? history?.[0] : undefined;

  const {
    growthLinear: historyGrowthLinear,
    totalBiomassLbs: historyTotalBiomassLbs,
    totalBiomassLbsByHa: historyTotalBiomassLbsByHa
  } = lastHistoryOnPlanDate ?? {};

  const historyAnimalsHarvestedM2 =
    (lastHistoryOnPlanDate?.animalsHarvestedM2 ?? 0) + (lastHistoryOnPlanDate?.animalsRemainingM2 ?? 0);
  const historyAnimalsHarvestedHa =
    (lastHistoryOnPlanDate?.animalsHarvestedHa ?? 0) + (lastHistoryOnPlanDate?.animalsRemainingHa ?? 0);

  const historyTotalAnimalsRemaining =
    lastHistoryOnPlanDate?.animalsRemainingHa && pondSize ? lastHistoryOnPlanDate?.animalsRemainingHa * pondSize : 0;
  const historyTotalAnimalsHarvested =
    (lastHistoryOnPlanDate?.totalAnimalsHarvested ?? 0) + historyTotalAnimalsRemaining;

  // planned survival is calculated as the total animals harvested divided by the seeding quantity including harvested animals
  const historySurvival =
    seedingQuantity > 0 && historyTotalAnimalsHarvested > 0
      ? historyTotalAnimalsHarvested / seedingQuantity
      : undefined;

  const stockedAtDateLuxon = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone ?? timezone });
  const targetHarvestDateFormatted = stockedAtDateLuxon.plus({ days: cycleLength }).toFormat('yyyy-MM-dd');

  const targetCycleComparison = productionCycleComparison?.find((item) => {
    return item.date === targetHarvestDateFormatted;
  });

  const {
    growthLinear: targetGrowthLinear,
    totalBiomassLbs: targetTotalBiomassLbs,
    totalBiomassLbsByHa: targetTotalBiomassLbsByHa,
    animalsRemainingM2: targetAnimalsHarvestedM2,
    animalsRemainingHa: targetAnimalsHarvestedHa
  } = targetCycleComparison ?? {};

  const targetTotalAnimalsHarvested = isNumber(targetAnimalsHarvestedHa) ? targetAnimalsHarvestedHa * pondSize : null;

  // target survival is calculated as the total animals harvested divided by the seeding quantity including harvested animals
  const targetSurvival = seedingQuantity > 0 ? targetTotalAnimalsHarvested / seedingQuantity : 0;

  const formatValue = (
    value: number,
    options: { isConverted?: boolean; isPercentage?: boolean; fractionDigits?: number } = {}
  ) => {
    const { isConverted = false, isPercentage = false, fractionDigits = 0 } = options;
    if (!isNumber(value)) return '-';
    if (!isConverted) return formatNumber(value, { fractionDigits, isPercentage, lang });
    return formatNumber(convertUnitByDivision(value, unitConfigBiomass), { fractionDigits, lang });
  };

  const viewVariable = variable as BiomassValues;
  const values: Record<BiomassValues, ReactNode> = {
    growthLinear: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_growth_g_wk')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestGrowthLinear, { fractionDigits: 1 })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(plannedGrowthLinear ?? historyGrowthLinear, { fractionDigits: 1 })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetGrowthLinear, { fractionDigits: 1 })}</Text>
        </GridItem>
      </Grid>
    ),
    totalBiomassLbs: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{isBiomassUnitLbs ? trans('t_biomass_lb') : trans('t_biomass_kg')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestTotalBiomassLbs, { isConverted: true })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>
            {formatValue(plannedTotalBiomassLbs ?? historyTotalBiomassLbs, { isConverted: true })}
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetTotalBiomassLbs, { isConverted: true })}</Text>
        </GridItem>
      </Grid>
    ),
    biomassLbsByHa: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{isBiomassUnitLbs ? trans('t_biomass_lb_ha') : trans('t_biomass_kg_ha')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestTotalBiomassLbsByHa, { isConverted: true })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>
            {formatValue(plannedTotalBiomassLbsByHa ?? historyTotalBiomassLbsByHa, { isConverted: true })}
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetTotalBiomassLbsByHa, { isConverted: true })}</Text>
        </GridItem>
      </Grid>
    ),
    animalsHarvested: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_animals_harvested')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestTotalAnimalsHarvested)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(plannedTotalAnimalsHarvested ?? historyTotalAnimalsHarvested)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetTotalAnimalsHarvested)}</Text>
        </GridItem>
      </Grid>
    ),
    animalsHarvestedM2: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_animals_harvested_per_m2')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestAnimalsHarvestedM2, { fractionDigits: 2 })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>
            {formatValue(plannedAnimalsHarvestedM2 ?? historyAnimalsHarvestedM2, { fractionDigits: 2 })}
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetAnimalsHarvestedM2, { fractionDigits: 2 })}</Text>
        </GridItem>
      </Grid>
    ),
    animalsHarvestedHa: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_animals_harvested_ha')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestAnimalsHarvestedHa)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(plannedAnimalsHarvestedHa ?? historyAnimalsHarvestedHa)}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetAnimalsHarvestedHa)}</Text>
        </GridItem>
      </Grid>
    ),
    survival: (
      <Grid
        templateColumns='180px 1fr 1fr 1fr'
        gap='lg'
        p='sm-alt'
        borderBottom='0.5px solid'
        borderBottomColor='gray.200'
        {...containerProps}
      >
        <GridItem>
          <Text size='label200'>{trans('t_survival_include_harvests')}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(harvestSurvival, { isPercentage: true, fractionDigits: 1 })}</Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>
            {formatValue(plannedSurvival ?? historySurvival, { isPercentage: true, fractionDigits: 1 })}
          </Text>
        </GridItem>
        <GridItem>
          <Text size='label200'>{formatValue(targetSurvival, { isPercentage: true, fractionDigits: 1 })}</Text>
        </GridItem>
      </Grid>
    )
  };

  return values[viewVariable];
}
