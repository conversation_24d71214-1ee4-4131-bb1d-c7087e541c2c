import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { useMemo } from 'react';
import { Box, Flex, Text } from '@chakra-ui/react';
import { HeadsOnAndOffChart } from '@screens/monitoring/components/heads-on-and-off-chart';
import { PopulationHarvest, PopulationHarvestPlan, PopulationPartialHarvest } from '@xpertsea/module-farm-sdk';
import { PartialPageLoader } from '@components/loaders/partial-page-loader';
import isUndefined from 'lodash/isUndefined';
import {
  ClassificationBreakdownTable,
  ClassificationType
} from '@screens/pond/components/harvest-plan/classification-breakdown-table';
import { getHarvestTypeText } from '@screens/pond/helpers/harvest-plan';
import { useGetProcessorOptions } from '@screens/pond/hooks/use-get-processor-options';

export type ClassificationBreakdownProps = {
  isLoading: boolean;
  classification: string;
  isPartialHarvest?: boolean;
  harvestPlan?: PopulationHarvestPlan;
  harvest?: PopulationHarvest;
  isRecordedView?: boolean;
  isRecordedPartialHarvest?: boolean;
  partialHarvest?: PopulationPartialHarvest;
  biomassLbs: number;
  biomassKg: number;
  weightDistribution: number[];
};

export function ClassificationBreakdown(props: ClassificationBreakdownProps) {
  const {
    isLoading,
    harvestPlan,
    harvest,
    partialHarvest,
    classification,
    isPartialHarvest = false,
    biomassLbs,
    biomassKg,
    isRecordedView,
    isRecordedPartialHarvest,
    weightDistribution
  } = props;

  const { trans } = getTrans();

  const { processorOptions } = useGetProcessorOptions();
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);
  const user = useAppSelector((state) => state.auth.user);

  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);

  const { harvestTypeText, commercialClassBreakdownData, percentTail } = useMemo(() => {
    const harvestProcessorId = isRecordedView ? harvest?.processorId : harvestPlan?.selectedProcessorId;
    const harvestProcessorPriceList = isRecordedView ? harvest?.processorPriceList : harvestPlan?.processorPriceList;
    const harvestCommercialClassBreakdown = isRecordedView
      ? harvest?.commercialClassBreakdown
      : harvestPlan?.commercialClassBreakdown;

    const processorId = isPartialHarvest ? partialHarvest?.processorId : harvestProcessorId;
    const processorPriceList = isPartialHarvest ? partialHarvest?.processorPriceList : harvestProcessorPriceList;

    const {
      defaultHarvestType: harvestType,
      percentHeadlessAsClassA,
      percentHarvestAsHeadsOn,
      percentTail
    } = processorPriceList ?? {};

    const commercialClassBreakdownData = isPartialHarvest
      ? partialHarvest?.commercialClassBreakdown
      : harvestCommercialClassBreakdown;

    const harvestTypeText = getHarvestTypeText({
      harvestType,
      processorId,
      processorOptions,
      percentHeadlessAsClassA,
      percentHarvestAsHeadsOn: percentHarvestAsHeadsOn
    });

    return { commercialClassBreakdownData, harvestTypeText, percentTail };
  }, [
    harvestPlan,
    harvest,
    currentFarm,
    partialHarvest,
    classification,
    isPartialHarvest,
    biomassLbs,
    biomassKg,
    weightDistribution
  ]);
  if (isLoading) return <PartialPageLoader showLoadingText />;
  const isRecorded = isRecordedPartialHarvest || isRecordedView;
  const harvestTitle = isRecorded ? trans('t_recorded') : trans('t_plan');

  return (
    <>
      <Flex
        p='md'
        gap='lg'
        rounded='2xl'
        bgColor='gray.100'
        direction='column'
        css={{ '& .highcharts-background': { fill: 'none' } }}
      >
        <Flex align='center' justify='space-between'>
          <Text size='label300' color='text.gray.weak'>
            {trans('t_harvest_type_classification', { harvestType: harvestTypeText })}
          </Text>
          <Flex gap='xs' align='center' css={{ span: { display: 'flex' } }}>
            <Box w='12px' h='12px' bg={isRecorded ? 'graphBrandBlue' : 'shrimpyPinky.600'} rounded='base' />
            <Text size='label300' color='text.gray.weak'>
              {harvestTitle}
            </Text>
          </Flex>
        </Flex>
        <HeadsOnAndOffChart
          height={288}
          isReversed={classification === 'headon'}
          percentTail={percentTail}
          wd={weightDistribution}
          type={classification === 'headon' ? 'headsOn' : 'headsOff'}
          customConfig={{ hideTitle: true, barColor: isRecorded ? '#576CFF' : '#FF755B' }}
        />
      </Flex>
      <Text mt='md' mb='md' size='label100'>
        {classification === 'headon' ? trans('t_head_on') : trans('t_headless')}
      </Text>
      <ClassificationBreakdownTable
        unit={isBiomassUnitLbs ? 'lbs' : 'kg'}
        unitsConfig={unitsConfig}
        isUnitSwitchVisible={false}
        type={classification as ClassificationType}
        commercialClassBreakdownData={commercialClassBreakdownData}
      />
    </>
  );
}
