import { useAppSelector } from '@redux/hooks';
import { Flex, FlexProps } from '@chakra-ui/react';
import { ReactNode } from 'react';
import { formatNumber } from '@utils/number';
import { DaysSinceDate } from '@components/farm-days-since-monitored/days-since-date';
import { FarmDaysBetweenMonitoringAndStocking } from '@components/farm-days-since-stocked/farm-days-between-monitoring-and-stocking';
import { CalendarFilledIcon } from '@icons/calendar/calendar-filled-icon';
import { getTrans } from '@i18n/get-trans';
import { CropIcon } from '@icons/crop-icon';

export function PondInfoBar(props: FlexProps) {
  const { currentPond, currentFarm, currentPopulation } = useAppSelector((state) => state.farm);
  const { size: pondSize } = currentPond ?? {};
  const { timezone } = currentFarm ?? {};

  const { lastMonitoringDate, stockedAt } = currentPopulation ?? {};

  return (
    <Flex flexWrap='wrap' align='center' gap='md-alt' {...props}>
      <DaysSinceLastMonitoringLabel date={lastMonitoringDate} timezone={timezone} />
      <DaysFromMonitorToStockLabel stockedAt={stockedAt} lastMonitoringDate={lastMonitoringDate} timezone={timezone} />
      <PondSizeLabel pondSize={pondSize} />
    </Flex>
  );
}

function PondSizeLabel({ pondSize }: { pondSize: number }) {
  const lang = useAppSelector((state) => state.app.lang);
  const { trans } = getTrans();
  if (!pondSize) return null;

  return (
    <LabelContainer icon={<CropIcon />}>
      {`${formatNumber(pondSize, { lang, fractionDigits: 2, shouldRound: false })}${trans('t_ha')}`}
    </LabelContainer>
  );
}

function DaysSinceLastMonitoringLabel({ date, timezone }: { date: string; timezone: string }) {
  if (!date) return null;

  return (
    <LabelContainer>
      <DaysSinceDate date={date} timezone={timezone} />
    </LabelContainer>
  );
}

type DaysFromMonitorToStockLabelProps = {
  stockedAt: string;
  lastMonitoringDate: string;
  timezone: string;
};

function DaysFromMonitorToStockLabel(props: DaysFromMonitorToStockLabelProps) {
  const { stockedAt, lastMonitoringDate, timezone } = props ?? {};

  if (!lastMonitoringDate) return null;

  return (
    <LabelContainer icon={<CalendarFilledIcon />}>
      <FarmDaysBetweenMonitoringAndStocking
        stockedAt={stockedAt}
        farmTimezone={timezone}
        monitoredAt={lastMonitoringDate}
      />
    </LabelContainer>
  );
}

export function LabelContainer(props: FlexProps & { icon?: ReactNode; iconHasBackground?: boolean }) {
  const { children, icon, iconHasBackground, ...rest } = props;
  return (
    <Flex gap='sm-alt' align='center' textStyle='label.200' {...rest}>
      {icon}
      {children}
    </Flex>
  );
}
