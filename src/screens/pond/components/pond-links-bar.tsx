import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { useRouter } from 'next/router';
import { getPondState } from '@screens/pond/helpers/get-pond-state';
import { getDataTabs } from '@screens/pond/helpers/pond-details-tabs';
import { Flex } from '@chakra-ui/react';
import { actionsName } from '@utils/segment';
import { BaseButton, BaseButtonProps } from '@components/base/base-button';
import { BaseLink, BaseLinkProps } from '@components/base/base-link';
import { PondActions } from '@screens/pond/components/pond-actions';
import { PondInfoBar } from '@screens/pond/components/pond-info-bar';
import { getPondActiveTab } from '@screens/pond/helpers/pond-links-bar';
import { useEnableTechnicalFeatures } from '@hooks/use-is-xpertsea-user';
import { VisionOnlyGuard } from '@components/permission-wrappers/vision-only-guard';
import { HideOnMobileView } from '@components/mobile-view/hide-on-mobile-view';

export function PondLinksBar({ isInPondView }: { isInPondView?: boolean }) {
  return (
    <Flex align='center' justify='space-between' flexWrap='wrap' gap='md-alt'>
      <Flex gap='md-alt' align='center' flexWrap='wrap'>
        <HideOnMobileView>
          <PondLinks />
        </HideOnMobileView>
        <PondInfoBar />
      </Flex>
      <PondActions isInPondView={isInPondView} />
    </Flex>
  );
}

function PondLinks() {
  const { trans } = getTrans();
  const { route, query } = useRouter();
  const currentPopulation = useAppSelector((state) => state.farm?.currentPopulation);
  const enableTechnicalFeatures = useEnableTechnicalFeatures();
  const productOffering = useAppSelector((state) => state.farm?.currentFarm?.metadata?.productOffering);

  const isVisionOnly = productOffering === 'visionOnly';

  const { isNewPond } = getPondState({ population: currentPopulation });

  if (isNewPond) return null;

  const activeTab = getPondActiveTab(route, query);

  return (
    <Flex rounded='full' p='3xs' bg='bg.gray.strong' data-cy='pond-details-tabs'>
      <LinkButton
        analyticsId={actionsName.pondOverviewTabClicked}
        analyticsData={{ tab: 'overview' }}
        linkProps={{
          route,
          params: { ...query, tab: undefined }
        }}
        _focus={{ outline: 'none' }}
        isActive={activeTab === 'isOverview'}
        data-cy='pond-overview-tab-btn'
      >
        {trans('t_overview')}
      </LinkButton>

      <VisionOnlyGuard>
        <LinkButton
          analyticsId={actionsName.pondOverviewTabClicked}
          analyticsData={{ tab: 'plan' }}
          linkProps={{
            route,
            params: { ...query, tab: 'plan' }
          }}
          isActive={activeTab === 'isPlan'}
          _focus={{ outline: 'none' }}
          data-cy='pond-harvest-plan-tab-btn'
        >
          {trans('t_harvest_plan')}
        </LinkButton>
      </VisionOnlyGuard>

      <LinkButton
        analyticsId={actionsName.pondOverviewTabClicked}
        analyticsData={{ tab: 'data' }}
        linkProps={{
          route,
          params: { ...query, tab: getDataTabs(isVisionOnly)[0] }
        }}
        _focus={{ outline: 'none' }}
        isActive={activeTab === 'isData'}
        data-cy='pond-data-tab-btn'
      >
        {trans('t_data')}
      </LinkButton>

      <VisionOnlyGuard>
        {enableTechnicalFeatures && (
          <LinkButton
            linkProps={{
              route,
              params: { ...query, tab: 'sandbox' }
            }}
            _focus={{ outline: 'none' }}
            isActive={activeTab === 'isSandbox'}
            data-cy='pond-sandbox-tab-btn'
            display={{ base: 'none', lg: 'flex' }}
          >
            {trans('t_sandbox')}
          </LinkButton>
        )}
      </VisionOnlyGuard>
    </Flex>
  );
}
type LinkButtonProps = BaseButtonProps & { isActive: boolean; linkProps: BaseLinkProps };

function LinkButton({ isActive, children, linkProps, ...rest }: LinkButtonProps) {
  return (
    <BaseButton
      rounded='full'
      h='36px'
      px='2sm'
      w='max-content'
      {...(!isActive && { color: 'text.gray.disabled', bgColor: 'transparent' })}
      {...(isActive && {
        bgColor: 'bg.squidInkPowder.down',
        color: 'white'
      })}
      {...rest}
      asChild
    >
      <BaseLink {...linkProps}>{children}</BaseLink>
    </BaseButton>
  );
}
