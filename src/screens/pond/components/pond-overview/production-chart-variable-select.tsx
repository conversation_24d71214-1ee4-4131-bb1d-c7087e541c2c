import { Box, Flex, Input } from '@chakra-ui/react';
import {
  ProductionChartVariableMap,
  ProductionChartVariableOption
} from '@screens/pond/components/pond-overview/hooks/use-production-chart-variables';
import { useAppSelector } from '@redux/hooks';
import { usePermission } from '@hooks/use-permission';
import { getTrans } from '@i18n/get-trans';
import { ChevronUpFilled } from '@icons/chevron-up/chevron-up-filled';
import { ChevronDownFilled } from '@icons/chevron-down/chevron-down-filled';
import { useState } from 'react';
import { SearchDuoIcon } from '@icons/search/search-duo-icon';
import { ChartVarsList } from '@screens/pond/components/pond-overview/helpers/pond-production-chart-helpers';

import { MenuButton, MenuContent, MenuItem, MenuRoot } from '@components/ui/menu';
import { InputGroup } from '@components/ui/input-group';
import { shouldHideVariable } from '@screens/pond/helpers/production-chart-select-values-permissions';
import { CloseFilled } from '@icons/close/close-filled';

interface ProductionChartVariableSelectProps {
  onVariableChange(variable: 'chartVariableOne' | 'chartVariableTwo', value: ProductionChartVariableOption): void;

  chartVariableOne: ProductionChartVariableOption;
  chartVariableTwo: ProductionChartVariableOption;
  chartVariableMap: ProductionChartVariableMap;
  chartSelectedVars: ChartVarsList;
  isDisabled?: boolean;
}

export function ProductionChartVariableSelect(props: ProductionChartVariableSelectProps) {
  const { onVariableChange, chartVariableMap, chartVariableTwo, chartVariableOne, chartSelectedVars, isDisabled } =
    props;
  const currentFarmId = useAppSelector((state) => state.farm?.currentFarm?._id);
  const productOffering = useAppSelector((state) => state.farm?.currentFarm?.metadata?.productOffering);

  const isVisionOnly = productOffering === 'visionOnly';
  const { trans } = getTrans();

  const [firstVariableSearch, setFirstVariableSearch] = useState('');
  const [secondVariableSearch, setSecondVariableSearch] = useState('');

  const [firstMenuOpen, setFirstMenuOpen] = useState(false);
  const [secondMenuOpen, setSecondMenuOpen] = useState(false);

  const isAdmin = usePermission({
    role: 'admin',
    entity: 'farm',
    entityId: currentFarmId
  });

  const isSupervisor = usePermission({
    role: 'supervisor',
    entity: 'farm',
    entityId: currentFarmId
  });

  const variableOneLabel = chartVariableMap[chartVariableOne]?.title ?? trans('t_select_option');
  const variableTwoLabel = chartVariableMap[chartVariableTwo]?.title ?? trans('t_select_option');

  const variableKeys = Object.keys(chartVariableMap) as ProductionChartVariableOption[];

  const chartFirstVariableList = variableKeys.filter((item) => {
    const isSearched =
      firstVariableSearch.length > 1
        ? chartVariableMap[item]?.title?.toLowerCase()?.includes(firstVariableSearch.toLowerCase())
        : true;
    return (
      item !== chartVariableTwo && !shouldHideVariable(item, { isAdmin, isSupervisor, isVisionOnly }) && isSearched
    );
  });

  const chartSecondVariableList = variableKeys.filter((item) => {
    const isSearched =
      secondVariableSearch.length > 1
        ? chartVariableMap[item]?.title?.toLowerCase()?.includes(secondVariableSearch.toLowerCase())
        : true;
    return (
      item !== chartVariableOne && !shouldHideVariable(item, { isAdmin, isSupervisor, isVisionOnly }) && isSearched
    );
  });

  const hasMoreThanOneItemSelected = chartSelectedVars.length > 1;
  const hasMoreThanOrEqualTwoItemSelected = chartSelectedVars.length >= 2;
  return (
    <Flex flexWrap='wrap' align='center' gap='md-alt'>
      <MenuRoot
        onOpenChange={(value) => {
          setFirstMenuOpen(value.open);
          if (!value.open) {
            setFirstVariableSearch('');
          }
        }}
        positioning={{ placement: 'bottom-start' }}
        highlightedValue={chartVariableOne}
      >
        <MenuButton
          size='sm'
          variant='link'
          color='text.gray.weak'
          _active={{ color: 'text.gray.weak' }}
          _hover={{ textDecoration: 'none', color: 'text.gray.weak' }}
          disabled={isDisabled || (hasMoreThanOrEqualTwoItemSelected && !chartVariableOne)}
        >
          <Box boxSize='12px' rounded='base' bgColor='bg.shrimpyPinky' />
          <Box as='span' color={chartVariableOne ? 'primary' : 'gray.500'}>
            {variableOneLabel}
          </Box>
          <Box as='span'>
            {hasMoreThanOneItemSelected && chartVariableOne ? (
              <CloseFilled
                w='20px'
                h='20px'
                bgColor='transparent'
                onClick={(e) => {
                  e.stopPropagation();
                  onVariableChange('chartVariableOne', undefined);
                }}
              />
            ) : (
              <>
                {firstMenuOpen && (
                  <ChevronUpFilled w='20px' h='20px' className='chevron-icon' hasBackground={false} color='icon.gray' />
                )}
                {!firstMenuOpen && (
                  <ChevronDownFilled
                    w='20px'
                    h='20px'
                    className='chevron-icon'
                    hasBackground={false}
                    color='icon.gray'
                  />
                )}
              </>
            )}
          </Box>
        </MenuButton>
        <MenuContent minW='214px' maxH='55vh' overflowY='auto' portalled={false}>
          <InputGroup
            w='full'
            startElement={<SearchDuoIcon pointerEvents='none' />}
            endElement={
              firstVariableSearch && (
                <CloseFilled hasBackground={false} cursor='pointer' onClick={() => setFirstVariableSearch('')} />
              )
            }
          >
            <Input
              value={firstVariableSearch}
              borderRadius='full'
              fontSize='lg'
              outline='none'
              autoFocus
              autoComplete='off'
              placeholder={trans('t_search')}
              _placeholder={{ color: 'text.gray.disabled' }}
              onChange={(e) => {
                setFirstVariableSearch(e.target.value);
                e.stopPropagation();
                e.target.focus();
              }}
            />
          </InputGroup>
          {chartFirstVariableList.map((item, index) => {
            const isFirstItem = index === 0;

            return (
              <MenuItem
                autoFocus={false}
                key={item}
                value={item}
                {...(isFirstItem && { mt: 'xs-alt' })}
                onClick={() => onVariableChange('chartVariableOne', item)}
              >
                {chartVariableMap[item]?.title ?? item}
              </MenuItem>
            );
          })}
        </MenuContent>
      </MenuRoot>

      <MenuRoot
        onOpenChange={(value) => {
          setSecondMenuOpen(value.open);
          if (!value.open) {
            setSecondVariableSearch('');
          }
        }}
        positioning={{ placement: 'bottom-start' }}
        highlightedValue={chartVariableTwo}
      >
        <MenuButton
          size='sm'
          variant='link'
          color='text.gray.weak'
          _active={{ color: 'text.gray.weak' }}
          _hover={{ textDecoration: 'none', color: 'text.gray.weak' }}
          disabled={isDisabled || (hasMoreThanOrEqualTwoItemSelected && !chartVariableTwo)}
        >
          <Box boxSize='12px' rounded='base' bgColor='bg.brandBlue.profile' />
          <Box as='span' color={chartVariableTwo ? 'primary' : 'gray.500'}>
            {variableTwoLabel}
          </Box>
          <Box as='span'>
            {hasMoreThanOneItemSelected && chartVariableTwo ? (
              <CloseFilled
                w='20px'
                h='20px'
                bgColor='transparent'
                onClick={(e) => {
                  e.stopPropagation();
                  onVariableChange('chartVariableTwo', undefined);
                }}
              />
            ) : (
              <>
                {secondMenuOpen && (
                  <ChevronUpFilled w='20px' h='20px' className='chevron-icon' hasBackground={false} color='icon.gray' />
                )}
                {!secondMenuOpen && (
                  <ChevronDownFilled
                    w='20px'
                    h='20px'
                    className='chevron-icon'
                    hasBackground={false}
                    color='icon.gray'
                  />
                )}
              </>
            )}
          </Box>
        </MenuButton>
        <MenuContent maxH='55vh' overflowY='auto' portalled={false}>
          <InputGroup
            w='full'
            startElement={<SearchDuoIcon pointerEvents='none' />}
            endElement={
              secondVariableSearch && (
                <CloseFilled hasBackground={false} cursor='pointer' onClick={() => setSecondVariableSearch('')} />
              )
            }
          >
            <Input
              value={secondVariableSearch}
              borderRadius='full'
              fontSize='lg'
              outline='none'
              autoFocus
              autoComplete='off'
              placeholder={trans('t_search')}
              _placeholder={{ color: 'text.gray.disabled' }}
              onChange={(e) => {
                setSecondVariableSearch(e.target.value);
                e.stopPropagation();
                e.target.focus();
              }}
            />
          </InputGroup>
          <MenuItem value='none' display='none' />
          {chartSecondVariableList.map((item, index) => {
            const isFirstItem = index === 0;

            return (
              <MenuItem
                autoFocus={false}
                key={item}
                value={item}
                {...(isFirstItem && { mt: 'xs-alt' })}
                onClick={() => onVariableChange('chartVariableTwo', item)}
              >
                {chartVariableMap[item]?.title ?? item}
              </MenuItem>
            );
          })}
        </MenuContent>
      </MenuRoot>
    </Flex>
  );
}
