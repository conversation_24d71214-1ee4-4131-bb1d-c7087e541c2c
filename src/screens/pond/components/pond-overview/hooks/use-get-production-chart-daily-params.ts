import { setCacheItem, sevenDaysCacheTime } from '@utils/local-cache';
import { useState } from 'react';
import { useAppSelector } from '@redux/hooks';
import { FarmDailyParameters, PopulationDailyParameters } from '@xpertsea/module-farm-sdk';
import { DateTime } from 'luxon';
import { getDaysDiffBetweenDates } from '@screens/farm/helpers/farm-dates';
import { isNumber } from '@utils/number';

type GenerateSeriesArgs = {
  dailyParameters: PopulationDailyParameters[];
  selectedParam: FarmDailyParameters;
  stockedAt: string;
  stockedAtTimezone: string;
  timezone: string;
};

function generateSeries(args: GenerateSeriesArgs) {
  const { dailyParameters, selectedParam, stockedAt, stockedAtTimezone, timezone } = args;
  if (!dailyParameters?.length || !selectedParam) return { series: [], maxValue: 0, minValue: 0 };

  let maxValue: number = null;
  let minValue: number = null;
  const series = dailyParameters.map(({ date, params }) => {
    const selectedDailyParam = params.find((param) => param.paramId === selectedParam._id);

    const stockedAtLuxon = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone ?? timezone }).startOf('day');
    const dateLuxon = DateTime.fromISO(date, { zone: timezone }).startOf('day');
    const cycleDays = getDaysDiffBetweenDates({
      baseDate: dateLuxon.toFormat('yyyy-MM-dd'),
      dateToCompare: stockedAtLuxon.toFormat('yyyy-MM-dd')
    });

    const yValue = selectedDailyParam?.value ?? null;
    if (isNumber(yValue)) {
      maxValue = maxValue === null ? yValue : Math.max(maxValue, yValue);
      minValue = minValue === null ? yValue : Math.min(minValue, yValue);
    }

    return {
      x: cycleDays,
      y: yValue,
      custom: {
        dateLuxon,
        name: selectedDailyParam ? selectedParam.name : '',
        isDailyParam: true
      }
    };
  });
  return { series, maxValue: maxValue ?? 0, minValue: minValue ?? 0 };
}

type useGetProductionChartDailyParamsProps = {
  defaultValue?: { variableOne?: FarmDailyParameters; variableTwo?: FarmDailyParameters };
};

export function useGetProductionChartDailyParams(props: useGetProductionChartDailyParamsProps) {
  const { defaultValue } = props;
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);
  const currentPopulation = useAppSelector((state) => state.farm.currentPopulation);
  const { dailyParameters, timezone } = currentFarm ?? {};
  const { stockedAt, stockedAtTimezone, dailyParameters: populationDailyParameters } = currentPopulation ?? {};

  const activeDailyParameters = dailyParameters?.filter((param) => param.status === 'active' && param.isEnabled) ?? [];

  const [chartDailyParamVariableOne, setChartDailyParamVariableOne] = useState<FarmDailyParameters>(
    defaultValue?.variableOne
  );
  const [chartDailyParamVariableTwo, setChartDailyParamVariableTwo] = useState<FarmDailyParameters>(
    defaultValue?.variableTwo
  );

  const sortedDailyParameters = [...populationDailyParameters].sort((a, b) => {
    return DateTime.fromISO(a.date).toMillis() - DateTime.fromISO(b.date).toMillis();
  });

  const dailyParamFirstSeries = generateSeries({
    dailyParameters: sortedDailyParameters,
    selectedParam: chartDailyParamVariableOne,
    stockedAt,
    stockedAtTimezone,
    timezone
  });
  const dailyParamSecondSeries = generateSeries({
    dailyParameters: sortedDailyParameters,
    selectedParam: chartDailyParamVariableTwo,
    stockedAt,
    stockedAtTimezone,
    timezone
  });

  const handleChangeChartDailyParamVariableOne = (value: FarmDailyParameters) => {
    setChartDailyParamVariableOne(value);
    setCacheItem('pondProductionChartDailyParamVariableOne', value, sevenDaysCacheTime).then();
  };

  const handleChangeChartDailyParamVariableTwo = (value: FarmDailyParameters) => {
    setChartDailyParamVariableTwo(value);
    setCacheItem('pondProductionChartDailyParamVariableTwo', value, sevenDaysCacheTime).then();
  };

  return {
    activeDailyParameters,
    dailyParamFirstSeries,
    dailyParamSecondSeries,
    chartDailyParamVariableOne,
    chartDailyParamVariableTwo,
    handleChangeChartDailyParamVariableOne,
    handleChangeChartDailyParamVariableTwo
  };
}
