import { getTrans } from '@i18n/get-trans';
import { getCacheItem, setCacheItem, sevenDaysCacheTime } from '@utils/local-cache';
import { useEffect, useState } from 'react';
import { useAppSelector } from '@redux/hooks';
import isUndefined from 'lodash/isUndefined';
import { convertUnitByDivision, convertUnitByMultiplication } from '@utils/number';
import { shouldHideVariable } from '@screens/pond/helpers/production-chart-select-values-permissions';

export type ProductionChartVariableOption =
  | 'averageWeight'
  | 'growthLinear'
  | 'growthDaily'
  | 'weeklyGrowth'
  | 'growthTwoWeeks'
  | 'growthThreeWeeks'
  | 'growth4w'
  | 'cumulativeFcr'
  | 'weeklyFcr'
  | 'adjustedFcr'
  | 'feedKgByHa'
  | 'weeklyFeedGiven'
  | 'totalFeedGivenKg'
  | 'totalFeedGivenLbs'
  | 'feedAsBiomassPercent'
  | 'survival'
  | 'survivalFeed'
  | 'survivalWithPartialHarvest'
  | 'biomassLbs'
  | 'totalBiomassLbs'
  | 'biomassLbsByHa'
  | 'totalBiomassLbsByHa'
  | 'animalsRemainingM2'
  | 'animalsRemainingHa'
  | 'weightCv'
  | 'profitPerHaPerDay'
  | 'profitPerPound'
  | 'totalProfit'
  | 'totalRevenue'
  | 'revenuePerPound'
  | 'totalRevenuePound'
  | 'totalCosts'
  | 'stockingCosts'
  | 'cumulativeOverheadCosts'
  | 'cumulativeFeedCosts'
  | 'costPerPound'
  | 'costPoundHarvest'
  | 'feedCostPerKg'
  | 'stockingCostsMillar';

type VariableData = {
  title: string;
  suffix?: string;
  isCurrency?: boolean;
  isPercentage?: boolean;
  fractionDigits: number;
  shouldConvertUnit?: boolean;
  convertUnit?: typeof convertUnitByDivision;
};

export type ProductionChartVariableMap = Record<ProductionChartVariableOption, VariableData>;

type useProductionChartVariablesProps = {
  skipInitially?: boolean;
  defaultValue?: { variableOne?: ProductionChartVariableOption; variableTwo?: ProductionChartVariableOption };
  isAdmin?: boolean;
  isSupervisor?: boolean;
  isVisionOnly?: boolean;
};
export function useProductionChartVariables(props: useProductionChartVariablesProps = {}) {
  const { skipInitially, defaultValue, isAdmin, isSupervisor, isVisionOnly } = props;
  const { trans } = getTrans();

  const user = useAppSelector((state) => state.auth.user);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};

  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);
  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');

  const chartVariableMap: ProductionChartVariableMap = {
    averageWeight: {
      title: trans('t_abw_g'),
      fractionDigits: 2
    },
    growthLinear: {
      title: trans('t_growth_g_wk'),
      fractionDigits: 2
    },
    growthDaily: {
      title: trans('t_growth_g_day'),
      fractionDigits: 2
    },
    weeklyGrowth: {
      title: trans('t_growth_monitored_x_week_g_wk', { weeks: 1 }),
      fractionDigits: 2
    },
    growthTwoWeeks: {
      title: trans('t_growth_monitored_x_week_g_wk', { weeks: 2 }),
      fractionDigits: 2
    },
    growthThreeWeeks: {
      title: trans('t_growth_monitored_x_week_g_wk', { weeks: 3 }),
      fractionDigits: 2
    },
    growth4w: {
      title: trans('t_growth_monitored_x_week_g_wk', { weeks: 4 }),
      fractionDigits: 2
    },
    cumulativeFcr: {
      title: trans('t_fcr_cumulative'),
      fractionDigits: 2
    },
    weeklyFcr: {
      title: trans('t_fcr_weekly'),
      fractionDigits: 2
    },
    adjustedFcr: {
      title: trans('t_fcr_adjusted'),
      fractionDigits: 2
    },
    feedKgByHa: {
      title: trans('t_feed_given_daily_kg_ha'),
      fractionDigits: 1
    },
    weeklyFeedGiven: {
      title: trans('t_feed_given_weekly_kg'),
      fractionDigits: 0
    },
    totalFeedGivenKg: {
      title: trans('t_feed_given_kg'),
      fractionDigits: 0
    },
    totalFeedGivenLbs: {
      title: trans('t_feed_given_lg'),
      fractionDigits: 0
    },
    feedAsBiomassPercent: {
      title: trans('t_feed_%_of_biomass'),
      isPercentage: true,
      fractionDigits: 2
    },
    survival: {
      title: trans('t_survival_live'),
      isPercentage: true,
      fractionDigits: 1
    },
    survivalFeed: {
      title: trans('t_kampi_survival'),
      isPercentage: true,
      fractionDigits: 1
    },
    survivalWithPartialHarvest: {
      title: trans('t_survival_include_harvests'),
      isPercentage: true,
      fractionDigits: 1
    },
    biomassLbs: {
      title: trans('t_biomass_in_pond_unit', { unit: unitLabel }),
      fractionDigits: 0,
      shouldConvertUnit: true,
      convertUnit: convertUnitByDivision
    },
    totalBiomassLbs: {
      title: trans('t_biomass_include_harvests_unit', { unit: unitLabel }),
      fractionDigits: 0,
      shouldConvertUnit: true,
      convertUnit: convertUnitByDivision
    },
    biomassLbsByHa: {
      title: trans('t_biomass_in_pond_unit_ha', { unit: unitLabel }),
      fractionDigits: 0,
      shouldConvertUnit: true,
      convertUnit: convertUnitByDivision
    },
    totalBiomassLbsByHa: {
      title: trans('t_biomass_include_harvests_unit_ha', { unit: unitLabel }),
      fractionDigits: 0,
      shouldConvertUnit: true,
      convertUnit: convertUnitByDivision
    },
    animalsRemainingM2: {
      title: trans('t_animals_in_pond_m2'),
      fractionDigits: 2
    },
    animalsRemainingHa: {
      title: trans('t_animals_in_pond_ha'),
      fractionDigits: 0
    },
    weightCv: {
      title: trans('t_dispersion_cv'),
      isPercentage: true,
      fractionDigits: 1
    },
    profitPerPound: {
      title: isBiomassUnitLbs ? trans('t_profit_lb') : trans('t_profit_kg'),
      isCurrency: true,
      fractionDigits: 2,
      shouldConvertUnit: true,
      convertUnit: convertUnitByMultiplication
    },
    profitPerHaPerDay: {
      title: trans('t_profit_include_dry_days_ha_day'),
      isCurrency: true,
      fractionDigits: 2
    },
    totalProfit: {
      title: trans('t_profit'),
      isCurrency: true,
      fractionDigits: 0
    },
    totalRevenue: {
      title: trans('t_revenue'),
      isCurrency: true,
      fractionDigits: 0
    },
    revenuePerPound: {
      title: isBiomassUnitLbs ? trans('t_revenue_per_lb_live') : trans('t_revenue_per_kg_live'),
      isCurrency: true,
      fractionDigits: 2,
      shouldConvertUnit: true,
      convertUnit: convertUnitByMultiplication
    },
    totalRevenuePound: {
      title: isBiomassUnitLbs ? trans('t_revenue_per_lb_include_harvest') : trans('t_revenue_per_kg_include_harvest'),
      isCurrency: true,
      fractionDigits: 2,
      shouldConvertUnit: true,
      convertUnit: convertUnitByMultiplication
    },
    totalCosts: {
      title: trans('t_cost_$'),
      isCurrency: true,
      fractionDigits: 0
    },
    stockingCosts: {
      title: trans('t_stocking_cost'),
      isCurrency: true,
      fractionDigits: 0
    },
    stockingCostsMillar: {
      title: trans('t_cost_millar'),
      isCurrency: true,
      fractionDigits: 2
    },
    cumulativeOverheadCosts: {
      title: trans('t_overhead_cost_cumulative'),
      isCurrency: true,
      fractionDigits: 0
    },
    cumulativeFeedCosts: {
      title: trans('t_feed_cost_$'),
      isCurrency: true,
      fractionDigits: 0
    },
    costPerPound: {
      title: trans('t_cost_per_unit_processed', { unit: unitLabel }),
      isCurrency: true,
      fractionDigits: 2,
      shouldConvertUnit: true,
      convertUnit: convertUnitByMultiplication
    },
    costPoundHarvest: {
      title: trans('t_cost_per_unit_harvested', { unit: unitLabel }),
      isCurrency: true,
      fractionDigits: 2,
      shouldConvertUnit: true,
      convertUnit: convertUnitByMultiplication
    },
    feedCostPerKg: {
      title: trans('t_feed_cost_per_kg'),
      isCurrency: true,
      fractionDigits: 3
    }
  };

  const { variableOne, variableTwo } = defaultValue ?? {};
  const [chartVariableOne, setChartVariableOne] = useState<ProductionChartVariableOption>(variableOne);
  const [chartVariableTwo, setChartVariableTwo] = useState<ProductionChartVariableOption>(variableTwo);

  useEffect(() => {
    if (skipInitially || !!defaultValue) return;
    getCacheItem<ProductionChartVariableOption>('pondProductionChartVariableOne').then((res) => {
      if (!res) return setChartVariableOne('growthLinear');
      if (shouldHideVariable(res, { isAdmin, isSupervisor, isVisionOnly })) {
        setChartVariableOne('growthLinear');
      } else {
        setChartVariableOne(res);
      }
    });
    getCacheItem<ProductionChartVariableOption>('pondProductionChartVariableTwo').then((res) => {
      const visionOnlyDefault = isVisionOnly ? 'averageWeight' : 'cumulativeFcr';
      if (!res) return setChartVariableTwo(visionOnlyDefault);
      if (shouldHideVariable(res, { isAdmin, isSupervisor, isVisionOnly })) {
        setChartVariableTwo(visionOnlyDefault);
      } else {
        setChartVariableTwo(res);
      }
    });
  }, [skipInitially, isAdmin, isSupervisor, isVisionOnly, JSON.stringify(defaultValue)]);

  const changeChartVariableOne = (value: ProductionChartVariableOption) => {
    setChartVariableOne(value);
    setCacheItem('pondProductionChartVariableOne', value, sevenDaysCacheTime).then();
  };

  const changeChartVariableTwo = (value: ProductionChartVariableOption) => {
    setChartVariableTwo(value);
    setCacheItem('pondProductionChartVariableTwo', value, sevenDaysCacheTime).then();
  };

  return { chartVariableMap, chartVariableOne, chartVariableTwo, changeChartVariableOne, changeChartVariableTwo };
}
