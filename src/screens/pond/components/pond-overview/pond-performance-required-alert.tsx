import { getTrans } from '@i18n/get-trans';
import { Box, Text, useDisclosure } from '@chakra-ui/react';
import { PopoverArrow, PopoverBody, PopoverContent, PopoverRoot, PopoverTrigger } from '@components/ui/popover';
import { InfoFilledIcon } from '@icons/info/info-filled-icon';
import { useRef } from 'react';

export function PondPerformanceRequiredAlert() {
  const { trans } = getTrans();

  const { open, onOpen, onClose } = useDisclosure();
  const ref = useRef<HTMLDivElement>(null);

  return (
    <Box ref={ref}>
      <PopoverRoot
        open={open}
        onOpenChange={(e) => (e.open ? onOpen() : onClose())}
        positioning={{ placement: 'bottom-start' }}
      >
        <PopoverTrigger asChild>
          <InfoFilledIcon
            width='20px'
            height='20px'
            cursor='pointer'
            userSelect='none'
            _hover={{ opacity: 0.8 }}
            secondSectionColor='white'
            firstSectionColor='gray.700'
          />
        </PopoverTrigger>

        <PopoverContent
          w='340px'
          bgColor='bg.gray.strong'
          shadow='elevation.100'
          _focusVisible={{ outline: 'none', shadow: 'elevation.100' }}
          portalRef={ref}
        >
          <PopoverArrow bgColor='bg.gray.strong' />
          <PopoverBody py='sm-alt' px='md'>
            <Text size='label300'>{trans('t_pond_performance_required')}</Text>
          </PopoverBody>
        </PopoverContent>
      </PopoverRoot>
    </Box>
  );
}
