import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { HistoryVariable } from '@screens/pond/components/modals/hooks/use-get-history-view-variables';
import { DateTime } from 'luxon';
import { CardContainer } from '@components/card-container/card-container';
import {
  Box,
  Flex,
  IconButton,
  Table,
  TableCellProps,
  TableColumnHeaderProps,
  TableRowProps,
  Text,
  TextProps
} from '@chakra-ui/react';
import { HistoryChangeVariablesModal } from '@screens/pond/components/modals/history-change-variables-modal';
import { convertUnitByDivision, convertUnitByMultiplication, formatNumber, isNumber } from '@utils/number';
import { Dispatch, ReactNode, SetStateAction, useEffect, useState } from 'react';
import { useGetAdminFinancials } from '@screens/pond/helpers/use-admin-financials';
import { actionsName } from '@utils/segment';
import {
  PopulationHarvestPlanIndicators,
  PopulationHistory,
  PopulationManualAverageWeights
} from '@xpertsea/module-farm-sdk';
import { useAnalytics } from '@hooks/use-analytics';
import { useUpdatePopulationApi } from '@screens/population/hooks/use-update-population-api';
import debounce from 'lodash/debounce';
import { BaseSpinner } from '@components/base/base-spinner';
import { FlagSolid } from '@icons/flag/flag-solid';
import { FlagOutlined } from '@icons/flag/flag-outlined';
import { usePermission } from '@hooks/use-permission';
import { useReloadCurrentPond } from '@screens/pond/hooks/use-reload-current-pond';
import {
  IndicatingKeys,
  indicatingStatusIconMap,
  indicatorsColorMap,
  PondCardViewType as _PondCardViewType
} from '@components/pond-card/helper';
import { getCacheItem, setCacheItem, sevenDaysCacheTime } from '@utils/local-cache';
import { BaseMenu } from '@components/base/base-menu';
import { FileAdjustedIcon } from '@icons/file-adjusted-icon';
import { AverageWeightChangePopover } from '@screens/data-updater/components/average-weight-change-popover';
import { getHistoryVariables } from '@screens/pond/components/pond-overview/helpers/get-history-variables';
import isUndefined from 'lodash/isUndefined';
import { InfoIconToolTip } from '@components/tooltip/info-icon-tool-tip';
import {
  getHarvestVariables,
  HarvestVariablesType
} from '@screens/pond/components/pond-overview/helpers/get-harvest-variables';
import {
  HarvestVariable,
  isHarvestVariable
} from '@screens/pond/components/modals/hooks/use-get-harvest-view-variables';
import { HarvestChangeVariablesModal } from '@screens/pond/components/modals/harvest-change-variables-modal';
import { SectionLoader } from '@components/loaders/section-loader';
import keyBy from 'lodash/keyBy';
import { SettingsAdjustFilled } from '@icons/settings-adjust/settings-adjust-filled';
import { Tooltip } from '@components/ui/tooltip';
import { TableContainer } from '@components/ui/table-container';
import { gramsInKg, poundPerKg } from '@utils/constants';
import { AdminOrSupervisorOrOperationWrapper } from '@components/permission-wrappers/admin-or-supervisor-or-operation-wrapper';

type PondCardViewType = _PondCardViewType | 'harvestActual';

export function LastHistoryCard() {
  const { trans } = getTrans();

  const lang = useAppSelector((state) => state.app.lang);
  const preferences = useAppSelector((state) => state.auth?.user?.preferences);
  const population = useAppSelector((state) => state.farm?.currentPopulation);
  const currentFarmId = useAppSelector((state) => state.farm?.currentFarm?._id);
  const productOffering = useAppSelector((state) => state.farm?.currentFarm?.metadata?.productOffering);

  const isVisionOnly = productOffering === 'visionOnly';
  const { viewFields } = preferences ?? {};
  const { pondLastTwoHistory, harvestVariables } = viewFields ?? {};

  // We should remove the deprecated variable that was removed from the platform but is still saved in the user preferences
  const filteredHarvestVariables = harvestVariables?.filter((hv: { name: string }) => isHarvestVariable(hv.name));

  const isAdmin = usePermission({
    role: 'admin',
    entity: 'farm',
    entityId: currentFarmId
  });

  const isSupervisor = usePermission({
    role: 'supervisor',
    entity: 'farm',
    entityId: currentFarmId
  });

  const pondLastHistoryVariables = getHistoryVariables({
    historyViewVariables: pondLastTwoHistory,
    isAdmin,
    isSupervisor,
    isVisionOnly
  });

  const pondHarvestVariables = getHarvestVariables({
    harvestViewVariables: filteredHarvestVariables,
    isAdmin,
    isSupervisor,
    isVisionOnly
  });

  const { harvest, history, harvestPlan, estimatedSurvival, estimatedFeed, stockingCostsMillar, manualAverageWeights } =
    population ?? {};
  const { indicators } = harvestPlan ?? {};
  const latestHistory = history?.[0];

  const { date: latestDate } = latestHistory ?? {};

  const manualWeight = manualAverageWeights?.find((item) => item.date === latestDate);

  const isHarvested = !!harvest?.date;
  const [profitProjectionToView, setProfitProjectionToView] = useState<PondCardViewType>(
    isHarvested ? 'harvest' : 'current'
  );
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const latestDateFormatted = latestDate ? DateTime.fromISO(latestDate).toFormat('MMM dd', { locale: lang }) : null;
  const hasEstimatedSurvival = estimatedSurvival?.[latestDate];
  const hasEstimatedFeed = estimatedFeed?.[latestDate];
  const hasEstimatedValues = hasEstimatedSurvival ?? hasEstimatedFeed;
  const profitIndicatorColor = !isVisionOnly
    ? indicatorsColorMap[indicators?.profitPerHaPerDay?.current?.color]
    : undefined;
  const estimatedValuesTitle = hasEstimatedValues ? trans('t_using_imputed_values') : trans('t_up_to_date');

  const isHarvestView = profitProjectionToView === 'harvest' || profitProjectionToView === 'harvestActual';
  const isHarvestActualView = profitProjectionToView === 'harvestActual';
  const isCurrentView = profitProjectionToView === 'current';
  const cardTitle = isHarvestActualView ? trans('t_recorded') : estimatedValuesTitle;

  useEffect(() => {
    getCacheItem<PondCardViewType>('pondOverviewPondView')
      .then((valueFromCache) => {
        if (isHarvested && valueFromCache === 'current') return setProfitProjectionToView('harvest');
        if (!isHarvested && valueFromCache === 'harvestActual') return setProfitProjectionToView('current');
        if (valueFromCache) setProfitProjectionToView(valueFromCache);
      })
      .finally(() => setIsLoading(false));
  }, [population?._id]);

  const commonThStyles: TableColumnHeaderProps = {
    pos: 'sticky',
    left: 0,
    border: 'none',
    bgColor: 'transparent',
    color: 'text.gray.disabled',
    px: '2sm',
    pt: 'sm-alt',
    pb: 'sm',
    textAlign: 'center'
  };

  return (
    <CardContainer
      p={0}
      pt='xs-alt'
      overflow='hidden'
      w={{ base: '100%', xl: '380px' }}
      pos='relative'
      _before={{
        content: '""',
        position: 'absolute',
        top: 0,
        left: '3px',
        right: '3px',
        height: '6px',
        backgroundColor: profitIndicatorColor,
        borderTopRadius: '2xl'
      }}
    >
      <SectionLoader isLoading={isLoading} />
      <Flex align='center' justify='space-between' pe='md' h='40px'>
        <HistoryViewSelector
          setProfitProjectionToView={setProfitProjectionToView}
          profitProjectionToView={profitProjectionToView}
          isHarvested={isHarvested}
        />
        <Flex align='center' gap='sm-alt'>
          <HistoryCardHeader />

          {isCurrentView && <MoreOptionsMenu pondLastHistoryVariables={pondLastHistoryVariables} />}
          {isHarvestView && <HarvestMoreOptionsMenu harvestVariables={pondHarvestVariables} />}
        </Flex>
      </Flex>

      <Text size='label300' color='text.gray.disabled' ps='md' mb='xs'>
        {cardTitle}
      </Text>

      <TableContainer mx='md'>
        <Table.Root>
          {isCurrentView ? (
            <Table.Header>
              <Table.Row>
                <Table.ColumnHeader
                  {...commonThStyles}
                  ps='md-alt'
                  textAlign='start'
                  minW='125px'
                  bgColor='white'
                  zIndex={1}
                >
                  <Text size='label300'>{trans('t_variable')}</Text>
                </Table.ColumnHeader>

                <Table.ColumnHeader {...commonThStyles}>
                  <Text size='label300'>{latestDateFormatted ?? '-'}</Text>
                </Table.ColumnHeader>

                {!isVisionOnly && (
                  <Table.ColumnHeader {...commonThStyles}>
                    <Text size='label300'>{trans('t_target')}</Text>
                  </Table.ColumnHeader>
                )}
              </Table.Row>
            </Table.Header>
          ) : (
            <Table.Header>
              <Table.Row>
                <Table.ColumnHeader w='50%' p={0} border='none' />
                <Table.ColumnHeader w='50%' p={0} border='none' />
              </Table.Row>
            </Table.Header>
          )}
          <Table.Body>
            {isHarvestView && (
              <HistoryHarvestTableRows
                isHarvestActualView={isHarvestActualView}
                pondHarvestVariables={pondHarvestVariables}
              />
            )}
            {isCurrentView && (
              <>
                {pondLastHistoryVariables.map((variable, index) => {
                  const isLastItem = index === pondLastHistoryVariables.length - 1;
                  return (
                    <HistoryVariableMapper
                      key={variable}
                      variable={variable}
                      latestHistory={latestHistory}
                      indicators={indicators}
                      hasSurvivalEstimatedValues={hasEstimatedSurvival}
                      hasFeedEstimatedValues={hasEstimatedFeed}
                      isLastItem={isLastItem}
                      stockingCostsMillar={stockingCostsMillar}
                      manualAverageWeight={manualWeight}
                    />
                  );
                })}
              </>
            )}
          </Table.Body>
        </Table.Root>
      </TableContainer>
    </CardContainer>
  );
}

interface HistoryViewSelectorProps {
  setProfitProjectionToView: Dispatch<SetStateAction<PondCardViewType>>;
  profitProjectionToView: PondCardViewType;
  isHarvested: boolean;
}

function HistoryViewSelector(props: HistoryViewSelectorProps) {
  const { setProfitProjectionToView, isHarvested, profitProjectionToView } = props;
  const { trans } = getTrans();
  const { trackAction } = useAnalytics();
  const productOffering = useAppSelector((state) => state.farm?.currentFarm?.metadata?.productOffering);

  const isVisionOnly = productOffering === 'visionOnly';

  const options: { label: string; value: PondCardViewType }[] = isHarvested
    ? [
        { label: trans('t_harvest_recorded'), value: 'harvestActual' },
        { label: trans('t_harvest_expected'), value: 'harvest' }
      ]
    : [
        { label: trans('t_current'), value: 'current' },
        { label: trans('t_harvest'), value: 'harvest' }
      ];
  const selectedOption = options.find((option) => option.value === profitProjectionToView);

  const updateProfitProjectToView = (value: PondCardViewType) => {
    trackAction(actionsName.profitProjectionToViewPondOverviewClicked, { profitProjectionToView: value }).then();
    setCacheItem('pondOverviewPondView', value, sevenDaysCacheTime).then();
    setProfitProjectionToView(value);
  };

  useEffect(() => {
    if (isHarvested) {
      updateProfitProjectToView('harvestActual');
    } else {
      updateProfitProjectToView('current');
    }
  }, [isHarvested]);

  if (isVisionOnly) {
    return (
      <Text ps='md' textStyle='button.100'>
        {isHarvested ? trans('t_harvest_recorded') : trans('t_current')}
      </Text>
    );
  }

  return (
    <BaseMenu
      h='fit-content'
      menuOffset={{ mainAxis: 12, crossAxis: 8 }}
      selectVariant='secondary'
      selectedOption={selectedOption}
      options={options}
      iconProps={{ boxSize: '20px' }}
      onItemSelect={(option) => updateProfitProjectToView(option.value as PondCardViewType)}
    />
  );
}

function HistoryCardHeader() {
  const [populationIdToUpdateFlag, setPopulationIdToUpdateFlag] = useState<string>();
  const [isUpdatingFlag, setIsUpdatingFlag] = useState<boolean>(false);

  const currentPopulation = useAppSelector((state) => state.farm.currentPopulation);
  const { metadata, _id: populationId, pondId, cycleInformation } = currentPopulation ?? {};
  const isFlagged = metadata?.isFlagged;

  const { reloadCurrentPond } = useReloadCurrentPond();
  const [_, updatePopulation] = useUpdatePopulationApi();

  const { trans } = getTrans();
  const { trackAction } = useAnalytics();

  const onUpdateFlag = debounce((params: { populationId: string; isFlagged: boolean }) => {
    const { isFlagged, populationId } = params;

    setIsUpdatingFlag(true);
    setPopulationIdToUpdateFlag(populationId);
    updatePopulation({
      params: {
        filter: { populationId },
        set: { metadata: { isFlagged: !isFlagged } }
      },
      successCallback() {
        reloadCurrentPond(() => {
          setIsUpdatingFlag(false);
          setPopulationIdToUpdateFlag(undefined);
        });
      },
      failureCallback() {
        setIsUpdatingFlag(false);
        setPopulationIdToUpdateFlag(undefined);
      }
    });
  }, 250);

  const FlagIcon = isFlagged ? FlagSolid : FlagOutlined;
  const isAdjusted = cycleInformation?.projection?.projectedGrowth?.type === 'custom';
  return (
    <Flex align='center' gap='sm-alt'>
      {isAdjusted && (
        <Tooltip content={trans('t_using_custom_values_for_projection')}>
          <Flex alignSelf='center'>
            <FileAdjustedIcon w='20px' h='20px' />
          </Flex>
        </Tooltip>
      )}
      {isUpdatingFlag && populationIdToUpdateFlag === populationId && <BaseSpinner borderWidth='2px' size='sm' />}
      <AdminOrSupervisorOrOperationWrapper>
        {populationIdToUpdateFlag !== populationId && (
          <FlagIcon
            cursor='pointer'
            onClick={() => {
              onUpdateFlag({ isFlagged, populationId });
              trackAction(actionsName.farmSummaryViewPondFlagClicked, { pondId }).then();
            }}
          />
        )}
      </AdminOrSupervisorOrOperationWrapper>
    </Flex>
  );
}

type HarvestMoreOptionsMenuProps = {
  harvestVariables: HarvestVariablesType[];
};

function HarvestMoreOptionsMenu(props: HarvestMoreOptionsMenuProps) {
  const { harvestVariables } = props;

  return (
    <HarvestChangeVariablesModal selectedVariables={harvestVariables}>
      <IconButton
        h='fit-content'
        w='fit-content'
        bgColor='white'
        aria-label='filter-button'
        _focus={{ outline: 'none' }}
        data-cy='harvest-customize-view-btn'
      >
        <SettingsAdjustFilled w='20px' h='20px' transform='scale(1, -1)' />{' '}
      </IconButton>
    </HarvestChangeVariablesModal>
  );
}

type MoreOptionsProps = {
  pondLastHistoryVariables: HistoryVariable[];
};

function MoreOptionsMenu(props: MoreOptionsProps) {
  const { pondLastHistoryVariables } = props;

  return (
    <HistoryChangeVariablesModal selectedVariables={pondLastHistoryVariables}>
      <IconButton
        h='fit-content'
        w='fit-content'
        bgColor='white'
        aria-label='filter-button'
        _focus={{ outline: 'none' }}
        data-cy='customize-view-btn'
      >
        <SettingsAdjustFilled w='20px' h='20px' transform='scale(1, -1)' />
      </IconButton>
    </HistoryChangeVariablesModal>
  );
}

type HistoryHarvestTableRowsProps = {
  isHarvestActualView: boolean;
  pondHarvestVariables: HarvestVariablesType[];
};

function HistoryHarvestTableRows(props: HistoryHarvestTableRowsProps) {
  const { isHarvestActualView, pondHarvestVariables } = props;
  const { trans } = getTrans();

  const user = useAppSelector((state) => state.auth.user);
  const lang = useAppSelector((state) => state.app?.lang);
  const farmId = useAppSelector((state) => state.farm.currentFarm?._id);
  const population = useAppSelector((state) => state.farm?.currentPopulation);
  const pond = useAppSelector((state) => state.farm?.currentPond);

  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);

  const { harvestPlan, productionPrediction, harvest, seedingQuantity, partialHarvest, partialHarvestPlanned } =
    population ?? {};
  const { lbsHarvested } = harvest ?? {};
  const { size: pondSize } = pond ?? {};
  const { projection, harvestType: oldHarvestType, processorPriceList, harvestDate, indicators } = harvestPlan ?? {};
  const harvestType = processorPriceList?.defaultHarvestType ?? oldHarvestType;

  const { plannedHarvestIdx, profitProjectionData: plannedProjectionData } = projection ?? {};

  const plannedProfitProjection = plannedProjectionData?.[plannedHarvestIdx]?.[harvestType as 'headon'];
  const productionPredictionOnPlannedHarvest = productionPrediction?.find((item) => item.date === harvestDate);

  const predictionData = isHarvestActualView ? harvest : productionPredictionOnPlannedHarvest;
  const profitProjectionData = isHarvestActualView ? harvest : plannedProfitProjection;

  const variablesConfig = keyBy(pondHarvestVariables, 'name');

  const {
    date,
    daysOfCulture: cycleDays,
    averageWeight,
    weeklyGrowth,
    growthLinear,
    cumulativeFcr,
    adjustedFcr,
    survival,
    biomassLbs,
    biomassLbsByHa,
    totalBiomassLbs,
    totalBiomassLbsByHa,
    totalCosts,
    cumulativeFeedCosts,
    costPoundHarvest,
    growthDaily,
    animalsHarvestedHa,
    animalsHarvestedM2,
    animalsRemainingM2,
    animalsRemainingHa,
    totalPartialHarvestBiomassLbs,
    cumulativePartialHarvestRevenue,
    partialRevenuePound,
    partialRevenuePoundHarvest
  } = predictionData ?? {};

  const {
    costPerPound,
    profitPerHaPerDay,
    totalRevenue,
    revenuePerPound,
    revenuePoundHarvest,
    totalProfit,
    profitPerPound,
    profitPoundHarvest,
    totalRevenuePound,
    totalRevenuePoundHarvest,
    rofi
  } = profitProjectionData ?? {};

  const {
    rofiAdminValue,
    profitPerHaPerDayAdminValue,
    profitPerHaPerDayTextColor,
    totalProfitAdminValue,
    totalRevenueAdminValue,
    totalRevenuePerPoundAdminValue,
    totalRevenuePoundHarvestAdminValue,
    revenuePerPoundAdminValue,
    revenuePoundHarvestAdminValue,
    profitPerPoundAdminValue,
    profitPoundHarvestAdminValue,
    totalCostsAdminValue,
    cumulativeFeedCostsAdminValue,
    costPerPoundAdminValue,
    costPoundHarvestAdminValue
  } = useGetAdminFinancials({
    rofi,
    profitPerHaPerDay,
    profitPerPound,
    profitPoundHarvest,
    totalRevenuePound,
    totalRevenue,
    totalRevenuePoundHarvest,
    totalProfit,
    costPoundHarvest,
    costPerPound,
    totalCosts,
    cumulativeFeedCosts,
    revenuePerPound,
    revenuePoundHarvest,
    indicators,
    profitProjectionView: 'harvest',
    farmId
  });

  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');

  const cycleDaysFormatted = isNumber(cycleDays) ? `(${formatNumber(cycleDays, { lang, fractionDigits: 0 })}d)` : '';

  const harvestDateFormatted = date ? DateTime.fromISO(date).toFormat('MMM dd', { locale: lang }) : '-';

  const averageWeightFormatted = formatNumber(averageWeight, { lang, fractionDigits: 2 });
  const growthValue = isHarvestActualView ? growthLinear : weeklyGrowth;
  const weeklyGrowthFormatted = isNumber(growthValue)
    ? `(${formatNumber(growthValue, { lang, fractionDigits: 2 })} ${trans('t_g_over_wk')})`
    : '';

  const dailyGrowthFormatted = isNumber(growthDaily)
    ? `(${trans('t_growth_g_over_d', { growth: formatNumber(growthDaily, { lang, fractionDigits: 2 }) })})`
    : '';

  const growthFormatted =
    variablesConfig.averageWeight?.subOptions === 'daily' ? dailyGrowthFormatted : weeklyGrowthFormatted; // HERE

  const abwValue = averageWeightFormatted ? `${averageWeightFormatted} ${growthFormatted || ''}` : '-';

  const fcrFormatted = formatNumber(cumulativeFcr, { lang, fractionDigits: 2 }) ?? '-';
  const adjustedFcrFormatted = formatNumber(adjustedFcr, { lang, fractionDigits: 2 }) ?? '-';

  // survival
  const animals = animalsRemainingHa && pondSize ? animalsRemainingHa * pondSize : 0;
  const finalHarvestAnimalsHarvested = isHarvestActualView
    ? (lbsHarvested / poundPerKg) * (gramsInKg / averageWeight)
    : animals;

  const totalPartialAnimalsHarvested = [...(partialHarvest ?? []), ...(partialHarvestPlanned ?? [])].reduce(
    (accumulated, harvest) => accumulated + (harvest.quantity || 0),
    0
  );

  // survival all harvests
  const survivalAllHarvests =
    seedingQuantity > 0 ? (finalHarvestAnimalsHarvested + totalPartialAnimalsHarvested) / seedingQuantity : 0;

  const survivalAllHarvestsValue = isHarvestActualView ? survival : survivalAllHarvests;

  const survivalAllHarvestsFormatted =
    formatNumber(survivalAllHarvestsValue, { lang, fractionDigits: 0, isPercentage: true }) ?? '-';

  // survival final harvest
  const survivalFinalHarvestValue = seedingQuantity > 0 ? finalHarvestAnimalsHarvested / seedingQuantity : 0;
  const survivalFinalHarvestFormatted =
    formatNumber(survivalFinalHarvestValue, { lang, fractionDigits: 0, isPercentage: true }) ?? '-';

  const biomassLbsFormatted = formatNumber(
    convertUnitByDivision(isHarvestActualView ? totalBiomassLbs : biomassLbs, unitsConfig?.biomass),
    {
      lang,
      fractionDigits: 0
    }
  );
  const totalBiomassValue = isHarvestActualView ? totalBiomassLbsByHa : biomassLbsByHa;
  const biomassLbsByHaFormatted = isNumber(totalBiomassValue)
    ? ` (${formatNumber(convertUnitByDivision(totalBiomassValue, unitsConfig?.biomass), { lang, fractionDigits: 0 })} ${unitLabel}${trans('t_over_ha')})`
    : '';

  const biomassTextValue = biomassLbsByHaFormatted || unitLabel;

  const biomassValue = biomassLbsFormatted ? `${biomassLbsFormatted}${biomassTextValue}` : '-';

  // Biomass partial harvest
  const partialHarvestBiomassFormatted = formatNumber(
    convertUnitByDivision(totalPartialHarvestBiomassLbs, unitsConfig?.biomass),
    {
      lang,
      fractionDigits: 0
    }
  );

  const partialHarvestBiomassByHaValue =
    isNumber(totalPartialHarvestBiomassLbs) && pondSize > 0 ? totalPartialHarvestBiomassLbs / pondSize : 0;
  const partialHarvestBiomassByHaFormatted = isNumber(partialHarvestBiomassByHaValue)
    ? ` (${formatNumber(convertUnitByDivision(partialHarvestBiomassByHaValue, unitsConfig?.biomass), { lang, fractionDigits: 0 })} ${unitLabel}${trans('t_over_ha')})`
    : '';

  const partialHarvestBiomassByHaTextValue = partialHarvestBiomassByHaFormatted || unitLabel;

  const partialHarvestBiomass = isNumber(partialHarvestBiomassFormatted)
    ? `${partialHarvestBiomassFormatted}${partialHarvestBiomassByHaTextValue}`
    : '-';

  // Biomass final harvest
  const biomassFinalHarvest = isHarvestActualView ? harvest?.lbsHarvested : totalBiomassLbs;

  const finalHarvestBiomassByHaValue =
    isNumber(biomassFinalHarvest) && pondSize > 0 ? biomassFinalHarvest / pondSize : 0;
  const finalHarvestBiomassByHaFormatted = isNumber(finalHarvestBiomassByHaValue)
    ? ` (${formatNumber(convertUnitByDivision(finalHarvestBiomassByHaValue, unitsConfig?.biomass), { lang, fractionDigits: 0 })} ${unitLabel}${trans('t_over_ha')})`
    : '';

  const finalHarvestBiomassByHaTextValue = finalHarvestBiomassByHaFormatted || unitLabel;
  const finalHarvestBiomassFormatted = isNumber(biomassFinalHarvest)
    ? formatNumber(convertUnitByDivision(biomassFinalHarvest, unitsConfig?.biomass), {
        lang,
        fractionDigits: 0
      })
    : '-';

  const finalHarvestBiomass = isNumber(finalHarvestBiomassFormatted)
    ? `${finalHarvestBiomassFormatted}${finalHarvestBiomassByHaTextValue}`
    : '-';

  // revenue per pound

  const revenuePerPoundValue = revenuePerPoundAdminValue ?? '-';
  const revenuePoundHarvestValue = revenuePoundHarvestAdminValue ?? '-';

  // revenue total
  const totalRevenuePerPoundText =
    totalRevenuePoundHarvestAdminValue && totalRevenuePoundHarvestAdminValue !== 'N/A'
      ? ` (${totalRevenuePoundHarvestAdminValue} /${unitLabel})`
      : '';
  const totalRevenuePerPoundAdminText =
    totalRevenuePerPoundAdminValue && totalRevenuePerPoundAdminValue !== 'N/A'
      ? ` (${totalRevenuePerPoundAdminValue} /${unitLabel})`
      : '';
  const totalRevenuePerPoundValue = totalRevenuePerPoundAdminValue ?? '-';
  const totalRevenuePoundHarvestValue = totalRevenuePoundHarvestAdminValue ?? '-';

  const totalRevenueType = variablesConfig.totalRevenue?.subOptions ?? 'harvested';
  const totalRevenueTitle = totalRevenueType === 'harvested' ? totalRevenuePerPoundText : totalRevenuePerPoundAdminText;
  const totalRevenueText = totalRevenueAdminValue ? `${totalRevenueAdminValue} ${totalRevenueTitle}` : '-';

  // profit per pound
  const profitPerPoundText =
    profitPoundHarvestAdminValue && profitPoundHarvestAdminValue !== 'N/A'
      ? ` (${profitPoundHarvestAdminValue} /${unitLabel})`
      : '';

  // animals harvested
  const animalsHarvestedHaValue = isHarvestActualView ? animalsHarvestedHa : animalsRemainingHa;
  const animalsHarvestedM2Value = isHarvestActualView ? animalsHarvestedM2 : animalsRemainingM2;

  const animalsHarvestedTitle =
    variablesConfig?.animalHarvested?.subOptions === 'animalHarvestedHa' ? trans('t_anim_ha') : trans('t_anim_m2');
  const animalsHarvested =
    variablesConfig?.animalHarvested?.subOptions === 'animalHarvestedHa'
      ? animalsHarvestedHaValue
      : animalsHarvestedM2Value;

  const animalsHarvestedValue = isNumber(animalsHarvested)
    ? `${formatNumber(animalsHarvested, { fractionDigits: 1, lang })}`
    : '-';

  // profit total
  const totalProfitText = totalProfitAdminValue ? `${totalProfitAdminValue} ${profitPerPoundText}` : '-';

  const profitPePoundValue = profitPerPoundAdminValue ?? '-';
  const profitPoundHarvestValue = profitPoundHarvestAdminValue ?? '-';

  const costPoundHarvestText =
    costPoundHarvestAdminValue && costPoundHarvestAdminValue !== 'N/A'
      ? ` (${costPoundHarvestAdminValue} /${unitLabel})`
      : '';
  const costPerPoundAdminValueText =
    costPerPoundAdminValue && costPerPoundAdminValue !== 'N/A' ? ` (${costPerPoundAdminValue} /${unitLabel})` : '';

  const totalCostsType = variablesConfig.totalCosts?.subOptions || 'harvested';
  const costPerPoundText = totalCostsType === 'processed' ? costPerPoundAdminValueText : costPoundHarvestText;
  const totalCostsText = totalCostsAdminValue ? `${totalCostsAdminValue} ${costPerPoundText}` : '-';

  // Revenue partial harvest
  const partialRevenuePerPound = convertUnitByMultiplication(
    variablesConfig.revenuePartial?.subOptions === 'harvested' ? partialRevenuePoundHarvest : partialRevenuePound,
    unitsConfig?.biomass
  );

  const partialRevenuePerPoundFormatted = isNumber(partialRevenuePerPound)
    ? `(${formatNumber(partialRevenuePerPound, { fractionDigits: 2, lang, isCurrency: true })} /${unitLabel})`
    : '';

  const cumulativePartialHarvestRevenueFormatted = formatNumber(cumulativePartialHarvestRevenue, {
    fractionDigits: 0,
    lang,
    isCurrency: true
  });
  const partialHarvestRevenueText = isNumber(cumulativePartialHarvestRevenue)
    ? `${cumulativePartialHarvestRevenueFormatted} ${partialRevenuePerPoundFormatted}`
    : '-';

  // Final partial harvest
  const revenueFinalType = variablesConfig.revenueFinal?.subOptions ?? 'processed';
  const revenueFinalHarvest =
    revenueFinalType === 'harvested' ? harvest?.revenue : plannedProfitProjection?.liveTotalRevenue;
  const finalRevenuePerPound = revenueFinalType === 'harvested' ? revenuePoundHarvest : revenuePerPound;

  const finalRevenuePerPoundFormatted = isNumber(finalRevenuePerPound)
    ? `(${formatNumber(finalRevenuePerPound, { fractionDigits: 2, lang, isCurrency: true })} /${unitLabel})`
    : '';

  const finalHarvestRevenueText = isNumber(revenueFinalHarvest)
    ? `${formatNumber(revenueFinalHarvest, { fractionDigits: 0, lang, isCurrency: true })} ${finalRevenuePerPoundFormatted}`
    : '-';

  type HarvestVariableItem = {
    title: string;
    value: string | number;
    color?: TextProps['color'];
    infoComponent?: ReactNode;
  };

  const valueMap: Record<HarvestVariable, HarvestVariableItem> = {
    profitPerHaPerDay: {
      title: trans('t_prof_ha_d'),
      value: profitPerHaPerDayAdminValue,
      color: profitPerHaPerDayTextColor
    },
    averageWeight: {
      title: trans('t_abw_g'),
      value: abwValue
    },
    fcr: {
      title: trans('t_fcr'),
      value: fcrFormatted
    },
    adjustedFcr: {
      title: trans('t_fcr_adjusted'),
      value: adjustedFcrFormatted
    },
    survivalAllHarvests: {
      title: trans('t_survival_include_harvests'),
      value: survivalAllHarvestsFormatted
    },
    survivalFinalHarvest: {
      title: trans('t_survival_final_harvest'),
      value: survivalFinalHarvestFormatted
    },
    animalHarvested: {
      title: animalsHarvestedTitle,
      value: animalsHarvestedValue
    },
    totalBiomass: {
      title: isBiomassUnitLbs ? trans('t_biomass_lb') : trans('t_biomass_kg'),
      value: biomassValue
    },
    biomassPartialHarvest: {
      title: trans('t_bioL_p'),
      value: partialHarvestBiomass
    },
    biomassFinalHarvest: {
      title: trans('t_bioL_f'),
      value: finalHarvestBiomass
    },
    totalCosts: {
      title: totalCostsType === 'processed' ? trans('t_cost_p_lb') : trans('t_cost_h_lb'),
      value: totalCostsText,
      infoComponent: (
        <InfoIconToolTip
          hasArrow={false}
          triggerComponentProps={{ ms: 'xs-alt' }}
          iconProps={{ firstSectionColor: 'gray.700', secondSectionColor: 'white' }}
        >
          <Flex direction='column' gap='sm-alt'>
            <Text size='label300'>
              {trans('t_cost_$')}/{unitLabel}:
            </Text>
            <Text size='label300'>
              {trans('t_processed')}: {costPerPoundAdminValue ?? '-'}
            </Text>
            <Text size='label300'>
              {trans('t_harvested')}: {costPoundHarvestAdminValue ?? '-'}
            </Text>
          </Flex>
        </InfoIconToolTip>
      )
    },
    totalRevenue: {
      title: trans('t_rev'),
      value: totalRevenueText,
      infoComponent: (
        <InfoIconToolTip
          hasArrow={false}
          triggerComponentProps={{ ms: 'xs-alt' }}
          iconProps={{ firstSectionColor: 'gray.700', secondSectionColor: 'white' }}
        >
          <Flex direction='column' gap='sm-alt'>
            <Text size='label300'>
              {trans('t_revenue')}/{unitLabel}:
            </Text>
            <Text size='label300'>
              {trans('t_processed')}: {isHarvestActualView ? totalRevenuePerPoundValue : revenuePerPoundValue}
            </Text>
            <Text size='label300'>
              {trans('t_harvested')}: {isHarvestActualView ? totalRevenuePoundHarvestValue : revenuePoundHarvestValue}
            </Text>
          </Flex>
        </InfoIconToolTip>
      )
    },
    totalProfit: {
      title: trans('t_prof'),
      value: totalProfitText,
      infoComponent: (
        <InfoIconToolTip
          hasArrow={false}
          triggerComponentProps={{ ms: 'xs-alt' }}
          iconProps={{ firstSectionColor: 'gray.700', secondSectionColor: 'white' }}
        >
          <Flex direction='column' gap='sm-alt'>
            <Text size='label300'>
              {trans('t_profit')}/{unitLabel}:
            </Text>
            <Text size='label300'>
              {trans('t_processed')}: {profitPePoundValue}
            </Text>
            <Text size='label300'>
              {trans('t_harvested')}: {profitPoundHarvestValue}
            </Text>
          </Flex>
        </InfoIconToolTip>
      )
    },
    totalFeedCosts: {
      title: trans('t_feed_cost_$'),
      value: cumulativeFeedCostsAdminValue ?? '-'
    },
    revenuePartial: {
      title: trans('t_rev_p'),
      value: partialHarvestRevenueText
    },
    revenueFinal: {
      title: trans('t_rev_h'),
      value: finalHarvestRevenueText
    },
    rofi: {
      title: trans('t_rofi'),
      value: rofiAdminValue
    }
  };

  return (
    <>
      <TableRow>
        <TableCell textAlign='start'>
          <Text size='label200'>{trans('t_date')}</Text>
        </TableCell>
        <TableCell textAlign='start'>
          <Flex align='center' gap='2xs'>
            <Text size='label200'>{harvestDateFormatted}</Text>
            <Text size='label200'>{cycleDaysFormatted}</Text>
          </Flex>
        </TableCell>
      </TableRow>
      {pondHarvestVariables.map((variable, index) => {
        const isLastItem = index === pondHarvestVariables.length - 1;
        const valueItem = valueMap[variable.name];
        const { title, value, infoComponent, color } = valueItem ?? {};
        const cellBorder = isLastItem ? 'none' : '1px solid {colors.gray.400}';

        return (
          <TableRow key={variable.name}>
            <TableCell textAlign='start' borderBottom={cellBorder}>
              <Flex align='center' gap='xs-alt'>
                <Text size='label200' color={color}>
                  {title}
                </Text>
                {infoComponent}
              </Flex>
            </TableCell>
            <TableCell textAlign='start' borderBottom={cellBorder}>
              <Text size='label200'>{value}</Text>
            </TableCell>
          </TableRow>
        );
      })}
    </>
  );
}

type HistoryVariableMapperProps = {
  variable: HistoryVariable;
  latestHistory: PopulationHistory;
  indicators: PopulationHarvestPlanIndicators;
  hasSurvivalEstimatedValues: boolean;
  hasFeedEstimatedValues: boolean;
  isLastItem: boolean;
  stockingCostsMillar: number;
  manualAverageWeight: PopulationManualAverageWeights;
};

function HistoryVariableMapper(props: HistoryVariableMapperProps) {
  const {
    variable,
    latestHistory,
    indicators,
    hasFeedEstimatedValues,
    hasSurvivalEstimatedValues,
    isLastItem,
    stockingCostsMillar,
    manualAverageWeight
  } = props;

  const lang = useAppSelector((state) => state.app?.lang);
  const user = useAppSelector((state) => state.auth.user);
  const currentFarm = useAppSelector((state) => state.farm?.currentFarm);
  const farmId = currentFarm?._id;
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const isVisionOnly = currentFarm?.metadata?.productOffering === 'visionOnly';

  const { trans } = getTrans();

  const {
    modelAverageWeight,
    averageWeight,
    growthLinear,
    growthDaily,
    weeklyGrowth,
    growthTwoWeeks,
    growthThreeWeeks,
    growth4w,
    cumulativeFcr,
    weeklyFcr,
    adjustedFcr,
    feedKgByHa,
    weeklyFeedGiven,
    totalFeedGivenKg,
    totalFeedGivenLbs,
    feedAsBiomassPercent,
    survival,
    survivalWithPartialHarvest,
    survivalFeed,
    biomassLbs,
    totalBiomassLbs,
    biomassLbsByHa,
    totalBiomassLbsByHa,
    animalsRemainingM2,
    animalsRemainingHa,
    weightCv,
    profitPerHaPerDay,
    totalProfit,
    rofi,
    totalRevenue,
    totalCosts,
    stockingCosts,
    feedCostPerKg,
    cumulativeOverheadCosts,
    cumulativeFeedCosts,
    costPerPound,
    costPoundHarvest,
    revenuePerPound,
    totalRevenuePound
  } = latestHistory ?? {};

  const latestSurvivalValue = formatNumber(survival, { fractionDigits: 0, lang, isPercentage: true });
  const latestFeedAsBiomassPercentValue = formatNumber(feedAsBiomassPercent, {
    fractionDigits: 2,
    lang,
    isPercentage: true
  });
  const latestSurvivalWithPartialHarvestValue = formatNumber(survivalWithPartialHarvest, {
    fractionDigits: 0,
    lang,
    isPercentage: true
  });
  const latestWeightCvValue = formatNumber(weightCv, { fractionDigits: 1, lang, isPercentage: true });
  const latestSurvivalFeedValue = formatNumber(survivalFeed, { fractionDigits: 2, lang, isPercentage: true });

  const {
    rofiAdminValue,
    profitPerHaPerDayAdminValue,
    costPerPoundAdminValue,
    costPoundHarvestAdminValue,
    totalProfitAdminValue,
    totalCostsAdminValue,
    stockingCostsAdminValue,
    stockingCostsMillarAdminValue,
    feedCostPerKgAdminValue,
    cumulativeOverheadCostsAdminValue,
    cumulativeFeedCostsAdminValue,
    totalRevenueAdminValue,
    revenuePerPoundAdminValue,
    totalRevenuePerPoundAdminValue
  } = useGetAdminFinancials({
    rofi,
    profitPerHaPerDay,
    costPerPound,
    costPoundHarvest,
    stockingCostsMillar,
    totalRevenue,
    revenuePerPound,
    totalRevenuePound,
    totalProfit,
    totalCosts,
    stockingCosts,
    feedCostPerKg,
    cumulativeOverheadCosts,
    cumulativeFeedCosts,
    indicators,
    farmId
  });

  type StatusIndicatorKeyOptions = {
    key: IndicatingKeys;
    fractionDigits: number;
    isPercentage?: boolean;
    isCurrency?: boolean;
    shouldAddZeros?: boolean;
    shouldConvertUnit?: boolean;
    convertUnit?: typeof convertUnitByDivision;
  };

  const statusIndicatorKeyMapping: Partial<Record<HistoryVariable, StatusIndicatorKeyOptions>> = {
    averageWeight: { key: 'abw', fractionDigits: 2 },
    biomassLbHa: {
      key: 'biomassLbsByHa',
      fractionDigits: 0,
      shouldConvertUnit: true,
      convertUnit: convertUnitByDivision
    },
    fcrCumulative: { key: 'fcr', fractionDigits: 2 },
    growthLinear: { key: 'growthLinear', fractionDigits: 2 },
    growth1WeekAvg: { key: 'growth', fractionDigits: 2 },
    growth2WeekAvg: { key: 'growth', fractionDigits: 2 },
    growthThreeWeeks: { key: 'growth', fractionDigits: 2 },
    growth4w: { key: 'growth', fractionDigits: 2 },
    survival: { key: 'survivalRate', fractionDigits: 2, isPercentage: true, shouldAddZeros: false },
    survivalWithPartialHarvest: { key: 'survivalRate', fractionDigits: 2, isPercentage: true, shouldAddZeros: false },
    costPerPound: {
      key: 'costPerPound',
      fractionDigits: 2,
      isCurrency: true,
      shouldConvertUnit: true,
      convertUnit: convertUnitByMultiplication
    },
    costPoundHarvest: {
      key: 'costPerPound',
      fractionDigits: 2,
      isCurrency: true,
      shouldConvertUnit: true,
      convertUnit: convertUnitByMultiplication
    },
    profitPerHaPerDay: { key: 'profitPerHaPerDay', fractionDigits: 2, isCurrency: true }
  };

  const { key, fractionDigits, isPercentage, isCurrency, shouldAddZeros, shouldConvertUnit, convertUnit } =
    statusIndicatorKeyMapping[variable] ?? {};

  const targetIndicator = indicators?.[key]?.current;
  const { color, val: targetValue } = targetIndicator ?? {};

  const indicatorIcon = !isVisionOnly ? indicatingStatusIconMap[color] : undefined;

  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);
  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');

  const indicatorValue = formatNumber(
    shouldConvertUnit ? convertUnit(targetValue, unitsConfig?.biomass) : targetValue,
    { fractionDigits, lang, isCurrency, isPercentage, shouldAddZeros }
  );

  const historyVariableComponentMap: Record<HistoryVariable, ReactNode> = {
    averageWeight: (
      <VariableRow
        title={trans('t_abw_g')}
        firstValue={formatNumber(averageWeight, {
          fractionDigits: 2,
          lang
        })}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
        {...(!!manualAverageWeight?.averageWeight && {
          firstValueBeforeElement: (
            <AverageWeightChangePopover
              modelAverageWeight={modelAverageWeight}
              updatedBy={manualAverageWeight?.updatedBy}
              containerProps={{ position: 'absolute', left: '0px', top: '14px' }}
            />
          )
        })}
        isVisionOnly={isVisionOnly}
      />
    ),
    growthLinear: (
      <VariableRow
        title={trans('t_growth_g_wk')}
        firstValue={formatNumber(growthLinear, {
          fractionDigits: 2,
          lang
        })}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
        isVisionOnly={isVisionOnly}
      />
    ),
    growthDaily: (
      <VariableRow
        title={trans('t_growth_g_day')}
        firstValue={formatNumber(growthDaily, {
          fractionDigits: 2,
          lang
        })}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
        isVisionOnly={isVisionOnly}
      />
    ),
    growth1WeekAvg: (
      <VariableRow
        title={trans('t_growth_monitored_x_week_g_wk', { weeks: 1 })}
        firstValue={formatNumber(weeklyGrowth, {
          fractionDigits: 2,
          lang
        })}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
        isVisionOnly={isVisionOnly}
      />
    ),
    growth2WeekAvg: (
      <VariableRow
        title={trans('t_growth_monitored_x_week_g_wk', { weeks: 2 })}
        firstValue={formatNumber(growthTwoWeeks, {
          fractionDigits: 2,
          lang
        })}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
        isVisionOnly={isVisionOnly}
      />
    ),
    growthThreeWeeks: (
      <VariableRow
        title={trans('t_growth_monitored_x_week_g_wk', { weeks: 3 })}
        firstValue={formatNumber(growthThreeWeeks, {
          fractionDigits: 2,
          lang
        })}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    growth4w: (
      <VariableRow
        title={trans('t_growth_monitored_x_week_g_wk', { weeks: 4 })}
        firstValue={formatNumber(growth4w, {
          fractionDigits: 2,
          lang
        })}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    fcrCumulative: (
      <VariableRow
        title={trans('t_fcr_cumulative')}
        firstValue={formatNumber(cumulativeFcr, {
          fractionDigits: 2,
          lang
        })}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    fcrWeekly: (
      <VariableRow
        title={trans('t_fcr_weekly')}
        firstValue={formatNumber(weeklyFcr, {
          fractionDigits: 2,
          lang
        })}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    adjustedFcr: (
      <VariableRow
        title={trans('t_fcr_adjusted')}
        firstValue={formatNumber(adjustedFcr, {
          fractionDigits: 2,
          lang
        })}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    kgPerHaPerDayGivenKg: (
      <VariableRow
        title={trans('t_feed_given_daily_kg_ha')}
        firstValue={formatNumber(feedKgByHa, {
          fractionDigits: 1,
          lang
        })}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    weeklyFeedGiven: (
      <VariableRow
        title={trans('t_feed_given_weekly_kg')}
        firstValue={formatNumber(weeklyFeedGiven, {
          fractionDigits: 0,
          lang
        })}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    totalFeedGivenKg: (
      <VariableRow
        title={trans('t_feed_given_kg')}
        firstValue={formatNumber(totalFeedGivenKg, {
          fractionDigits: 0,
          lang
        })}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
        hasEstimatedValues={hasFeedEstimatedValues}
      />
    ),
    totalFeedGivenLbs: (
      <VariableRow
        title={trans('t_feed_given_lg')}
        firstValue={formatNumber(totalFeedGivenLbs, {
          fractionDigits: 0,
          lang
        })}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
        hasEstimatedValues={hasFeedEstimatedValues}
      />
    ),
    biomassPercentage: (
      <VariableRow
        title={trans('t_feed_%_of_biomass')}
        firstValue={latestFeedAsBiomassPercentValue}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    survival: (
      <VariableRow
        title={trans('t_survival_live')}
        firstValue={latestSurvivalValue}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
        hasEstimatedValues={hasSurvivalEstimatedValues}
      />
    ),
    survivalFeed: (
      <VariableRow
        title={trans('t_kampi_survival')}
        firstValue={latestSurvivalFeedValue}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
        hasEstimatedValues={hasFeedEstimatedValues}
      />
    ),
    survivalWithPartialHarvest: (
      <VariableRow
        title={trans('t_survival_include_harvests')}
        firstValue={latestSurvivalWithPartialHarvestValue}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
        hasEstimatedValues={hasSurvivalEstimatedValues}
      />
    ),
    biomassLb: (
      <VariableRow
        title={trans('t_biomass_in_pond_unit', { unit: unitLabel })}
        firstValue={formatNumber(convertUnitByDivision(biomassLbs, unitsConfig?.biomass), { fractionDigits: 0, lang })}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    biomassLbTotal: (
      <VariableRow
        title={trans('t_biomass_include_harvests_unit', { unit: unitLabel })}
        firstValue={formatNumber(convertUnitByDivision(totalBiomassLbs, unitsConfig?.biomass), {
          fractionDigits: 0,
          lang
        })}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    biomassLbHa: (
      <VariableRow
        title={trans('t_biomass_in_pond_unit_ha', { unit: unitLabel })}
        firstValue={formatNumber(convertUnitByDivision(biomassLbsByHa, unitsConfig?.biomass), {
          fractionDigits: 0,
          lang
        })}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    totalBiomassLbHa: (
      <VariableRow
        title={trans('t_biomass_include_harvests_unit_ha', { unit: unitLabel })}
        firstValue={formatNumber(convertUnitByDivision(totalBiomassLbsByHa, unitsConfig?.biomass), {
          fractionDigits: 0,
          lang
        })}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    animalsRemainingM2: (
      <VariableRow
        title={trans('t_animals_in_pond_m2')}
        firstValue={formatNumber(animalsRemainingM2, {
          fractionDigits: 2,
          lang
        })}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    animalsRemainingHa: (
      <VariableRow
        title={trans('t_animals_in_pond_ha')}
        firstValue={formatNumber(animalsRemainingHa, {
          fractionDigits: 0,
          lang
        })}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    dispersion: (
      <VariableRow
        title={trans('t_dispersion_cv')}
        firstValue={latestWeightCvValue}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
        isVisionOnly={isVisionOnly}
      />
    ),
    profitPerHaPerDay: (
      <VariableRow
        title={trans('t_profit_include_dry_days_ha_day')}
        firstValue={profitPerHaPerDayAdminValue}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    totalProfit: (
      <VariableRow
        title={trans('t_profit')}
        firstValue={totalProfitAdminValue}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    rofi: (
      <VariableRow
        title={trans('t_rofi')}
        firstValue={rofiAdminValue}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    totalRevenue: (
      <VariableRow
        title={trans('t_revenue')}
        firstValue={totalRevenueAdminValue}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    revenuePerPound: (
      <VariableRow
        title={isBiomassUnitLbs ? trans('t_revenue_per_lb_live') : trans('t_revenue_per_kg_live')}
        firstValue={revenuePerPoundAdminValue}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    totalRevenuePound: (
      <VariableRow
        title={isBiomassUnitLbs ? trans('t_revenue_per_lb_include_harvest') : trans('t_revenue_per_kg_include_harvest')}
        firstValue={totalRevenuePerPoundAdminValue}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    totalCosts: (
      <VariableRow
        title={trans('t_cost_$')}
        firstValue={totalCostsAdminValue}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    stockingCosts: (
      <VariableRow
        title={trans('t_stocking_cost')}
        firstValue={stockingCostsAdminValue}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    stockingCostsMillar: (
      <VariableRow
        title={trans('t_cost_millar')}
        firstValue={stockingCostsMillarAdminValue}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    feedCostPerKg: (
      <VariableRow
        title={trans('t_feed_cost_per_kg')}
        firstValue={feedCostPerKgAdminValue}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    cumulativeOverheadCosts: (
      <VariableRow
        title={trans('t_overhead_cost_cumulative')}
        firstValue={cumulativeOverheadCostsAdminValue}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    cumulativeFeedCosts: (
      <VariableRow
        title={trans('t_feed_cost_$')}
        firstValue={cumulativeFeedCostsAdminValue}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    costPerPound: (
      <VariableRow
        title={trans('t_cost_per_unit_processed', { unit: unitLabel })}
        firstValue={costPerPoundAdminValue}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    ),
    costPoundHarvest: (
      <VariableRow
        title={trans('t_cost_per_unit_harvested', { unit: unitLabel })}
        firstValue={costPoundHarvestAdminValue}
        icon={indicatorIcon}
        secondValue={indicatorValue}
        isLastItem={isLastItem}
      />
    )
  };

  return <>{historyVariableComponentMap[variable]}</>;
}

type VariableRowProps = {
  title: string;
  firstValue: string | number;
  secondValue: string | number;
  icon: ReactNode;
  isLastItem: boolean;
  hasEstimatedValues?: boolean;
  firstValueBeforeElement?: ReactNode;
  isVisionOnly?: boolean;
};

function VariableRow(props: VariableRowProps) {
  const {
    title,
    firstValue,
    secondValue,
    icon,
    isLastItem,
    hasEstimatedValues,
    firstValueBeforeElement,
    isVisionOnly
  } = props;

  const estimatedValuesStyles: TextProps = hasEstimatedValues
    ? { py: '2xs', px: 'xs', bgColor: 'bg.gray.strong', borderRadius: 'full' }
    : {};
  const cellBorder = isLastItem ? 'none' : '1px solid {colors.gray.400}';
  return (
    <TableRow>
      <TableCell
        textAlign='start'
        pos='sticky'
        left={0}
        bgColor='white'
        zIndex={1}
        pe='xs-alt'
        borderBottom={cellBorder}
      >
        <Flex align='center' gap='2xs'>
          {icon || <Box w='20px' h='20px' />}
          <Text
            size='label200'
            maxW={{ base: 'auto', xl: '160px' }}
            overflow='hidden'
            textOverflow='ellipsis'
            title={title}
          >{`${title}${hasEstimatedValues ? ' *' : ''}`}</Text>
        </Flex>
      </TableCell>

      <TableCell
        ps={hasEstimatedValues || firstValueBeforeElement ? 'sm' : 0}
        position='relative'
        borderBottom={cellBorder}
      >
        {firstValueBeforeElement}
        <Text size='label200' {...estimatedValuesStyles}>
          {firstValue ?? '-'}
        </Text>
      </TableCell>
      {!isVisionOnly && (
        <TableCell borderBottom={cellBorder}>
          <Text size='label200'>{secondValue ?? '-'}</Text>
        </TableCell>
      )}
    </TableRow>
  );
}

function TableRow(props: TableRowProps) {
  return <Table.Row {...props} />;
}

function TableCell(props: TableCellProps) {
  return (
    <Table.Cell
      py='md'
      px={0}
      color='text.gray'
      textAlign='center'
      borderBottom='1px solid {colors.gray.400}'
      {...props}
    />
  );
}
