import { getTrans } from '@i18n/get-trans';

export function getDataCheckLabelMap() {
  const { trans } = getTrans();

  return {
    hasStockingDate: trans('t_stocking_date'),
    hasStockingQuantity: trans('t_animals_stocked'),
    hasStockingWeight: trans('t_stocking_weight_g'),
    hasStockingCosts: trans('t_stocking_cost'),
    hasDryDays: trans('t_dry_days'),
    hasMonitorings: trans('t_monitorings'),
    hasCarryingCapacity: trans('t_carrying_capacity'),
    hasTargetType: trans('t_target_type'),
    hasTargetSurvival: trans('t_target_survival'),
    hasTargetBiomassToHarvest: trans('t_target_biomass_to_harvest'),
    hasTargetCycleLength: trans('t_target_cycle_length'),
    hasTargetHarvestWeight: trans('t_target_harvest_weight'),
    hasTargetFCR: trans('t_target_fcr'),
    hasOverheadCostsPerHaPerDay: trans('t_overhead_cost_ha_day'),
    hasProjectedFeedType: trans('t_projected_feed_type'),
    hasExpectedFeed: trans('t_expected_feed'),
    hasFeedTableId: trans('t_feed_table'),
    hasFeedTypes: trans('t_feed_types'),
    hasProjectedGrowthGrams: trans('t_projected_growth_grams'),
    hasProjectedGrowthDays: trans('t_projected_growth_days'),
    hasProjectedSurvivalType: trans('t_projected_survival_type'),
    hasDailyMortalityPercent: trans('t_daily_mortality_percent'),
    hasExpectedTargetSurvival: trans('t_expected_target_survival'),
    hasExpectedTargetSurvivalDays: trans('t_expected_target_survival_days'),
    hasFeedDataForLastMonitoring: trans('t_feed_data_for_last_monitoring'),
    hasHarvestDate: trans('t_harvest_date'),
    hasSelectedProcessor: trans('t_selected_processor'),
    hasValidSelectedProcessor: trans('t_selected_processor_exists'),
    hasProfitProjection: trans('t_profit_projection'),
    hasProductionProjection: trans('t_production_projection'),
    hasCyleComparisonProjection: trans('t_cycle_comparison_projection'),
    hasActiveProcessorPriceLists: trans('t_active_processor_price_lists'),
    hasActiveFeedTable: trans('t_active_feed_table'),
    hasActiveFeedTypes: trans('t_active_feed_types'),
    hasMonitoringDaysConfig: trans('t_monitoring_days_config'),
    hasStartOfWeekConfig: trans('t_start_of_week_config'),
    hasFarmProductionDays: trans('t_farm_production_days'),
    hasFarmWeight: trans('t_farm_weight'),
    hasFarmFCR: trans('t_farm_fcr'),
    hasFarmCostPerPound: trans('t_farm_cost_per_pound'),
    hasFarmProfitPerHaPerDay: trans('t_farm_profit_per_ha_per_day'),
    hasFarmBiomassLbsHa: trans('t_farm_biomass_lb_ha'),
    hasFarmDryDays: trans('t_farm_dry_days'),
    hasFarmGrowthDensity: trans('t_farm_growth_density'),
    hasFarmDaysInNursery: trans('t_farm_days_in_nursery'),
    hasFarmNurserySuvival: trans('t_farm_nursery_survival'),
    hasFarmAvgStockingWeight: trans('t_farm_avg_stocking_weight')
  };
}
