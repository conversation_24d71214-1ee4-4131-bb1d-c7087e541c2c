import { getTrans } from '@i18n/get-trans';
import { convertUnitByDivision, convertUnitByMultiplication, formatNumber, isNumber } from '@utils/number';
import {
  PopulationHarvest,
  PopulationHarvestPlanProjectionProfitProjectionDataHeadon,
  PopulationHistory,
  PopulationManualAverageWeights,
  PopulationPartialHarvest
} from '@xpertsea/module-farm-sdk';
import { SeriesZonesOptionsObject, XAxisPlotLinesOptions } from 'highcharts';
import { DateTime } from 'luxon';
import { renderToString } from 'react-dom/server';
import { getDaysDiffBetweenDates } from '@screens/farm/helpers/farm-dates';
import { ProductionChartVariableOption } from '@screens/pond/components/pond-overview/hooks/use-production-chart-variables';
import { getCarryingCapacityPercent } from '@screens/farm-summary/helpers/pond-list';
import { shouldHideVariable } from '@screens/pond/helpers/production-chart-select-values-permissions';
import { DailyParams } from '@screens/settings/hooks/use-get-daily-data-trans';

const plotLineShared: Partial<XAxisPlotLinesOptions> = {
  width: 1.5,
  zIndex: 3,
  dashStyle: 'LongDash',
  label: {
    x: 0,
    y: 3,
    rotation: 0,
    useHTML: true,
    align: 'center'
  }
};

type LuxonDateType = DateTime<true> | DateTime<false>;

type ProductionPredictionsType = {
  date: string;
  averageWeight: number;
  cumulativeFcr: number;
  adjustedFcr: number;
  survival: number;
  biomassLbsByHa: number;
  totalBiomassLbs: number;
  totalRevenue: number;
  revenuePerPound: number;
  totalProfit: number;
  isEstimatedSurvival: number;
};

type getProductionPlotLinesDataParams = {
  maxX: number;
  timezone: string;
  harvestInfo: { harvestDate: string; harvestPlanDate: string };
  stockedAtLuxon: LuxonDateType;
  pondSize: number;
  partialHarvest: PopulationPartialHarvest[];
  partialHarvestPlanned: PopulationPartialHarvest[];
  productionPredictions: Partial<ProductionPredictionsType>[];
  graphCustomization: { partialHarvest?: boolean; fullHarvest?: boolean };
  estimatedSurvival: Record<string, number>;
  harvest: PopulationHarvest;
};

export function getProductionPlotLinesData(params: getProductionPlotLinesDataParams) {
  const {
    maxX,
    pondSize,
    harvestInfo,
    timezone,
    stockedAtLuxon,
    partialHarvest,
    partialHarvestPlanned,
    productionPredictions,
    graphCustomization,
    estimatedSurvival,
    harvest
  } = params;

  const { trans } = getTrans();

  const currentDate = DateTime.local({ zone: timezone }).set({ hour: 0, minute: 0, second: 0, millisecond: 0 });

  const plotLines: XAxisPlotLinesOptions[] = [];
  const series: HarvestSeriesType[] = [];

  //   FINAL HARVEST
  const finalHarvestDate = harvestInfo?.harvestDate || harvestInfo?.harvestPlanDate;
  if (finalHarvestDate && graphCustomization.fullHarvest) {
    const isRecorded = !!harvestInfo?.harvestDate;
    const harvestDateLuxon = DateTime.fromISO(isRecorded ? harvestInfo?.harvestDate : harvestInfo?.harvestPlanDate, {
      zone: timezone
    });
    const cycleDays = getDaysDiffBetweenDates({
      baseDate: harvestDateLuxon.toFormat('yyyy-MM-dd'),
      dateToCompare: stockedAtLuxon.toFormat('yyyy-MM-dd')
    });

    const finalHarvestPlotLine = getPlotLine({
      cycleDays,
      isPast: harvestDateLuxon < currentDate,
      isRecorded,
      id: `full${isRecorded ? '-recorded' : '-planned'}`,
      dashStyle: 'Solid',
      text: isRecorded ? trans('t_recorded_harvest') : trans('t_planned_harvest')
    });

    plotLines.push(finalHarvestPlotLine);

    if (cycleDays <= maxX) {
      const harvestInfo = isRecorded
        ? harvest
        : (productionPredictions?.find((ele) => ele.date === finalHarvestDate) ?? {});

      series.push({
        x: cycleDays,
        custom: {
          dateLuxon: harvestDateLuxon,
          isHarvest: true,
          ...harvestInfo,
          harvestType: 'full',
          isEstimatedSurvival: estimatedSurvival?.[finalHarvestDate]
        }
      });
    }
  }

  //   Partial Harvests
  if (graphCustomization.partialHarvest) {
    [partialHarvest, partialHarvestPlanned].forEach((hItem, i) => {
      hItem
        ?.filter((e) => e?.date)
        ?.forEach((item, itemIndex) => {
          const predictionData = productionPredictions?.find((ele) => ele.date === item.date)
            ? {
                ...productionPredictions?.find((ele) => ele.date === item.date),
                totalRevenue: item.revenue, // revenue should be from partial harvest planned info
                biomassLbsByHa: pondSize > 0 ? item.lbsHarvested / pondSize : 0, // lbsHarvested should be from partial harvest planned info
                revenuePerPound: item.revenue / item.lbsHarvested // revenuePerPound should be from partial harvest planned info
              }
            : {};

          const harvestDateLuxon = DateTime.fromISO(item.date, { zone: timezone });

          const isRecorded = i === 0;
          const cycleDays = getDaysDiffBetweenDates({
            baseDate: item.date,
            dateToCompare: stockedAtLuxon.toFormat('yyyy-MM-dd')
          });

          const partialHarvestPlotLine = getPlotLine({
            cycleDays,
            isPast: harvestDateLuxon < currentDate,
            isRecorded,
            id: `partial-` + isRecorded ? 'recorded' : 'planned',
            dashStyle: 'Solid',
            zIndex: 100 - itemIndex,
            text: `${trans('t_partial_harvest')} - ${isRecorded ? trans('t_recorded') : trans('t_planned')}`
          });

          plotLines.push(partialHarvestPlotLine);
          if (cycleDays <= maxX) {
            const harvestInfo = isRecorded
              ? {
                  averageWeight: item.weight,
                  date: item.date,
                  survival: item.survival,
                  totalRevenue: item.revenue,
                  biomassLbsByHa: pondSize > 0 ? item.lbsHarvested / pondSize : 0,
                  revenuePerPound: item.revenue / item.lbsHarvested
                }
              : predictionData;

            series.push({
              x: cycleDays,
              custom: {
                dateLuxon: harvestDateLuxon,
                isHarvest: true,
                harvestType: 'partial',
                isEstimatedSurvival: estimatedSurvival?.[item.date],
                ...harvestInfo
              }
            });
          }
        });
    });
  }

  return {
    plotLines,
    series
  };
}

const getPlotLine = ({
  cycleDays,
  isPast,
  isRecorded,
  text,
  ...rest
}: Omit<XAxisPlotLinesOptions, 'label'> & {
  cycleDays: number;
  isPast: boolean;
  isRecorded: boolean;
  text: string;
}): XAxisPlotLinesOptions => {
  return {
    ...plotLineShared,
    ...rest,
    color: isPast ? '#B1B1B1' : '#1C1B1F',
    value: cycleDays,
    label: {
      ...plotLineShared.label,
      formatter(): string {
        return generateLabel({ isPast, isRecorded, text });
      }
    }
  };
};

const generateLabel = ({ isPast, isRecorded, text }: { isPast: boolean; isRecorded: boolean; text: string }) => {
  const bgColor = isPast ? '#F2F2F2' : '#000F25';
  const iconColor = isPast ? '#909090' : 'white';
  const textColor = isPast ? '#000F25' : 'white';

  return renderToString(
    <div
      className='chartPlotLine'
      style={{
        background: bgColor,
        gap: '6px',
        display: 'flex',
        alignItems: 'center',
        borderRadius: '20px',
        position: 'relative',
        width: 'fit-content',
        height: '24px',
        zIndex: 101
      }}
    >
      <div className='chartPlotLineIcon' style={{ padding: '4px 6px' }}>
        {!isRecorded ? (
          <svg width='12px' height='12px' fill='none'>
            <path
              d='M5.16522 0.56341C5.46828 -0.187803 6.53182 -0.187803 6.83488 0.56341L7.99751 3.4453C8.03998 3.55058 8.13823 3.62288 8.25138 3.63211L11.1717 3.8704C11.9636 3.93501 12.2896 4.91888 11.693 5.44356L9.47704 7.39258C9.39503 7.46471 9.35868 7.5757 9.38215 7.68237L10.0908 10.9033C10.2678 11.7079 9.36057 12.3079 8.68947 11.83L6.17409 10.0391C6.06992 9.96489 5.93018 9.96489 5.82601 10.0391L3.31063 11.83C2.63952 12.3079 1.73231 11.7079 1.90932 10.9033L2.61795 7.68237C2.64142 7.57571 2.60507 7.46471 2.52306 7.39258L0.306973 5.44357C-0.289598 4.9189 0.036431 3.93501 0.828272 3.8704L3.74872 3.63211C3.86187 3.62288 3.96012 3.55058 4.00259 3.4453L5.16522 0.56341Z'
              fill={iconColor}
            />
          </svg>
        ) : (
          <svg width='12px' height='12px' fill='none'>
            <path
              d='M4.5 2C4.5 1.72386 4.27614 1.5 4 1.5C3.72386 1.5 3.5 1.72386 3.5 2V2.50001C3.27048 2.50015 3.07038 2.50157 2.90249 2.51529C2.70482 2.53144 2.50821 2.56709 2.31902 2.66349C2.03677 2.8073 1.8073 3.03677 1.66349 3.31902C1.56709 3.5082 1.53144 3.70481 1.51529 3.90249C1.49999 4.08976 1.49999 4.31715 1.5 4.58071V8.41929C1.49999 8.68285 1.49999 8.91024 1.51529 9.09752C1.53144 9.29519 1.56709 9.4918 1.66349 9.68099C1.8073 9.96323 2.03677 10.1927 2.31902 10.3365C2.50821 10.4329 2.70482 10.4686 2.90249 10.4847C3.08975 10.5 3.31719 10.5 3.58073 10.5H8.41935C8.68289 10.5 8.91025 10.5 9.09752 10.4847C9.29519 10.4686 9.4918 10.4329 9.68099 10.3365C9.96323 10.1927 10.1927 9.96323 10.3365 9.68099C10.4329 9.4918 10.4686 9.29519 10.4847 9.09752C10.5 8.91023 10.5 8.68285 10.5 8.41928V4.58072C10.5 4.31715 10.5 4.08977 10.4847 3.90249C10.4686 3.70481 10.4329 3.5082 10.3365 3.31902C10.1927 3.03677 9.96323 2.8073 9.68099 2.66349C9.4918 2.56709 9.29519 2.53144 9.09752 2.51529C8.92963 2.50157 8.72952 2.50015 8.5 2.50001V2C8.5 1.72386 8.27614 1.5 8 1.5C7.72386 1.5 7.5 1.72386 7.5 2V2.5H4.5V2ZM6.23087 4.65497C6.14547 4.44965 5.85461 4.44965 5.76921 4.65497L5.37449 5.60397C5.33872 5.68997 5.25811 5.74896 5.16532 5.75702L4.23114 5.83825C4.01677 5.85689 3.92453 6.11963 4.08021 6.26818L4.7898 6.94529C4.85415 7.00669 4.88103 7.09765 4.86039 7.18417L4.62008 8.19163C4.56848 8.40793 4.80379 8.5789 4.99357 8.46298L5.86972 7.92783C5.94973 7.87897 6.05035 7.87897 6.13035 7.92783L7.00651 8.46298C7.19628 8.5789 7.43159 8.40793 7.38 8.19163L7.13976 7.18448C7.11908 7.0978 7.14611 7.00666 7.2107 6.94527L7.9227 6.26852C8.07885 6.12009 7.98669 5.85685 7.77206 5.83825L6.8348 5.75699C6.74198 5.74894 6.66134 5.68995 6.62556 5.60393L6.23087 4.65497Z'
              fill={iconColor}
            />
          </svg>
        )}
      </div>
      <p
        style={{
          color: textColor,
          fontSize: '12px',
          fontWeight: 600,
          marginInlineEnd: '10px',
          position: 'relative',
          zIndex: 101
        }}
      >
        {text}
      </p>
    </div>
  );
};
export type DailyParamPointCustom = { dateLuxon: LuxonDateType; name?: string; isDailyParam?: boolean };
type DailyParamPoint = {
  custom: DailyParamPointCustom;
  x: number;
  y: number;
};

export function getDailyParamToolTip(
  point: DailyParamPoint,
  options: { lang: string; dailyWithUnitsTransMap: Record<DailyParams, string> }
) {
  const { trans } = getTrans();
  const { lang, dailyWithUnitsTransMap } = options;
  const rowStyles = { display: 'flex', gap: '6px' };
  const cycleDays = point.x;
  const value = point.y;

  const { dateLuxon, name } = point.custom;
  const title = dailyWithUnitsTransMap[name as DailyParams] ?? name;
  return renderToString(
    <div
      style={{
        padding: '10px 16px',
        fontSize: '12px',
        fontWeight: 600,
        color: '#1C1B1F',
        backgroundColor: '#E6E6E6',
        borderRadius: '8px',
        minWidth: '161px',
        lineHeight: '16px',
        display: 'flex',
        flexDirection: 'column',
        gap: '10px'
      }}
    >
      <div style={{ fontSize: '14px' }}>{title}</div>
      <div>{dateLuxon.toFormat('MMM dd, yyyy', { locale: lang })} </div>

      {cycleDays && <div> {trans('t_count_days_of_culture', { count: cycleDays })} </div>}

      <hr style={{ borderColor: '#b1b1b1' }} />

      <div style={rowStyles}>
        <p>{trans('t_value')}:</p>
        <p>{value ? formatNumber(value, { lang, fractionDigits: 1 }) : '-'}</p>
      </div>
    </div>
  );
}

export type HarvestSeriesType = {
  custom: Partial<ProductionPredictionsType> & {
    dateLuxon: LuxonDateType;
    isHarvest?: boolean;
    harvestType?: 'partial' | 'full';
  };
  x: number;
  y?: number;
};

export function getHarvestTooltip(
  point: HarvestSeriesType,
  options: {
    lang: string;
    isBiomassUnitLbs: boolean;
    unitsConfig: { biomass: 'kg' | 'lbs' };
    isAdmin?: boolean;
    isSupervisor?: boolean;
  }
) {
  const { trans } = getTrans();
  const { lang, isBiomassUnitLbs, unitsConfig, isAdmin, isSupervisor } = options;
  const rowStyles = { display: 'flex', gap: '6px' };
  const cycleDays = point.x;

  const {
    averageWeight,
    survival,
    biomassLbsByHa,
    totalRevenue,
    revenuePerPound,
    dateLuxon,
    harvestType,
    isEstimatedSurvival
  } = point.custom;

  return renderToString(
    <div
      style={{
        padding: '10px 16px',
        fontSize: '12px',
        fontWeight: 600,
        color: '#1C1B1F',
        backgroundColor: '#E6E6E6',
        borderRadius: '8px',
        minWidth: '161px',
        lineHeight: '16px',
        display: 'flex',
        flexDirection: 'column',
        gap: '10px',
        position: 'relative',
        zIndex: 999
      }}
    >
      <div style={{ fontSize: '14px' }}>
        {harvestType === 'full' ? trans('t_final_harvest') : trans('t_partial_harvest')}
      </div>
      <div>{dateLuxon.toFormat('MMM dd, yyyy', { locale: lang })} </div>

      {cycleDays && <div> {trans('t_count_days_of_culture', { count: cycleDays })} </div>}

      <hr style={{ borderColor: '#b1b1b1' }} />

      <div style={rowStyles}>
        <p>{trans('t_abw_g')}:</p>
        <p>{averageWeight ? formatNumber(averageWeight, { lang, fractionDigits: 2 }) : '-'}</p>
      </div>

      <div style={rowStyles}>
        <p>{trans('t_survival')}:</p>
        <p>
          {survival
            ? `${formatNumber(survival, { lang, fractionDigits: 2, isPercentage: true })} ${isEstimatedSurvival ? '*' : ''}`
            : '-'}
        </p>
      </div>

      <div style={rowStyles}>
        <p>{isBiomassUnitLbs ? trans('t_biomass_lb_ha') : trans('t_biomass_kg_ha')}:</p>
        <p>
          {biomassLbsByHa
            ? `${formatNumber(convertUnitByDivision(biomassLbsByHa, unitsConfig?.biomass), { lang, fractionDigits: 0 })}`
            : '-'}
        </p>
      </div>

      {!shouldHideVariable('totalRevenue', { isAdmin, isSupervisor }) && (
        <>
          <hr style={{ borderColor: '#b1b1b1' }} />
          <h2 style={{ fontSize: '14px' }}>{trans('t_financials')}</h2>
          <div style={rowStyles}>
            <p>{trans('t_revenue')}:</p>
            <p>{totalRevenue ? `${formatNumber(totalRevenue, { lang, fractionDigits: 0 })}` : '-'}</p>
          </div>
          <div style={rowStyles}>
            <p>{isBiomassUnitLbs ? trans('t_revenue_per_pound') : trans('t_revenue_per_kg')}:</p>
            <p>
              {revenuePerPound
                ? `${formatNumber(convertUnitByMultiplication(revenuePerPound, unitsConfig?.biomass), { lang, fractionDigits: 2, isCurrency: true })}`
                : '-'}
            </p>
          </div>
        </>
      )}
    </div>
  );
}

export type CapacityToolTipCustomPoint = {
  dateLuxon: LuxonDateType;
  isCarryingCapacity?: boolean;
  carryingCapacity?: number;
  biomassLbsByHa?: number;
  carryingCapacityPercent?: number;
  cycleDays?: number;
};

type GetCapacityTooltipPoint = {
  custom: CapacityToolTipCustomPoint;
  x: number;
  y: number;
};

export function getCapacityTooltip(
  point: GetCapacityTooltipPoint,
  options: { lang: string; isBiomassUnitLbs: boolean; unitsConfig: { biomass: 'kg' | 'lbs' } }
) {
  const { lang, isBiomassUnitLbs, unitsConfig } = options;

  const { trans } = getTrans();

  const rowStyles = { display: 'flex', gap: '6px' };
  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');
  const {
    carryingCapacity,
    biomassLbsByHa,
    carryingCapacityPercent,
    dateLuxon,
    cycleDays: customCycleDays
  } = point.custom;
  const cycleDays = customCycleDays ?? point.x;
  return renderToString(
    <div
      style={{
        padding: '10px 16px',
        fontSize: '12px',
        fontWeight: 600,
        color: '#1C1B1F',
        backgroundColor: '#E6E6E6',
        borderRadius: '8px',
        minWidth: '161px',
        lineHeight: '16px',
        display: 'flex',
        flexDirection: 'column',
        gap: '10px'
      }}
    >
      <div>{dateLuxon.toFormat('MMM dd, yyyy', { locale: lang })} </div>

      {cycleDays && <div> {trans('t_count_days_of_culture', { count: cycleDays })} </div>}

      <hr style={{ borderColor: '#b1b1b1' }} />

      <div style={rowStyles}>
        <p>{trans('t_%_of_carrying_capacity')}:</p>
        <p>
          {isNumber(carryingCapacityPercent)
            ? `${formatNumber(carryingCapacityPercent, { lang, fractionDigits: 0 })}%`
            : '-'}
        </p>
      </div>
      <div style={rowStyles}>
        <p>{trans('t_biomass_in_pond_unit_ha', { unit: unitLabel })}:</p>
        <p>
          {isNumber(biomassLbsByHa)
            ? formatNumber(convertUnitByDivision(biomassLbsByHa, unitsConfig?.biomass), { lang, fractionDigits: 0 })
            : '-'}
        </p>
      </div>
      <div style={rowStyles}>
        <p>{isBiomassUnitLbs ? trans('t_carrying_capacity_lb_ha') : trans('t_carrying_capacity_kg_ha')}:</p>
        <p>
          {carryingCapacity
            ? formatNumber(convertUnitByDivision(carryingCapacity, unitsConfig?.biomass), { lang, fractionDigits: 0 })
            : '-'}
        </p>
      </div>
    </div>
  );
}

export type PopulationHistoryItem = Omit<PopulationHistory, '__typename'>;

export type ChartDataCustom = {
  dateLuxon: DateTime<true> | DateTime<false>;
  variableKey: ProductionChartVariableOption;
  additionalText: {
    averageWeight: string;
    totalRevenue: string | number;
    averageDailyFeed: string | number;
    biomassTotal: string | number;
    isManualAverageWeight: boolean;
  };
};

export type ChartData = {
  y: number;
  x: number;
  custom?: ChartDataCustom;
};
export type ProductionDataWithCostMillar = PopulationHistoryItem & { stockingCostsMillar: number };

interface GetMaxProductionValueBasedOnChartVariableArgs {
  variableKey: ProductionChartVariableOption;
  productionData: ProductionDataWithCostMillar[];
  timezone: string;
  stockedAtLuxon: DateTime<true> | DateTime<false>;
  lang: string;
  isPrediction?: boolean;
  manualAverageWeights: PopulationManualAverageWeights[];
  shouldConvertUnit: boolean;
  unitsConfig: { biomass: 'kg' | 'lbs' };
  convertUnit: typeof convertUnitByDivision;
}

export function getChartDataBasedOnVariableKey(args: GetMaxProductionValueBasedOnChartVariableArgs) {
  const {
    variableKey,
    productionData,
    timezone,
    stockedAtLuxon,
    lang,
    isPrediction,
    manualAverageWeights,
    unitsConfig,
    shouldConvertUnit,
    convertUnit
  } = args;

  const { trans } = getTrans();

  if (!productionData?.length) {
    return { chartData: [], maxY: 0, minY: 0 };
  }

  const keysNotToIncludeBefore21Days: ProductionChartVariableOption[] = ['profitPerHaPerDay', 'totalProfit'];

  let minY: number;
  let maxY: number;
  const chartData: ChartData[] = [];

  productionData.forEach((item) => {
    const value = shouldConvertUnit ? convertUnit(item[variableKey], unitsConfig?.biomass) : item[variableKey];
    const hasValue = value || value === 0;

    const manualAverageWeight = manualAverageWeights?.find((x) => x.date === item.date);

    const historyDateLuxon = DateTime.fromISO(item.date, { zone: timezone }).startOf('day');
    const cycleDays = getDaysDiffBetweenDates({
      baseDate: historyDateLuxon.toFormat('yyyy-MM-dd'),
      dateToCompare: stockedAtLuxon.toFormat('yyyy-MM-dd')
    });

    const isBefore21DaysVariable = cycleDays < 21 && keysNotToIncludeBefore21Days.includes(variableKey);

    const fcrIsOutOfRange = variableKey === 'cumulativeFcr' && (value < 0 || value > 5);

    if (hasValue && !isBefore21DaysVariable && !fcrIsOutOfRange) {
      const hasMinY = minY || minY === 0;
      const hasMaxY = maxY || maxY === 0;
      minY = hasMinY ? Math.min(minY, value) : value;
      maxY = hasMaxY ? Math.max(maxY, value) : value;

      const averageWeightValue = item.averageWeight
        ? (formatNumber(item.averageWeight, { lang, fractionDigits: 2 }) as string)
        : '-';
      const totalRevenueValue = item.totalRevenue
        ? formatNumber(item.totalRevenue, { lang, fractionDigits: 0, isCurrency: true })
        : '-';
      const biomassTotalValue = item.totalBiomassLbs
        ? formatNumber(convertUnitByDivision(item.totalBiomassLbs, unitsConfig?.biomass), { lang, fractionDigits: 0 })
        : '-';

      const avgDailyFeed = item.weeklyFeedGiven
        ? formatNumber(item.weeklyFeedGiven / 7, { lang, fractionDigits: 0 })
        : undefined;

      const dailyFeedBags = item.weeklyFeedGiven
        ? formatNumber(item.weeklyFeedGiven / 7 / 25, { lang, fractionDigits: 1 })
        : undefined;

      const averageDailyFeedValue =
        avgDailyFeed && dailyFeedBags
          ? `${avgDailyFeed} ${trans('t_kg')} (${dailyFeedBags} ${trans('t_bags')})`
          : ' - ';

      chartData.push({
        y: value,
        x: cycleDays,
        custom: {
          dateLuxon: historyDateLuxon,
          variableKey,
          additionalText: {
            averageWeight: averageWeightValue,
            totalRevenue: totalRevenueValue,
            averageDailyFeed: averageDailyFeedValue,
            biomassTotal: biomassTotalValue,
            isManualAverageWeight: !!manualAverageWeight?.averageWeight
          }
        }
      });
    }
  });

  //if prediction has only last history date return nothing
  if (isPrediction && chartData.length <= 1) {
    return { chartData: [], maxY: maxY, minY: minY };
  }

  return { chartData, maxY: maxY, minY: minY };
}

function checkIfVariablesAreSameGroup(
  chartVariableOne: ProductionChartVariableOption,
  chartVariableTwo: ProductionChartVariableOption
) {
  const fcrGroup: ProductionChartVariableOption[] = ['cumulativeFcr', 'weeklyFcr', 'adjustedFcr'];
  const growthGroup: ProductionChartVariableOption[] = [
    'growthLinear',
    'growthDaily',
    'weeklyGrowth',
    'growthTwoWeeks',
    'growthThreeWeeks',
    'growth4w'
  ];
  const totalFinanceGroup: ProductionChartVariableOption[] = [
    'totalRevenue',
    'totalProfit',
    'totalCosts',
    'cumulativeFeedCosts',
    'stockingCosts',
    'cumulativeOverheadCosts'
  ];
  const financeGroup: ProductionChartVariableOption[] = [
    'costPerPound',
    'revenuePerPound',
    'totalRevenuePound',
    'profitPerPound',
    'costPoundHarvest'
  ];
  const survivalGroup: ProductionChartVariableOption[] = ['survival', 'survivalWithPartialHarvest', 'survivalFeed'];
  const biomassGroup: ProductionChartVariableOption[] = ['biomassLbs', 'totalBiomassLbs'];
  const biomassHaGroup: ProductionChartVariableOption[] = ['biomassLbsByHa', 'totalBiomassLbsByHa'];

  const groups = [fcrGroup, growthGroup, totalFinanceGroup, financeGroup, survivalGroup, biomassGroup, biomassHaGroup];

  return groups.some((group) => group.includes(chartVariableOne) && group.includes(chartVariableTwo));
}

interface GetChartMinMaxValuesBasedOnComparisonArgs {
  variableOneDataMinY: number;
  variableOneDataMaxY: number;
  variableOnePredictionMinY: number;
  variableOnePredictionMaxY: number;
  variableOneCycleComparisonMinY: number;
  variableOneCycleComparisonMaxY: number;
  variableTwoDataMinY: number;
  variableTwoPredictionMinY: number;
  variableTwoCycleComparisonMinY: number;
  variableTwoDataMaxY: number;
  variableTwoPredictionMaxY: number;
  variableTwoCycleComparisonMaxY: number;
  hasComparison: boolean;
  chartVariableOne: ProductionChartVariableOption;
  chartVariableTwo: ProductionChartVariableOption;
}

export function getChartMinMaxValuesBasedOnComparison(args: GetChartMinMaxValuesBasedOnComparisonArgs) {
  const {
    variableOneDataMinY,
    variableOnePredictionMinY,
    variableOneCycleComparisonMinY,
    hasComparison,
    variableOneDataMaxY,
    variableOnePredictionMaxY,
    variableOneCycleComparisonMaxY,
    variableTwoDataMinY,
    variableTwoPredictionMinY,
    variableTwoCycleComparisonMinY,
    variableTwoDataMaxY,
    variableTwoPredictionMaxY,
    variableTwoCycleComparisonMaxY,
    chartVariableOne,
    chartVariableTwo
  } = args;

  const areVariablesInSameGroup = checkIfVariablesAreSameGroup(chartVariableOne, chartVariableTwo);

  const variableOneMinY = Math.min(variableOneDataMinY ?? 0, variableOnePredictionMinY ?? 0);
  const variableOneMaxY = Math.max(variableOneDataMaxY ?? 0, variableOnePredictionMaxY ?? 0);

  const variableTwoMinY = Math.min(variableTwoDataMinY ?? 0, variableTwoPredictionMinY ?? 0);
  const variableTwoMaxY = Math.max(variableTwoDataMaxY ?? 0, variableTwoPredictionMaxY ?? 0);

  if (!hasComparison) {
    if (areVariablesInSameGroup) {
      const unifiedMinY = Math.min(variableOneMinY, variableTwoMinY);
      const unifiedMaxY = Math.max(variableOneMaxY, variableTwoMaxY);
      return {
        variableOneMinY: unifiedMinY,
        variableOneMaxY: unifiedMaxY,
        variableTwoMinY: unifiedMinY,
        variableTwoMaxY: unifiedMaxY
      };
    }

    return {
      variableOneMinY,
      variableOneMaxY,
      variableTwoMinY,
      variableTwoMaxY
    };
  }

  const variableOneMinYWithCycle =
    variableOneCycleComparisonMinY || variableOneCycleComparisonMinY === 0
      ? Math.min(variableOneMinY, variableOneCycleComparisonMinY)
      : variableOneMinY;
  const variableOneMaxYWithCycle =
    variableOneCycleComparisonMaxY || variableOneCycleComparisonMaxY === 0
      ? Math.max(variableOneMaxY, variableOneCycleComparisonMaxY)
      : variableOneMaxY;

  const variableTwoMinYWithCycle =
    variableTwoCycleComparisonMinY || variableTwoCycleComparisonMinY === 0
      ? Math.min(variableTwoMinY, variableTwoCycleComparisonMinY)
      : variableTwoMinY;
  const variableTwoMaxYWithCycle =
    variableTwoCycleComparisonMaxY || variableTwoCycleComparisonMaxY === 0
      ? Math.max(variableTwoMaxY, variableTwoCycleComparisonMaxY)
      : variableTwoMaxY;

  if (areVariablesInSameGroup) {
    const unifiedMinY = Math.min(variableOneMinYWithCycle, variableTwoMinYWithCycle);
    const unifiedMaxY = Math.max(variableOneMaxYWithCycle, variableTwoMaxYWithCycle);

    return {
      variableOneMinY: unifiedMinY,
      variableOneMaxY: unifiedMaxY,
      variableTwoMinY: unifiedMinY,
      variableTwoMaxY: unifiedMaxY
    };
  }

  return {
    variableOneMinY: variableOneMinYWithCycle,
    variableOneMaxY: variableOneMaxYWithCycle,
    variableTwoMinY: variableTwoMinYWithCycle,
    variableTwoMaxY: variableTwoMaxYWithCycle
  };
}

export function addStockingCostsMillarToProductionData(
  productionData: PopulationHistoryItem[],
  stockingCostsMillar: number
): ProductionDataWithCostMillar[] {
  if (!productionData) return [];
  return productionData?.map((item) => {
    return { ...item, stockingCostsMillar };
  });
}

type AddCostsMillarAndProfitToProductionDataArgs = {
  productionData: PopulationHistoryItem[];
  stockingCostsMillar: number;
  profitMap: Record<string, Omit<PopulationHarvestPlanProjectionProfitProjectionDataHeadon, '__typename'>>;
};

export function addCostsMillarAndProfitToProductionData(
  args: AddCostsMillarAndProfitToProductionDataArgs
): ProductionDataWithCostMillar[] {
  const { productionData, stockingCostsMillar, profitMap } = args;
  if (!productionData) return [];
  return productionData?.map((item) => {
    const profitData = profitMap?.[item.date];
    return { ...profitData, ...item, stockingCostsMillar };
  });
}

function interpolateColor(startColor: number[], endColor: number[], factor: number) {
  const result = startColor.slice();
  for (let i = 0; i < 3; i++) {
    result[i] = Math.round(result[i] + factor * (endColor[i] - startColor[i]));
  }
  return `rgb(${result[0]}, ${result[1]}, ${result[2]})`;
}

function hexToRgb(hex: string) {
  const bigint = parseInt(hex.slice(1), 16);
  return [bigint >> 16, (bigint >> 8) & 255, bigint & 255];
}

export function getCarryingCapacityColor(capacity: number) {
  const grey = hexToRgb('#E6E6E6');
  const red = hexToRgb('#C73434');

  if (capacity <= 60) {
    return '#E6E6E6';
  }

  if (capacity >= 100) {
    return '#000000';
  }
  // Linear interpolation between grey and red for capacities between 60% and 100%
  const factor = (capacity - 60) / 40;
  return interpolateColor(grey, red, factor); // Gradient from grey to red
}

export type CarryingCapacitySeries = {
  x: number;
  custom: {
    dateLuxon: DateTime<true> | DateTime<false>;
    isCarryingCapacity: boolean;
    carryingCapacity: number;
    biomassLbsByHa: number;
    carryingCapacityPercent: number;
  };
};

interface GetCarryingCapacitySeriesAndZonesArgs {
  productionData: PopulationHistoryItem[];
  carryingCapacity: number;
  stockedAtLuxon: LuxonDateType;
  timezone: string;
}

export function getCarryingCapacitySeriesAndZones(args: GetCarryingCapacitySeriesAndZonesArgs) {
  const { productionData, carryingCapacity, stockedAtLuxon, timezone } = args;
  if (!productionData?.length) {
    return { carryingCapacitySeries: [], carryingCapacityZones: [] };
  }
  const carryingCapacityZones: SeriesZonesOptionsObject[] = [];
  const carryingCapacitySeries: CarryingCapacitySeries[] = productionData
    ?.map((item, index, array) => {
      const { date, biomassLbsByHa } = item;

      const nextItem = array[index + 1];
      const historyDateLuxon = DateTime.fromISO(date, { zone: timezone }).startOf('day');
      const cycleDays = getDaysDiffBetweenDates({
        baseDate: historyDateLuxon.toFormat('yyyy-MM-dd'),
        dateToCompare: stockedAtLuxon.toFormat('yyyy-MM-dd')
      });

      const carryingCapacityPercent = getCarryingCapacityPercent({
        biomassLbsByHa: biomassLbsByHa,
        carryingCapacity
      });
      const carryingCapacityPercentFormatted = formatNumber(carryingCapacityPercent, {
        fractionDigits: 3,
        asNumber: true
      }) as number;

      const nextCarryingCapacityPercent = getCarryingCapacityPercent({
        biomassLbsByHa: nextItem?.biomassLbsByHa,
        carryingCapacity
      });
      const nextCarryingCapacityFormatted = formatNumber(nextCarryingCapacityPercent, {
        fractionDigits: 3,
        asNumber: true
      }) as number;

      // Calculate average carrying capacity between two points for color interpolation
      const averageCarryingCapacity = isNumber(nextCarryingCapacityPercent)
        ? (carryingCapacityPercentFormatted + nextCarryingCapacityFormatted) / 2
        : carryingCapacityPercentFormatted;

      carryingCapacityZones.push({
        value: cycleDays,
        color: getCarryingCapacityColor(averageCarryingCapacity)
      });

      return {
        x: cycleDays,
        custom: {
          dateLuxon: historyDateLuxon,
          isCarryingCapacity: true,
          carryingCapacity,
          biomassLbsByHa,
          carryingCapacityPercent: carryingCapacityPercentFormatted
        }
      };
    })
    .filter((item) => item !== null);

  return { carryingCapacitySeries, carryingCapacityZones };
}

export type ChartSeriesMainNames = 'variableOne' | 'variableTwo' | 'dailyParamVariableOne' | 'dailyParamVariableTwo';
export type ChartVarsList = {
  name: ChartSeriesMainNames;
  label: string;
  value: string;
}[];

export function getSelectedVariableYAxisIndex(variableName: ChartSeriesMainNames, chartSelectedVars: ChartVarsList) {
  const index = chartSelectedVars.findIndex((variable) => variable.name === variableName);
  if (index > 1) return 1;
  return index !== -1 ? index : 0;
}

type GetSelectedVariablesMinAndMaxYArgs = {
  chartSelectedVars: ChartVarsList;
  dailyParamFirstSeriesMin: number;
  dailyParamFirstSeriesMax: number;
  dailyParamSecondSeriesMin: number;
  dailyParamSecondSeriesMax: number;
  variableOneMinY: number;
  variableOneMaxY: number;
  variableTwoMinY: number;
  variableTwoMaxY: number;
};

export function getSelectedVariablesMinAndMaxY(args: GetSelectedVariablesMinAndMaxYArgs) {
  const {
    chartSelectedVars,
    dailyParamFirstSeriesMin,
    dailyParamFirstSeriesMax,
    dailyParamSecondSeriesMin,
    dailyParamSecondSeriesMax,
    variableOneMinY,
    variableOneMaxY,
    variableTwoMinY,
    variableTwoMaxY
  } = args;

  return chartSelectedVars.map((variable) => {
    const { name, value } = variable;
    const isDailyParamVariable = name.includes('daily');

    if (name.includes('One')) {
      return {
        name,
        value,
        minY: isDailyParamVariable ? dailyParamFirstSeriesMin : variableOneMinY,
        maxY: isDailyParamVariable ? dailyParamFirstSeriesMax : variableOneMaxY
      };
    }

    return {
      name,
      value,
      minY: isDailyParamVariable ? dailyParamSecondSeriesMin : variableTwoMinY,
      maxY: isDailyParamVariable ? dailyParamSecondSeriesMax : variableTwoMaxY
    };
  });
}
