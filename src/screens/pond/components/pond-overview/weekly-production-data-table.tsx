import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import {
  getHistoryVariablesLabelMap,
  HistoryVariable
} from '@screens/pond/components/modals/hooks/use-get-history-view-variables';
import { getPondState } from '@screens/pond/helpers/get-pond-state';
import { getHistoryWeeklyData } from '@screens/pond/helpers/population-history';
import { Box, BoxProps, Flex, Table, TableCellProps, Text, TextProps } from '@chakra-ui/react';
import { CardContainer } from '@components/card-container/card-container';
import { HistoryChangeVariablesModal } from '@screens/pond/components/modals/history-change-variables-modal';
import { DateTime } from 'luxon';
import { getDaysDiffBetweenDates } from '@screens/farm/helpers/farm-dates';
import { convertUnitByDivision, formatNumber } from '@utils/number';
import { useGetAdminFinancials } from '@screens/pond/helpers/use-admin-financials';
import { actionsName } from '@utils/segment';
import {
  PopulationHarvestPlanIndicators,
  PopulationHistory,
  PopulationManualAverageWeights
} from '@xpertsea/module-farm-sdk';
import { useAnalytics } from '@hooks/use-analytics';
import { ReactNode, useRef } from 'react';
import { MoreHoriz } from '@icons/more-horiz/more-horiz';
import { usePermission } from '@hooks/use-permission';
import { getHistoryVariables } from '@screens/pond/components/pond-overview/helpers/get-history-variables';
import { AverageWeightChangePopover } from '@screens/data-updater/components/average-weight-change-popover';
import {
  ExportWeeklyProductionDataModal,
  ExportWeeklyProductionDataModalProps
} from '@screens/pond/components/pond-overview/export-weekly-production-data-modal';
import { MenuButton, MenuContent, MenuItem, MenuRoot } from '@components/ui/menu';
import { TableContainer } from '@components/ui/table-container';
import { InfoIconToolTip } from '@components/tooltip/info-icon-tool-tip';

export function WeeklyProductionDataTable(props: BoxProps & { tableMaxHeight?: BoxProps['maxH'] }) {
  const { tableMaxHeight, ...rest } = props;
  const { trans } = getTrans();
  const preferences = useAppSelector((state) => state.auth?.user?.preferences);
  const currentPopulation = useAppSelector((state) => state.farm?.currentPopulation);
  const currentFarm = useAppSelector((state) => state.farm?.currentFarm);
  const currentPond = useAppSelector((state) => state.farm?.currentPond);

  const { otherConfig, timezone: farmTimezone, _id: farmId, metadata } = currentFarm ?? {};

  const isVisionOnly = metadata?.productOffering === 'visionOnly';
  const monitoringDays = otherConfig?.monitoringDays ?? [];
  const { day: feedingDay } = otherConfig?.startOfWeek ?? {};

  const {
    history,
    stockedAtTimezone,
    stockedAt,
    harvestPlan,
    stockingCostsMillar,
    _id: populationId,
    manualAverageWeights
  } = currentPopulation ?? {};
  const { indicators } = harvestPlan ?? {};
  const { isEmpty, isHarvested, isNewPond } = getPondState({ population: currentPopulation });

  const isPastCycle = currentPond?.currentPopulationId !== populationId;

  const isAdmin = usePermission({
    role: 'admin',
    entity: 'farm',
    entityId: farmId
  });

  const isSupervisor = usePermission({
    role: 'supervisor',
    entity: 'farm',
    entityId: farmId
  });

  const { viewFields, unitsConfig } = preferences ?? {};

  const pondProductionDataVariables = getHistoryVariables({
    historyViewVariables: viewFields?.pondProductionData,
    isAdmin,
    isSupervisor,
    isVisionOnly
  });

  const historyWeeklyData = getHistoryWeeklyData(history, monitoringDays, feedingDay);

  const historyLabelHash = getHistoryVariablesLabelMap({ unitsConfig });

  const tdStyles: BoxProps = {
    textAlign: 'center',
    borderBottomWidth: '0.5px',
    borderColor: 'border.gray',
    px: 'md',
    py: 'xs',
    textStyle: 'label.200'
  };

  const thStyles: BoxProps = {
    background: 'white',
    border: 'none',
    borderRight: '6px solid white',
    textAlign: 'center',
    p: 0,
    position: 'sticky',
    top: 0,
    zIndex: 4,
    color: 'text.gray'
  };

  const thTextStyles: TextProps = {
    color: 'text.gray',
    size: 'label200',
    borderRadius: 'xl',
    backgroundColor: 'bg.gray.medium',
    px: 'md',
    py: 'xs',
    h: '40px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center'
  };

  return (
    <CardContainer h='max-content' pt={{ base: 'lg', lg: 'md' }} {...rest}>
      <Flex align='center' justify='space-between' mb='md-alt' pe={{ base: 'xl', lg: 0 }}>
        <Text textStyle={{ base: 'headline.300.heavy', lg: 'label.100' }}>{trans('t_weekly_production_data')}</Text>
        <MoreOptionsMenu
          historyLabelHash={historyLabelHash}
          historyWeeklyData={historyWeeklyData}
          pondProductionDataVariables={pondProductionDataVariables}
        />
      </Flex>
      <TableContainer maxH={tableMaxHeight ?? '300px'} overflowY='auto'>
        <Table.Root size='lg' borderCollapse='collapse'>
          <Table.Header>
            <Table.Row>
              <Table.ColumnHeader {...thStyles}>
                <Text {...thTextStyles}>{trans('t_cycle_week')}</Text>
              </Table.ColumnHeader>
              <Table.ColumnHeader {...thStyles}>
                <Text {...thTextStyles}>{trans('t_days_of_culture')}</Text>
              </Table.ColumnHeader>
              {pondProductionDataVariables.map((variable) => {
                return (
                  <Table.ColumnHeader key={variable} {...thStyles}>
                    <Text {...thTextStyles}>{historyLabelHash[variable]}</Text>
                  </Table.ColumnHeader>
                );
              })}
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {!historyWeeklyData.length && (
              <Table.Row>
                <Table.Cell textAlign='center' colSpan={pondProductionDataVariables.length + 2}>
                  {trans('t_no_history_registered_for_any_feed_days')}
                </Table.Cell>
              </Table.Row>
            )}
            {historyWeeklyData.map((history, index) => {
              const { date } = history;
              const weekNumber = index + 1;
              const populationIsEmpty = isEmpty || isHarvested || isNewPond;
              const historyDateLuxon = DateTime.fromISO(date, { zone: farmTimezone });
              const stockedAtLuxon = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone ?? farmTimezone });
              const cycleDays = getDaysDiffBetweenDates({
                baseDate: historyDateLuxon.toFormat('yyyy-MM-dd'),
                dateToCompare: stockedAtLuxon.toFormat('yyyy-MM-dd')
              });

              const manualWeight = manualAverageWeights?.find((item) => item.date === date);

              const isLastItem = index === historyWeeklyData.length - 1;

              const tdStylesMerged = {
                ...tdStyles,
                ...(isLastItem && { borderBottom: 'none !important', paddingBottom: 0 })
              };

              return (
                <Table.Row key={date}>
                  <Table.Cell {...tdStylesMerged}>{weekNumber}</Table.Cell>
                  <Table.Cell {...tdStylesMerged}>
                    {isPastCycle ? cycleDays : populationIsEmpty ? '-' : cycleDays}
                  </Table.Cell>
                  {pondProductionDataVariables.map((variable) => {
                    return (
                      <WeeklyProductionDataCellMapper
                        key={variable}
                        variable={variable}
                        history={history}
                        style={tdStylesMerged}
                        indicators={indicators}
                        stockingCostsMillar={stockingCostsMillar}
                        farmId={farmId}
                        manualAverageWeight={manualWeight}
                      />
                    );
                  })}
                </Table.Row>
              );
            })}
          </Table.Body>
        </Table.Root>
      </TableContainer>
    </CardContainer>
  );
}

type MoreOptionsProps = Omit<ExportWeeklyProductionDataModalProps, 'children'>;

function MoreOptionsMenu(props: MoreOptionsProps) {
  const { pondProductionDataVariables, historyWeeklyData } = props;

  const { trans } = getTrans();
  const { trackAction } = useAnalytics();

  const exportButtonRef = useRef<HTMLDivElement>(null);
  const customizeViewButtonRef = useRef<HTMLDivElement>(null);

  return (
    <Box pos='relative' zIndex={999}>
      <ExportWeeklyProductionDataModal {...props}>
        <Box ref={exportButtonRef} display='none'>
          {trans('t_export')}
        </Box>
      </ExportWeeklyProductionDataModal>
      <HistoryChangeVariablesModal selectedVariables={pondProductionDataVariables} isProductionDataTable>
        <Box ref={customizeViewButtonRef} display='none'>
          {trans('t_customize_view')}
        </Box>
      </HistoryChangeVariablesModal>
      <MenuRoot
        positioning={{
          offset: {
            mainAxis: 0,
            crossAxis: 6
          }
        }}
      >
        <MenuButton
          h='20px'
          selectVariant='secondary'
          size='sm'
          px={0}
          _hover={{ opacity: 0.8 }}
          analyticsId={actionsName.customizeViewClicked}
        >
          <MoreHoriz w='20px' h='20px' />
        </MenuButton>

        <MenuContent portalled={false}>
          <MenuItem
            onClick={() => {
              trackAction(actionsName.changeVariablesClicked).then();
              customizeViewButtonRef.current?.click();
            }}
            value='customize-view'
            data-cy='customize-view-btn'
          >
            {trans('t_customize_view')}
          </MenuItem>
          <MenuItem
            value='export'
            onClick={() => exportButtonRef.current?.click()}
            disabled={!historyWeeklyData?.length}
          >
            {trans('t_export')}
          </MenuItem>
        </MenuContent>
      </MenuRoot>
    </Box>
  );
}

export type HistoryVariableMap = Record<HistoryVariable, ReactNode>;

interface WeeklyProductionDataCellMapperProps {
  variable: HistoryVariable;
  history: PopulationHistory;
  style: TableCellProps;
  indicators: PopulationHarvestPlanIndicators;
  farmId: string;
  stockingCostsMillar: number;
  manualAverageWeight: PopulationManualAverageWeights;
}

function WeeklyProductionDataCellMapper(props: WeeklyProductionDataCellMapperProps) {
  const { variable, history, style, indicators, stockingCostsMillar, farmId, manualAverageWeight } = props;

  const lang = useAppSelector((state) => state.app?.lang);
  const user = useAppSelector((state) => state.auth.user);

  const { trans } = getTrans();

  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};

  const {
    modelAverageWeight,
    averageWeight,
    growthLinear,
    growthDaily,
    weeklyGrowth,
    growthTwoWeeks,
    growthThreeWeeks,
    growth4w,
    cumulativeFcr,
    weeklyFcr,
    adjustedFcr,
    feedKgByHa,
    weeklyFeedGiven,
    totalFeedGivenKg,
    totalFeedGivenLbs,
    feedAsBiomassPercent,
    survival,
    survivalWithPartialHarvest,
    biomassLbs,
    totalBiomassLbs,
    totalBiomassLbsByHa,
    biomassLbsByHa,
    animalsRemainingM2,
    animalsRemainingHa,
    weightCv,
    profitPerHaPerDay,
    totalProfit,
    rofi,
    totalRevenue,
    totalCosts,
    stockingCosts,
    feedCostPerKg,
    cumulativeOverheadCosts,
    cumulativeFeedCosts,
    costPerPound,
    costPoundHarvest,
    survivalFeed,
    totalRevenuePound,
    revenuePerPound
  } = history ?? {};

  const {
    profitPerHaPerDayTextColor,
    costPerPoundTextColor,
    profitPerHaPerDayAdminValue,
    rofiAdminValue,
    costPerPoundAdminValue,
    costPoundHarvestAdminValue,
    stockingCostsMillarAdminValue,
    totalProfitAdminValue,
    totalCostsAdminValue,
    totalRevenueAdminValue,
    revenuePerPoundAdminValue,
    totalRevenuePerPoundAdminValue,
    stockingCostsAdminValue,
    feedCostPerKgAdminValue,
    cumulativeOverheadCostsAdminValue,
    cumulativeFeedCostsAdminValue
  } = useGetAdminFinancials({
    profitPerHaPerDay,
    costPerPound,
    costPoundHarvest,
    stockingCostsMillar,
    totalRevenue,
    revenuePerPound,
    totalRevenuePound,
    totalProfit,
    rofi,
    totalCosts,
    stockingCosts,
    feedCostPerKg,
    cumulativeOverheadCosts,
    cumulativeFeedCosts,
    indicators,
    farmId
  });

  const valueMap: HistoryVariableMap = {
    averageWeight: (
      <Flex justify='center' align='center' gap='2xs' w='100%'>
        {!!manualAverageWeight?.averageWeight && (
          <AverageWeightChangePopover
            modelAverageWeight={modelAverageWeight}
            updatedBy={manualAverageWeight?.updatedBy}
          />
        )}
        <Text>{formatNumber(averageWeight, { fractionDigits: 2, lang })}</Text>
      </Flex>
    ),
    growthLinear: formatNumber(growthLinear, { fractionDigits: 2, lang }),
    growthDaily: formatNumber(growthDaily, { fractionDigits: 2, lang }),
    growth1WeekAvg: formatNumber(weeklyGrowth, { fractionDigits: 2, lang }),
    growth2WeekAvg: formatNumber(growthTwoWeeks, { fractionDigits: 2, lang }),
    growthThreeWeeks: formatNumber(growthThreeWeeks, { fractionDigits: 2, lang }),
    growth4w: formatNumber(growth4w, { fractionDigits: 2, lang }),
    fcrCumulative: formatNumber(cumulativeFcr, { fractionDigits: 2, lang }),
    fcrWeekly: formatNumber(weeklyFcr, { fractionDigits: 2, lang }),
    adjustedFcr: formatNumber(adjustedFcr, { fractionDigits: 2, lang }),
    kgPerHaPerDayGivenKg: formatNumber(feedKgByHa, { fractionDigits: 1, lang }),
    weeklyFeedGiven: formatNumber(weeklyFeedGiven, { fractionDigits: 0, lang }),
    totalFeedGivenKg: formatNumber(totalFeedGivenKg, { fractionDigits: 0, lang }),
    totalFeedGivenLbs: formatNumber(totalFeedGivenLbs, { fractionDigits: 0, lang }),
    biomassPercentage: formatNumber(feedAsBiomassPercent, { fractionDigits: 2, lang, isPercentage: true }),
    survival: formatNumber(survival, { fractionDigits: 0, lang, isPercentage: true }),
    survivalWithPartialHarvest: formatNumber(survivalWithPartialHarvest, {
      fractionDigits: 0,
      lang,
      isPercentage: true
    }),
    biomassLb: formatNumber(convertUnitByDivision(biomassLbs, unitsConfig?.biomass), { fractionDigits: 0, lang }),
    biomassLbTotal: formatNumber(convertUnitByDivision(totalBiomassLbs, unitsConfig?.biomass), {
      fractionDigits: 0,
      lang
    }),
    biomassLbHa: formatNumber(convertUnitByDivision(biomassLbsByHa, unitsConfig?.biomass), { fractionDigits: 0, lang }),
    totalBiomassLbHa: formatNumber(convertUnitByDivision(totalBiomassLbsByHa, unitsConfig?.biomass), {
      fractionDigits: 0,
      lang
    }),
    animalsRemainingM2: formatNumber(animalsRemainingM2, { fractionDigits: 2, lang }),
    animalsRemainingHa: formatNumber(animalsRemainingHa, { fractionDigits: 0, lang }),
    dispersion: !!manualAverageWeight?.averageWeight ? (
      <Flex justify='center' align='center' gap='2xs' w='100%'>
        <InfoIconToolTip iconProps={{ secondSectionColor: 'white', firstSectionColor: 'gray.700' }}>
          {trans('t_no_cv_for_manual_monitoring')}
        </InfoIconToolTip>
        <Text>-</Text>
      </Flex>
    ) : (
      formatNumber(weightCv, { fractionDigits: 1, lang, isPercentage: true })
    ),
    profitPerHaPerDay: profitPerHaPerDayAdminValue,
    totalProfit: totalProfitAdminValue,
    rofi: rofiAdminValue,
    totalRevenue: totalRevenueAdminValue,
    revenuePerPound: revenuePerPoundAdminValue,
    totalRevenuePound: totalRevenuePerPoundAdminValue,
    totalCosts: totalCostsAdminValue,
    stockingCosts: stockingCostsAdminValue,
    stockingCostsMillar: stockingCostsMillarAdminValue,
    feedCostPerKg: feedCostPerKgAdminValue,
    cumulativeOverheadCosts: cumulativeOverheadCostsAdminValue,
    cumulativeFeedCosts: cumulativeFeedCostsAdminValue,
    costPerPound: costPerPoundAdminValue,
    costPoundHarvest: costPoundHarvestAdminValue,
    survivalFeed: formatNumber(survivalFeed, { fractionDigits: 2, lang, isPercentage: true })
  };

  const colorsMap: Partial<Record<HistoryVariable, string>> = {
    profitPerHaPerDay: profitPerHaPerDayTextColor,
    costPerPound: costPerPoundTextColor,
    costPoundHarvest: costPerPoundTextColor
  };

  return (
    <Table.Cell key={variable} color={colorsMap[variable]} {...style}>
      {valueMap[variable] ?? '-'}
    </Table.Cell>
  );
}
