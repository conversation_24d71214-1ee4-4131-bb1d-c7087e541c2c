import { DateTime } from 'luxon';
import { useAppSelector } from '@redux/hooks';
import { getTrans } from '@i18n/get-trans';
import { formatNumber } from '@utils/number';
import { Box, BoxProps, Flex, IconButton, Text, useToken } from '@chakra-ui/react';
import { Fragment, ReactNode, useEffect, useMemo, useRef, useState } from 'react';
import { ProductionChartVariableSelect } from '@screens/pond/components/pond-overview/production-chart-variable-select';
import {
  ProductionChartVariableOption,
  useProductionChartVariables
} from '@screens/pond/components/pond-overview/hooks/use-production-chart-variables';
import {
  addCostsMillarAndProfitToProductionData,
  addStockingCostsMillarToProductionData,
  CapacityToolTipCustomPoint,
  CarryingCapacitySeries,
  ChartData,
  ChartDataCustom,
  ChartVarsList,
  DailyParamPointCustom,
  getCapacityTooltip,
  getCarryingCapacitySeriesAndZones,
  getChartDataBasedOnVariableKey,
  getChartMinMaxValuesBasedOnComparison,
  getDailyParamToolTip,
  getHarvestTooltip,
  getProductionPlotLinesData,
  getSelectedVariablesMinAndMaxY,
  getSelectedVariableYAxisIndex,
  HarvestSeriesType,
  PopulationHistoryItem,
  ProductionDataWithCostMillar
} from '@screens/pond/components/pond-overview/helpers/pond-production-chart-helpers';
import { renderToString } from 'react-dom/server';
import { CardContainer } from '@components/card-container/card-container';
import { useAnalytics } from '@hooks/use-analytics';
import { actionsName } from '@utils/segment';
import {
  FarmDailyParameters,
  PopulationHarvestPlanProjectionProfitProjectionDataHeadon
} from '@xpertsea/module-farm-sdk';
import { usePermission } from '@hooks/use-permission';
import {
  CustomizationProductionFormValues,
  CustomizeProductionChartModal
} from '@screens/pond/components/pond-overview/customize-production-chart-modal';
import { useGetProductionChartDailyParams } from '@screens/pond/components/pond-overview/hooks/use-get-production-chart-daily-params';
import { InfoIconToolTip } from '@components/tooltip/info-icon-tool-tip';
import { ProductionChartDailyParamSelect } from '@screens/pond/components/pond-overview/production-chart-daily-param-select';
import { PondPerformanceRequiredAlert } from '@screens/pond/components/pond-overview/pond-performance-required-alert';
import { getDaysDiffBetweenDates } from '@screens/farm/helpers/farm-dates';
import isUndefined from 'lodash/isUndefined';
import { AddEditPondProjectionModal } from '@screens/pond/components/modals/add-edit-pond-projection-modal';
import { BaseButton } from '@components/base/base-button';
import { HighchartsNextComp, HighchartsRef } from '@components/base/highcharts-next-comp';
import { SettingsAdjustFilled } from '@icons/settings-adjust/settings-adjust-filled';
import { Switch } from '@components/ui/switch';
import { useUrlDailyParams } from './hooks/use-url-daily-params';
import { getCacheItem, setCacheItem, sevenDaysCacheTime } from '@utils/local-cache';
import { DailyParams, useGetDailyDataTrans } from '@screens/settings/hooks/use-get-daily-data-trans';
import { AdminOrSupervisorOrOperationWrapper } from '@components/permission-wrappers/admin-or-supervisor-or-operation-wrapper';
import { VisionOnlyGuard } from '@components/permission-wrappers/vision-only-guard';
import { VisionOnlyAccess } from '@components/permission-wrappers/vision-only-access';
import type { Options, SeriesZonesOptionsObject, XAxisPlotLinesOptions } from 'highcharts';
import { getMoonHtml } from '@screens/pond/components/pond-overview/helpers/highcharts';

const POND_PRODUCTION_FORM_VALUES_KEY = 'pondProductionFormValues';

export function PondProductionChart(props: BoxProps & { chartHeight?: number }) {
  const { chartHeight, ...rest } = props;
  const { trans } = getTrans();
  const { trackAction } = useAnalytics();
  const [hasComparison, setHasComparison] = useState(false);
  const [fontFamily] = useToken('fonts', ['body']);
  const [
    gray200,
    gray400,
    gray800,
    shrimpyPinky600,
    graphBrandBlue,
    brandGreen800,
    semanticYellow800,
    squidInkPowder700
  ] = useToken('colors', [
    'gray.200',
    'gray.400',
    'gray.800',
    'shrimpyPinky.600',
    'graphBrandBlue',
    'brandGreen.800',
    'semanticYellow.800',
    'squidInkPowder.700'
  ]);

  const lang = useAppSelector((state) => state.app.lang);
  const user = useAppSelector((state) => state.auth.user);
  const currentPopulation = useAppSelector((state) => state.farm?.currentPopulation);
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);
  const currentPond = useAppSelector((state) => state.farm.currentPond);
  const timezone = useAppSelector((state) => state.farm?.currentFarm?.timezone);

  const isVisionOnly = currentFarm?.metadata?.productOffering === 'visionOnly';
  const currentFarmId = currentFarm?._id;

  const isAdmin = usePermission({ role: 'admin', entity: 'farm', entityId: currentFarmId });
  const isSupervisor = usePermission({
    role: 'supervisor',
    entity: 'farm',
    entityId: currentFarmId
  });

  const { dailyParams: dailyParamFromUrl, productionVariables: productionVariablesFromUrl } = useUrlDailyParams() ?? {};

  const { chartVariableMap, chartVariableOne, chartVariableTwo, changeChartVariableOne, changeChartVariableTwo } =
    useProductionChartVariables({
      skipInitially: !!dailyParamFromUrl,
      defaultValue: productionVariablesFromUrl,
      isAdmin,
      isSupervisor,
      isVisionOnly
    });

  const {
    activeDailyParameters,
    dailyParamFirstSeries,
    dailyParamSecondSeries,
    chartDailyParamVariableOne,
    chartDailyParamVariableTwo,
    handleChangeChartDailyParamVariableOne,
    handleChangeChartDailyParamVariableTwo
  } = useGetProductionChartDailyParams({ defaultValue: dailyParamFromUrl });
  const highchartsRef = useRef<HighchartsRef>(null);

  const variableOneLabel = chartVariableMap[chartVariableOne]?.title;
  const variableTwoLabel = chartVariableMap[chartVariableTwo]?.title;
  const convertUnitVariableOne = chartVariableMap[chartVariableOne]?.convertUnit;
  const convertUnitVariableTwo = chartVariableMap[chartVariableTwo]?.convertUnit;
  const shouldConvertUnitVariableOne = chartVariableMap[chartVariableOne]?.shouldConvertUnit;
  const shouldConvertUnitVariableTwo = chartVariableMap[chartVariableTwo]?.shouldConvertUnit;

  const { size: pondSize } = currentPond;

  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);
  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');

  const [graphCustomization, setGraphCustomization] = useState<CustomizationProductionFormValues>({
    partialHarvest: !isVisionOnly,
    fullHarvest: !isVisionOnly,
    dailyData: activeDailyParameters?.length > 0
  });

  useEffect(() => {
    getCacheItem<CustomizationProductionFormValues>(POND_PRODUCTION_FORM_VALUES_KEY).then((res) => {
      if (res) {
        if (!isVisionOnly) {
          setGraphCustomization(res);
        } else {
          setGraphCustomization({
            ...res,
            partialHarvest: false,
            fullHarvest: false
          });
        }
      }
    });
  }, []);

  const onSetGraphCustomization = (v: CustomizationProductionFormValues) => {
    setCacheItem(POND_PRODUCTION_FORM_VALUES_KEY, v, sevenDaysCacheTime).then();
    setGraphCustomization(v);
  };

  const {
    history,
    productionPrediction = [],
    productionCycleComparison,
    harvest,
    harvestPlan,
    partialHarvest,
    partialHarvestPlanned,
    stockedAt,
    stockedAtTimezone,
    _id: populationId,
    stockingCostsMillar,
    cycleInformation,
    manualAverageWeights,
    estimatedSurvival,
    estimatedFeed
  } = currentPopulation ?? {};

  const { dailyWithUnitsTransMap } = useGetDailyDataTrans();

  const allChartVars: ChartVarsList = [
    { name: 'variableOne', label: variableOneLabel, value: chartVariableOne },
    { name: 'variableTwo', label: variableTwoLabel, value: chartVariableTwo },
    {
      name: 'dailyParamVariableOne',
      label:
        dailyWithUnitsTransMap[chartDailyParamVariableOne?.name as DailyParams] ?? chartDailyParamVariableOne?.name,
      value: chartDailyParamVariableOne?.name
    },
    {
      name: 'dailyParamVariableTwo',
      label:
        dailyWithUnitsTransMap[chartDailyParamVariableTwo?.name as DailyParams] ?? chartDailyParamVariableTwo?.name,
      value: chartDailyParamVariableTwo?.name
    }
  ];

  const chartSelectedVars: ChartVarsList = allChartVars.filter((variable) => variable.value);
  const actualHarvestDate = harvest?.date;
  const memoDependencies = [
    history,
    productionPrediction,
    productionCycleComparison,
    actualHarvestDate,
    harvestPlan,
    partialHarvest,
    partialHarvestPlanned,
    stockingCostsMillar,
    manualAverageWeights,
    cycleInformation?.carryingCapacity,
    chartVariableOne,
    chartVariableTwo,
    chartDailyParamVariableOne?._id,
    chartDailyParamVariableTwo?._id,
    lang,
    stockedAt,
    timezone,
    hasComparison,
    graphCustomization,
    isBiomassUnitLbs
  ];
  const { options, shouldHaveMargin } = useMemo(() => {
    if (!history?.length) return {};
    const { harvestType: oldHarvestType, processorPriceList, projection, harvestDate } = harvestPlan ?? {};
    const harvestType = processorPriceList?.defaultHarvestType ?? oldHarvestType;

    const { profitProjectionData, optimalHarvestIdx } = projection ?? {};

    const lastHistory = history?.[0];
    const lastHistoryDate = lastHistory?.date;
    const stockedAtLuxon = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone ?? timezone }).startOf('day');
    const lastHistoryLuxonDate = DateTime.fromISO(lastHistoryDate, { zone: timezone }).startOf('day');

    const profitMap = profitProjectionData?.reduce(
      (acc: Record<string, Omit<PopulationHarvestPlanProjectionProfitProjectionDataHeadon, '__typename'>>, item) => {
        acc[item.date] = item?.[harvestType as 'headon'];
        return acc;
      },
      {}
    );

    const reverseHistory: PopulationHistoryItem[] = [...(history ?? [])].reverse();
    const historyProductionData = addStockingCostsMillarToProductionData(reverseHistory, stockingCostsMillar);

    const optimalHarvest = profitProjectionData?.[optimalHarvestIdx];
    const harvestPlanDateTime = harvestDate ? DateTime.fromFormat(harvestDate, 'yyyy-MM-dd') : undefined;
    const optimalHarvestDateTIme = optimalHarvest?.date
      ? DateTime.fromFormat(optimalHarvest?.date, 'yyyy-MM-dd')
      : undefined;

    const baseDateTime =
      harvestPlanDateTime && optimalHarvestDateTIme
        ? DateTime.max(harvestPlanDateTime, optimalHarvestDateTIme)
        : undefined;
    const offsetAfterHarvest = 7;
    const daysUntilHarvest = baseDateTime
      ? Math.ceil(baseDateTime.diff(lastHistoryLuxonDate, 'days').days) + offsetAfterHarvest
      : 0;

    const projectionDays = graphCustomization.projectionLength;

    const selectedNumberOfDays = daysUntilHarvest && daysUntilHarvest >= 30 ? daysUntilHarvest : 30;
    const maxNumberOfDays = projectionDays || selectedNumberOfDays;

    const predictionDaysAfterLastHistory: PopulationHistoryItem[] = productionPrediction?.filter((prediction) => {
      const predictionLuxonDate = DateTime.fromISO(prediction.date, { zone: timezone }).startOf('day');
      if (actualHarvestDate) {
        const harvestLuxonDate = DateTime.fromISO(actualHarvestDate, { zone: timezone }).startOf('day');
        return predictionLuxonDate.diff(harvestLuxonDate, 'days').days <= 7;
      }
      return predictionLuxonDate.diff(lastHistoryLuxonDate, 'days').days <= maxNumberOfDays;
    });

    const predictionMergedWithProfit = addCostsMillarAndProfitToProductionData({
      productionData: predictionDaysAfterLastHistory,
      stockingCostsMillar,
      profitMap
    });

    const lastHistoryItem: ProductionDataWithCostMillar = { ...lastHistory, stockingCostsMillar };

    const lastPredictionDate = predictionMergedWithProfit?.[predictionMergedWithProfit.length - 1]?.date;
    const lastPredictionLuxonDate = DateTime.fromISO(lastPredictionDate, { zone: timezone }).startOf('day');

    const lastHistoryCycleDays = getDaysDiffBetweenDates({
      baseDate: lastHistoryLuxonDate.toFormat('yyyy-MM-dd'),
      dateToCompare: stockedAtLuxon.toFormat('yyyy-MM-dd')
    });

    const lastPredictionCycleDays = lastPredictionDate
      ? getDaysDiffBetweenDates({
          baseDate: lastPredictionLuxonDate.toFormat('yyyy-MM-dd'),
          dateToCompare: stockedAtLuxon.toFormat('yyyy-MM-dd')
        })
      : lastHistoryCycleDays;

    const projectedX = projectionDays ? lastHistoryCycleDays + projectionDays : lastPredictionCycleDays;

    const maxX = Math.max(projectedX, 20);

    let variableOneCycleComparisonMinY: number;
    let variableOneCycleComparisonMaxY: number;
    let variableTwoCycleComparisonMinY: number;
    let variableTwoCycleComparisonMaxY: number;
    let variableOnePredictionMinY: number;
    let variableOnePredictionMaxY: number;
    let variableTwoPredictionMinY: number;
    let variableTwoPredictionMaxY: number;

    let firstVariableChartCycleComparisonData: ChartData[] = [];
    let secondVariableChartCycleComparisonData: ChartData[] = [];
    let firstVariableChartPredictionData: ChartData[] = [];
    let secondVariableChartPredictionData: ChartData[] = [];

    let harvestPlotLines: XAxisPlotLinesOptions[] = [];
    let harvestSeries: HarvestSeriesType[] = [];

    let carryingCapacityZones: SeriesZonesOptionsObject[] = [];
    let carryingCapacitySeries: CarryingCapacitySeries[] = [];

    if (hasComparison) {
      const cycleComparisonAfterLastHistory = productionCycleComparison?.filter((cycle) => {
        const predictionLuxonDate = DateTime.fromISO(cycle.date, { zone: timezone }).startOf('day');
        return predictionLuxonDate.diff(lastHistoryLuxonDate, 'days').days <= selectedNumberOfDays;
      });

      const cycleComparisonAfterLastHistoryWithCostMillar = addStockingCostsMillarToProductionData(
        cycleComparisonAfterLastHistory,
        stockingCostsMillar
      );
      const {
        chartData: firstVariableChartData,
        minY: variableOneMinY,
        maxY: variableOneMaxY
      } = getChartDataBasedOnVariableKey({
        unitsConfig,
        manualAverageWeights,
        variableKey: chartVariableOne,
        convertUnit: convertUnitVariableOne,
        shouldConvertUnit: shouldConvertUnitVariableOne,
        productionData: cycleComparisonAfterLastHistoryWithCostMillar,
        timezone,
        stockedAtLuxon,
        lang
      });

      const {
        chartData: secondVariableChartData,
        minY: variableTwoMinY,
        maxY: variableTwoMaxY
      } = getChartDataBasedOnVariableKey({
        unitsConfig,
        manualAverageWeights,
        variableKey: chartVariableTwo,
        convertUnit: convertUnitVariableTwo,
        shouldConvertUnit: shouldConvertUnitVariableTwo,
        productionData: cycleComparisonAfterLastHistoryWithCostMillar,
        timezone,
        stockedAtLuxon,
        lang
      });

      firstVariableChartCycleComparisonData = firstVariableChartData;
      secondVariableChartCycleComparisonData = secondVariableChartData;
      variableOneCycleComparisonMinY = variableOneMinY;
      variableOneCycleComparisonMaxY = variableOneMaxY;
      variableTwoCycleComparisonMinY = variableTwoMinY;
      variableTwoCycleComparisonMaxY = variableTwoMaxY;
    }

    if (!isVisionOnly) {
      const productionPredictionWithLastHistoryItem = predictionMergedWithProfit?.length
        ? [lastHistoryItem].concat(predictionMergedWithProfit)
        : [];
      const {
        chartData: firstChartPredictionData,
        minY: firstPredictionMinY,
        maxY: firstPredictionMaxY
      } = getChartDataBasedOnVariableKey({
        unitsConfig,
        manualAverageWeights,
        variableKey: chartVariableOne,
        convertUnit: convertUnitVariableOne,
        shouldConvertUnit: shouldConvertUnitVariableOne,
        productionData: productionPredictionWithLastHistoryItem,
        timezone,
        stockedAtLuxon,
        lang,
        isPrediction: true
      });

      firstVariableChartPredictionData = firstChartPredictionData;
      variableOnePredictionMinY = firstPredictionMinY;
      variableOnePredictionMaxY = firstPredictionMaxY;

      const {
        chartData: secondChartPredictionData,
        minY: secondPredictionMinY,
        maxY: secondPredictionMaxY
      } = getChartDataBasedOnVariableKey({
        unitsConfig,
        manualAverageWeights,
        variableKey: chartVariableTwo,
        convertUnit: convertUnitVariableTwo,
        shouldConvertUnit: shouldConvertUnitVariableTwo,
        productionData: productionPredictionWithLastHistoryItem,
        timezone,
        stockedAtLuxon,
        lang,
        isPrediction: true
      });
      secondVariableChartPredictionData = secondChartPredictionData;
      variableTwoPredictionMinY = secondPredictionMinY;
      variableTwoPredictionMaxY = secondPredictionMaxY;

      const { plotLines, series } = getProductionPlotLinesData({
        maxX,
        pondSize,
        timezone,
        harvestInfo: { harvestDate: actualHarvestDate, harvestPlanDate: harvestDate },
        stockedAtLuxon,
        partialHarvest,
        partialHarvestPlanned,
        productionPredictions: productionPredictionWithLastHistoryItem,
        graphCustomization,
        estimatedSurvival,
        harvest
      });

      harvestPlotLines = plotLines;
      harvestSeries = series;

      const { carryingCapacityZones: capacityZones, carryingCapacitySeries: capacitySeries } =
        getCarryingCapacitySeriesAndZones({
          productionData: predictionDaysAfterLastHistory
            ? reverseHistory.concat(predictionDaysAfterLastHistory)
            : reverseHistory,
          carryingCapacity: cycleInformation?.carryingCapacity,
          stockedAtLuxon,
          timezone
        });

      carryingCapacityZones = capacityZones;
      carryingCapacitySeries = capacitySeries;
    }

    const {
      chartData: firstVariableChartData,
      minY: variableOneDataMinY,
      maxY: variableOneDataMaxY
    } = getChartDataBasedOnVariableKey({
      unitsConfig,
      manualAverageWeights,
      variableKey: chartVariableOne,
      convertUnit: convertUnitVariableOne,
      shouldConvertUnit: shouldConvertUnitVariableOne,
      productionData: historyProductionData,
      timezone,
      stockedAtLuxon,
      lang
    });

    const {
      chartData: secondVariableChartData,
      minY: variableTwoDataMinY,
      maxY: variableTwoDataMaxY
    } = getChartDataBasedOnVariableKey({
      unitsConfig,
      manualAverageWeights,
      variableKey: chartVariableTwo,
      convertUnit: convertUnitVariableTwo,
      shouldConvertUnit: shouldConvertUnitVariableTwo,
      productionData: historyProductionData,
      timezone,
      stockedAtLuxon,
      lang
    });

    const { variableOneMinY, variableTwoMinY, variableOneMaxY, variableTwoMaxY } =
      getChartMinMaxValuesBasedOnComparison({
        variableOneDataMinY,
        variableOneDataMaxY,
        variableOnePredictionMinY,
        variableOnePredictionMaxY,
        variableOneCycleComparisonMinY,
        variableOneCycleComparisonMaxY,
        variableTwoDataMinY,
        variableTwoPredictionMinY,
        variableTwoCycleComparisonMinY,
        variableTwoDataMaxY,
        variableTwoPredictionMaxY,
        variableTwoCycleComparisonMaxY,
        hasComparison,
        chartVariableOne,
        chartVariableTwo
      });

    const selectedVariablesWithMinAndMax = getSelectedVariablesMinAndMaxY({
      chartSelectedVars,
      dailyParamFirstSeriesMin: dailyParamFirstSeries.minValue,
      dailyParamFirstSeriesMax: dailyParamFirstSeries.maxValue,
      dailyParamSecondSeriesMin: dailyParamSecondSeries.minValue,
      dailyParamSecondSeriesMax: dailyParamSecondSeries.maxValue,
      variableOneMinY,
      variableOneMaxY,
      variableTwoMinY,
      variableTwoMaxY
    });

    const eightySevenPercentOfMaxX = formatNumber(maxX * 0.87, { fractionDigits: 0, lang, asNumber: true }) as number;
    const shouldHaveMargin = harvestPlotLines.some(
      (line) => (line.value as number) >= eightySevenPercentOfMaxX && (line.value as number) <= maxX
    );

    const customStyleText = { fontSize: '10px', fontWeight: '600', color: gray800, lineHeight: '12px' };

    const options: Options = {
      title: { text: undefined },
      time: { timezone },
      plotOptions: {
        series: { states: { inactive: { enabled: false } } },
        line: {
          lineWidth: 1.5,
          animation: false,
          showInLegend: false,
          marker: { enabled: true, symbol: 'circle', radius: 5 }
        }
      },
      chart: { spacing: [18, 26, 0, 0], animation: false, style: { fontFamily } },
      xAxis: {
        min: 0,
        max: maxX,
        tickInterval: 7,
        tickWidth: 0,
        lineWidth: 0,
        plotLines: harvestPlotLines,
        title: { text: trans('t_days_of_culture'), margin: 16, style: customStyleText },
        labels: {
          distance: 10,
          rotation: 0,
          useHTML: true,
          style: { textOverflow: 'visible' },
          formatter(this): string {
            const cycleDays = this.value as number;
            const valueLuxon = stockedAtLuxon.plus({ days: cycleDays }).setZone(timezone);

            const currentDate = DateTime.local({ zone: timezone }).set({
              hour: 0,
              minute: 0,
              second: 0,
              millisecond: 0
            });

            const isPastDate = valueLuxon < currentDate;
            const dateColor = isPastDate ? gray400 : gray800;

            return `
              <div style="display: flex; flex-direction: column; align-items: center; gap: 4px;">
                <p style="font-size: 10px; font-weight: 600; color: ${dateColor}; line-height: 12px;">
                  ${cycleDays}
                </p>
                ${getMoonHtml(valueLuxon.toJSDate())}
              </div>
            `;
          }
        }
      },
      yAxis: [
        {
          min: selectedVariablesWithMinAndMax[0]?.minY,
          max: selectedVariablesWithMinAndMax[0]?.maxY,
          zIndex: 1,
          title: {
            text: ''
          },
          gridLineWidth: 0.5,
          gridLineColor: gray400,
          gridLineDashStyle: 'LongDash',
          labels: {
            useHTML: true,
            style: customStyleText,
            formatter: function (this): string {
              const value = this.value as number;
              const variable = selectedVariablesWithMinAndMax[0]?.value as ProductionChartVariableOption;
              const variableOption = chartVariableMap[variable];
              const { isCurrency, isPercentage, fractionDigits } = variableOption ?? {};
              return formatNumber(value, {
                lang,
                fractionDigits: fractionDigits ?? 2,
                compact: !!variableOption,
                isPercentage,
                isCurrency
              }) as string;
            }
          }
        },
        {
          min: selectedVariablesWithMinAndMax[1]?.minY,
          max: selectedVariablesWithMinAndMax[1]?.maxY,
          zIndex: 1,
          gridZIndex: 1,
          opposite: true,
          title: {
            text: ''
          },
          gridLineWidth: 0.5,
          gridLineColor: gray400,
          gridLineDashStyle: 'LongDash',
          labels: {
            useHTML: true,
            style: customStyleText,
            formatter: function (this): string {
              const value = this.value as number;
              const variable = selectedVariablesWithMinAndMax[1]?.value as ProductionChartVariableOption;

              const variableOption = chartVariableMap[variable];
              const { isCurrency, isPercentage, fractionDigits } = variableOption ?? {};
              return formatNumber(value, {
                lang,
                fractionDigits: fractionDigits ?? 2,
                compact: !!variableOption,
                isPercentage,
                isCurrency
              }) as string;
            }
          }
        }
      ],
      series: [
        {
          type: 'line',
          name: 'variableOne',
          yAxis: getSelectedVariableYAxisIndex('variableOne', chartSelectedVars),
          data: firstVariableChartData,
          zoneAxis: 'x',
          zIndex: 2,
          color: shrimpyPinky600
        },
        {
          type: 'line',
          name: 'variableTwo',
          yAxis: getSelectedVariableYAxisIndex('variableTwo', chartSelectedVars),
          data: secondVariableChartData,
          zoneAxis: 'x',
          zIndex: 2,
          color: graphBrandBlue
        },
        {
          type: 'line',
          dashStyle: 'Dash',
          lineWidth: 1.5,
          name: 'variableOnePrediction',
          yAxis: getSelectedVariableYAxisIndex('variableOne', chartSelectedVars),
          data: firstVariableChartPredictionData,
          zoneAxis: 'x',
          zIndex: 2,
          color: shrimpyPinky600,
          marker: { enabled: false }
        },
        {
          type: 'line',
          dashStyle: 'Dash',
          lineWidth: 1.5,
          name: 'variableTwoPrediction',
          yAxis: getSelectedVariableYAxisIndex('variableTwo', chartSelectedVars),
          data: secondVariableChartPredictionData,
          zoneAxis: 'x',
          zIndex: 2,
          color: graphBrandBlue,
          marker: { enabled: false }
        },
        {
          type: 'line',
          dashStyle: 'Dot',
          name: 'variableOneCycleComparison',
          lineWidth: 1.5,
          yAxis: getSelectedVariableYAxisIndex('variableOne', chartSelectedVars),
          data: firstVariableChartCycleComparisonData,
          zoneAxis: 'x',
          zIndex: 2,
          color: shrimpyPinky600,
          marker: { enabled: false }
        },
        {
          type: 'line',
          dashStyle: 'Dot',
          name: 'variableTwoCycleComparison',
          lineWidth: 1.5,
          yAxis: getSelectedVariableYAxisIndex('variableTwo', chartSelectedVars),
          data: secondVariableChartCycleComparisonData,
          zoneAxis: 'x',
          zIndex: 2,
          color: graphBrandBlue,
          marker: { enabled: false }
        },
        {
          type: 'line',
          name: 'dailyParamVariableOne',
          yAxis: getSelectedVariableYAxisIndex('dailyParamVariableOne', chartSelectedVars),
          data: dailyParamFirstSeries.series,
          zoneAxis: 'x',
          zIndex: 2,
          color: brandGreen800
        },
        {
          type: 'line',
          name: 'dailyParamVariableTwo',
          yAxis: getSelectedVariableYAxisIndex('dailyParamVariableTwo', chartSelectedVars),
          zIndex: 2,
          data: dailyParamSecondSeries.series,
          zoneAxis: 'x',
          color: semanticYellow800
        },
        {
          type: 'line',
          dashStyle: 'Dot',
          id: 'harvestPlotLine',
          name: 'harvestPlotLine',
          yAxis: 0,
          marker: { radius: 9 },
          lineWidth: 0,
          states: { hover: { enabled: false } },
          data: harvestSeries
        },
        {
          name: 'carryingCapacity',
          type: 'line',
          data: carryingCapacitySeries,
          zoneAxis: 'x',
          className: 'carrying-capacity',
          zIndex: 1,
          yAxis: 0,
          zones: carryingCapacityZones,
          marker: { enabled: false, states: { hover: { enabled: false } } },
          lineWidth: 13,
          linecap: 'square'
        }
      ],
      tooltip: {
        crosshairs: { color: squidInkPowder700, dashStyle: 'Dash' },
        zIndex: 1000,
        padding: 0,
        useHTML: true,
        shadow: false,
        borderRadius: 8,
        backgroundColor: gray200,
        formatter: function (this) {
          type Point = {
            custom: ChartDataCustom &
              CapacityToolTipCustomPoint &
              DailyParamPointCustom & { isHarvest?: boolean; harvestType?: 'partial' | 'full' };
            x: number;
            y: number;
          };

          const currentPoint = this as unknown as Point;

          if (currentPoint?.custom?.isDailyParam) {
            return getDailyParamToolTip(currentPoint, { lang, dailyWithUnitsTransMap });
          }

          if (currentPoint?.custom?.isHarvest) {
            return getHarvestTooltip(currentPoint, { lang, isBiomassUnitLbs, unitsConfig, isAdmin, isSupervisor });
          }

          if (currentPoint?.custom?.isCarryingCapacity) {
            return getCapacityTooltip(currentPoint, { lang, isBiomassUnitLbs, unitsConfig });
          }

          const cycleDays = currentPoint.x;
          let historyCommonDataHtml: ReactNode = <></>;
          let predictionCommonDataHtml: ReactNode = <></>;
          let comparisonCommonDataHtml: ReactNode = <></>;
          const mainVariablesHtmlPoints: ReactNode[] = [];
          const comparisonHtmlPoints: ReactNode[] = [];

          const existingKeys: ProductionChartVariableOption[] = [];
          const chartSeries = this.series.chart.series;

          chartSeries.forEach((item) => {
            const point = item.points?.find((item) => item.x === cycleDays) as unknown as Point;
            const { y: value, custom } = point ?? {};
            const { variableKey, dateLuxon, additionalText: commonValues } = custom ?? {};
            const dateIso = dateLuxon?.toFormat('yyyy-MM-dd');
            const isPredictionFirstNode = item.name?.includes('Prediction') && dateIso === lastHistoryDate;
            if (!point || isPredictionFirstNode || !chartVariableMap[variableKey]) return;

            const { averageWeight, totalRevenue, biomassTotal, averageDailyFeed, isManualAverageWeight } =
              commonValues ?? {};
            const isManualWeightAndNotComparison = isManualAverageWeight && !item.name.includes('CycleComparison');
            //record existing keys to avoid duplicate data
            existingKeys.push(variableKey);

            const canShowAverageWeight = !existingKeys.includes('averageWeight');
            const canShowTotalRevenueKey = !existingKeys.includes('totalRevenue');
            const canShowBiomassTotalKey = !existingKeys.includes('totalBiomassLbs');

            const isSurvivalNode = variableKey === 'survival' || variableKey === 'survivalWithPartialHarvest';
            const isEstimatedSurvivalNode = isSurvivalNode && !!estimatedSurvival?.[dateIso];
            const isWeeklyFeedNode = variableKey === 'weeklyFeedGiven';
            const isEstimatedFeedNode = isWeeklyFeedNode && !!estimatedFeed?.[dateIso];
            const hasEstimatedValue = isEstimatedSurvivalNode || isEstimatedFeedNode;

            const { title, fractionDigits, isCurrency, isPercentage, suffix } = chartVariableMap[variableKey] ?? {};

            const valueFormatted = formatNumber(value, { lang, fractionDigits, isCurrency, isPercentage });
            const estimatedIndicatorText = hasEstimatedValue ? ' *' : '';
            const suffixValue = suffix ? `${suffix} ${estimatedIndicatorText}` : estimatedIndicatorText;

            const htmlData = (
              <div style={{ display: 'flex', alignItems: 'center', gap: '4px', fontWeight: 700, marginBottom: '2px' }}>
                {isManualWeightAndNotComparison && variableKey === 'averageWeight' && (
                  <svg width='24px' height='24px' fill='none'>
                    <path
                      d='M8 6.2C8 5.0799 8 4.51984 8.21799 4.09202C8.40973 3.71569 8.71569 3.40973 9.09202 3.21799C9.51984 3 10.0799 3 11.2 3H17.8C18.9201 3 19.4802 3 19.908 3.21799C20.2843 3.40973 20.5903 3.71569 20.782 4.09202C21 4.51984 21 5.0799 21 6.2V12.8C21 13.9201 21 14.4802 20.782 14.908C20.5903 15.2843 20.2843 15.5903 19.908 15.782C19.4802 16 18.9201 16 17.8 16H11.2C10.0799 16 9.51984 16 9.09202 15.782C8.71569 15.5903 8.40973 15.2843 8.21799 14.908C8 14.4802 8 13.9201 8 12.8V6.2Z'
                      fill={gray400}
                    />
                    <path
                      d='M3 11.2C3 10.0799 3 9.51984 3.21799 9.09202C3.40973 8.71569 3.71569 8.40973 4.09202 8.21799C4.51984 8 5.0799 8 6.2 8H12.8C13.9201 8 14.4802 8 14.908 8.21799C15.2843 8.40973 15.5903 8.71569 15.782 9.09202C16 9.51984 16 10.0799 16 11.2V17.8C16 18.9201 16 19.4802 15.782 19.908C15.5903 20.2843 15.2843 20.5903 14.908 20.782C14.4802 21 13.9201 21 12.8 21H6.2C5.0799 21 4.51984 21 4.09202 20.782C3.71569 20.5903 3.40973 20.2843 3.21799 19.908C3 19.4802 3 18.9201 3 17.8V11.2Z'
                      fill={gray800}
                    />
                    <path
                      fill='white'
                      d='M12.7803 12.2441C13.0732 12.5695 13.0732 13.0972 12.7803 13.4226L9.78033 16.7559C9.48744 17.0814 9.01256 17.0814 8.71967 16.7559L7.21967 15.0893C6.92678 14.7638 6.92678 14.2362 7.21967 13.9107C7.51256 13.5853 7.98744 13.5853 8.28033 13.9107L9.25 14.9882L11.7197 12.2441C12.0126 11.9186 12.4874 11.9186 12.7803 12.2441Z'
                    />
                  </svg>
                )}
                <p>{title}:</p>
                <p>
                  {valueFormatted} {suffixValue}
                </p>
              </div>
            );

            const commonVariableData = (
              <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                {canShowAverageWeight && (
                  <div style={{ display: 'flex', gap: '4px', alignItems: 'center' }}>
                    {isManualWeightAndNotComparison && (
                      <svg width='24px' height='24px' fill='none'>
                        <path
                          d='M8 6.2C8 5.0799 8 4.51984 8.21799 4.09202C8.40973 3.71569 8.71569 3.40973 9.09202 3.21799C9.51984 3 10.0799 3 11.2 3H17.8C18.9201 3 19.4802 3 19.908 3.21799C20.2843 3.40973 20.5903 3.71569 20.782 4.09202C21 4.51984 21 5.0799 21 6.2V12.8C21 13.9201 21 14.4802 20.782 14.908C20.5903 15.2843 20.2843 15.5903 19.908 15.782C19.4802 16 18.9201 16 17.8 16H11.2C10.0799 16 9.51984 16 9.09202 15.782C8.71569 15.5903 8.40973 15.2843 8.21799 14.908C8 14.4802 8 13.9201 8 12.8V6.2Z'
                          fill={gray400}
                        />
                        <path
                          d='M3 11.2C3 10.0799 3 9.51984 3.21799 9.09202C3.40973 8.71569 3.71569 8.40973 4.09202 8.21799C4.51984 8 5.0799 8 6.2 8H12.8C13.9201 8 14.4802 8 14.908 8.21799C15.2843 8.40973 15.5903 8.71569 15.782 9.09202C16 9.51984 16 10.0799 16 11.2V17.8C16 18.9201 16 19.4802 15.782 19.908C15.5903 20.2843 15.2843 20.5903 14.908 20.782C14.4802 21 13.9201 21 12.8 21H6.2C5.0799 21 4.51984 21 4.09202 20.782C3.71569 20.5903 3.40973 20.2843 3.21799 19.908C3 19.4802 3 18.9201 3 17.8V11.2Z'
                          fill={gray800}
                        />
                        <path
                          fill='white'
                          d='M12.7803 12.2441C13.0732 12.5695 13.0732 13.0972 12.7803 13.4226L9.78033 16.7559C9.48744 17.0814 9.01256 17.0814 8.71967 16.7559L7.21967 15.0893C6.92678 14.7638 6.92678 14.2362 7.21967 13.9107C7.51256 13.5853 7.98744 13.5853 8.28033 13.9107L9.25 14.9882L11.7197 12.2441C12.0126 11.9186 12.4874 11.9186 12.7803 12.2441Z'
                        />
                      </svg>
                    )}
                    <p>{trans('t_abw_g')}:</p>

                    <p>{averageWeight}</p>
                  </div>
                )}
                {!isVisionOnly && (
                  <>
                    {canShowTotalRevenueKey && isAdmin && (
                      <div style={{ display: 'flex', gap: '4px' }}>
                        <p>{trans('t_revenue')}:</p>
                        <p>{totalRevenue} </p>
                      </div>
                    )}

                    {canShowBiomassTotalKey && (
                      <div style={{ display: 'flex', gap: '4px' }}>
                        <p>{trans('t_biomass_include_harvests_unit', { unit: unitLabel })}:</p>
                        <p>{biomassTotal}</p>
                      </div>
                    )}
                    <div style={{ display: 'flex', gap: '4px' }}>
                      <p>{trans('t_average_daily_feed')}:</p>
                      <p>{averageDailyFeed}</p>
                    </div>
                  </>
                )}
              </div>
            );

            switch (item.name) {
              case 'variableOneCycleComparison':
              case 'variableTwoCycleComparison':
                comparisonCommonDataHtml = commonVariableData;
                comparisonHtmlPoints.push(htmlData);
                break;
              case 'variableOnePrediction':
              case 'variableTwoPrediction':
                predictionCommonDataHtml = commonVariableData;
                mainVariablesHtmlPoints.push(htmlData);
                break;
              default:
                historyCommonDataHtml = commonVariableData;
                mainVariablesHtmlPoints.push(htmlData);
            }
          });

          const { dateLuxon } = currentPoint?.custom ?? {};
          const dateString = dateLuxon.toFormat('MMM dd, yyyy', { locale: lang });
          const isComparison = this.series.name?.includes('Comparison');
          const hasComparisonItems = comparisonHtmlPoints.length > 0;
          const hasMainItems = mainVariablesHtmlPoints.length > 0;
          const hasBothComparisonAndMainItems = hasComparisonItems && hasMainItems;
          const mainNodes = hasMainItems ? mainVariablesHtmlPoints : comparisonHtmlPoints;

          const isInTheFuture = dateLuxon.diff(lastHistoryLuxonDate, 'days').days > 0;
          return renderToString(
            <div
              style={{
                padding: '10px 16px',
                fontSize: '12px',
                fontWeight: 600,
                color: gray800,
                backgroundColor: gray200,
                borderRadius: '8px',
                minWidth: '161px',
                lineHeight: '16px',
                display: 'flex',
                flexDirection: 'column',
                gap: '10px'
              }}
            >
              {isInTheFuture && <p style={{ fontSize: '14px' }}>{trans('t_projected')}</p>}

              {isComparison && !isInTheFuture && !hasMainItems && (
                <p style={{ fontSize: '14px' }}>{trans('t_target')}</p>
              )}

              <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                <p>{dateString}</p>
                <p>{trans('t_count_days_of_culture', { count: cycleDays })}</p>
              </div>

              <hr style={{ borderColor: gray400 }} />

              <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                  {mainNodes.map((item, index) => (
                    <Fragment key={index}>{item}</Fragment>
                  ))}
                </div>
                {isInTheFuture && predictionCommonDataHtml}
                {!isInTheFuture && historyCommonDataHtml}
              </div>

              {hasBothComparisonAndMainItems && (
                <>
                  <hr style={{ borderColor: gray400 }} />
                  <p style={{ fontSize: '14px' }}>{trans('t_target')}</p>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
                      {comparisonHtmlPoints.map((item, index) => (
                        <Fragment key={index}>{item}</Fragment>
                      ))}
                    </div>
                    {comparisonCommonDataHtml}
                  </div>
                </>
              )}
            </div>
          );
        }
      } as Highcharts.TooltipOptions
    };

    return { options, shouldHaveMargin };
  }, memoDependencies);

  useEffect(() => {
    if (!highchartsRef?.current?.chart) return;
    const chart = highchartsRef.current.chart;
    const extremes = chart.yAxis[0].getExtremes();

    chart.series.forEach((series) => {
      if (series.name === 'harvestPlotLine') {
        series.data.forEach((point) => {
          point.update({ y: extremes.max });
        });
      }
      if (series.name === 'carryingCapacity') {
        series.data.forEach((point) => {
          point.update({ y: extremes.min });
        });
      }
    });

    chart.redraw(false);
  }, [options, highchartsRef?.current]);

  useEffect(() => {
    if (!populationId) return;
    setHasComparison(false);
  }, [populationId]);

  useEffect(() => {
    if (graphCustomization?.dailyData) return;
    if (chartDailyParamVariableOne && chartDailyParamVariableTwo) {
      changeChartVariableOne('averageWeight');
    }
    handleChangeChartDailyParamVariableOne(undefined);
    handleChangeChartDailyParamVariableTwo(undefined);
  }, [graphCustomization?.dailyData]);

  const handleVariableChange = (
    variable: 'chartVariableOne' | 'chartVariableTwo',
    value: ProductionChartVariableOption
  ) => {
    if (variable === 'chartVariableOne') {
      changeChartVariableOne(value);
      trackAction(actionsName.pondOverviewProductionChartVariableOneChanged, { variable: value }).then();
    } else {
      changeChartVariableTwo(value);
      trackAction(actionsName.pondOverviewProductionChartVariableTwoChanged, { variable: value }).then();
    }
  };

  const handleDailyParamVariableChange = (variable: 'one' | 'two', value: FarmDailyParameters) => {
    if (variable === 'one') {
      handleChangeChartDailyParamVariableOne(value);
      trackAction(actionsName.pondOverviewProductionDailyParamVariableOneChanged).then();
    } else {
      handleChangeChartDailyParamVariableTwo(value);
      trackAction(actionsName.pondOverviewProductionDailyParamVariableTwoChanged).then();
    }
  };

  return (
    <CardContainer
      px={0}
      pb={0}
      pt={{ base: 'lg', lg: 'md' }}
      display='flex'
      flexDir='column'
      justifyContent='space-between'
      gap='xs'
      flex={1}
      {...rest}
    >
      <Flex justify='space-between' px='md' gap={{ base: 'xs', lg: 'md' }} flexWrap='wrap'>
        <Flex align='center' gap='xs-alt'>
          <Text textStyle={{ base: 'headline.300.heavy', lg: 'label.100' }}>{trans('t_production')}</Text>
          <InfoIconToolTip>{trans('t_production_chart_alert')}</InfoIconToolTip>
        </Flex>

        <Flex align='center' gap='md-alt' flexWrap='wrap' pe={{ base: 'xl', lg: '0' }}>
          <ProductionChartVariableSelect
            onVariableChange={handleVariableChange}
            chartVariableOne={chartVariableOne}
            chartVariableTwo={chartVariableTwo}
            chartVariableMap={chartVariableMap}
            chartSelectedVars={chartSelectedVars}
          />
          {graphCustomization?.dailyData && (
            <ProductionChartDailyParamSelect
              onVariableChange={handleDailyParamVariableChange}
              chartDailyParamVariableOne={chartDailyParamVariableOne}
              chartDailyParamVariableTwo={chartDailyParamVariableTwo}
              activeDailyParameters={activeDailyParameters}
              chartSelectedVars={chartSelectedVars}
            />
          )}
          <MoreOptionsMenu graphCustomization={graphCustomization} onSubmit={onSetGraphCustomization} />
        </Flex>
      </Flex>
      {!!history?.length && (
        <>
          <Flex
            align='center'
            gap='sm-alt'
            px='md'
            pos='relative'
            css={{
              '& g.highcharts-series path': { strokeLinecap: 'round', strokeLinejoin: 'round' },
              '& g.carrying-capacity path': { strokeLinecap: 'square', strokeLinejoin: 'square' }
            }}
          >
            <Text
              fontSize='xs'
              fontWeight={600}
              lineHeight='12px'
              color='text.gray.weak'
              display='flex'
              w='12px'
              h='12px'
              mt='-60px'
              whiteSpace='pre'
              alignItems='center'
              flexDirection='row'
              justifyContent='center'
              css={{ textWrap: 'nowrap', rotate: '-90deg' }}
            >
              {chartSelectedVars[0]?.label}
            </Text>

            <HighchartsNextComp
              ref={highchartsRef}
              options={options}
              containerProps={{ style: { width: '100%', height: chartHeight ? `${chartHeight}px` : undefined } }}
            />

            <Text
              fontSize='xs'
              fontWeight={600}
              lineHeight='12px'
              color='text.gray.weak'
              display='flex'
              w='12px'
              h='12px'
              mt='-60px'
              whiteSpace='pre'
              alignItems='center'
              flexDirection='row'
              justifyContent='center'
              insetEnd={shouldHaveMargin ? '16px' : null}
              pos={shouldHaveMargin ? 'absolute' : 'static'}
              css={{ textWrap: 'nowrap', rotate: '-90deg' }}
            >
              {chartSelectedVars[1]?.label}
            </Text>
          </Flex>
          <VisionOnlyGuard>
            <Flex
              gap='xs-alt'
              alignItems='center'
              borderBottomRadius='2xl'
              bgColor='bg.gray.strong'
              px='md'
              py='sm-alt'
              justify='space-between'
            >
              {!!productionCycleComparison?.length && (
                <Flex gap='xs-alt' alignItems='center'>
                  <Switch
                    size='sm'
                    checked={hasComparison}
                    onCheckedChange={(value) => {
                      setHasComparison(value.checked);
                    }}
                    id='comparison'
                    controlProps={{ bgColor: 'icon.gray.inverted.weak', _checked: { bgColor: 'text.gray' } }}
                  />
                  <Text size='label200'>{trans('t_comparison_to_target')}</Text>
                  <PondPerformanceRequiredAlert />
                </Flex>
              )}
              <AdminOrSupervisorOrOperationWrapper>
                <AddEditPondProjectionModal containerProps={{ ms: 'auto' }}>
                  <BaseButton
                    analyticsId={actionsName.pondOverviewManageProjectionClicked}
                    analyticsData={{ addCurrentPopulation: true }}
                    size='sm'
                    bg='transparent'
                    variant='secondary'
                  >
                    {trans('t_manage_projections')}
                  </BaseButton>
                </AddEditPondProjectionModal>
              </AdminOrSupervisorOrOperationWrapper>
            </Flex>
          </VisionOnlyGuard>
          <VisionOnlyAccess>
            <Box />
          </VisionOnlyAccess>
        </>
      )}
    </CardContainer>
  );
}

function MoreOptionsMenu({
  graphCustomization,
  onSubmit
}: {
  graphCustomization?: CustomizationProductionFormValues;
  onSubmit: (v: CustomizationProductionFormValues) => void;
}) {
  return (
    <CustomizeProductionChartModal onSubmit={onSubmit} defaultValues={graphCustomization}>
      <IconButton
        h='fit-content'
        w='fit-content'
        bgColor='white'
        aria-label='filter-button'
        _focus={{ outline: 'none' }}
        css={{
          '&:hover svg.first-path': { fill: 'brandBlue.300' },
          '&:hover svg.second-path': { fill: 'graphBrandBlue' }
        }}
      >
        <SettingsAdjustFilled w='20px' h='20px' transform='scale(1, -1)' />
      </IconButton>
    </CustomizeProductionChartModal>
  );
}
