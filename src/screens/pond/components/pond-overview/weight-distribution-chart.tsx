import { useMemo, useState } from 'react';
import { useAppSelector } from '@redux/hooks';
import { getRangeFromWeightDistribution } from '@screens/population/helpers/weight-distribution';
import { WeightDistributionChartContainer } from '@screens/monitoring/components/monitoring-details';

type WeightCharts = 'weight' | 'heads-on' | 'heads-off';

export function WeightDistributionChart() {
  const [selectedWeightChart, setSelectedWeightChart] = useState<WeightCharts>('weight');

  const lang = useAppSelector((state) => state.app.lang);
  const currentPopulation = useAppSelector((state) => state.farm?.currentPopulation);
  const {
    growthBands,
    seedingAverageWeight,
    lastMonitoringDate,
    metadata,
    lastMonitoringMlResult,
    history,
    manualAverageWeights
  } = currentPopulation;
  const manuallyMonitored = metadata?.manuallyMonitored;

  const { averageWeight, weightCv, weightDistribution, date: lastHistoryDate, modelAverageWeight } = history?.[0] ?? {};
  const averageWeightValue = averageWeight ?? seedingAverageWeight;

  const { minWeight, maxWeight, weightRageDiff, isInvalidABW } = useMemo(() => {
    const { min: minWeight, max: maxWeight } = getRangeFromWeightDistribution(weightDistribution, 0.95, lang) ?? {};
    const weightRageDiff: number | undefined = minWeight && maxWeight ? Math.round(maxWeight - minWeight) : undefined;
    const isInvalidABW = averageWeightValue > 40 || averageWeightValue < 2;

    return {
      minWeight,
      maxWeight,
      weightRageDiff,
      isInvalidABW
    };
  }, [weightDistribution, weightCv, growthBands, lang]);

  if (isInvalidABW || manuallyMonitored) return null;

  return (
    <WeightDistributionChartContainer
      selectedWeightChart={selectedWeightChart}
      setSelectedWeightChart={setSelectedWeightChart}
      mlResult={lastMonitoringMlResult}
      minWeight={minWeight}
      maxWeight={maxWeight}
      weightRageDiff={weightRageDiff}
      averageWeightValue={averageWeightValue}
      monitoringDate={lastMonitoringDate}
      modelAverageWeight={modelAverageWeight}
      manualAverageWeight={manualAverageWeights?.find((item) => item.date === lastHistoryDate)}
    />
  );
}
