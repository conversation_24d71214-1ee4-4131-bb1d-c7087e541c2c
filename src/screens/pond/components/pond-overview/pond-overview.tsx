import { Box, Flex, Skeleton, Text } from '@chakra-ui/react';
import { useAppSelector } from '@redux/hooks';
import { PondProductionChart } from '@screens/pond/components/pond-overview/pond-production-chart';
import { WeeklyProductionDataTable } from '@screens/pond/components/pond-overview/weekly-production-data-table';
import { LastHistoryCard } from '@screens/pond/components/pond-overview/last-history-card';
import { WeightDistributionChart } from '@screens/pond/components/pond-overview/weight-distribution-chart';
import { ManualWeeklyGrowth } from '@screens/pond/components/weekly-growth/manual-weekly-growth';
import { getPondState } from '@screens/pond/helpers/get-pond-state';
import { useSetFarmBookRefererInRedux } from '@components/farm-book/hooks/use-set-farm-book-referer-in-redux';
import { CardContainer } from '@components/card-container/card-container';
import { PondAlerts } from '@screens/pond/components/alerts/pond-alerts';
import { getTrans } from '@i18n/get-trans';
import { MobileLandscapeOnlyModal } from '@components/mobile-view/mobile-landscape-only-modal';
import { useGetDeviceDimensions } from '@hooks/use-get-device-dimensions';
import { ViewTableIcon } from '@icons/view-table/view-table-icon';
import { InView } from 'react-intersection-observer';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { VisionOnlyGuard } from '@components/permission-wrappers/vision-only-guard';
import dynamic from 'next/dynamic';
import { CloseFilled } from '@icons/close/close-filled';
import { Expand } from '@icons/expand/expand';
import { useListTraysApi } from '@screens/monitoring/hooks/use-list-trays-api';
import { formatNumber, isNumber } from '@utils/number';
import { convertGpsDistance } from '@utils/gps';
import { GpsAlerts } from '@screens/pond/components/alerts/gps-alerts';
import { TrayCarousel } from '@screens/monitoring/components/tray-carousel';
import { useListMonitoringApi } from '@screens/monitoring/hooks/use-list-monitoring-api';

const PolygonMap = dynamic(
  () => import('@components/polygon-map/polygon-map').then((mod) => ({ default: mod.PolygonMap })),
  {
    ssr: false,
    loading: () => (
      <Box height='300px' bg='gray.100' display='flex' alignItems='center' justifyContent='center'>
        Loading map...
      </Box>
    )
  }
);

export function PondOverview() {
  useSetFarmBookRefererInRedux('overview');
  const currentPopulation = useAppSelector((state) => state.farm?.currentPopulation);
  const [photosExpanded, setPhotosExpanded] = useState(false);

  const { _id: populationId, stockedAt, stockedAtTimezone } = currentPopulation ?? {};

  const { canHarvest, hasMonitoring } = getPondState({ population: currentPopulation });

  if (canHarvest && !hasMonitoring) {
    return (
      <>
        <ManualWeeklyGrowth stockedAt={stockedAt} stockedAtTimezone={stockedAtTimezone} populationId={populationId} />

        <VisionOnlyGuard>
          <PondAlerts />
        </VisionOnlyGuard>
      </>
    );
  }

  return (
    <Flex direction='column' gap='2lg' pos='relative'>
      <VisionOnlyGuard>
        <PondAlerts />
      </VisionOnlyGuard>
      <GpsAlerts />

      <Flex
        gap={{ base: '2lg', xl: 'sm-alt' }}
        direction={{ base: 'column-reverse', xl: 'row' }}
        flexWrap={{ base: 'wrap', xl: 'nowrap' }}
      >
        <LastHistoryCard />
        <PondProductionChart display={{ base: 'none', lg: 'flex' }} />
        <PondProductionChartMobileModal />
      </Flex>

      <WeeklyProductionDataTable display={{ base: 'none', lg: 'block' }} />
      <WeeklyProductionDataTableMobileModal />

      <WeightDistributionChart />

      <Flex
        mb='2xl'
        gap={{ base: '2lg', xl: 'sm-alt' }}
        direction={{ base: 'column', lg: 'row' }}
        flexWrap={{ base: 'wrap', xl: 'nowrap' }}
      >
        <Box flex={1}>
          <ImageCarouselCard photosExpanded={photosExpanded} setPhotosExpanded={setPhotosExpanded} />
        </Box>
        {currentPopulation?.lastMonitoringDistance != undefined && (
          <Box flex={1}>
            <GpsLocationCard photosExpanded={photosExpanded} />
          </Box>
        )}
      </Flex>
    </Flex>
  );
}

function GpsLocationCard({ photosExpanded }: { photosExpanded: boolean }) {
  const { trans } = getTrans();
  const [isExpanded, setIsExpanded] = useState(false);
  const [mapKey, setMapKey] = useState(0);

  const [{ data: monitoringRes, isLoading: isLoadingMonitoring, error: errorMonitoring }, listMonitoring] =
    useListMonitoringApi();

  const lang = useAppSelector((state) => state.app.lang);
  const geoPolygon = useAppSelector((state) => state.farm?.currentPond?.geoPolygon);
  const lastMonitoringIds = useAppSelector((state) => state.farm?.currentPopulation?.lastMonitoringIds);
  const lastMonitoringDistance = useAppSelector((state) => state.farm?.currentPopulation?.lastMonitoringDistance);

  const convertedDistance = convertGpsDistance(lastMonitoringDistance);
  const convertedDistanceToPond = convertedDistance ? convertedDistance.distance : null;
  const convertedDistanceToPondUnit = convertedDistance ? convertedDistance.unit : null;
  const lastMonitoringDistanceFormatted = isNumber(convertedDistanceToPond)
    ? formatNumber(Math.abs(convertedDistanceToPond), { lang, fractionDigits: 2 })
    : null;

  useEffect(() => {
    if (!lastMonitoringIds?.length) return;

    listMonitoring({
      loadRelated: false,
      params: {
        sort: [{ field: 'startedAt', order: 'desc' }],
        filter: { id: lastMonitoringIds },
        page: { current: 1, size: 100000 }
      }
    });
  }, [JSON.stringify(lastMonitoringIds ?? [])]);

  useEffect(() => {
    // Force map to re-render after container size change (both expand and collapse)
    setTimeout(() => {
      setMapKey((prev) => prev + 1);
    }, 100);
  }, [isExpanded]);

  const { monitorings = [] } = monitoringRes ?? {};

  // Hide the map when photos are expanded
  if (photosExpanded) {
    return null;
  }

  const markerPositions: { latitude: number; longitude: number; distanceToPond: number }[] = [];
  if (monitorings?.[0]?.coordinate?.coordinates.length > 0) {
    const [lat, lng] = monitorings[0].coordinate.coordinates;
    markerPositions.push({
      latitude: lat,
      longitude: lng,
      distanceToPond: convertedDistanceToPond
    });
  }

  return (
    <InView rootMargin='40px' triggerOnce>
      {({ inView, ref }) => (
        <CardContainer
          p='md'
          minH='max-content'
          {...(isExpanded && {
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 1000,
            height: '100%',
            rounded: 'none'
          })}
        >
          <div ref={ref}>
            {!inView && <Skeleton height='100%' minH='300px' w='100%' id='gps-location' />}
            {inView && (
              <>
                <Flex align='center' justify='space-between' mb='md' id='gps-location'>
                  <Flex align='center' gap='md-alt'>
                    <Text size='label100'>{trans('t_latest_monitoring_location')}</Text>
                  </Flex>
                  <Box
                    cursor='pointer'
                    onClick={() => {
                      setIsExpanded(!isExpanded);
                    }}
                  >
                    {isExpanded ? <CloseFilled w='24px' h='24px' /> : <Expand w='24px' h='24px' />}
                  </Box>
                </Flex>
                <PolygonMap
                  key={mapKey}
                  coordinates={
                    geoPolygon?.coordinates?.map((coord: number[]) => ({
                      latitude: coord[0]?.toString() || '',
                      longitude: coord[1]?.toString() || ''
                    })) || []
                  }
                  markerPositions={markerPositions?.map((markerPosition, index) => ({
                    latitude: markerPosition.latitude,
                    longitude: markerPosition.longitude,
                    tooltip:
                      markerPosition?.distanceToPond > 0
                        ? `${trans('t_gps_distance')}: ${lastMonitoringDistanceFormatted}${convertedDistanceToPondUnit}`
                        : `${trans('t_gps_distance')}: ${trans('t_near_pond')}`,
                    color: ['#e74c3c', '#3498db', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c', '#e67e22', '#34495e'][
                      index % 8
                    ],
                    size: 'small'
                  }))}
                  height={isExpanded ? 'calc(100vh - 120px)' : '300px'}
                />

                <Text size='label200' color={lastMonitoringDistance > 20 ? 'semanticRed.600' : 'black'} mt='md'>
                  {lastMonitoringDistance === 0
                    ? trans('x_monitoring_location_near_the_pond')
                    : trans('x_monitoring_location_from_the_pond', {
                        distance: lastMonitoringDistanceFormatted,
                        unit: convertedDistanceToPondUnit
                      })}
                </Text>
              </>
            )}
          </div>
        </CardContainer>
      )}
    </InView>
  );
}

function ImageCarouselCard({
  photosExpanded,
  setPhotosExpanded
}: {
  photosExpanded: boolean;
  setPhotosExpanded: Dispatch<SetStateAction<boolean>>;
}) {
  const [{ data: traysRes }, listTrays] = useListTraysApi();
  const manuallyMonitored = useAppSelector((state) => state.farm?.currentPopulation?.metadata?.manuallyMonitored);
  const lastMonitoringIds = useAppSelector((state) => state.farm?.currentPopulation?.lastMonitoringIds);

  useEffect(() => {
    if (!lastMonitoringIds?.length || manuallyMonitored) return;
    listTrays({
      params: {
        filter: { monitoringId: lastMonitoringIds },
        page: { current: 1, size: 10000 },
        sort: [{ field: 'createdAt', order: 'desc' }]
      }
    });
  }, [JSON.stringify(lastMonitoringIds ?? [])]);

  if (manuallyMonitored) return null;

  return (
    <InView rootMargin='40px' triggerOnce>
      {({ inView, ref }) => (
        <CardContainer
          p='md'
          minH='max-content'
          {...(photosExpanded && {
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 1000,
            height: '100%',
            rounded: 'none'
          })}
        >
          <div ref={ref}>
            {!inView && <Skeleton height='100%' minH='300px' w='100%' />}
            {inView && (
              <TrayCarousel
                trays={traysRes?.trays ?? []}
                isExpanded={photosExpanded}
                setIsExpanded={setPhotosExpanded}
              />
            )}
          </div>
        </CardContainer>
      )}
    </InView>
  );
}

function WeeklyProductionDataTableMobileModal() {
  const { trans } = getTrans();

  const deviceDimensions = useGetDeviceDimensions();

  const chartHeight = deviceDimensions.height - 80;

  return (
    <MobileLandscapeOnlyModal
      buttonIcon={<ViewTableIcon />}
      buttonTitle={trans('t_click_to_see_weekly_production_data')}
    >
      <WeeklyProductionDataTable tableMaxHeight={`${chartHeight}px`} />
    </MobileLandscapeOnlyModal>
  );
}

function PondProductionChartMobileModal() {
  const { trans } = getTrans();

  const deviceDimensions = useGetDeviceDimensions();

  const isPortrait = deviceDimensions.height > deviceDimensions.width;

  const chartHeight = isPortrait ? deviceDimensions.height - 150 : deviceDimensions.height - 120;

  return (
    <MobileLandscapeOnlyModal buttonTitle={trans('t_mobile_graph_msg')}>
      <PondProductionChart flex={0} chartHeight={chartHeight} />
    </MobileLandscapeOnlyModal>
  );
}
