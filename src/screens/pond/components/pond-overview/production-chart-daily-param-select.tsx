import { Box, Flex, Input } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { ChevronUpFilled } from '@icons/chevron-up/chevron-up-filled';
import { ChevronDownFilled } from '@icons/chevron-down/chevron-down-filled';
import { useState } from 'react';
import { SearchDuoIcon } from '@icons/search/search-duo-icon';
import { FarmDailyParameters } from '@xpertsea/module-farm-sdk';
import { ChartVarsList } from '@screens/pond/components/pond-overview/helpers/pond-production-chart-helpers';
import { MenuButton, MenuContent, MenuItem, MenuRoot } from '@components/ui/menu';
import { InputGroup } from '@components/ui/input-group';
import { CloseFilled } from '@icons/close/close-filled';
import { DailyParams, useGetDailyDataTrans } from '@screens/settings/hooks/use-get-daily-data-trans';

interface ProductionChartVariableSelectProps {
  onVariableChange(variable: 'one' | 'two', value: FarmDailyParameters): void;

  chartDailyParamVariableOne: FarmDailyParameters;
  chartDailyParamVariableTwo: FarmDailyParameters;
  activeDailyParameters: FarmDailyParameters[];
  chartSelectedVars: ChartVarsList;
  isDisabled?: boolean;
}

export function ProductionChartDailyParamSelect(props: ProductionChartVariableSelectProps) {
  const {
    onVariableChange,
    activeDailyParameters,
    chartDailyParamVariableOne,
    chartDailyParamVariableTwo,
    chartSelectedVars,
    isDisabled
  } = props;
  const { trans } = getTrans();

  const [firstVariableSearch, setFirstVariableSearch] = useState('');
  const [secondVariableSearch, setSecondVariableSearch] = useState('');
  const [firstMenuOpen, setFirstMenuOpen] = useState(false);
  const [secondMenuOpen, setSecondMenuOpen] = useState(false);

  const { dailyWithUnitsTransMap } = useGetDailyDataTrans();
  const firstDailyParamName = chartDailyParamVariableOne?.name;
  const secondDailyParamName = chartDailyParamVariableTwo?.name;

  const variableOneLabel =
    dailyWithUnitsTransMap[firstDailyParamName as DailyParams] ?? firstDailyParamName ?? trans('t_select_option');
  const variableTwoLabel =
    dailyWithUnitsTransMap[secondDailyParamName as DailyParams] ?? secondDailyParamName ?? trans('t_select_option');

  const firstVariableList = activeDailyParameters.filter((item) => {
    const itemName = dailyWithUnitsTransMap[item.name as DailyParams] ?? item.name;
    const isSearched =
      firstVariableSearch.length > 1 ? itemName.toLowerCase().includes(firstVariableSearch.toLowerCase()) : true;
    return item._id !== chartDailyParamVariableTwo?._id && isSearched;
  });

  const secondVariableList = activeDailyParameters.filter((item) => {
    const itemName = dailyWithUnitsTransMap[item.name as DailyParams] ?? item.name;
    const isSearched =
      secondVariableSearch.length > 1 ? itemName.toLowerCase().includes(secondVariableSearch.toLowerCase()) : true;
    return item._id !== chartDailyParamVariableOne?._id && isSearched;
  });

  const hasMoreThanOneItemSelected = chartSelectedVars.length > 1;
  const hasMoreThanOrEqualTwoItemSelected = chartSelectedVars.length >= 2;

  return (
    <Flex flexWrap='wrap' align='center' gap='md-alt'>
      <MenuRoot
        onOpenChange={(value) => {
          setFirstMenuOpen(value.open);
          if (!value.open) {
            setFirstVariableSearch('');
          }
        }}
        positioning={{ placement: 'bottom-start' }}
        highlightedValue={chartDailyParamVariableOne?._id}
      >
        <MenuButton
          size='sm'
          variant='link'
          color='text.gray.weak'
          _active={{ color: 'text.gray.weak' }}
          _hover={{ textDecoration: 'none', color: 'text.gray.weak' }}
          disabled={isDisabled || (hasMoreThanOrEqualTwoItemSelected && !chartDailyParamVariableOne)}
        >
          <Box boxSize='12px' rounded='base' bgColor='brandGreen.800' />
          <Box as='span' color={chartDailyParamVariableOne ? 'primary' : 'gray.500'}>
            {variableOneLabel}
          </Box>
          <Box as='span'>
            {hasMoreThanOneItemSelected && chartDailyParamVariableOne ? (
              <CloseFilled
                w='20px'
                h='20px'
                bgColor='transparent'
                onClick={(e) => {
                  e.stopPropagation();
                  onVariableChange('one', undefined);
                }}
              />
            ) : (
              <>
                {firstMenuOpen && (
                  <ChevronUpFilled w='20px' h='20px' className='chevron-icon' hasBackground={false} color='icon.gray' />
                )}
                {!firstMenuOpen && (
                  <ChevronDownFilled
                    w='20px'
                    h='20px'
                    className='chevron-icon'
                    hasBackground={false}
                    color='icon.gray'
                  />
                )}
              </>
            )}
          </Box>
        </MenuButton>
        <MenuContent minW='214px' maxH='55vh' overflowY='auto' portalled={false}>
          <InputGroup
            w='full'
            startElement={<SearchDuoIcon pointerEvents='none' />}
            endElement={
              firstVariableSearch && (
                <CloseFilled hasBackground={false} cursor='pointer' onClick={() => setFirstVariableSearch('')} />
              )
            }
          >
            <Input
              value={firstVariableSearch}
              borderRadius='full'
              fontSize='lg'
              outline='none'
              autoFocus
              autoComplete='off'
              placeholder={trans('t_search')}
              _placeholder={{ color: 'text.gray.disabled' }}
              onChange={(e) => {
                setFirstVariableSearch(e.target.value);
                e.stopPropagation();
                e.target.focus();
              }}
            />
          </InputGroup>
          <MenuItem value='none' display='none' />
          {firstVariableList.map((item, index) => {
            const isFirstItem = index === 0;
            return (
              <MenuItem
                autoFocus={false}
                key={item._id}
                value={item._id}
                {...(isFirstItem && { mt: 'xs-alt' })}
                onClick={() => onVariableChange('one', item)}
              >
                {dailyWithUnitsTransMap[item.name as DailyParams] ?? item.name}
              </MenuItem>
            );
          })}
        </MenuContent>
      </MenuRoot>

      <MenuRoot
        onOpenChange={(value) => {
          setSecondMenuOpen(value.open);
          if (!value.open) {
            setSecondVariableSearch('');
          }
        }}
        positioning={{ placement: 'bottom-start' }}
        highlightedValue={chartDailyParamVariableTwo?._id}
      >
        <MenuButton
          size='sm'
          variant='link'
          color='text.gray.weak'
          _active={{ color: 'text.gray.weak' }}
          _hover={{ textDecoration: 'none', color: 'text.gray.weak' }}
          disabled={isDisabled || (hasMoreThanOrEqualTwoItemSelected && !chartDailyParamVariableTwo)}
        >
          <Box boxSize='12px' rounded='base' bgColor='semanticYellow.800' />
          <Box as='span' color={chartDailyParamVariableTwo ? 'primary' : 'gray.500'}>
            {variableTwoLabel}
          </Box>
          <Box as='span'>
            {hasMoreThanOneItemSelected && chartDailyParamVariableTwo ? (
              <CloseFilled
                w='20px'
                h='20px'
                bgColor='transparent'
                onClick={(e) => {
                  e.stopPropagation();
                  onVariableChange('two', undefined);
                }}
              />
            ) : (
              <>
                {secondMenuOpen && (
                  <ChevronUpFilled w='20px' h='20px' className='chevron-icon' hasBackground={false} color='icon.gray' />
                )}
                {!secondMenuOpen && (
                  <ChevronDownFilled
                    w='20px'
                    h='20px'
                    className='chevron-icon'
                    hasBackground={false}
                    color='icon.gray'
                  />
                )}
              </>
            )}
          </Box>
        </MenuButton>
        <MenuContent maxH='55vh' overflowY='auto' portalled={false}>
          <InputGroup
            w='full'
            startElement={<SearchDuoIcon pointerEvents='none' />}
            endElement={
              secondVariableSearch && (
                <CloseFilled hasBackground={false} cursor='pointer' onClick={() => setSecondVariableSearch('')} />
              )
            }
          >
            <Input
              value={secondVariableSearch}
              borderRadius='full'
              fontSize='lg'
              outline='none'
              autoFocus
              autoComplete='off'
              placeholder={trans('t_search')}
              _placeholder={{ color: 'text.gray.disabled' }}
              onChange={(e) => {
                setSecondVariableSearch(e.target.value);
                e.stopPropagation();
                e.target.focus();
              }}
            />
          </InputGroup>
          <MenuItem value='none' display='none' />
          {secondVariableList.map((item, index) => {
            const isFirstItem = index === 0;

            return (
              <MenuItem
                autoFocus={false}
                key={item._id}
                value={item._id}
                {...(isFirstItem && { mt: 'xs-alt' })}
                onClick={() => onVariableChange('two', item)}
              >
                {dailyWithUnitsTransMap[item.name as DailyParams] ?? item.name}
              </MenuItem>
            );
          })}
        </MenuContent>
      </MenuRoot>
    </Flex>
  );
}
