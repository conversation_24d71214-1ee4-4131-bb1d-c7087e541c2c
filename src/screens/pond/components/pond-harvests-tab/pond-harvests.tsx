import { Box, chakra, Flex, FlexProps, Separator, Table, Text } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { EmptyState } from '@components/errors/empty-state';
import { DateTime } from 'luxon';
import { convertUnitByDivision, convertUnitByMultiplication, formatNumber, isNumber } from '@utils/number';
import { ChangeEvent, useRef, useState } from 'react';
import {
  getPopulationHarvestsData,
  HarvestsInfoType
} from '@screens/pond/components/pond-harvests-tab/pond-harvests-helpers';
import {
  OnUploadReportArgs,
  usePondPartialHarvests
} from '@screens/pond/components/pond-harvests-tab/use-pond-partial-harvests';
import {
  DeleteHarvestModal,
  HarvestMenuContainer,
  RecordHarvestModal
} from '@screens/pond/components/pond-harvests-tab/harvests-table-components';
import { useSetFarmBookRefererInRedux } from '@components/farm-book/hooks/use-set-farm-book-referer-in-redux';
import { usePermission } from '@hooks/use-permission';
import { AdminOnlyWrapper } from '@components/permission-wrappers/admin-only-wrapper';
import { useGetFileUrlApi } from '@screens/file/hooks/use-get-file-url-api';
import { BaseSpinner } from '@components/base/base-spinner';
import { useCreateFileApi } from '@screens/file/hooks/use-create-file-api';
import isUndefined from 'lodash/isUndefined';
import { FileIcon } from '@icons/file/file-icon';
import { MenuItem } from '@components/ui/menu';
import { OnRecordPartialHarvestFunction } from '@screens/population/components/harvest-modals/types';
import { TableContainer } from '@components/ui/table-container';
import { actionsName } from '@utils/segment';
import { EditFinalHarvestPlanModal } from '@screens/population/components/harvest-modals/edit-final-harvest-plan-modal';
import { useAnalytics } from '@hooks/use-analytics';
import { AddEditPartialHarvestPlanModal } from '@screens/population/components/harvest-modals/add-edit-partial-harvest-plan-modal';
import { getHarvestTypeText } from '@screens/pond/helpers/harvest-plan';

export function PondHarvests() {
  const { trans } = getTrans();

  useSetFarmBookRefererInRedux('harvest');

  const { currentFarm, currentPopulation, currentPond } = useAppSelector((state) => state.farm);
  const { harvest } = currentPopulation ?? {};

  const { isLoading, onRecordPartialHarvest } = usePondPartialHarvests();

  const { fullHarvestData, partialHarvestData } = getPopulationHarvestsData({
    farm: currentFarm,
    pond: currentPond,
    population: currentPopulation
  });

  const isHarvested = harvest?.date;

  const { name: pondName, _id: pondId, size: pondSize } = currentPond ?? {};

  return (
    <Flex direction='column' gap='md' py='md' px='sm-alt' borderRadius='xl' bgColor='white'>
      <Box>
        <Flex justify='space-between' align='center' mb='md'>
          <Text size='label100'>{trans('t_final_harvest')}</Text>

          <Flex gap='sm'>
            {!isHarvested && (
              <AddEditPartialHarvestPlanModal
                pondId={pondId}
                pondName={pondName}
                pondSize={pondSize}
                isInPondView
                triggerContainerProps={{ width: 'unset' }}
              >
                <BaseButton
                  size='sm'
                  data-cy='add-partial-harvest-plan-pond-btn'
                  analyticsId={actionsName.addPartialHarvestPlanClicked}
                  analyticsData={{
                    trigger: 'data-tab-add-partial-harvest-plan-action-button',
                    state: 'Add partial harvest plan',
                    addCurrentPopulation: true
                  }}
                >
                  {trans('t_plan_partial_harvest')}
                </BaseButton>
              </AddEditPartialHarvestPlanModal>
            )}
            <RecordHarvestModal
              pondId={pondId}
              pondName={pondName}
              pondSize={pondSize}
              onRecordPartialHarvest={onRecordPartialHarvest}
              isPartial
              isLoading={isLoading}
            >
              <BaseButton
                size='sm'
                loading={isLoading}
                analyticsId={actionsName.addHarvestPlanClicked}
                analyticsData={{
                  addCurrentPopulation: true,
                  trigger: 'data-tab-add-partial-harvest-action-button',
                  state: 'Add partial harvest'
                }}
              >
                {trans('t_record_partial_harvest')}
              </BaseButton>
            </RecordHarvestModal>
          </Flex>
        </Flex>
        <HarvestTable harvests={fullHarvestData} />
        {!fullHarvestData?.length && <EmptyState pt='lg' pb='sm' />}
      </Box>
      <Separator borderColor='border.gray.weak' />
      <Box>
        <Text mb='md' size='label100'>
          {trans('t_partial_harvest')}
        </Text>

        <HarvestTable harvests={partialHarvestData} isPartial />
        {!partialHarvestData?.length && <EmptyState pt='lg' pb='sm' />}
      </Box>
    </Flex>
  );
}

function HarvestTable({ harvests, isPartial }: { harvests: HarvestsInfoType[]; isPartial?: boolean }) {
  const { trans } = getTrans();

  const user = useAppSelector((state) => state.auth.user);
  const currentFarmId = useAppSelector((state) => state.farm?.currentFarm?._id);

  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);
  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');

  const isAdmin = usePermission({
    role: 'admin',
    entity: 'farm',
    entityId: currentFarmId
  });

  const { isLoading, onRecordPartialHarvest, onDeletePartialHarvest, onUploadReport } = usePondPartialHarvests();

  const revenuePerPoundTitle = trans('t_revenue_processed_unit', { unit: unitLabel });
  const biomassHarvestTitle = trans('t_biomass_harvested_unit_ha', { unit: unitLabel });

  return (
    <TableContainer>
      <Table.Root>
        <Table.Header>
          <Table.Row border='none'>
            {[
              trans('t_status'),
              trans('t_date'),
              biomassHarvestTitle,
              trans('t_harvest_type'),
              trans('t_abw_g'),
              ...(isAdmin ? [revenuePerPoundTitle, trans('t_revenue'), trans('t_actions')] : [trans('t_actions')])
            ].map((title, i) => (
              <Table.ColumnHeader border='none' textAlign='center' p='0' key={i}>
                <Text
                  h='40px'
                  display='flex'
                  alignItems='center'
                  bgColor='bg.gray.medium'
                  justifyContent='center'
                  borderRadius='xl'
                  mx='2xs'
                  px='sm-alt'
                  size='label200'
                  textAlign='center'
                >
                  {title}
                </Text>
              </Table.ColumnHeader>
            ))}
            <Table.ColumnHeader w='50px' p='0' border='none' />
          </Table.Row>
        </Table.Header>

        <Table.Body>
          {harvests?.map((harvest, index) => {
            const isLastRow = index === harvests.length - 1;
            return (
              <HarvestRow
                key={index}
                harvest={harvest}
                isPartial={isPartial}
                isLoading={isLoading}
                isLastRow={isLastRow}
                onUploadReport={onUploadReport}
                onRecordPartialHarvest={onRecordPartialHarvest}
                onDeletePartialHarvest={onDeletePartialHarvest}
              />
            );
          })}
        </Table.Body>
      </Table.Root>
    </TableContainer>
  );
}

type HarvestRowProps = {
  isLastRow?: boolean;
  harvest: HarvestsInfoType;
  isPartial: boolean;
  isLoading?: boolean;
  onUploadReport?: (args: OnUploadReportArgs) => void;
  onRecordPartialHarvest: OnRecordPartialHarvestFunction;
  onDeletePartialHarvest: (harvestId: string, isRecorded: boolean) => void;
};

function HarvestRow(props: HarvestRowProps) {
  const { harvest, isLastRow, isPartial, isLoading, onUploadReport, onRecordPartialHarvest, onDeletePartialHarvest } =
    props;
  const { trans } = getTrans();

  const lang = useAppSelector((state) => state.app.lang);
  const user = useAppSelector((state) => state.auth.user);
  const { trackAction } = useAnalytics();

  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);

  const {
    fileIds,
    state,
    biomass,
    biomassPerHa,
    harvestDate,
    averageWeight,
    revenue,
    revenuePerPound,
    harvestId,
    isPlanned,
    processorOptions,
    selectedProcessorId,
    priceList,
    enablesEstimation,
    survivalAtRecord,
    pondName,
    pondId,
    pondSize
  } = harvest;

  const { defaultHarvestType: harvestType, percentHarvestAsHeadsOn, percentHeadlessAsClassA } = priceList ?? {};

  const harvestDateValue = harvestDate
    ? DateTime.fromISO(harvestDate, { locale: lang }).toFormat('MMMM dd', { locale: lang })
    : '-';
  const unitTitle = isBiomassUnitLbs ? trans('t_lb_over_ha') : trans('t_kg_over_ha');
  const biomassPerHaValue = biomassPerHa
    ? `${formatNumber(convertUnitByDivision(biomassPerHa, unitsConfig?.biomass), {
        lang,
        fractionDigits: 2
      })} ${unitTitle}`
    : '-';
  const isRecorded = state === 'recorded';

  const estimatedStyles: FlexProps = enablesEstimation
    ? { px: 'xs', py: 'xs-alt', bgColor: 'bg.gray.strong', borderRadius: 'full' }
    : {};

  const estimatedSuffix = enablesEstimation ? '*' : '';

  const typeLabel = isPartial ? trans('t_partial') : trans('t_final');

  return (
    <Table.Row borderBottom='0.5px solid {colors.border.gray}' {...(isLastRow && { borderBottom: 'none' })}>
      <Text as='td' size='label200' textAlign='center' p='md' {...(isLastRow && { pb: 0 })}>
        {isRecorded ? trans('t_recorded') : trans('t_planned')}
      </Text>

      <Text as='td' size='label200' p='md' textAlign='center' {...(isLastRow && { pb: 0 })} textTransform='capitalize'>
        {harvestDateValue}
      </Text>

      <Text as='td' size='label200' textAlign='center' p='md' {...(isLastRow && { pb: 0 })}>
        {biomassPerHaValue}
      </Text>

      <Text as='td' size='label200' textAlign='center' p='md' {...(isLastRow && { pb: 0 })}>
        {getHarvestTypeText({
          harvestType,
          processorOptions,
          percentHarvestAsHeadsOn,
          percentHeadlessAsClassA,
          processorId: selectedProcessorId
        })}
      </Text>

      <Text as='td' size='label200' textAlign='center' p='md' {...(isLastRow && { pb: 0 })}>
        {isNumber(averageWeight) ? formatNumber(averageWeight, { lang, fractionDigits: 2 }) : '-'}
      </Text>
      <AdminOnlyWrapper>
        <Text as='td' size='label200' textAlign='center' p='md' {...(isLastRow && { pb: 0 })}>
          <Flex justify='center' align='center' {...estimatedStyles}>
            {isNumber(revenuePerPound)
              ? `${formatNumber(convertUnitByMultiplication(revenuePerPound, unitsConfig?.biomass), {
                  lang,
                  fractionDigits: 2,
                  isCurrency: true
                })}${estimatedSuffix}`
              : '-'}
          </Flex>
        </Text>
        <Text as='td' size='label200' textAlign='center' p='md' {...(isLastRow && { pb: 0 })}>
          <Flex justify='center' align='center' {...estimatedStyles}>
            {isNumber(revenue)
              ? `${formatNumber(revenue, {
                  lang,
                  fractionDigits: 2,
                  shouldAddZeros: false,
                  isCurrency: true
                })}${estimatedSuffix}`
              : '-'}
          </Flex>
        </Text>
      </AdminOnlyWrapper>
      <Table.Cell textAlign='center' py='md' {...(isLastRow && { pb: 0, borderBottom: 'none' })}>
        {isPlanned ? (
          <RecordHarvestModal
            pondId={pondId}
            pondName={pondName}
            pondSize={pondSize}
            harvestDate={harvestDate}
            harvestAmount={biomass}
            harvestAbw={averageWeight}
            revenue={revenue}
            selectedProcessorId={selectedProcessorId}
            harvestId={harvestId}
            enablesEstimation={enablesEstimation}
            priceList={priceList}
            survivalAtRecord={survivalAtRecord}
            isPartial={isPartial}
            onRecordPartialHarvest={onRecordPartialHarvest}
            isLoading={isLoading}
          >
            <BaseButton
              size='sm'
              variant='link'
              onClick={() => {
                trackAction(actionsName.harvestPondClicked, {
                  isPlanned: true,
                  itemName: 'record-planned',
                  trigger: 'data-tab-record-harvest-action-button',
                  state: `Record planned ${typeLabel} harvest`
                }).then();
              }}
            >
              {trans('t_record_harvest')}
            </BaseButton>
          </RecordHarvestModal>
        ) : (
          <AdminOnlyWrapper placeholder='-'>
            <UploadOrViewFileButton
              fileIds={fileIds}
              harvestId={harvestId}
              isPartial={isPartial}
              onUploadReport={onUploadReport}
            />
          </AdminOnlyWrapper>
        )}
      </Table.Cell>

      <Table.Cell textAlign='right' p='0' {...(isLastRow && { pb: 0, borderBottom: 'none' })}>
        <HarvestMenuContainer>
          <RecordHarvestModal
            pondId={pondId}
            pondName={pondName}
            pondSize={pondSize}
            harvestDate={harvestDate}
            harvestAmount={biomass}
            harvestAbw={averageWeight}
            revenue={revenue}
            selectedProcessorId={selectedProcessorId}
            harvestId={harvestId}
            enablesEstimation={enablesEstimation}
            priceList={priceList}
            survivalAtRecord={survivalAtRecord}
            isPartial={isPartial}
            onRecordPartialHarvest={onRecordPartialHarvest}
            isEdit={isRecorded}
            isLoading={isLoading}
          >
            <MenuItem
              value='record-edit'
              p='xs'
              onClick={() => {
                trackAction(actionsName.harvestPondClicked, {
                  isPlanned,
                  itemName: isPlanned ? 'record' : 'edit',
                  trigger: `data-tab-${isPlanned ? 'record' : 'edit'}-harvest-action-button`,
                  state: isRecorded ? `Edit recorded ${typeLabel} harvest` : `Record ${typeLabel} harvest`
                }).then();
              }}
            >
              {isPlanned ? trans('t_record') : trans('t_edit')}
            </MenuItem>
          </RecordHarvestModal>

          {isPlanned && (
            <>
              {!isPartial && (
                <EditFinalHarvestPlanModal
                  pondId={pondId}
                  pondName={pondName}
                  pondSize={pondSize}
                  isInPondView
                  triggerContainerProps={{ width: 'unset' }}
                >
                  <MenuItem
                    value='edit-harvest-plan-pond-btn'
                    data-cy='edit-harvest-plan-pond-btn'
                    onClick={() => {
                      trackAction(actionsName.editHarvestPlanClicked, {
                        pondId,
                        pondName,
                        trigger: 'data-tab-edit-harvest-plan-action-button',
                        state: 'edit final harvest plan'
                      }).then();
                    }}
                  >
                    {trans('t_edit')}
                  </MenuItem>
                </EditFinalHarvestPlanModal>
              )}
              {isPartial && (
                <AddEditPartialHarvestPlanModal
                  pondId={pondId}
                  pondName={pondName}
                  pondSize={pondSize}
                  isInPondView
                  triggerContainerProps={{ width: 'unset' }}
                  defaultValues={{
                    date: harvestDate,
                    processorId: selectedProcessorId,
                    priceList,
                    lbsHarvested: biomass
                  }}
                  harvestId={harvestId}
                >
                  <MenuItem
                    value='edit-partial-harvest-plan-pond-btn'
                    data-cy='edit-partial-harvest-plan-pond-btn'
                    onClick={() => {
                      trackAction(actionsName.editPartialHarvestPlanClicked, {
                        pondId,
                        pondName,
                        trigger: 'data-tab-edit-partial-harvest-plan-action-button',
                        state: 'edit partial harvest plan',
                        harvestId,
                        harvestDate
                      }).then();
                    }}
                  >
                    {trans('t_edit')}
                  </MenuItem>
                </AddEditPartialHarvestPlanModal>
              )}
            </>
          )}

          {isPartial && (
            <DeleteHarvestModal
              pondName={pondName}
              isPlanned={isPlanned}
              onConfirm={() => onDeletePartialHarvest(harvestId, isRecorded)}
            >
              <MenuItem value='delete' p='xs'>
                {trans('t_delete')}
              </MenuItem>
            </DeleteHarvestModal>
          )}
        </HarvestMenuContainer>
      </Table.Cell>
    </Table.Row>
  );
}

type UploadOrViewFileButtonProps = {
  fileIds: string[];
  harvestId: string;
  isPartial: boolean;
  onUploadReport: (args: OnUploadReportArgs) => void;
};

function UploadOrViewFileButton(props: UploadOrViewFileButtonProps) {
  const { fileIds, harvestId, isPartial, onUploadReport } = props;
  const { trans } = getTrans();
  const currentFarmId = useAppSelector((state) => state.farm.currentFarm?._id);
  const currentPondId = useAppSelector((state) => state.farm.currentPond?._id);
  const currentPopulationId = useAppSelector((state) => state.farm.currentPopulation?._id);

  const inputFileRef = useRef<HTMLInputElement>(null);
  const [isUploadingReport, setIsUploadingReport] = useState<boolean>(false);

  const [{ isLoading: isFileUrlLoading }, getFileUrl] = useGetFileUrlApi();
  const [_, createFile] = useCreateFileApi();

  if (!fileIds.length) {
    return (
      <>
        <chakra.input
          type='file'
          display='none'
          ref={inputFileRef}
          accept='application/pdf'
          onChangeCapture={(event: ChangeEvent<HTMLInputElement>) => {
            const file = event.target.files[0];

            setIsUploadingReport(true);
            createFile({
              params: {
                file,
                entity: 'farm',
                pondId: currentPondId,
                entityId: currentFarmId,
                populationId: currentPopulationId
              },
              successCallback(data) {
                onUploadReport({
                  fileId: data.file._id,
                  harvestId,
                  onSuccess: () => {
                    setIsUploadingReport(false);
                  },
                  isPartial
                });
              }
            });
          }}
        />
        <BaseButton size='sm' variant='link' loading={isUploadingReport} onClick={() => inputFileRef?.current?.click()}>
          {trans('t_upload_report')}
        </BaseButton>
      </>
    );
  }

  return (
    <>
      {isFileUrlLoading && (
        <Flex align='center' justify='center'>
          <BaseSpinner size='sm' borderWidth='2px' />
        </Flex>
      )}
      {!isFileUrlLoading && (
        <FileIcon
          color='brandSecondary'
          cursor='pointer'
          _hover={{ opacity: 0.8 }}
          onClick={() => {
            const [firstFileId] = fileIds;

            getFileUrl({
              params: { filter: { fileId: firstFileId } },
              successCallback(data) {
                globalThis.window?.open?.(data.url, '_blank');
              }
            });
          }}
        />
      )}
    </>
  );
}
