import { PondType } from '@redux/farm/set-current-pond';
import { FarmProcessorPriceLists } from '@xpertsea/module-farm-sdk';
import { DateTime } from 'luxon';
import { getDaysDiffBetweenDates } from '@screens/farm/helpers/farm-dates';
import { PopulationType } from '@redux/farm/set-current-population';
import { FarmType } from '@redux/farm/set-current-farm';

import { PriceList, SurvivalAtRecord } from '@screens/population/components/harvest-modals/types';
import { sqPerHectare } from '@utils/constants';

type TypeValues = 'final' | 'partial';

type StateValues = 'recorded' | 'planned';

export type HarvestsInfoType = {
  pondEid: number;
  harvestId: string;
  harvestDate: string;
  averageWeight: number;
  state: StateValues;
  fileIds: string[];
  biomass: number;
  biomassPerHa: number;
  profit: number;
  isPlanned: boolean;
  processorOptions: ReturnType<typeof getProcessorOptions>;
  selectedProcessorId?: string;
  priceList?: PriceList;
  enablesEstimation?: boolean;
  survivalAtRecord?: SurvivalAtRecord;
  pondId: string;
  biomassProcessed: number;
  cycle: string;
  type: TypeValues;
  pondName: string;
  pondSize: number;
  cycleDays: number;
  survival: number;
  animalsHarvested: number;
  animalsHarvestedHa: number;
  animalsHarvestedM2: number;
  revenue: number;
  revenuePerPound: number;
  revenuePoundHarvest: number;
  populationId: string;
};

function getProcessorOptions(processorPriceLists: FarmProcessorPriceLists[]) {
  const activeProcessorPriceLists = processorPriceLists?.filter((item) => item.status === 'active');
  return activeProcessorPriceLists?.map((item) => ({ value: item._id, label: item.name }));
}

interface GetPopulationHarvestsDataArgs {
  farm: FarmType;
  pond: PondType;
  population: PopulationType;
}

export function getPopulationHarvestsData(args: GetPopulationHarvestsDataArgs) {
  const { farm, pond, population } = args;

  const { timezone: farmTimezone, processorPriceLists } = farm ?? {};
  const { eid: pondEid, name: pondName, _id: pondId, size: pondSize } = pond ?? {};
  const {
    cycle,
    harvest,
    harvestPlan,
    partialHarvest,
    partialHarvestPlanned,
    productionPrediction,
    stockedAt,
    stockedAtTimezone,
    _id: populationId
  } = population ?? {};

  const stockedAtLuxon = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone ?? farmTimezone });
  const stockedAtDate = stockedAtLuxon.toFormat('yyyy-MM-dd');

  const {
    harvestDate: plannedHarvestDate,
    projection,
    harvestId: plannedHarvestId,
    processorPriceList: plannedProcessorPriceList,
    selectedProcessorId
  } = harvestPlan ?? {};

  const { profitProjectionData, plannedHarvestIdx } = projection ?? {};

  const processorOptions = getProcessorOptions(processorPriceLists);

  const res = {
    partialHarvestData: [] as HarvestsInfoType[],
    fullHarvestData: [] as HarvestsInfoType[]
  };

  [partialHarvest, partialHarvestPlanned].forEach((items, i) => {
    items //
      ?.filter((item) => !!item?.date)
      ?.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      ?.forEach((partialHarvest) => {
        const {
          harvestId,
          date,
          revenue,
          weight,
          lbsHarvested,
          lbsProcessed,
          fileIds,
          processorId,
          enablesEstimation,
          processorPriceList,
          survival,
          revenuePerPound,
          revenuePoundHarvest,
          quantity,
          survivalAtRecord
        } = partialHarvest;
        const cycleDays = getDaysDiffBetweenDates({ baseDate: date, dateToCompare: stockedAtDate });
        const isPlanned = i === 1;
        const plannedEnablesEstimation = isPlanned || undefined;
        const predictionOnPlannedHarvest = isPlanned
          ? productionPrediction?.find((item) => item.date === date)
          : undefined;
        const profitOnPlannedHarvestDate = isPlanned
          ? profitProjectionData?.find((item) => item.date === date)
          : undefined;

        const data = {
          pondEid,
          harvestId,
          fileIds,
          harvestDate: DateTime.fromISO(date).isValid
            ? date
            : DateTime.fromJSDate(new Date(date)).toFormat('yyyy-MM-dd'),
          isPlanned: isPlanned,
          state: (i === 0 ? 'recorded' : 'planned') as StateValues,
          biomass: lbsHarvested,
          biomassPerHa: lbsHarvested / pondSize,
          averageWeight: isPlanned ? predictionOnPlannedHarvest?.averageWeight : weight,
          revenue,
          profit:
            i === 0 ? undefined : profitOnPlannedHarvestDate?.[processorPriceList?.defaultHarvestType]?.profitPerPound,
          processorOptions,
          selectedProcessorId: processorId,
          priceList: processorPriceList,
          enablesEstimation: enablesEstimation ?? plannedEnablesEstimation,
          survivalAtRecord,
          pondId,
          biomassProcessed: lbsProcessed,
          cycle,
          type: 'partial' as TypeValues,
          pondName,
          pondSize,
          cycleDays,
          survival,
          revenuePerPound,
          revenuePoundHarvest,
          animalsHarvested: quantity,
          animalsHarvestedHa: quantity / pondSize,
          animalsHarvestedM2: quantity / pondSize / sqPerHectare,
          populationId
        };
        res.partialHarvestData.push(data);
      });
  });

  // full harvest
  const {
    harvestId,
    date: harvestDate,
    lbsHarvested,
    weight,
    fileIds,
    revenue,
    profitPoundHarvest: harvestProfitPerPound,
    totalProcessedBiomassLbs,
    enablesEstimation,
    processorPriceList: harvestProcessorPriceList,
    survival: harvestSurvival,
    revenuePerPound: harvestRevenuePerPound,
    revenuePoundHarvest: harvestRevenuePoundHarvest,
    animalsHarvestedHa: harvestAnimalsHarvestedHa,
    animalsHarvestedM2: harvestAnimalsHarvestedM2,
    totalAnimalsHarvested: harvestTotalAnimalsHarvested,
    processorId: fullHarvestProcessorId
  } = harvest ?? {};

  const profitOnPlannedHarvestDate =
    profitProjectionData?.[plannedHarvestIdx]?.[plannedProcessorPriceList?.defaultHarvestType as 'headon'];
  const predictionOnPlannedHarvest = productionPrediction?.find((item) => item.date === plannedHarvestDate);

  let populationHarvest: HarvestsInfoType = undefined;

  if (harvestDate) {
    const date = DateTime.fromISO(harvestDate).isValid
      ? harvestDate
      : DateTime.fromJSDate(new Date(harvestDate)).toFormat('yyyy-MM-dd');
    const cycleDays = getDaysDiffBetweenDates({ baseDate: date, dateToCompare: stockedAtDate });

    const farmPriceList = processorPriceLists?.find((item) => item._id === fullHarvestProcessorId)?.priceList;
    const priceList = harvestProcessorPriceList ?? farmPriceList;

    populationHarvest = {
      pondEid,
      harvestId,
      harvestDate: date,
      biomass: lbsHarvested,
      biomassPerHa: lbsHarvested / pondSize,
      averageWeight: weight,
      state: 'recorded',
      isPlanned: false,
      processorOptions,
      revenue,
      profit: harvestProfitPerPound,
      fileIds: fileIds ?? [],
      selectedProcessorId: fullHarvestProcessorId,
      priceList,
      enablesEstimation,
      pondId,
      biomassProcessed: totalProcessedBiomassLbs,
      cycle,
      type: 'final',
      pondName,
      pondSize,
      cycleDays,
      survival: harvestSurvival,
      revenuePerPound: harvestRevenuePerPound,
      revenuePoundHarvest: harvestRevenuePoundHarvest,
      animalsHarvested: harvestTotalAnimalsHarvested,
      animalsHarvestedHa: harvestAnimalsHarvestedHa,
      animalsHarvestedM2: harvestAnimalsHarvestedM2,
      populationId
    };
    res.fullHarvestData.push(populationHarvest);
  } else if (plannedHarvestDate) {
    const date = DateTime.fromISO(plannedHarvestDate).isValid
      ? plannedHarvestDate
      : DateTime.fromJSDate(new Date(plannedHarvestDate)).toFormat('yyyy-MM-dd');
    const cycleDays = getDaysDiffBetweenDates({ baseDate: date, dateToCompare: stockedAtDate });
    const farmPriceList = processorPriceLists?.find((item) => item._id === selectedProcessorId)?.priceList;
    const priceList = plannedProcessorPriceList ?? farmPriceList;

    const productionPredictionOnHarvestDate = productionPrediction?.find((data) => data.date === date);

    populationHarvest = {
      pondEid,
      harvestId: plannedHarvestId,
      harvestDate: date,
      biomass: productionPredictionOnHarvestDate?.biomassLbs,
      biomassPerHa: productionPredictionOnHarvestDate?.biomassLbsByHa,
      averageWeight: productionPredictionOnHarvestDate?.averageWeight,
      state: 'planned',
      isPlanned: true,
      processorOptions,
      revenue: profitOnPlannedHarvestDate?.liveTotalRevenue,
      profit: profitOnPlannedHarvestDate?.profitPerPound,
      fileIds: [],
      selectedProcessorId,
      priceList,
      enablesEstimation: true,
      pondId,
      biomassProcessed: profitOnPlannedHarvestDate?.biomassProcessedLbs,
      cycle,
      type: 'final',
      cycleDays,
      pondName,
      pondSize,
      survival: predictionOnPlannedHarvest?.survival,
      revenuePerPound: profitOnPlannedHarvestDate?.revenuePerPound,
      revenuePoundHarvest: profitOnPlannedHarvestDate?.revenuePoundHarvest,
      animalsHarvested: productionPredictionOnHarvestDate?.animalsRemainingHa, // (predictionOnPlannedHarvest?.animalsRemainingHa ?? 0) * pondSize,
      animalsHarvestedHa: productionPredictionOnHarvestDate?.animalsRemainingHa,
      animalsHarvestedM2: productionPredictionOnHarvestDate?.animalsRemainingM2,
      populationId
    };
    res.fullHarvestData.push(populationHarvest);
  }

  return res;
}
