import { Box, Text } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { AddEditFinalHarvestModal } from '@screens/population/components/harvest-modals/add-edit-final-harvest-modal';
import { ReactNode, useState } from 'react';
import { MoreHoriz } from '@icons/more-horiz/more-horiz';
import {
  OnRecordPartialHarvestFunction,
  PriceList,
  SurvivalAtRecord
} from '@screens/population/components/harvest-modals/types';
import { PopulationType } from '@redux/farm/set-current-population';
import { DialogBody, DialogContent, DialogFooter, DialogHeader, DialogRoot, DialogTitle } from '@components/ui/dialog';
import { MenuButton, MenuContent, MenuRoot } from '@components/ui/menu';
import { RecordPartialHarvestModal } from '@screens/population/components/harvest-modals/record-partial-harvest-modal';

type DeleteHarvestConfirmationProps = {
  pondName: string;
  children: ReactNode;
  isPlanned?: boolean;
  onConfirm: () => void;
  onCancel?: () => void;
};
export function DeleteHarvestModal(props: DeleteHarvestConfirmationProps) {
  const { children, pondName, isPlanned, onConfirm, onCancel: propOnCancel } = props;

  const { trans } = getTrans();
  const [open, setOpen] = useState(false);

  const onCancel = () => {
    setOpen(false);
    propOnCancel?.();
  };

  return (
    <DialogRoot
      placement='center'
      open={open}
      onOpenChange={(value) => {
        value.open ? setOpen(value.open) : onCancel();
      }}
    >
      <Box onClick={() => setOpen(true)}>{children}</Box>

      <DialogContent portalled={false} gap='2lg' display='flex' maxW='500px' flexDirection='column' bgColor='gray.100'>
        <DialogHeader px='lg' pt='lg' pb='0px '>
          <DialogTitle>{trans('t_delete_harvest_confirmation')}</DialogTitle>
        </DialogHeader>
        <DialogBody display='flex' flexDirection='column' gap='2lg' px='lg' py={0}>
          <Text size='label100' color='gray.700'>
            {trans('t_you_are_about_to_delete_harvest', { pondName })}
          </Text>
          <Text size='label100' color='gray.700'>
            {isPlanned ? trans('t_delete_planned_partial_harvest') : trans('t_delete_recorded_partial_harvest')}
          </Text>
          {!isPlanned && (
            <Text size='label100' fontWeight='bold' color='gray.700'>
              {trans('t_please_adjust_your_survival_rate_accordingly')}
            </Text>
          )}
        </DialogBody>
        <DialogFooter gap='sm-alt' p='lg' pt='0px !important'>
          <BaseButton variant='secondary' onClick={onCancel} bgColor='transparent'>
            {trans('t_cancel')}
          </BaseButton>
          <BaseButton
            onClick={() => {
              onConfirm();
              setOpen(false);
            }}
          >
            {trans('t_delete')}
          </BaseButton>
        </DialogFooter>
      </DialogContent>
    </DialogRoot>
  );
}

export function HarvestMenuContainer({ children }: { children: ReactNode[] }) {
  return (
    <MenuRoot closeOnSelect={false}>
      <MenuButton variant='link' _hover={{ opacity: 0.8 }}>
        <MoreHoriz />
      </MenuButton>
      <MenuContent
        px='xs'
        fontSize='md'
        rounded='base'
        color='gray.700'
        fontWeight={500}
        lineHeight='16px'
        boxShadow='0px 3px 6px -3px rgba(23, 24, 24, 0.08), 0px 8px 20px -4px rgba(23, 24, 24, 0.12)'
      >
        {children}
      </MenuContent>
    </MenuRoot>
  );
}

interface RecordHarvestModalProps {
  children: ReactNode;
  harvestDate?: string;
  harvestAmount?: number;
  harvestAbw?: number;
  revenue?: number;
  partialSurvival?: number;
  selectedProcessorId?: string;
  harvestId?: string;
  isPartial: boolean;
  onRecordPartialHarvest: OnRecordPartialHarvestFunction;
  isEdit?: boolean;
  enablesEstimation?: boolean;
  priceList?: PriceList;
  isLoading?: boolean;
  pondName: string;
  pondId: string;
  pondSize: number;
  population?: PopulationType;
  onCancel?: () => void;
  onSuccess?: () => void;
  isInPondView?: boolean;
  isInPondListView?: boolean;
  survivalAtRecord?: SurvivalAtRecord;
}

export function RecordHarvestModal(props: RecordHarvestModalProps) {
  const {
    children,
    harvestDate,
    harvestAmount,
    harvestAbw,
    revenue,
    harvestId,
    selectedProcessorId,
    isPartial,
    onRecordPartialHarvest,
    isEdit,
    enablesEstimation,
    priceList,
    isLoading,
    pondName,
    pondId,
    pondSize,
    population,
    onCancel,
    onSuccess,
    isInPondView = true,
    isInPondListView = false,
    survivalAtRecord
  } = props;

  const currentPopulation = useAppSelector((state) => state.farm?.currentPopulation);

  const populationToUse = population ?? currentPopulation;

  if (isPartial) {
    return (
      <RecordPartialHarvestModal
        pondId={pondId}
        pondName={pondName}
        pondSize={pondSize}
        population={populationToUse}
        harvestId={harvestId}
        isEdit={isEdit}
        survivalAtRecord={survivalAtRecord}
        defaultValues={{
          date: harvestDate,
          lbsHarvested: harvestAmount,
          weight: harvestAbw,
          revenue,
          processorId: selectedProcessorId,
          enablesEstimation,
          priceList
        }}
        isLoading={isLoading}
        onSuccess={onSuccess}
        onCancel={onCancel}
        onSubmit={onRecordPartialHarvest}
      >
        {children}
      </RecordPartialHarvestModal>
    );
  }

  return (
    <AddEditFinalHarvestModal
      pondId={pondId}
      pondName={pondName}
      population={populationToUse}
      onCancel={onCancel}
      isInPondView={isInPondView}
      isInPondListView={isInPondListView}
      triggerContainerProps={{ width: '100%' }}
      defaultValues={{
        date: harvestDate,
        lbsHarvested: harvestAmount,
        weight: harvestAbw,
        revenue,
        processorId: selectedProcessorId,
        enablesEstimation,
        priceList
      }}
      isEdit={isEdit}
      onSuccess={onSuccess}
    >
      {children}
    </AddEditFinalHarvestModal>
  );
}
