import { Box, Flex, FlexProps, IconButton, Menu, SimpleGrid, Text, useDisclosure } from '@chakra-ui/react';
import React, { useEffect } from 'react';
import { Dashboard } from '@xpertsea/module-farm-sdk';
import { keyBy } from 'lodash';
import { User } from '@xpertsea/module-user-sdk';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { useDeleteDashboardApi } from '@screens/pond/apis/use-delete-dashboard-api';
import { MdOutlineDeleteOutline } from 'react-icons/md';
import { BaseButton } from '@components/base/base-button';
import { ArrowDownIcon } from '@screens/group-summary/icons/arrow-down-icon';

interface Props {
  dashboards: Dashboard[];
  dashboard: Dashboard;
  refetchDashboards: () => void;
  onChangeLayout: (id: Dashboard) => void;
  users: User[];
}

export function SandboxDashboardSelector(props: Props) {
  const { dashboard, dashboards } = props;
  const { open, onClose, onOpen } = useDisclosure();

  if (!dashboards?.length) return <Text> No data </Text>;

  return (
    <Menu.Root open={open} onOpenChange={(val) => (val.open ? onOpen() : onClose())}>
      <Menu.Trigger>
        <BaseButton variant='ghost' size='sm' _hover={{ bg: 'graph.brandBlue.weakShade1' }}>
          {dashboard.title} <ArrowDownIcon size='md' />
        </BaseButton>
      </Menu.Trigger>

      <Menu.Positioner>
        <Menu.Content minWidth='450px' p='md' rounded='2xl' shadow='elevation.400'>
          <TheList {...props} onClose={onClose} />
        </Menu.Content>
      </Menu.Positioner>
    </Menu.Root>
  );
}

function TheList(props: Props & { onClose: () => void }) {
  const { dashboards, onChangeLayout, refetchDashboards, users, onClose } = props;
  const { trans } = getTrans();
  const { _id: farmId } = useAppSelector((state) => state.farm.currentFarm) ?? {};
  const [_, deleteDashboard] = useDeleteDashboardApi();
  const { _id: userId } = useAppSelector((state) => state.auth?.user) ?? {};
  const usersById = keyBy(users, '_id');

  const { kampiDashboards, userDashboards, groupDashboards } = dashboards.reduce(
    (acc, ele) => {
      const isKampi = ele.createdBy === 'kampi';
      const isSameUser = userId === ele.createdBy;

      if (isKampi) {
        acc.kampiDashboards.push(ele);
      } else if (isSameUser) {
        acc.userDashboards.push(ele);
      } else {
        acc.groupDashboards.push(ele);
      }
      return acc;
    },
    { kampiDashboards: [] as Dashboard[], userDashboards: [] as Dashboard[], groupDashboards: [] as Dashboard[] }
  );

  useEffect(() => {
    refetchDashboards();
  }, [farmId]);

  const onDelete = (id: string) => {
    deleteDashboard({
      params: { filter: { id } },
      successCallback: refetchDashboards
    });
  };

  const handleDeleteClick = (id: string) => {
    const confirmed = window.confirm(trans('t_proceed_confirmation_msg'));
    if (confirmed) {
      onDelete(id);
    }
  };

  const handleOnChange = (val: Dashboard) => {
    onChangeLayout(val);
    onClose();
  };

  return (
    <SimpleGrid columns={2} gap='md'>
      <Box>
        <TitleGroup title={trans('t_kampi')} />

        {kampiDashboards.map((ele) => {
          return (
            <DashboardLink key={ele._id} onClick={() => handleOnChange(ele)}>
              <Text textStyle='label.200'>{ele.title}</Text>
            </DashboardLink>
          );
        })}
      </Box>
      <Box>
        <TitleGroup title={trans('t_my_dashboards')} />

        {userDashboards.map((ele) => {
          return (
            <DashboardLink key={ele._id} onClick={() => handleOnChange(ele)}>
              <Flex justify='space-between' align='center'>
                <Text textStyle='label.200'>{ele.title}</Text>
                <IconButton
                  size='xs'
                  variant='ghost'
                  h='auto'
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteClick(ele._id);
                  }}
                >
                  <MdOutlineDeleteOutline />
                </IconButton>
              </Flex>
            </DashboardLink>
          );
        })}
      </Box>
      {!!groupDashboards?.length && (
        <Box>
          <TitleGroup title={'Group Dashboards'} />
          {groupDashboards.map((ele) => {
            const user = usersById[ele.createdBy];
            return (
              <DashboardLink key={ele._id} onClick={() => handleOnChange(ele)}>
                <Text textStyle='label.200'>{ele.title}</Text>
                {user?.firstName && (
                  <Text textStyle='label.400' color='gray.400'>
                    {trans('t_created_by')}: {user?.firstName}
                  </Text>
                )}
              </DashboardLink>
            );
          })}
        </Box>
      )}
    </SimpleGrid>
  );
}

function TitleGroup({ title }: { title: string }) {
  return (
    <Text
      textStyle='label.200'
      mb='xs'
      color='text.gray.disabled'
      borderBottom='1px solid'
      pb='xs'
      borderColor='gray.200'
    >
      {title}
    </Text>
  );
}

function DashboardLink(props: FlexProps) {
  return (
    <Flex
      flexDir='column'
      justify='center'
      px='xs'
      minH='40px'
      _hover={{ bg: 'bg.brandBlue.weakShade1' }}
      rounded='xl'
      cursor='pointer'
      {...props}
    />
  );
}
