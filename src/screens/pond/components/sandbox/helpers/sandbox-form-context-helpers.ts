import { Dashboard, FarmDailyParameters } from '@xpertsea/module-farm-sdk';
import { ProductionChartVariableOption } from '@screens/pond/components/pond-overview/hooks/use-production-chart-variables';

type ChartConfigType = {
  title: string;
  variables: ProductionChartVariableOption[];
  dailyParametersVariables: FarmDailyParameters[];
  showHarvest?: boolean;
  showPartialHarvest?: boolean;
  showDailyParameters?: boolean;
};

export type SandboxFormValues = {
  title: string;
  isSharing: boolean;
  layoutId: string;
  chartsConfig: ChartConfigType[];
  config?: {
    isEditing: boolean;
  };
};

export function getSandboxFormDefaultValues(layout: Dashboard): SandboxFormValues {
  const { isShared = false, metadata, blocks = [], title } = layout ?? {};
  const { layoutPresetId = '4_1' } = metadata ?? {};

  const chartsConfig: ChartConfigType[] = [
    {
      variables: ['averageWeight', 'cumulativeFcr'],
      dailyParametersVariables: [],
      title: 'Chart 1',
      showHarvest: true,
      showPartialHarvest: true
    },
    {
      variables: ['adjustedFcr', 'animalsRemainingHa'],
      dailyParametersVariables: [],
      title: 'Chart 2',
      showHarvest: true,
      showPartialHarvest: true
    },
    {
      variables: ['feedCostPerKg', 'biomassLbs'],
      dailyParametersVariables: [],
      title: 'Chart 3',
      showHarvest: true,
      showPartialHarvest: true
    },
    {
      variables: ['averageWeight', 'cumulativeFcr'],
      dailyParametersVariables: [],
      title: 'Chart 4',
      showHarvest: true,
      showPartialHarvest: true
    },
    {
      variables: ['averageWeight', 'cumulativeFcr'],
      dailyParametersVariables: [],
      title: 'Chart 5',
      showHarvest: true,
      showPartialHarvest: true
    },
    {
      variables: ['averageWeight', 'cumulativeFcr'],
      dailyParametersVariables: [],
      title: 'Chart 6',
      showHarvest: true,
      showPartialHarvest: true
    }
  ];

  blocks.forEach((block, idx) => {
    const title = block.title;
    const {
      variables,
      showHarvest = false,
      showPartialHarvest = false,
      showDailyParameters = false,
      dailyParametersVariables
    } = block?.config ?? {};
    if (variables?.length) {
      chartsConfig[idx].variables = variables;
    }
    if (dailyParametersVariables?.length) {
      chartsConfig[idx].dailyParametersVariables = dailyParametersVariables;
    }
    if (title) chartsConfig[idx].title = title;
    chartsConfig[idx].showHarvest = showHarvest;
    chartsConfig[idx].showPartialHarvest = showPartialHarvest;
    chartsConfig[idx].showDailyParameters = showDailyParameters;
  });

  return {
    title,
    isSharing: isShared,
    layoutId: layoutPresetId,
    chartsConfig
  };
}
