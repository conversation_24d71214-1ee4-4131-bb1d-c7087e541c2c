import { useAppSelector } from '@redux/hooks';
import { getDaysDiffBetweenDates } from '@screens/farm/helpers/farm-dates';
import { FarmDailyParameters, PopulationDailyParameters } from '@xpertsea/module-farm-sdk';
import { isNumber } from 'lodash';
import { DateTime } from 'luxon';

type GenerateSeriesArgs = {
  dailyParameters: PopulationDailyParameters[];
  selectedParam: FarmDailyParameters;
  stockedAt: string;
  stockedAtTimezone: string;
  timezone: string;
};

function generateSeries(args: GenerateSeriesArgs) {
  const { dailyParameters, selectedParam, stockedAt, stockedAtTimezone, timezone } = args;
  if (!dailyParameters?.length || !selectedParam) return { series: [], maxValue: 0, minValue: 0 };

  let maxValue: number = null;
  let minValue: number = null;
  const series = dailyParameters.map(({ date, params }) => {
    const selectedDailyParam = params.find((param) => param.paramId === selectedParam._id);

    const stockedAtLuxon = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone ?? timezone }).startOf('day');
    const dateLuxon = DateTime.fromISO(date, { zone: timezone }).startOf('day');
    const cycleDays = getDaysDiffBetweenDates({
      baseDate: dateLuxon.toFormat('yyyy-MM-dd'),
      dateToCompare: stockedAtLuxon.toFormat('yyyy-MM-dd')
    });

    const yValue = selectedDailyParam?.value ?? null;
    if (isNumber(yValue)) {
      maxValue = maxValue === null ? yValue : Math.max(maxValue, yValue);
      minValue = minValue === null ? yValue : Math.min(minValue, yValue);
    }

    return {
      x: cycleDays,
      y: yValue,
      custom: {
        dateLuxon,
        name: selectedDailyParam ? selectedParam.name : '',
        isDailyParam: true
      }
    };
  });
  return { series, maxValue: maxValue ?? 0, minValue: minValue ?? 0 };
}

export type dailyParamSeriesData = {
  series: {
    x: number;
    y: number;
    custom: {
      dateLuxon: DateTime;
      name: string;
      isDailyParam: boolean;
    };
  }[];
  maxValue: number;
  minValue: number;
};

export const useGetSandboxDailyParametersData = () => {
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);
  const currentPopulation = useAppSelector((state) => state.farm.currentPopulation);
  const { timezone } = currentFarm ?? {};
  const { stockedAt, stockedAtTimezone, dailyParameters: populationDailyParameters } = currentPopulation ?? {};

  const sortedDailyParameters = [...populationDailyParameters].sort((a, b) => {
    return DateTime.fromISO(a.date).toMillis() - DateTime.fromISO(b.date).toMillis();
  });

  const getDailyParamSeriesData = (dailyParameterVariable: FarmDailyParameters): dailyParamSeriesData => {
    const dailyParamFirstSeries = generateSeries({
      dailyParameters: sortedDailyParameters,
      selectedParam: dailyParameterVariable,
      stockedAt,
      stockedAtTimezone,
      timezone
    });

    return dailyParamFirstSeries;
  };

  return { getDailyParamSeriesData };
};
