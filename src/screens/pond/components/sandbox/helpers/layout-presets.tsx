import { <PERSON>actNode } from 'react';
import { OneChartIcon } from '@icons/sandbox-layouts/one-chart-icon';
import { TwoChartsSideBySideIcon } from '@icons/sandbox-layouts/two-charts-side-by-side-icon';
import { TwoChartsStackedIcon } from '@icons/sandbox-layouts/two-charts-stacked-icon';
import { ThreeChartsSideBySideIcon } from '@icons/sandbox-layouts/three-charts-side-by-side-icon';
import { ThreeChartsStackedIcon } from '@icons/sandbox-layouts/three-charts-stacked-icon';
import { ThreeChartsFullRightIcon } from '@icons/sandbox-layouts/three-charts-full-right-icon';
import { ThreeChartsFullLeftIcon } from '@icons/sandbox-layouts/three-chart-full-left-icon';
import { FourChartsGridIcon } from '@icons/sandbox-layouts/four-charts-grid-icon';
import { FourChartsSideBySideIcon } from '@icons/sandbox-layouts/four-charts-side-by-side-icon';
import { FourChartsStackedIcon } from '@icons/sandbox-layouts/four-charts-stacked-icon';
import { SixChartsStackedIcon } from '@icons/sandbox-layouts/six-charts-side-by-side';
import { SixChartsGridHorizontalIcon } from '@icons/sandbox-layouts/six-charts-grid-horizontal-icon';
import { SixChartsGridVerticalIcon } from '@icons/sandbox-layouts/six-charts-grid-vertical-icon';
import { Dashboard } from '@xpertsea/module-farm-sdk';

export type SandboxLayoutPreset = {
  id: string;
  name: string;
  // This will be for grid-template-areas
  areas: string[];
  rows: string;
  cols: string;
  placeholders: number;
  icon: ReactNode;
};

export const sandBoxLayoutPresets: SandboxLayoutPreset[] = [
  {
    id: '1_1',
    name: '1 Chart – Full',
    areas: ['a'],
    rows: '1fr',
    cols: '1fr',
    placeholders: 1,
    icon: <OneChartIcon w='16px' h='16px' />
  },
  {
    id: '2_1',
    name: '2 Charts – Side by Side',
    areas: ['a b'],
    rows: '1fr',
    cols: '1fr 1fr',
    placeholders: 2,
    icon: <TwoChartsSideBySideIcon w='16px' h='16px' />
  },
  {
    id: '2_2',
    name: '2 Charts – Stacked',
    placeholders: 2,
    areas: ['a', 'b'],
    rows: '1fr 1fr',
    cols: '1fr',
    icon: <TwoChartsStackedIcon w='16px' h='16px' />
  },
  {
    id: '3-1',
    name: '3 Charts – Side by Side',
    areas: ['a b c'],
    rows: '1fr',
    cols: '1fr 1fr 1fr',
    placeholders: 3,
    icon: <ThreeChartsSideBySideIcon w='16px' h='16px' />
  },
  {
    id: '3_2',
    name: '3 Charts – Stacked',
    areas: ['a', 'b', 'c'],
    rows: '1fr 1fr 1fr',
    cols: '1fr',
    placeholders: 3,
    icon: <ThreeChartsStackedIcon w='16px' h='16px' />
  },
  {
    id: '3_3',
    name: 'Two Charts - Full Left',
    areas: ['a b', 'a c'],
    rows: '1fr 1fr',
    cols: '1fr 1fr',
    placeholders: 3,
    icon: <ThreeChartsFullLeftIcon w='16px' h='16px' />
  },
  {
    id: '3_4',
    name: 'Two Charts - Full right',
    rows: '1fr 1fr',
    cols: '1fr 1fr',
    areas: ['a c', 'b c'],
    placeholders: 3,
    icon: <ThreeChartsFullRightIcon w='16px' h='16px' />
  },
  {
    id: '4_1',
    name: 'Four Charts – Grid',
    areas: ['a b', 'c d'],
    rows: '1fr 1fr',
    cols: '1fr 1fr',
    placeholders: 4,
    icon: <FourChartsGridIcon w='16px' h='16px' />
  },
  {
    id: '4_2',
    name: 'Four Charts – Side by Side',
    areas: ['a b c d'],
    rows: '1fr',
    cols: '1fr 1fr 1fr 1fr',
    placeholders: 4,
    icon: <FourChartsSideBySideIcon w='16px' h='16px' />
  },
  {
    id: '4_3',
    name: 'Four Charts – Stacked',
    areas: ['a', 'b', 'c', 'd'],
    rows: '1fr 1fr 1fr 1fr',
    cols: '1fr',
    placeholders: 4,
    icon: <FourChartsStackedIcon w='16px' h='16px' />
  },
  {
    id: '6_1',
    name: 'Six Charts – Grid',
    areas: ['a b d', 'e f g'],
    rows: '1fr 1fr 1fr',
    cols: '1fr 1fr 1fr',
    placeholders: 6,
    icon: <SixChartsGridHorizontalIcon w='16px' h='16px' />
  },
  {
    id: '6_2',
    name: 'Six Charts – Stacked',
    areas: ['a', 'b', 'c', 'd', 'e', 'f'],
    rows: '1fr 1fr 1fr 1fr 1fr 1fr',
    cols: '1fr',
    placeholders: 6,
    icon: <SixChartsStackedIcon style={{ transform: 'rotate(90deg)' }} w='16px' h='16px' />
  },
  {
    id: '6_3',
    name: 'Six Charts – Two per row',
    areas: ['a b', 'c d', 'e g'],
    rows: '1fr 1fr',
    cols: '1fr 1fr',
    placeholders: 6,
    icon: <SixChartsGridVerticalIcon w='16px' h='16px' />
  }
];

export const defaultKampiPreset: Dashboard = {
  __typename: 'Dashboard',
  _id: '668b2f8e9a6eae1bcd56f10c',
  farmId: '6650deed36ac251f6fe42c40',
  isShared: true,
  createdBy: 'kampi',
  title: 'Kampi',
  blocks: [
    {
      __typename: 'DashboardBlocks',
      _id: '6864c880947296368fdd8d4f',
      config: {
        variables: ['averageWeight', 'cumulativeFcr'],
        title: 'Kampi 1',
        showHarvest: true,
        showPartialHarvest: true
      },
      createdAt: '2025-07-02T05:49:52.126Z',
      title: 'Kampi 1'
    },
    {
      __typename: 'DashboardBlocks',
      _id: '6864c880947296368fdd8d50',
      config: {
        variables: ['growthLinear', 'animalsRemainingHa'],
        title: 'Kampi 2',
        showHarvest: true,
        showPartialHarvest: true
      },
      createdAt: '2025-07-02T05:49:52.126Z',
      title: 'Kampi 2'
    },
    {
      __typename: 'DashboardBlocks',
      _id: '6864c880947296368fdd8d51',
      config: {
        variables: ['feedCostPerKg', 'biomassLbs'],
        title: 'Kampi 3',
        showHarvest: true,
        showPartialHarvest: true
      },
      createdAt: '2025-07-02T05:49:52.126Z',
      title: 'Kampi 3'
    },
    {
      __typename: 'DashboardBlocks',
      _id: '6864c880947296368fdd8d52',
      config: {
        variables: ['averageWeight', 'cumulativeFcr'],
        title: 'Kampi 4',
        showHarvest: true,
        showPartialHarvest: true
      },
      createdAt: '2025-07-02T05:49:52.126Z',
      title: 'Kampi 4'
    }
  ],
  metadata: { layoutPresetId: '4_1' }
};
