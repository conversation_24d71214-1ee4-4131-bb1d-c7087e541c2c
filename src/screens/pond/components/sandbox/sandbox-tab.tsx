import { Box } from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { Dashboard } from '@xpertsea/module-farm-sdk';
import { FormProvider, useForm } from 'react-hook-form';
import { getSandboxFormDefaultValues, SandboxFormValues } from './helpers/sandbox-form-context-helpers';
import { useListDashboardsApi } from '@screens/pond/apis/use-list-dashboards-api';
import { useAppSelector } from '@redux/hooks';
import { useRouter } from 'next/router';
import { goToUrl } from '@utils/url';
import { PartialPageLoader } from '@components/loaders/partial-page-loader';
import { SandboxLayouts } from '@screens/pond/components/sandbox/sandbox-layout';
import { getPondState } from '@screens/pond/helpers/get-pond-state';
import { EmptyState } from '@components/errors/empty-state';
import { defaultKampiPreset } from './helpers/layout-presets';
import { CreateEditDashboardPanel } from './create-edit-dashboard-panel';

export function SandboxTab() {
  const population = useAppSelector((state) => state.farm?.currentPopulation);

  const { canHarvest, hasMonitoring } = getPondState({ population });

  if (canHarvest && !hasMonitoring) {
    return <EmptyState h='400px' bg='white' />;
  }

  return <SandboxLogic />;
}

export function SandboxLogic() {
  const { query, route } = useRouter() as { query: { sandboxId?: string }; route: string };
  const { sandboxId } = query;

  const { _id: farmId } = useAppSelector((state) => state.farm.currentFarm) ?? {};

  const [currentLayout, setCurrentLayout] = useState<Dashboard>(defaultKampiPreset);
  const [{ isLoading, isFinishedOnce, data }, listDashboards] = useListDashboardsApi();
  const dashboardsList = [...(data?.dashboards ?? []), defaultKampiPreset];

  const fetchListDashboard = () => {
    listDashboards({
      params: { filter: { farmId: [farmId] } }
    });
  };

  useEffect(() => {
    fetchListDashboard();
  }, [farmId]);

  useEffect(() => {
    if (!isFinishedOnce || !dashboardsList?.length) return;

    const layout = dashboardsList.find((ele) => ele._id === sandboxId);
    if (layout) {
      setCurrentLayout(layout);
    }
  }, [sandboxId, isFinishedOnce, dashboardsList, currentLayout?._id]);

  const onChangeLayout = (val: Dashboard) => {
    goToUrl({ route, params: { ...query, sandboxId: val._id } });
  };

  if (isLoading && !isFinishedOnce) return <PartialPageLoader />;

  return (
    <SandboxComponent
      refetchLayouts={fetchListDashboard}
      dashboards={dashboardsList}
      layout={currentLayout}
      onChangeLayout={onChangeLayout}
    />
  );
}

type SandboxComponentProps = {
  dashboards: Dashboard[];
  refetchLayouts: () => void;
  layout: Dashboard;
  onChangeLayout: (val: Dashboard) => void;
};

function SandboxComponent(props: SandboxComponentProps) {
  const { layout, dashboards, refetchLayouts, onChangeLayout } = props;

  const formMethods = useForm<SandboxFormValues>({
    values: getSandboxFormDefaultValues(layout)
  });

  return (
    <FormProvider {...formMethods}>
      <Box bg='white' p='md' rounded='xl' w='100%' h='100%' overflowY='auto'>
        <Box mb='md'>
          <CreateEditDashboardPanel
            onSuccess={onChangeLayout}
            refetchLayouts={refetchLayouts}
            dashboard={layout}
            dashboards={dashboards}
          />
        </Box>

        <SandboxLayouts />
      </Box>
    </FormProvider>
  );
}
