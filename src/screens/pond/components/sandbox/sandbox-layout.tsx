import { Box, Flex, Input, Switch, Text } from '@chakra-ui/react';
import { useFormContext } from 'react-hook-form';
import { MenuButton, MenuContent, MenuRoot } from '@components/ui/menu';
import { MoreHoriz } from '@icons/more-horiz/more-horiz';
import { getTrans } from '@i18n/get-trans';
import { SandboxFormValues } from '@screens/pond/components/sandbox/helpers/sandbox-form-context-helpers';
import { ProductionChartVariableOption } from '@screens/pond/components/pond-overview/hooks/use-production-chart-variables';
import { sandBoxLayoutPresets } from '@screens/pond/components/sandbox/helpers/layout-presets';
import { SandboxChart } from '@screens/pond/components/sandbox/sandbox-chart';
import { getSandboxChartsLength } from './helpers/get-sanbox-charts-length';
import { useAppSelector } from '@redux/hooks';
import { FarmDailyParameters } from '@xpertsea/module-farm-sdk';
import { SandboxChartVariablesDailyParametersSelectors } from './sandbox-chart-variables-daily-parameters-selectors';

export function SandboxLayouts() {
  const { trans } = getTrans();

  const { currentFarm, currentPopulation } = useAppSelector((state) => state.farm);
  const { watch, setValue } = useFormContext<SandboxFormValues>();
  const layoutId = watch('layoutId');
  const chartsConfig = watch('chartsConfig');
  const config = watch('config');
  const isEditing = config?.isEditing;

  const layout = sandBoxLayoutPresets.find((p) => p?.id === layoutId);

  if (!layout) return null;

  const areaList = Array.from(new Set(layout.areas.flatMap((row) => row.split(' '))));

  const onVariablesChange = (v: ProductionChartVariableOption[], idx: number) => {
    const newVariables = [...chartsConfig];
    newVariables[idx].variables = v;
    setValue('chartsConfig', newVariables);
  };

  const onDailyParameterVariablesChange = (v: FarmDailyParameters[], idx: number) => {
    const newVariables = [...chartsConfig];
    newVariables[idx].dailyParametersVariables = v;

    setValue('chartsConfig', newVariables);
  };

  const onShowFinalHarvestChange = (v: boolean, idx: number) => {
    const newChartsConfig = [...chartsConfig];
    newChartsConfig[idx].showHarvest = v;
    setValue('chartsConfig', newChartsConfig);
  };

  const onShowPartialHarvestChange = (v: boolean, idx: number) => {
    const newChartsConfig = [...chartsConfig];
    newChartsConfig[idx].showPartialHarvest = v;
    setValue('chartsConfig', newChartsConfig);
  };

  const onShowDailyParametersChange = (v: boolean, idx: number) => {
    const newChartsConfig = [...chartsConfig];
    newChartsConfig[idx].showDailyParameters = v;
    if (!v) newChartsConfig[idx].dailyParametersVariables = [];
    setValue('chartsConfig', newChartsConfig);
  };

  const onTitleChange = (v: string, idx: number) => {
    const newVariables = [...chartsConfig];
    newVariables[idx].title = v;
    setValue('chartsConfig', newVariables);
  };

  const maxX = getSandboxChartsLength({ population: currentPopulation, timezone: currentFarm.timezone });

  return (
    <Box
      display='grid'
      gridTemplateRows={layout.rows}
      gridTemplateColumns={layout.cols}
      gridTemplateAreas={layout.areas.map((row) => `"${row}"`).join(' ')}
      gap='md'
    >
      {areaList.map((area, idx) => {
        return (
          <Box key={`${area}${idx}`} bg='gray.100' p='md' gridArea={area}>
            <Flex justify='space-between' align='center' mb='md'>
              <ChartTitle title={chartsConfig[idx]?.title} onChange={(v) => onTitleChange(v, idx)} />
              <Flex align='center'>
                <SandboxChartVariablesDailyParametersSelectors
                  values={chartsConfig[idx]?.variables}
                  dailyParametersValues={chartsConfig[idx]?.dailyParametersVariables}
                  onChange={(v) => onVariablesChange(v, idx)}
                  onChangeDailyParameters={(v) => onDailyParameterVariablesChange(v, idx)}
                  showDailyParameter={chartsConfig[idx]?.showDailyParameters}
                />

                <MenuRoot>
                  <MenuButton
                    selectVariant='secondary'
                    size='sm'
                    px={0}
                    _hover={{ opacity: '0.8' }}
                    data-cy='more-options-menu-btn'
                    disabled={!isEditing}
                  >
                    <MoreHoriz />
                  </MenuButton>

                  <MenuContent>
                    <Box>
                      <Switch.Root
                        checked={chartsConfig[idx]?.showHarvest}
                        onCheckedChange={(e) => onShowFinalHarvestChange(e.checked, idx)}
                        w='full'
                        _hover={{ bg: 'gray.100' }}
                      >
                        <Flex justifyContent='space-between' align='center' p='xs' w='full'>
                          <Switch.Label>
                            <Text textStyle='label.300'>{trans('t_final_harvest')}</Text>
                          </Switch.Label>
                          <Switch.HiddenInput />
                          <Switch.Control>
                            <Switch.Thumb />
                          </Switch.Control>
                        </Flex>
                      </Switch.Root>
                    </Box>

                    <Box>
                      <Switch.Root
                        checked={chartsConfig[idx]?.showPartialHarvest}
                        onCheckedChange={(e) => onShowPartialHarvestChange(e.checked, idx)}
                        w='full'
                        _hover={{ bg: 'gray.100' }}
                      >
                        <Flex justifyContent='space-between' align='center' p='xs' w='full'>
                          <Switch.Label>
                            <Text textStyle='label.300'>{trans('t_partial_harvest')}</Text>
                          </Switch.Label>
                          <Switch.HiddenInput />
                          <Switch.Control>
                            <Switch.Thumb />
                          </Switch.Control>
                        </Flex>
                      </Switch.Root>
                    </Box>

                    <Box>
                      <Switch.Root
                        checked={chartsConfig[idx]?.showDailyParameters}
                        onCheckedChange={(e) => onShowDailyParametersChange(e.checked, idx)}
                        w='full'
                        _hover={{ bg: 'gray.100' }}
                      >
                        <Flex justifyContent='space-between' align='center' p='xs' w='full'>
                          <Switch.Label>
                            <Text textStyle='label.300'>{trans('t_show_daily_parameters')}</Text>
                          </Switch.Label>
                          <Switch.HiddenInput />
                          <Switch.Control>
                            <Switch.Thumb />
                          </Switch.Control>
                        </Flex>
                      </Switch.Root>
                    </Box>
                  </MenuContent>
                </MenuRoot>
              </Flex>
            </Flex>

            <Flex rounded='lg' minH='200px' align='center' justify='center'>
              <SandboxChart key={layoutId} chartConfig={chartsConfig[idx]} maxX={maxX} />
            </Flex>
          </Box>
        );
      })}
    </Box>
  );
}

function ChartTitle({ title, onChange }: { title: string; onChange: (v: string) => void }) {
  const { watch } = useFormContext<SandboxFormValues>();
  const isEditing = watch('config.isEditing');

  return (
    <Box w='100px'>
      {isEditing ? (
        <Input size='sm' defaultValue={title} onChange={(v) => onChange(v.target.value)} w='100%' />
      ) : (
        <Flex>{title}</Flex>
      )}
    </Box>
  );
}
