import React, { useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { SandboxFormValues } from './helpers/sandbox-form-context-helpers';
import { Box, Flex, Input, Text, Switch } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { groupBy } from 'lodash';
import { sandBoxLayoutPresets } from './helpers/layout-presets';
import { getTrans } from '@i18n/get-trans';
import { useCreateDashboardApi } from '@screens/pond/apis/use-create-dashboard-api';
import { useGetGroupFarms } from './use-get-group-farms';
import { useAppSelector } from '@redux/hooks';
import { Dashboard } from '@xpertsea/module-farm-sdk';
import { useUpdateDashboardApi } from '@screens/pond/apis/use-update-dashboard-api';
import { SandboxDashboardSelector } from './sandbox-dashboard-selector';
import { useListUsersApi } from '@screens/user/hooks/use-list-users-api';

interface Props {
  dashboard: Dashboard;
  onSuccess: (val: Dashboard) => void;
  refetchLayouts: () => void;
  dashboards: Dashboard[];
}

const useGetUsers = () => {
  const { _id: farmId } = useAppSelector((state) => state.farm.currentFarm) ?? {};
  const [{ data: userListRes }, listUsers] = useListUsersApi();

  const users = userListRes?.users ?? [];

  useEffect(() => {
    if (userListRes?.users.length) return;

    listUsers({
      params: {
        page: {
          size: 1000,
          current: 1
        },
        filter: { entityId: [farmId] }
      }
    });
  }, []);

  return users;
};

export function CreateEditDashboardPanel(props: Props) {
  const { dashboard, dashboards, onSuccess, refetchLayouts } = props;
  const { trans } = getTrans();

  const [isOpen, setIsOpen] = useState<'create' | 'edit'>();
  const users = useGetUsers();

  const { _id: userId } = useAppSelector((state) => state.auth?.user) ?? {};

  const { watch, setValue } = useFormContext<SandboxFormValues>();
  const title = watch('title');
  const isSharing = watch('isSharing');

  const onOpen = (type: 'create' | 'edit') => {
    setValue('config', { isEditing: true });
    setIsOpen(type);
  };

  const onClose = () => {
    setValue('config', { isEditing: false });
    setIsOpen(undefined);
  };

  const isSameUser = dashboard.createdBy === userId;

  if (isOpen) {
    return (
      <>
        <Flex gap='lg' borderBottom='1px solid' borderColor='border.gray.weak' pb='lg' mb='lg'>
          <Box>
            <Text mb='sm' textStyle='label.100'>
              {trans('t_dashboard_name')}
            </Text>
            <Input value={title} onChange={(e) => setValue('title', e.target.value)} />
          </Box>

          <LayoutSelector />
        </Flex>

        <Flex justify='space-between'>
          <Box>
            <Switch.Root checked={isSharing} onCheckedChange={(e) => setValue('isSharing', e.checked)}>
              <Switch.HiddenInput />
              <Switch.Control>
                <Switch.Thumb />
              </Switch.Control>
              <Switch.Label>{trans('t_sharing')}</Switch.Label>
            </Switch.Root>
          </Box>
          <FormActionBtns
            onClose={onClose}
            formType={isOpen}
            onSuccess={onSuccess}
            refetchLayouts={refetchLayouts}
            dashboard={dashboard}
          />
        </Flex>
      </>
    );
  }

  return (
    <Flex align='center' justify='space-between' gap='md'>
      <SandboxDashboardSelector
        refetchDashboards={refetchLayouts}
        onChangeLayout={onSuccess}
        dashboards={dashboards}
        users={users}
        dashboard={dashboard}
      />
      <Flex align='center' gap='md'>
        {isSameUser && (
          <BaseButton size='sm' variant='outline' onClick={() => onOpen('edit')}>
            {trans('t_edit')}
          </BaseButton>
        )}
        <BaseButton size='sm' variant='outline' onClick={() => onOpen('create')}>
          {trans('t_add_new_dashboard')}
        </BaseButton>
      </Flex>
    </Flex>
  );
}

function LayoutSelector() {
  const { trans } = getTrans();
  const layOutPresetsByPlaceholders = groupBy(sandBoxLayoutPresets, 'placeholders');
  const { watch, setValue } = useFormContext<SandboxFormValues>();
  const layoutId = watch('layoutId');

  const initial = sandBoxLayoutPresets.find((ele) => ele.id === layoutId);
  const [selectedLayout, setSelectedLayout] = useState(initial.placeholders ?? 1);

  const layouts = layOutPresetsByPlaceholders[selectedLayout];

  const handleSelect = (id: string) => {
    setValue('layoutId', id);
  };

  return (
    <>
      <Box>
        <Text mb='sm' textStyle='label.100'>
          {trans('t_number_of_charts')}
        </Text>

        <Flex gap='xs'>
          {Object.keys(layOutPresetsByPlaceholders).map((ele) => (
            <BaseButton
              key={ele}
              size='xs'
              variant={selectedLayout === +ele ? 'primary' : 'outline'}
              onClick={() => setSelectedLayout(+ele)}
            >
              {ele}
            </BaseButton>
          ))}
        </Flex>
      </Box>

      <Box>
        <Text mb='sm' textStyle='label.100'>
          {trans('t_select_layout')}
        </Text>
        <Box>
          {layouts?.map((ele) => (
            <BaseButton
              key={ele.id}
              size='sm'
              variant={layoutId === ele.id ? 'secondary' : 'ghost'}
              disabled={layoutId === ele.id}
              onClick={() => handleSelect(ele.id)}
            >
              {ele.icon}
            </BaseButton>
          ))}
        </Box>
      </Box>
    </>
  );
}

function FormActionBtns(
  props: Pick<Props, 'onSuccess' | 'refetchLayouts' | 'dashboard'> & {
    onClose: () => void;
    formType: 'create' | 'edit';
  }
) {
  const { onSuccess, refetchLayouts, dashboard, formType, onClose } = props;
  const { trans } = getTrans();

  const { _id: farmId } = useAppSelector((state) => state.farm.currentFarm) ?? {};
  const { groupFarmsIds } = useGetGroupFarms();

  const [{ isLoading: isCreating }, createDashboard] = useCreateDashboardApi();
  const [{ isLoading: isUpdating }, updateDashboard] = useUpdateDashboardApi();

  const { getValues, reset } = useFormContext<SandboxFormValues>();

  const onCancel = () => {
    onClose();
    reset();
  };

  const handleOnSave = () => {
    const title = getValues('title');
    const isShared = getValues('isSharing');
    const layoutId = getValues('layoutId');
    const chartsConfig = getValues('chartsConfig');

    const blocks = chartsConfig.map((ele, i) => ({
      title: ele.title || `Block ${i}`,
      config: ele
    }));

    const sharedWith = isShared ? groupFarmsIds : [];

    const sharedObject = { title, isShared, blocks, sharedWith, metadata: { layoutPresetId: layoutId } };

    if (formType === 'create') {
      createDashboard({
        params: { record: { farmId, ...sharedObject } },
        successCallback: (val) => {
          onSuccess(val.dashboard);
          refetchLayouts();
          onClose();
        }
      });
    } else {
      updateDashboard({
        params: {
          filter: { id: dashboard._id },
          set: sharedObject
        },
        successCallback: () => {
          refetchLayouts();
          onClose();
        }
      });
    }
  };

  return (
    <Flex gap='md'>
      <BaseButton onClick={onCancel} variant='outline' size='sm'>
        {trans('t_cancel')}
      </BaseButton>
      <BaseButton size='sm' onClick={handleOnSave} loading={isCreating || isUpdating}>
        {trans('t_save_dashboard')}
      </BaseButton>
    </Flex>
  );
}
