import { Flex } from '@chakra-ui/react';
import { ChartVarsList } from '../pond-overview/helpers/pond-production-chart-helpers';
import {
  ProductionChartVariableOption,
  useProductionChartVariables
} from '../pond-overview/hooks/use-production-chart-variables';
import { ProductionChartVariableSelect } from '../pond-overview/production-chart-variable-select';
import { FarmDailyParameters } from '@xpertsea/module-farm-sdk';
import { useGetProductionChartDailyParams } from '../pond-overview/hooks/use-get-production-chart-daily-params';
import { ProductionChartDailyParamSelect } from '../pond-overview/production-chart-daily-param-select';
import { useFormContext } from 'react-hook-form';
import { SandboxFormValues } from './helpers/sandbox-form-context-helpers';

interface Props {
  showDailyParameter: boolean;
  values: ProductionChartVariableOption[];
  dailyParametersValues: FarmDailyParameters[];
  onChange: (variables: ProductionChartVariableOption[]) => void;
  onChangeDailyParameters: (variables: FarmDailyParameters[]) => void;
}

export function SandboxChartVariablesDailyParametersSelectors(props: Props) {
  const { values, dailyParametersValues, showDailyParameter, onChange, onChangeDailyParameters } = props;

  const { watch } = useFormContext<SandboxFormValues>();
  const isEditing = watch('config.isEditing');

  const { chartVariableMap } = useProductionChartVariables();
  const { activeDailyParameters } = useGetProductionChartDailyParams({
    defaultValue: {
      variableOne: dailyParametersValues[0],
      variableTwo: dailyParametersValues[1]
    }
  });

  const variableOneLabel = chartVariableMap[values[0]]?.title;
  const variableTwoLabel = chartVariableMap[values[1]]?.title;

  const allChartVars: ChartVarsList = [
    { name: 'variableOne', label: variableOneLabel, value: values[0] },
    { name: 'variableTwo', label: variableTwoLabel, value: values[1] },
    { name: 'dailyParamVariableOne', label: dailyParametersValues[0]?.name, value: dailyParametersValues?.[0]?._id },
    { name: 'dailyParamVariableTwo', label: dailyParametersValues[1]?.name, value: dailyParametersValues?.[1]?._id }
  ];
  const chartSelectedVars: ChartVarsList = allChartVars.filter((variable) => variable.value);

  const handleVariableChange = (
    variable: 'chartVariableOne' | 'chartVariableTwo',
    value: ProductionChartVariableOption
  ) => {
    const newVariables = variable === 'chartVariableTwo' ? [values[0], value] : [value, values[1]];
    onChange(newVariables);
  };

  const handleDailyParametersVariableChange = (variable: 'one' | 'two', value: FarmDailyParameters) => {
    const newVariables = variable === 'two' ? [dailyParametersValues[0], value] : [value, dailyParametersValues[1]];
    onChangeDailyParameters(newVariables);
  };

  return (
    <Flex>
      <ProductionChartVariableSelect
        chartVariableOne={values[0]}
        chartVariableTwo={values[1]}
        chartVariableMap={chartVariableMap}
        chartSelectedVars={chartSelectedVars}
        onVariableChange={handleVariableChange}
        isDisabled={!isEditing}
      />
      {showDailyParameter && (
        <ProductionChartDailyParamSelect
          onVariableChange={handleDailyParametersVariableChange}
          chartDailyParamVariableOne={dailyParametersValues[0]}
          chartDailyParamVariableTwo={dailyParametersValues[1]}
          activeDailyParameters={activeDailyParameters}
          chartSelectedVars={chartSelectedVars}
          isDisabled={!isEditing}
        />
      )}
    </Flex>
  );
}
