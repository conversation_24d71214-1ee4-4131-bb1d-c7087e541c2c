import { HighchartsNextComp, HighchartsRef } from '@components/base/highcharts-next-comp';
import React, { useMemo, useRef } from 'react';
import { useAppSelector } from '@redux/hooks';
import { DateTime } from 'luxon';
import { getTrans } from '@i18n/get-trans';
import { Options, XAxisPlotLinesOptions } from 'highcharts';
import { PopulationType } from '@redux/farm/set-current-population';
import { getDaysDiffBetweenDates } from '@screens/farm/helpers/farm-dates';
import { SandboxFormValues } from '@screens/pond/components/sandbox/helpers/sandbox-form-context-helpers';
import {
  GetSandboxChartDataFnReturnType,
  useGetSandboxChartData
} from '@screens/pond/components/sandbox/helpers/use-get-sandbox-chart-data';
import { getMoonHtml } from '@screens/pond/components/pond-overview/helpers/highcharts';
import { renderToString } from 'react-dom/server';
import { formatNumber } from '@utils/number';
import {
  dailyParamSeriesData,
  useGetSandboxDailyParametersData
} from './helpers/use-get-sandbox-daily-parameters-data';
import { FarmDailyParameters } from '@xpertsea/module-farm-sdk';

interface Props {
  chartConfig: SandboxFormValues['chartsConfig'][0];
  maxX: number;
}

export function SandboxChart(props: Props) {
  const { chartConfig, maxX } = props;
  const population = useAppSelector((state) => state.farm.currentPopulation);

  const { getData } = useGetSandboxChartData(population);

  const { variables, showHarvest, showPartialHarvest } = chartConfig ?? {};
  const variableOneData = getData(variables?.[0]);
  const variableTwoData = getData(variables?.[1]);

  const { getDailyParamSeriesData } = useGetSandboxDailyParametersData();

  const dailyParameterVariableOneData = getDailyParamSeriesData(chartConfig?.dailyParametersVariables?.[0]);
  const dailyParameterVariableTwoData = getDailyParamSeriesData(chartConfig?.dailyParametersVariables?.[1]);

  return (
    <TheChart
      variableOneData={variableOneData}
      variableTwoData={variableTwoData}
      dailyParameterOneData={{
        chartData: dailyParameterVariableOneData,
        paramsData: chartConfig?.dailyParametersVariables?.[0]
      }}
      dailyParameterTwoData={{
        chartData: dailyParameterVariableTwoData,
        paramsData: chartConfig?.dailyParametersVariables?.[1]
      }}
      showHarvest={showHarvest || false}
      showPartialHarvest={showPartialHarvest || false}
      maxX={maxX}
    />
  );
}

const getHarvestInfo = (population: PopulationType, showHarvest: boolean, showPartialHarvest: boolean) => {
  if (!showHarvest && !showPartialHarvest) return [];
  const { harvest, harvestPlan, stockedAt, partialHarvest, partialHarvestPlanned } = population;
  const actualHarvestDate = harvest?.date;
  const { harvestDate: harvestPlanDate } = harvestPlan ?? {};

  const finalHarvestDate = actualHarvestDate || harvestPlanDate;
  const finalHarvestDateLuxon = DateTime.fromISO(finalHarvestDate);

  const stockedAtLuxon = DateTime.fromISO(stockedAt);
  let finalHarvestX;
  if (finalHarvestDate) {
    finalHarvestX = finalHarvestDateLuxon.diff(stockedAtLuxon, ['days', 'hours']).days;
  }

  const partialHarvestX: number[] = [];

  if (showPartialHarvest) {
    [partialHarvest, partialHarvestPlanned].forEach((hItem) => {
      hItem
        ?.filter((e) => e?.date)
        .forEach((item) => {
          const cycleDays = getDaysDiffBetweenDates({
            baseDate: item.date,
            dateToCompare: stockedAtLuxon.toFormat('yyyy-MM-dd')
          });

          partialHarvestX.push(cycleDays);
        });
    });
  }

  const finalHarvestPlotLine: XAxisPlotLinesOptions[] =
    showHarvest && finalHarvestX
      ? [
          {
            width: 1,
            value: finalHarvestX,
            dashStyle: 'Solid',
            label: {
              text: 'Harvest',
              rotation: 0,
              verticalAlign: 'top',
              align: 'center',
              style: { fontSize: '10', color: '#aaa' },
              y: -10
            }
          }
        ]
      : [];

  const partialHarvestPlotLines: XAxisPlotLinesOptions[] = partialHarvestX?.length
    ? partialHarvestX.map((ele) => ({
        width: 1,
        value: ele,
        dashStyle: 'Solid',
        label: {
          text: 'Partial Harvest',
          rotation: 0,
          verticalAlign: 'top',
          align: 'center',
          style: { fontSize: '10', color: '#aaa' },

          y: -10
        }
      }))
    : [];

  return [...finalHarvestPlotLine, ...partialHarvestPlotLines];
};

function TheChart({
  variableOneData,
  variableTwoData,
  dailyParameterOneData,
  dailyParameterTwoData,
  showHarvest,
  showPartialHarvest,
  maxX
}: {
  variableOneData: GetSandboxChartDataFnReturnType;
  variableTwoData: GetSandboxChartDataFnReturnType;
  dailyParameterOneData: { chartData: dailyParamSeriesData; paramsData: FarmDailyParameters };
  dailyParameterTwoData: { chartData: dailyParamSeriesData; paramsData: FarmDailyParameters };
  showHarvest: boolean;
  showPartialHarvest: boolean;
  maxX: number;
}) {
  const { trans } = getTrans();

  const timezone = useAppSelector((state) => state.farm?.currentFarm?.timezone);
  const population = useAppSelector((state) => state.farm.currentPopulation);
  const { stockedAt, stockedAtTimezone } = population ?? {};
  const stockedAtLuxon = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone ?? timezone }).startOf('day');

  const highchartsRef = useRef<HighchartsRef>(null);

  const customStyleText = { fontSize: '10px', fontWeight: '600', color: '#464646', lineHeight: '12px' };

  const chartSelectedVars = useMemo(() => {
    const selectedVars = [];

    if (variableOneData?.variableData?.length) selectedVars.push('variableOne');
    if (variableTwoData?.variableData?.length) selectedVars.push('variableTwo');
    if (dailyParameterOneData?.chartData?.series?.length) selectedVars.push('dailyParamVariableOne');
    if (dailyParameterTwoData?.chartData?.series?.length) selectedVars.push('dailyParamVariableTwo');

    return selectedVars;
  }, [variableOneData, variableTwoData, dailyParameterOneData, dailyParameterTwoData]);

  function getSelectedVariableYAxisIndex(variableName: string) {
    const index = chartSelectedVars.findIndex((variable) => variable === variableName);
    if (index > 1) return 1;
    return index !== -1 ? index : 0;
  }

  const options: Options = {
    chart: { type: 'line', marginTop: 20 },
    title: { text: undefined },
    xAxis: {
      min: 0,
      max: maxX,
      tickInterval: 7,
      tickWidth: 0,
      lineWidth: 0,
      plotLines: showHarvest || showPartialHarvest ? getHarvestInfo(population, showHarvest, showPartialHarvest) : [],
      title: { text: trans('t_days_of_culture'), margin: 16, style: customStyleText },
      labels: {
        distance: 10,
        rotation: 0,
        useHTML: true,
        style: { textOverflow: 'visible' },
        formatter(this): string {
          const cycleDays = this.value as number;
          const valueLuxon = stockedAtLuxon.plus({ days: cycleDays }).setZone(timezone);

          const currentDate = DateTime.local({ zone: timezone }).set({
            hour: 0,
            minute: 0,
            second: 0,
            millisecond: 0
          });

          const isPastDate = valueLuxon < currentDate;
          const dateColor = isPastDate ? '#B1B1B1' : '#464646';

          return `
              <div style="display: flex; flex-direction: column; align-items: center; gap: 4px;">
                <p style="font-size: 10px; font-weight: 600; color: ${dateColor}; line-height: 12px;">
                  ${cycleDays}
                </p>
                ${getMoonHtml(valueLuxon.toJSDate())}
              </div>
            `;
        }
      }
    },
    yAxis: [
      {
        zIndex: 1,
        gridLineWidth: 0.5,
        gridLineColor: '#B1B1B1',
        gridLineDashStyle: 'LongDash',
        title: { text: variableOneData?.variableLabel?.title }
      },
      {
        zIndex: 1,
        gridZIndex: 1,
        opposite: true,
        gridLineWidth: 0.5,
        gridLineColor: '#B1B1B1',
        gridLineDashStyle: 'LongDash',
        title: { text: variableTwoData?.variableLabel?.title }
      }
    ],
    tooltip: {
      shared: true,
      useHTML: true,
      formatter: function (this) {
        const { variableLabel: variableOneLabel } = variableOneData ?? {};
        const { variableLabel: variableTwoLabel } = variableTwoData ?? {};
        const { paramsData: dailyParameterOneParamsData } = dailyParameterOneData ?? {};
        const { paramsData: dailyParameterTwoParamsData } = dailyParameterTwoData ?? {};

        const variableOneValue = this.points[getSelectedVariableYAxisIndex('variableOne')]?.y;
        const variableTwoValue = this.points[getSelectedVariableYAxisIndex('variableTwo')]?.y;
        const dailyParamsOneValue = this.points[getSelectedVariableYAxisIndex('dailyParamVariableOne')]?.y;
        const dailyParamsTwoValue = this.points[getSelectedVariableYAxisIndex('dailyParamVariableTwo')]?.y;
        return renderToString(
          <div>
            {variableOneLabel && variableOneValue && (
              <p>
                {`${variableOneLabel?.title}: ${formatNumber(variableOneValue, { fractionDigits: variableOneLabel?.fractionDigits ?? 2 })}`}
              </p>
            )}
            {variableTwoLabel && variableTwoValue && (
              <p>
                {`${variableTwoLabel?.title}: ${formatNumber(variableTwoValue, { fractionDigits: variableTwoLabel?.fractionDigits ?? 2 })}`}
              </p>
            )}
            {!!dailyParameterOneParamsData?.name && dailyParamsOneValue && (
              <p>
                {`${dailyParameterOneParamsData.name}: ${formatNumber(dailyParamsOneValue, { fractionDigits: 2 })}`}
              </p>
            )}
            {!!dailyParameterTwoParamsData?.name && dailyParamsTwoValue && (
              <p>
                {`${dailyParameterTwoParamsData.name}: ${formatNumber(dailyParamsTwoValue, { fractionDigits: 2 })}`}
              </p>
            )}
          </div>
        );
      }
    },
    plotOptions: {
      spline: {
        marker: {
          radius: 4,
          lineColor: '#666666',
          lineWidth: 1
        }
      }
    },
    series: [
      {
        type: 'line',
        name: 'variableOne',
        yAxis: getSelectedVariableYAxisIndex('variableOne'),
        data: variableOneData.variableData,
        zoneAxis: 'x',
        zIndex: 2,
        color: '#FF755B'
      },
      {
        type: 'line',
        name: 'variableTwo',
        yAxis: getSelectedVariableYAxisIndex('variableTwo'),
        data: variableTwoData.variableData,
        zoneAxis: 'x',
        zIndex: 2,
        color: '#576CFF'
      },
      {
        type: 'line',
        dashStyle: 'Dash',
        lineWidth: 1.5,
        name: 'variableOnePrediction',
        yAxis: 0,
        data: variableOneData.variablePredication,
        zoneAxis: 'x',
        zIndex: 2,
        color: '#FF755B',
        marker: { enabled: false }
      },
      {
        type: 'line',
        dashStyle: 'Dash',
        lineWidth: 1.5,
        name: 'variableTwoPrediction',
        yAxis: 1,
        data: variableTwoData.variablePredication,
        zoneAxis: 'x',
        zIndex: 2,
        color: '#576CFF',
        marker: { enabled: false }
      },

      {
        type: 'line',
        name: 'dailyParamVariableOne',
        yAxis: getSelectedVariableYAxisIndex('dailyParamVariableOne'),
        data: dailyParameterOneData?.chartData?.series,
        zoneAxis: 'x',
        zIndex: 2,
        color: '#289A56'
      },
      {
        type: 'line',
        name: 'dailyParamVariableTwo',
        yAxis: getSelectedVariableYAxisIndex('dailyParamVariableTwo'),
        zIndex: 2,
        data: dailyParameterTwoData?.chartData?.series,
        zoneAxis: 'x',
        color: '#C69E00'
      }
    ]
  };

  return (
    <HighchartsNextComp
      ref={highchartsRef}
      options={options}
      containerProps={{ style: { width: '100%', height: `100%` } }}
    />
  );
}
