import { Box, Flex, Text } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import keyBy from 'lodash/keyBy';

export function FarmsGroupListAlert({ farmIds }: { farmIds: string[] }) {
  const { trans } = getTrans();

  const { farmsData } = useAppSelector((state) => state.farm);

  if (!farmIds) return null;

  const farmsDataById = keyBy(farmsData, '_id');
  const groupFarms = farmIds.map((ele) => farmsDataById[ele]);

  if (!groupFarms?.length) return null;

  return (
    <Box px='md' pb='md'>
      <Box
        fontSize='md'
        py='sm-alt'
        px='md-alt'
        border='1px solid'
        borderColor='semanticYellow.600'
        bgColor='semanticYellow.100'
        borderRadius='base'
      >
        <Text mb='xs' fontWeight={500}>
          {trans('t_layout_will_be_shared_across_all_farms')}
        </Text>
        <Flex gap='xs' flexWrap='wrap'>
          {groupFarms?.map((farm, index) => {
            return (
              <Text key={farm.eid}>
                {farm.name}
                {index === groupFarms?.length - 1 ? '' : ','}
              </Text>
            );
          })}
        </Flex>
      </Box>
    </Box>
  );
}
