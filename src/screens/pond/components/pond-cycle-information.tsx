import { Box, BoxProps, Flex, Separator, Text, TextProps } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { convertUnitByDivision, formatNumber, isNumber } from '@utils/number';
import { parsePercent } from '@screens/monitoring/helpers/weight-distribution';
import { ReactNode, useMemo } from 'react';
import { EditPondEquipmentsModal } from '@screens/pond/components/modals/edit-pond-equipments-modal';
import { AddEditPondFeedModal } from '@screens/pond/components/modals/add-edit-pond-feed-modal';
import { EditPondTargetModal } from '@screens/pond/components/modals/edit-pond-target-modal';
import { PopulationType } from '@redux/farm/set-current-population';
import { AddEditPondProjectionModal } from '@screens/pond/components/modals/add-edit-pond-projection-modal';
import { useSetFarmBookRefererInRedux } from '@components/farm-book/hooks/use-set-farm-book-referer-in-redux';
import { DateTime } from 'luxon';
import { actionsName } from '@utils/segment';
import { AdminOrSupervisorWrapper } from '@components/permission-wrappers/admin-or-supervisor-wrapper';
import isUndefined from 'lodash/isUndefined';
import { EditOutlinedIcon } from '@icons/edit/edit-outlined-icon';
import { BaseIconButton } from '@components/base/base-icon-button';
import { AdminOrSupervisorOrOperationWrapper } from '@components/permission-wrappers/admin-or-supervisor-or-operation-wrapper';

export function PondCycleInformation() {
  useSetFarmBookRefererInRedux('cycleInformation');
  const { currentPopulation } = useAppSelector((state) => state.farm);
  const { cycleInformation } = currentPopulation ?? {};

  return (
    <Flex direction='column' gap='md' bgColor='white' borderRadius='2xl' p='md'>
      <TargetsSection cycleInformation={cycleInformation} />
      <Separator borderColor='border.gray.weak' />
      <EquipmentSection cycleInformation={cycleInformation} />
      <Separator borderColor='border.gray.weak' />
      <FeedSection
        feedBrand={cycleInformation?.equipment?.feedBrand}
        farmFeedTableId={cycleInformation?.projection?.projectedFeed?.farmFeedTableId}
      />
      <Separator borderColor='border.gray.weak' />
      <Projection population={currentPopulation} />
    </Flex>
  );
}

function TargetsSection({ cycleInformation }: { cycleInformation: PopulationType['cycleInformation'] }) {
  const { trans } = getTrans();
  const { harvestWeight, cycleLength, fcr, biomassToHarvest, survival } = cycleInformation?.target ?? {};

  const user = useAppSelector((state) => state.auth.user);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};

  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);

  const lang = useAppSelector((state) => state.app.lang);
  const formattedBiomassToHarvest = biomassToHarvest
    ? `${formatNumber(convertUnitByDivision(biomassToHarvest, unitsConfig?.biomass), { lang, fractionDigits: 0 })} ${isBiomassUnitLbs ? trans('t_lb_over_ha') : trans('t_kg_over_ha')}`
    : '-';
  const formattedSurvival = survival ? formatNumber(survival, { lang, fractionDigits: 0, isPercentage: true }) : null;
  const formattedHarvestWeight = harvestWeight ? formatNumber(harvestWeight, { lang, fractionDigits: 1 }) : null;
  const formattedFcr = fcr ? formatNumber(fcr, { lang, fractionDigits: 2 }) : null;
  const formattedCycleLength = cycleLength ? `${cycleLength} ${trans('t_day_s')}` : '-';
  const biomassHaTitle = isBiomassUnitLbs ? trans('t_biomass_lb_ha') : trans('t_biomass_kg_ha');

  return (
    <Box>
      <Flex justify='space-between' align='center' mb='md-alt'>
        <Text size='label100' lineHeight='20px'>
          {trans('t_targets')}
        </Text>
        <EditPondTargetModal>
          <BaseIconButton aria-label='edit-pond-target' analyticsId={actionsName.pondOverviewTargetEditClicked}>
            <EditOutlinedIcon />
          </BaseIconButton>
        </EditPondTargetModal>
      </Flex>

      <Flex flexWrap='wrap' gap='xs-alt'>
        <InfoCell title={trans('t_harvest_weight_g')}>{formattedHarvestWeight}</InfoCell>
        <InfoCell title={trans('t_cycle_length')}>{formattedCycleLength}</InfoCell>
        <InfoCell title={trans('t_fcr')}>{formattedFcr}</InfoCell>
        <InfoCell title={biomassHaTitle}>{formattedBiomassToHarvest}</InfoCell>
        <InfoCell title={trans('t_survival')}>{formattedSurvival}</InfoCell>
      </Flex>
    </Box>
  );
}

function EquipmentSection({ cycleInformation }: { cycleInformation: PopulationType['cycleInformation'] }) {
  const { trans } = getTrans();

  const lang = useAppSelector((state) => state.app.lang);
  const user = useAppSelector((state) => state.auth.user);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};

  const { carryingCapacity, equipment } = cycleInformation ?? {};
  const { numberOfAerators, numberOfAutoFeeders, autoFeederBrand, aeratorType, autoFeederProtocol } = equipment ?? {};
  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);
  const unitLabel = isBiomassUnitLbs ? trans('t_lb_over_ha') : trans('t_kg_over_ha');
  return (
    <Box>
      <Flex justify='space-between' align='center' mb='md-alt'>
        <Text size='label100' lineHeight='20px'>
          {trans('t_pond_setup')}
        </Text>
        <EditPondEquipmentsModal>
          <BaseIconButton aria-label='edit-pond-equipments' analyticsId={actionsName.pondOverviewPondSetupEditClicked}>
            <EditOutlinedIcon />
          </BaseIconButton>
        </EditPondEquipmentsModal>
      </Flex>

      <Flex flexWrap='wrap' gap='xs-alt'>
        <InfoCell title={trans('t_#_of_autofeeders')}>{numberOfAutoFeeders || '-'}</InfoCell>
        <InfoCell title={trans('t_#_of_aerators')}> {numberOfAerators || '-'} </InfoCell>
        <InfoCell title={trans('t_type_of_aerator')}> {aeratorType || '-'} </InfoCell>
        <InfoCell title={trans('t_autofeeder_brand')}>{autoFeederBrand || '-'}</InfoCell>
        <InfoCell valueProps={{ textTransform: 'capitalize' }} title={trans('t_autofeeder_protocol')}>
          {autoFeederProtocol || '-'}
        </InfoCell>

        <InfoCell title={isBiomassUnitLbs ? trans('t_carrying_capacity_lb_ha') : trans('t_carrying_capacity_kg_ha')}>
          {isNumber(carryingCapacity)
            ? `${formatNumber(convertUnitByDivision(carryingCapacity, unitsConfig?.biomass), { lang, fractionDigits: 0 })} ${unitLabel}`
            : '-'}
        </InfoCell>
      </Flex>
    </Box>
  );
}

function FeedSection({ farmFeedTableId, feedBrand }: { farmFeedTableId: string; feedBrand: string }) {
  const { trans } = getTrans();
  const { currentFarm } = useAppSelector((state) => state.farm);

  const feedTableName = currentFarm?.feedTable?.find((table) => table._id === farmFeedTableId);

  return (
    <Box>
      <Flex justify='space-between' align='center' mb='md-alt'>
        <Text size='label100' lineHeight='20px'>
          {trans('t_feed')}
        </Text>
        <AddEditPondFeedModal>
          <BaseIconButton aria-label='edit-pond-feed' analyticsId={actionsName.pondOverviewFeedEditClicked}>
            <EditOutlinedIcon />
          </BaseIconButton>
        </AddEditPondFeedModal>
      </Flex>

      <Flex flexWrap='wrap' gap='xs-alt'>
        <InfoCell title={trans('t_primary_feed_brand')}>{feedBrand || '-'}</InfoCell>
        <InfoCell title={trans('t_feed_table')}>{feedTableName?.name || '-'}</InfoCell>
      </Flex>
    </Box>
  );
}

function Projection({ population }: { population: PopulationType }) {
  const { trans } = getTrans();
  const { lang } = useAppSelector((state) => state.app);

  const { projection, target } = population?.cycleInformation ?? {};
  const { projectedGrowth, projectedSurvival, projectedFeed, overheadCostsPerHaPerDay } = projection ?? {};

  const { type: growthType } = projectedGrowth ?? {};
  const { fcr } = target ?? {};

  const survivalString = useMemo(() => {
    const { expectedTargetSurvival, expectedTargetSurvivalDays, dailyMortalityPercent, projectedSurvivalType } =
      projectedSurvival ?? {};
    if (projectedSurvivalType === 'mortality') {
      return dailyMortalityPercent ? `${parsePercent(dailyMortalityPercent)}% daily` : '-';
    }
    if (projectedSurvivalType === 'endOfCycle') {
      return expectedTargetSurvival
        ? `${formatNumber(expectedTargetSurvival, { lang, fractionDigits: 0, isPercentage: true })}${expectedTargetSurvivalDays ? ` ${trans('t_at')} ${expectedTargetSurvivalDays}d` : ''}`
        : '-';
    }
    return '-';
  }, [projectedSurvival]);

  const feedString = useMemo(() => {
    const { expectedFeed, type, feedAggressionMultiplier } = projectedFeed ?? {};

    if (type === 'fcr') {
      const { stockedAt, harvestPlan, productionPrediction } = population ?? {};
      const { harvestDate } = harvestPlan ?? {};
      const productionPredictionHarvestDate = productionPrediction?.find((p) => p.date === harvestDate);
      const { cumulativeFcr } = productionPredictionHarvestDate ?? {};
      const daysFromStockingToPlannedHarvest = DateTime.fromISO(harvestDate).diff(DateTime.fromISO(stockedAt), [
        'days',
        'hours'
      ]).days;

      return `${trans('t_count_fcr', { count: formatNumber(cumulativeFcr, { lang, fractionDigits: 2 }) || '-' })} ${trans('t_at')} ${daysFromStockingToPlannedHarvest || '-'}d`;
    }

    if (type === 'feedTable') {
      if (!feedAggressionMultiplier) return '-';
      return `${formatNumber(feedAggressionMultiplier, { lang, fractionDigits: 0, signDisplay: 'always', isPercentage: true })} ${trans('t_from_feed_table')}`;
    }

    if (type === 'kgPerHaPerDay') {
      return expectedFeed
        ? `${formatNumber(expectedFeed, { fractionDigits: 0, lang })} ${trans('t_kg_over_ha_over_day')}`
        : '-';
    }
  }, [projectedFeed]);

  return (
    <Box>
      <Flex justify='space-between' align='center' mb='md-alt'>
        <Text size='label100' lineHeight='20px'>
          {trans('t_projections')}
        </Text>
        <AdminOrSupervisorOrOperationWrapper>
          <AddEditPondProjectionModal>
            <BaseIconButton
              aria-label='edit-pond-projection'
              analyticsId={actionsName.pondOverviewProjectionEditClicked}
            >
              <EditOutlinedIcon />
            </BaseIconButton>
          </AddEditPondProjectionModal>
        </AdminOrSupervisorOrOperationWrapper>
      </Flex>

      <Flex flexWrap='wrap' gap='xs-alt'>
        <InfoCell title={trans('t_growth_rate')}>
          {!growthType || growthType === 'xpertsea' ? trans('t_kampi') : trans('t_custom')}
        </InfoCell>

        <InfoCell title={trans('t_feed_amount')}>{feedString}</InfoCell>

        <InfoCell title={trans('t_fcr')}>
          {isNumber(fcr) ? `${formatNumber(fcr, { fractionDigits: 2, lang })}` : '-'}
        </InfoCell>

        <InfoCell title={trans('t_survival')}>{survivalString}</InfoCell>
        <AdminOrSupervisorWrapper>
          <InfoCell title={trans('t_overhead_cost_ha_day')}>
            {isNumber(overheadCostsPerHaPerDay)
              ? formatNumber(overheadCostsPerHaPerDay, { fractionDigits: 2, lang, isCurrency: true })
              : '-'}
          </InfoCell>
        </AdminOrSupervisorWrapper>
      </Flex>
    </Box>
  );
}

function InfoCell({
  title,
  children,
  containerProps,
  valueProps
}: {
  containerProps?: BoxProps;
  valueProps?: TextProps;
  title: string;
  children: ReactNode;
}) {
  return (
    <Box flex='0 0 200px' {...containerProps}>
      <Flex
        flexDir='column'
        align='center'
        justify='center'
        bgColor='bg.gray.medium'
        borderRadius='xl'
        mb='md'
        height='40px'
      >
        <Text size='label200' textAlign='center'>
          {title}
        </Text>
      </Flex>

      <Text textAlign='center' size='label200' {...valueProps}>
        {children}
      </Text>
    </Box>
  );
}
