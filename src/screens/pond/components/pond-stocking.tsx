import { Box, Flex, Stack, Table, Text } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { DateTime } from 'luxon';
import { getPondState } from '@screens/pond/helpers/get-pond-state';
import { formatNumber, isNumber } from '@utils/number';
import { useSetFarmBookRefererInRedux } from '@components/farm-book/hooks/use-set-farm-book-referer-in-redux';
import { EditPondStockingModal } from '@screens/pond/components/modals/add-edit-pond-stocking/edit-pond-stocking-modal';
import { actionsName } from '@utils/segment';
import { PondAlerts } from '@screens/pond/components/alerts/pond-alerts';
import { AdminOrSupervisorWrapper } from '@components/permission-wrappers/admin-or-supervisor-wrapper';
import { EditOutlinedIcon } from '@icons/edit/edit-outlined-icon';
import { BaseIconButton } from '@components/base/base-icon-button';
import { TableContainer } from '@components/ui/table-container';

export function PondStocking() {
  const { trans } = getTrans();
  useSetFarmBookRefererInRedux('stocking');

  const lang = useAppSelector((state) => state.app.lang);
  const currentPopulation = useAppSelector((state) => state.farm?.currentPopulation);
  const currentPond = useAppSelector((state) => state.farm?.currentPond);

  const { name: pondName, _id: pondId, size: pondSize } = currentPond ?? {};
  const {
    harvest,
    stockedAt,
    seedingQuantity,
    seedingAverageWeight,
    history,
    geneticName,
    stockingCostsMillar,
    hatcheryName,
    nurserySources
  } = currentPopulation ?? {};

  const { isHarvested } = getPondState({ population: currentPopulation });

  return (
    <Flex bgColor='white' flexDir='column' gap='md' borderRadius='2xl' p='md' data-cy='pond-stocking-details'>
      <Stack gap='lg'>
        <PondAlerts />
        <Box>
          <Flex justify='space-between' align='center' mb='md-alt'>
            <Text size='label100' lineHeight='20px'>
              {trans('t_stocking')}
            </Text>
            {!harvest?.date && (
              <EditPondStockingModal
                pondId={pondId}
                pondName={pondName}
                pondSize={pondSize}
                isHarvested={isHarvested}
                firstMonitoring={history?.[history.length - 1]}
                nurserySources={nurserySources}
                isInPondView
              >
                <BaseIconButton
                  aria-label='edit-pond-stocking'
                  analyticsId={actionsName.pondOverviewPondStockingEditClicked}
                  data-cy='edit-stocking-btn'
                >
                  <EditOutlinedIcon />
                </BaseIconButton>
              </EditPondStockingModal>
            )}
          </Flex>

          <TableContainer>
            <Table.Root>
              <Table.Header>
                <Table.Row border='none'>
                  <TableHeaderCell title={trans('t_lab_source')} />
                  <TableHeaderCell title={trans('t_nauplii_source')} />
                  <TableHeaderCell title={trans('t_animals_stocked')} />
                  <TableHeaderCell title={trans('t_stocking_date')} />
                  <TableHeaderCell title={trans('t_stocking_weight_g')} />
                  <AdminOrSupervisorWrapper>
                    <TableHeaderCell title={trans('t_cost_millar')} />
                  </AdminOrSupervisorWrapper>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                <Table.Row>
                  <Table.Cell textAlign='center' pb={0} borderBottom='none'>
                    <Text size='label200'>{hatcheryName || '-'}</Text>
                  </Table.Cell>
                  <Table.Cell textAlign='center' pb={0} borderBottom='none'>
                    <Text size='label200'>{geneticName || '-'}</Text>
                  </Table.Cell>
                  <Table.Cell textAlign='center' pb={0} borderBottom='none'>
                    <Text size='label200'>
                      {isNumber(seedingQuantity) ? formatNumber(seedingQuantity, { lang, fractionDigits: 0 }) : '-'}
                    </Text>
                  </Table.Cell>
                  <Table.Cell textAlign='center' pb={0} borderBottom='none'>
                    <Text size='label200'>
                      {stockedAt ? DateTime.fromISO(stockedAt).toFormat('MMM dd, yyyy', { locale: lang }) : '-'}
                    </Text>
                  </Table.Cell>
                  <Table.Cell textAlign='center' pb={0} borderBottom='none'>
                    <Text size='label200'>
                      {isNumber(seedingAverageWeight)
                        ? formatNumber(seedingAverageWeight, { lang, fractionDigits: 2 })
                        : '-'}
                    </Text>
                  </Table.Cell>
                  <AdminOrSupervisorWrapper>
                    <Table.Cell textAlign='center' pb={0} borderBottom='none'>
                      <Text size='label200'>
                        {isNumber(stockingCostsMillar)
                          ? formatNumber(stockingCostsMillar, { lang, fractionDigits: 2, isCurrency: true })
                          : '-'}
                      </Text>
                    </Table.Cell>
                  </AdminOrSupervisorWrapper>
                </Table.Row>
              </Table.Body>
            </Table.Root>
          </TableContainer>
        </Box>
      </Stack>
    </Flex>
  );
}

function TableHeaderCell({ title }: { title: string }) {
  return (
    <Table.ColumnHeader border='none' textAlign='center' p='0'>
      <Text bgColor='gray.100' py='sm' borderRadius='xl' mx='2xs' size='label200'>
        {title}
      </Text>
    </Table.ColumnHeader>
  );
}
