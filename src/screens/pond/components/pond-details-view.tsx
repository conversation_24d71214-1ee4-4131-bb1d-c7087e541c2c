import { EmptyState } from '@components/errors/empty-state';
import { SomethingWentWrong } from '@components/errors/something-went-wrong';
import { PartialPageLoader } from '@components/loaders/partial-page-loader';
import { useAppSelector } from '@redux/hooks';
import { useEffect } from 'react';
import { useGetPondDetailsApi } from '@screens/pond/hooks/use-get-pond-details-api';
import { useCurrentPondReduxUpdate } from '@screens/pond/hooks/use-subscribe-pond-updated';
import { useCurrentPopulationReduxUpdate } from '@screens/pond/hooks/use-subscribe-population-updated';
import { PondView } from '@screens/pond/components/pond-view';

export function PondDetailsView({ pondEid }: { pondEid: number }) {
  const [{ isFinishedOnce, isLoading, error }, getPondDetails] = useGetPondDetailsApi();
  const { currentPond: pond } = useAppSelector((state) => state.farm);

  useCurrentPondReduxUpdate({
    farmId: pond?.farmId
  });
  useCurrentPopulationReduxUpdate({
    farmId: pond?.farmId
  });

  const getPond = () => {
    getPondDetails({
      loadRelated: true,
      params: { filter: { pondEid } }
    });
  };

  useEffect(() => {
    getPond();
  }, [pondEid]);

  if (error) return <SomethingWentWrong />;

  if ((isLoading && !isFinishedOnce) || !isFinishedOnce) return <PartialPageLoader showLoadingText />;

  if (!pond) return <EmptyState />;

  return <PondView isInPondView={true} />;
}
