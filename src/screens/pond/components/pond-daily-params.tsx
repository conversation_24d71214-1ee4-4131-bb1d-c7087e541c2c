import { getTrans } from '@i18n/get-trans';
import { Box, chakra, Flex, Skeleton, Table, TableCellProps, Text, TextProps } from '@chakra-ui/react';
import { useAppSelector } from '@redux/hooks';
import { EmptyState } from '@components/errors/empty-state';
import { ReactNode, useEffect, useState } from 'react';
import { ArrowBigLeftFilled } from '@icons/arrow-big-left/arrow-big-left-filled';
import { ArrowBigRightFilled } from '@icons/arrow-big-right/arrow-big-right-filled';
import { CardContainer } from '@components/card-container/card-container';
import { BaseIconButton } from '@components/base/base-icon-button';
import { EditOutlinedIcon } from '@icons/edit/edit-outlined-icon';
import { DateTime } from 'luxon';
import { PopulationDailyParameters, PopulationDailyParametersParams } from '@xpertsea/module-farm-sdk';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { numberTransform, useYupSchema, Yup, YupResolverType } from '@utils/yup';
import { BaseFormNumberInput } from '@components/form/base-form-number-input';
import { BaseButton } from '@components/base/base-button';
import { actionsName } from '@utils/segment';
import { useUpdatePopulationApi } from '@screens/population/hooks/use-update-population-api';
import { useReloadCurrentPond } from '@screens/pond/hooks/use-reload-current-pond';
import { isNumber } from '@utils/number';
import { TableContainer } from '@components/ui/table-container';
import { DailyParams, useGetDailyDataTrans } from '@screens/settings/hooks/use-get-daily-data-trans';

export function PondDailyParams() {
  return (
    <CardContainer borderRadius='2xl' p='md'>
      <DailyParamsTable />
    </CardContainer>
  );
}

function getInitialSevenDaysLuxon(today: DateTime) {
  const dateMinusSevenDays = today.minus({ days: 7 });
  const dates: DateTime[] = [];
  let tempDate = dateMinusSevenDays;
  while (tempDate <= today) {
    dates.push(tempDate);
    tempDate = tempDate.plus({ days: 1 });
  }
  return dates;
}

type DailyParamsMap = Record<
  string,
  { params: Record<string, number> } & Omit<PopulationDailyParameters, 'params' | 'date'>
>;

function generateHashMapOfDailyParams(populationDailyParams: PopulationDailyParameters[]) {
  const dailyParamsMap: DailyParamsMap = {};

  for (const { date, params, ...rest } of populationDailyParams) {
    if (!dailyParamsMap[date]) {
      dailyParamsMap[date] = { params: {}, ...rest };
    }

    for (const { paramId, value } of params) {
      dailyParamsMap[date].params[paramId] = value;
    }
  }
  return dailyParamsMap;
}

function revertDailyParamsMap(dailyParamsMap: DailyParamsMap) {
  const dailyParams: PopulationDailyParameters[] = [];

  for (const date in dailyParamsMap) {
    const { params, ...rest } = dailyParamsMap[date];

    const paramsArray = Object.entries(params).map(([paramId, value]) => ({
      paramId,
      value: isNumber(value) ? value : null
    })) as PopulationDailyParametersParams[];

    dailyParams.push({ date, params: paramsArray, ...rest });
  }

  return dailyParams;
}

function DailyParamsTable() {
  const { trans } = getTrans();
  const currentDate = DateTime.now();
  const dates = getInitialSevenDaysLuxon(currentDate);
  const [days, setDays] = useState<DateTime[]>(dates);
  const lastDate = days[days.length - 1];
  const lastDatePlusSeven = lastDate.plus({ days: 7 });
  const isWithinTodayRange = lastDatePlusSeven <= currentDate;

  const lang = useAppSelector((state) => state.app.lang);
  const { currentPopulation, currentFarm } = useAppSelector((state) => state.farm);
  const { dailyParameters: populationDailyParams, _id: populationId } = currentPopulation ?? {};
  const { dailyParameters: farmDailyParams } = currentFarm ?? {};

  const sortedFarmDailyParams = farmDailyParams
    .filter((item) => item.status === 'active' && item.isEnabled)
    .sort((a, b) => {
      if (a.isDefault && !b.isDefault) return -1;
      if (!a.isDefault && b.isDefault) return 1;
      return 0;
    });

  const { reloadCurrentPond } = useReloadCurrentPond();
  const [{ isLoading }, updatePopulation] = useUpdatePopulationApi();
  const { dailyWithUnitsTransMap } = useGetDailyDataTrans();

  const [isEditing, setIsEditing] = useState(false);

  const dailyParamsMap = generateHashMapOfDailyParams(populationDailyParams);

  type FormValues = typeof dailyParamsMap;
  const { schema } = useYupSchema({
    ...Object.keys(dailyParamsMap).reduce((acc, date) => {
      acc[date] = Yup.object().shape({
        params: Yup.object().shape(
          Object.keys(dailyParamsMap[date].params).reduce((paramAcc, paramId) => {
            paramAcc[paramId] = Yup.number()
              .nullable()
              .transform(numberTransform)
              .min(0, trans('t_min_x', { min: 0 }));
            return paramAcc;
          }, {} as Yup.ObjectShape)
        )
      });

      return acc;
    }, {} as Yup.ObjectShape)
  });

  const {
    reset,
    handleSubmit,
    control,
    formState: { errors, dirtyFields }
  } = useForm<FormValues>({
    resolver: yupResolver(schema) as YupResolverType<FormValues>,
    defaultValues: dailyParamsMap
  });

  useEffect(() => {
    if (!populationId) return;
    reset(dailyParamsMap);
  }, [JSON.stringify(dailyParamsMap ?? {})]);

  //VIEW CONDITIONS
  if (!sortedFarmDailyParams?.length) return <EmptyState />;

  //handlers
  const handleGoingBackSevenDays = () => {
    const newDays = days.map((date) => date.minus({ days: 7 }));
    setDays(newDays);
  };

  const handleGoingForwardSevenDays = () => {
    if (!isWithinTodayRange) return;

    const newDays = days.map((date) => date.plus({ days: 7 }));
    setDays(newDays);
  };
  const tdCommonStyles: TableCellProps = {
    px: '0',
    py: 'md',
    textAlign: 'center',
    borderBottomColor: 'border.gray.weak'
  };

  const handleCancelEditing = () => {
    setIsEditing(false);
    reset(dailyParamsMap);
  };
  const handleStartEditing = () => {
    setIsEditing(true);
  };

  const onSubmit = handleSubmit((data: FormValues) => {
    if (!Object.keys(dirtyFields).length) {
      handleCancelEditing();
      return;
    }

    const filteredData = Object.keys(data).reduce((acc, date) => {
      if (dirtyFields[date]) {
        acc[date] = data[date];
      }
      return acc;
    }, {} as FormValues);

    const dailyParameters = revertDailyParamsMap(filteredData);

    updatePopulation({
      params: {
        filter: {
          populationId
        },
        set: {
          dailyParameters
        }
      },
      successCallback() {
        reloadCurrentPond(() => {
          setIsEditing(false);
        });
      }
    });
  });

  return (
    <chakra.form onSubmit={onSubmit} noValidate>
      <Flex align='center' justify='space-between' mb='md-alt'>
        <Text size='label100' lineHeight='20px'>
          {trans('t_daily_parameters')}
        </Text>

        {!isEditing && (
          <BaseIconButton aria-label='edit-daily-params' onClick={handleStartEditing}>
            <EditOutlinedIcon />
          </BaseIconButton>
        )}
        {isEditing && (
          <Flex align='center' gap='sm-alt' justify='flex-end'>
            <BaseButton
              variant='secondary'
              w='max-content'
              onClick={handleCancelEditing}
              analyticsId={actionsName.pondOverviewCancelDailyParamsClicked}
            >
              {trans('t_cancel')}
            </BaseButton>
            <BaseButton
              type='submit'
              w='max-content'
              analyticsId={actionsName.pondOverviewUpdateDailyParamsClicked}
              loading={isLoading}
            >
              {trans('t_apply')}
            </BaseButton>
          </Flex>
        )}
      </Flex>
      {isLoading && (
        <Box>
          <Skeleton height='40px' width='100%' mb='md-alt' />
          <Skeleton height='40px' width='100%' mb='sm-alt' />
          <Skeleton height='40px' width='100%' mb='sm-alt' />
          <Skeleton height='40px' width='100%' mb='sm-alt' />
        </Box>
      )}
      {!isLoading && (
        <TableContainer>
          <Table.Root>
            <Table.Header>
              <Table.Row border='none'>
                <TableHeaderCell title={trans('t_parameter')} />
                <TableHeaderCell title={<ArrowBigLeftFilled cursor='pointer' onClick={handleGoingBackSevenDays} />} />
                {days.map((date) => (
                  <TableHeaderCell
                    key={date.toISODate()}
                    title={date.toFormat('MMM dd', { locale: lang })}
                    textProps={{ minW: '85px' }}
                  />
                ))}

                <TableHeaderCell
                  title={
                    <ArrowBigRightFilled
                      cursor={isWithinTodayRange ? 'pointer' : 'not-allowed'}
                      opacity={isWithinTodayRange ? 1 : 0.5}
                      onClick={handleGoingForwardSevenDays}
                    />
                  }
                />
              </Table.Row>
            </Table.Header>

            <Table.Body>
              {sortedFarmDailyParams.map((param, index) => {
                const { _id: paramId, name } = param;
                const isLastItem = index === sortedFarmDailyParams.length - 1;
                return (
                  <Table.Row key={paramId}>
                    <Table.Cell borderBottom={isLastItem ? 'none' : '0.5px solid'} {...tdCommonStyles}>
                      <Text size='label200'>{dailyWithUnitsTransMap[name as DailyParams] ?? name}</Text>
                    </Table.Cell>
                    <Table.Cell borderBottom={isLastItem ? 'none' : '0.5px solid'} {...tdCommonStyles} />
                    {days.map((date) => {
                      const dateISO = date.toISODate();
                      const value = dailyParamsMap[dateISO]?.params?.[paramId];
                      return (
                        <Table.Cell
                          key={dateISO}
                          borderBottom={isLastItem ? 'none' : '0.5px solid'}
                          {...tdCommonStyles}
                        >
                          {isEditing && (
                            <BaseFormNumberInput
                              name={`${dateISO}.params.${paramId}`}
                              control={control}
                              error={errors?.[dateISO]?.params?.[paramId]?.message}
                              numericFormatProps={{
                                decimalScale: 1
                              }}
                              inputProps={{ textAlign: 'center', w: '85px', textStyle: 'label.200' }}
                              w='100%'
                              px='xs-alt'
                              display='flex'
                              justifyContent='center'
                              alignItems='center'
                            />
                          )}
                          {!isEditing && <Text size='label200'>{value ?? '-'}</Text>}
                        </Table.Cell>
                      );
                    })}
                    <Table.Cell borderBottom={isLastItem ? 'none' : '0.5px solid'} {...tdCommonStyles} />
                  </Table.Row>
                );
              })}
            </Table.Body>
          </Table.Root>
        </TableContainer>
      )}
    </chakra.form>
  );
}

function TableHeaderCell({ title, textProps }: { title: ReactNode; textProps?: TextProps }) {
  const isStringOrNumberValue = typeof title === 'string' || typeof title === 'number';
  return (
    <Table.ColumnHeader border='none' textAlign='center' p='0'>
      <Text
        as={isStringOrNumberValue ? 'p' : 'div'}
        userSelect={isStringOrNumberValue ? 'text' : 'none'}
        bgColor='bg.gray.medium'
        py='xs-alt'
        borderRadius='xl'
        mx='2xs'
        mb='sm'
        h='40px'
        px='2sm'
        minW={isStringOrNumberValue ? '130px' : 'unset'}
        display='flex'
        alignItems='center'
        justifyContent='center'
        size='label200'
        {...textProps}
      >
        {title}
      </Text>
    </Table.ColumnHeader>
  );
}
