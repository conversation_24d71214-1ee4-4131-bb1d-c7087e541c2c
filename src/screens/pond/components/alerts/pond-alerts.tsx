import { Text } from '@chakra-ui/react';
import {
  formatNurseriesNames,
  getAvailableNurseriesData
} from '@screens/pond/components/modals/helpers/nurseries-helpers';
import { getPondState } from '@screens/pond/helpers/get-pond-state';
import { AlertRowContainer } from '@components/alert/alert-row-container';
import { ExclamationMarkSolid } from '@icons/exclamation-mark/exclamation-mark-solid';
import { EditPondStockingModal } from '@screens/pond/components/modals/add-edit-pond-stocking/edit-pond-stocking-modal';
import { BaseButton } from '@components/base/base-button';
import { ArrowBigRight } from '@icons/arrow-big-right/arrow-big-right';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { ReactNode } from 'react';
import { AddPondStockingModal } from '@screens/pond/components/modals/add-edit-pond-stocking/add-pond-stocking-modal';
import { BaseLink } from '@components/base/base-link';
import { HideOnMobileView } from '@components/mobile-view/hide-on-mobile-view';
import { AdminOrSupervisorWrapper } from '@components/permission-wrappers/admin-or-supervisor-wrapper';

export function PondAlerts() {
  const { trans } = getTrans();

  const { currentPond, currentPondNurseries, currentPopulation, currentFarmSlug } = useAppSelector(
    (state) => state.farm
  );
  const { _id: pondId, name, size } = currentPond;

  const { isHarvested, isEmpty } = getPondState({ population: currentPopulation });

  const { nurserySources: populationNursery, estimatedSurvival, estimatedFeed, history } = currentPopulation ?? {};

  const lastHistoryDate = history?.[0]?.date;
  const nurserySources = populationNursery ?? [];
  const { availableNurseries } = getAvailableNurseriesData({
    nurseries: currentPondNurseries,
    pondId,
    nurserySources,
    isHarvested
  });

  if (availableNurseries?.length) {
    return (
      <AlertContainer>
        <>
          <Text size={{ base: 'label300', md: 'label200' }}>
            {trans('t_you_have_incoming_transfers_to_this_pond_from')}
            {formatNurseriesNames({ nurseries: availableNurseries })}
          </Text>
          <HideOnMobileView>
            {isEmpty ? (
              <AddPondStockingModal
                pondId={pondId}
                pondName={name}
                pondSize={size}
                isHarvested={isHarvested}
                firstMonitoring={currentPopulation?.history?.[history.length - 1]}
                isInPondView
              >
                <BaseButton variant='link' size='sm'>
                  {trans('t_add_stocking_information_to_link_transfers')}{' '}
                  <ArrowBigRight color='button.link' w='20px' h='20px' />
                </BaseButton>
              </AddPondStockingModal>
            ) : (
              <EditPondStockingModal
                pondId={pondId}
                pondName={name}
                pondSize={size}
                isHarvested={isHarvested}
                firstMonitoring={currentPopulation?.history?.[history.length - 1]}
                nurserySources={nurserySources}
                isInPondView
              >
                <BaseButton variant='link' size='sm'>
                  {trans('t_edit_stocking_information_to_link_transfers')}{' '}
                  <ArrowBigRight color='button.link' w='20px' h='20px' />
                </BaseButton>
              </EditPondStockingModal>
            )}
          </HideOnMobileView>
        </>
      </AlertContainer>
    );
  }

  if (!currentPopulation?.stockingCostsMillar && !isEmpty) {
    return (
      <AdminOrSupervisorWrapper>
        <AlertContainer>
          <Text size={{ base: 'label300', md: 'label200' }}>
            {trans('t_you_are_missing_important_stocking_information')}
          </Text>
          <HideOnMobileView>
            <EditPondStockingModal
              pondId={pondId}
              pondName={name}
              pondSize={size}
              isHarvested={isHarvested}
              firstMonitoring={currentPopulation?.history?.[history.length - 1]}
              nurserySources={nurserySources}
              isInPondView
            >
              <BaseButton variant='link' size='sm'>
                {trans('t_add_details_now')} <ArrowBigRight color='button.link' w='20px' h='20px' />
              </BaseButton>
            </EditPondStockingModal>
          </HideOnMobileView>
        </AlertContainer>
      </AdminOrSupervisorWrapper>
    );
  }

  if (!!estimatedSurvival?.[lastHistoryDate] || !!estimatedFeed?.[lastHistoryDate]) {
    return (
      <AlertContainer>
        <Text maxW={{ base: '285px', sm: 'none' }} size={{ base: 'label300', md: 'label200' }}>
          {trans('t_estimate_data_alert_msg')}
        </Text>
        <HideOnMobileView>
          <BaseLink route='/farm/[farmEid]/data-updater' params={{ farmEid: currentFarmSlug }}>
            <BaseButton variant='link' size='sm'>
              {trans('t_update_data')} <ArrowBigRight color='button.link' w='20px' h='20px' />
            </BaseButton>
          </BaseLink>
        </HideOnMobileView>
      </AlertContainer>
    );
  }

  return null;
}

function AlertContainer({ children }: { children: ReactNode }) {
  return (
    <AlertRowContainer status='error'>
      <ExclamationMarkSolid color='icon.semanticRed' w='20px' h='20px' />
      {children}
    </AlertRowContainer>
  );
}
