import { Flex, Text } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { LocationMarker } from '@icons/location-marker/location-marker-icon';
import { BaseLink } from '@components/base/base-link';

export function GpsAlerts() {
  const { trans } = getTrans();

  const { currentPopulation } = useAppSelector((state) => state.farm);

  const { lastMonitoringDistance } = currentPopulation ?? {};

  if (lastMonitoringDistance > 20) {
    return (
      <Flex
        gap='sm-alt'
        flexWrap='wrap'
        align='center'
        p='sm-alt'
        borderRadius='4xl'
        pos='relative'
        bg='icon.shrimpyPinky.weak'
      >
        <LocationMarker color='icon.semanticRed' w='16px' h='16px' />
        <BaseLink _hover={{ textDecoration: 'underline' }} route='#gps-location'>
          <Text
            maxW={{ base: '285px', sm: 'none' }}
            fontWeight='semibold'
            size={{ base: 'label300', md: 'label200' }}
            color='bg.semanticRed'
          >
            {trans('t_gps_location_appears_farm_from_the_pond')}
          </Text>
        </BaseLink>
      </Flex>
    );
  }

  return null;
}
