import { Box, BoxProps, Flex, Text } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { getTrans } from '@i18n/get-trans';
import { AddEditFinalHarvestModal } from '@screens/population/components/harvest-modals/add-edit-final-harvest-modal';
import { PopulationType } from '@redux/farm/set-current-population';
import { DateTime } from 'luxon';
import { useAppSelector } from '@redux/hooks';
import { actionsName } from '@utils/segment';
import { usePondPartialHarvests } from '@screens/pond/components/pond-harvests-tab/use-pond-partial-harvests';
import { ManagePartialHarvestModal } from '@screens/population/components/harvest-modals/manage-partial-harvest-modal';
import { PopulationHarvestPlan, PopulationPartialHarvest } from '@xpertsea/module-farm-sdk';
import { RecordPartialHarvestModal } from '@screens/population/components/harvest-modals/record-partial-harvest-modal';
import { EditFinalHarvestPlanModal } from '@screens/population/components/harvest-modals/edit-final-harvest-plan-modal';

function AlertContainer(props: BoxProps) {
  return (
    <Box
      bgColor='semanticYellow.300'
      border='1px solid'
      borderColor='semanticYellow.600'
      py='sm-alt'
      px='md'
      rounded='xl'
      {...props}
    />
  );
}

interface PassedPartialHarvestProps {
  passedPartialHarvest: PopulationPartialHarvest;
  pondId: string;
  pondName: string;
  pondSize: number;
}

function PassedPartialHarvest(props: PassedPartialHarvestProps) {
  const { passedPartialHarvest, pondId, pondName, pondSize } = props;
  const { trans } = getTrans();
  const { isLoading, onRecordPartialHarvest } = usePondPartialHarvests();
  const currentPopulation = useAppSelector((state) => state.farm?.currentPopulation);
  const { productionPrediction } = currentPopulation ?? {};

  const {
    date: harvestDate,
    processorId,
    enablesEstimation,
    processorPriceList,
    lbsHarvested,
    revenue,
    survivalAtRecord,
    harvestId
  } = passedPartialHarvest ?? {};
  const predictionOnPlannedHarvest = productionPrediction?.find((item) => item.date === harvestDate);
  return (
    <AlertContainer>
      <Flex justify='space-between' align='center' gap='sm' flexWrap='wrap' whiteSpace='normal'>
        <Box fontSize='md'>
          <Text fontWeight={500}>
            {trans('t_planned_partial_harvest_on')}
            <Box as='span' color='brandSecondary' mx='2xs'>
              {harvestDate ? DateTime.fromISO(harvestDate).toLocaleString(DateTime.DATE_MED) : '-'}
            </Box>
            {trans('t_has_passed')}!
          </Text>
          <Text>{trans('t_partial_harvest_passed')}</Text>
        </Box>
        <Flex gap='sm' align='center' flexWrap='wrap'>
          <RecordPartialHarvestModal
            pondId={pondId}
            pondName={pondName}
            pondSize={pondSize}
            harvestId={harvestId}
            population={currentPopulation}
            survivalAtRecord={survivalAtRecord}
            defaultValues={{
              date: harvestDate,
              lbsHarvested,
              weight: predictionOnPlannedHarvest?.averageWeight,
              revenue,
              processorId,
              enablesEstimation,
              priceList: processorPriceList
            }}
            isLoading={isLoading}
            onSubmit={onRecordPartialHarvest}
          >
            <BaseButton
              size='sm'
              loading={isLoading}
              analyticsId={actionsName.addHarvestPlanClicked}
              analyticsData={{
                pondId,
                pondName,
                trigger: 'harvest-alert-button',
                state: 'manage partial harvests'
              }}
            >
              {trans('t_record_partial_harvest')}
            </BaseButton>
          </RecordPartialHarvestModal>

          <ManagePartialHarvestModal>
            <BaseButton
              size='sm'
              variant='link'
              color='text.gray.disabled'
              analyticsId={actionsName.managePartialHarvestClicked}
              analyticsData={{
                pondId,
                pondName,
                trigger: 'harvest-alert-button',
                state: 'manage partial harvests'
              }}
            >
              {trans('t_manage_partial_harvests')}
            </BaseButton>
          </ManagePartialHarvestModal>
        </Flex>
      </Flex>
    </AlertContainer>
  );
}

interface PassedHarvestProps {
  passedHarvest: PopulationHarvestPlan;
  pondId: string;
  pondName: string;
  pondSize: number;
}

function PassedHarvest(props: PassedHarvestProps) {
  const { passedHarvest, pondId, pondName, pondSize } = props;
  const productionPrediction = useAppSelector((state) => state.farm?.currentPopulation?.productionPrediction);

  const { trans } = getTrans();
  const { harvestDate, projection, processorPriceList, selectedProcessorId } = passedHarvest ?? {};
  const { profitProjectionData, plannedHarvestIdx } = projection ?? {};
  const profitOnPlannedHarvestDate =
    profitProjectionData?.[plannedHarvestIdx]?.[processorPriceList?.defaultHarvestType as 'headon'];
  const productionPredictionOnHarvestDate = productionPrediction?.find((data) => data.date === harvestDate);

  return (
    <AlertContainer>
      <Flex justify='space-between' align='center' gap='sm' flexWrap='wrap' whiteSpace='normal'>
        <Box fontSize='md'>
          <Text fontWeight={500}>
            {trans('t_planned_harvest_on')}
            <Box as='span' color='brandSecondary' mx='2xs'>
              {harvestDate ? DateTime.fromISO(harvestDate).toLocaleString(DateTime.DATE_MED) : '-'}
            </Box>
            {trans('t_has_passed')}!
          </Text>
          <Text>{trans('t_harvest_passed_msg')}</Text>
        </Box>
        <Flex gap='sm' align='center' flexWrap='wrap'>
          <AddEditFinalHarvestModal
            pondId={pondId}
            pondName={pondName}
            isInPondView
            defaultValues={{
              date: harvestDate,
              lbsHarvested: productionPredictionOnHarvestDate?.biomassLbs,
              weight: productionPredictionOnHarvestDate?.averageWeight,
              revenue: profitOnPlannedHarvestDate?.liveTotalRevenue,
              processorId: selectedProcessorId,
              enablesEstimation: true,
              priceList: processorPriceList
            }}
          >
            <BaseButton
              size='sm'
              analyticsId={actionsName.harvestPondClicked}
              analyticsData={{
                pondId,
                pondName,
                trigger: 'harvest-action-button',
                state: 'record final harvest'
              }}
            >
              {trans('t_record_harvest')}
            </BaseButton>
          </AddEditFinalHarvestModal>

          <EditFinalHarvestPlanModal
            pondId={pondId}
            pondName={pondName}
            pondSize={pondSize}
            isInPondView
            triggerContainerProps={{ width: 'unset' }}
          >
            <BaseButton
              size='sm'
              variant='link'
              color='text.gray.disabled'
              analyticsId={actionsName.editHarvestPlanClicked}
              analyticsData={{
                pondId,
                pondName,
                trigger: 'edit-harvest-plan-button',
                state: 'edit final harvest plan'
              }}
            >
              {trans('t_edit_harvest_plan')}
            </BaseButton>
          </EditFinalHarvestPlanModal>
        </Flex>
      </Flex>
    </AlertContainer>
  );
}

function hasPassedHarvests(population: PopulationType, timezone?: string) {
  const { harvestPlan, harvest, partialHarvestPlanned } = population ?? {};

  const todayDate = DateTime.local({ zone: timezone }).startOf('day');

  const passedPartialHarvestsPlanned = partialHarvestPlanned?.filter((partialHarvest) => {
    const targetDate = DateTime.fromISO(partialHarvest?.date, { zone: timezone }).startOf('day');
    return targetDate < todayDate;
  });
  const targetDate = DateTime.fromISO(harvestPlan?.harvestDate, { zone: timezone }).startOf('day');

  if (harvest?.date) {
    return {};
  }
  const passedPartialHarvest = passedPartialHarvestsPlanned?.[0];

  if (targetDate > todayDate) {
    return { passedPartialHarvest };
  }

  return { passedHarvest: harvestPlan, passedPartialHarvest };
}

export function PassedPlansAlerts() {
  const population = useAppSelector((state) => state.farm?.currentPopulation);
  const pondName = useAppSelector((state) => state.farm?.currentPond?.name);
  const pondId = useAppSelector((state) => state.farm?.currentPond?._id);
  const pondSize = useAppSelector((state) => state.farm?.currentPond?.size);
  const timezone = useAppSelector((state) => state.farm?.currentFarm?.timezone);

  const { passedHarvest, passedPartialHarvest } = hasPassedHarvests(population, timezone) ?? {};

  if (passedHarvest?.harvestDate) {
    return <PassedHarvest passedHarvest={passedHarvest} pondId={pondId} pondName={pondName} pondSize={pondSize} />;
  }

  if (passedPartialHarvest?.date)
    return (
      <PassedPartialHarvest
        pondId={pondId}
        pondName={pondName}
        pondSize={pondSize}
        passedPartialHarvest={passedPartialHarvest}
      />
    );

  return null;
}
