import { Box, Flex, Heading } from '@chakra-ui/react';
import { useAppSelector } from '@redux/hooks';
import { PondSelector } from '@screens/pond/components/pond-top-bar/pond-selector';
import { CycleMenu } from '@screens/pond/components/pond-top-bar/cycle-menu';
import { ArrowBigRightFilled } from '@icons/arrow-big-right/arrow-big-right-filled';
import { ArrowBigLeftFilled } from '@icons/arrow-big-left/arrow-big-left-filled';
import { BaseLink } from '@components/base/base-link';
import { useRouter } from 'next/router';
import { ShowOnMobileView } from '@components/mobile-view/show-on-mobile-view';
import { HideOnMobileView } from '@components/mobile-view/hide-on-mobile-view';

export function PondEntitySelector() {
  const currentFarmSlug = useAppSelector((state) => state.farm.currentFarmSlug);
  const currentPond = useAppSelector((state) => state.farm.currentPond);
  const farmName = useAppSelector((state) => state.farm.currentFarm?.name);
  const { query } = useRouter();
  if (!currentPond?._id) return null;

  return (
    <Box pos='relative' zIndex={99}>
      <Flex gap={{ base: 'xs', sm: 'md' }} align='center' flexWrap='wrap'>
        <ShowOnMobileView>
          <BaseLink
            me='md'
            route='/farm/[farmEid]'
            params={{ ...query, farmEid: currentFarmSlug, tab: undefined }}
            display='block'
          >
            <ArrowBigLeftFilled />
          </BaseLink>
        </ShowOnMobileView>

        <HideOnMobileView>
          <BaseLink
            route='/farm/[farmEid]'
            params={{ ...query, farmEid: currentFarmSlug, tab: undefined }}
            display='block'
          >
            <Heading
              as='span'
              w='fit-content'
              overflow='hidden'
              whiteSpace='nowrap'
              letterSpacing='unset'
              textOverflow='ellipsis'
              size='heavy300'
              maxW={{ base: '190px', sm: '280px', md: 'max-content' }}
            >
              {farmName}
            </Heading>
          </BaseLink>

          <ArrowBigRightFilled />
        </HideOnMobileView>

        <Flex h='40px' gap={{ base: 'xs', sm: 'md' }} align='center'>
          <PondSelector currentPond={currentPond} />
          <CycleMenu />
        </Flex>
      </Flex>
    </Box>
  );
}
