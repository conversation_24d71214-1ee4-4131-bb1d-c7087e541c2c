import { Flex } from '@chakra-ui/react';
import { SectionLoader } from '@components/loaders/section-loader';
import { useAppSelector } from '@redux/hooks';
import { PondEmptyState } from '@screens/pond/components/pond-empty-state';
import { getPondState } from '@screens/pond/helpers/get-pond-state';
import { PondDetails } from '@screens/pond/components/pond-details';
import { PondAlerts } from '@screens/pond/components/alerts/pond-alerts';

export interface PondViewProps {
  isInPondView?: boolean;
}

export function PondView(props: PondViewProps) {
  const { isInPondView } = props;

  const currentPopulation = useAppSelector((state) => state.farm?.currentPopulation);
  const isLoadingPondData = useAppSelector((state) => state.farm?.isLoadingPondData);
  const isLoadingPopulationData = useAppSelector((state) => state.farm?.isLoadingPopulationData);
  const { isNewPond } = getPondState({ population: currentPopulation });

  return (
    <Flex direction='column' gap='md-alt' pos='relative' data-cy='pond-view-screen' flex={1}>
      <SectionLoader isLoading={isLoadingPondData || isLoadingPopulationData} zIndex={40} />
      {isNewPond ? (
        <>
          <PondEmptyState isInPondView={isInPondView} />
          <PondAlerts />
        </>
      ) : (
        <PondDetails isInPondView={isInPondView} />
      )}
    </Flex>
  );
}
