import { Box, Flex, Heading, Text } from '@chakra-ui/react';
import { SummaryDrawer } from '@components/summary-drawer/summary-drawer';
import { getTrans } from '@i18n/get-trans';
import { CurrentPondNurseriesType } from '@redux/farm/set-current-pond-nurseries';
import { useAppSelector } from '@redux/hooks';
import { convertUnitByDivision, convertUnitByMultiplication, formatNumber, isNumber } from '@utils/number';
import {
  PopulationHarvestPlanProjectionProfitProjectionDataHeadon,
  PopulationNurserySources
} from '@xpertsea/module-farm-sdk';
import { DateTime } from 'luxon';
import { AdminOnlyWrapper } from '@components/permission-wrappers/admin-only-wrapper';
import { AdminOrSupervisorWrapper } from '@components/permission-wrappers/admin-or-supervisor-wrapper';
import { sqPerHectare } from '@utils/constants';
import isUndefined from 'lodash/isUndefined';
import { getDaysSinceDate } from '@screens/notifications/utils/dayDiffUtils';
import { VisionOnlyGuard } from '@components/permission-wrappers/vision-only-guard';

const getNurseriesNames = ({
  nurserySources,
  nurseries
}: {
  nurserySources: PopulationNurserySources[];
  nurseries: CurrentPondNurseriesType;
}) => {
  const names: string[] = [];
  if (!nurserySources?.length || !nurseries?.length) return names;

  nurserySources.forEach((ns) => {
    const nursery = nurseries.find((ele) => ele._id === ns.nurseryId);
    if (nursery) names.push(nursery.name);
  });

  return names;
};

export function PondSummary() {
  const { trans } = getTrans();
  const { lang } = useAppSelector((state) => state.app);

  const { currentPond, currentPopulation, isLoadingPondData, currentPondNurseries, currentFarm } = useAppSelector(
    (state) => state.farm
  );
  const user = useAppSelector((state) => state.auth.user);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};

  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);
  const unitHaLabel = isBiomassUnitLbs ? trans('t_lb_over_ha') : trans('t_kg_over_ha');
  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');

  if (isLoadingPondData || !currentPond?._id) return null;

  const { timezone: farmTimezone } = currentFarm ?? {};
  const { size } = currentPond;
  const {
    stockedAt,
    lastMonitoringDate,
    lastMonitoringDateTimezone,
    stockingType,
    stockingCostsMillar,
    hatcheryName,
    geneticName,
    seedingQuantity,
    seedingAverageWeight,
    cycleInformation,
    dryDaysBeforeStocking,
    nurserySources,
    harvestPlan,
    harvest,
    productionPrediction
  } = currentPopulation ?? {};

  const stockingDensityOverHa = size && seedingQuantity ? seedingQuantity / size : null;

  const originPonds = getNurseriesNames({ nurserySources, nurseries: currentPondNurseries });

  const daysSinceLastMonitoring = getDaysSinceDate({
    date: lastMonitoringDate,
    timezone: lastMonitoringDateTimezone,
    lang
  });

  const stockingDensityOverM2 = stockingDensityOverHa ? stockingDensityOverHa / sqPerHectare : null;

  const getFormattedDollars = (num: number, fractionDigits: number = 0) =>
    formatNumber(num, { fractionDigits, lang, isCurrency: true });

  //   Protocol data
  const { carryingCapacity, equipment } = cycleInformation ?? {};
  const { numberOfAerators, numberOfAutoFeeders, autoFeederBrand, feedBrand } = equipment ?? {};
  const aeratorsPerHa = numberOfAerators / size;
  const autoFeedersPerHa = numberOfAutoFeeders / size;

  //   Harvest data

  const {
    projection,
    harvestDate: harvestPlanDate,
    harvestType: oldHarvestType,
    processorPriceList
  } = harvestPlan ?? {};

  const harvestDate = harvest?.date ?? harvestPlanDate;
  const harvestType = processorPriceList?.defaultHarvestType ?? oldHarvestType;

  let averageWeight = harvest?.weight;
  let totalCosts = harvest?.totalCosts;
  let costPoundHarvest = harvest?.costPoundHarvest;
  let daysOfCulture = harvest?.daysOfCulture;
  let totalProfit = harvest?.totalProfit;
  let totalRevenue = harvest?.totalRevenue;
  let liveTotalRevenue = harvest?.revenue;
  let revenuePerPound = harvest?.revenuePerPound;
  let costPerPound = harvest?.costPerPound;
  let profitPerHaPerDay = harvest?.profitPerHaPerDay;

  if (!harvest?.date) {
    const productionPredictionOnPlannedHarvest = productionPrediction?.find((item) => item.date === harvestDate);
    const profitProjectionOnPlannedHarvest = projection?.profitProjectionData?.find(
      (item) => item.date === harvestDate
    );

    const {
      averageWeight: plannedAverageWeight,
      totalCosts: plannedTotalCosts,
      costPoundHarvest: plannedCostPoundHarvest,
      daysOfCulture: plannedDaysOfCulture
    } = productionPredictionOnPlannedHarvest ?? {};

    const {
      totalProfit: plannedTotalProfit,
      totalRevenue: plannedTotalRevenue,
      liveTotalRevenue: plannedLiveTotalRevenue,
      revenuePerPound: plannedRevenuePerPound,
      costPerPound: plannedCostPerPound,
      profitPerHaPerDay: plannedProfitPerHaPerDay
    } = profitProjectionOnPlannedHarvest?.[harvestType as 'headon'] ??
    ({} as PopulationHarvestPlanProjectionProfitProjectionDataHeadon);

    averageWeight = plannedAverageWeight;
    totalCosts = plannedTotalCosts;
    costPoundHarvest = plannedCostPoundHarvest;
    daysOfCulture = plannedDaysOfCulture;
    totalProfit = plannedTotalProfit;
    totalRevenue = plannedTotalRevenue;
    liveTotalRevenue = plannedLiveTotalRevenue;
    revenuePerPound = plannedRevenuePerPound;
    costPerPound = plannedCostPerPound;
    profitPerHaPerDay = plannedProfitPerHaPerDay;
  }

  const directStockingType = stockingType === 'direct' ? trans('t_direct') : '-';

  return (
    <SummaryDrawer title={trans('t_pond_summary')}>
      <Flex direction='column' gap='lg'>
        <VisionOnlyGuard>
          <AdminOnlyWrapper>
            <Flex justify='space-between' align='center' p='md' bg='bg.brandBlue.weakShade2' rounded='xl'>
              <Text fontSize='lg'>{trans('t_total_sale_value')}</Text>
              <Heading color='text.brandBlue' fontSize='2xl'>
                {isNumber(totalRevenue)
                  ? formatNumber(totalRevenue, { lang, fractionDigits: 2, isCurrency: true })
                  : '-'}
              </Heading>
            </Flex>
          </AdminOnlyWrapper>
        </VisionOnlyGuard>
        <Box>
          <Flex>
            <Text>{trans('t_summary')}</Text>
          </Flex>

          <OneRow title={trans('t_pond_size')} value={size ? `${size} ha` : '-'} />
          <VisionOnlyGuard>
            <OneRow
              title={isBiomassUnitLbs ? trans('t_carrying_capacity_lb_ha') : trans('t_carrying_capacity_kg_ha')}
              value={
                isNumber(carryingCapacity)
                  ? `${formatNumber(convertUnitByDivision(carryingCapacity, unitsConfig?.biomass), { lang, fractionDigits: 0 })} ${unitHaLabel}`
                  : '-'
              }
            />
          </VisionOnlyGuard>
          <OneRow title={trans('t_dry_days')} value={isNumber(dryDaysBeforeStocking) ? dryDaysBeforeStocking : '-'} />
          <OneRow title={trans('t_latest_monitoring')} value={daysSinceLastMonitoring} />
        </Box>

        <Box>
          <Flex>
            <Text>{trans('t_final_harvest')}</Text>
          </Flex>

          <>
            <OneRow
              title={trans('t_date')}
              value={
                harvestDate
                  ? DateTime.fromISO(harvestDate, { zone: farmTimezone }).toFormat('MMM dd, yyyy', { locale: lang })
                  : '-'
              }
            />
            <OneRow title={trans('t_days_of_culture')} value={daysOfCulture || '-'} />
            <OneRow
              title={trans('t_abw_g')}
              value={isNumber(averageWeight) ? formatNumber(averageWeight, { lang, fractionDigits: 2 }) : '-'}
            />
            <VisionOnlyGuard>
              <AdminOnlyWrapper>
                <OneRow
                  title={trans('t_revenue_processed_unit', { unit: unitLabel })}
                  value={
                    isNumber(revenuePerPound)
                      ? formatNumber(convertUnitByMultiplication(revenuePerPound, unitsConfig?.biomass), {
                          lang,
                          fractionDigits: 2,
                          isCurrency: true
                        })
                      : '-'
                  }
                />
                <OneRow
                  title={trans('t_revenue')}
                  value={
                    isNumber(liveTotalRevenue)
                      ? formatNumber(liveTotalRevenue, { lang, fractionDigits: 2, isCurrency: true })
                      : '-'
                  }
                />
              </AdminOnlyWrapper>
              <AdminOrSupervisorWrapper>
                <OneRow
                  title={trans('t_cost_per_unit_processed', { unit: unitLabel })}
                  value={
                    isNumber(costPerPound)
                      ? formatNumber(convertUnitByMultiplication(costPerPound, unitsConfig?.biomass), {
                          lang,
                          fractionDigits: 2,
                          isCurrency: true
                        })
                      : '-'
                  }
                />
                <OneRow
                  title={trans('t_cost_per_unit_harvested', { unit: unitLabel })}
                  value={
                    isNumber(costPoundHarvest)
                      ? formatNumber(convertUnitByMultiplication(costPoundHarvest, unitsConfig?.biomass), {
                          lang,
                          fractionDigits: 2,
                          isCurrency: true
                        })
                      : '-'
                  }
                />
                <OneRow
                  title={trans('t_cost_$')}
                  value={
                    isNumber(totalCosts) ? formatNumber(totalCosts, { lang, fractionDigits: 2, isCurrency: true }) : '-'
                  }
                />
              </AdminOrSupervisorWrapper>
              <AdminOnlyWrapper>
                <OneRow
                  title={trans('t_profit_include_dry_days_ha_day')}
                  value={
                    isNumber(profitPerHaPerDay)
                      ? formatNumber(profitPerHaPerDay, { lang, fractionDigits: 2, isCurrency: true })
                      : '-'
                  }
                />
                <OneRow
                  title={trans('t_profit')}
                  value={
                    isNumber(totalProfit)
                      ? formatNumber(totalProfit, {
                          lang,
                          fractionDigits: 2,
                          isCurrency: true
                        })
                      : '-'
                  }
                />
              </AdminOnlyWrapper>
            </VisionOnlyGuard>
          </>
        </Box>

        <Box>
          <Flex>
            <Text>{trans('t_stocking')}</Text>
          </Flex>
          <VisionOnlyGuard>
            <OneRow title={trans('t_pond_source')} value={originPonds?.length ? originPonds.join(', ') : '-'} />
            <OneRow
              title={trans('t_type_of_stocking')}
              value={stockingType === 'transfer' ? trans('t_transfer') : directStockingType}
            />
            <AdminOrSupervisorWrapper>
              <OneRow
                title={trans('t_cost_millar')}
                value={isNumber(stockingCostsMillar) ? getFormattedDollars(stockingCostsMillar, 2) : '-'}
              />
            </AdminOrSupervisorWrapper>

            <OneRow title={trans('t_lab_source')} value={hatcheryName || '-'} />
            <OneRow title={trans('t_nauplii_source')} value={geneticName || '-'} />
          </VisionOnlyGuard>
          <OneRow
            title={trans('t_stocking_density_ha')}
            value={
              isNumber(stockingDensityOverHa)
                ? `${formatNumber(stockingDensityOverHa, { lang, fractionDigits: 0 })}`
                : '-'
            }
          />
          <OneRow
            title={trans('t_stocking_density_m2')}
            value={
              isNumber(stockingDensityOverM2)
                ? `${formatNumber(stockingDensityOverM2, { lang, fractionDigits: 1 })}`
                : '-'
            }
          />
          <OneRow
            title={trans('t_stocking_date')}
            value={stockedAt ? DateTime.fromISO(stockedAt).toFormat('LLL dd, yyyy', { locale: lang }) : '-'}
          />
          <OneRow
            title={trans('t_animals_stocked')}
            value={isNumber(seedingQuantity) ? `${formatNumber(seedingQuantity, { lang, fractionDigits: 0 })}` : '-'}
          />
          <OneRow
            title={trans('t_stocking_weight_g')}
            value={
              isNumber(seedingAverageWeight) ? formatNumber(seedingAverageWeight, { lang, fractionDigits: 2 }) : '-'
            }
          />
        </Box>
        <VisionOnlyGuard>
          <Box>
            <Flex>
              <Text>{trans('t_protocol')}</Text>
            </Flex>

            <OneRow
              title={trans('t_#_of_autofeeders')}
              value={isNumber(numberOfAutoFeeders) ? numberOfAutoFeeders : '-'}
            />
            <OneRow title={trans('t_autofeeder_brand')} value={autoFeederBrand || '-'} />
            <OneRow title={trans('t_#_of_aerators')} value={isNumber(numberOfAerators) ? numberOfAerators : '-'} />
            <OneRow title={trans('t_feed_brand')} value={feedBrand || '-'} />
            <OneRow
              title={trans('t_autofeeders_ha')}
              value={isNumber(autoFeedersPerHa) ? formatNumber(autoFeedersPerHa, { lang, fractionDigits: 2 }) : '-'}
            />
            <OneRow
              title={trans('t_aerators_per_ha')}
              value={isNumber(aeratorsPerHa) ? formatNumber(aeratorsPerHa, { lang, fractionDigits: 2 }) : '-'}
            />
          </Box>
        </VisionOnlyGuard>
      </Flex>
    </SummaryDrawer>
  );
}

function OneRow({ title, value }: { title: string; value: string | number }) {
  return (
    <Flex justify='space-between' align='center' py='sm-alt' borderBottom='0.5px solid' borderColor='border.gray'>
      <Text>{title}</Text>
      <Text>{value}</Text>
    </Flex>
  );
}
