import { getTrans } from '@i18n/get-trans';
import { Box, Table, Text } from '@chakra-ui/react';
import { useAppSelector } from '@redux/hooks';
import { DateTime } from 'luxon';
import { formatNumber, isNumber } from '@utils/number';
import { PopulationNurserySources } from '@xpertsea/module-farm-sdk';
import { CurrentPondNurseriesType } from '@redux/farm/set-current-pond-nurseries';
import { EmptyState } from '@components/errors/empty-state';
import { AdminOrSupervisorWrapper } from '@components/permission-wrappers/admin-or-supervisor-wrapper';
import { TableContainer } from '@components/ui/table-container';
import { transformWeightWithUnit } from '@screens/nursery/helpers/transform-weight-with-unit';

export function PondTransferHistory() {
  const { trans } = getTrans();

  return (
    <Box bgColor='white' p='md' borderRadius='2xl'>
      <Text size='label100' mb='md' lineHeight='20px'>
        {trans('t_transfer_data')}
      </Text>

      <TransferDataTable />
    </Box>
  );
}

type TableDataType = {
  harvestDate: string;
  transferPercentage: number;
  transferredFrom: string;
  survivalRate: number;
  numberOfAnimalsTransferred: number;
  costsMillar: number;
  avgWeight: number;
  avgWeightUnit: string;
};
const getNurseries = ({
  currentPondId,
  nurseries,
  nurserySources
}: {
  currentPondId: string;
  nurseries: CurrentPondNurseriesType;
  nurserySources: PopulationNurserySources[];
}) => {
  const data: TableDataType[] = [];
  if (!nurseries?.length) return [];

  nurserySources.forEach((source) => {
    const { nurseryId, nurseryPopulationId } = source;
    const nursery = nurseries?.find((ele) => ele._id === nurseryId);
    if (!nursery) return;

    const { population, previousPopulations = [] } = nursery;
    const allPopulations = [population, ...previousPopulations];
    const { harvest, seedingQuantity } = allPopulations.find((ele) => ele._id === nurseryPopulationId) ?? {};

    if (!harvest) return;

    const { date: harvestDate, transfers } = harvest ?? {};
    const { totalAnimals } = transfers.reduce(
      (acc, ele) => {
        acc.totalAnimals += ele.value || 0;
        return acc;
      },
      { totalAnimals: 0 }
    );

    transfers?.forEach(
      ({ value: numberOfAnimalsTransferred, pondId, costsMillar, avgWeight, avgWeightUnit, isStocked }) => {
        if (pondId === currentPondId && isStocked) {
          data.push({
            harvestDate,
            numberOfAnimalsTransferred,
            costsMillar,
            avgWeight,
            avgWeightUnit,
            transferPercentage: numberOfAnimalsTransferred / totalAnimals,
            transferredFrom: nursery.name,
            survivalRate: seedingQuantity ? totalAnimals / seedingQuantity : 0
          });
        }
      }
    );
  });

  return data;
};

function TransferDataTable() {
  const { trans } = getTrans();

  const lang = useAppSelector((state) => state.app.lang);
  const { currentPopulation, currentPondNurseries, currentPond } = useAppSelector((state) => state.farm);

  const tableData = getNurseries({
    currentPondId: currentPond?._id,
    nurseries: currentPondNurseries,
    nurserySources: currentPopulation?.nurserySources
  });

  if (!tableData?.length) {
    return <EmptyState />;
  }

  return (
    <TableContainer>
      <Table.Root>
        <Table.Header>
          <Table.Row border='none'>
            <TableHeaderCell title={trans('t_source')} />
            <TableHeaderCell title={trans('t_transfer_date')} />
            <TableHeaderCell title={trans('t_animals_%')} />
            <TableHeaderCell title={trans('t_survival')} />
            <TableHeaderCell title={trans('t_animals')} />
            <AdminOrSupervisorWrapper>
              <TableHeaderCell title={trans('t_cost_millar')} />
            </AdminOrSupervisorWrapper>
            <TableHeaderCell title={trans('t_abw')} />
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {tableData.map((row, i) => {
            const isLast = tableData.length - 1 === i;
            const {
              harvestDate,
              transferPercentage,
              transferredFrom,
              survivalRate,
              numberOfAnimalsTransferred,
              costsMillar,
              avgWeight,
              avgWeightUnit
            } = row;
            const { weightString } = transformWeightWithUnit({ lang, weightInGrams: avgWeight, unit: avgWeightUnit });

            return (
              <Table.Row borderBottom='0.5px solid' borderBottomColor={isLast ? 'transparent' : 'gray.400'} key={i}>
                <Table.Cell textAlign='center'>
                  <Text size='label200'>{transferredFrom || '-'}</Text>
                </Table.Cell>
                <Table.Cell textAlign='center'>
                  <Text size='label200'>
                    {harvestDate ? DateTime.fromISO(harvestDate).toFormat('LLL dd, yyyy', { locale: lang }) : ''}
                  </Text>
                </Table.Cell>
                <Table.Cell textAlign='center'>
                  <Text size='label200'>
                    {isNumber(transferPercentage)
                      ? formatNumber(transferPercentage, { lang, fractionDigits: 0, isPercentage: true })
                      : '-'}
                  </Text>
                </Table.Cell>
                <Table.Cell textAlign='center'>
                  <Text size='label200'>
                    {isNumber(survivalRate)
                      ? formatNumber(survivalRate, { lang, fractionDigits: 0, isPercentage: true })
                      : '-'}
                  </Text>
                </Table.Cell>
                <Table.Cell textAlign='center'>
                  <Text size='label200'>
                    {isNumber(numberOfAnimalsTransferred)
                      ? formatNumber(numberOfAnimalsTransferred, { lang, fractionDigits: 0 })
                      : '-'}
                  </Text>
                </Table.Cell>
                <AdminOrSupervisorWrapper>
                  <Table.Cell textAlign='center'>
                    <Text size='label200'>
                      {isNumber(costsMillar)
                        ? formatNumber(costsMillar, { lang, fractionDigits: 2, isCurrency: true })
                        : '-'}
                    </Text>
                  </Table.Cell>
                </AdminOrSupervisorWrapper>
                <Table.Cell textAlign='center'>
                  <Text size='label200'>{weightString}</Text>
                </Table.Cell>
              </Table.Row>
            );
          })}
        </Table.Body>
      </Table.Root>
    </TableContainer>
  );
}

function TableHeaderCell({ title }: { title: string }) {
  return (
    <Table.ColumnHeader border='none' textAlign='center' p='0'>
      <Text bgColor='gray.100' py='sm' borderRadius='xl' mx='2xs' size='label200'>
        {title}
      </Text>
    </Table.ColumnHeader>
  );
}
