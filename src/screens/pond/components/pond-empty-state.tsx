import { Box, Flex, Text } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { getPondState } from '@screens/pond/helpers/get-pond-state';
import { AddPondStockingModal } from '@screens/pond/components/modals/add-edit-pond-stocking/add-pond-stocking-modal';
import { actionsName } from '@utils/segment';
import { VisionOnlyAccess } from '@components/permission-wrappers/vision-only-access';
import { VisionOnlyGuard } from '@components/permission-wrappers/vision-only-guard';
import { VisionOnlyAddEditPondStockingModal } from '@screens/pond/components/modals/add-edit-pond-stocking/vision-only-add-edit-pond-stocking-modal';

export function PondEmptyState({ isInPondView }: { isInPondView?: boolean }) {
  const { trans } = getTrans();

  const population = useAppSelector((state) => state.farm.currentPopulation);
  const pond = useAppSelector((state) => state.farm.currentPond);
  const { name: pondName, _id: pondId, size: pondSize } = pond ?? {};
  const { history } = population ?? {};
  const { isHarvested } = getPondState({ population });

  return (
    <Flex
      gap='sm-alt'
      rounded='xl'
      bgColor='bg.gray.strong'
      p='sm-alt'
      justify='space-between'
      align='center'
      data-cy='new-pond'
    >
      <Box>
        <Text fontWeight={500} fontSize='lg'>
          {trans('t_your_pond_is_empty')}
        </Text>
        <Text fontWeight={400} fontSize='md'>
          {trans('t_stock_pond_to_get_started')}
        </Text>
      </Box>
      <VisionOnlyAccess>
        <VisionOnlyAddEditPondStockingModal
          pondId={pondId}
          pondName={pondName}
          pondSize={pondSize}
          isHarvested={isHarvested}
          firstMonitoring={history?.[history.length - 1]}
          isInPondView={isInPondView}
        >
          <BaseButton
            data-cy='stock-pond-btn'
            size='sm'
            px='sm'
            analyticsId={actionsName.stockPondClicked}
            analyticsData={{ source: 'pond details harvested alert bar stock pond' }}
          >
            {trans('t_stock_your_pond')}
          </BaseButton>
        </VisionOnlyAddEditPondStockingModal>
      </VisionOnlyAccess>
      <VisionOnlyGuard>
        <AddPondStockingModal
          pondId={pondId}
          pondName={pondName}
          pondSize={pondSize}
          isHarvested={isHarvested}
          firstMonitoring={history?.[history.length - 1]}
          isInPondView={isInPondView}
        >
          <BaseButton
            data-cy='stock-pond-btn'
            size='sm'
            px='sm'
            analyticsId={actionsName.stockPondClicked}
            analyticsData={{ source: 'pond details harvested alert bar stock pond' }}
          >
            {trans('t_stock_your_pond')}
          </BaseButton>
        </AddPondStockingModal>
      </VisionOnlyGuard>
    </Flex>
  );
}
