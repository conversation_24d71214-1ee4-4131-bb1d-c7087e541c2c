import { PondDetailsView } from '@screens/pond/components/pond-details-view';
import { useRouter } from 'next/router';
import { useAppSelector } from '@redux/hooks';
import { PartialPageLoader } from '@components/loaders/partial-page-loader';
import { Box, Flex } from '@chakra-ui/react';
import { PondEntitySelector } from '@screens/pond/components/pond-entity-selector';
import { PondSummary } from '@screens/pond/components/pond-summary';
import { FarmSelector } from '@screens/farm/components/farm-selector';
import { NoPondsInYourFarmCard } from '@screens/group-summary/components/no-ponds-in-your-farm-card';

export function PondViewScreen() {
  const { pondEid } = useRouter().query as { pondEid: string };
  const isLoadingFarmData = useAppSelector((state) => state.farm.isLoadingFarmData);

  const pondEidNumber = Number(pondEid?.split('-')[0]);

  if (!pondEidNumber) {
    return (
      <Flex p='md' direction='column' gap='md-alt' id='ponds-list' data-cy='ponds-list-data' bgColor='white'>
        <FarmSelector />
        <NoPondsInYourFarmCard id='ponds-list' />
      </Flex>
    );
  }

  if (isLoadingFarmData) {
    return <PartialPageLoader />;
  }

  return (
    <Flex direction='column' gap='md-alt' p='md' minH='100vh'>
      <Flex gap='sm-alt' justify='space-between' flexWrap='wrap' alignItems='center'>
        <PondEntitySelector />
        <Box ms='auto'>
          <PondSummary />
        </Box>
      </Flex>
      <PondDetailsView pondEid={pondEidNumber} />
    </Flex>
  );
}
