import { Box, Flex } from '@chakra-ui/react';
import { InboxType } from '@screens/notifications/utils/notificationTypes';
import {
  CardActionsMenu,
  CardDate,
  CardFooterInfo,
  CardHeaderContainer,
  CardNotificationText,
  CardPondName,
  CardPondNameContainer,
  CustomLabel,
  NotificationAccordionWrapper,
  NotificationCardWithCheckbox,
  NotificationCardWrapper
} from '@screens/notifications/components/notifications-ui';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { useOnNotificationClicked } from '@screens/notifications/hooks/use-on-notification-clicked';
import { DateTime } from 'luxon';
import { useOnNotificationActions } from '@screens/notifications/hooks/use-notification-actions';
import { SectionLoader } from '@components/loaders/section-loader';
import { VisionOnlyGuard } from '@components/permission-wrappers/vision-only-guard';
import isUndefined from 'lodash/isUndefined';
import { getSharedNotificationFooterData } from '@screens/notifications/utils/get-shared-notification-footer-data';
import { formatNumber } from '@utils/number';

interface Props {
  notifications: InboxType[];
  reloadNotifications: () => void;
  selectedNotifications: string[];
  onSelectedNotificationsChange: (id: string, checked: boolean) => void;
  isManagingNotifications: boolean;
}

export function SuddenTemperatureChangeNotifications(props: Props) {
  const {
    notifications,
    selectedNotifications,
    isManagingNotifications,
    reloadNotifications,
    onSelectedNotificationsChange
  } = props;

  const { trans } = getTrans();

  if (!notifications?.length) return null;

  const getIsChecked = (id: string) => selectedNotifications.includes(id);

  if (notifications?.length === 1) {
    return (
      <NotificationCardWithCheckbox
        isManageActive={isManagingNotifications}
        checked={getIsChecked(notifications[0]._id)}
        onCheckChange={(v) => onSelectedNotificationsChange(notifications[0]._id, v)}
      >
        <NotificationCard notification={notifications[0]} reloadNotifications={reloadNotifications} />
      </NotificationCardWithCheckbox>
    );
  }

  return (
    <NotificationAccordionWrapper title={trans('t_sudden_temperature_change_ponds', { count: notifications.length })}>
      <Flex flexDir='column' gap='md' ps='lg'>
        {notifications.map((notification) => (
          <NotificationCardWithCheckbox
            key={notification._id}
            isManageActive={isManagingNotifications}
            checked={getIsChecked(notification._id)}
            onCheckChange={(v) => onSelectedNotificationsChange(notification._id, v)}
          >
            <NotificationCard notification={notification} reloadNotifications={reloadNotifications} />
          </NotificationCardWithCheckbox>
        ))}
      </Flex>
    </NotificationAccordionWrapper>
  );
}

const getNotificationContent = (payload: InboxType['payload'], timezone: string) => {
  const { trans } = getTrans();

  const { currentTemperature, currentTemperatureRecordedAt, previousTemperature, previousTemperatureRecordedAt } =
    payload ?? {};

  const changedToTime = DateTime.fromISO(currentTemperatureRecordedAt, { zone: timezone }).toFormat('LLL dd');
  const changedFromTime = DateTime.fromISO(previousTemperatureRecordedAt, { zone: timezone }).toFormat('LLL dd');

  return {
    dropped: {
      label: trans('t_sudden_temperature_drop'),
      title: trans('t_temperature_dropped_notification', {
        dropped_to: currentTemperature,
        dropped_to_time: changedToTime,
        dropped_from: previousTemperature,
        dropped_from_time: changedFromTime
      })
    },
    increased: {
      label: trans('t_sudden_temperature_increase'),
      title: trans('t_temperature_increased_notification', {
        increased_to: currentTemperature,
        increased_to_time: changedToTime,
        increased_from: previousTemperature,
        increased_from_time: changedFromTime
      })
    }
  };
};

function NotificationCard({
  notification,
  reloadNotifications
}: {
  notification: InboxType;
  reloadNotifications: () => void;
}) {
  const { trans } = getTrans();

  const { payload, isRead, pondId, createdAt, _id: notificationId, templateName } = notification ?? {};
  const {
    pondName,
    currentBiomassLbsByHa,
    currentCarryingCapacityPercentage,
    currentTemperature,
    temperateThresholds
  } = payload ?? {};

  const { minValue, maxValue } = temperateThresholds ?? {};

  const user = useAppSelector((state) => state.auth.user);
  const lang = useAppSelector((state) => state.app.lang);
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);

  const { timezone } = currentFarm ?? {};
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};

  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);

  const handleOnClick = useOnNotificationClicked();
  const { isUpdatingInbox, onMarkAsUnread, onDelete } = useOnNotificationActions({ notification });

  const maxTempChange = currentTemperature > maxValue ? 'increased' : '';
  const changeType = currentTemperature < minValue ? 'dropped' : maxTempChange;

  if (!changeType) return null;

  const notificationContent = getNotificationContent(payload, timezone);

  const biomassLbsPerHaTitle = isBiomassUnitLbs ? trans('t_biomass_in_pond_lb_ha') : trans('t_biomass_in_pond_kg_ha');
  const { biomassHaValue } = getSharedNotificationFooterData({
    currentBiomassLbsByHa,
    carryingCapacityPercent: currentCarryingCapacityPercentage,
    lang,
    unit: unitsConfig?.biomass
  });

  const carryingCapacityPercentageFormatted = currentCarryingCapacityPercentage
    ? `${formatNumber(currentCarryingCapacityPercentage, { lang, isPercentage: true, fractionDigits: 0 })}`
    : '-';

  return (
    <NotificationCardWrapper
      onClick={() => handleOnClick({ isRead, notificationId, pondId, chartParam: templateName })}
      pos='relative'
    >
      <SectionLoader isLoading={isUpdatingInbox} />

      <CardHeaderContainer>
        <CardPondNameContainer>
          <CardPondName pondName={pondName} />
          <CustomLabel text={notificationContent[changeType].label} isRead={isRead} />
        </CardPondNameContainer>
        <CardDate>{DateTime.fromISO(createdAt, { zone: timezone }).toRelative({ locale: lang })}</CardDate>
      </CardHeaderContainer>

      <Flex alignItems='center'>
        <Box flex={1}>
          <CardNotificationText isRead={isRead}>{notificationContent[changeType].title}</CardNotificationText>
          <VisionOnlyGuard>
            <CardFooterInfo
              info={[
                `${trans('t_carrying_capacity_usage')}: ${carryingCapacityPercentageFormatted}`,
                `${biomassLbsPerHaTitle}: ${biomassHaValue}`
              ]}
            />
          </VisionOnlyGuard>
        </Box>

        <CardActionsMenu
          onDelete={() => onDelete({ onSuccess: reloadNotifications })}
          onMarkAsUnread={isRead ? () => onMarkAsUnread({ onSuccess: reloadNotifications }) : undefined}
        />
      </Flex>
    </NotificationCardWrapper>
  );
}
