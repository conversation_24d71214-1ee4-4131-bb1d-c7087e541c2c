import { Box, Flex } from '@chakra-ui/react';
import { InboxType, NOTIFICATION_TYPE } from '@screens/notifications/utils/notificationTypes';
import {
  CardActionsMenu,
  CardDate,
  CardFooterInfo,
  CardHeaderContainer,
  CardNotificationText,
  CardPondName,
  CardPondNameContainer,
  CustomLabel,
  NotificationAccordionWrapper,
  NotificationCardWithCheckbox,
  NotificationCardWrapper
} from '@screens/notifications/components/notifications-ui';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { useOnNotificationClicked } from '@screens/notifications/hooks/use-on-notification-clicked';
import { DateTime } from 'luxon';
import { useOnNotificationActions } from '@screens/notifications/hooks/use-notification-actions';
import { SectionLoader } from '@components/loaders/section-loader';
import { VisionOnlyGuard } from '@components/permission-wrappers/vision-only-guard';
import isUndefined from 'lodash/isUndefined';
import { getSharedNotificationFooterData } from '@screens/notifications/utils/get-shared-notification-footer-data';
import { formatNumber } from '@utils/number';

interface Props {
  notifications: InboxType[];
  reloadNotifications: () => void;
  selectedNotifications: string[];
  onSelectedNotificationsChange: (id: string, checked: boolean) => void;
  isManagingNotifications: boolean;
}

export function DissolvedOxygenChangeNotifications(props: Props) {
  const {
    notifications,
    selectedNotifications,
    isManagingNotifications,
    reloadNotifications,
    onSelectedNotificationsChange
  } = props;

  const { trans } = getTrans();

  if (!notifications?.length) return null;

  const getIsChecked = (id: string) => selectedNotifications.includes(id);

  if (notifications?.length === 1) {
    return (
      <NotificationCardWithCheckbox
        isManageActive={isManagingNotifications}
        checked={getIsChecked(notifications[0]._id)}
        onCheckChange={(v) => onSelectedNotificationsChange(notifications[0]._id, v)}
      >
        <NotificationCard notification={notifications[0]} reloadNotifications={reloadNotifications} />
      </NotificationCardWithCheckbox>
    );
  }

  const groupingTitle =
    notifications[0].templateName === 'am-dissolved-oxygen-change-notification'
      ? trans('t_do_am_grouping_msg', { count: notifications.length })
      : trans('t_do_pm_grouping_msg', { count: notifications.length });

  return (
    <NotificationAccordionWrapper title={groupingTitle}>
      <Flex flexDir='column' gap='md' ps='lg'>
        {notifications.map((notification) => (
          <NotificationCardWithCheckbox
            key={notification._id}
            isManageActive={isManagingNotifications}
            checked={getIsChecked(notification._id)}
            onCheckChange={(v) => onSelectedNotificationsChange(notification._id, v)}
          >
            <NotificationCard notification={notification} reloadNotifications={reloadNotifications} />
          </NotificationCardWithCheckbox>
        ))}
      </Flex>
    </NotificationAccordionWrapper>
  );
}

function NotificationCard({
  notification,
  reloadNotifications
}: {
  notification: InboxType;
  reloadNotifications: () => void;
}) {
  const { payload, isRead, pondId, createdAt, _id: notificationId, templateName } = notification ?? {};
  const {
    pondName,
    currentBiomassLbsByHa,
    currentOxygen,

    currentCarryingCapacityPercentage,
    oxygenThresholds
  } = payload ?? {};
  const { trans } = getTrans();

  const user = useAppSelector((state) => state.auth.user);
  const lang = useAppSelector((state) => state.app.lang);
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);

  const { timezone } = currentFarm ?? {};
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};

  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);

  const handleOnClick = useOnNotificationClicked();
  const { isUpdatingInbox, onMarkAsUnread, onDelete } = useOnNotificationActions({ notification });

  const changeTypeMaxValue = currentOxygen > oxygenThresholds?.maxValue ? 'increased' : '';
  const changeType = currentOxygen < oxygenThresholds?.minValue ? 'dropped' : changeTypeMaxValue;

  if (!changeType) return;

  const textValues = {
    dropped: {
      label:
        templateName === NOTIFICATION_TYPE.AM_DISSOLVED_OXYGEN_CHANGE
          ? trans('t_am_do_value', { value: currentOxygen })
          : trans('t_pm_do_value', { value: currentOxygen }),
      title:
        templateName === NOTIFICATION_TYPE.AM_DISSOLVED_OXYGEN_CHANGE
          ? trans('t_do_am_low', { value: currentOxygen })
          : trans('t_do_pm_low', { value: currentOxygen })
    },
    increased: {
      label:
        templateName === NOTIFICATION_TYPE.AM_DISSOLVED_OXYGEN_CHANGE
          ? trans('t_am_do_value', { value: currentOxygen })
          : trans('t_pm_do_value', { value: currentOxygen }),
      title:
        templateName === NOTIFICATION_TYPE.AM_DISSOLVED_OXYGEN_CHANGE
          ? trans('t_do_am_high', { value: currentOxygen })
          : trans('t_do_pm_high', { value: currentOxygen })
    }
  };

  const biomassLbsPerHaTitle = isBiomassUnitLbs ? trans('t_biomass_in_pond_lb_ha') : trans('t_biomass_in_pond_kg_ha');
  const { biomassHaValue } = getSharedNotificationFooterData({
    currentBiomassLbsByHa,
    carryingCapacityPercent: currentCarryingCapacityPercentage,
    lang,
    unit: unitsConfig?.biomass
  });

  const carryingCapacityPercentageFormatted = currentCarryingCapacityPercentage
    ? `${formatNumber(currentCarryingCapacityPercentage, { lang, isPercentage: true, fractionDigits: 0 })}`
    : '-';

  return (
    <NotificationCardWrapper
      onClick={() => handleOnClick({ isRead, notificationId, pondId, chartParam: templateName })}
      pos='relative'
    >
      <SectionLoader isLoading={isUpdatingInbox} />

      <CardHeaderContainer>
        <CardPondNameContainer>
          <CardPondName pondName={pondName} />
          <CustomLabel text={textValues[changeType].label} isRead={isRead} />
        </CardPondNameContainer>
        <CardDate>{DateTime.fromISO(createdAt, { zone: timezone }).toRelative({ locale: lang })}</CardDate>
      </CardHeaderContainer>

      <Flex alignItems='center'>
        <Box flex={1}>
          <CardNotificationText isRead={isRead}>{textValues[changeType].title}</CardNotificationText>

          <VisionOnlyGuard>
            <CardFooterInfo
              info={[
                `${trans('t_carrying_capacity_usage')}: ${carryingCapacityPercentageFormatted}`,
                `${biomassLbsPerHaTitle}: ${biomassHaValue}`
              ]}
            />
          </VisionOnlyGuard>
        </Box>

        <CardActionsMenu
          onDelete={() => onDelete({ onSuccess: reloadNotifications })}
          onMarkAsUnread={isRead ? () => onMarkAsUnread({ onSuccess: reloadNotifications }) : undefined}
        />
      </Flex>
    </NotificationCardWrapper>
  );
}
