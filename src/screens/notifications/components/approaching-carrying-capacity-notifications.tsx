import { Box, Flex } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import {
  CardActionsMenu,
  CardDate,
  CardFooterInfo,
  CardHeaderContainer,
  CardNotificationText,
  CardPondName,
  CardPondNameContainer,
  CustomLabel,
  NotificationAccordionWrapper,
  NotificationCardWithCheckbox,
  NotificationCardWrapper
} from '@screens/notifications/components/notifications-ui';
import { DateTime } from 'luxon';
import { SectionLoader } from '@components/loaders/section-loader';
import { InboxType } from '@screens/notifications/utils/notificationTypes';
import { useOnNotificationClicked } from '@screens/notifications/hooks/use-on-notification-clicked';
import { useOnNotificationActions } from '@screens/notifications/hooks/use-notification-actions';
import { getDaysUntilDate } from '@screens/notifications/utils/dayDiffUtils';
import { getSharedNotificationFooterData } from '@screens/notifications/utils/get-shared-notification-footer-data';
import isUndefined from 'lodash/isUndefined';
import { formatNumber } from '@utils/number';

interface Props {
  notifications: InboxType[];
  reloadNotifications: () => void;
  selectedNotifications: string[];
  onSelectedNotificationsChange: (id: string, checked: boolean) => void;
  isManagingNotifications: boolean;
}

export function ApproachingCarryingCapacityNotifications(props: Props) {
  const {
    notifications,
    selectedNotifications,
    isManagingNotifications,
    reloadNotifications,
    onSelectedNotificationsChange
  } = props;

  const { trans } = getTrans();

  if (!notifications?.length) return null;

  const getIsChecked = (id: string) => selectedNotifications.includes(id);

  if (notifications?.length === 1) {
    return (
      <NotificationCardWithCheckbox
        isManageActive={isManagingNotifications}
        checked={getIsChecked(notifications[0]._id)}
        onCheckChange={(v) => onSelectedNotificationsChange(notifications[0]._id, v)}
      >
        <NotificationCard notification={notifications[0]} reloadNotifications={reloadNotifications} />
      </NotificationCardWithCheckbox>
    );
  }

  return (
    <NotificationAccordionWrapper
      title={trans('t_carrying_capacity_will_be_reached_on_ponds', {
        count: notifications?.length
      })}
    >
      <Flex flexDir='column' gap='md' ps='lg'>
        {notifications.map((notification) => (
          <NotificationCardWithCheckbox
            key={notification._id}
            isManageActive={isManagingNotifications}
            checked={getIsChecked(notification._id)}
            onCheckChange={(v) => onSelectedNotificationsChange(notification._id, v)}
          >
            <NotificationCard notification={notification} reloadNotifications={reloadNotifications} />
          </NotificationCardWithCheckbox>
        ))}
      </Flex>
    </NotificationAccordionWrapper>
  );
}

function NotificationCard({
  notification,
  reloadNotifications
}: {
  notification: InboxType;
  reloadNotifications: () => void;
}) {
  const { payload, isRead, pondId, createdAt, _id: notificationId, templateName } = notification ?? {};
  const { pondName, currentCarryingCapacityPercentage, currentBiomassLbsByHa, targetCarryingCapacityDate } =
    payload ?? {};
  const { trans } = getTrans();

  const user = useAppSelector((state) => state.auth.user);
  const lang = useAppSelector((state) => state.app.lang);
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);

  const { timezone } = currentFarm ?? {};
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};

  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);

  const handleOnClick = useOnNotificationClicked();
  const { isUpdatingInbox, onMarkAsUnread, onDelete } = useOnNotificationActions({ notification });

  const daysUntilDate = getDaysUntilDate({ date: targetCarryingCapacityDate, lang });

  const biomassLbsPerHaTitle = isBiomassUnitLbs ? trans('t_biomass_in_pond_lb_ha') : trans('t_biomass_in_pond_kg_ha');
  const { biomassHaValue } = getSharedNotificationFooterData({
    currentBiomassLbsByHa,
    carryingCapacityPercent: currentCarryingCapacityPercentage,
    lang,
    unit: unitsConfig?.biomass
  });

  const carryingCapacityPercentageFormatted = currentCarryingCapacityPercentage
    ? `${formatNumber(currentCarryingCapacityPercentage, { lang, isPercentage: true, fractionDigits: 0 })}`
    : '-';

  return (
    <NotificationCardWrapper
      onClick={() => handleOnClick({ isRead, notificationId, pondId, chartParam: templateName })}
      pos='relative'
    >
      <SectionLoader isLoading={isUpdatingInbox} />
      <CardHeaderContainer>
        <CardPondNameContainer>
          <CardPondName pondName={pondName} />
          <CustomLabel text={trans('t_reaching_full_capacity')} bg='yellow.400' color='black' isRead={isRead} />
        </CardPondNameContainer>
        <CardDate>{DateTime.fromISO(createdAt, { zone: timezone }).toRelative({ locale: lang })}</CardDate>
      </CardHeaderContainer>

      <Flex alignItems='center'>
        <Box flex={1}>
          <CardNotificationText isRead={isRead}>
            {trans('t_carrying_capacity_will_be_reached_notification', { days: daysUntilDate })}
          </CardNotificationText>

          <CardFooterInfo
            info={[
              `${trans('t_carrying_capacity_usage')}: ${carryingCapacityPercentageFormatted}`,
              `${biomassLbsPerHaTitle}: ${biomassHaValue}`
            ]}
          />
        </Box>

        <CardActionsMenu
          onDelete={() => onDelete({ onSuccess: reloadNotifications })}
          onMarkAsUnread={isRead ? () => onMarkAsUnread({ onSuccess: reloadNotifications }) : undefined}
        />
      </Flex>
    </NotificationCardWrapper>
  );
}
