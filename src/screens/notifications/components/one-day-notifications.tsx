import { InboxType, NOTIFICATION_TYPE } from '@screens/notifications/utils/notificationTypes';
import groupBy from 'lodash/groupBy';
import { CarryingCapacityReachedNotifications } from '@screens/notifications/components/carrying-capacity-reached-notifications';
import { ApproachingCarryingCapacityNotifications } from '@screens/notifications/components/approaching-carrying-capacity-notifications';
import { SuddenTemperatureChangeNotifications } from '@screens/notifications/components/sudden-temperature-change-notifications';
import { AbsoluteTemperatureChangeNotifications } from '@screens/notifications/components/absolute-temperature-change-notifications';
import { DissolvedOxygenChangeNotifications } from '@screens/notifications/components/dissolved-oxygen-change-notifications';
import { Box, Flex, Text } from '@chakra-ui/react';
import { DateTime } from 'luxon';
import { useAppSelector } from '@redux/hooks';
import { getTrans } from '@i18n/get-trans';
import { VisionOnlyGuard } from '@components/permission-wrappers/vision-only-guard';

type OneDayNotificationsPropsType = {
  date: string;
  notifications: InboxType[];
  reloadNotifications: () => void;
  selectedNotifications: string[];
  onSelectedNotificationsChange: (id: string, checked: boolean) => void;
  isManagingNotifications: boolean;
};

export function OneDayNotifications({
  date,
  notifications,
  selectedNotifications,
  isManagingNotifications,
  onSelectedNotificationsChange,
  reloadNotifications
}: OneDayNotificationsPropsType) {
  const { trans } = getTrans();

  const lang = useAppSelector((state) => state.app.lang);
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);

  if (!notifications?.length) return null;

  const groupedNotifications = groupBy(notifications, 'templateName');

  const isToday = false;
  const isYesterday = false;
  const yesterdayLabel = isYesterday
    ? trans('t_yesterday')
    : DateTime.fromISO(date, { zone: currentFarm.timezone }).toFormat('EEEE, MMM dd', { locale: lang });

  return (
    <Box>
      <Flex justify='center' mb='2lg'>
        <Flex align='center' rounded='4xl' h='20px' bg='brandBlue.200' w='fit-content' px='md'>
          <Text size='label300'>{isToday ? trans('t_today') : yesterdayLabel}</Text>
        </Flex>
      </Flex>

      <Flex flexDir='column' gap='md'>
        {Object.entries(groupedNotifications).map(([key, notifications]) => {
          switch (key) {
            case NOTIFICATION_TYPE.CARRYING_CAPACITY_REACHED:
              return (
                <VisionOnlyGuard key={key}>
                  <CarryingCapacityReachedNotifications
                    notifications={notifications}
                    reloadNotifications={reloadNotifications}
                    selectedNotifications={selectedNotifications}
                    onSelectedNotificationsChange={onSelectedNotificationsChange}
                    isManagingNotifications={isManagingNotifications}
                  />
                </VisionOnlyGuard>
              );

            case NOTIFICATION_TYPE.APPROACHING_CARRYING_CAPACITY:
              return (
                <VisionOnlyGuard key={key}>
                  <ApproachingCarryingCapacityNotifications
                    notifications={notifications}
                    reloadNotifications={reloadNotifications}
                    selectedNotifications={selectedNotifications}
                    onSelectedNotificationsChange={onSelectedNotificationsChange}
                    isManagingNotifications={isManagingNotifications}
                  />
                </VisionOnlyGuard>
              );

            case NOTIFICATION_TYPE.SUDDEN_TEMPERATURE_CHANGE:
            case NOTIFICATION_TYPE.ABSOLUTE_TEMPERATURE_CHANGE:
              return (
                <SuddenTemperatureChangeNotifications
                  key={key}
                  notifications={notifications}
                  reloadNotifications={reloadNotifications}
                  selectedNotifications={selectedNotifications}
                  onSelectedNotificationsChange={onSelectedNotificationsChange}
                  isManagingNotifications={isManagingNotifications}
                />
              );

            case NOTIFICATION_TYPE.PM_WATER_TEMPERATURE_CHANGE:
            case NOTIFICATION_TYPE.AM_WATER_TEMPERATURE_CHANGE:
              return (
                <AbsoluteTemperatureChangeNotifications
                  key={key}
                  notifications={notifications}
                  reloadNotifications={reloadNotifications}
                  selectedNotifications={selectedNotifications}
                  onSelectedNotificationsChange={onSelectedNotificationsChange}
                  isManagingNotifications={isManagingNotifications}
                />
              );

            case NOTIFICATION_TYPE.AM_DISSOLVED_OXYGEN_CHANGE:
            case NOTIFICATION_TYPE.PM_DISSOLVED_OXYGEN_CHANGE:
              return (
                <DissolvedOxygenChangeNotifications
                  key={key}
                  notifications={notifications}
                  reloadNotifications={reloadNotifications}
                  selectedNotifications={selectedNotifications}
                  onSelectedNotificationsChange={onSelectedNotificationsChange}
                  isManagingNotifications={isManagingNotifications}
                />
              );

            default:
              return null;
          }
        })}
      </Flex>
    </Box>
  );
}
