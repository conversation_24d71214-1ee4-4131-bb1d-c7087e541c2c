import { Box, Flex } from '@chakra-ui/react';
import {
  CardActionsMenu,
  CardDate,
  CardFooterInfo,
  CardHeaderContainer,
  CardNotificationText,
  CardPondName,
  CardPondNameContainer,
  CustomLabel,
  NotificationAccordionWrapper,
  NotificationCardWithCheckbox,
  NotificationCardWrapper
} from '@screens/notifications/components/notifications-ui';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { DateTime } from 'luxon';
import { SectionLoader } from '@components/loaders/section-loader';
import {
  InboxType,
  NOTIFICATION_TYPE,
  NotificationTemplateNameType
} from '@screens/notifications/utils/notificationTypes';
import { useOnNotificationClicked } from '@screens/notifications/hooks/use-on-notification-clicked';
import { useOnNotificationActions } from '@screens/notifications/hooks/use-notification-actions';
import isUndefined from 'lodash/isUndefined';
import { VisionOnlyG<PERSON> } from '@components/permission-wrappers/vision-only-guard';
import { getSharedNotificationFooterData } from '@screens/notifications/utils/get-shared-notification-footer-data';
import { formatNumber } from '@utils/number';

interface Props {
  notifications: InboxType[];
  reloadNotifications: () => void;
  selectedNotifications: string[];
  onSelectedNotificationsChange: (id: string, checked: boolean) => void;
  isManagingNotifications: boolean;
}

export function AbsoluteTemperatureChangeNotifications(props: Props) {
  const {
    notifications,
    selectedNotifications,
    isManagingNotifications,
    reloadNotifications,
    onSelectedNotificationsChange
  } = props;

  const { trans } = getTrans();

  if (!notifications?.length) return null;

  const getIsChecked = (id: string) => selectedNotifications.includes(id);

  if (notifications?.length === 1) {
    return (
      <NotificationCardWithCheckbox
        isManageActive={isManagingNotifications}
        checked={getIsChecked(notifications[0]._id)}
        onCheckChange={(v) => onSelectedNotificationsChange(notifications[0]._id, v)}
      >
        <NotificationCard notification={notifications[0]} reloadNotifications={reloadNotifications} />
      </NotificationCardWithCheckbox>
    );
  }

  const groupingTitle =
    notifications[0].templateName === NOTIFICATION_TYPE.AM_WATER_TEMPERATURE_CHANGE
      ? trans('t_temperature_am_grouping_msg', { count: notifications.length })
      : trans('t_temperature_pm_grouping_msg', { count: notifications.length });

  return (
    <NotificationAccordionWrapper title={groupingTitle}>
      <Flex flexDir='column' gap='md' ps='lg'>
        {notifications.map((notification) => (
          <NotificationCardWithCheckbox
            key={notification._id}
            isManageActive={isManagingNotifications}
            checked={getIsChecked(notification._id)}
            onCheckChange={(v) => onSelectedNotificationsChange(notification._id, v)}
          >
            <NotificationCard notification={notification} reloadNotifications={reloadNotifications} />
          </NotificationCardWithCheckbox>
        ))}
      </Flex>
    </NotificationAccordionWrapper>
  );
}

const getNotificationContent = (payload: InboxType['payload'], templateName: NotificationTemplateNameType) => {
  const { trans } = getTrans();

  const { currentTemperature } = payload ?? {};

  return {
    dropped: {
      label:
        templateName === NOTIFICATION_TYPE.AM_WATER_TEMPERATURE_CHANGE
          ? trans('t_am_water_temperature_drop', { temperature: currentTemperature })
          : trans('t_pm_water_temperature_drop', { temperature: currentTemperature }),
      title: trans('t_water_temperature_low', {
        temperature: currentTemperature,
        type: templateName === NOTIFICATION_TYPE.AM_WATER_TEMPERATURE_CHANGE ? 'AM' : 'PM'
      })
    },
    increased: {
      label:
        templateName === NOTIFICATION_TYPE.AM_WATER_TEMPERATURE_CHANGE
          ? trans('t_am_water_temperature_drop', { temperature: currentTemperature })
          : trans('t_pm_water_temperature_drop', { temperature: currentTemperature }),
      title: trans('t_water_temperature_high', {
        temperature: currentTemperature,
        type: templateName === NOTIFICATION_TYPE.AM_WATER_TEMPERATURE_CHANGE ? 'AM' : 'PM'
      })
    }
  };
};

function NotificationCard({
  notification,
  reloadNotifications
}: {
  notification: InboxType;
  reloadNotifications: () => void;
}) {
  const { payload, isRead, pondId, createdAt, _id: notificationId, templateName } = notification ?? {};
  const {
    pondName,
    currentBiomassLbsByHa,
    currentTemperature,
    currentCarryingCapacityPercentage,
    temperatureThresholds
  } = payload ?? {};
  const { trans } = getTrans();

  const user = useAppSelector((state) => state.auth.user);
  const lang = useAppSelector((state) => state.app.lang);
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);

  const { timezone } = currentFarm ?? {};
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};

  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);

  const handleOnClick = useOnNotificationClicked();
  const { isUpdatingInbox, isDeletingInbox, onMarkAsUnread, onDelete } = useOnNotificationActions({ notification });

  const changeTypeMaxValue = currentTemperature > temperatureThresholds?.maxValue ? 'increased' : '';
  const changeType = currentTemperature < temperatureThresholds?.minValue ? 'dropped' : changeTypeMaxValue;

  if (!changeType) return null;

  const notificationContent = getNotificationContent(payload, templateName);

  const biomassLbsPerHaTitle = isBiomassUnitLbs ? trans('t_biomass_in_pond_lb_ha') : trans('t_biomass_in_pond_kg_ha');
  const { biomassHaValue } = getSharedNotificationFooterData({
    currentBiomassLbsByHa,
    carryingCapacityPercent: currentCarryingCapacityPercentage,
    lang,
    unit: unitsConfig?.biomass
  });

  const carryingCapacityPercentageFormatted = currentCarryingCapacityPercentage
    ? `${formatNumber(currentCarryingCapacityPercentage, { lang, isPercentage: true, fractionDigits: 0 })}`
    : '-';

  return (
    <NotificationCardWrapper
      onClick={() => handleOnClick({ isRead, notificationId, pondId, chartParam: templateName })}
      pos='relative'
      flexGrow={1}
    >
      <SectionLoader isLoading={isUpdatingInbox || isDeletingInbox} />
      <CardHeaderContainer>
        <CardPondNameContainer>
          <CardPondName pondName={pondName} />
          <CustomLabel text={notificationContent[changeType].label} isRead={isRead} />
        </CardPondNameContainer>
        <CardDate>{DateTime.fromISO(createdAt, { zone: timezone }).toRelative({ locale: lang })}</CardDate>
      </CardHeaderContainer>

      <Flex alignItems='center'>
        <Box flex={1}>
          <CardNotificationText isRead={isRead}>{notificationContent[changeType].title}</CardNotificationText>

          <VisionOnlyGuard>
            <CardFooterInfo
              info={[
                `${trans('t_carrying_capacity_usage')}: ${carryingCapacityPercentageFormatted}`,
                `${biomassLbsPerHaTitle}: ${biomassHaValue}`
              ]}
            />
          </VisionOnlyGuard>
        </Box>

        <CardActionsMenu
          onDelete={() => onDelete({ onSuccess: reloadNotifications })}
          onMarkAsUnread={isRead ? () => onMarkAsUnread({ onSuccess: reloadNotifications }) : undefined}
        />
      </Flex>
    </NotificationCardWrapper>
  );
}
