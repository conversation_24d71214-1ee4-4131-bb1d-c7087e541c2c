import { Box, BoxProps, Flex, FlexProps, Text, TextProps } from '@chakra-ui/react';
import { BaseFormCheckbox } from '@components/form/base-form-checkbox';
import { MenuButton, MenuContent, MenuItem, MenuRoot } from '@components/ui/menu';
import { getTrans } from '@i18n/get-trans';
import { ChevronDownFilled } from '@icons/chevron-down/chevron-down-filled';
import { ChevronUpFilled } from '@icons/chevron-up/chevron-up-filled';
import { MoreHoriz } from '@icons/more-horiz/more-horiz';
import { ReactNode, useState } from 'react';

export function NotificationAccordionWrapper({ title, children }: { title: string; children: ReactNode }) {
  const [isOpen, setIsOpen] = useState(true);

  return (
    <>
      <Flex justify='space-between' py='sm' cursor='pointer' onClick={() => setIsOpen((val) => !val)}>
        <Text size='heavy100'>{title}</Text>
        {isOpen ? (
          <ChevronUpFilled hasBackground={false} color='icon.gray' />
        ) : (
          <ChevronDownFilled hasBackground={false} color='icon.gray' />
        )}
      </Flex>

      <Box display={isOpen ? 'initial' : 'none'}>{children}</Box>
    </>
  );
}

export function CardHeaderContainer(props: FlexProps) {
  return <Flex justify='space-between' align='flex-start' mb='sm-alt' {...props} />;
}

export function CardPondNameContainer(props: FlexProps) {
  return (
    <Flex
      gap='sm-alt'
      flexDir={{ sm: 'row', base: 'column' }}
      align={{ sm: 'center', base: 'flex-start' }}
      {...props}
    />
  );
}

export function CardNotificationText({ isRead, ...rest }: TextProps & { isRead: boolean }) {
  return <Text textStyle={isRead ? 'paragraph.200.light' : 'paragraph.200.heavy'} mb='sm-alt' {...rest} />;
}

export function CardDate(props: TextProps) {
  return <Text size='label400' color='gray.500' {...props} />;
}

export function CustomLabel({ text, isRead, ...rest }: FlexProps & { text: string; isRead: boolean }) {
  return (
    <Flex
      align='center'
      justify='center'
      bg='semanticRed.600'
      color='white'
      px='xs-alt'
      h='16px'
      rounded='4xl'
      opacity={isRead ? 0.4 : 1}
      {...rest}
    >
      <Text size='label300'>{text}</Text>
    </Flex>
  );
}

export function NotificationCardWrapper(props: BoxProps) {
  return <Box bg='white' p='sm-alt' rounded='2xl' {...props} cursor='pointer' />;
}

export function CardPondName({ pondName }: { pondName: string }) {
  const { trans } = getTrans();

  return (
    <Text size='heavy200'>
      {trans('t_pond')} {pondName}
    </Text>
  );
}

export function CardFooterInfo({ info }: { info: string[] }) {
  return (
    <Flex gap='md'>
      {info?.map((ele, i) => (
        <Text key={i} size='light300' color='gray.700'>
          {ele}
        </Text>
      ))}
    </Flex>
  );
}

export function CardActionsMenu({ onDelete, onMarkAsUnread }: { onDelete: () => void; onMarkAsUnread?: () => void }) {
  const { trans } = getTrans();

  return (
    <MenuRoot>
      <MenuButton
        selectVariant='secondary'
        size='sm'
        px={0}
        _hover={{ opacity: '0.8' }}
        data-cy='more-options-menu-btn'
        onClick={(e) => e.stopPropagation()}
      >
        <MoreHoriz transform='rotate(90deg)' w='20px' h='20px' />
      </MenuButton>

      <MenuContent>
        <MenuItem
          onClick={(e) => {
            e.stopPropagation();
            onDelete();
          }}
          data-cy='delete-notification-btn'
          value='delete-notification-btn'
        >
          {trans('t_delete')}
        </MenuItem>

        {!!onMarkAsUnread && (
          <MenuItem
            data-cy='mark-as-read-btn'
            value='mark-as-read-btn'
            w='100%'
            onClick={(e) => {
              e.stopPropagation();
              onMarkAsUnread();
            }}
          >
            {trans('t_mark_as_unread')}
          </MenuItem>
        )}
      </MenuContent>
    </MenuRoot>
  );
}

export function NotificationCardWithCheckbox({
  children,
  isManageActive,
  checked,
  onCheckChange
}: {
  children: ReactNode;
  isManageActive: boolean;
  checked: boolean;
  onCheckChange: (v: boolean) => void;
}) {
  if (!isManageActive) return <>{children}</>;

  return (
    <Flex align='center' gap='md'>
      <BaseFormCheckbox bg='white' checked={checked} onCheckedChange={(v) => onCheckChange(v.checked as boolean)} />
      <Box flex={1}>{children}</Box>
    </Flex>
  );
}
