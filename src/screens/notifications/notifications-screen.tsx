import { Box, Flex, Text } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { AllNotifications } from './components/all-notifications';
import { FarmSelector } from '@screens/farm/components/farm-selector';
import { SettingsFilled } from '@icons/settings/settings-filled';
import { useAppSelector } from '@redux/hooks';
import { BaseLink } from '@components/base/base-link';
import { PartialPageLoader } from '@components/loaders/partial-page-loader';
import { BaseButton } from '@components/base/base-button';
import { useState } from 'react';

export function NotificationsScreen() {
  const { trans } = getTrans();

  const { currentFarmSlug, currentFarmId, isLoadingFarmData } = useAppSelector((state) => state.farm);
  const [isManagingNotifications, setIsManagingNotifications] = useState(false);

  const cancelManageNotifications = () => setIsManagingNotifications(false);

  if (isLoadingFarmData) return <PartialPageLoader />;

  return (
    <Flex direction='column' gap='md-alt' p='md'>
      <FarmSelector />

      <Box borderRadius='2xl' p='md' boxShadow='elevation.400'>
        <Flex justify='space-between' mb='md' alignItems='flex-start'>
          <Text as='h2' size='heavy100' letterSpacing='unset'>
            {trans('t_kampi_notification_center')}
          </Text>

          <Flex align='center' gap='md'>
            <BaseButton
              size='sm'
              variant='outline'
              onClick={() => setIsManagingNotifications(true)}
              disabled={isManagingNotifications}
            >
              {trans('t_manage_notifications')}
            </BaseButton>
            <BaseLink route='/farm/[farmEid]/settings' params={{ farmEid: currentFarmSlug, tab: 'notifications' }}>
              <SettingsFilled secondSectionColor='gray.700' />
            </BaseLink>
          </Flex>
        </Flex>

        <AllNotifications
          key={currentFarmId}
          farmId={currentFarmId}
          isManagingNotifications={isManagingNotifications}
          cancelManageNotifications={cancelManageNotifications}
        />
      </Box>
    </Flex>
  );
}
