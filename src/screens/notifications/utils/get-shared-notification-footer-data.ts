import { convertUnitByDivision, formatNumber, isNumber } from '@utils/number';

interface GetSharedNotificationFooterDataProps {
  currentBiomassLbsByHa: number;
  carryingCapacityPercent: number;
  lang: string;
  unit: 'lbs' | 'kg';
}

export function getSharedNotificationFooterData(props: GetSharedNotificationFooterDataProps) {
  const { currentBiomassLbsByHa, carryingCapacityPercent, lang, unit } = props;

  const biomassLbsByHaConverted = convertUnitByDivision(currentBiomassLbsByHa, unit);

  const biomassLbsHa = formatNumber(biomassLbsByHaConverted, { lang, fractionDigits: 0 });

  const carryingCapacityPercentFormatted = formatNumber(carryingCapacityPercent, {
    lang,
    fractionDigits: 0
  });

  const biomassHaValue = isNumber(biomassLbsHa) ? biomassLbsHa : '-';
  const carryingCapacityPercentValue = carryingCapacityPercentFormatted ? `${carryingCapacityPercentFormatted}%` : '-';

  return { biomassHaValue, carryingCapacityPercentValue };
}
