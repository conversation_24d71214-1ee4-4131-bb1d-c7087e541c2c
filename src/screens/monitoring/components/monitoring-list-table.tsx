import { Box, Flex, Icon, Skeleton, Table, Text, TextProps } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { FarmDaysBetweenMonitoringAndStocking } from '@components/farm-days-since-stocked/farm-days-between-monitoring-and-stocking';
import { GrowthWeeklyToolTip } from '@components/growth-weekly-tooltip/growth-weekly-tooltip';
import { InfoIconToolTip } from '@components/tooltip/info-icon-tool-tip';
import { useAnalytics } from '@hooks/use-analytics';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { CVLearningDrawer } from '@screens/farm/components/cv-learning-drawer';
import { ABWInvalidDiv } from '@screens/farm/components/invalid-abw';
import { isInvalidABW } from '@screens/farm/helpers/abw';
import { getDaysDiffBetweenDates } from '@screens/farm/helpers/farm-dates';
import {
  ViewMonitoringModal,
  ViewMonitoringModalProps
} from '@screens/monitoring/components/monitoring-list-modal-view';
import { MonitoringTableActions } from '@screens/monitoring/components/monitoring-table-actions';
import { SurvivalRateButton } from '@screens/monitoring/components/survival-rate-button';
import { WeightDistributionChart } from '@screens/monitoring/components/weight-distribution-chart';
import { parsePercent } from '@screens/monitoring/helpers/weight-distribution';
import { useReloadCurrentPopulationMonitorings } from '@screens/monitoring/hooks/use-reload-current-population-monitorings';
import { useSubmitMonitoringApi } from '@screens/monitoring/hooks/use-submit-monitoring-api';
import { AddEditSurvivalRateModal } from '@screens/pond/components/modals/add-edit-survival-rate-modal';
import { getRangeFromWeightDistribution } from '@screens/population/helpers/weight-distribution';
import { exportAsXLSX } from '@utils/files';
import { convertUnitByDivision, formatNumber } from '@utils/number';
import { actionsName } from '@utils/segment';
import { EnumMonitoringProcessingStatus, Monitoring } from '@xpertsea/module-farm-sdk';
import filter from 'lodash/filter';
import find from 'lodash/find';
import lowerCase from 'lodash/lowerCase';
import map from 'lodash/map';
import orderBy from 'lodash/orderBy';
import startCase from 'lodash/startCase';
import uniqBy from 'lodash/uniqBy';
import { DateTime } from 'luxon';
import { Fragment, ReactNode, useMemo, useState } from 'react';
import { MdArrowDownward, MdMoreHoriz, MdPhoneIphone } from 'react-icons/md';
import snakeCase from 'lodash/snakeCase';
import { generateBiomassValues } from '@screens/population/helpers/population-values';
import { getWeeklyGrowthInfo } from '@components/growth-weekly-tooltip/helpers';
import { AverageWeightChangePopover } from '@screens/data-updater/components/average-weight-change-popover';
import { PopulationHistoryItem } from '@screens/pond/components/pond-overview/helpers/pond-production-chart-helpers';
import isUndefined from 'lodash/isUndefined';
import { HistoryDataItem, ViewHistoryModal } from '@screens/monitoring/components/view-history-modal';
import { TableContainer } from '@components/ui/table-container';
import { MenuButton, MenuContent, MenuItem, MenuRoot } from '@components/ui/menu';
import { PlusOutlined } from '@icons/plus/plus-outlined';
import { InfoFilledIcon } from '@icons/info/info-filled-icon';
import { MinusOutlined } from '@icons/minus/minus-outlined';
import { getDaysSinceDate } from '@screens/notifications/utils/dayDiffUtils';
import keyBy from 'lodash/keyBy';
import { VisionOnlyGuard } from '@components/permission-wrappers/vision-only-guard';
import { MoreHoriz } from '@icons/more-horiz/more-horiz';
import { BaseIconButton } from '@components/base/base-icon-button';
import { useGetMonitoringStrengthValues } from '@screens/monitoring/hooks/use-get-monitoring-strength-values';

type MonitoringListTableProps = {
  isFinishedOnce: boolean;
};

export function MonitoringListTable(props: MonitoringListTableProps) {
  const { isFinishedOnce } = props;

  const { trans } = getTrans();

  const processingStatusText: Partial<Record<EnumMonitoringProcessingStatus, string>> = {
    processing: trans('t_processing'),
    failed: trans('t_processing_failed')
  };

  const lang = useAppSelector((state) => state.app?.lang);
  const user = useAppSelector((state) => state.auth.user);
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);
  const currentPond = useAppSelector((state) => state.farm?.currentPond);
  const currentPopulation = useAppSelector((state) => state.farm?.currentPopulation);
  const currentPopulationMonitorings = useAppSelector((state) => state.farm?.currentPopulationMonitorings);
  const currentPopulationHistoryInfo = useAppSelector((state) => state.farm?.currentPopulationHistoryInfo);

  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const { name: pondName, _id: pondId } = currentPond;
  const { timezone, name: currentFarmName, metadata } = currentFarm ?? {};
  const isVisionOnly = metadata?.productOffering === 'visionOnly';
  const {
    history,
    stockedAt,
    survivalRate,
    seedingQuantity,
    cycle: populationCycle,
    _id: currentPopulationId,
    partialHarvest,
    estimatedSurvival,
    manualAverageWeights
  } = currentPopulation ?? {};

  const lastHistory = history?.[0];
  const lastHistoryDate = lastHistory?.date;
  const hasEstimatedValues = !!estimatedSurvival?.[lastHistoryDate];

  const [monitoringIdToView, setMonitoringIdToView] = useState<string>();
  const [expandedHistoryIndex, setExpandedHistoryIndex] = useState<number>();
  const [isOpenViewHistoryModal, setIsOpenViewHistoryModal] = useState(false);
  const [historyDataToView, setHistoryDataToView] = useState<HistoryDataItem>();
  const [isOpenViewMonitoringModal, setIsOpenViewMonitoringModal] = useState(false);
  const [isSelectedMonitorManual, setIsSelectedMonitorManual] = useState(false);
  const [monitoringIdToSubmit, setMonitoringIdToSubmit] = useState<string>();

  const { reloadCurrentPopulationMonitorings } = useReloadCurrentPopulationMonitorings();
  const [{ isLoading: isLoadingSubmitMonitoring }, submitMonitoring] = useSubmitMonitoringApi();

  const { trackAction } = useAnalytics();

  const monitoringsHashMap = useMemo(
    () => keyBy(currentPopulationMonitorings, '_id'),
    [JSON.stringify(currentPopulationMonitorings)]
  );

  const transformedHistory = useMemo(() => {
    // Get all the monitorings from the monitorings hash map that is not part of the history yet
    // based on the createdAt of the monitoringsHashMap item

    // When the history is empty, we will
    // use the currentPopulationMonitorings
    let monitorings = currentPopulationMonitorings;

    if (history?.length) {
      monitorings = Object.values(monitoringsHashMap).filter((monitoring) => {
        return !history.find((historyItem) => {
          // Check if the createdAt of the monitoring is the same as the date of
          // the history item using luxon DateTime
          const historyDate = historyItem?.date?.includes('GMT')
            ? new Date(historyItem.date).toISOString()
            : historyItem?.date;
          const historyItemLuxonDate = DateTime.fromISO(historyDate, { zone: timezone }).startOf('day');
          const monitoringLuxonDate = DateTime.fromISO(monitoring?.startedAt ?? monitoring?.createdAt, {
            zone: monitoring?.startedAtTimezone ?? timezone
          }).startOf('day');
          return historyItemLuxonDate.toISODate() === monitoringLuxonDate.toISODate();
        });
      });
    }

    // Remove the duplicate monitorings based on the
    // createdAt of the monitoring using lodash uniqBy
    const uniqMonitorings = uniqBy(monitorings, (monitoring) => {
      return DateTime.fromISO(monitoring?.startedAt ?? monitoring.createdAt, {
        zone: monitoring?.startedAtTimezone ?? timezone
      }).toISODate();
    });

    const newHistory = uniqMonitorings.map((uniqMonitoring) => {
      const uniqMonitoringLuxonDate = DateTime.fromISO(uniqMonitoring?.startedAt ?? uniqMonitoring?.createdAt, {
        zone: uniqMonitoring?.startedAtTimezone ?? timezone
      }).startOf('day');

      const monitoringIds = map(
        filter(monitorings, (monitoring) => {
          const monitoringLuxonDate = DateTime.fromISO(monitoring?.startedAt ?? monitoring?.createdAt, {
            zone: monitoring?.startedAtTimezone ?? timezone
          });
          return monitoringLuxonDate.toISODate() === uniqMonitoringLuxonDate.toISODate();
        }),
        '_id'
      );

      return {
        date: uniqMonitoringLuxonDate.toISODate(),
        averageWeight: undefined,
        weightCv: undefined,
        total: undefined,
        weightDistribution: undefined,
        monitoringIds
      } as PopulationHistoryItem;
    });

    // Sort the history by date in descending order
    return orderBy(
      [...(history ?? []), ...newHistory],
      (historyItem) => DateTime.fromISO(historyItem.date).toMillis(),
      'desc'
    );
  }, [monitoringsHashMap, history]);

  const dataForViewMonitoringModal = useMemo<Partial<ViewMonitoringModalProps>>(() => {
    const monitoring = monitoringsHashMap[monitoringIdToView];

    const { _id, mlResult, createdAt, startedAt, metadata } = monitoring ?? {};
    const date = startedAt ?? createdAt;

    return {
      mlResult,
      metadata,
      monitoringId: _id,
      monitoringStartDate: date,
      monitoringDistanceToPond: monitoring?.distanceToPond
    };
  }, [monitoringsHashMap, monitoringIdToView]);

  const onSubmitMonitoring = (monitoringId: string) => {
    setMonitoringIdToSubmit(monitoringId);

    submitMonitoring({
      params: { monitoringId, force: true },
      successCallback: () => {
        setMonitoringIdToSubmit(undefined);
        reloadCurrentPopulationMonitorings();
      }
    });
  };

  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);
  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');

  const onDownloadMonitorings = () => {
    const isVisionOnlyGuardedColumns = isVisionOnly
      ? []
      : [trans('t_survival_live'), trans('t_biomass_in_pond_unit', { unit: unitLabel })];
    let columnHeaders = [
      trans('t_days_of_culture'),
      trans('t_date'),
      trans('t_abw_g'),
      trans('t_growth'),
      trans('t_dispersion_cv'),
      ...isVisionOnlyGuardedColumns,
      trans('t_animals')
    ];

    // Format header text
    columnHeaders = columnHeaders.map((header) => startCase(lowerCase(header)));

    const monitorings = transformedHistory.map((historyItem) => {
      const { date, weightCv, total, averageWeight } = historyItem ?? {};
      const historyLuxonDate = DateTime.fromFormat(date, 'yyyy-MM-dd', { zone: timezone }).startOf('day');

      // Get history info for the current history item
      const historyInfoData = currentPopulationHistoryInfo?.filter((historyInfoItem) => {
        const { date: historyInfoDate } = historyInfoItem;
        const historyInfoLuxonDate = DateTime.fromFormat(historyInfoDate, 'yyyy-MM-dd', { zone: timezone }).startOf(
          'day'
        );

        return historyLuxonDate.toISODate() === historyInfoLuxonDate.toISODate();
      });

      const weeklyGrowth = find(historyInfoData, { type: 'weeklyGrowth' });

      const historyItemSurvivalRate = survivalRate?.[date];

      const { biomassLbs } = generateBiomassValues({
        averageWeight,
        seedingQuantity,
        survivalRate: historyItemSurvivalRate
      });

      const stockedDaysDiff = getDaysDiffBetweenDates({
        dateToCompare: stockedAt,
        baseDate: historyLuxonDate.toString()
      });

      let growth;
      if (weeklyGrowth?.info) {
        const lastWeekGrowth = formatNumber(weeklyGrowth.info?.lastWeekGrowth, { fractionDigits: 2, lang });
        growth = trans('t_last_week_growth', { lastWeekGrowth });
      }

      let survival;
      if (historyItemSurvivalRate)
        survival = formatNumber(historyItemSurvivalRate, { lang, fractionDigits: 0, isPercentage: true });

      let biomassFormatted;
      if (biomassLbs) {
        biomassFormatted = `${formatNumber(biomassLbs, {
          lang,
          fractionDigits: 0
        })} lb`;
      }

      if (isVisionOnly) {
        return {
          days: stockedDaysDiff >= 0 ? stockedDaysDiff : 0,
          date: historyLuxonDate.toFormat('dd LLL yyyy', { locale: lang }),
          weight: formatNumber(averageWeight, { fractionDigits: 2, lang }),
          growth: growth ?? '-',
          dispersion: formatNumber(weightCv, { lang, fractionDigits: 0, isPercentage: true }),
          animals: formatNumber(total, { lang, fractionDigits: 0 }) ?? '-'
        };
      }

      return {
        days: stockedDaysDiff >= 0 ? stockedDaysDiff : 0,
        date: historyLuxonDate.toFormat('dd LLL yyyy', { locale: lang }),
        weight: formatNumber(averageWeight, { fractionDigits: 2, lang }),
        growth: growth ?? '-',
        dispersion: formatNumber(weightCv, { lang, fractionDigits: 0, isPercentage: true }),
        survival: survival ?? '-',
        biomass: biomassFormatted ?? '-',
        animals: formatNumber(total, { lang, fractionDigits: 0 }) ?? '-'
      };
    });

    exportAsXLSX({
      columnHeaders,
      columnWidth: 30,
      list: monitorings,
      fileName: snakeCase(
        `${currentFarmName.slice(0, 8)}_${pondName.slice(0, 20)}_${DateTime.local().toFormat('yyyy-MM-dd')}`.slice(
          0,
          30
        )
      )
    });
  };

  const onRowClick = (monitoring: Monitoring) => {
    if (!monitoring) return;
    setMonitoringIdToView(monitoring._id);
    setIsOpenViewMonitoringModal(true);
  };

  return (
    <Flex
      borderRadius='xl'
      bg='white'
      p='md'
      gap='md'
      direction='column'
      position='relative'
      data-cy='pond-monitorings-list'
    >
      <ViewMonitoringModal
        isOpen={isOpenViewMonitoringModal}
        onClose={() => setIsOpenViewMonitoringModal(false)}
        {...(dataForViewMonitoringModal as ViewMonitoringModalProps)}
      />
      <ViewHistoryModal
        history={historyDataToView}
        isOpen={isOpenViewHistoryModal}
        onClose={() => {
          setHistoryDataToView(undefined);
          setIsOpenViewHistoryModal(false);
          setIsSelectedMonitorManual(false);
        }}
        manuallyMonitored={isSelectedMonitorManual}
      />
      <Flex align='center' justify='space-between' gap='md'>
        <Text size='label100'>{trans('t_monitoring')}</Text>
        <MenuRoot>
          <MenuButton>
            <MoreHoriz />
          </MenuButton>
          <MenuContent>
            <MenuItem value='download' onClick={onDownloadMonitorings}>
              {trans('t_download')}
            </MenuItem>
          </MenuContent>
        </MenuRoot>
      </Flex>

      {hasEstimatedValues && (
        <Text size='label300' color='text.gray.disabled'>
          {trans('t_using_imputed_values')}
        </Text>
      )}

      <TableContainer>
        <Table.Root>
          <Table.Header>
            <Table.Row border='none'>
              <TableHeaderFlexCell title={trans('t_date')}>
                <Icon as={MdArrowDownward} fontSize='lg' />
              </TableHeaderFlexCell>

              <TableHeaderTextCell>{trans('t_days_of_culture')}</TableHeaderTextCell>

              <TableHeaderTextCell>{trans('t_abw_g')}</TableHeaderTextCell>

              <TableHeaderTextCell>{trans('t_growth')}</TableHeaderTextCell>

              <TableHeaderFlexCell title={trans('t_dispersion_cv')}>
                <CVLearningDrawer>
                  <InfoFilledIcon
                    onClick={() => {
                      trackAction(actionsName.monitoringListDispersionInfoClicked, {
                        addCurrentPopulation: true
                      }).then();
                    }}
                    w='20px'
                    h='20px'
                    _hover={{ opacity: 0.8 }}
                  />
                </CVLearningDrawer>
              </TableHeaderFlexCell>

              <TableHeaderTextCell>{trans('t_distribution')}</TableHeaderTextCell>
              <VisionOnlyGuard>
                <TableHeaderFlexCell title={trans('t_survival_live')}>
                  <InfoIconToolTip position='bottom'>{trans('t_survival_rate_tooltip')}</InfoIconToolTip>
                </TableHeaderFlexCell>

                <TableHeaderFlexCell title={trans('t_biomass_in_pond_unit', { unit: unitLabel })}>
                  <InfoIconToolTip position='bottom'>{trans('t_biomass_tooltip')}</InfoIconToolTip>
                </TableHeaderFlexCell>
              </VisionOnlyGuard>

              <TableHeaderTextCell>{trans('t_monitoring_strength')}</TableHeaderTextCell>

              <Table.ColumnHeader border='none' p='0' />
            </Table.Row>
          </Table.Header>

          {!isFinishedOnce && (
            <Table.Body data-cy='table-skeleton-loader'>
              <Table.Row>
                <Table.Cell px={[0, 'md']} whiteSpace={'nowrap'} colSpan={10} borderBottomWidth={0}>
                  <Skeleton h='40px' mb='md' />
                  <Skeleton h='40px' mb='md' />
                  <Skeleton h='40px' mb='md' />
                  <Skeleton h='40px' mb='md' />
                  <Skeleton h='40px' mb='md' />
                  <Skeleton h='40px' mb='md' />
                  <Skeleton h='40px' mb='md' />
                  <Skeleton h='40px' mb='md' />
                </Table.Cell>
              </Table.Row>
            </Table.Body>
          )}

          {isFinishedOnce && !transformedHistory?.length && (
            <Table.Body data-cy='table-has-no-data' id='table-data-results'>
              <Table.Row>
                <Table.Cell colSpan={10}>{trans('t_no_monitoring_available')}</Table.Cell>
              </Table.Row>
            </Table.Body>
          )}

          {isFinishedOnce && !!transformedHistory?.length && (
            <Table.Body data-cy='table-data'>
              {transformedHistory.map((historyItem, historyItemIndex) => {
                const { date, averageWeight, monitoringIds, weightDistribution, modelAverageWeight, weightCv } =
                  historyItem;
                const historyDate = date.includes('GMT') ? new Date(date).toISOString() : date;
                const historyDateLuxon = DateTime.fromISO(historyDate, { zone: timezone }).startOf('day');

                // Get history info for the current history item
                const historyInfoData = currentPopulationHistoryInfo?.filter((historyInfoItem) => {
                  const { date } = historyInfoItem;
                  const historyInfoDate = date.includes('GMT') ? new Date(date).toISOString() : date;
                  const historyInfoLuxonDate = DateTime.fromISO(historyInfoDate, { zone: timezone }).startOf('day');

                  return historyDateLuxon.toISODate() === historyInfoLuxonDate.toISODate();
                });

                const weeklyGrowth = find(historyInfoData, { type: 'weeklyGrowth' });

                const newHistoryInfoData = history?.find((historyInfoItem) => {
                  const { date } = historyInfoItem;
                  const historyInfoDate = date.includes('GMT') ? new Date(date).toISOString() : date;
                  const historyInfoLuxonDate = DateTime.fromISO(historyInfoDate, { zone: timezone }).startOf('day');

                  return historyDateLuxon.toISODate() === historyInfoLuxonDate.toISODate();
                });

                // Get monitorings ids from monitoringsHashMap which are not inside the
                // monitoringIds based on the date from the historyItem
                const otherMonitoringsIds = Object.keys(monitoringsHashMap).filter((monitoringId) => {
                  const monitoring = monitoringsHashMap[monitoringId];
                  const { startedAt, createdAt, startedAtTimezone } = monitoring ?? {};

                  const monitoringDate = startedAt ?? createdAt;
                  const monitoringLuxonDate = DateTime.fromISO(monitoringDate, {
                    zone: startedAtTimezone ?? timezone
                  }).startOf('day');

                  return (
                    historyDateLuxon.toISODate() === monitoringLuxonDate.toISODate() &&
                    !monitoringIds.includes(monitoringId)
                  );
                });

                // Combine monitoringIds and otherMonitoringsIds
                let historyMonitoringsIds = [...monitoringIds, ...otherMonitoringsIds];

                // Remove ids from historyMonitoringsIds which are not
                // inside the monitoringsHashMap
                historyMonitoringsIds = historyMonitoringsIds.filter(
                  (monitoringId) => !!monitoringsHashMap[monitoringId]
                );

                // Sort historyMonitoringsIds by startedAt or createdAt
                historyMonitoringsIds = orderBy(
                  historyMonitoringsIds,
                  (historyMonitoringId) => {
                    const historyMonitoring = monitoringsHashMap[historyMonitoringId];

                    const { startedAt, createdAt, startedAtTimezone } = historyMonitoring ?? {};
                    const date = startedAt ?? createdAt;

                    return DateTime.fromISO(date, { zone: startedAtTimezone ?? timezone }).toMillis();
                  },
                  'asc'
                );

                // Get the monitoring id for the given history which is
                // processing or failed
                const failedOrProcessingMonitoringId: string = historyMonitoringsIds.find((monitoringId) => {
                  const monitoring = monitoringsHashMap[monitoringId];
                  const { processingStatus } = monitoring;

                  return processingStatus === 'failed' || processingStatus === 'processing';
                });
                const failedOrProcessingMonitoring = monitoringsHashMap?.[failedOrProcessingMonitoringId];

                const historyItemSurvivalRate = survivalRate?.[date];

                const latestMonitoring = monitoringsHashMap[historyMonitoringsIds[historyMonitoringsIds.length - 1]];
                const latestMonitoringDate = latestMonitoring?.startedAt ?? latestMonitoring?.createdAt;

                const latestMonitoringLuxon = DateTime.fromISO(latestMonitoringDate ?? historyDate, {
                  zone: latestMonitoring?.startedAtTimezone ?? timezone
                });
                const manuallyMonitored = latestMonitoring?.metadata?.manuallyMonitored;

                const latestMonitoringDaysFromTodayText = getDaysSinceDate({
                  date: latestMonitoringLuxon.toISODate(),
                  timezone: latestMonitoring?.startedAtTimezone ?? timezone,
                  lang
                });

                const { biomassLbs } = generateBiomassValues({
                  averageWeight,
                  seedingQuantity,
                  survivalRate: historyItemSurvivalRate
                });

                const isExpanded = expandedHistoryIndex === historyItemIndex;
                const hasMoreThenOneMonitoring = historyMonitoringsIds.length > 1;
                const { lastWeeklyGrowthDays } = weeklyGrowth?.info ?? {};
                const { weeklyGrowth: lastWeekGrowth, quality, strength, completeness } = newHistoryInfoData || {};

                const { lastGrowth } = getWeeklyGrowthInfo({ lastWeeklyGrowthDays, lastWeekGrowth });
                const manualAverageWeight = manualAverageWeights?.find(
                  (item) => item.date === historyDateLuxon.toISODate()
                );
                const monitoringScore: HistoryDataItem['score'] = { quality, strength, completeness };

                return (
                  <Fragment key={historyItemIndex}>
                    <Table.Row
                      userSelect='none'
                      position='relative'
                      {...(expandedHistoryIndex !== null &&
                        expandedHistoryIndex + 1 === historyItemIndex && {
                          boxShadow: '0px -2px 4px rgba(0, 0, 0, 0.15)'
                        })}
                      {...(isExpanded && { boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.15)' })}
                      onClick={(event) => {
                        if (event.target instanceof SVGElement) return;
                        event.stopPropagation();
                        trackAction(actionsName.monitoringListItemClicked, { addCurrentPopulation: true }).then();
                        setHistoryDataToView({
                          ...historyItem,
                          score: monitoringScore
                        });
                        setIsOpenViewHistoryModal(true);
                        setIsSelectedMonitorManual(manuallyMonitored);
                      }}
                      css={{
                        '&:hover .highcharts-background': { fill: 'gray.50' }
                      }}
                      _hover={{
                        cursor: 'pointer',
                        bgColor: 'gray.50'
                      }}
                      data-cy={monitoringIds[0]}
                    >
                      <Table.Cell>
                        <Flex flexDirection='column' gap='2xs' textAlign='center'>
                          <Text size='light100' data-cy='days-from-today'>
                            {latestMonitoringDaysFromTodayText}
                          </Text>
                          <Text color='gray.500' size='light200' data-cy='monitoring-date'>
                            {latestMonitoringLuxon.toFormat('dd MMM', { locale: lang })}
                          </Text>
                        </Flex>
                      </Table.Cell>

                      <Table.Cell>
                        <FarmDaysBetweenMonitoringAndStocking
                          isOnlyNumber
                          stockedAt={stockedAt}
                          farmTimezone={timezone}
                          monitoredAt={date}
                          data-cy='days-from-stocking'
                          pb='lg'
                          size='light100'
                          textAlign='center'
                        />
                      </Table.Cell>

                      {!!failedOrProcessingMonitoring && (
                        <Table.Cell colSpan={7}>
                          <Flex gap='xs' align='center' pb='lg' textAlign='center'>
                            <Text size='light100'>
                              {processingStatusText[failedOrProcessingMonitoring.processingStatus]}
                            </Text>
                            {failedOrProcessingMonitoring.processingStatus === 'failed' && (
                              <InfoIconToolTip>
                                <Flex gap='sm' flexDirection='column'>
                                  <Text>{trans('t_processing_failed_tooltip_title')}</Text>
                                  <Text>{trans('t_processing_failed_tooltip_subtitle')}</Text>
                                </Flex>
                              </InfoIconToolTip>
                            )}
                          </Flex>
                        </Table.Cell>
                      )}

                      {!failedOrProcessingMonitoring && (
                        <>
                          <Table.Cell position='relative' textAlign='center'>
                            <Flex gap='2xs' justify='center'>
                              {!!manualAverageWeight?.averageWeight && (
                                <AverageWeightChangePopover
                                  updatedBy={manualAverageWeight?.updatedBy}
                                  modelAverageWeight={modelAverageWeight}
                                />
                              )}
                              <ABWInvalidDiv population={currentPopulation} weight={averageWeight}>
                                <WeightCell
                                  lang={lang}
                                  averageWeight={averageWeight}
                                  weightDistribution={weightDistribution}
                                />
                              </ABWInvalidDiv>
                            </Flex>
                          </Table.Cell>

                          <Table.Cell>
                            {!!weeklyGrowth?.info && (
                              <GrowthWeeklyToolTip
                                lastWeeklyGrowthDays={lastWeeklyGrowthDays}
                                lastWeekGrowth={lastWeekGrowth}
                              >
                                <Text textAlign='center' pb='lg' size='light100' data-cy='growth-since-last-monitoring'>
                                  {formatNumber(lastGrowth, { fractionDigits: 2, lang })}
                                  {trans('t_g_in_days', { days: lastWeeklyGrowthDays })}
                                </Text>
                              </GrowthWeeklyToolTip>
                            )}
                            {!weeklyGrowth?.info && (
                              <Text textAlign='center' pb='lg'>
                                -
                              </Text>
                            )}
                          </Table.Cell>

                          <Table.Cell position='relative' data-cy='weight-cv'>
                            {manuallyMonitored && (
                              <Text pb='lg' textAlign='center'>
                                -
                              </Text>
                            )}
                            {!manuallyMonitored && <CvCell weightCv={weightCv} />}
                          </Table.Cell>
                          <Table.Cell ps={!!weightDistribution && !manuallyMonitored ? 0 : 'lg'}>
                            {(!weightDistribution || manuallyMonitored) && (
                              <Text pb='lg' textAlign='center'>
                                -
                              </Text>
                            )}
                            {!!weightDistribution && !manuallyMonitored && (
                              <WeightDistributionChart
                                isPlain
                                height={manualAverageWeight ? 120 : 100}
                                width={250}
                                wd={weightDistribution}
                                averageWeight={averageWeight}
                                manualAverageWeight={manualAverageWeight}
                                customConfig={{ size: 'sm', barColor: '#C4C4C4' }}
                                modelAverageWeight={modelAverageWeight}
                              />
                            )}
                          </Table.Cell>
                          <VisionOnlyGuard>
                            <Table.Cell
                              data-cy='survival-rate'
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                              }}
                            >
                              <AddEditSurvivalRateModal
                                pondId={pondId}
                                isEdit={historyItemSurvivalRate}
                                populationId={currentPopulationId}
                                pondName={`${pondName}.${populationCycle}`}
                                lastMonitoringDate={historyDateLuxon.toISODate()}
                                survivalRate={parsePercent(historyItemSurvivalRate)}
                                partialHarvest={partialHarvest}
                                seedingQuantity={seedingQuantity}
                                isInPondView
                              >
                                <SurvivalRateButton
                                  hasEstimatedValue={estimatedSurvival?.[date]}
                                  survivalRate={historyItemSurvivalRate}
                                  containerStyle={{
                                    pb: historyItemSurvivalRate ? 'lg' : 'xs'
                                  }}
                                />
                              </AddEditSurvivalRateModal>
                            </Table.Cell>

                            <Table.Cell>
                              <Text textAlign='center' size='light100' pb='lg'>
                                {!biomassLbs && '-'}
                                {!!biomassLbs &&
                                  formatNumber(convertUnitByDivision(biomassLbs, unitsConfig?.biomass), {
                                    lang,
                                    fractionDigits: 0
                                  })}
                              </Text>
                            </Table.Cell>
                          </VisionOnlyGuard>
                          <Table.Cell pos='relative' data-cy='monitoring-strength'>
                            <Box pb='lg'>
                              <MonitoringStrengthCell monitoringScore={monitoringScore} />
                            </Box>
                          </Table.Cell>
                        </>
                      )}
                      <Table.Cell
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                        }}
                      >
                        <Flex
                          flexDirection='column'
                          align='flex-end'
                          justify='center'
                          onClick={(e) => {
                            e.stopPropagation();
                            e.stopPropagation();
                          }}
                          pb='lg'
                        >
                          {!hasMoreThenOneMonitoring && !!failedOrProcessingMonitoring && (
                            <BaseButton
                              size='sm'
                              minW='84px'
                              height='28px'
                              cursor='pointer'
                              variant='outline'
                              bgColor='gray.50'
                              fontWeight='normal'
                              justifySelf='flex-end'
                              _hover={{ bgColor: 'gray.100' }}
                              analyticsId={actionsName.reSubmitMonitoringClicked}
                              onClick={(event) => {
                                event.preventDefault();
                                event.stopPropagation();
                                onSubmitMonitoring(historyMonitoringsIds[0]);
                              }}
                              loading={isLoadingSubmitMonitoring && historyMonitoringsIds[0] === monitoringIdToSubmit}
                            >
                              {trans('t_try_again')}
                            </BaseButton>
                          )}
                          {hasMoreThenOneMonitoring && (
                            <BaseIconButton
                              variant='link'
                              px='xs'
                              onClick={(event) => {
                                if (event.target instanceof HTMLButtonElement) return;
                                event.stopPropagation();
                                event.stopPropagation();
                                setExpandedHistoryIndex(isExpanded ? undefined : historyItemIndex);
                              }}
                            >
                              {isExpanded && <MinusOutlined />}
                              {!isExpanded && <PlusOutlined />}
                            </BaseIconButton>
                          )}
                          {!hasMoreThenOneMonitoring && historyItemIndex === 0 && (
                            <MenuRoot closeOnSelect={false}>
                              <MenuButton
                                variant='link'
                                data-cy='action-menu-button'
                                py='0'
                                px='xs'
                                onClick={(e) => {
                                  e.stopPropagation();
                                  e.stopPropagation();
                                }}
                              >
                                <MoreHoriz />
                              </MenuButton>
                              <MenuContent>
                                <MonitoringTableActions monitoringId={historyMonitoringsIds[0]}>
                                  <MenuItem
                                    value='delete'
                                    data-cy='delete-monitoring-btn'
                                    onClick={() => {
                                      trackAction(actionsName.deleteMonitoringClicked, { pondId, pondName }).then();
                                    }}
                                  >
                                    {trans('t_delete_monitoring')}
                                  </MenuItem>
                                </MonitoringTableActions>
                              </MenuContent>
                            </MenuRoot>
                          )}
                        </Flex>
                      </Table.Cell>
                    </Table.Row>

                    {historyItemIndex === expandedHistoryIndex && hasMoreThenOneMonitoring && (
                      <>
                        {historyMonitoringsIds.map((monitoringId, monitoringItemIndex) => {
                          const monitoring = monitoringsHashMap[monitoringId];
                          const {
                            manualAverageWeight,
                            startedAt,
                            createdAt,
                            startedAtTimezone,
                            mlResult,
                            processingStatus
                          } = monitoring ?? {};
                          const manuallyMonitored = monitoring?.metadata?.manuallyMonitored;

                          const {
                            averageWeight,
                            weightDistribution: monitoringWeightDistribution,
                            weightCv: monitoringWeightCv,
                            score
                          } = mlResult ?? {};

                          const monitoringAverageWeight = manualAverageWeight ?? averageWeight;
                          const { isInvalid: isInvalidAverageWeight } = isInvalidABW(monitoringAverageWeight);
                          const date = startedAt ?? createdAt;
                          const startedAtTz = DateTime.fromISO(date, { zone: startedAtTimezone ?? timezone });
                          const formattedStartedAtDate = startedAtTz.toFormat('HH:mm', { locale: lang });

                          const isMonitoringFailedOrProcessing =
                            processingStatus === 'failed' || processingStatus === 'processing';

                          if (processingStatus === 'pending') return null;

                          const subMonitoringManualWeight = manualAverageWeights?.find(
                            (item) => item.date === startedAtTz.toISODate()
                          );
                          return (
                            <Table.Row
                              key={monitoringItemIndex}
                              onClick={(event) => {
                                if (event.target instanceof HTMLButtonElement) return;

                                onRowClick(monitoring);
                              }}
                              css={{
                                '& .highcharts-background': { display: 'none' },
                                '&:hover .highcharts-background': { fill: 'gray.50' }
                              }}
                              userSelect='none'
                              _hover={{
                                cursor: 'pointer',
                                bgColor: 'gray.50'
                              }}
                              {...(historyMonitoringsIds.length - 1 === monitoringItemIndex && {
                                css: { td: { borderColor: 'none' } }
                              })}
                            >
                              <Table.Cell />
                              <Table.Cell>
                                <Flex justify='center' align='center' gap='xs-alt'>
                                  <Box as={MdPhoneIphone} fontSize='xl' color='gray.500' />
                                  <Text color='gray.500' fontSize='lg'>
                                    {formattedStartedAtDate}
                                  </Text>
                                </Flex>
                              </Table.Cell>
                              {isMonitoringFailedOrProcessing && (
                                <>
                                  <Table.Cell colSpan={7}>
                                    <Text color='gray.500' size='light100'>
                                      {processingStatusText[processingStatus] ?? '-'}
                                    </Text>
                                  </Table.Cell>

                                  {processingStatus === 'failed' && (
                                    <Table.Cell
                                      textAlign='right'
                                      onClick={(e) => {
                                        e.preventDefault();
                                        e.stopPropagation();
                                      }}
                                    >
                                      <BaseButton
                                        size='sm'
                                        minW='84px'
                                        height='28px'
                                        cursor='pointer'
                                        variant='outline'
                                        bgColor='gray.50'
                                        fontWeight='normal'
                                        justifySelf='flex-end'
                                        _hover={{ bgColor: 'gray.100' }}
                                        analyticsId={actionsName.reSubmitMonitoringClicked}
                                        onClick={(e) => {
                                          e.preventDefault();
                                          e.stopPropagation();
                                          onSubmitMonitoring(monitoringId);
                                        }}
                                        loading={isLoadingSubmitMonitoring && monitoringId === monitoringIdToSubmit}
                                      >
                                        {trans('t_try_again')}
                                      </BaseButton>
                                    </Table.Cell>
                                  )}
                                </>
                              )}
                              {!isMonitoringFailedOrProcessing && (
                                <>
                                  <Table.Cell>
                                    <Text textAlign='center' color='gray.500' size='light100'>
                                      {!monitoringAverageWeight && '-'}
                                      {!!monitoringAverageWeight &&
                                        formatNumber(monitoringAverageWeight, {
                                          lang,
                                          fractionDigits: 2
                                        })}
                                    </Text>
                                  </Table.Cell>
                                  <Table.Cell>
                                    <Text textAlign='center' color='gray.500' size='light100'>
                                      -
                                    </Text>
                                  </Table.Cell>
                                  <Table.Cell color='gray.500' fontSize='lg'>
                                    <Text textAlign='center' color='gray.500' size='light100'>
                                      {!monitoringWeightCv || isInvalidAverageWeight || manuallyMonitored
                                        ? '-'
                                        : formatNumber(monitoringWeightCv, {
                                            lang,
                                            fractionDigits: 0,
                                            isPercentage: true
                                          })}
                                    </Text>
                                  </Table.Cell>
                                  <Table.Cell>
                                    {(!monitoringWeightDistribution || manuallyMonitored) && <Text pb='lg'>-</Text>}
                                    {!!monitoringWeightDistribution && !manuallyMonitored && (
                                      <WeightDistributionChart
                                        isPlain
                                        height={subMonitoringManualWeight ? 120 : 100}
                                        width={250}
                                        key={`${pondId}-${monitoringId}`}
                                        wd={monitoringWeightDistribution}
                                        averageWeight={monitoringAverageWeight}
                                        customConfig={{ size: 'sm', barColor: '#C4C4C4' }}
                                        modelAverageWeight={modelAverageWeight}
                                        manualAverageWeight={subMonitoringManualWeight}
                                      />
                                    )}
                                  </Table.Cell>
                                  <Table.Cell>
                                    <Text textAlign='center' color='gray.500' size='light100'>
                                      -
                                    </Text>
                                  </Table.Cell>
                                  {!isMonitoringFailedOrProcessing && (
                                    <Table.Cell>
                                      <Text textAlign='center' color='gray.500' size='light100'>
                                        -
                                      </Text>
                                    </Table.Cell>
                                  )}
                                  <Table.Cell>
                                    <MonitoringStrengthCell color='gray.500' monitoringScore={score} />
                                  </Table.Cell>
                                </>
                              )}
                              {processingStatus !== 'failed' && (
                                <Table.Cell
                                  textAlign='right'
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                  }}
                                >
                                  {historyItemIndex === 0 && (
                                    <MenuRoot closeOnSelect={false}>
                                      <MenuButton
                                        variant='link'
                                        onClick={(e) => {
                                          e.stopPropagation();
                                        }}
                                      >
                                        <Icon as={MdMoreHoriz} w='24px' h='24px' color='black' />
                                      </MenuButton>
                                      <MenuContent
                                        color='black'
                                        boxShadow='0px 8px 20px -4px rgba(23, 24, 24, 0.12), 0px 3px 6px -3px rgba(23, 24, 24, 0.08)'
                                      >
                                        <MonitoringTableActions
                                          monitoringId={monitoringId}
                                          onSuccess={() => {
                                            reloadCurrentPopulationMonitorings();
                                            // If there is only one monitoring left, close the expanded row
                                            if (historyMonitoringsIds.length === 2 && isExpanded) {
                                              setExpandedHistoryIndex(null);
                                            }
                                          }}
                                        >
                                          <MenuItem
                                            value='delete'
                                            data-cy='delete-monitoring-btn'
                                            onClick={() => {
                                              trackAction(actionsName.deleteMonitoringClicked, {
                                                pondId,
                                                pondName
                                              }).then();
                                            }}
                                          >
                                            {trans('t_delete_monitoring')}
                                          </MenuItem>
                                        </MonitoringTableActions>
                                      </MenuContent>
                                    </MenuRoot>
                                  )}
                                </Table.Cell>
                              )}
                            </Table.Row>
                          );
                        })}
                      </>
                    )}
                  </Fragment>
                );
              })}
            </Table.Body>
          )}
        </Table.Root>
      </TableContainer>
    </Flex>
  );
}

type WeightCellProps = {
  lang: string;
  averageWeight: number;
  weightDistribution: number[];
};

function WeightCell(props: WeightCellProps) {
  const { lang, averageWeight, weightDistribution } = props;

  const { minWeight, maxWeight } = useMemo(() => {
    const { min: minWeight, max: maxWeight } = getRangeFromWeightDistribution(weightDistribution, 0.95, lang) ?? {};

    return { minWeight, maxWeight };
  }, [weightDistribution, lang]);

  return (
    <Flex flexDirection='column' gap='2px' position='relative'>
      <Text size='light100' {...(!minWeight && !maxWeight && { pb: 'lg' })}>
        {averageWeight ? formatNumber(averageWeight, { fractionDigits: 2, lang }) : '-'}
      </Text>
      {!!minWeight && !!maxWeight && (
        <Flex gap='2px' color='gray.500'>
          <Text size='light200'>{formatNumber(minWeight, { fractionDigits: 2, lang })}</Text>
          <Text size='light200'>-</Text>
          <Text size='light200'>{formatNumber(maxWeight, { fractionDigits: 2, lang })}</Text>
        </Flex>
      )}
    </Flex>
  );
}

interface MonitoringStrengthCellProps extends TextProps {
  monitoringScore: HistoryDataItem['score'];
}

function MonitoringStrengthCell(props: MonitoringStrengthCellProps) {
  const { monitoringScore, ...rest } = props;
  const { trans } = getTrans();
  const { strengthValues, qualityValues, completenessTitles } = useGetMonitoringStrengthValues();

  const strengthData = strengthValues[monitoringScore?.strength];
  const qualityTitle = qualityValues[monitoringScore?.quality]?.title;
  const completenessTitle = completenessTitles[monitoringScore?.completeness];
  if (!strengthData?.title) {
    return (
      <Text textAlign='center' size='light100' {...rest}>
        -
      </Text>
    );
  }
  return (
    <InfoIconToolTip
      position='bottom'
      hasArrow={false}
      triggerComponentProps={{ boxSize: '100%', display: 'flex', justifyContent: 'center', ms: '0' }}
      triggerComponent={
        <Text
          size='label200'
          textAlign='center'
          rounded='full'
          py='2xs'
          px='xs'
          w='max-content'
          bgColor={strengthData?.bgColor}
          color={strengthData?.color}
        >
          {strengthData?.title}
        </Text>
      }
    >
      <Text>{qualityTitle ? `${trans('t_quality')}: ${qualityTitle} ` : '-'}</Text>
      <Text>{completenessTitle ? `${trans('t_completeness')}: ${completenessTitle} ` : '-'}</Text>
    </InfoIconToolTip>
  );
}

function TableHeaderTextCell({ children }: { children: string }) {
  return (
    <Table.ColumnHeader border='none' textAlign='center' px='0' pt='0' pb='sm-alt'>
      <Text bgColor='gray.100' py='sm' px='xs' borderRadius='xl' mx='2xs'>
        {children}
      </Text>
    </Table.ColumnHeader>
  );
}
function TableHeaderFlexCell({ children, title }: { children: ReactNode; title: string }) {
  return (
    <Table.ColumnHeader border='none' px='0' pt='0' pb='sm-alt'>
      <Flex gap='xs' justify='center' align='center' bgColor='gray.100' py='sm' px='xs' borderRadius='xl' mx='2xs'>
        <Text>{title}</Text>

        {children}
      </Flex>
    </Table.ColumnHeader>
  );
}

type CvCellProps = {
  weightCv: number;
};

function CvCell(props: CvCellProps) {
  const { weightCv } = props;

  const lang = useAppSelector((state) => state.app.lang);

  return (
    <Text size='light100' pb='lg' textAlign='center'>
      {weightCv ? formatNumber(weightCv, { lang, fractionDigits: 0, isPercentage: true }) : '-'}
    </Text>
  );
}
