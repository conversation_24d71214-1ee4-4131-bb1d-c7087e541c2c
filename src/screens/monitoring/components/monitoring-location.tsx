import { Tray } from '@xpertsea/module-farm-sdk';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { useEffect, useState } from 'react';
import { formatNumber, isNumber } from '@utils/number';
import { CardContainer } from '@components/card-container/card-container';
import { Box, Flex, Text } from '@chakra-ui/react';
import { CloseFilled } from '@icons/close/close-filled';
import { Expand } from '@icons/expand/expand';
import dynamic from 'next/dynamic';
import { convertGpsDistance } from '@utils/gps';

const PolygonMap = dynamic(
  () => import('@components/polygon-map/polygon-map').then((mod) => ({ default: mod.PolygonMap })),
  {
    ssr: false,
    loading: () => (
      <Box height='300px' bg='gray.100' display='flex' alignItems='center' justifyContent='center'>
        Loading map...
      </Box>
    )
  }
);

type MonitoringLocationProps = {
  photosExpanded: boolean;
  trays: Tray[];
  monitoringsDistanceToPond: number[];
};

export function MonitoringLocation({ photosExpanded, trays, monitoringsDistanceToPond }: MonitoringLocationProps) {
  const { trans } = getTrans();
  const lang = useAppSelector((state) => state.app.lang);
  const [isExpanded, setIsExpanded] = useState(false);
  const [mapKey, setMapKey] = useState(0);

  const geoPolygon = useAppSelector((state) => state.farm?.currentPond?.geoPolygon);

  useEffect(() => {
    // Force map to re-render after container size change (both expand and collapse)
    setTimeout(() => {
      setMapKey((prev) => prev + 1);
    }, 100);
  }, [isExpanded]);

  // Hide the map when photos are expanded
  if (photosExpanded || !geoPolygon?.coordinates?.length) {
    return null;
  }

  const markerPositions = trays
    .filter((tray) => tray?.coordinate?.coordinates?.length >= 2)
    .map((tray) => {
      const coords = tray.coordinate.coordinates;
      const lat = coords[0];
      const lng = coords[1];
      const distanceToPond = tray?.distanceToPond;
      return {
        latitude: lat,
        longitude: lng,
        distanceToPond: distanceToPond > 0 ? formatNumber(distanceToPond, { lang, fractionDigits: 2 }) : ''
      };
    });

  return (
    <CardContainer
      p='md'
      minH='max-content'
      {...(isExpanded && {
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1000,
        height: '100%',
        rounded: 'none'
      })}
      flex={1}
    >
      <Flex align='center' justify='space-between' mb='md' id='gps-location'>
        <Flex align='center' gap='md-alt'>
          <Text size='label100'>{trans('t_monitoring_location')}</Text>
        </Flex>
        <Box
          cursor='pointer'
          onClick={() => {
            setIsExpanded(!isExpanded);
          }}
        >
          {isExpanded ? <CloseFilled w='24px' h='24px' /> : <Expand w='24px' h='24px' />}
        </Box>
      </Flex>
      <PolygonMap
        key={mapKey}
        coordinates={
          geoPolygon?.coordinates?.map((coord: number[]) => ({
            latitude: coord[0]?.toString() || '',
            longitude: coord[1]?.toString() || ''
          })) || []
        }
        markerPositions={markerPositions?.map((markerPosition, index) => {
          const convertedDistance = convertGpsDistance(+markerPosition.distanceToPond);
          const convertedDistanceToPond = convertedDistance ? convertedDistance.distance : null;
          const convertedDistanceToPondUnit = convertedDistance ? convertedDistance.unit : null;
          const lastMonitoringDistanceFormatted = isNumber(convertedDistanceToPond)
            ? formatNumber(Math.abs(convertedDistanceToPond), { lang, fractionDigits: 2 })
            : null;

          return {
            latitude: markerPosition.latitude,
            longitude: markerPosition.longitude,
            tooltip:
              +markerPosition?.distanceToPond > 0
                ? `${trans('t_gps_distance')}: ${lastMonitoringDistanceFormatted}${convertedDistanceToPondUnit}`
                : `${trans('t_gps_distance')}: ${trans('t_near_pond')}`,
            color: ['#e74c3c', '#3498db', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c', '#e67e22', '#34495e'][index % 8],
            size: 'small'
          };
        })}
        height={isExpanded ? 'calc(100vh - 120px)' : '300px'}
      />
      {monitoringsDistanceToPond?.map((monitoringDistanceToPond, index) => {
        const convertedDistance = convertGpsDistance(monitoringDistanceToPond);
        if (!convertedDistance) {
          return;
        }

        const formattedDistance = isNumber(convertedDistance.distance)
          ? formatNumber(convertedDistance.distance, { lang, fractionDigits: 2 })
          : null;

        if (monitoringsDistanceToPond?.length === 1) {
          return (
            <Text key={index} size='label200' color='black' mt='md'>
              {monitoringDistanceToPond > 0
                ? trans('t_monitoring_distance_to_pond', {
                    distance: formattedDistance,
                    unit: convertedDistance.unit
                  })
                : trans('t_monitoring_is_near_pond')}
            </Text>
          );
        }

        return (
          <Text key={index} size='label200' color='black' mt='md'>
            {monitoringDistanceToPond > 0
              ? trans('t_monitoring_x_distance_to_pond', {
                  x: index + 1,
                  distance: formattedDistance,
                  unit: convertedDistance.unit
                })
              : trans('t_monitoring_x_is_near_pond', { x: index + 1 })}
          </Text>
        );
      })}
    </CardContainer>
  );
}
