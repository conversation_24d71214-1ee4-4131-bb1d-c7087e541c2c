import { Box, Flex } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { DaysSinceDate } from '@components/farm-days-since-monitored/days-since-date';
import { FarmDaysBetweenMonitoringAndStocking } from '@components/farm-days-since-stocked/farm-days-between-monitoring-and-stocking';
import { useEnableTechnicalFeatures } from '@hooks/use-is-xpertsea-user';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { appLinks } from '@utils/app-links';
import { AiOutlineEye } from 'react-icons/ai';
import { CalendarFilledIcon } from '@icons/calendar/calendar-filled-icon';

interface MonitoringInfoBarProps {
  monitoringId?: string;
  totalAnimals: number;
  monitoredAt: string;
}

export function MonitoringInfoBar(props: MonitoringInfoBarProps) {
  const { monitoringId, totalAnimals, monitoredAt } = props;

  const { currentPopulation, currentFarm } = useAppSelector((state) => state.farm);
  const { stockedAt } = currentPopulation ?? {};
  const manuallyMonitored = currentPopulation?.metadata?.manuallyMonitored;
  const { timezone: farmTimezone } = currentFarm ?? {};
  const enableTechnicalFeatures = useEnableTechnicalFeatures();
  const backofficeURL = `${appLinks('backoffice')}/monitoring/view?id=${monitoringId}`.toLowerCase();

  return (
    <Flex alignItems='center' gap='md-alt' textStyle='label.200' flexWrap='wrap'>
      <DaysSinceDate date={monitoredAt} timezone={farmTimezone} />
      <Flex align='center' gap='sm-alt'>
        <CalendarFilledIcon />
        <FarmDaysBetweenMonitoringAndStocking
          stockedAt={stockedAt}
          farmTimezone={farmTimezone}
          monitoredAt={monitoredAt}
        />
      </Flex>
      {!manuallyMonitored && <AnimalsComponent totalAnimals={totalAnimals} />}
      {enableTechnicalFeatures && !!monitoringId && (
        <Flex flex='1' alignContent='start' flexDirection='row-reverse'>
          <a href={backofficeURL} target='_blank' rel='noopener noreferrer'>
            <BaseButton variant='outline'>
              <AiOutlineEye fontSize='1.25em' /> View in Backoffice
            </BaseButton>
          </a>
        </Flex>
      )}
    </Flex>
  );
}

function AnimalsComponent(props: { totalAnimals: number }) {
  const { totalAnimals } = props;
  const { trans } = getTrans();

  return (
    <Box as='span' textTransform='lowercase' data-cy='total-animals'>
      {totalAnimals} {trans('t_animals')}
    </Box>
  );
}
