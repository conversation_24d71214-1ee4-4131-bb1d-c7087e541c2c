import { Population } from '@xpertsea/module-farm-sdk';
import { Flex, Text } from '@chakra-ui/react';
import { ReactNode } from 'react';
import { BaseLink } from '@components/base/base-link';
import { BaseButton } from '@components/base/base-button';
import { getTrans } from '@i18n/get-trans';
import { getSymmetryAlertText } from '@screens/monitoring/helpers/alert-description';
import { SymmetryTooltipContainer } from '@components/pond-card/symmetry-tooltip/symmetry-tooltip-container';

export function SymmetryTooltip({
  symmetry,
  totalAnimals,
  children
}: {
  symmetry: Population['symmetry'];
  totalAnimals: number;
  children?: ReactNode;
}) {
  const { trans } = getTrans();

  const { isRed, isGreen, isYellow } = symmetry?.alert || {};
  const hasAlert = isRed || isGreen || isYellow;

  if (!symmetry?.type || !hasAlert) {
    return <>{children}</>;
  }

  const alertInfo = getSymmetryAlertText(symmetry);

  return (
    <SymmetryTooltipContainer
      position='left-start'
      triggerProps={{ flexDir: 'column' }}
      trigger={
        <>
          {children}
          {(isYellow || isRed) && <Text color='text.semanticRed'>{alertInfo.title}</Text>}
        </>
      }
    >
      <Flex flexDir='column' gap='md'>
        {!!alertInfo.title && <Text fontWeight='bold'>{alertInfo.title}</Text>}
        <Text>{alertInfo.info}</Text>
        {(isYellow || isRed) && (
          <BaseLink route='https://www.xpertsea.com/blog/how-to-fix-an-asymmetric-distribution' target='_blank'>
            <BaseButton w='100%'>{trans('t_show_me_how')}</BaseButton>
          </BaseLink>
        )}
      </Flex>
    </SymmetryTooltipContainer>
  );
}
