import { Box, Flex, FlexProps, Text, TextProps, ThemeProps } from '@chakra-ui/react';
import { useAnalytics } from '@hooks/use-analytics';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { CVLearningDrawer } from '@screens/farm/components/cv-learning-drawer';
import { getAlertDescription } from '@screens/monitoring/helpers/alert-description';
import { getRangeFromWeightDistribution } from '@screens/population/helpers/weight-distribution';
import { formatNumber, isNumber } from '@utils/number';
import { actionsName } from '@utils/segment';
import { MonitoringMlResult, MonitoringMlResultScore, PopulationManualAverageWeights } from '@xpertsea/module-farm-sdk';
import { DateTime } from 'luxon';
import { ReactNode, useMemo } from 'react';
import { MdInfo } from 'react-icons/md';
import { SymmetryTooltip } from '@screens/monitoring/components/symmetry-tooltip';
import { EditIcon } from '@icons/edit/edit-icon';
import { goToUrl } from '@utils/url';
import { slugify } from '@utils/string';
import { AverageWeightChangePopover } from '@screens/data-updater/components/average-weight-change-popover';
import { InfoIconToolTip } from '@components/tooltip/info-icon-tool-tip';
import { useGetMonitoringStrengthValues } from '@screens/monitoring/hooks/use-get-monitoring-strength-values';

interface PondMonitoringCardsProps {
  mlResult: MonitoringMlResult;
  lastHistoryDate: string;
  manualAverageWeight: PopulationManualAverageWeights;
  manuallyMonitored?: boolean;
}

export function PondMonitoringCards(props: PondMonitoringCardsProps) {
  const { manualAverageWeight, mlResult, lastHistoryDate, manuallyMonitored } = props;
  const { trans } = getTrans();
  const appLanguage = useAppSelector((state) => state.app.lang);
  const { currentPopulation, currentFarmSlug, currentPond } = useAppSelector((state) => state.farm);
  const { growthBands, growthTarget, seedingAverageWeight } = currentPopulation ?? {};

  const { averageWeight, weightCv, weightDistribution, total, score } = mlResult ?? {};
  const manualAverageWeightValue = manualAverageWeight?.averageWeight;
  const averageWeightValue = Number(averageWeight ?? seedingAverageWeight);
  const { description: labelABW, labelColor } = getAlertDescription(growthBands?.alert.caseNumber);

  const { isRedTarget, isYellowTarget, isInvalidABW, invalidValue, isGreenTarget, weightRange } = useMemo(() => {
    const { rangeValue: weightRange } = getRangeFromWeightDistribution(weightDistribution, 0.95, appLanguage) ?? {};
    const { isRed: isRedTarget, isYellow: isYellowTarget, isGreen: isGreenTarget } = growthBands?.alert ?? {};

    const isInvalidABW = averageWeightValue > 40 || averageWeightValue < 2;
    const abwInvalidString = averageWeight > 40 ? '>40' : '<2';
    const invalidValue = isInvalidABW ? abwInvalidString : null;
    return {
      isRedTarget,
      isYellowTarget,
      isInvalidABW,
      invalidValue,
      isGreenTarget,
      weightRange
    };
  }, [weightCv, growthBands, appLanguage]);

  if (!currentPopulation) return null;
  const isGreenBgColor: ThemeProps['bgColor'] = isGreenTarget ? 'border.brandGreen' : undefined;
  const isYellowBgColor: ThemeProps['bgColor'] = isYellowTarget ? 'border.semanticYellow' : isGreenBgColor;
  const invalidBgColor: ThemeProps['bgColor'] = isRedTarget ? 'border.semanticRed' : isYellowBgColor;
  const invalidAbwValue =
    invalidValue || averageWeightValue
      ? invalidValue || formatNumber(averageWeightValue, { fractionDigits: 2, lang: appLanguage })
      : '-';

  return (
    <Flex flexDirection={{ base: 'column', lg: 'row' }} gap='2xs'>
      <CardContainer>
        <AbwInfoDiv isInvalid={isInvalidABW}>
          <CardTitle>{trans('t_abw_g')}</CardTitle>
        </AbwInfoDiv>
        <Box>
          <Flex align='center' justify='space-between'>
            <CardHeader data-cy='average-weight'>
              <Box
                borderTopRadius='md'
                h='8px'
                pos='absolute'
                top='-1px'
                left='0'
                right='0'
                marginStart='-1px'
                marginEnd='-1px'
                bgColor={isInvalidABW ? undefined : growthTarget?.weight && invalidBgColor}
              />
              <Flex align='center' gap='2xs'>
                {!!manualAverageWeightValue && (
                  <AverageWeightChangePopover
                    modelAverageWeight={averageWeightValue}
                    updatedBy={manualAverageWeight?.updatedBy}
                  />
                )}
                <Text>
                  {isNumber(manualAverageWeightValue)
                    ? formatNumber(manualAverageWeightValue, { fractionDigits: 2, lang: appLanguage })
                    : invalidAbwValue}
                </Text>
              </Flex>
            </CardHeader>
            <EditIcon
              cursor='pointer'
              onClick={() => {
                goToUrl({
                  route: `/farm/[farmEid]/data-updater`,
                  params: {
                    farmEid: currentFarmSlug,
                    pondEid: slugify(`${currentPond.eid}-${currentPond.name}`),
                    date: lastHistoryDate,
                    view: 'weightData'
                  }
                });
              }}
            />
          </Flex>
          {!isInvalidABW && weightRange && (
            <Text as='span' color='textSecondary'>
              {weightRange}
            </Text>
          )}
        </Box>

        {!isInvalidABW &&
          ((isRedTarget && !!growthTarget?.weight) ||
            (isYellowTarget && !!growthTarget?.weight) ||
            (isGreenTarget && !!growthTarget?.weight)) && <Text color={labelColor}>{labelABW}</Text>}
      </CardContainer>
      {!manuallyMonitored && (
        <CardContainer>
          <CVCard averageWeightValue={averageWeight} weightCv={weightCv} isInvalidABW={isInvalidABW} />
        </CardContainer>
      )}
      <SymmetryCard monitoringStartDate={lastHistoryDate} total={total} />
      {!manuallyMonitored && <MonitoringStrengthCard monitoringScore={score} />}
    </Flex>
  );
}

function CardHeader(props: TextProps) {
  return <Text as='h2' size='heavy100' {...props} />;
}

function CardTitle(props: TextProps) {
  return <Text color='text.gray.weak' {...props} />;
}

function CardContainer(props: FlexProps) {
  return (
    <Flex
      bg='white'
      p='md'
      border='1px solid'
      borderColor='gray.200'
      borderRadius='8px'
      flexDirection='column'
      gap='xs'
      pos='relative'
      h='100%'
      whiteSpace='normal'
      flex={1}
      {...props}
    />
  );
}

function MonitoringStrengthCard(props: { monitoringScore?: MonitoringMlResultScore }) {
  const { monitoringScore } = props;
  const { trans } = getTrans();
  const { strength, quality, completeness } = monitoringScore ?? {};
  const { strengthValues, qualityValues, completenessTitles, strengthDescription } = useGetMonitoringStrengthValues();

  if (!strength) {
    return null;
  }

  const strengthData = strengthValues[strength];
  const qualityTitle = qualityValues[quality]?.title;
  const completenessTitle = completenessTitles[completeness];
  const description = strengthDescription[`${strength}-${quality}-${completeness}`];
  return (
    <CardContainer>
      <Box h='8px' pos='absolute' top='0' left='0' right='0' borderTopRadius='md' bgColor={strengthData?.borderColor} />
      <Flex align='center' gap='2xs'>
        <CardTitle>{trans('t_monitoring_strength')}</CardTitle>
        <Text
          size='label200'
          textAlign='center'
          rounded='full'
          py='2xs'
          px='xs'
          w='max-content'
          bgColor={strengthData?.bgColor}
          color={strengthData?.color}
        >
          {strengthData?.title ?? '-'}
        </Text>
      </Flex>

      <Flex
        align={{ base: 'flex-start', lg: 'center' }}
        gap={{ base: '0', lg: 'xl-alt' }}
        direction={{ base: 'column', lg: 'row' }}
      >
        {qualityTitle && (
          <Text>
            {trans('t_quality')}:{' '}
            <Text as='span' fontWeight={700}>
              {qualityTitle}
            </Text>
          </Text>
        )}
        {completenessTitle && (
          <Text>
            {trans('t_completeness')}:{' '}
            <Text as='span' fontWeight={700}>
              {completenessTitle}
            </Text>
          </Text>
        )}
      </Flex>
      {description && <Text>{description}</Text>}
    </CardContainer>
  );
}

function CVCard(props: { averageWeightValue: number; weightCv: number; isInvalidABW: boolean }) {
  const { weightCv, isInvalidABW } = props;
  const { trans } = getTrans();
  const { trackAction } = useAnalytics();
  const lang = useAppSelector((state) => state.app.lang);

  const { currentPond } = useAppSelector((state) => state.farm);
  const { _id: pondId, name: pondName } = currentPond ?? {};
  return (
    <>
      <Flex align='center' justify='space-between'>
        <CardTitle>{trans('t_dispersion_cv')}</CardTitle>
        <CVLearningDrawer>
          <Box
            as={MdInfo}
            fontSize='lg'
            opacity='0.5'
            color='gray.700'
            _hover={{ opacity: '0.8' }}
            onClick={() => {
              trackAction(actionsName.cvInfoClicked, { pondId, pondName, placement: 'pond-details-cv-card' }).then();
            }}
          />
        </CVLearningDrawer>
      </Flex>
      <CardHeader data-cy='distribution-cv'>
        {!!weightCv && !isInvalidABW ? formatNumber(weightCv, { lang, fractionDigits: 0, isPercentage: true }) : '-'}
      </CardHeader>
    </>
  );
}

function AbwInfoDiv(props: { isInvalid?: boolean; children?: ReactNode }) {
  const { isInvalid, children } = props ?? {};
  const { trans } = getTrans();

  if (isInvalid) {
    return (
      <InfoIconToolTip contentProps={{ maxW: '400px' }}>
        <Text whiteSpace='initial'>{trans('t_invalid_weight')}</Text>
      </InfoIconToolTip>
    );
  }

  return children;
}

function SymmetryCard({ monitoringStartDate, total }: { monitoringStartDate?: string; total: number }) {
  const { currentPopulationHistoryInfo } = useAppSelector((state) => state.farm);

  const monitoringDate = DateTime.fromISO(monitoringStartDate).toFormat('yyyy-MM-dd');
  const symmetryData = currentPopulationHistoryInfo?.find(
    (ele) => ele.type === 'symmetry' && ele.date === monitoringDate
  );
  const { info: symmetry } = symmetryData ?? {};

  if (!symmetry) return null;

  const { trans } = getTrans();

  const { type = '-', alert } = symmetry;

  const typeText = {
    left: trans('t_left'),
    right: trans('t_right'),
    t_normal: trans('t_normal')
  };

  const { isRed, isGreen, isYellow } = alert ?? {};
  const isGreenColor: ThemeProps['bgColor'] = isGreen ? 'border.brandGreen' : undefined;
  const isYellowColor: ThemeProps['bgColor'] = isYellow ? 'border.semanticYellow' : isGreenColor;
  const isRedColor: ThemeProps['bgColor'] = isRed ? 'border.semanticRed' : isYellowColor;

  return (
    <CardContainer>
      <Box h='8px' pos='absolute' top='0' left='0' right='0' borderTopRadius='md' bgColor={isRedColor} />

      <SymmetryTooltip symmetry={symmetry} totalAnimals={total}>
        <Box>
          <CardTitle>{trans('t_symmetry')}</CardTitle>
          <CardHeader textTransform='capitalize'>{typeText[type as keyof typeof typeText] || type}</CardHeader>
        </Box>
      </SymmetryTooltip>
    </CardContainer>
  );
}
