import { Dispatch, SetStateAction, useMemo, useState } from 'react';
import { useAppSelector } from '@redux/hooks';
import { useEnableTechnicalFeatures } from '@hooks/use-is-xpertsea-user';
import { useAnalytics } from '@hooks/use-analytics';
import { getTrans } from '@i18n/get-trans';
import { ImageCarousel, ImageCarouselProps } from '@components/image-carousel/image-carousel';
import { EmptyState } from '@components/errors/empty-state';
import { Box, Flex, Text } from '@chakra-ui/react';
import { formatNumber, isNumber } from '@utils/number';
import { AnnotateTrayImageModal } from '@screens/monitoring/components/annotate-tray-image-modal';
import { ImageEditIcon } from '@icons/image-edit-icon';
import { actionsName } from '@utils/segment';
import { CloseFilled } from '@icons/close/close-filled';
import { Expand } from '@icons/expand/expand';
import { useGetMonitoringStrengthValues } from '@screens/monitoring/hooks/use-get-monitoring-strength-values';
import { Tray } from '@xpertsea/module-farm-sdk';

export function TrayCarousel(props: {
  trays: Tray[];
  isExpanded?: boolean;
  setIsExpanded: Dispatch<SetStateAction<boolean>>;
}) {
  const { isExpanded, setIsExpanded, trays } = props;

  const lang = useAppSelector((state) => state.app.lang);
  const currentPond = useAppSelector((state) => state.farm.currentPond);
  const { _id: pondId, name: pondName } = currentPond;

  const isEnableTechnicalFeatures = useEnableTechnicalFeatures();
  const { qualityValues } = useGetMonitoringStrengthValues();

  const [currentTrayIndex, setCurrentTrayIndex] = useState(0);

  const { trackAction } = useAnalytics();
  const { trans } = getTrans();

  const currentDomain = window.location.hostname.split('.').slice(-2).join('.');
  const traySlides = useMemo<ImageCarouselProps['slides']>(() => {
    if (!trays.length) return;

    const slideImages: ImageCarouselProps['slides'] = [];

    trays.forEach((tray, trayIndex) => {
      // allow kampi urls to be used in the carousel images as we use the cookies
      // for the main domain while the url in the backend includes different domains
      // so the cookie cannot be authenticated
      const image = tray.images?.[0];
      if (!currentDomain?.includes('localhost')) {
        image.imageUrl = image.imageUrl.replace(/(https?:\/\/)[^/]+/, `$1${currentDomain}`);
      }

      slideImages.push({
        imageUrl: image.imageUrl,
        imageAlt: trans('t_photo_count', { count: trayIndex + 1 })
      });
    });

    return slideImages;
  }, [trays]);

  if (!trays.length) return <EmptyState />;

  const selectedTray = trays[currentTrayIndex];
  const currentQuality = qualityValues[selectedTray?.mlResult?.score?.quality];
  const qualityTitle = currentQuality?.title;
  const totalAnimals = selectedTray?.mlResult?.total;
  const goodCrop = selectedTray?.mlResult?.goodCrop;

  return (
    <>
      <Flex align='center' justify='space-between' mb='2lg'>
        <Flex align='center' gap='md-alt'>
          <Text size='label100'>{trans('t_photos')}</Text>
          {isNumber(totalAnimals) && (
            <Text size='light300' color='text.gray.weak'>
              {trans('t_total_animals_detected', {
                total: formatNumber(totalAnimals, { lang, fractionDigits: 0 })
              })}
            </Text>
          )}
        </Flex>
        <Flex align='center' gap='2sm'>
          {!isExpanded && isEnableTechnicalFeatures && (
            <AnnotateTrayImageModal tray={selectedTray}>
              <ImageEditIcon firstSectionColor='gray.400' secondSectionColor='gray.800' />
            </AnnotateTrayImageModal>
          )}
          <Box
            cursor='pointer'
            onClick={() => {
              trackAction(actionsName.monitoringPhotoExpandClicked, { isExpanded, addCurrentPopulation: true });
              setIsExpanded(!isExpanded);
            }}
          >
            {isExpanded ? <CloseFilled w='24px' h='24px' /> : <Expand w='24px' h='24px' />}
          </Box>
        </Flex>
      </Flex>
      {qualityTitle && (
        <Flex align='center' gap='2xs' mb='md'>
          {trans('t_quality')}:{' '}
          <Text
            size='label200'
            textAlign='center'
            rounded='full'
            py='2xs'
            px='xs'
            w='max-content'
            bgColor={currentQuality?.bgColor}
            color={currentQuality?.color}
          >
            {qualityTitle}
          </Text>
        </Flex>
      )}
      <ImageCarousel
        slides={traySlides}
        isExpanded={isExpanded}
        setIsExpanded={setIsExpanded}
        onSlideMove={(value) => {
          setCurrentTrayIndex(value);
          trackAction(actionsName.imageCarouselChanged).then();
        }}
        onSlideClick={(imageSlide) => {
          const { imageUrl, imageAlt } = imageSlide ?? {};
          trackAction(actionsName.imageCarouselClicked, { pondName, pondId, imageUrl, imageAlt }).then();
          if (!isExpanded) setIsExpanded(true);
        }}
      />
      {isNumber(goodCrop) && isNumber(totalAnimals) && (
        <Text size='label200' mt='md'>
          {trans('t_x_usual_shrimps_for_weights_out_x_counted', {
            total: formatNumber(totalAnimals, { lang, fractionDigits: 0 }),
            good: formatNumber(goodCrop, { lang, fractionDigits: 0 })
          })}
        </Text>
      )}
    </>
  );
}
