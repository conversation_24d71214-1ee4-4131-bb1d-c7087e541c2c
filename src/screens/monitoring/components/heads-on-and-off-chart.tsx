import { getTrans } from '@i18n/get-trans';
import { WeightDistributionChartProps } from '@screens/monitoring/components/weight-distribution-chart';
import { weightDistributionToBucketBins } from '@screens/monitoring/helpers/weight-distribution';
import { type Options } from 'highcharts';
import { useEffect, useMemo, useRef } from 'react';
import { useAppSelector } from '@redux/hooks';
import {
  CategorySize,
  defaultPercentTail,
  generateHeadlessSizeLookup,
  headonSizesLookup
} from '@screens/settings/constants/default-price-list';
import { formatNumber } from '@utils/number';
import { renderToString } from 'react-dom/server';
import { HighchartsNextComp, HighchartsRef } from '@components/base/highcharts-next-comp';
import { useToken } from '@chakra-ui/react';

interface HeadsOnAndOffChartProps
  extends Omit<WeightDistributionChartProps, 'modelAverageWeight' | 'manualAverageWeight'> {
  type: 'headsOn' | 'headsOff';
  buckets?: CategorySize;
  isReversed?: boolean;
  width?: number;
  percentTail?: number;
}

export function HeadsOnAndOffChart(props: HeadsOnAndOffChartProps) {
  const {
    wd = [],
    height = 400,
    width,
    type = 'headsOn',
    isPlain,
    customConfig = {},
    isReversed = false,
    buckets: bucketsFromConfig,
    percentTail = defaultPercentTail
  } = props;

  const [gray200, gray400, gray800, graphBrandBlue, brandBlue700] = useToken('colors', [
    'gray.200',
    'gray.400',
    'gray.800',
    'graphBrandBlue',
    'brandBlue.700'
  ]);
  const [fontFamily] = useToken('fonts', ['body']);

  const { trans } = getTrans();
  const title = type === 'headsOn' ? trans('t_heads_on') : trans('t_heads_off');
  const lang = useAppSelector((state) => state.app.lang);
  const { datasets, bucketsCount, bucketKeys } = useMemo(() => {
    const buckets =
      bucketsFromConfig || (type === 'headsOn' ? headonSizesLookup : generateHeadlessSizeLookup(percentTail));
    const bucketKeys = Object.keys(buckets);
    const bucketsCount = bucketKeys.length;
    const transformedData = weightDistributionToBucketBins({
      wd,
      buckets: buckets
    });

    return {
      datasets: [...(transformedData?.data ?? [])],
      buckets,
      bucketsCount,
      bucketKeys
    };
  }, [wd, type, bucketsFromConfig]);

  const {
    barColor,
    hideYTitle = isPlain,
    hideTitle = isPlain ?? true,
    hideDataLabel = isPlain,
    hideSubTitle = isPlain ?? true,
    hideLegendLabel = isPlain
  } = customConfig;
  const chartComponent = useRef<HighchartsRef>(null);

  const defaultGrayColor = gray800;
  const defaultTextStyle = {
    fontSize: '10px',
    fontWeight: '600',
    color: defaultGrayColor,
    lineHeight: '12px'
  };

  const options: Options = {
    chart: {
      spacing: [6, 1, -1, -6],
      style: {
        fontFamily
      },
      animation: false,
      height,
      width
    },
    plotOptions: {
      column: {
        borderRadius: 6,
        pointPadding: 0,
        borderWidth: 1,
        groupPadding: 0,
        shadow: false,
        dataLabels: {
          padding: 2,
          enabled: !hideDataLabel,
          style: defaultTextStyle,
          formatter: function () {
            const { custom } = this as unknown as {
              custom: { x: string; y: number; cp: number; wp: string; c: number };
            };
            const { cp } = custom;
            return `${formatNumber(cp, { lang, fractionDigits: cp < 1 ? 1 : 0, shouldAddZeros: false })}%`;
          }
        }
      }
    },
    tooltip: {
      padding: 0,
      shadow: false,
      useHTML: true,
      borderRadius: 8,
      backgroundColor: gray200,
      formatter: function (this): string {
        const { custom } = this as unknown as {
          custom: { x: string; y: number; cp: number; wp: string; c: number };
        };
        const { x, cp } = custom ?? {};

        return renderToString(
          <div
            style={{
              padding: '10px 16px',
              fontSize: '12px',
              fontWeight: 600,
              backgroundColor: gray200,
              borderRadius: '8px',
              minWidth: '138px',
              lineHeight: '16px',
              color: defaultGrayColor
            }}
          >
            {x}: {formatNumber(cp, { lang, fractionDigits: 1 })}%
          </div>
        );
      }
    },
    title: { text: !hideTitle ? title : undefined },
    subtitle: {
      text: !hideSubTitle ? `Sample size = ${wd.length}` : undefined
    },
    xAxis: {
      tickWidth: 0,
      lineWidth: 0,
      tickInterval: 0,
      gridLineWidth: 0,
      endOnTick: true,
      startOnTick: true,
      type: 'category',
      reversed: isReversed,
      categories: bucketKeys,
      showEmpty: true,
      max: bucketsCount - 1,
      min: 0,
      title: { text: trans('t_size'), style: defaultTextStyle, margin: 10 },
      labels: { style: defaultTextStyle, distance: 10 }
    },
    yAxis: {
      tickWidth: 0,
      lineWidth: 0,
      gridLineWidth: 0.5,
      gridLineColor: gray400,
      gridLineDashStyle: 'LongDash',
      title: !hideYTitle
        ? {
            style: defaultTextStyle,
            text: trans('t_animals_%'),
            margin: 10
          }
        : undefined,
      labels: {
        distance: 10,
        style: defaultTextStyle,
        formatter(this): string {
          return `${formatNumber(this.value as number, { lang, fractionDigits: 0 })}%`;
        }
      }
    },
    series: [
      {
        showInLegend: !hideLegendLabel,
        name: trans('t_weight_g'),
        type: 'column',
        borderRadius: 6,
        color: barColor ?? graphBrandBlue,
        zoneAxis: 'x',
        animation: false,
        events: {
          click: function (e) {
            e.preventDefault();
          }
        },
        data: datasets?.map((p) => {
          return { name: p.x, y: p.y, custom: p };
        }),
        borderWidth: 1,
        states: barColor
          ? {}
          : {
              hover: {
                color: {
                  linearGradient: {
                    x1: 0,
                    x2: 0,
                    y1: 0,
                    y2: 1
                  },
                  stops: [
                    [0, brandBlue700],
                    [1, graphBrandBlue]
                  ]
                }
              }
            }
      }
    ]
  };
  const optionStringify = JSON.stringify(options ?? []);

  useEffect(() => {
    if (!chartComponent?.current?.chart) return;
    chartComponent.current.chart.redraw();
  }, [optionStringify, chartComponent?.current]);

  if (!datasets?.length) {
    return null;
  }

  return (
    <HighchartsNextComp
      ref={chartComponent}
      options={options}
      key={optionStringify}
      containerProps={{ style: { width: '100%', height: height ? `${height}px` : undefined } }}
    />
  );
}
