import { useDisclosure } from '@chakra-ui/react';
import { useAppSelector } from '@redux/hooks';
import { MonitoringDetails } from '@screens/monitoring/components/monitoring-details';
import { MonitoringInfoBar } from '@screens/monitoring/components/monitoring-info-bar';
import { getUniDate } from '@utils/date';
import { MonitoringMlResult } from '@xpertsea/module-farm-sdk';
import { DialogBody, DialogContent, DialogHeader, DialogRoot, DialogTitle } from '@components/ui/dialog';

export type ViewMonitoringModalProps = {
  mlResult: MonitoringMlResult;
  monitoringId: string;
  monitoringStartDate: string;
  monitoringDistanceToPond?: number;
  metadata?: Record<string, boolean | number>;
  isOpen: boolean;
  onClose: () => void;
};

export function ViewMonitoringModal(props: ViewMonitoringModalProps) {
  const {
    metadata,
    monitoringId,
    mlResult,
    monitoringStartDate,
    isOpen: isOpenProp,
    onClose,
    monitoringDistanceToPond
  } = props;

  const { open } = useDisclosure({
    open: isOpenProp
  });

  const { currentPopulation, currentPond } = useAppSelector((state) => state.farm);
  const { name } = currentPond;
  const { cycle = '' } = currentPopulation;

  const lastMonitoringDateFormatted = getUniDate({
    fromTimezone: 'UTC',
    date: monitoringStartDate
  }).f2;

  return (
    <DialogRoot
      open={open}
      onOpenChange={(e) => {
        !e.open && onClose();
      }}
      size={{ base: 'full', md: 'cover' }}
      scrollBehavior='inside'
      restoreFocus={false}
    >
      <DialogContent data-cy='monitoring-list-modal' rounded='2xl' bgColor='gray.100'>
        <DialogHeader p='lg' data-cy='monitor-modal-title'>
          <DialogTitle>{cycle ? `${name}.${cycle}` : name}</DialogTitle>
        </DialogHeader>
        <DialogBody display='flex' pt={0} px='lg' pb='lg' flexDirection='column' gap='md'>
          <MonitoringInfoBar
            totalAnimals={mlResult?.total}
            monitoredAt={monitoringStartDate}
            monitoringId={monitoringId}
          />
          <MonitoringDetails
            mlResult={mlResult}
            monitoringId={[monitoringId]}
            metadata={metadata}
            monitoringDistanceToPond={monitoringDistanceToPond}
            lastHistoryDate={lastMonitoringDateFormatted}
          />
        </DialogBody>
      </DialogContent>
    </DialogRoot>
  );
}
