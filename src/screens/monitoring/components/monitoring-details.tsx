import { Box, Flex, FlexProps, Skeleton, Text, useBreakpointValue } from '@chakra-ui/react';
import { EmptyState } from '@components/errors/empty-state';
import { useAnalytics } from '@hooks/use-analytics';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { HeadsOnAndOffChart } from '@screens/monitoring/components/heads-on-and-off-chart';
import { PondMonitoringCards } from '@screens/monitoring/components/pond-monitoring-cards';
import { WeightDistributionChart } from '@screens/monitoring/components/weight-distribution-chart';
import { getRangeFromWeightDistribution } from '@screens/population/helpers/weight-distribution';
import { formatNumber, isNumber } from '@utils/number';
import { actionsName } from '@utils/segment';
import { MonitoringMlResult, PopulationManualAverageWeights } from '@xpertsea/module-farm-sdk';
import { ComponentProps, Dispatch, SetStateAction, useEffect, useMemo, useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { getPriceList } from '@screens/settings/helpers/get-price-list';
import { DateTime } from 'luxon';
import { CardContainer } from '@components/card-container/card-container';
import { InView } from 'react-intersection-observer';
import { BaseMenu } from '@components/base/base-menu';
import { AverageWeightChangePopover } from '@screens/data-updater/components/average-weight-change-popover';
import { TrayCarousel } from '@screens/monitoring/components/tray-carousel';
import { MonitoringLocation } from '@screens/monitoring/components/monitoring-location';
import { useListTraysApi } from '@screens/monitoring/hooks/use-list-trays-api';

type WeightCharts = 'weight' | 'heads-on' | 'heads-off';
interface MonitoringDetailsProps {
  mlResult: MonitoringMlResult;
  monitoringId: string[];
  lastHistoryDate?: string;
  containerStyle?: FlexProps;
  monitoringDistanceToPond?: number;
  metadata?: Record<string, boolean | number>;
}
export function MonitoringDetails(props: MonitoringDetailsProps) {
  const { metadata, mlResult, monitoringId, lastHistoryDate, containerStyle, monitoringDistanceToPond } = props;

  const manuallyMonitored = metadata?.manuallyMonitored;

  const breakPoint = useBreakpointValue({ base: 'mobile', sm: 'iPad' });

  const lang = useAppSelector((state) => state.app.lang);
  const currentPopulation = useAppSelector((state) => state.farm?.currentPopulation);
  const { growthBands, seedingAverageWeight, lastMonitoringDate, manualAverageWeights } = currentPopulation ?? {};
  const { averageWeight, weightCv, weightDistribution } = mlResult ?? {};

  const [isExpanded, setIsExpanded] = useState(false);

  const manualAverageWeight = manualAverageWeights?.find((item) => item.date === lastHistoryDate);

  const [selectedWeightChart, setSelectedWeightChart] = useState<WeightCharts>('weight');

  const averageWeightValue = averageWeight ?? seedingAverageWeight;

  const { minWeight, maxWeight, weightRageDiff, isInvalidABW } = useMemo(() => {
    const { min: minWeight, max: maxWeight } = getRangeFromWeightDistribution(weightDistribution, 0.95, lang) ?? {};
    const weightRageDiff: number | undefined = minWeight && maxWeight ? Math.round(maxWeight - minWeight) : undefined;
    const isInvalidABW = averageWeightValue > 40 || averageWeightValue < 2;

    return {
      minWeight,
      maxWeight,
      weightRageDiff,
      isInvalidABW
    };
  }, [weightDistribution, weightCv, growthBands, lang]);

  const [{ data: traysRes }, listTrays] = useListTraysApi();

  useEffect(() => {
    if (!monitoringId?.length) return;
    listTrays({
      params: {
        filter: { monitoringId },
        page: { current: 1, size: 10000 },
        sort: [{ field: 'createdAt', order: 'desc' }]
      }
    });
  }, [JSON.stringify(monitoringId ?? [])]);

  return (
    <Flex flexDirection='column' gap='sm-alt' {...containerStyle}>
      <PondMonitoringCards
        mlResult={mlResult}
        lastHistoryDate={lastHistoryDate}
        manualAverageWeight={manualAverageWeight}
        manuallyMonitored={!!manuallyMonitored}
      />

      {!isInvalidABW && !manuallyMonitored && (
        <WeightDistributionChartContainer
          key={breakPoint}
          selectedWeightChart={selectedWeightChart}
          setSelectedWeightChart={setSelectedWeightChart}
          mlResult={mlResult}
          minWeight={minWeight}
          maxWeight={maxWeight}
          weightRageDiff={weightRageDiff}
          averageWeightValue={averageWeightValue}
          monitoringDate={lastMonitoringDate}
          modelAverageWeight={averageWeight}
          manualAverageWeight={manualAverageWeight}
        />
      )}

      {!manuallyMonitored && (
        <Flex
          mb='2xl'
          gap={{ base: '2lg', xl: 'sm-alt' }}
          direction={{ base: 'column', lg: 'row' }}
          flexWrap={{ base: 'wrap', xl: 'nowrap' }}
        >
          <InView rootMargin='40px' triggerOnce>
            {({ inView, ref }) => (
              <CardContainer
                p='md'
                minH='max-content'
                {...(isExpanded && {
                  position: 'fixed',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  zIndex: 1000,
                  height: '100%',
                  rounded: 'none'
                })}
                flex={1}
              >
                <div ref={ref}>
                  {!inView && <Skeleton height='100%' minH='300px' w='100%' />}
                  {inView && (
                    <TrayCarousel trays={traysRes?.trays || []} isExpanded={isExpanded} setIsExpanded={setIsExpanded} />
                  )}
                </div>
              </CardContainer>
            )}
          </InView>
          {
            <MonitoringLocation
              monitoringsDistanceToPond={[monitoringDistanceToPond]}
              photosExpanded={isExpanded}
              trays={traysRes?.trays || []}
            />
          }
        </Flex>
      )}
    </Flex>
  );
}

interface WeightDistributionChartContainerProps {
  selectedWeightChart: WeightCharts;
  setSelectedWeightChart: Dispatch<SetStateAction<WeightCharts>>;
  mlResult: MonitoringMlResult;
  monitoringDate?: string;
  minWeight: number;
  maxWeight: number;
  weightRageDiff: number;
  averageWeightValue: number;
  isSimple?: boolean;
  modelAverageWeight: number;
  manualAverageWeight: PopulationManualAverageWeights;
}

export function WeightDistributionChartContainer(props: WeightDistributionChartContainerProps) {
  const {
    mlResult,
    selectedWeightChart,
    setSelectedWeightChart,
    minWeight,
    maxWeight,
    weightRageDiff,
    averageWeightValue,
    isSimple,
    monitoringDate,
    manualAverageWeight,
    modelAverageWeight
  } = props;

  const appLanguage = useAppSelector((state) => state.app.lang);
  const { currentPond, currentPopulation } = useAppSelector((state) => state.farm);

  const { _id: pondId } = currentPond;
  const { farmId, _id: populationId } = currentPopulation;

  const breakPoint = useBreakpointValue({ base: 'mobile', sm: 'iPad' });

  const { trackAction } = useAnalytics();

  const monitoringDateFormatted = monitoringDate
    ? DateTime.fromISO(monitoringDate).toFormat('MMM dd, yyyy', { locale: appLanguage })
    : '';

  const { weightDistribution } = mlResult ?? {};
  const { trans } = getTrans();

  let weightRange = '';
  let grams = '';

  if (minWeight && maxWeight) {
    weightRange = `${formatNumber(minWeight, {
      lang: appLanguage,
      fractionDigits: 2
    })} - ${formatNumber(maxWeight, {
      fractionDigits: 2,
      lang: appLanguage
    })}`;
    grams = formatNumber(weightRageDiff, { fractionDigits: 2, lang: appLanguage }) as string;
  }

  const options = [
    { label: trans('t_weight_distribution'), value: 'weight' },
    { label: trans('t_head_on_distribution'), value: 'heads-on' },
    { label: trans('t_head_less_distribution'), value: 'heads-off' }
  ];

  return (
    <CardContainer data-cy='monitoring-weight-distribution'>
      {isSimple && (
        <Flex gap='md-alt' align='center' mb='lg'>
          <Text size='label100'>{trans('t_monitoring')}</Text>
          <Text size='light300' color='text.gray.weak'>
            {trans('t_last_monitored_on_x', { date: monitoringDateFormatted })}
          </Text>
        </Flex>
      )}
      {!isSimple && (
        <Flex flexDir={['column', 'row', 'row', 'row']} align='flex-start' justify='space-between' gap='md'>
          <Box flex={1}>
            <Flex align='center' justify='space-between' mb={selectedWeightChart === 'weight' ? 'xs-alt' : 'md-alt'}>
              <Flex gap='sm' flexDir={{ base: 'column', sm: 'row' }} align={{ base: 'flex-start', sm: 'center' }}>
                {!!weightDistribution?.length && (
                  <Box minW='100px'>
                    <BaseMenu
                      w='auto'
                      variant='link'
                      px='0px !important'
                      h='unset !important'
                      _active={{ color: 'dark' }}
                      _hover={{ textDecor: 'none' }}
                      selectedOption={options.find((ele) => ele.value === selectedWeightChart)}
                      options={options}
                      onItemSelect={(option) => {
                        trackAction(actionsName.monitoringDistributionSelected, {
                          option: option.value,
                          addCurrentPopulation: true
                        }).then();
                        setSelectedWeightChart(option.value as WeightCharts);
                      }}
                      size={{ base: 'sm', md: 'md' }}
                      css={{
                        '& .chakra-button__icon': { ms: 'xs-alt' },
                        '& span:first-of-type': { fontSize: 'md', color: 'text.gray' }
                      }}
                    />
                  </Box>
                )}
                <Text size='light300' color='text.gray.weak'>
                  {trans('t_last_monitored_on_x', { date: monitoringDateFormatted })}
                </Text>
              </Flex>
              {!!manualAverageWeight?.averageWeight && (
                <AverageWeightChangePopover
                  modelAverageWeight={modelAverageWeight}
                  updatedBy={manualAverageWeight?.updatedBy}
                />
              )}
            </Flex>
            {isNumber(grams) && isNumber(weightRange) && (
              <Text>{trans('t_percentage_sampled_animals', { percent: '95%', grams, range: weightRange })}</Text>
            )}
          </Box>
        </Flex>
      )}

      {!weightDistribution?.length && (
        <Text fontSize='md' mt='sm' color='gray.700'>
          {trans('t_no_data_to_display')}
        </Text>
      )}
      {!!weightDistribution?.length && (
        <Box>
          <ErrorBoundary
            resetKeys={[farmId, pondId, populationId, selectedWeightChart]}
            FallbackComponent={() => (
              <EmptyState title={`${trans('t_oops')}!`} description={trans('t_something_went_wrong')} />
            )}
          >
            {selectedWeightChart === 'weight' && (
              <WeightDistributionChart
                height={280}
                wd={weightDistribution}
                averageWeight={averageWeightValue}
                modelAverageWeight={modelAverageWeight}
                manualAverageWeight={manualAverageWeight}
                customConfig={{
                  size: breakPoint === 'mobile' ? 'sm' : 'md'
                }}
              />
            )}
            {selectedWeightChart === 'heads-on' && (
              <WithPriceList
                height={280}
                key={`${farmId}-${pondId}-${populationId}-${selectedWeightChart}`}
                wd={weightDistribution}
                type={'headsOn'}
              />
            )}
            {selectedWeightChart === 'heads-off' && (
              <WithPriceList
                height={280}
                key={`${farmId}-${pondId}-${populationId}-${selectedWeightChart}`}
                wd={weightDistribution}
                type={'headsOff'}
              />
            )}
          </ErrorBoundary>
        </Box>
      )}
    </CardContainer>
  );
}

function WithPriceList(props: ComponentProps<typeof HeadsOnAndOffChart>) {
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);
  const currentPopulation = useAppSelector((state) => state.farm.currentPopulation);

  const selectedProcessorId = currentPopulation?.harvestPlan?.selectedProcessorId;
  const { allBuckets } = getPriceList({ currentFarm, selectedProcessorId });
  const buckets = props.type === 'headsOn' ? allBuckets?.headOn : allBuckets?.headless;

  return (
    <Box>
      <HeadsOnAndOffChart
        {...props}
        wd={props.wd}
        isReversed={true}
        key={buckets?.buckets?.toString()}
        buckets={!buckets?.isMissingRanges ? buckets?.buckets : undefined}
      />
    </Box>
  );
}
