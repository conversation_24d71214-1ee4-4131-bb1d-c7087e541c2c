import { Box, Flex, Text } from '@chakra-ui/react';
import { formatNumber, isNumber } from '@utils/number';
import { Expand } from '@icons/expand/expand';
import { useAppSelector } from '@redux/hooks';
import { getTrans } from '@i18n/get-trans';
import '@splidejs/react-splide/css'; // Import default styles
import { Dispatch, SetStateAction, useEffect, useMemo, useState } from 'react';
import { CloseFilled } from '@icons/close/close-filled';
import { ImageCarousel } from '@components/image-carousel/image-carousel';
import { useAnalytics } from '@hooks/use-analytics';
import { actionsName } from '@utils/segment';
import { Tray } from '@xpertsea/module-farm-sdk';
import { useGetMonitoringStrengthValues } from '@screens/monitoring/hooks/use-get-monitoring-strength-values';

type HistoryImages = {
  trays: Tray[];
  setPhotosExpanded: Dispatch<SetStateAction<boolean>>;
};

export function HistoryImages(props: HistoryImages) {
  const { trays, setPhotosExpanded } = props;

  const { trans } = getTrans();

  const lang = useAppSelector((state) => state.app.lang);

  const [isExpanded, setIsExpanded] = useState(false);
  const [currentTrayIndex, setCurrentTrayIndex] = useState(0);

  const { qualityValues } = useGetMonitoringStrengthValues();
  const { trackAction } = useAnalytics();

  const { images, imageToTrayMap } = useMemo(() => {
    if (!trays?.length) return { images: [], imageToTrayMap: [] };

    const imageUrls = [];
    const trayMapping = [];

    for (const [trayIndex, item] of trays.entries()) {
      for (const image of item.images) {
        imageUrls.push({
          imageUrl: image.imageUrl,
          imageAlt: trans('t_photo_count', { count: imageUrls.length + 1 })
        });
        trayMapping.push(trayIndex);
      }
    }

    return {
      images: imageUrls,
      imageToTrayMap: trayMapping
    };
  }, [trays, lang]);

  useEffect(() => {
    setPhotosExpanded(isExpanded);
  }, [isExpanded]);

  const currentTray = trays?.[imageToTrayMap?.[currentTrayIndex]];
  const currentQuality = qualityValues[currentTray?.mlResult?.score?.quality];
  const qualityTitle = currentQuality?.title;
  const totalAnimals = currentTray?.mlResult?.total;
  const goodCrop = currentTray?.mlResult?.goodCrop;

  return (
    <Flex
      p='md'
      rounded='2xl'
      bgColor='white'
      flexDir='column'
      position='relative'
      {...(isExpanded && {
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1000,
        rounded: 'none'
      })}
      flex={1}
    >
      <Flex mb='2lg' align='center' justify='space-between'>
        <Flex align='center' gap='md-alt'>
          <Text size='label100'>{trans('t_photos')}</Text>
          {isNumber(totalAnimals) && (
            <Text size='light300' color='text.gray.weak'>
              {trans('t_total_animals_detected', {
                total: formatNumber(totalAnimals, { lang, fractionDigits: 0 })
              })}
            </Text>
          )}
        </Flex>
        <Box
          cursor='pointer'
          onClick={() => {
            trackAction(actionsName.monitoringPhotoExpandClicked, { isExpanded, addCurrentPopulation: true }).then();
            setIsExpanded(!isExpanded);
          }}
        >
          {isExpanded ? <CloseFilled w='24px' h='24px' /> : <Expand w='24px' h='24px' />}
        </Box>
      </Flex>

      {qualityTitle && (
        <Flex align='center' gap='2xs' mb='md'>
          {trans('t_quality')}:{' '}
          <Text
            size='label200'
            textAlign='center'
            rounded='full'
            py='2xs'
            px='xs'
            w='max-content'
            bgColor={currentQuality?.bgColor}
            color={currentQuality?.color}
          >
            {qualityTitle}
          </Text>
        </Flex>
      )}
      <ImageCarousel
        isExpanded={isExpanded}
        slides={images}
        setIsExpanded={setIsExpanded}
        onSlideMove={(value) => setCurrentTrayIndex(value)}
      />

      {isNumber(goodCrop) && isNumber(totalAnimals) && (
        <Text size='label200' mt='md'>
          {trans('t_x_usual_shrimps_for_weights_out_x_counted', {
            total: formatNumber(totalAnimals, { lang, fractionDigits: 0 }),
            good: formatNumber(goodCrop, { lang, fractionDigits: 0 })
          })}
        </Text>
      )}
    </Flex>
  );
}
