import { SomethingWentWrong } from '@components/errors/something-went-wrong';
import { useAppSelector } from '@redux/hooks';
import { MonitoringListTable } from '@screens/monitoring/components/monitoring-list-table';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { useSetFarmBookRefererInRedux } from '@components/farm-book/hooks/use-set-farm-book-referer-in-redux';
import { useListMonitoringApi } from '@screens/monitoring/hooks/use-list-monitoring-api';

export function MonitoringListTableWrapper() {
  useSetFarmBookRefererInRedux('monitorings');
  const currentPopulation = useAppSelector((state) => state.farm?.currentPopulation);

  const { query } = useRouter();
  const { page = 1, size = 10000 } = query as { page?: string; size?: string };

  const [{ error, isFinishedOnce, isLoading }, listMonitoring] = useListMonitoringApi();

  useEffect(() => {
    if (!currentPopulation?._id) return;

    listMonitoring({
      loadRelated: true,
      params: {
        sort: [{ field: 'startedAt', order: 'desc' }],
        filter: { populationId: [currentPopulation._id], farmId: [currentPopulation.farmId] },
        page: { size: Number(size), current: Number(page) }
      }
    });
  }, [currentPopulation?._id, page, size]);

  if (error) return <SomethingWentWrong />;

  return <MonitoringListTable isFinishedOnce={!isLoading && isFinishedOnce} />;
}
