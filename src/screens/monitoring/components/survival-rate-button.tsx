import { Flex, FlexProps, Text } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { getTrans } from '@i18n/get-trans';
import { formatNumber } from '@utils/number';
import { useAppSelector } from '@redux/hooks';
import { EditIcon } from '@icons/edit/edit-icon';

type SurvivalRateButtonProps = {
  survivalRate: number;
  containerStyle?: FlexProps;
  hasEstimatedValue: boolean;
};

export function SurvivalRateButton(props: SurvivalRateButtonProps) {
  const { survivalRate, containerStyle = {}, hasEstimatedValue } = props;
  const { trans } = getTrans();

  const lang = useAppSelector((state) => state.app.lang);

  if (survivalRate) {
    return (
      <Flex gap='xs' alignItems='center' justify='center' {...containerStyle}>
        <Text size='light100' data-cy='pond-survival-rate'>
          {formatNumber(survivalRate, { lang, fractionDigits: 0, isPercentage: true })}
          {hasEstimatedValue ? ' *' : ''}
        </Text>
        <EditIcon cursor='pointer' />
      </Flex>
    );
  }

  return (
    <Flex justify='center' {...containerStyle}>
      <BaseButton size='sm' minWidth='85px' variant='outline' bgColor='gray.50' data-cy='set-target-btn'>
        {trans('t_set_survival_rate')}
      </BaseButton>
    </Flex>
  );
}
