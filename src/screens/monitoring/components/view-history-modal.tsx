import { Flex, Text, useDisclosure } from '@chakra-ui/react';
import { PopulationHistoryItem } from '@screens/pond/components/pond-overview/helpers/pond-production-chart-helpers';
import { useAppSelector } from '@redux/hooks';
import { MonitoringInfoBar } from '@screens/monitoring/components/monitoring-info-bar';
import { WeightDistributionChart } from '@screens/monitoring/components/weight-distribution-chart';
import { getTrans } from '@i18n/get-trans';
import { BaseMenu } from '@components/base/base-menu';
import { useEffect, useState } from 'react';
import { DateTime } from 'luxon';
import { HeadsOnAndOffChart } from '@screens/monitoring/components/heads-on-and-off-chart';
import { getPriceList } from '@screens/settings/helpers/get-price-list';
import { HistoryImages } from '@screens/monitoring/components/history-images';
import { PondMonitoringCards } from '@screens/monitoring/components/pond-monitoring-cards';
import { MonitoringMlResult, MonitoringMlResultScore } from '@xpertsea/module-farm-sdk';
import { <PERSON>alog<PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogRoot, DialogTitle } from '@components/ui/dialog';
import { actionsName } from '@utils/segment';
import { useAnalytics } from '@hooks/use-analytics';
import { useListTraysApi } from '@screens/monitoring/hooks/use-list-trays-api';
import { MonitoringLocation } from '@screens/monitoring/components/monitoring-location';
import { useListMonitoringApi } from '@screens/monitoring/hooks/use-list-monitoring-api';

type WeightChart = 'weight' | 'headsOn' | 'headsOff';
export type HistoryDataItem = PopulationHistoryItem & { score: Omit<MonitoringMlResultScore, '__typename'> };

type ViewHistoryModalProps = {
  isOpen: boolean;
  onClose: () => void;
  history: HistoryDataItem;
  manuallyMonitored: boolean;
};

export function ViewHistoryModal(props: ViewHistoryModalProps) {
  const { history, isOpen, manuallyMonitored, onClose } = props;
  const { trans } = getTrans();

  const lang = useAppSelector((state) => state.app.lang);
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);
  const currentPond = useAppSelector((state) => state.farm.currentPond);
  const currentPopulation = useAppSelector((state) => state.farm.currentPopulation);

  const { name } = currentPond ?? {};
  const { cycle, harvestPlan, manualAverageWeights } = currentPopulation ?? {};
  const { selectedProcessorId } = harvestPlan ?? {};

  const { open } = useDisclosure({ open: isOpen });
  const [selectedWeightChart, setSelectedWeightChart] = useState<WeightChart>('weight');
  const [photosExpanded, setPhotosExpanded] = useState(false);
  const { date, averageWeight, modelAverageWeight, weightDistribution, total, weightCv, score } = history ?? {};

  const options = [
    { label: trans('t_weight_distribution'), value: 'weight' },
    { label: trans('t_head_on_distribution'), value: 'headsOn' },
    { label: trans('t_head_less_distribution'), value: 'headsOff' }
  ];

  const { allBuckets } = getPriceList({ currentFarm, selectedProcessorId });
  const buckets = selectedWeightChart === 'headsOn' ? allBuckets?.headOn : allBuckets?.headless;

  const { trackAction } = useAnalytics();

  const [{ data: traysRes }, listTrays] = useListTraysApi();
  const [{ data: monitoringRes }, listMonitoring] = useListMonitoringApi();
  const monitoringId = history?.monitoringIds ?? [];

  useEffect(() => {
    if (!monitoringId.length) return;
    listTrays({
      params: {
        page: { current: 1, size: 10000 },
        filter: { monitoringId },
        sort: [{ field: 'createdAt', order: 'desc' }]
      }
    });
    listMonitoring({
      params: {
        page: { current: 1, size: 10000 },
        filter: { id: monitoringId },
        sort: [{ field: 'createdAt', order: 'desc' }]
      }
    });
  }, [JSON.stringify(monitoringId)]);

  const mlResult = {
    total,
    averageWeight,
    weightDistribution,
    weightCv,
    score
  } as MonitoringMlResult;

  return (
    <DialogRoot
      open={open}
      onOpenChange={(e) => !e.open && onClose()}
      size={{ base: 'full', md: 'cover' }}
      scrollBehavior='inside'
      restoreFocus={false}
    >
      <DialogContent rounded='2xl' bgColor='gray.100'>
        <DialogHeader p='lg' data-cy='monitor-modal-title'>
          <DialogTitle>{cycle ? `${name}.${cycle}` : name}</DialogTitle>
        </DialogHeader>
        <DialogBody display='flex' pt={0} px='lg' pb='lg' flexDirection='column' gap='md'>
          <MonitoringInfoBar monitoredAt={date} totalAnimals={total} />
          <PondMonitoringCards
            mlResult={mlResult}
            lastHistoryDate={date}
            manualAverageWeight={manualAverageWeights?.find((item) => item.date === date)}
            manuallyMonitored={manuallyMonitored}
          />
          {!manuallyMonitored && (
            <Flex
              p='md'
              rounded='2xl'
              bgColor='white'
              flexDir='column'
              gap={selectedWeightChart === 'weight' ? 'xs-alt' : 'md-alt'}
            >
              <Flex align='center' gap='md-alt' flexWrap='wrap'>
                <BaseMenu
                  variant='link'
                  w='fit-content'
                  options={options}
                  px='0px !important'
                  h='unset !important'
                  _active={{ color: 'dark' }}
                  _hover={{ textDecor: 'none' }}
                  size={{ base: 'sm', md: 'md' }}
                  selectedOption={options.find((ele) => ele.value === selectedWeightChart)}
                  onItemSelect={(option) => {
                    trackAction(actionsName.monitoringDistributionSelected, {
                      option: option.value,
                      addCurrentPopulation: true
                    });
                    setSelectedWeightChart(option.value as WeightChart);
                  }}
                />
                <Text size='light300' color='text.gray.weak'>
                  {trans('t_last_monitored_on_x', {
                    date: DateTime.fromISO(date).toFormat('LLL dd, yyyy', { locale: lang })
                  })}
                </Text>
              </Flex>
              {selectedWeightChart === 'weight' && (
                <WeightDistributionChart
                  height={280}
                  wd={weightDistribution}
                  averageWeight={averageWeight}
                  modelAverageWeight={modelAverageWeight}
                  manualAverageWeight={manualAverageWeights?.find((item) => item.date === date)}
                />
              )}
              {selectedWeightChart !== 'weight' && (
                <HeadsOnAndOffChart
                  height={280}
                  isReversed={true}
                  wd={weightDistribution}
                  type={selectedWeightChart}
                  averageWeight={averageWeight}
                  key={buckets?.buckets?.toString()}
                  buckets={!buckets?.isMissingRanges ? buckets?.buckets : undefined}
                />
              )}
            </Flex>
          )}
          {!manuallyMonitored && (
            <Flex
              mb='2xl'
              gap={{ base: '2lg', xl: 'sm-alt' }}
              direction={{ base: 'column', lg: 'row' }}
              flexWrap={{ base: 'wrap', xl: 'nowrap' }}
            >
              <HistoryImages trays={traysRes?.trays || []} setPhotosExpanded={setPhotosExpanded} />
              {
                <MonitoringLocation
                  photosExpanded={photosExpanded}
                  trays={traysRes?.trays || []}
                  monitoringsDistanceToPond={
                    monitoringRes?.monitorings?.map((monitoring) => monitoring.distanceToPond) || []
                  }
                />
              }
            </Flex>
          )}
        </DialogBody>
      </DialogContent>
    </DialogRoot>
  );
}
