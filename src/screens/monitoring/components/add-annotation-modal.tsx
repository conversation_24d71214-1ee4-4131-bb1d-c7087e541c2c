import { Box, Button, chakra, Flex, Text } from '@chakra-ui/react';
import { BaseFormInput } from '@components/form/base-form-input';
import { BaseFormTextarea } from '@components/form/base-form-textarea';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Footer, <PERSON>alog<PERSON>eader, DialogRoot, DialogTitle } from '@components/ui/dialog';
import { yupResolver } from '@hookform/resolvers/yup';
import { getTrans } from '@i18n/get-trans';
import { useYupSchema, Yup, YupResolverType } from '@utils/yup';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

type AnnotationFormValues = {
  label: string;
  comment?: string;
};

interface AddAnnotationModalProps {
  open: boolean;
  onClose: () => void;
  handleRemove: () => void;
  onSave: (data: AnnotationFormValues) => void;
  editingAnnotationId: string | null;
  initialValues?: AnnotationFormValues;
}

export function AddAnnotationModal(props: AddAnnotationModalProps) {
  const { open, onClose, onSave, editingAnnotationId, initialValues, handleRemove } = props;

  const { trans } = getTrans();
  const [shouldCloseModal, setShouldCloseModal] = useState(false);

  const { schema } = useYupSchema({
    label: Yup.string().required().label('t_label'),
    comment: Yup.string().nullable().label('t_comment')
  });

  const {
    reset,
    register,
    handleSubmit,
    formState: { errors, isValid, isDirty }
  } = useForm<AnnotationFormValues>({
    resolver: yupResolver(schema) as YupResolverType<AnnotationFormValues>,
    mode: 'onChange',
    defaultValues: { label: '', comment: '' }
  });

  useEffect(() => {
    if (open && initialValues) {
      reset(initialValues);
    }
  }, [open, initialValues, reset]);

  const onSubmit = handleSubmit((data: AnnotationFormValues) => {
    onSave(data);
    reset({ label: '', comment: '' });
  });

  const handleCancel = () => {
    if (isDirty) {
      const confirmed = window.confirm(
        'You have unsaved changes to this annotation. Are you sure you want to close? Your changes will be lost.'
      );
      if (!confirmed) return;
    }

    reset({ label: '', comment: '' });
    setShouldCloseModal(true);
  };

  const handleOpenChange = (details: { open: boolean }) => {
    if (!details.open) {
      handleCancel();
    }
  };

  useEffect(() => {
    if (shouldCloseModal) {
      onClose();
      setShouldCloseModal(false);
    }
  }, [shouldCloseModal, onClose]);

  return (
    <DialogRoot size='xs' restoreFocus={false} open={open} onOpenChange={handleOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle as={Box}>
            <Text size='heavy100'>{editingAnnotationId ? 'Edit Annotation' : 'Add Annotation'}</Text>
          </DialogTitle>
        </DialogHeader>
        <chakra.form display='contents' onSubmit={onSubmit} noValidate>
          <DialogBody display='flex' flexDirection='column' gap='md' pt={0} pb='2lg'>
            <BaseFormInput
              id='label'
              autoFocus={true}
              required
              autoComplete='off'
              label={trans('t_label')}
              placeholder={trans('t_enter')}
              error={errors?.label?.message}
              {...register('label')}
            />
            <BaseFormTextarea
              id='comment'
              rows={4}
              resize='none'
              autoComplete='off'
              label={trans('t_comment')}
              placeholder={trans('t_enter')}
              error={errors?.comment?.message}
              {...register('comment')}
            />
          </DialogBody>
          <DialogFooter
            justifyContent={editingAnnotationId ? 'space-between' : 'flex-end'}
            py='sm-alt'
            px='md'
            shadow='elevation.200'
          >
            {editingAnnotationId && (
              <Button variant='outline' colorPalette='red' size='sm' onClick={handleRemove}>
                {trans('t_remove')}
              </Button>
            )}
            <Flex align='center' gap='sm-alt'>
              <Button variant='outline' onClick={handleCancel} size='sm'>
                {trans('t_cancel')}
              </Button>
              <Button type='submit' disabled={!isValid} size='sm'>
                {trans('t_save')}
              </Button>
            </Flex>
          </DialogFooter>
        </chakra.form>
      </DialogContent>
    </DialogRoot>
  );
}
