import { getTrans } from '@i18n/get-trans';
import { weightDistributionToWeightBins } from '@screens/monitoring/helpers/weight-distribution';
import { PopulationManualAverageWeights } from '@xpertsea/module-farm-sdk';
import { useEffect, useMemo, useRef } from 'react';
import { renderToString } from 'react-dom/server';
import { formatNumber } from '@utils/number';
import { useAppSelector } from '@redux/hooks';
import { HighchartsNextComp, HighchartsRef } from '@components/base/highcharts-next-comp';
import { type Options } from 'highcharts';
import { useToken } from '@chakra-ui/react';

export interface WeightDistributionChartProps {
  wd: number[];
  height?: number;
  width?: number;
  isPlain?: boolean;
  averageWeight?: number;
  modelAverageWeight: number;
  manualAverageWeight: PopulationManualAverageWeights;
  customConfig?: {
    barColor?: string;
    hideXTitle?: boolean;
    hideYTitle?: boolean;
    hideTitle?: boolean;
    hideSubTitle?: boolean;
    hideYAxis?: boolean;
    size?: 'sm' | 'md';
    hideDataLabel?: boolean;
    hideLegendLabel?: boolean;
  };
}

export function WeightDistributionChart(props: WeightDistributionChartProps) {
  const {
    wd = [],
    height = 400,
    width,
    isPlain,
    customConfig = {},
    averageWeight,
    modelAverageWeight,
    manualAverageWeight
  } = props;

  const [gray200, gray400, gray800, squidInkPowder800, brandBlue700, graphBrandBlue] = useToken('colors', [
    'gray.200',
    'gray.400',
    'gray.800',
    'squidInkPowder.800',
    'brandBlue.700',
    'graphBrandBlue'
  ]);
  const [fontFamily] = useToken('fonts', ['body']);
  const { trans } = getTrans();
  const lang = useAppSelector((state) => state.app.lang);

  const { datasets, maxX, maxY } = useMemo(() => {
    const transformedData = weightDistributionToWeightBins({ wd });
    const maxY = Math.max(
      ...transformedData.map((series) => series.data.reduce((acc, e) => Math.max(acc, e.y), 0) + 1 || 1)
    );
    const maxX = Math.max(
      ...transformedData.map((series) => series.data.reduce((acc, e) => Math.max(acc, e.x), 0) + 1 || 1)
    );

    const maxXMoreThan29 = maxX > 29 ? 40 : 30;
    const maxXMoreThan39 = maxX > 39 ? 50 : maxXMoreThan29;

    return {
      datasets: transformedData ?? [],
      maxY,
      maxX: maxX > 49 ? 60 : maxXMoreThan39
    };
  }, [wd]);

  const {
    size,
    barColor,
    hideYTitle = isPlain,
    hideYAxis = isPlain,
    hideTitle = isPlain ?? true,
    hideSubTitle = isPlain ?? true,
    hideLegendLabel = isPlain
  } = customConfig;

  const chartComponent = useRef<HighchartsRef>(null);

  const defaultGrayColor = gray800;
  const defaultTextStyle = {
    fontSize: '10px',
    fontWeight: '600',
    color: defaultGrayColor,
    lineHeight: '12px'
  };
  const isSmall = size === 'sm';

  const options: Options = {
    chart: {
      spacing: [18, 2, 0, 0],
      style: {
        fontFamily,
        ...defaultTextStyle,
        height,
        width
      },
      animation: false
    },
    plotOptions: {
      column: {
        pointPadding: 0,
        borderWidth: 1,
        groupPadding: 0,
        shadow: false
      }
    },
    tooltip: {
      padding: 0,
      shadow: false,
      useHTML: true,
      borderRadius: 8,
      backgroundColor: gray200,
      formatter: function (this): string {
        const { s, x, p } = this as unknown as { s: number; x: number; p: number };
        const from = x - s / 2;
        const to = x + s / 2;

        return renderToString(
          <div
            style={{
              gap: '10px',
              display: 'flex',
              flexDirection: 'column',
              padding: '10px 16px',
              fontSize: '12px',
              fontWeight: 600,
              backgroundColor: gray200,
              borderRadius: '8px',
              minWidth: isPlain ? '150px' : '252px',
              lineHeight: '12px',
              color: defaultGrayColor
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
              <p>{trans('t_range_g')}:</p>
              <p>
                {from}-{to}
              </p>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
              <p>{trans('t_animals_%')}:</p>
              <p>{p}%</p>
            </div>
            <div style={{ height: '1px', backgroundColor: gray400 }} />
            {!!manualAverageWeight?.averageWeight && (
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <svg width='20px' height='20px' viewBox='0 0 20 20' fill='none'>
                  <path
                    fill={gray400}
                    d='M6.66675 5.16667C6.66675 4.23325 6.66675 3.76654 6.8484 3.41002C7.00819 3.09641 7.26316 2.84144 7.57676 2.68166C7.93328 2.5 8.39999 2.5 9.33341 2.5H14.8334C15.7668 2.5 16.2335 2.5 16.5901 2.68166C16.9037 2.84144 17.1586 3.09641 17.3184 3.41002C17.5001 3.76654 17.5001 4.23325 17.5001 5.16667V10.6667C17.5001 11.6001 17.5001 12.0668 17.3184 12.4233C17.1586 12.7369 16.9037 12.9919 16.5901 13.1517C16.2335 13.3333 15.7668 13.3333 14.8334 13.3333H9.33341C8.39999 13.3333 7.93328 13.3333 7.57676 13.1517C7.26316 12.9919 7.00819 12.7369 6.8484 12.4233C6.66675 12.0668 6.66675 11.6001 6.66675 10.6667V5.16667Z'
                  />
                  <path
                    fill={gray800}
                    d='M2.5 9.33329C2.5 8.39987 2.5 7.93316 2.68166 7.57664C2.84144 7.26304 3.09641 7.00807 3.41002 6.84828C3.76654 6.66663 4.23325 6.66663 5.16667 6.66663H10.6667C11.6001 6.66663 12.0668 6.66663 12.4233 6.84828C12.7369 7.00807 12.9919 7.26304 13.1517 7.57664C13.3333 7.93316 13.3333 8.39987 13.3333 9.33329V14.8333C13.3333 15.7667 13.3333 16.2334 13.1517 16.5899C12.9919 16.9035 12.7369 17.1585 12.4233 17.3183C12.0668 17.5 11.6001 17.5 10.6667 17.5H5.16667C4.23325 17.5 3.76654 17.5 3.41002 17.3183C3.09641 17.1585 2.84144 16.9035 2.68166 16.5899C2.5 16.2334 2.5 15.7667 2.5 14.8333V9.33329Z'
                  />
                  <path
                    fill='white'
                    fillRule='evenodd'
                    clipRule='evenodd'
                    d='M10.7803 10.2441C11.0732 10.5695 11.0732 11.0972 10.7803 11.4226L7.78033 14.7559C7.48744 15.0814 7.01256 15.0814 6.71967 14.7559L5.21967 13.0893C4.92678 12.7638 4.92678 12.2362 5.21967 11.9107C5.51256 11.5853 5.98744 11.5853 6.28033 11.9107L7.25 12.9882L9.71967 10.2441C10.0126 9.91864 10.4874 9.91864 10.7803 10.2441Z'
                  />
                </svg>
                <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                  <p>{trans('t_abw_g')}:</p>
                  <p>{formatNumber(manualAverageWeight.averageWeight, { lang, fractionDigits: 2 })}</p>
                </div>
              </div>
            )}
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
              <p>{trans('t_kampi_weight_g')}:</p>
              <p>{formatNumber(modelAverageWeight, { lang, fractionDigits: 2 })}</p>
            </div>
          </div>
        );
      },
      style: { zIndex: 9999 }
    },
    title: { text: !hideTitle ? trans('t_weight_distribution') : undefined },
    subtitle: {
      text: !hideSubTitle ? `Sample size = ${wd.length}` : undefined
    },
    xAxis: {
      min: 0,
      max: maxX,
      lineWidth: 0,
      tickWidth: 0,
      tickInterval: 5,
      endOnTick: true,
      labels: {
        distance: 10,
        enabled: !hideLegendLabel,
        style: defaultTextStyle,
        formatter: ({ pos }) => `${pos}`
      },
      startOnTick: true,
      title: !hideLegendLabel
        ? {
            margin: 16,
            style: defaultTextStyle,
            text: trans('t_weight_g')
          }
        : {},

      plotLines:
        averageWeight && !isPlain
          ? [
              {
                color: squidInkPowder800,
                value: averageWeight,
                dashStyle: 'Solid',
                width: 1,
                zIndex: 4
              }
            ]
          : []
    },
    yAxis: {
      min: 0,
      max: maxY,
      ...(hideYAxis && { visible: false }),
      labels: !hideLegendLabel
        ? {
            distance: 10,
            style: defaultTextStyle,
            formatter: ({ pos }) => `${pos}%`
          }
        : {},
      tickInterval: 5,
      gridLineWidth: 0.5,
      gridLineColor: gray400,
      gridLineDashStyle: 'LongDash',
      title:
        !hideYTitle && !isSmall
          ? {
              style: defaultTextStyle,
              text: trans('t_animals_%'),
              margin: 10
            }
          : undefined
    },
    series: [
      {
        showInLegend: false,
        name: trans('t_weight_g'),
        type: 'column',
        pointWidth: !isSmall ? 24 : 4,
        data: datasets[0]?.data,
        borderRadius: 6,
        states: {
          hover: {
            color: brandBlue700
          }
        },
        color: barColor ?? graphBrandBlue,
        zoneAxis: 'x',
        animation: false,
        events: {
          click: function (e) {
            e.preventDefault();
          }
        }
      }
    ]
  };

  const optionStringify = JSON.stringify(options ?? []);

  useEffect(() => {
    if (!chartComponent?.current?.chart) return;
    chartComponent.current.chart.redraw();
  }, [optionStringify, chartComponent?.current]);

  if (!datasets?.length) {
    return null;
  }

  return (
    <HighchartsNextComp
      ref={chartComponent}
      options={options}
      key={optionStringify}
      containerProps={{ style: { width: width ? `${width}px` : '100%', height: height ? `${height}px` : undefined } }}
    />
  );
}
