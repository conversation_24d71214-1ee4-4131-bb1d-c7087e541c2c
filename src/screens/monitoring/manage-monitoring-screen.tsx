import { <PERSON>ert, Box, Flex, HStack, Icon, Input, Stack, Tag, Text } from '@chakra-ui/react';

import { BaseButton } from '@components/base/base-button';
import { BaseUploadInput } from '@components/base/base-upload-input';
import { SomethingWentWrong } from '@components/errors/something-went-wrong';
import { PageScreenHeader } from '@components/layout/page-screen-header';
import { PartialPageLoader } from '@components/loaders/partial-page-loader';
import { BasePagination } from '@components/base/base-pagination';
import { ITableContentProps, TableComponent } from '@components/table/table-component';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { TrayImagesModal } from '@screens/monitoring/components/tray-images-modal';
import { WeightDistributionChart } from '@screens/monitoring/components/weight-distribution-chart';
import { useListMonitoringApi } from '@screens/monitoring/hooks/use-list-monitoring-api';
import { useListTraysApi } from '@screens/monitoring/hooks/use-list-trays-api';
import { useStartMonitoringApi } from '@screens/monitoring/hooks/use-start-monitoring-api';
import { useSubmitMonitoringApi } from '@screens/monitoring/hooks/use-submit-monitoring-api';
import { useUploadTrayTarApi } from '@screens/monitoring/hooks/use-upload-tray-tar-api';
import { formatDate, getUniDate } from '@utils/date';
import { formatNumber } from '@utils/number';
import { goToUrl } from '@utils/url';
import {
  EnumMonitoringProcessingStatus,
  EnumTrayImagesProcessingStatus,
  Monitoring,
  MonitoringMlResult,
  Tray,
  TrayImagesRequest
} from '@xpertsea/module-farm-sdk';
import { useRouter } from 'next/router';
import { ChangeEvent, useCallback, useEffect, useMemo, useState } from 'react';
import { MdMoreHoriz } from 'react-icons/md';
import ReactSelect from 'react-select';
import { useGetPopulationDetailsApi } from '@screens/population/hooks/use-get-population-details-api';
import { MenuButton, MenuContent, MenuItem, MenuRoot } from '@components/ui/menu';
import { isDevApp } from '@utils/app-links';

export function ManageMonitoringScreen() {
  const { query, pathname } = useRouter();
  const { populationId, monitoringId } = query as { populationId: string; monitoringId: string };

  const { trans } = getTrans();
  const [{ isLoading: isCreating }, createMonitoring] = useStartMonitoringApi();
  const [{ data: monitoringRes, isLoading }, fetchMonitoringList] = useListMonitoringApi();
  const { monitorings = [] } = monitoringRes ?? {};

  const [startedAt, setStartedAt] = useState<string | null>();
  const options = useMemo(
    () =>
      monitorings.map((monitoring) => {
        return { label: monitoring._id, value: monitoring._id };
      }),
    [monitorings]
  );

  const selectedMonitoring = useMemo(
    () => monitorings.find((monitoring: Monitoring) => monitoring._id === monitoringId),
    [monitoringId, monitorings]
  );

  const selectedOption = useMemo(
    () => options.find((option: (typeof options)[0]) => option.value === monitoringId),
    [monitoringId, options]
  );

  const onLoadMonitorings = useCallback(() => {
    if (!populationId) return;
    fetchMonitoringList({
      loadRelated: false,
      params: {
        filter: { populationId: [populationId] },
        page: { current: 1, size: 10000 }
      }
    });
  }, [populationId]);

  const onMonitoringStart = useCallback(() => {
    if (!populationId) return;
    createMonitoring({
      params: { populationId, startedAt: startedAt ? new Date(startedAt).toISOString() : undefined },
      successCallback: (res) => {
        onLoadMonitorings();
        goToUrl({ route: pathname, params: { populationId, monitoringId: res?.monitoring?._id }, replace: true });
      }
    });
  }, [populationId, startedAt]);

  useEffect(() => {
    if (!populationId) return;
    onLoadMonitorings();
  }, [populationId]);

  const isDev = isDevApp();

  if (!isDev)
    return (
      <Flex h='100%' justify='center' align='center'>
        {trans('t_you_dont_have_permission_to_see_this_page')}
      </Flex>
    );

  return (
    <Stack p='md' gap='xl' data-cy='monitoring-screen'>
      <PageScreenHeader
        title={trans('t_manage_monitoring')}
        actions={
          <HStack alignItems='center' gap='md'>
            <Box>
              <Input type='date' onChange={(e) => setStartedAt(e.target.value)} data-cy='monitoring-date-input' />
            </Box>
            <Box w={'300px'}>
              <ReactSelect
                id='monitoring-select'
                options={options}
                onChange={(selectedValue) => {
                  goToUrl({
                    route: pathname,
                    params: { populationId, monitoringId: selectedValue.value },
                    replace: true
                  });
                }}
                value={selectedOption}
                placeholder={trans('t_select_existing_monitoring')}
                isLoading={isLoading}
              />
            </Box>
            <Text as='span' textTransform='uppercase'>
              {trans('t_or')}
            </Text>
            <BaseButton loading={isCreating} onClick={onMonitoringStart} data-cy='start-monitoring'>
              {trans('t_start_new_monitoring')}
            </BaseButton>
          </HStack>
        }
      />

      {selectedMonitoring && (
        <MonitoringDetails
          populationId={populationId}
          monitoring={selectedMonitoring}
          onLoadMonitorings={onLoadMonitorings}
        />
      )}
      {!selectedMonitoring && (
        <Alert.Root status='warning' rounded='md' title={trans('t_select_or_create_monitoring')}>
          <Alert.Indicator />
          <Alert.Title> {trans('t_select_or_create_monitoring')}</Alert.Title>
        </Alert.Root>
      )}
    </Stack>
  );
}

type TraysProps = {
  isMonitoringSubmitted: boolean;
  onLoadMonitorings(): void;
};

function Trays(props: TraysProps) {
  const { isMonitoringSubmitted, onLoadMonitorings } = props;

  const { query } = useRouter() as {
    query: { page?: string; size?: string; populationId?: string; monitoringId?: string };
  };
  const { page = 1, size = 100, populationId, monitoringId } = query;

  const { trans } = getTrans();
  const [tray, setTray] = useState<Tray>();
  const [isOpenTrayImagesModal, setIsOpenTrayImagesModal] = useState<boolean>(false);

  const { lang } = useAppSelector((state) => state.app);
  const [{ data: traysRes, isLoading, error }, listTrays] = useListTraysApi();
  const [{ isLoading: isUploadingTrayTar }, uploadTrayTar] = useUploadTrayTarApi();
  const [{ isLoading: isSubmittingMonitoring }, submitMonitoring] = useSubmitMonitoringApi();

  useEffect(() => {
    if (!monitoringId) return;
    onLoadTrays();
  }, [monitoringId, page]);

  const onLoadTrays = useCallback(() => {
    listTrays({
      params: {
        filter: { monitoringId: [monitoringId] },
        page: { current: Number(page), size: Number(size) },
        sort: [{ field: 'createdAt', order: 'desc' }]
      }
    });
  }, [monitoringId, page, size]);

  const { trays = [] } = traysRes ?? {};
  const hasTrays = useMemo(() => trays.length > 0, [trays]);

  const onUploadTray = useCallback(
    (event: ChangeEvent<HTMLInputElement>) => {
      uploadTrayTar({
        params: {
          monitoringId,
          tar: event.target.files[0]
        },
        successCallback: () => {
          onLoadTrays();
        }
      });
    },
    [monitoringId]
  );

  const onSubmitMonitoring = useCallback(() => {
    submitMonitoring({
      params: { monitoringId },
      successCallback: () => {
        onLoadMonitorings();
      }
    });
  }, [monitoringId]);

  const columns = useMemo<ITableContentProps<(typeof trays)[0]>['columns']>(() => {
    return [
      {
        Header: trans('t_id'),
        accessor: '_id',
        Cell: (_id: string) => <Text>{_id}</Text>
      },
      {
        Header: trans('t_total_images'),
        accessor: 'images',
        Cell: (images: TrayImagesRequest[]) => <Text>{images.length}</Text>
      },
      {
        Header: trans('t_total'),
        Cell: (_: unknown, row) => {
          if (!row?.mlResult?.total) return <Text>-</Text>;
          return <Text>{row.mlResult.total}</Text>;
        }
      },
      {
        Header: trans('t_abw_g'),
        Cell: (_: unknown, row) => {
          if (!row?.mlResult?.averageWeight) return <Text>-</Text>;
          return <Text>{formatNumber(row.mlResult.averageWeight, { fractionDigits: 2, lang })}</Text>;
        }
      },
      {
        Header: trans('t_cv'),
        Cell: (_: unknown, row) => {
          if (!row?.mlResult?.weightCv) return <Text>-</Text>;
          return <Text>{formatNumber(row.mlResult.weightCv, { lang, fractionDigits: 0, isPercentage: true })}</Text>;
        }
      },
      {
        Header: trans('t_processing_status'),
        accessor: 'processingStatus',
        Cell(processingStatus: EnumTrayImagesProcessingStatus) {
          let statusColor: string;

          switch (processingStatus) {
            case 'processing':
              statusColor = 'blue.500';
              break;
            case 'failed':
              statusColor = 'red.500';
              break;
            case 'submitted':
              statusColor = 'orange.500';
              break;
            case 'completed':
              statusColor = 'green.500';
              break;
            default:
              statusColor = 'gray.500';
              break;
          }

          return (
            <HStack>
              <Box bg={statusColor} w='8px' h='8px' rounded='lg' />
              <Text textTransform='capitalize'>{processingStatus ?? trans('t_in_process')}</Text>
            </HStack>
          );
        }
      },
      {
        Header: trans('t_finished_at'),
        accessor: 'processingFinishedAt',
        Cell: (processingFinishedAt: string) => {
          if (!processingFinishedAt) return <Text>-</Text>;
          return <Text>{formatDate({ date: processingFinishedAt, locale: lang })}</Text>;
        }
      },
      {
        Header: trans('t_uploaded_at'),
        accessor: 'createdAt',
        Cell: (createdAt: string) => {
          const { f: createdAtFormatted } = getUniDate({
            date: createdAt,
            fromTimezone: 'UTC'
          });

          return <Text>{createdAtFormatted}</Text>;
        }
      },
      {
        Cell(_: unknown, row) {
          return (
            <MenuRoot>
              <MenuButton variant='link'>
                <Icon as={MdMoreHoriz} w='6' h='6' color='gray.700' />
              </MenuButton>
              <MenuContent boxShadow='0px 8px 20px -4px rgba(23, 24, 24, 0.12), 0px 3px 6px -3px rgba(23, 24, 24, 0.08)'>
                <MenuItem
                  value='view-images'
                  onClick={() => {
                    setTray(row);
                    setIsOpenTrayImagesModal(!isOpenTrayImagesModal);
                  }}
                >
                  {trans('t_view_images')}
                </MenuItem>
              </MenuContent>
            </MenuRoot>
          );
        }
      }
    ];
  }, [trays, lang]);

  if (error) return <SomethingWentWrong />;

  if (isLoading) return <PartialPageLoader />;

  return (
    <Stack gap='sm' data-cy='trays-table'>
      <Flex align='center' justify='space-between'>
        <Text fontSize='lg' fontWeight={500}>
          {trans('t_trays')}
        </Text>
        <HStack gap='sm'>
          {!isMonitoringSubmitted && (
            <Box>
              <BaseUploadInput
                onChange={onUploadTray}
                buttonText='Upload Tray'
                resetFiles={isUploadingTrayTar}
                buttonProps={{
                  variant: 'outline',
                  borderColor: 'gray.300',
                  loading: isUploadingTrayTar,
                  'data-cy': 'upload-tray'
                }}
              />
            </Box>
          )}
          {!isMonitoringSubmitted && hasTrays && (
            <BaseButton
              colorPalette='green'
              loading={isSubmittingMonitoring}
              onClick={onSubmitMonitoring}
              data-cy='submit-monitoring'
            >
              {trans('t_submit_monitoring')}
            </BaseButton>
          )}
        </HStack>
      </Flex>
      <TrayImagesModal
        tray={tray}
        isOpen={isOpenTrayImagesModal}
        onClose={() => setIsOpenTrayImagesModal(!isOpenTrayImagesModal)}
      />
      <TableComponent data={trays} columns={columns} data-cy='tray-table' />
      <BasePagination
        hideOnSinglePage={true}
        current={Number(page)}
        pageSize={Number(size)}
        route='/manage-monitoring'
        total={traysRes?.totalRecords}
        query={{ page: String(page), size: String(size), populationId, monitoringId }}
      />
    </Stack>
  );
}

interface MonitoringDetailsProps {
  monitoring: Monitoring;
  populationId: string;
  onLoadMonitorings(): void;
}

function MonitoringDetails(props: MonitoringDetailsProps) {
  const { monitoring, populationId, onLoadMonitorings } = props;

  const { trans } = getTrans();
  const { lang } = useAppSelector((state) => state.app);

  const [{ data: getPopulationDetailsRes }] = useGetPopulationDetailsApi({
    loadRelated: false,
    params: { filter: { populationId } }
  });

  const { population } = getPopulationDetailsRes ?? {};
  const { manualAverageWeights, history } = population ?? {};
  const lastHistory = history?.[0];

  const columns = useMemo<ITableContentProps<typeof monitoring>['columns']>(() => {
    return [
      {
        Header: trans('t_id'),
        accessor: '_id',
        Cell: (_id: string) => <Text>{_id}</Text>
      },
      {
        Header: trans('t_date'),
        accessor: 'startedAt',
        Cell: (startedAt: string) => {
          if (!startedAt) return <Text>-</Text>;
          return <Text>{formatDate({ locale: lang, date: startedAt, format: 'dd LLL yyyy hh:mm a' })}</Text>;
        }
      },
      {
        Header: trans('t_days'),
        Cell: () => <Text>-</Text>
      },
      {
        Header: trans('t_abw_g'),
        accessor: 'mlResult',
        Cell: (mlResult: MonitoringMlResult) => {
          if (!mlResult?.averageWeight) return <Text>-</Text>;
          return <Text>{formatNumber(mlResult?.averageWeight, { fractionDigits: 2, lang })}</Text>;
        }
      },
      {
        Header: trans('t_growth'),
        Cell: () => <Text>-</Text>
      },
      {
        Header: trans('t_cv'),
        accessor: 'mlResult',
        Cell: (mlResult: MonitoringMlResult) => {
          if (!mlResult?.weightCv) return <Text>-</Text>;
          return <Text>{formatNumber(mlResult?.weightCv, { lang, fractionDigits: 0, isPercentage: true })}</Text>;
        }
      },
      {
        Header: trans('t_distribution'),
        accessor: 'mlResult',
        Cell: (mlResult: MonitoringMlResult) => {
          if (!mlResult?.weightDistribution) return <Text>-</Text>;
          return (
            <WeightDistributionChart
              wd={mlResult.weightDistribution}
              averageWeight={mlResult?.averageWeight}
              modelAverageWeight={lastHistory?.modelAverageWeight}
              manualAverageWeight={manualAverageWeights?.find((item) => item.date === lastHistory?.date)}
            />
          );
        }
      },
      {
        Header: trans('t_animals'),
        accessor: 'mlResult',
        Cell: (mlResult: MonitoringMlResult) => <Text>{mlResult?.total ?? '-'}</Text>
      },
      {
        Header: trans('t_processing_status'),
        accessor: 'processingStatus',
        Cell: (processingStatus: EnumMonitoringProcessingStatus) => {
          let statusColor: string;

          switch (processingStatus) {
            case 'processing':
              statusColor = 'blue.500';
              break;
            case 'failed':
              statusColor = 'red.500';
              break;
            case 'pending':
              statusColor = 'yellow.500';
              break;
            case 'submitted':
              statusColor = 'orange.500';
              break;
            case 'completed':
              statusColor = 'green.500';
              break;
            default:
              statusColor = 'gray.500';
              break;
          }

          return (
            <HStack data-cy={`processing-status-${processingStatus}`}>
              <Box bg={statusColor} w='8px' h='8px' rounded='lg' />
              <Text textTransform='capitalize'>{processingStatus ?? trans('t_unknown')}</Text>
            </HStack>
          );
        }
      }
    ];
  }, [monitoring, lang]);
  const isMonitoringSubmitted = useMemo(() => Boolean(monitoring.submittedAt), [monitoring]);

  return (
    <>
      <Stack gap='sm'>
        <Flex align='center' justify='space-between'>
          <Text fontSize='lg' fontWeight={500}>
            {trans('t_monitoring')}
          </Text>
          {isMonitoringSubmitted && (
            <Tag.Root colorPalette='green'>
              <Tag.Label>{trans('t_submitted')}</Tag.Label>
            </Tag.Root>
          )}
        </Flex>
        <TableComponent data={[monitoring]} columns={columns} data-cy='monitoring-table' />
      </Stack>
      <Trays isMonitoringSubmitted={isMonitoringSubmitted} onLoadMonitorings={onLoadMonitorings} />
    </>
  );
}
