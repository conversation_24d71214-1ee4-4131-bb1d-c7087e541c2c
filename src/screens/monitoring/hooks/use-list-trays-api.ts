import { IApiHookInput, ICallbackParams, IHookInputType, useApiHookWrapper } from '@hooks/use-api-hook-wrapper';
import { getTrans } from '@i18n/get-trans';
import { addToastAction } from '@redux/app';
import { moduleFarm } from '@sdks/module-farm';
import { throwErrorMsg } from '@utils/errors';
import { Head, QueryRequest, v1ListTraysOutputRequest } from '@xpertsea/module-farm-sdk';

async function mountFn(props: ICallbackParams<Input>) {
  const { input, successCallback } = props;

  const {
    params,
    fields = {
      totalRecords: 1,
      trays: {
        _id: 1,
        pondId: 1,
        farmId: 1,
        populationId: 1,
        monitoringId: 1,
        trayUuid: 1,
        images: {
          imageNr: 1,
          imageUrl: 1,
          computerVisionData: 1
        },
        mlResult: {
          total: 1,
          goodCrop: 1,
          averageWeight: 1,
          weightCv: 1,
          score: { strength: 1, quality: 1, completeness: 1 }
        },
        processingStatus: 1,
        processingFinishedAt: 1,
        createdAt: 1,
        coordinate: {
          coordinates: 1
        },
        distanceToPond: 1
      }
    }
  } = input;

  const { data, errors } = await moduleFarm.query({
    v1ListTrays: [params, fields]
  });

  if (errors) {
    throwErrorMsg(errors);
  }

  const traysRes = data.v1ListTrays;

  successCallback?.(traysRes);

  return traysRes;
}

async function errorFn(props: ICallbackParams<Input>) {
  const { dispatch, error } = props;
  const { trans } = getTrans();
  dispatch(addToastAction({ type: 'error', title: trans('t_error'), msg: error }));
}

type Input = IApiHookInput<Head<QueryRequest['v1ListTrays']>, v1ListTraysOutputRequest>;
type Output = Awaited<ReturnType<typeof mountFn>>;

export function useListTraysApi(props?: IHookInputType<Input, Output>) {
  return useApiHookWrapper({
    mountFn,
    errorFn,
    initialInput: props,
    initialIsLoading: !!props,
    skipInitialApiCallOnEmptyInputs: true,
    unmountFn: undefined,
    __t: props?.__t, // cache buster key
    __cachePrefix: props?.__cachePrefix, // cache key prefix
    __cacheTTL: props?.__cacheTTL // cache ttl in seconds
  });
}
