import { IApiHookInput, ICallbackParams, IHookInputType, useApiHookWrapper } from '@hooks/use-api-hook-wrapper';
import { getTrans } from '@i18n/get-trans';
import { addToastAction } from '@redux/app';
import { moduleFarm } from '@sdks/module-farm';
import { throwErrorMsg } from '@utils/errors';
import { Head, QueryRequest, v1ListTrayAnnotationsOutputRequest } from '@xpertsea/module-farm-sdk';
import { moduleUser } from '@sdks/module-user';

async function mountFn(props: ICallbackParams<Input>) {
  const { input, successCallback } = props;

  const {
    params,
    fields = {
      totalRecords: 1,
      trayAnnotations: {
        _id: 1,
        shrimpId: 1,
        annotations: {
          label: 1,
          comment: 1,
          pos: { x: 1, y: 1 }
        }
      }
    }
  } = input;

  const { data, errors } = await moduleFarm.query({
    v1ListTrayAnnotations: [params, fields]
  });

  if (errors) {
    throwErrorMsg(errors);
  }

  const listTrayAnnotationsRes = data.v1ListTrayAnnotations;
  const userIds: string[] = [];

  listTrayAnnotationsRes?.trayAnnotations?.forEach((trayAnnotation) => {
    const createdBy = trayAnnotation.createdBy;
    if (createdBy && !userIds.includes(createdBy)) userIds.push(createdBy);
  });

  if (!userIds.length) {
    successCallback?.({ listTrayAnnotationsRes });
    return { listTrayAnnotationsRes };
  }

  const { data: usersRes, errors: userErrors } = await moduleUser.query({
    v1ListUsers: [{ filter: { id: userIds } }, { users: { _id: 1, email: 1, firstName: 1, lastName: 1 } }]
  });

  if (userErrors) {
    throwErrorMsg(userErrors);
  }

  const usersMap = usersRes?.v1ListUsers?.users?.reduce(
    (acc, user) => {
      acc[user._id] = user;
      return acc;
    },
    {} as Record<string, (typeof usersRes.v1ListUsers.users)[0]>
  );

  successCallback?.({ listTrayAnnotationsRes, usersMap });
  return { listTrayAnnotationsRes, usersMap };
}

async function errorFn(props: ICallbackParams<Input>) {
  const { dispatch, error } = props;
  const { trans } = getTrans();
  dispatch(addToastAction({ type: 'error', title: trans('t_error'), msg: error }));
}

type Input = IApiHookInput<Head<QueryRequest['v1ListTrayAnnotations']>, v1ListTrayAnnotationsOutputRequest>;
type Output = Awaited<ReturnType<typeof mountFn>>;

export function useListTrayAnnotationsApi(props?: IHookInputType<Input, Output>) {
  return useApiHookWrapper({
    mountFn,
    errorFn,
    initialInput: props,
    unmountFn: undefined,
    initialIsLoading: !!props,
    __t: props?.__t, // cache buster key
    skipInitialApiCallOnEmptyInputs: true,
    __cacheTTL: props?.__cacheTTL, // cache ttl in seconds
    __cachePrefix: props?.__cachePrefix // cache key prefix
  });
}
