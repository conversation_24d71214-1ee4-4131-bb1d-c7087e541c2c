import { IApiHookInput, ICallbackParams, IHookInputType, useApiHookWrapper } from '@hooks/use-api-hook-wrapper';
import { getTrans } from '@i18n/get-trans';
import { addToastAction } from '@redux/app';
import { throwErrorMsg } from '@utils/errors';
import { moduleFarm } from '@sdks/module-farm';
import { Head, QueryRequest, v1GetTrayDetailsOutputRequest } from '@xpertsea/module-farm-sdk';

async function mountFn(props: ICallbackParams<Input>) {
  const { input, successCallback } = props;

  const {
    params,
    fields = {
      tray: {
        _id: 1,
        monitoringId: 1,
        trayUuid: 1,
        images: {
          imageNr: 1,
          imageUrl: 1
        },
        mlResult: {
          total: 1,
          averageWeight: 1,
          weightCv: 1
        },
        processingStatus: 1,
        processingFinishedAt: 1,
        createdAt: 1,
        coordinate: {
          coordinates: 1
        },
        distanceToPond: 1
      }
    }
  } = input;

  const { data, errors } = await moduleFarm.query({
    v1GetTrayDetails: [params, fields]
  });

  if (errors) {
    throwErrorMsg(errors);
  }

  const trayRes = data.v1GetTrayDetails;

  successCallback?.(trayRes);

  return trayRes;
}

async function errorFn(props: ICallbackParams<Input>) {
  const { dispatch, error } = props;
  const { trans } = getTrans();
  dispatch(addToastAction({ type: 'error', title: trans('t_error'), msg: error }));
}

type Input = IApiHookInput<Head<QueryRequest['v1GetTrayDetails']>, v1GetTrayDetailsOutputRequest>;
type Output = Awaited<ReturnType<typeof mountFn>>;

export function useGetTrayDetailsApi(props?: IHookInputType<Input, Output>) {
  return useApiHookWrapper({
    mountFn,
    errorFn,
    initialInput: props,
    unmountFn: undefined,
    initialIsLoading: !!props,
    __t: props?.__t, // cache buster key
    skipInitialApiCallOnEmptyInputs: true,
    __cacheTTL: props?.__cacheTTL, // cache ttl in seconds
    __cachePrefix: props?.__cachePrefix // cache key prefix
  });
}
