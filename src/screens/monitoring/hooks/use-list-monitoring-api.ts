import { IApiHookInput, ICallbackParams, IHookInputType, useApiHookWrapper } from '@hooks/use-api-hook-wrapper';
import { getTrans } from '@i18n/get-trans';
import { addToastAction } from '@redux/app';
import {
  setCurrentPopulationHistoryInfoAction,
  setCurrentPopulationMonitoringsAction,
  setIsLoadingCurrentPopulationHistoryInfoAction,
  setLoadingCurrentPopulationMonitoringsAction
} from '@redux/farm';
import { moduleFarm } from '@sdks/module-farm';
import { throwErrorMsg } from '@utils/errors';
import { Head, QueryRequest, v1ListMonitoringOutputRequest } from '@xpertsea/module-farm-sdk';

async function mountFn(props: ICallbackParams<Input>) {
  const { input, dispatch, successCallback } = props;

  dispatch(setIsLoadingCurrentPopulationHistoryInfoAction(true));
  dispatch(setLoadingCurrentPopulationMonitoringsAction(true));

  const {
    params,
    loadRelated,
    fields = {
      totalRecords: 1,
      monitorings: {
        _id: 1,
        startedAt: 1,
        createdAt: 1,
        submittedAt: 1,
        populationId: 1,
        processingStatus: 1,
        startedAtTimezone: 1,
        manualAverageWeight: 1,
        coordinate: { coordinates: 1 },
        metadata: 1,
        mlResult: {
          total: 1,
          goodCrop: 1,
          weightCv: 1,
          averageWeight: 1,
          originalImageUrl: 1,
          weightDistribution: 1,
          preProcessedImageUrl: 1,
          score: { strength: 1, quality: 1, completeness: 1 }
        },
        distanceToPond: 1
      }
    }
  } = input;

  const { data, errors } = await moduleFarm.query({
    v1ListMonitorings: [params, fields]
  });

  if (errors) {
    throwErrorMsg(errors);
  }

  const monitoringRes = data.v1ListMonitorings;
  dispatch(
    setCurrentPopulationMonitoringsAction({
      monitorings: monitoringRes.monitorings,
      totalRecords: monitoringRes.totalRecords
    })
  );

  if (!loadRelated) {
    dispatch(setIsLoadingCurrentPopulationHistoryInfoAction(false));
    dispatch(setLoadingCurrentPopulationMonitoringsAction(false));
    successCallback?.(monitoringRes);
    return monitoringRes;
  }

  const { data: v1ListPopulationHistoryInfoData, errors: v1ListPopulationHistoryInfoErrors } = await moduleFarm.query({
    v1ListPopulationHistoryInfo: [
      {
        filter: {
          farmId: params.filter.farmId[0],
          populationId: params.filter.populationId[0]
        },
        page: { size: 1000 }
      },
      {
        populationHistoriesInfo: {
          _id: true,
          date: true,
          info: true,
          type: true
        }
      }
    ]
  });
  if (v1ListPopulationHistoryInfoErrors) {
    throwErrorMsg(v1ListPopulationHistoryInfoErrors);
  }

  const { populationHistoriesInfo } = v1ListPopulationHistoryInfoData.v1ListPopulationHistoryInfo;
  dispatch(
    setCurrentPopulationHistoryInfoAction({
      currentPopulationHistoryInfo: populationHistoriesInfo
    })
  );
  dispatch(setIsLoadingCurrentPopulationHistoryInfoAction(false));
  dispatch(setLoadingCurrentPopulationMonitoringsAction(false));

  successCallback?.(monitoringRes);

  return monitoringRes;
}

async function errorFn(props: ICallbackParams<Input>) {
  const { dispatch, error } = props;
  dispatch(setIsLoadingCurrentPopulationHistoryInfoAction(false));
  dispatch(setLoadingCurrentPopulationMonitoringsAction(false));
  const { trans } = getTrans();
  dispatch(addToastAction({ type: 'error', title: trans('t_error'), msg: error }));
}

type Input = IApiHookInput<Head<QueryRequest['v1ListMonitorings']>, v1ListMonitoringOutputRequest>;
type Output = Awaited<ReturnType<typeof mountFn>>;

export function useListMonitoringApi(props?: IHookInputType<Input, Output>) {
  return useApiHookWrapper({
    mountFn,
    errorFn,
    initialInput: props,
    initialIsLoading: !!props,
    skipInitialApiCallOnEmptyInputs: true,
    unmountFn: undefined,
    __t: props?.__t, // cache buster key
    __cachePrefix: props?.__cachePrefix, // cache key prefix
    __cacheTTL: props?.__cacheTTL // cache ttl in seconds
  });
}
