import { useAppSelector } from '@redux/hooks';
import { useListMonitoringApi } from '@screens/monitoring/hooks/use-list-monitoring-api';
import { useRouter } from 'next/router';

export function useReloadCurrentPopulationMonitorings() {
  const { query } = useRouter();
  const { page = 1, size = 100 } = query as { page?: string; size?: string };

  const { currentPopulation } = useAppSelector((state) => state.farm);

  const [{ error }, listMonitoring] = useListMonitoringApi();

  const reloadCurrentPopulationMonitorings = () => {
    listMonitoring({
      loadRelated: true,
      params: {
        sort: [{ field: 'startedAt', order: 'desc' }],
        filter: { populationId: [currentPopulation._id], farmId: [currentPopulation.farmId] },
        page: { size: Number(size), current: Number(page) }
      }
    });
  };

  return { error, reloadCurrentPopulationMonitorings };
}
