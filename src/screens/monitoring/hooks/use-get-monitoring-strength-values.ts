import { MonitoringMlResultScore } from '@xpertsea/module-farm-sdk';
import { ThemeProps } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';

type StrengthValues = Record<
  MonitoringMlResultScore['strength'],
  {
    title: string;
    color: ThemeProps['color'];
    bgColor: ThemeProps['bgColor'];
    borderColor: ThemeProps['borderColor'];
  }
>;

type QualityValues = Record<
  MonitoringMlResultScore['quality'],
  {
    title: string;
    color: ThemeProps['color'];
    bgColor: ThemeProps['bgColor'];
  }
>;
type TextKey =
  `${MonitoringMlResultScore['strength']}-${MonitoringMlResultScore['quality']}-${MonitoringMlResultScore['completeness']}`;

export function useGetMonitoringStrengthValues() {
  const { trans } = getTrans();

  const qualityValues: QualityValues = {
    belowStandard: {
      title: trans('t_below_standard'),
      color: 'text.semanticYellow',
      bgColor: 'bg.semanticYellow.weak'
    },
    good: { title: trans('t_good'), color: 'text.brandGreen', bgColor: 'bg.brandGreen.weakShade2' },
    great: { title: trans('t_great'), color: 'text.brandBlue', bgColor: 'bg.brandBlue.weakShade2' },
    poor: { title: trans('t_poor'), color: 'text.semanticRed', bgColor: 'bg.shrimpyPinky.weak' }
  };
  const completenessTitles: Record<MonitoringMlResultScore['completeness'], string> = {
    incomplete: trans('t_incomplete'),
    partial: trans('t_partial'),
    complete: trans('t_complete'),
    great: trans('t_great')
  };
  const strengthValues: StrengthValues = {
    weak: {
      title: trans('t_weak'),
      color: 'text.semanticRed',
      bgColor: 'bg.shrimpyPinky.weak',
      borderColor: 'border.semanticRed'
    },
    moderate: {
      title: trans('t_moderate'),
      color: 'text.semanticYellow',
      bgColor: 'bg.semanticYellow.weak',
      borderColor: 'border.semanticYellow'
    },
    strong: {
      title: trans('t_strong'),
      color: 'text.brandGreen',
      bgColor: 'bg.brandGreen.weakShade2',
      borderColor: 'border.brandGreen'
    },
    great: {
      title: trans('t_great'),
      color: 'text.brandBlue',
      bgColor: 'bg.brandBlue.weakShade1',
      borderColor: 'border.brandBlue'
    }
  };

  const strengthDescription: Partial<Record<TextKey, string>> = {
    'weak-great-incomplete': trans('t_weak_great_incomplete_desc'),
    'moderate-great-partial': trans('t_moderate_great_partial_desc'),
    'strong-great-complete': trans('t_strong_great_complete_desc'),
    'great-great-great': trans('t_great_great_great_desc'),
    'weak-good-incomplete': trans('t_weak_good_incomplete_desc'),
    'moderate-good-partial': trans('t_moderate_good_partial_desc'),
    'strong-good-complete': trans('t_strong_good_complete_desc'),
    'strong-good-great': trans('t_strong_good_great_desc'),
    'weak-belowStandard-incomplete': trans('t_weak_below_standard_incomplete_desc'),
    'moderate-belowStandard-partial': trans('t_moderate_below_standard_partial_desc'),
    'moderate-belowStandard-complete': trans('t_moderate_below_standard_complete_desc'),
    'moderate-belowStandard-great': trans('t_moderate_below_standard_great_desc'),
    'weak-poor-incomplete': trans('t_weak_poor_incomplete_desc'),
    'weak-poor-partial': trans('t_weak_poor_partial_desc'),
    'weak-poor-complete': trans('t_weak_poor_complete_desc'),
    'weak-poor-great': trans('t_weak_poor_great_desc')
  };

  return {
    strengthValues,
    strengthDescription,
    qualityValues,
    completenessTitles
  };
}
