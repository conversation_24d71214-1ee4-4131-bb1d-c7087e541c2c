import { IApiHookInput, ICallbackParams, IHookInputType, useApiHookWrapper } from '@hooks/use-api-hook-wrapper';
import { getTrans } from '@i18n/get-trans';
import { addToastAction } from '@redux/app';
import { moduleFarm } from '@sdks/module-farm';
import { throwErrorMsg } from '@utils/errors';
import { Head, QueryRequest, v1ListMonitoringOutputRequest } from '@xpertsea/module-farm-sdk';
import { moduleUser } from '@sdks/module-user';

async function mountFn(props: ICallbackParams<Input>) {
  const { input, successCallback } = props;

  const {
    loadRelated = true,
    params,
    fields = {
      totalRecords: 1,
      monitorings: {
        _id: 1,
        pondId: 1,
        populationId: 1,
        farmId: 1,
        createdBy: 1,
        metadata: 1,
        expectedFileCount: 1,
        uploadedFileCount: 1,
        submittedAt: 1,
        processingStatus: 1,
        startedAt: 1,
        startedAtTimezone: 1,
        processingFinishedAt: 1,
        processingStartedAt: 1,
        createdAt: 1,
        distanceToPond: 1
      }
    }
  } = input;

  const { data, errors } = await moduleFarm.query({
    v1ListMonitorings: [params, fields]
  });

  if (errors) {
    throwErrorMsg(errors);
  }

  const monitoringRes = data.v1ListMonitorings;
  const userIds: string[] = [];
  const monitoringIds: string[] = [];

  monitoringRes?.monitorings?.forEach((monitoring) => {
    const createdBy = monitoring.createdBy;
    const monitoringId = monitoring._id;
    if (monitoringId) monitoringIds.push(monitoringId);
    if (createdBy && !userIds.includes(createdBy)) userIds.push(createdBy);
  });

  if (!loadRelated || !monitoringIds.length) {
    successCallback?.({ monitoringRes });
    return { monitoringRes };
  }

  if (!userIds.length) {
    successCallback?.({ monitoringRes });
    return { monitoringRes };
  }

  const { data: usersRes, errors: userErrors } = await moduleUser.query({
    v1ListUsers: [{ filter: { id: userIds } }, { users: { _id: 1, email: 1, firstName: 1, lastName: 1 } }]
  });

  if (userErrors) {
    throwErrorMsg(userErrors);
  }

  const usersMap = usersRes?.v1ListUsers?.users?.reduce(
    (acc, user) => {
      acc[user._id] = user;
      return acc;
    },
    {} as Record<string, (typeof usersRes.v1ListUsers.users)[0]>
  );

  successCallback?.({ monitoringRes, usersMap });
  return { monitoringRes, usersMap };
}

async function errorFn(props: ICallbackParams<Input>) {
  const { dispatch, error } = props;
  const { trans } = getTrans();
  dispatch(addToastAction({ type: 'error', title: trans('t_error'), msg: error }));
}

type Input = IApiHookInput<Head<QueryRequest['v1ListMonitorings']>, v1ListMonitoringOutputRequest>;
type Output = Awaited<ReturnType<typeof mountFn>>;

export function useListFarmMonitoringApi(props?: IHookInputType<Input, Output>) {
  return useApiHookWrapper({
    mountFn,
    errorFn,
    initialInput: props,
    initialIsLoading: !!props,
    skipInitialApiCallOnEmptyInputs: true,
    unmountFn: undefined,
    __t: props?.__t, // cache buster key
    __cachePrefix: props?.__cachePrefix, // cache key prefix
    __cacheTTL: props?.__cacheTTL // cache ttl in seconds
  });
}
