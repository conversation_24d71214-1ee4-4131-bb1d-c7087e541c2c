import { getTrans } from '@i18n/get-trans';
import { addToastAction } from '@redux/app';
import { throwErrorMsg } from '@utils/errors';
import { moduleFarm } from '@sdks/module-farm';
import { Head, MutationRequest, v1BulkCreateTrayAnnotationOutputRequest } from '@xpertsea/module-farm-sdk';
import { IApiHookInput, ICallbackParams, IHookInputType, useApiHookWrapper } from '@hooks/use-api-hook-wrapper';

async function mountFn(props: ICallbackParams<Input>) {
  const { input, dispatch, successCallback } = props;

  const {
    params,
    fields = {
      trayAnnotations: { _id: 1 }
    }
  } = input;

  const { data, errors } = await moduleFarm.mutation({
    v1BulkCreateTrayAnnotation: [params, fields]
  });

  if (errors) {
    throwErrorMsg(errors);
  }

  const results = data.v1BulkCreateTrayAnnotation;

  successCallback?.(results);

  const { trans } = getTrans();
  dispatch(
    addToastAction({
      type: 'success',
      title: trans('t_success'),
      msg: trans('t_tray_annotations_created')
    })
  );

  return results;
}

async function errorFn(props: ICallbackParams<Input>) {
  const { dispatch, error, failureCallback } = props;
  const { trans } = getTrans();
  dispatch(addToastAction({ type: 'error', title: trans('t_error'), msg: error }));
  failureCallback?.();
}

type Input = IApiHookInput<
  Head<MutationRequest['v1BulkCreateTrayAnnotation']>,
  v1BulkCreateTrayAnnotationOutputRequest
>;
type Output = Awaited<ReturnType<typeof mountFn>>;

export function useBulkCreateTrayAnnotationApi(props?: IHookInputType<Input, Output>) {
  return useApiHookWrapper({
    mountFn,
    errorFn,
    initialInput: props,
    unmountFn: undefined,
    initialIsLoading: !!props,
    __t: props?.__t, // cache buster key
    skipInitialApiCallOnEmptyInputs: true,
    __cacheTTL: props?.__cacheTTL, // cache ttl in seconds
    __cachePrefix: props?.__cachePrefix // cache key prefix
  });
}
