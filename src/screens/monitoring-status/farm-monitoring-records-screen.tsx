import { Box, Flex, Heading, Image, Link, Skeleton, Table, TableCellProps, Tag, Text } from '@chakra-ui/react';
import { useEnableTechnicalFeatures } from '@hooks/use-is-xpertsea-user';
import { getTrans } from '@i18n/get-trans';
import { useRouter } from 'next/router';
import React, { ReactNode, useEffect, useState } from 'react';
import { FarmSelector } from '@screens/farm/components/farm-selector';
import { useAppSelector } from '@redux/hooks';
import { useListFarmMonitoringApi } from '@screens/monitoring/hooks/use-list-farm-monitoring-api';
import { BasePagination } from '@components/base/base-pagination';
import { TableContainer } from '@components/ui/table-container';
import { BaseLink } from '@components/base/base-link';
import { slugify } from '@utils/string';
import { BaseButton } from '@components/base/base-button';
import { ArrowBigRight } from '@icons/arrow-big-right/arrow-big-right';
import { EnumMonitoringProcessingStatus, Monitoring } from '@xpertsea/module-farm-sdk';
import { DateTime } from 'luxon';
import { ChevronRightFilled } from '@icons/chevron-right/chevron-right-filled';
import { ChevronDownFilled } from '@icons/chevron-down/chevron-down-filled';
import { InView } from 'react-intersection-observer';
import { ElapsedTime } from '@screens/monitoring-status/component/elapsed-time';
import { useListTraysApi } from '@screens/monitoring/hooks/use-list-trays-api';

const firstColumnWidth = 70;
const secondColumnWidth = 125;
const firstTwoColumnsWidth = firstColumnWidth + secondColumnWidth;

const cellWidthValues = (value: number) => ({
  w: `${value}px`,
  minW: `${value}px`,
  maxW: `${value}px`
});

export function FarmMonitoringRecordsScreen() {
  const { trans } = getTrans();
  const enableTechnicalFeatures = useEnableTechnicalFeatures();
  const currentFarm = useAppSelector((state) => state.farm?.currentFarm);
  const currentFarmPonds = useAppSelector((state) => state.farm?.currentFarmPonds);
  const isLoadingCurrentFarmPonds = useAppSelector((state) => state.farm?.isLoadingCurrentFarmPonds);
  const isLoadingFarmData = useAppSelector((state) => state.farm?.isLoadingFarmData);

  const { query, route } = useRouter();
  const { page = 1, size = 20 } = query as { page?: string; size?: string; farmEid?: string };

  const [{ data, isLoading: isLoadingMonitoring }, listMonitoring] = useListFarmMonitoringApi();

  const isLoading = isLoadingMonitoring || isLoadingCurrentFarmPonds || isLoadingFarmData;
  const { monitoringRes, usersMap } = data ?? {};

  const monitoringList = monitoringRes?.monitorings ?? [];

  useEffect(() => {
    if (!currentFarm?._id) return;

    listMonitoring({
      params: {
        sort: [{ field: 'startedAt', order: 'desc' }],
        filter: { farmId: [currentFarm._id], excludeManual: true },
        page: { size: Number(size), current: Number(page) }
      }
    });
  }, [currentFarm?._id, page, size]);

  if (!enableTechnicalFeatures) {
    return (
      <Flex h='100%' justify='center' align='center'>
        {trans('t_you_dont_have_permission_to_see_this_page')}
      </Flex>
    );
  }

  return (
    <Flex direction='column' gap='md-alt' p='md'>
      <FarmSelector />
      <Heading>{trans('t_monitoring_status')}</Heading>

      <TableRootContainer>
        <Table.Header>
          <Table.Row>
            <HeaderCell
              title=''
              zIndex={1}
              borderTopLeftRadius='2xl'
              position='sticky !important'
              left='0px !important'
              {...cellWidthValues(firstColumnWidth)}
            />

            <HeaderCell
              title={trans('t_pond')}
              zIndex={1}
              position='sticky !important'
              left={`${firstColumnWidth}px !important`}
              {...cellWidthValues(secondColumnWidth)}
            />
            <HeaderCell title={trans('t_user')} />
            <HeaderCell title={trans('t_photos')} />
            <HeaderCell title={trans('t_processing')} />
            <HeaderCell title={trans('t_upload')} />
            <HeaderCell title={trans('t_device')} />
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {isLoading && <LoadingSkeletonRow />}
          {!isLoading && !monitoringList.length && <EmptyDataRow />}
          {!isLoading && (
            <>
              {monitoringList.map((monitoring) => {
                const { firstName, lastName, email } = usersMap?.[monitoring.createdBy] ?? {};
                const pond = currentFarmPonds?.find((pond) => pond._id === monitoring.pondId);
                const fullName = `${firstName ?? email?.split('@')[0] ?? ''} ${lastName ?? ''}`.trim();

                return (
                  <MonitoringRow
                    key={monitoring._id}
                    pondName={pond?.name}
                    pondEid={pond?.eid}
                    monitoring={monitoring}
                    createdByName={fullName}
                    createdByEmail={email}
                  />
                );
              })}
            </>
          )}
        </Table.Body>
      </TableRootContainer>
      {!isLoading && (
        <BasePagination
          query={query}
          route={route}
          total={monitoringRes?.totalRecords}
          hideOnSinglePage
          showSizeChanger={false}
          pageSize={Number(size)}
          current={Number(page)}
          data-cy='farm-monitoring-table-pagination'
        />
      )}
    </Flex>
  );
}

function LoadingSkeletonRow() {
  return (
    <Table.Row borderBottom='none'>
      <Table.Cell colSpan={7} borderBottom='none'>
        <Flex direction='column' gap='xs'>
          <Skeleton w='100%' h='40px' />
          <Skeleton w='100%' h='40px' />
          <Skeleton w='100%' h='40px' />
          <Skeleton w='100%' h='40px' />
          <Skeleton w='100%' h='40px' />
          <Skeleton w='100%' h='40px' />
        </Flex>
      </Table.Cell>
    </Table.Row>
  );
}

function EmptyDataRow() {
  const { trans } = getTrans();
  const { query } = useRouter();
  const { farmEid } = (query ?? {}) as { farmEid?: string };

  return (
    <Table.Row borderBottom='none'>
      <Table.Cell colSpan={7} borderBottom='none'>
        <Flex w='100%' justify='center' align='center' gap='xs'>
          <Text size='light200'>{trans('t_no_monitoring_available')}</Text>
          <BaseButton size='sm' variant='link' asChild>
            <BaseLink route='/farm/[farmEid]' params={{ farmEid }}>
              {trans('t_go_to_summary')}
              <ArrowBigRight color='button.link' width='20px' height='20px' />
            </BaseLink>
          </BaseButton>
        </Flex>
      </Table.Cell>
    </Table.Row>
  );
}

type MonitoringRowProps = {
  pondName: string;
  pondEid: number;
  monitoring: Monitoring;
  createdByName: string;
  createdByEmail: string;
};

function MonitoringRow(props: MonitoringRowProps) {
  const { pondName, pondEid, monitoring, createdByName, createdByEmail } = props;

  const { trans } = getTrans();
  const { query } = useRouter();
  const { farmEid } = (query ?? {}) as { farmEid?: string };
  const enableTechnicalFeatures = useEnableTechnicalFeatures();

  const [isExpanded, setIsExpanded] = useState(false);
  const [hasCalledListTrays, setHasCalledListTrays] = useState(false);

  const {
    metadata,
    processingStatus,
    submittedAt,
    startedAt,
    startedAtTimezone,
    processingStartedAt,
    processingFinishedAt,
    expectedFileCount = 0
  } = monitoring ?? {};

  const deviceBrand = metadata?.headers?.['x-brand'];
  const deviceModel = metadata?.headers?.['x-model'];
  const deviceSystemVersion = metadata?.headers?.['x-systemversion'];
  const appVersion = metadata?.headers?.['x-version'];

  const submittedAtLuxon = submittedAt ? DateTime.fromISO(submittedAt) : null;
  const startedAtLuxon = startedAt ? DateTime.fromISO(startedAt, { zone: startedAtTimezone }) : null;
  const processingStartedAtLuxon = processingStartedAt ? DateTime.fromISO(processingStartedAt) : null;
  const processingFinishedAtLuxon = processingFinishedAt ? DateTime.fromISO(processingFinishedAt) : null;

  const [{ data: monitoringTrays, isLoading: isTraysLoading }, listTrays] = useListTraysApi();

  const uploadTimeDuration =
    submittedAtLuxon && startedAtLuxon
      ? submittedAtLuxon
          .diff(startedAtLuxon, ['days', 'hours', 'minutes', 'seconds', 'milliseconds'])
          .shiftTo('days', 'hours', 'minutes', 'seconds', 'milliseconds')
          .toObject()
      : null;

  const processingTimeDuration =
    processingFinishedAtLuxon && processingStartedAtLuxon
      ? processingFinishedAtLuxon
          .diff(processingStartedAtLuxon, ['days', 'hours', 'minutes', 'seconds', 'milliseconds'])
          .shiftTo('days', 'hours', 'minutes', 'seconds', 'milliseconds')
          .toObject()
      : null;

  const uploadedTray = monitoring?.uploadedFileCount;
  const missingTray = expectedFileCount - uploadedTray;

  const statusColorPalette: Record<EnumMonitoringProcessingStatus, string> = {
    completed: 'green',
    failed: 'red',
    pending: 'orange',
    processing: 'yellow',
    submitted: 'gray'
  };
  const currentDomain = window.location.hostname.split('.').slice(-2).join('.');

  useEffect(() => {
    if (!processingStatus) return;
    if (processingStatus === 'completed') {
      setIsExpanded(false);
    }
  }, [processingStatus]);

  useEffect(() => {
    if (isExpanded && !hasCalledListTrays) {
      listTrays({
        params: {
          filter: { monitoringId: [monitoring?._id] },
          page: { current: 1, size: 100000 },
          sort: [{ field: 'createdAt', order: 'desc' }]
        }
      });
      setHasCalledListTrays(true);
    }
  }, [isExpanded, hasCalledListTrays, listTrays, monitoring?._id]);

  return (
    <>
      <Table.Row
        cursor='pointer'
        onClick={() => {
          setIsExpanded((prev) => !prev);
        }}
      >
        <Table.Cell
          zIndex={1}
          position='sticky !important'
          left='0px !important'
          {...cellWidthValues(firstColumnWidth)}
        >
          {isExpanded ? (
            <ChevronDownFilled hasBackground={false} color='icon.gray' />
          ) : (
            <ChevronRightFilled hasBackground={false} />
          )}
        </Table.Cell>
        <Table.Cell
          zIndex={1}
          position='sticky !important'
          left={`${firstColumnWidth}px !important`}
          {...cellWidthValues(secondColumnWidth)}
        >
          <Flex align='center' direction='row' gap='2'>
            {!pondName && '-'}
            {pondName && (
              <BaseLink
                route='/farm/[farmEid]/pond/[pondEid]'
                _hover={{ textDecoration: 'underline' }}
                params={{ farmEid, pondEid: slugify(`${pondEid}-${pondName}`) }}
              >
                <Text size='label200' overflow='hidden' textOverflow='ellipsis' maxW='80px' title={pondName}>
                  {pondName}
                </Text>
              </BaseLink>
            )}
          </Flex>
        </Table.Cell>
        <Table.Cell>
          <Text size='label200' css={{ '&::first-letter': { textTransform: 'capitalize' } }}>
            {createdByName || '-'}
          </Text>
          {createdByEmail && <Text size='label300'>{createdByEmail}</Text>}
        </Table.Cell>
        <Table.Cell>
          <Text size='label200' color={uploadedTray === expectedFileCount ? 'brandGreen.600' : 'text.gray'}>
            {trans('t_total')}: {expectedFileCount ?? 0}
          </Text>
          {uploadedTray !== expectedFileCount && (
            <Text size='label200' color={uploadedTray === expectedFileCount ? 'brandGreen.600' : 'text.gray'}>
              {trans('t_uploaded')}: {uploadedTray}
            </Text>
          )}
          {missingTray > 0 && (
            <Text size='label200' color={missingTray > 0 ? 'semanticRed.600' : 'text.gray'}>
              {trans('t_pending_upload')}: {missingTray}
            </Text>
          )}
        </Table.Cell>
        <Table.Cell>
          <Flex align='flex-start' direction='column' gap='xs'>
            <Tag.Root size='lg' rounded='full' colorPalette={statusColorPalette[processingStatus]}>
              <Tag.Label textTransform='capitalize'>{processingStatus}</Tag.Label>
            </Tag.Root>
            <ElapsedTime duration={processingTimeDuration} />
            <Flex direction='column' gap='2xs'>
              <Text size='label300'>
                {trans('t_start')}:{' '}
                {processingStartedAt ? processingStartedAtLuxon.toLocaleString(DateTime.DATETIME_FULL) : '-'}
              </Text>
              <Text size='label300'>
                {trans('t_end')}:{' '}
                {processingFinishedAt ? processingFinishedAtLuxon.toLocaleString(DateTime.DATETIME_FULL) : '-'}
              </Text>
            </Flex>
          </Flex>
        </Table.Cell>

        <Table.Cell>
          <Flex direction='column' gap='xs'>
            <ElapsedTime duration={uploadTimeDuration} />
            <Flex direction='column' gap='2xs'>
              <Text size='label300'>
                {trans('t_start')}: {startedAt ? startedAtLuxon.toLocaleString(DateTime.DATETIME_FULL) : '-'}
              </Text>
              <Text size='label300'>
                {trans('t_end')}: {submittedAt ? submittedAtLuxon.toLocaleString(DateTime.DATETIME_FULL) : '-'}
              </Text>
              {enableTechnicalFeatures && (
                <Text size='label300'>
                  {trans('t_monitoring_id')}: {monitoring?._id?.toString() ?? '-'}
                </Text>
              )}
            </Flex>
          </Flex>
        </Table.Cell>

        <Table.Cell>
          <Text mb='xs' size='label200' css={{ '&::first-letter': { textTransform: 'capitalize' } }}>
            {deviceModel?.includes(deviceBrand) ? '' : deviceBrand} {deviceModel ?? '-'}
          </Text>

          <Text mb='2xs' size='label300'>
            {trans('t_os')}: {deviceSystemVersion ?? '-'}
          </Text>

          <Text size='label300'>
            {trans('t_version')}: {appVersion ?? '-'}
          </Text>
        </Table.Cell>
      </Table.Row>
      {isExpanded && (
        <Table.Row>
          <Table.Cell
            zIndex={1}
            position='sticky !important'
            left='0px !important'
            {...cellWidthValues(firstColumnWidth)}
            borderRight='none !important'
          ></Table.Cell>
          <Table.Cell
            zIndex={1}
            position='sticky !important'
            left={`${firstColumnWidth}px !important`}
            {...cellWidthValues(secondColumnWidth)}
            borderRight='none !important'
          ></Table.Cell>
          <Table.Cell colSpan={5} className='expanded-cell'>
            {!monitoringTrays && missingTray <= 0 && <Text size='label200'>{trans('t_no_trays_available')}</Text>}
            <InView rootMargin='50px' fallbackInView triggerOnce key={farmEid}>
              {({ inView, ref }) => (
                <Flex gap='sm-alt' align='center' flexWrap='wrap' ref={ref}>
                  {isTraysLoading && <Skeleton w='100%' h='200px' borderRadius='xl' />}
                  {!isTraysLoading && inView && (
                    <>
                      {monitoringTrays?.trays?.map((tray, trayIndex) => {
                        const image = tray?.images?.[0];
                        if (!image) return null;
                        if (!currentDomain?.includes('localhost') && image?.imageUrl) {
                          image.imageUrl = image?.imageUrl?.replace(/(https?:\/\/)[^/]+/, `$1${currentDomain}`);
                        }

                        return (
                          <Link
                            key={trayIndex}
                            href={image?.imageUrl}
                            target='_blank'
                            cursor='pointer'
                            p='sm-alt'
                            display='flex'
                            flexDirection='column'
                            bgColor='white'
                            borderRadius='xl'
                            _hover={{ textDecoration: 'none' }}
                            _focus={{ outlineColor: `${statusColorPalette[tray.processingStatus]}.500` }}
                            border='1px solid'
                            borderColor={`${statusColorPalette[tray.processingStatus]}.500`}
                          >
                            <Image
                              h='50px'
                              w='100px'
                              borderRadius='xl'
                              objectFit='contain'
                              src={image.imageUrl}
                              alt={trans('t_photo_count', { count: trayIndex + 1 })}
                              onError={(e) => (e.currentTarget.src = '/image-not-found.svg')}
                            />
                            <Tag.Root rounded='full' colorPalette={statusColorPalette[tray.processingStatus]}>
                              <Tag.Label textTransform='capitalize'>{tray.processingStatus}</Tag.Label>
                            </Tag.Root>
                          </Link>
                        );
                      })}
                      {Array.from({ length: missingTray }).map((_, index) => (
                        <Flex
                          key={index}
                          justify='center'
                          align='center'
                          bgColor='bg.gray.strong'
                          borderRadius='xl'
                          h='50px'
                          w='100px'
                        >
                          {trans('t_pending_upload')}
                        </Flex>
                      ))}
                    </>
                  )}
                </Flex>
              )}
            </InView>
          </Table.Cell>
        </Table.Row>
      )}
    </>
  );
}

interface HeaderCellProps extends TableCellProps {
  title: string;
}

function HeaderCell(props: HeaderCellProps) {
  const { title, ...rest } = props;

  return (
    <Table.ColumnHeader {...rest}>
      <Text size='label200'>{title}</Text>
    </Table.ColumnHeader>
  );
}

function TableRootContainer({ children }: { children: ReactNode }) {
  return (
    <Box pos='relative'>
      <TableContainer
        overflowY='auto'
        rounded='2xl'
        maxH='calc(100vh - 230px)'
        minH='225px'
        _before={{
          top: 0,
          left: 0,
          zIndex: 3,
          content: '""',
          height: '100%',
          position: 'absolute',
          boxShadow: 'elevation.200',
          borderTopLeftRadius: '2xl',
          borderBottomLeftRadius: '2xl',
          width: `${firstTwoColumnsWidth}px`,
          pointerEvents: 'none'
        }}
      >
        <Table.Root
          backgroundColor='transparent'
          borderSpacing='0'
          borderCollapse='separate'
          css={{
            whiteSpace: 'nowrap',
            '& td': { py: 'sm-alt', backgroundColor: 'white' },
            '& .expanded-cell': { backgroundColor: 'bg.gray.medium' },
            '& thead': { top: 0, zIndex: 2, position: 'sticky' },
            '& th': { py: 'sm', backgroundColor: 'squidInkPowder.100' },
            '& tr:hover td': { backgroundColor: 'squidInkPowder.100' },
            '& tr:last-of-type td': {
              borderBottom: 'none',
              '&:first-of-type': {
                borderLeft: 'none'
              }
            },
            '& td, th': {
              px: 'sm-alt',
              border: 'none',
              borderRight: '0.5px solid',
              borderBottom: '0.5px solid',
              borderRightColor: 'gray.400',
              borderBottomColor: 'gray.400',
              '&:nth-last-of-type(2)': { borderRight: 0 },
              '&:not([style*="position: sticky"])': { left: 0, position: 'relative' },
              '&:last-of-type': {
                borderRight: 'none',
                borderLeft: '0.5px solid',
                borderLeftColor: 'gray.400'
              }
            }
          }}
        >
          {children}
        </Table.Root>
      </TableContainer>
    </Box>
  );
}
