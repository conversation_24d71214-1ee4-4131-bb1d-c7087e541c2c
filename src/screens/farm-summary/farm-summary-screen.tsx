import { useSetFarmBookRefererInRedux } from '@components/farm-book/hooks/use-set-farm-book-referer-in-redux';
import { Box, Flex, FlexProps, IconButton, Skeleton } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { useEffect, useMemo, useState } from 'react';
import { PondFilterPopoverFormType } from '@components/pond-filter-popover/use-generate-filter-tags';
import { sortArray, sortCompareString } from '@utils/sort-array';
import get from 'lodash/get';
import {
  FarmSummaryViewVariable,
  getCarryingCapacityPercent,
  getPondSortValue
} from '@screens/farm-summary/helpers/pond-list';
import { isNumber } from '@utils/number';
import { getPondState } from '@screens/pond/helpers/get-pond-state';
import { NoPondsInYourFarmCard } from '@screens/group-summary/components/no-ponds-in-your-farm-card';
import { DateTime } from 'luxon';
import { FarmSelector } from '@screens/farm/components/farm-selector';
import { getDaysDiffBetweenDates } from '@screens/farm/helpers/farm-dates';
import { generateAlertKey } from '@components/alert/helper';
import { MissingDataAlertRow } from '@components/alert/missing-data-alert-row';
import { IndicatorOption, PondCardViewType } from '@components/pond-card/helper';
import { usePermission } from '@hooks/use-permission';
import { CurrentFarmPonds } from '@redux/farm/set-current-farm-ponds';
import { getCacheItem } from '@utils/local-cache';
import { getWebViewType } from '@utils/get-web-view-type';
import { SummaryDrawer } from '@components/summary-drawer/summary-drawer';
import { ProductionOverview } from '@screens/farm-summary/components/production-overview';
import {
  applyAbwFilter,
  applyCarryingCapacityFilter,
  applyCycleDaysFilter,
  applyFlaggedFilter,
  applyPerformanceFilter,
  applySearchFilter,
  applySupervisorFilter
} from '@components/pond-filter-popover/pond-filter-popover-helpers';
import { useGetFarmSummaryStatusIndicator } from '@screens/farm-summary/hooks/use-get-farm-summary-status-indicator';
import { useGetFarmSummaryCustomizeViewVariables } from '@screens/farm-summary/hooks/use-get-farm-summary-customize-view-variables';
import { compareNumberBasedOnPondStatus } from '@screens/farm/helpers/table-sort';
import { Pond } from '@xpertsea/module-farm-sdk';
import { LineUpFilled } from '@icons/line-up/line-up-filled';
import { PondsTab } from '@screens/farm-summary/components/ponds-tab';
import { useAnalytics } from '@hooks/use-analytics';
import { actionsName } from '@utils/segment';
import { useRouter } from 'next/router';
import { ParametersTab } from './components/parameters-tab';
import { FarmSummaryTabs } from '@screens/farm-summary/components/farm-summary-tabs';

type SortOrder = 'asc' | 'desc';
export type SortByKeys = FarmSummaryViewVariable | 'pondName';
export type SortByOptions = {
  key: SortByKeys;
  order: SortOrder;
};

export function FarmSummaryScreen() {
  useSetFarmBookRefererInRedux('farmSummary');
  const { trans } = getTrans();

  const { query } = useRouter();
  const { tab: activeTab = 'ponds' } = (query ?? {}) as { tab: 'ponds' | 'harvest' | 'parameters' };

  // REDUX
  const lang = useAppSelector((state) => state.app.lang);
  const currentFarm = useAppSelector((state) => state.farm?.currentFarm);
  const ponds = useAppSelector((state) => state.farm?.currentFarmPonds);
  const isLoadingCurrentFarmPonds = useAppSelector((state) => state.farm?.isLoadingCurrentFarmPonds);
  const isLoadingFarmData = useAppSelector((state) => state.farm?.isLoadingFarmData);

  const { timezone: farmTimezone, _id: farmId } = currentFarm ?? {};

  const { isWebViewTypeMobile } = getWebViewType();

  const isAdmin = usePermission({
    role: 'admin',
    entity: 'farm',
    entityId: farmId
  });

  const isSupervisor = usePermission({
    role: 'supervisor',
    entity: 'farm',
    entityId: farmId
  });

  const isNotSameFarm = !!ponds?.length && ponds?.[0]?.farmId !== farmId;
  const isLoading = isNotSameFarm || isLoadingCurrentFarmPonds;

  const arrayOfSkeletonNumbers = new Array(7).fill(0);

  const { firstNotDisabledStatusIndicator, indicatingStatusOptions } = useGetFarmSummaryStatusIndicator({
    isAdmin,
    isSupervisor
  });

  const { trackAction } = useAnalytics();
  const [search, setSearch] = useState('');
  const [isChartVisible, setIsChartVisible] = useState<boolean>();
  const [sortBy, setSortBy] = useState<SortByOptions>({ key: 'pondName', order: 'asc' });
  const [farmOverviewFilter, setFarmOverviewFilter] = useState<PondFilterPopoverFormType>();
  const [selectedIndicatingStatus, setSelectedIndicatingStatus] = useState<IndicatorOption>(
    firstNotDisabledStatusIndicator
  );
  const [profitProjectionToView, setProfitProjectionToView] = useState<PondCardViewType>('current');

  const pondViewVariables = useGetFarmSummaryCustomizeViewVariables({
    isAdmin,
    isSupervisor
  });

  useEffect(() => {
    getCacheItem<IndicatorOption>('farmIndicatingStatusSelector').then((valueFromCache) => {
      const valueFromOptions = indicatingStatusOptions.find((ele) => ele.value === valueFromCache?.value);
      const profitPerHaPerDayOption = indicatingStatusOptions.find((ele) => ele.value === 'profitPerHaPerDay');
      const averageWeightOption = indicatingStatusOptions.find((ele) => ele.value === 'averageWeight');
      const costPerPoundOption = indicatingStatusOptions.find((ele) => ele.value === 'costPerPound');

      if (valueFromOptions && !valueFromOptions.isDisabled) {
        setSelectedIndicatingStatus(valueFromCache);
      } else if (profitPerHaPerDayOption && !profitPerHaPerDayOption.isDisabled && isAdmin) {
        setSelectedIndicatingStatus(profitPerHaPerDayOption);
      } else if (costPerPoundOption && !costPerPoundOption.isDisabled && isSupervisor) {
        setSelectedIndicatingStatus(costPerPoundOption);
      } else if (averageWeightOption && !averageWeightOption.isDisabled) {
        setSelectedIndicatingStatus(averageWeightOption);
      } else {
        setSelectedIndicatingStatus(firstNotDisabledStatusIndicator);
      }
    });
  }, [JSON.stringify(pondViewVariables ?? []), isAdmin]);

  useEffect(() => {
    getCacheItem<PondCardViewType>('farmSummaryPondView').then((valueFromCache) => {
      if (valueFromCache) setProfitProjectionToView(valueFromCache);
    });

    getCacheItem<PondFilterPopoverFormType>('farmSummaryFilters').then((valueFromCache) => {
      if (valueFromCache) setFarmOverviewFilter(valueFromCache);
    });
  }, []);

  const {
    cycleDays: cycleDaysFilter,
    abw: abwFilter,
    aboveTarget,
    onTrack,
    offTrack,
    flagged,
    aboveCarryingCapacity,
    aboveCarryingCapacityFlag,
    superVisor
  } = farmOverviewFilter ?? {};

  const memoDeps = [
    JSON.stringify(ponds ?? []),
    search,
    sortBy?.key,
    sortBy?.order,
    farmTimezone,
    cycleDaysFilter?.lessThan,
    cycleDaysFilter?.moreThan,
    abwFilter?.lessThan,
    abwFilter?.moreThan,
    aboveTarget,
    offTrack,
    onTrack,
    flagged,
    aboveCarryingCapacity,
    superVisor?.id,
    profitProjectionToView,
    selectedIndicatingStatus?.value
  ];

  const {
    ponds: filteredPonds,
    estimatedValuesCounter,
    pondsAboveCarryingCapacity,
    aboveCapacityThreshold
  } = useMemo(() => {
    if (!ponds?.length) return { ponds: [] as Pond[], estimatedValuesCounter: 0, pondsAboveCarryingCapacity: 0 };

    let estimatedValuesCounter = 0;
    let pondsAboveCarryingCapacity = 0;
    const aboveCapacityThreshold = 85;

    let filteredPonds = ponds.filter((pond) => {
      const { currentPopulation } = pond;
      const { estimatedSurvival, estimatedFeed, cycleInformation, history } = currentPopulation ?? {};
      const { isNewPond, isHarvested } = getPondState({ population: currentPopulation });
      const isAvailable = !isNewPond && !isHarvested;
      const lastHistory = history?.[0];
      const lastHistoryDate = lastHistory?.date;
      const hasEstimatedValues = !!estimatedSurvival?.[lastHistoryDate] || !!estimatedFeed?.[lastHistoryDate];
      if (hasEstimatedValues && isAvailable) {
        estimatedValuesCounter++;
      }

      const carryingCapacityPercent = getCarryingCapacityPercent({
        biomassLbsByHa: lastHistory?.biomassLbsByHa,
        carryingCapacity: cycleInformation?.carryingCapacity
      });

      if (carryingCapacityPercent > aboveCapacityThreshold && isAvailable) {
        pondsAboveCarryingCapacity++;
      }

      return true;
    });

    const hasFilters =
      search?.length > 0 ||
      aboveTarget ||
      onTrack ||
      offTrack ||
      flagged ||
      superVisor?.id ||
      isNumber(aboveCarryingCapacity) ||
      isNumber(cycleDaysFilter?.lessThan) ||
      isNumber(cycleDaysFilter?.moreThan) ||
      isNumber(abwFilter?.lessThan) ||
      isNumber(abwFilter?.moreThan);

    if (hasFilters) {
      filteredPonds = filteredPonds.filter((pond) => {
        const { currentPopulation } = pond;
        const { isNewPond, isHarvested } = getPondState({ population: currentPopulation });

        if (isNewPond || isHarvested) return false;

        const hasPerformanceFilter = applyPerformanceFilter({
          currentPopulation,
          profitProjectionToView,
          onTrack,
          offTrack,
          aboveTarget,
          selectedIndicatingStatus: selectedIndicatingStatus?.value
        });

        return (
          applySearchFilter(search, pond.name) &&
          applyCycleDaysFilter({ currentPopulation, cycleDaysFilter, farmTimezone }) &&
          applyAbwFilter({ currentPopulation, abwFilter, lang }) &&
          hasPerformanceFilter &&
          applyFlaggedFilter({ currentPopulation, flagged }) &&
          applySupervisorFilter(superVisor?.id, pond.superVisorId) &&
          applyCarryingCapacityFilter({ currentPopulation, aboveCarryingCapacity, aboveCarryingCapacityFlag })
        );
      });
    }

    // Sorting
    const applySorting = (ponds: CurrentFarmPonds, sortOption: SortByOptions) => {
      const { key, order } = sortOption;
      trackAction(actionsName.farmSummaryListSorted, { key, order }).then();

      if (key === 'pondName') {
        return sortArray({
          list: ponds,
          sortDir: order,
          compareFunction: (a, b) => {
            const firstItemValue = get(a, 'name');
            const secondItemValue = get(b, 'name');
            return sortCompareString(firstItemValue, secondItemValue);
          }
        });
      }

      return sortArray({
        list: ponds,
        compareFunction: (a, b) => {
          const firstItem = getPondSortValue({
            population: a?.currentPopulation,
            variable: key,
            profitProjectionToView,
            pondSize: a?.size
          });

          const secondItem = getPondSortValue({
            population: b?.currentPopulation,
            variable: key,
            profitProjectionToView,
            pondSize: b?.size
          });

          return compareNumberBasedOnPondStatus({
            firstItem: firstItem.value,
            secondItem: secondItem.value,
            isFirstItemPondEmpty: firstItem.isEmpty,
            isSecondItemPondEmpty: secondItem.isEmpty,
            hasEmptyPondPriority: false,
            hasEmptyValuePriority: false,
            sortDir: order
          });
        }
      });
    };

    return {
      ponds: applySorting(filteredPonds, sortBy),
      estimatedValuesCounter,
      pondsAboveCarryingCapacity,
      aboveCapacityThreshold
    };
  }, memoDeps);

  if (isLoading || isLoadingFarmData) {
    return (
      <Flex direction='column' gap='sm' p='md' data-cy='ponds-list-skeleton-loader'>
        <Flex align='center' justify='space-between' flexWrap='wrap' gap='sm-alt'>
          <FarmSelector />
          <Flex gap='sm-alt' align='center'>
            <Skeleton borderRadius='full' w='40px' height='40px' />
            <Skeleton borderRadius='full' w='175px' height='40px' />
          </Flex>
        </Flex>

        <Skeleton h='44px' w='265px' borderRadius='full' />
        <Skeleton h='234px' borderRadius='base' />
        {arrayOfSkeletonNumbers.map((_, index) => (
          <Skeleton key={index} height='70px' />
        ))}
      </Flex>
    );
  }

  if (!ponds?.length) {
    return (
      <Flex p='md' direction='column' gap='md-alt' id='ponds-list' data-cy='ponds-list-data' bgColor='white'>
        <FarmSelector />
        <NoPondsInYourFarmCard id='ponds-list' />
      </Flex>
    );
  }

  return (
    <Flex gap='md-alt' p='md' direction='column' id='ponds-list' data-cy='ponds-list-data' bgColor='bg.gray.medium'>
      <Flex align='center' gap='sm-alt' justify='space-between' flexWrap='wrap'>
        <FarmSelector />
        <Flex justify='flex-end' gap='sm-alt' align='center' flex={1}>
          {activeTab === 'ponds' && (
            <>
              <IconButton
                bgColor='white'
                display={{ base: 'none', lg: 'block' }}
                css={{
                  '&:hover svg.first-path': {
                    fill: 'brandBlue.300'
                  },
                  '&:hover svg.second-path': {
                    fill: 'graphBrandBlue'
                  }
                }}
                _focus={{ outline: 'none' }}
                aria-label='chart-visibility-toggle'
                onClick={() => setIsChartVisible(!isChartVisible)}
                {...(isChartVisible && { bgColor: 'brandBlue.200' })}
              >
                <LineUpFilled
                  {...(isChartVisible && {
                    firstColor: 'brandBlue.300',
                    secondColor: 'graphBrandBlue'
                  })}
                />
              </IconButton>

              {!isWebViewTypeMobile && (
                <Box display={{ base: 'none', lg: 'block' }} data-cy='summary-drawer-desktop'>
                  <SummaryDrawer title={trans('t_farm_summary')}>
                    <ProductionOverview filteredPonds={filteredPonds} farmOverviewFilter={farmOverviewFilter} />
                  </SummaryDrawer>
                </Box>
              )}
            </>
          )}
        </Flex>
      </Flex>

      <FarmSummaryTabs />

      {!isWebViewTypeMobile && activeTab === 'ponds' && (
        <Box ms='auto' display={{ base: 'block', lg: 'none' }}>
          <SummaryDrawer title={trans('t_farm_summary')}>
            <ProductionOverview filteredPonds={filteredPonds} farmOverviewFilter={farmOverviewFilter} />
          </SummaryDrawer>
        </Box>
      )}

      {activeTab === 'ponds' && (
        <PondsTab
          search={search}
          sortBy={sortBy}
          setSearch={setSearch}
          setSortBy={setSortBy}
          isLoading={isLoading}
          filteredPonds={filteredPonds}
          isChartVisible={isChartVisible}
          pondViewVariables={pondViewVariables}
          farmOverviewFilter={farmOverviewFilter}
          setFarmOverviewFilter={setFarmOverviewFilter}
          profitProjectionToView={profitProjectionToView}
          aboveCapacityThreshold={aboveCapacityThreshold}
          estimatedValuesCounter={estimatedValuesCounter}
          indicatingStatusOptions={indicatingStatusOptions}
          selectedIndicatingStatus={selectedIndicatingStatus}
          setProfitProjectionToView={setProfitProjectionToView}
          pondsAboveCarryingCapacity={pondsAboveCarryingCapacity}
          setSelectedIndicatingStatus={setSelectedIndicatingStatus}
        />
      )}

      {activeTab === 'parameters' && <ParametersTab />}
    </Flex>
  );
}

export function DataUpdateAlert(props: { containerProps?: FlexProps }) {
  const farmTimezone = useAppSelector((state) => state.farm.currentFarm?.timezone);
  const currentFarmPonds = useAppSelector((state) => state.farm.currentFarmPonds);
  const currentFarmSlug = useAppSelector((state) => state.farm.currentFarmSlug);

  if (!currentFarmPonds?.length) return null;

  let missingFeedCounter = 0;
  let missingSurvivalCounter = 0;
  let pondMonitoredMoreThanWeekCounter = 0;

  let estimatedSurvivalRateCounter = 0;
  let estimatedFeedCounter = 0;

  for (const pond of currentFarmPonds) {
    const { currentPopulation } = pond;
    const { isEmpty } = getPondState({ population: currentPopulation });
    if (isEmpty) {
      continue;
    }

    const { survivalRate, lastMonitoringDate, feedData, estimatedSurvival, estimatedFeed } = currentPopulation;
    const currentDateLuxon = DateTime.local({ zone: farmTimezone });
    const lastMonitoringDateFormatted = DateTime.fromISO(lastMonitoringDate).toFormat('yyyy-MM-dd');
    const daysAwayFromLastMonitoring = getDaysDiffBetweenDates({
      baseDate: currentDateLuxon.toFormat('yyyy-MM-dd'),
      dateToCompare: lastMonitoringDateFormatted
    });
    if (!feedData) {
      missingFeedCounter++;
    }
    if (!survivalRate?.[lastMonitoringDateFormatted]) {
      missingSurvivalCounter++;
    }
    if (daysAwayFromLastMonitoring > 7) {
      pondMonitoredMoreThanWeekCounter++;
    }

    if (estimatedSurvival?.[lastMonitoringDateFormatted]) {
      estimatedSurvivalRateCounter++;
    }

    if (estimatedFeed?.[lastMonitoringDateFormatted]) {
      estimatedFeedCounter++;
    }
  }

  const alertKey = generateAlertKey({
    missingSurvivalCounter,
    missingFeedCounter,
    pondMonitoredMoreThanWeekCounter,
    pondsLength: currentFarmPonds?.length
  });

  return (
    <MissingDataAlertRow
      alertKey={alertKey}
      farmEid={currentFarmSlug}
      containerProps={props?.containerProps}
      hasEstimatedData={!!estimatedFeedCounter || !!estimatedSurvivalRateCounter}
    />
  );
}
