import { useSetFarmBookRefererInRedux } from '@components/farm-book/hooks/use-set-farm-book-referer-in-redux';
import { Flex } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { useEffect, useState } from 'react';
import { FarmSelector } from '@screens/farm/components/farm-selector';
import { ViewTableIcon } from '@icons/view-table/view-table-icon';
import { HarvestTab, HarvestTableFilterFormValues } from '@screens/farm-summary/components/harvest-tab';
import { BaseButton } from '@components/base/base-button';
import { FarmSummaryTabs } from '@screens/farm-summary/components/farm-summary-tabs';
import { useListPopulationsApi } from '@screens/population/hooks/use-list-populations-api';
import { FormProvider, useForm } from 'react-hook-form';
import { DateTime } from 'luxon';
import {
  harvestCommonGql,
  harvestPlanCommonGql,
  historyGqlData,
  partialHarvestCommonGql,
  partialHarvestPlannedCommonGql,
  productionPredictionCommonGql
} from '@screens/population/apis/common-queries';
import { OverlayLoader } from '@components/loaders/overlay-loader';

export function FarmSummaryHarvestsScreen() {
  useSetFarmBookRefererInRedux('farmSummary');
  const { trans } = getTrans();

  // REDUX
  const farmId = useAppSelector((state) => state.farm?.currentFarm?._id);
  const isLoadingFarmData = useAppSelector((state) => state.farm?.isLoadingFarmData);

  const [{ isLoading: isLoadingPonds, data: listPopulationsData }, listPopulations] = useListPopulationsApi();

  const ponds = listPopulationsData?.ponds;

  const isNotSameFarm = !!ponds?.length && ponds?.[0]?.farmId !== farmId;
  const isLoading = isNotSameFarm || isLoadingPonds;

  const [isHarvestSummaryTableVisible, setIsHarvestSummaryTableVisible] = useState<boolean>(true);

  const methods = useForm<HarvestTableFilterFormValues>({
    defaultValues: {
      search: '',
      typeFilters: {
        final: false,
        partial: false
      },
      stateFilters: {
        recorded: false,
        notRecorded: false
      },
      dateFilters: {
        next30d: true,
        past30d: true
      }
    }
  });

  const { watch } = methods;
  const next30dWatch = watch('dateFilters.next30d');
  const past30dWatch = watch('dateFilters.past30d');
  const endDateWatch = watch('dateFilters.customRange.endDate');
  const startDateWatch = watch('dateFilters.customRange.startDate');

  useEffect(() => {
    // Determine date range based on filter selections
    let harvestTo: string;
    let harvestFrom: string;

    if (startDateWatch && endDateWatch) {
      harvestTo = DateTime.fromJSDate(endDateWatch).toFormat('yyyy-MM-dd');
      harvestFrom = DateTime.fromJSDate(startDateWatch).toFormat('yyyy-MM-dd');
    } else {
      // Default date ranges based on filter selections
      const today = DateTime.now();

      if (past30dWatch && next30dWatch) {
        harvestFrom = today.minus({ days: 30 }).toFormat('yyyy-MM-dd');
        harvestTo = today.plus({ days: 30 }).toFormat('yyyy-MM-dd');
      } else if (past30dWatch) {
        harvestFrom = today.minus({ days: 30 }).toFormat('yyyy-MM-dd');
        harvestTo = today.toFormat('yyyy-MM-dd');
      } else if (next30dWatch) {
        harvestFrom = today.toFormat('yyyy-MM-dd');
        harvestTo = today.plus({ days: 30 }).toFormat('yyyy-MM-dd');
      }
    }

    const bothExist = !!harvestFrom && !!harvestTo;
    const bothMissing = !harvestFrom && !harvestTo && !startDateWatch && !endDateWatch;

    if (!bothExist && !bothMissing) return;

    listPopulations({
      loadRelated: true,
      params: {
        page: { size: 1000, current: 1 },
        filter: {
          farmId,
          harvestTo,
          harvestFrom
        }
      },
      fields: {
        populations: {
          _id: 1,
          cycle: 1,
          pondId: 1,
          stockedAt: 1,
          stockedAtTimezone: 1,
          lastMonitoringDate: 1,
          isMonitoringStarted: 1,
          seedingAverageWeight: 1,
          lastMonitoringStatus: 1,
          partialHarvest: partialHarvestCommonGql,
          partialHarvestPlanned: partialHarvestPlannedCommonGql,
          harvest: harvestCommonGql,
          harvestPlan: harvestPlanCommonGql,
          productionPrediction: productionPredictionCommonGql,
          lastMonitoringMlResult: { averageWeight: 1 },
          growthTarget: { weight: 1, productionDays: 1 },
          survivalRate: 1,
          lastWeekGrowth: 1,
          lastMonitoringDateTimezone: 1,
          history: historyGqlData
        }
      }
    });
  }, [startDateWatch, endDateWatch, next30dWatch, past30dWatch, farmId]);

  return (
    <Flex p='md' gap='md-alt' direction='column' id='ponds-list' data-cy='ponds-list-data' bgColor='bg.gray.medium'>
      <OverlayLoader isLoading={isLoading || isLoadingFarmData} />
      <Flex align='center' gap='sm-alt' justify='space-between' flexWrap='wrap'>
        <FarmSelector />

        <BaseButton
          display={{ base: 'none', lg: 'block' }}
          px='sm-alt'
          bgColor='white'
          color='text.gray'
          _focus={{ outline: 'none' }}
          aria-label='harvest-summary-table-toggle'
          {...(isHarvestSummaryTableVisible && { bgColor: 'brandBlue.200' })}
          onClick={() => setIsHarvestSummaryTableVisible(!isHarvestSummaryTableVisible)}
        >
          <ViewTableIcon
            {...(isHarvestSummaryTableVisible && {
              firstColor: 'brandBlue.300',
              secondColor: 'graphBrandBlue'
            })}
          />
          {isHarvestSummaryTableVisible && trans('t_hide_table')}
          {!isHarvestSummaryTableVisible && trans('t_show_table')}
        </BaseButton>
      </Flex>

      <FarmSummaryTabs />

      <FormProvider {...methods}>
        <HarvestTab isHarvestSummaryTableVisible={isHarvestSummaryTableVisible} ponds={ponds} />
      </FormProvider>
    </Flex>
  );
}
