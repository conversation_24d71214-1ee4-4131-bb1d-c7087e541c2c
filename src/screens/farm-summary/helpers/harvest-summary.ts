import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import isUndefined from 'lodash/isUndefined';

export type HarvestSummaryViewVariable =
  | 'state'
  | 'survival'
  | 'type'
  | 'cycleDays'
  | 'harvestType'
  | 'revenue'
  | 'revenuePerPound'
  | 'revenuePoundHarvest'
  | 'averageWeight'
  | 'biomassPerHa'
  | 'biomass'
  | 'animalsHarvested'
  | 'animalsHarvestedM2'
  | 'animalsHarvestedHa'
  | 'selectedProcessorId';

export function useGetHarvestSummaryVariableLabels(): Record<HarvestSummaryViewVariable, string> {
  const { trans } = getTrans();
  const user = useAppSelector((state) => state.auth.user);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);
  const biomassTitle = isBiomassUnitLbs ? trans('t_biomass_lb') : trans('t_biomass_kg');
  const biomassHaTitle = isBiomassUnitLbs ? trans('t_biomass_lb_ha') : trans('t_biomass_kg_ha');

  return {
    state: trans('t_status'),
    survival: trans('t_survival'),
    animalsHarvested: trans('t_animals_harvested'),
    animalsHarvestedHa: trans('t_animals_harvested_ha'),
    animalsHarvestedM2: trans('t_animals_harvested_per_m2'),
    averageWeight: trans('t_abw_g'),
    biomass: biomassTitle,
    cycleDays: trans('t_days_of_culture'),
    biomassPerHa: biomassHaTitle,
    harvestType: trans('t_harvest_type'),
    revenue: trans('t_rev'),
    revenuePerPound: trans('t_rev_per_pound_processed'),
    revenuePoundHarvest: trans('t_rev_per_pound_harvested'),
    selectedProcessorId: trans('t_processor'),
    type: trans('t_type')
  };
}
