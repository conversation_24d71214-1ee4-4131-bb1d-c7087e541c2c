import { DateTime } from 'luxon';
import { getDaysDiffBetweenDates } from '@screens/farm/helpers/farm-dates';
import { PopulationType } from '@redux/farm/set-current-population';
import { getPondState } from '@screens/pond/helpers/get-pond-state';
import { getTrans } from '@i18n/get-trans';
import { PopulationCycleInformation, PopulationHistory } from '@xpertsea/module-farm-sdk';
import isUndefined from 'lodash/isUndefined';
import { isInvalidABW } from '@screens/farm/helpers/abw';
import { PondCardViewType } from '@components/pond-card/helper';

type GetCarryingCapacityPercentParams = {
  biomassLbsByHa: PopulationHistory['biomassLbsByHa'];
  carryingCapacity: PopulationCycleInformation['carryingCapacity'];
};

export function getCarryingCapacityPercent({ biomassLbsByHa, carryingCapacity }: GetCarryingCapacityPercentParams) {
  if (!biomassLbsByHa || !carryingCapacity) return null;

  return (biomassLbsByHa / carryingCapacity) * 100;
}

type GetCycleDaysParams = {
  farmTimezone: string;
  population: PopulationType;
};

export function getCycleDays(params: GetCycleDaysParams) {
  const { population, farmTimezone } = params;
  const { stockedAt, stockedAtTimezone, lastMonitoringDate } = population ?? {};

  const { isEmpty, isHarvested, isNewPond } = getPondState({ population });
  if (isEmpty || isHarvested || isNewPond || !lastMonitoringDate || !stockedAt) return 0;

  const lastMonitoringDateLuxon = DateTime.fromISO(lastMonitoringDate, { zone: farmTimezone });
  const stockedAtLuxon = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone ?? farmTimezone });

  return getDaysDiffBetweenDates({
    baseDate: lastMonitoringDateLuxon.toFormat('yyyy-MM-dd'),
    dateToCompare: stockedAtLuxon.toFormat('yyyy-MM-dd')
  });
}

export type FarmSummaryViewVariable =
  | 'pondSize'
  | 'stockingDate'
  | 'animalsStockedHa'
  | 'partialHarvestLbsHa'
  | 'lastPartialHarvestDate'
  | 'plannedHarvestDate'
  | 'numberOfPartialHarvests'
  | 'averageWeight'
  | 'growthLinear'
  | 'growthDaily'
  | 'growth1WeekAvg'
  | 'growth2WeekAvg'
  | 'growth3WeekAvg'
  | 'growth4w'
  | 'fcrCumulative'
  | 'fcrWeekly'
  | 'adjustedFcr'
  | 'kgPerHaPerDayGivenKg'
  | 'weeklyFeedGivenKg'
  | 'totalFeedGivenKg'
  | 'totalFeedGivenLbs'
  | 'biomassPercentage'
  | 'survival'
  | 'survivalFeed'
  | 'survivalWithPartialHarvest'
  | 'biomassLb'
  | 'biomassLbTotal'
  | 'biomassLbHa'
  | 'totalBiomassLbHa'
  | 'animalsRemainingM2'
  | 'animalsRemainingHa'
  | 'dispersion'
  | 'symmetry'
  | 'profitPerHaPerDay'
  | 'profitPerPound'
  | 'totalProfit'
  | 'revenuePerPound'
  | 'totalRevenuePound'
  | 'totalRevenue'
  | 'totalCosts'
  | 'stockingCosts'
  | 'stockingCostsMillar'
  | 'cumulativeOverheadCosts'
  | 'cumulativeFeedCosts'
  | 'costPerPound'
  | 'costPoundHarvest'
  | 'feedCostPerKg'
  | 'daysUntilHarvest'
  | 'daysOffFromOptimal'
  | 'lastMonitoringDistance';

type GetPondViewVariableLabelsParams = {
  unitsConfig: { biomass: 'kg' | 'lbs' };
};

export function getPondViewVariableLabels(
  params: GetPondViewVariableLabelsParams
): Record<FarmSummaryViewVariable, string> {
  const { unitsConfig } = params;

  const { trans } = getTrans();
  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);
  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');

  return {
    pondSize: trans('t_pond_size'),
    stockingDate: trans('t_stocking_date'),
    animalsStockedHa: trans('t_animals_stocked_ha'),
    partialHarvestLbsHa: trans('t_biomass_from_partials_unit_ha', {
      unit: unitLabel
    }),
    lastPartialHarvestDate: trans('t_date_of_last_partial_harvest'),
    lastMonitoringDistance: trans('t_monitoring_distance_from_pond'),
    plannedHarvestDate: trans('t_final_harvest_date'),
    numberOfPartialHarvests: trans('t_partial_harvest_done'),
    averageWeight: trans('t_abw_g'),
    growthLinear: trans('t_growth_g_wk'),
    growthDaily: trans('t_growth_g_day'),
    growth1WeekAvg: trans('t_growth_monitored_x_week_g_wk', { weeks: 1 }),
    growth2WeekAvg: trans('t_growth_monitored_x_week_g_wk', { weeks: 2 }),
    growth3WeekAvg: trans('t_growth_monitored_x_week_g_wk', { weeks: 3 }),
    growth4w: trans('t_growth_monitored_x_week_g_wk', { weeks: 4 }),
    fcrCumulative: trans('t_fcr_cumulative'),
    fcrWeekly: trans('t_fcr_weekly'),
    adjustedFcr: trans('t_fcr_adjusted'),
    kgPerHaPerDayGivenKg: trans('t_feed_given_daily_kg_ha'),
    weeklyFeedGivenKg: trans('t_feed_given_weekly_kg'),
    totalFeedGivenKg: trans('t_feed_given_kg'),
    totalFeedGivenLbs: trans('t_feed_given_lg'),
    biomassPercentage: trans('t_feed_%_of_biomass'),
    survival: trans('t_survival_live'),
    survivalFeed: trans('t_kampi_survival'),
    survivalWithPartialHarvest: trans('t_survival_include_harvests'),
    biomassLb: trans('t_biomass_in_pond_unit', { unit: unitLabel }),
    biomassLbTotal: trans('t_biomass_include_harvests_unit', { unit: unitLabel }),
    biomassLbHa: trans('t_biomass_in_pond_unit_ha', { unit: unitLabel }),
    totalBiomassLbHa: trans('t_biomass_include_harvests_unit_ha', { unit: unitLabel }),
    animalsRemainingM2: trans('t_animals_in_pond_m2'),
    animalsRemainingHa: trans('t_animals_in_pond_ha'),
    dispersion: trans('t_dispersion_cv'),
    symmetry: trans('t_symmetry'),
    profitPerHaPerDay: trans('t_profit_include_dry_days_ha_day'),
    profitPerPound: trans('t_profit_per_unit_processed', { unit: unitLabel }),
    totalProfit: trans('t_profit'),
    revenuePerPound: trans('t_revenue_per_lb_live'),
    totalRevenuePound: trans('t_revenue_per_lb_include_harvest'),
    totalRevenue: trans('t_revenue'),
    totalCosts: trans('t_cost_$'),
    stockingCosts: trans('t_stocking_cost'),
    stockingCostsMillar: trans('t_cost_millar'),
    cumulativeOverheadCosts: trans('t_overhead_cost_cumulative'),
    cumulativeFeedCosts: trans('t_feed_cost_$'),
    costPerPound: trans('t_cost_per_unit_processed', { unit: unitLabel }),
    costPoundHarvest: trans('t_cost_per_unit_harvested', { unit: unitLabel }),
    feedCostPerKg: trans('t_feed_cost_per_kg'),
    daysUntilHarvest: trans('t_days_until_harvest'),
    daysOffFromOptimal: trans('t_optimal_harvest')
  };
}

type GetPondViewVariableUnitsParams = {
  population: PopulationType;
  variable: FarmSummaryViewVariable;
  profitProjectionToView: PondCardViewType;
  pondSize: number;
};
export function getPondSortValue(params: GetPondViewVariableUnitsParams) {
  const { population, profitProjectionToView, variable, pondSize } = params;
  if (!population) return { value: null, isEmpty: true };

  const {
    seedingAverageWeight,
    history,
    symmetry,
    harvestPlan,
    metadata,
    productionPrediction,
    stockingCostsMillar,
    manualAverageWeights,
    stockedAt,
    stockedAtTimezone,
    seedingQuantity,
    partialHarvest,
    lastMonitoringDistance
  } = population ?? {};

  const { projection, harvestType: oldHarvestType, processorPriceList, harvestDate } = harvestPlan ?? {};
  const harvestType = processorPriceList?.defaultHarvestType ?? oldHarvestType;

  const { plannedHarvestIdx, optimalHarvestIdx, profitProjectionData } = projection ?? {};
  const manuallyMonitored = metadata?.manuallyMonitored;

  const lastHistoryData = history?.[0];
  const lastHistoryDate = lastHistoryData?.date;
  const optimalHarvestDate = profitProjectionData?.[optimalHarvestIdx]?.date;

  const harvestPlanDateTime = harvestDate ? DateTime.fromISO(harvestDate).startOf('day') : null;
  const lastMonitoringDateTime = DateTime.fromISO(lastHistoryDate).startOf('day');
  const isLastMonitoringAfterHarvestPlan =
    profitProjectionToView === 'harvest' && harvestPlanDateTime && lastMonitoringDateTime > harvestPlanDateTime;

  const manualAverageWeight = manualAverageWeights?.find((item) => item.date === lastHistoryDate);
  const productionPredictionHarvestDate = productionPrediction?.find((p) => p.date === harvestDate);
  const predictionDate = productionPredictionHarvestDate?.date;

  const plannedProfitProjection = profitProjectionData?.[plannedHarvestIdx]?.[harvestType as 'headon'];
  const { isEmpty, hasMonitoring } = getPondState({ population });

  const viewData = profitProjectionToView === 'current' ? lastHistoryData : productionPredictionHarvestDate;
  const viewProfitData = profitProjectionToView === 'current' ? lastHistoryData : plannedProfitProjection;

  const {
    averageWeight,
    abw,
    weeklyFcr,
    cumulativeFcr,
    adjustedFcr,
    feedKgByHa,
    feedAsBiomassPercent,
    weeklyFeedGiven,
    totalFeedGivenKg,
    totalFeedGivenLbs,
    totalCosts,
    stockingCosts,
    costPoundHarvest,
    feedCostPerKg,
    cumulativeOverheadCosts,
    cumulativeFeedCosts,
    weeklyGrowth,
    growthTwoWeeks,
    growthThreeWeeks,
    growth4w,
    growthDaily,
    growthLinear,
    biomassLbsByHa,
    biomassLbs,
    totalBiomassLbs,
    totalBiomassLbsByHa,
    survival,
    survivalWithPartialHarvest,
    animalsRemainingHa,
    animalsRemainingM2,
    survivalFeed
  } = viewData ?? {};

  const {
    profitPerHaPerDay,
    profitPerPound,
    costPerPound,
    totalProfit,
    totalRevenue,
    revenuePerPound,
    totalRevenuePound
  } = viewProfitData ?? {};

  const { isInvalid } = isInvalidABW(averageWeight ?? abw);

  const abwValue = isInvalid ? abw : averageWeight;

  const manualAbwOrAbwValue = manualAverageWeight?.averageWeight ?? abwValue;
  const finalAbw =
    profitProjectionToView === 'current' ? manualAbwOrAbwValue || seedingAverageWeight : manualAbwOrAbwValue;
  const showHideCv = !!manuallyMonitored || isInvalid || !weeklyFcr;
  const dispersion = !showHideCv ? lastHistoryData?.weightCv : null;

  const daysUntilHarvest = getDaysDiffBetweenDates({
    baseDate: predictionDate,
    dateToCompare: lastHistoryDate
  });

  const daysOffFromOptimal = getDaysDiffBetweenDates({
    baseDate: optimalHarvestDate,
    dateToCompare: predictionDate
  });

  const symmetryValueMap = {
    normal: 1,
    right: 0,
    left: -1
  };

  const symmetryValue = symmetryValueMap[symmetry?.type];
  const symmetryIfManuallyMonitored = !manuallyMonitored ? symmetryValue : null;
  const stockedAtLuxon = stockedAt ? DateTime.fromISO(stockedAt, { zone: stockedAtTimezone }).startOf('day') : null;
  const sumOfLbsHarvested = partialHarvest?.reduce((acc, curr) => acc + (curr?.lbsHarvested ?? 0), 0);
  const numberOfPartialHarvests = partialHarvest?.length;
  const lastPartialHarvestDateValue = partialHarvest?.[numberOfPartialHarvests - 1]?.date;
  const lastPartialHarvestDateLuxon = lastPartialHarvestDateValue
    ? DateTime.fromISO(lastPartialHarvestDateValue).startOf('day')
    : null;

  const viewVariableValue: Record<FarmSummaryViewVariable, number | string> = {
    pondSize,
    stockingDate: stockedAtLuxon ? stockedAtLuxon.toMillis() : null,
    animalsStockedHa: seedingQuantity && pondSize ? seedingQuantity / pondSize : null,
    partialHarvestLbsHa: sumOfLbsHarvested && pondSize ? sumOfLbsHarvested / pondSize : null,
    lastPartialHarvestDate: lastPartialHarvestDateLuxon ? lastPartialHarvestDateLuxon.toMillis() : null,
    plannedHarvestDate: harvestPlanDateTime ? harvestPlanDateTime.toMillis() : null,
    numberOfPartialHarvests,
    averageWeight: finalAbw,
    growthLinear,
    growthDaily,
    growth1WeekAvg: weeklyGrowth,
    growth2WeekAvg: growthTwoWeeks,
    growth3WeekAvg: growthThreeWeeks,
    growth4w,
    fcrCumulative: cumulativeFcr,
    fcrWeekly: weeklyFcr,
    adjustedFcr,
    kgPerHaPerDayGivenKg: feedKgByHa,
    weeklyFeedGivenKg: weeklyFeedGiven,
    totalFeedGivenKg,
    totalFeedGivenLbs,
    biomassPercentage: feedAsBiomassPercent,
    survival,
    survivalFeed,
    survivalWithPartialHarvest,
    biomassLb: biomassLbs,
    biomassLbTotal: totalBiomassLbs,
    biomassLbHa: biomassLbsByHa,
    totalBiomassLbHa: totalBiomassLbsByHa,
    animalsRemainingM2,
    animalsRemainingHa,
    dispersion,
    symmetry: symmetryIfManuallyMonitored,
    profitPerHaPerDay,
    profitPerPound,
    totalProfit,
    revenuePerPound,
    totalRevenuePound,
    totalRevenue,
    totalCosts,
    stockingCosts,
    stockingCostsMillar,
    cumulativeOverheadCosts,
    cumulativeFeedCosts,
    costPerPound,
    costPoundHarvest,
    feedCostPerKg,
    daysUntilHarvest,
    daysOffFromOptimal,
    lastMonitoringDistance
  };

  return { value: viewVariableValue[variable], isEmpty: isEmpty || !hasMonitoring || isLastMonitoringAfterHarvestPlan };
}
