import { useAppSelector } from '@redux/hooks';
import { FarmSummaryViewVariable } from '@screens/farm-summary/helpers/pond-list';

export type CustomizeViewVariableProps = {
  isAdmin: boolean;
  isSupervisor: boolean;
};

export function useGetFarmSummaryCustomizeViewVariables({ isAdmin, isSupervisor }: CustomizeViewVariableProps) {
  const user = useAppSelector((state) => state.auth.user);
  const productOffering = useAppSelector((state) => state.farm?.currentFarm?.metadata?.productOffering);
  const isVisionOnly = productOffering === 'visionOnly';

  const { preferences } = user ?? {};
  const { viewFields } = preferences ?? {};

  const pondViewVariables: FarmSummaryViewVariable[] = viewFields?.pondVariables ?? [];

  if (isVisionOnly) {
    const visionOnlyValues: FarmSummaryViewVariable[] = [
      'pondSize',
      'stockingDate',
      'animalsRemainingHa',
      'plannedHarvestDate',
      'averageWeight',
      'growthLinear',
      'growthDaily',
      'growth1WeekAvg',
      'growth2WeekAvg',
      'dispersion',
      'symmetry',
      'daysUntilHarvest',
      'lastMonitoringDistance'
    ];
    const visionOnlyDefaultCustomizeViewVariables: FarmSummaryViewVariable[] = [
      'pondSize',
      'stockingDate',
      'animalsRemainingHa',
      'averageWeight',
      'growthLinear',
      'growthDaily',
      'dispersion',
      'daysUntilHarvest'
    ];
    return pondViewVariables?.length
      ? pondViewVariables.filter((variable) => {
          return visionOnlyValues.includes(variable);
        })
      : visionOnlyDefaultCustomizeViewVariables;
  }

  if (isAdmin) {
    const adminDefaultCustomizeViewVariables: FarmSummaryViewVariable[] = [
      'profitPerHaPerDay',
      'costPerPound',
      'totalRevenue',
      'averageWeight',
      'growthDaily',
      'biomassLbHa',
      'survival',
      'fcrCumulative'
    ];
    return pondViewVariables?.length ? pondViewVariables : adminDefaultCustomizeViewVariables;
  }

  const forbiddenVariables: FarmSummaryViewVariable[] = [
    'profitPerHaPerDay',
    'profitPerPound',
    'totalProfit',
    'revenuePerPound',
    'totalRevenuePound',
    'totalRevenue'
  ];

  if (isSupervisor) {
    const superVisorDefaultCustomizeViewVariables: FarmSummaryViewVariable[] = [
      'costPerPound',
      'totalCosts',
      'feedCostPerKg',
      'averageWeight',
      'growthDaily',
      'biomassLbHa',
      'survival',
      'fcrCumulative'
    ];
    if (!pondViewVariables?.length) return superVisorDefaultCustomizeViewVariables;

    return pondViewVariables.filter((variable) => {
      return !forbiddenVariables.includes(variable);
    });
  }

  const forbiddenUserVariables: FarmSummaryViewVariable[] = [
    ...forbiddenVariables,
    'totalCosts',
    'stockingCosts',
    'stockingCostsMillar',
    'cumulativeOverheadCosts',
    'cumulativeFeedCosts',
    'costPerPound',
    'costPoundHarvest',
    'feedCostPerKg'
  ];

  const userDefaultVariables: FarmSummaryViewVariable[] = [
    'averageWeight',
    'growthDaily',
    'growth1WeekAvg',
    'biomassLbHa',
    'survival',
    'fcrCumulative',
    'weeklyFeedGivenKg',
    'survivalFeed'
  ];

  if (!pondViewVariables?.length) return userDefaultVariables;

  return pondViewVariables.filter((variable) => {
    return !forbiddenUserVariables.includes(variable);
  });
}
