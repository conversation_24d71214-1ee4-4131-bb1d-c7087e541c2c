import { useAppSelector } from '@redux/hooks';
import { IndicatorOption, IndicatorValues } from '@components/pond-card/helper';
import { getTrans } from '@i18n/get-trans';
import isUndefined from 'lodash/isUndefined';
import {
  CustomizeViewVariableProps,
  useGetFarmSummaryCustomizeViewVariables
} from '@screens/farm-summary/hooks/use-get-farm-summary-customize-view-variables';

export function useGetFarmSummaryStatusIndicator({ isAdmin, isSupervisor }: CustomizeViewVariableProps) {
  const { trans } = getTrans();

  const user = useAppSelector((state) => state.auth.user);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};

  const customizeViewVariables = useGetFarmSummaryCustomizeViewVariables({ isAdmin, isSupervisor });

  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);
  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');
  const indicatingStatusOptions: IndicatorOption[] = [
    {
      label: trans('t_profit_include_dry_days_ha_day'),
      value: 'profitPerHaPerDay',
      isDisabled: !customizeViewVariables.includes('profitPerHaPerDay')
    },
    {
      label: trans('t_abw_g'),
      value: 'averageWeight',
      isDisabled: !customizeViewVariables.includes('averageWeight')
    },
    {
      label: trans('t_cost_per_unit_processed', { unit: unitLabel }),
      value: 'costPerPound',
      isDisabled: !customizeViewVariables.includes('costPerPound')
    },
    {
      label: trans('t_growth_monitored_x_week_g_wk', { weeks: 1 }),
      value: 'growth1WeekAvg',
      isDisabled: !customizeViewVariables.includes('growth1WeekAvg')
    },
    {
      label: trans('t_growth_g_wk'),
      value: 'growthLinear',
      isDisabled: !customizeViewVariables.includes('growthLinear')
    },
    {
      label: trans('t_survival'),
      value: 'survival',
      isDisabled: !customizeViewVariables.includes('survival')
    },
    {
      label: trans('t_fcr'),
      value: 'fcrCumulative',
      isDisabled: !customizeViewVariables.includes('fcrCumulative')
    }
  ];

  const forbiddenVariables: IndicatorValues[] = ['profitPerHaPerDay'];
  const userForbiddenVariables: IndicatorValues[] = ['costPerPound', ...forbiddenVariables];

  const firstNotDisabledStatusIndicator = indicatingStatusOptions.find((option) => {
    if (isAdmin) {
      return !option.isDisabled;
    }
    if (isSupervisor) {
      return !option.isDisabled && !forbiddenVariables.includes(option.value);
    }

    return !option.isDisabled && !userForbiddenVariables.includes(option.value);
  });

  const filteredIndicatingStatusOptions = indicatingStatusOptions.filter((option) => {
    if (isAdmin) {
      return true;
    }

    if (isSupervisor) {
      return !forbiddenVariables.includes(option.value);
    }

    return !userForbiddenVariables.includes(option.value);
  });

  return { firstNotDisabledStatusIndicator, indicatingStatusOptions: filteredIndicatingStatusOptions };
}
