import { getTrans } from '@i18n/get-trans';
import { FarmSummaryViewVariable } from '@screens/farm-summary/helpers/pond-list';
import { useAppSelector } from '@redux/hooks';
import isUndefined from 'lodash/isUndefined';

type FarmSummaryViewVariableOption = { label: string; value: FarmSummaryViewVariable };
export type FarmSummaryViewVariableGroup = {
  title: string;
  variables: FarmSummaryViewVariableOption[];
};
type FarmSummaryViewVariableGroupTitle =
  | 'cycleInfo'
  | 'weights'
  | 'feed'
  | 'biomass'
  | 'dispersion'
  | 'financials'
  | 'other';

export type UseGetFarmSummaryVariablesReturn = Record<FarmSummaryViewVariableGroupTitle, FarmSummaryViewVariableGroup>;
type UseGetFarmSummaryVariablesProps = { isAdmin: boolean; isSupervisor: boolean };

export function useGetFarmSummaryVariables({
  isAdmin,
  isSupervisor
}: UseGetFarmSummaryVariablesProps): UseGetFarmSummaryVariablesReturn {
  const { trans } = getTrans();
  const isUser = !isAdmin && !isSupervisor;

  const user = useAppSelector((state) => state.auth.user);
  const productOffering = useAppSelector((state) => state.farm?.currentFarm?.metadata?.productOffering);
  const isVisionOnly = productOffering === 'visionOnly';

  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};

  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);
  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');

  if (isVisionOnly) {
    const financials = {} as UseGetFarmSummaryVariablesReturn;
    return {
      cycleInfo: {
        title: trans('t_cycle_information'),
        variables: [
          { label: trans('t_pond_size'), value: 'pondSize' },
          { label: trans('t_stocking_date'), value: 'stockingDate' },
          { label: trans('t_animals_stocked_ha'), value: 'animalsStockedHa' },
          { label: trans('t_final_harvest_date'), value: 'plannedHarvestDate' }
        ]
      },
      weights: {
        title: trans('t_weights'),
        variables: [
          { label: trans('t_abw_g'), value: 'averageWeight' },
          { label: trans('t_growth_g_wk'), value: 'growthLinear' },
          { label: trans('t_growth_g_day'), value: 'growthDaily' },
          { label: trans('t_growth_monitored_x_week_g_wk', { weeks: 1 }), value: 'growth1WeekAvg' },
          { label: trans('t_growth_monitored_x_week_g_wk', { weeks: 2 }), value: 'growth2WeekAvg' }
        ]
      },
      ...financials,
      dispersion: {
        title: trans('t_dispersion'),
        variables: [
          { value: 'dispersion', label: trans('t_dispersion_cv') },
          { value: 'symmetry', label: trans('t_symmetry') }
        ]
      },
      other: {
        title: trans('t_other'),
        variables: [
          { label: trans('t_days_until_harvest'), value: 'daysUntilHarvest' },
          {
            label: trans('t_monitoring_distance_from_pond'),
            value: 'lastMonitoringDistance' as FarmSummaryViewVariable
          }
        ]
      }
    };
  }

  const adminFinancials: FarmSummaryViewVariableOption[] = isAdmin
    ? [
        { label: trans('t_profit_include_dry_days_ha_day'), value: 'profitPerHaPerDay' },
        { label: trans('t_profit_per_unit_processed', { unit: unitLabel }), value: 'profitPerPound' },
        { label: trans('t_profit'), value: 'totalProfit' },
        {
          label: isBiomassUnitLbs ? trans('t_revenue_per_lb_live') : trans('t_revenue_per_kg_live'),
          value: 'revenuePerPound'
        },
        {
          label: isBiomassUnitLbs
            ? trans('t_revenue_per_lb_include_harvest')
            : trans('t_revenue_per_kg_include_harvest'),
          value: 'totalRevenuePound'
        },
        { label: trans('t_revenue'), value: 'totalRevenue' }
      ]
    : [];

  const financials = (
    isUser
      ? {}
      : {
          financials: {
            title: trans('t_financials'),
            variables: [
              ...adminFinancials,
              { label: trans('t_cost_$'), value: 'totalCosts' },
              { label: trans('t_stocking_cost'), value: 'stockingCosts' },
              { label: trans('t_cost_millar'), value: 'stockingCostsMillar' },
              { label: trans('t_overhead_cost_cumulative'), value: 'cumulativeOverheadCosts' },
              { label: trans('t_feed_cost_$'), value: 'cumulativeFeedCosts' },
              { label: trans('t_feed_cost_per_kg'), value: 'feedCostPerKg' },
              {
                label: trans('t_cost_per_unit_processed', { unit: unitLabel }),
                value: 'costPerPound'
              },
              {
                label: trans('t_cost_per_unit_harvested', { unit: unitLabel }),
                value: 'costPoundHarvest'
              }
            ]
          }
        }
  ) as UseGetFarmSummaryVariablesReturn;

  return {
    cycleInfo: {
      title: trans('t_cycle_information'),
      variables: [
        { label: trans('t_pond_size'), value: 'pondSize' },
        { label: trans('t_stocking_date'), value: 'stockingDate' },
        { label: trans('t_animals_stocked_ha'), value: 'animalsStockedHa' },
        { label: trans('t_date_of_last_partial_harvest'), value: 'lastPartialHarvestDate' },
        { label: trans('t_final_harvest_date'), value: 'plannedHarvestDate' },
        { label: trans('t_partial_harvest_done'), value: 'numberOfPartialHarvests' }
      ]
    },
    weights: {
      title: trans('t_weights'),
      variables: [
        { label: trans('t_abw_g'), value: 'averageWeight' },
        { label: trans('t_growth_g_wk'), value: 'growthLinear' },
        { label: trans('t_growth_g_day'), value: 'growthDaily' },
        { label: trans('t_growth_monitored_x_week_g_wk', { weeks: 1 }), value: 'growth1WeekAvg' },
        { label: trans('t_growth_monitored_x_week_g_wk', { weeks: 2 }), value: 'growth2WeekAvg' },
        { label: trans('t_growth_monitored_x_week_g_wk', { weeks: 3 }), value: 'growth3WeekAvg' },
        { label: trans('t_growth_monitored_x_week_g_wk', { weeks: 4 }), value: 'growth4w' }
      ]
    },
    feed: {
      title: trans('t_feed'),
      variables: [
        { label: trans('t_fcr_cumulative'), value: 'fcrCumulative' },
        { label: trans('t_fcr_weekly'), value: 'fcrWeekly' },
        { label: trans('t_fcr_adjusted'), value: 'adjustedFcr' },
        { label: trans('t_feed_given_daily_kg_ha'), value: 'kgPerHaPerDayGivenKg' },
        { label: trans('t_feed_given_weekly_kg'), value: 'weeklyFeedGivenKg' },
        { label: trans('t_feed_given_kg'), value: 'totalFeedGivenKg' },
        { label: trans('t_feed_given_lg'), value: 'totalFeedGivenLbs' },
        { label: trans('t_feed_%_of_biomass'), value: 'biomassPercentage' }
      ]
    },
    biomass: {
      title: trans('t_biomass'),
      variables: [
        { value: 'survival', label: trans('t_survival_live') },
        { value: 'survivalFeed', label: trans('t_kampi_survival') },
        { value: 'survivalWithPartialHarvest', label: trans('t_survival_include_harvests') },
        { value: 'biomassLb', label: trans('t_biomass_in_pond_unit', { unit: unitLabel }) },
        {
          value: 'biomassLbTotal',
          label: trans('t_biomass_include_harvests_unit', { unit: unitLabel })
        },
        {
          value: 'biomassLbHa',
          label: trans('t_biomass_in_pond_unit_ha', { unit: unitLabel })
        },
        {
          value: 'totalBiomassLbHa',
          label: trans('t_biomass_include_harvests_unit_ha', { unit: unitLabel })
        },
        {
          label: trans('t_biomass_from_partials_unit_ha', {
            unit: unitLabel
          }),
          value: 'partialHarvestLbsHa'
        },
        { value: 'animalsRemainingM2', label: trans('t_animals_in_pond_m2') },
        { value: 'animalsRemainingHa', label: trans('t_animals_in_pond_ha') }
      ]
    },
    dispersion: {
      title: trans('t_dispersion'),
      variables: [
        { value: 'dispersion', label: trans('t_dispersion_cv') },
        { value: 'symmetry', label: trans('t_symmetry') }
      ]
    },
    ...financials,
    other: {
      title: trans('t_other'),
      variables: [
        { label: trans('t_days_until_harvest'), value: 'daysUntilHarvest' },
        { label: trans('t_optimal_harvest'), value: 'daysOffFromOptimal' },
        {
          label: trans('t_monitoring_distance_from_pond'),
          value: 'lastMonitoringDistance' as FarmSummaryViewVariable
        }
      ]
    }
  };
}
