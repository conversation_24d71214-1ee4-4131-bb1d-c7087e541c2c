import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { usePermission } from '@hooks/use-permission';
import { HarvestSummaryViewVariable } from '@screens/farm-summary/helpers/harvest-summary';
import isUndefined from 'lodash/isUndefined';

type HarvestSummaryViewVariableOption = { label: string; value: HarvestSummaryViewVariable };

type HarvestSummaryViewVariableGroup = {
  title: string;
  variables: HarvestSummaryViewVariableOption[];
};

type HarvestSummaryViewVariableGroupTitle = 'general' | 'financials' | 'biomass';

type UseGetHarvestSummaryVariablesReturn = Record<
  HarvestSummaryViewVariableGroupTitle,
  HarvestSummaryViewVariableGroup
>;

export function useGetHarvestSummaryVariables() {
  const { trans } = getTrans();

  const currentFarm = useAppSelector((state) => state.farm.currentFarm);
  const { _id: currentFarmId } = currentFarm ?? {};

  const isAdmin = usePermission({ role: 'admin', entity: 'farm', entityId: currentFarmId });
  const isSupervisor = usePermission({ role: 'supervisor', entity: 'farm', entityId: currentFarmId });
  const user = useAppSelector((state) => state.auth.user);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);

  const biomassTitle = isBiomassUnitLbs ? trans('t_biomass_lb') : trans('t_biomass_kg');
  const biomassHaTitle = isBiomassUnitLbs ? trans('t_biomass_lb_ha') : trans('t_biomass_kg_ha');

  const isUser = !isAdmin && !isSupervisor;

  const adminFinancials = isAdmin
    ? [
        { label: trans('t_rev'), value: 'revenue' },
        { label: trans('t_rev_per_pound_processed'), value: 'revenuePerPound' },
        { label: trans('t_rev_per_pound_harvested'), value: 'revenuePoundHarvest' }
      ]
    : [];

  const financials = isUser
    ? ({} as UseGetHarvestSummaryVariablesReturn)
    : ({
        financials: {
          title: trans('t_financials'),
          variables: [...adminFinancials]
        }
      } as UseGetHarvestSummaryVariablesReturn);

  return {
    general: {
      title: trans('t_general'),
      variables: [
        { label: trans('t_type'), value: 'type' },
        { label: trans('t_status'), value: 'state' },
        { label: trans('t_days_of_culture'), value: 'cycleDays' },
        { label: trans('t_abw_g'), value: 'averageWeight' },
        { label: trans('t_harvest_type'), value: 'harvestType' },
        { label: trans('t_processor'), value: 'selectedProcessorId' }
      ]
    },
    ...financials,
    biomass: {
      title: trans('t_biomass'),
      variables: [
        { label: biomassTitle, value: 'biomass' },
        { label: biomassHaTitle, value: 'biomassPerHa' },
        { label: trans('t_animals_harvested'), value: 'animalsHarvested' },
        { label: trans('t_animals_harvested_per_m2'), value: 'animalsHarvestedM2' },
        { label: trans('t_animals_harvested_ha'), value: 'animalsHarvestedHa' },
        { label: trans('t_survival'), value: 'survival' }
      ]
    }
  };
}
