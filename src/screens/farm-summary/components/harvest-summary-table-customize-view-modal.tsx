import { ReactNode } from 'react';
import { Box, BoxProps, useDisclosure } from '@chakra-ui/react';
import { CustomizeViewModal } from '@components/common/customize-view-modal';
import { useUpdateUserPreferencesApi } from '@screens/my-account/hooks/use-update-user-preferences';
import { useGetHarvestSummaryVariables } from '@screens/farm-summary/hooks/use-get-harvest-summary-variables';
import { useAppSelector } from '@redux/hooks';
import { useGetHarvestSummaryCustomizeViewVariables } from '@screens/farm-summary/hooks/use-get-harvest-summary-customize-view-variables';
import { useGetHarvestSummaryVariableLabels } from '@screens/farm-summary/helpers/harvest-summary';
import omit from 'lodash/omit';

interface HarvestSummaryTableCustomizeViewModalProps extends BoxProps {
  children?: ReactNode;
}

export function HarvestSummaryTableCustomizeViewModal(props: HarvestSummaryTableCustomizeViewModalProps) {
  const { children, ...rest } = props;

  const user = useAppSelector((state) => state.auth.user);

  const { preferences } = user ?? {};
  const { viewFields, ...prevPreferences } = preferences ?? {};

  const { open, onOpen, onClose } = useDisclosure();
  const [{ isLoading }, updateUser] = useUpdateUserPreferencesApi();

  const harvestSummaryVariables = useGetHarvestSummaryVariables();
  const pondVariables = useGetHarvestSummaryCustomizeViewVariables();

  const onSubmit = (selectedKeys: string[]) => {
    updateUser({
      params: {
        preferences: {
          ...omit(prevPreferences, '__typename'),
          viewFields: {
            ...viewFields,
            harvestSummaryVariables: selectedKeys
          }
        }
      },
      successCallback: onClose
    });
  };

  const variableLabels = useGetHarvestSummaryVariableLabels();

  return (
    <CustomizeViewModal
      isOpen={open}
      onClose={onClose}
      onSubmit={onSubmit}
      isLoading={isLoading}
      variableLabels={variableLabels}
      pondVariables={pondVariables}
      pondViewVariables={harvestSummaryVariables}
      hasNoLimit
    >
      <Box onClick={onOpen} {...rest}>
        {children}
      </Box>
    </CustomizeViewModal>
  );
}
