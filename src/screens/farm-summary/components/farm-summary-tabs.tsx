import { Flex, FlexProps, Text } from '@chakra-ui/react';
import { actionsName } from '@utils/segment';
import { goToUrl } from '@utils/url';
import { getTrans } from '@i18n/get-trans';
import { useRouter } from 'next/router';
import { useAnalytics } from '@hooks/use-analytics';
import { useAppSelector } from '@redux/hooks';

export function FarmSummaryTabs(props: FlexProps) {
  const { trans } = getTrans();
  const { trackAction } = useAnalytics();
  const { query, route } = useRouter();
  const defaultTab = route !== '/farm/[farmEid]/harvests' ? 'ponds' : undefined;
  const activeTab = query?.tab || defaultTab;

  const productOffering = useAppSelector((state) => state.farm?.currentFarm?.metadata?.productOffering);
  const isVisionOnly = productOffering === 'visionOnly';
  const tabs = [
    { route: '/farm/[farmEid]', tab: 'ponds', label: trans('t_ponds') },
    ...(!isVisionOnly ? [{ route: '/farm/[farmEid]/harvests', label: trans('t_harvests') }] : []),
    { route: '/farm/[farmEid]', tab: 'parameters', label: trans('t_parameters') }
  ];
  return (
    <Flex align='center' rounded='4xl' bgColor='gray.200' w='fit-content' p='3xs' {...props}>
      {tabs.map((tab, index) => (
        <Text
          h='36px'
          key={index}
          textStyle='button.100'
          rounded='4xl'
          display='flex'
          py='sm-alt'
          px='2sm'
          cursor='pointer'
          color='text.gray.disabled'
          userSelect='none'
          alignItems='center'
          justifyContent='center'
          {...(tab.route === route && activeTab === tab.tab && { color: 'white', bgColor: 'squidInkPowder.800' })}
          onClick={() => {
            trackAction(actionsName.farmSummaryTabClicked, { tabName: tab.tab }).then();
            goToUrl({ route: tab.route, params: { ...query, tab: tab.tab } });
          }}
        >
          {tab.label}
        </Text>
      ))}
    </Flex>
  );
}
