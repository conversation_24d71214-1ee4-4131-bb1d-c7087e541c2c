import { Collapsible } from '@chakra-ui/react';
import { HarvestSummaryTable } from '@screens/farm-summary/components/harvest-summary-table';
import { HarvestSummaryWeeklyAggregation } from '@screens/farm-summary/components/harvest-summary-weekly-aggregation';
import {
  getPopulationHarvestsData,
  HarvestsInfoType
} from '@screens/pond/components/pond-harvests-tab/pond-harvests-helpers';
import { useAppSelector } from '@redux/hooks';
import { MobileLandscapeOnlyModal } from '@components/mobile-view/mobile-landscape-only-modal';
import { getTrans } from '@i18n/get-trans';
import { ViewTableIcon } from '@icons/view-table/view-table-icon';
import { useFormContext } from 'react-hook-form';
import { useMemo } from 'react';
import { DateTime } from 'luxon';
import { CurrentFarmPonds } from '@redux/farm/set-current-farm-ponds';

export type HarvestTableFilterFormValues = {
  search: string;
  typeFilters: {
    final: boolean;
    partial: boolean;
  };
  stateFilters: {
    recorded: boolean;
    notRecorded: boolean;
  };
  dateFilters: {
    next30d: boolean;
    past30d: boolean;
    customRange: {
      startDate: Date | null;
      endDate: Date | null;
    };
  };
};

type HarvestTabProps = {
  isHarvestSummaryTableVisible: boolean;
  ponds: CurrentFarmPonds;
};

export function HarvestTab(props: HarvestTabProps) {
  const { isHarvestSummaryTableVisible, ponds } = props;
  const { trans } = getTrans();

  const currentFarm = useAppSelector((state) => state.farm.currentFarm);

  const pondList = ponds ?? [];

  const { watch } = useFormContext<HarvestTableFilterFormValues>();

  const formValues = watch();

  const harvestSummaryData = useMemo(() => {
    const data: HarvestsInfoType[] = [];

    for (const pond of pondList) {
      const { currentPopulation, _id: pondId } = pond ?? {};
      if (!pondId || !currentPopulation?._id || !currentPopulation?.stockedAt) continue;

      const { fullHarvestData, partialHarvestData } = getPopulationHarvestsData({
        pond,
        population: currentPopulation,
        farm: currentFarm
      });

      data.push(...fullHarvestData, ...partialHarvestData);
    }

    return data.filter((row) => {
      const typeFiltersPassed =
        (!formValues.typeFilters.final && !formValues.typeFilters.partial) ||
        (row.type === 'final' && formValues.typeFilters.final) ||
        (row.type === 'partial' && formValues.typeFilters.partial);
      if (!typeFiltersPassed) return false;

      const stateFiltersPassed =
        (!formValues.stateFilters.recorded && !formValues.stateFilters.notRecorded) ||
        (row.state === 'recorded' && formValues.stateFilters.recorded) ||
        (row.state === 'planned' && formValues.stateFilters.notRecorded);
      if (!stateFiltersPassed) return false;

      const rowDate = DateTime.fromISO(row.harvestDate).startOf('day');
      const today = DateTime.now().startOf('day');
      const next30Days = today.plus({ days: 30 });
      const past30Days = today.minus({ days: 30 });

      const isNext30Days = rowDate <= next30Days && rowDate > today;
      const isPast30Days = rowDate >= past30Days && rowDate <= today;

      const isInCustomRange =
        formValues.dateFilters.customRange?.startDate && formValues.dateFilters.customRange?.endDate
          ? rowDate >= DateTime.fromJSDate(formValues.dateFilters.customRange.startDate).startOf('day') &&
            rowDate <= DateTime.fromJSDate(formValues.dateFilters.customRange.endDate).startOf('day')
          : true;

      const dateFiltersPassed =
        (!formValues.dateFilters.next30d &&
          !formValues.dateFilters.past30d &&
          !formValues.dateFilters.customRange?.startDate &&
          !formValues.dateFilters.customRange?.endDate) ||
        (formValues.dateFilters.next30d && isNext30Days) ||
        (formValues.dateFilters.past30d && isPast30Days) ||
        (formValues.dateFilters.customRange?.startDate &&
          formValues.dateFilters.customRange?.endDate &&
          isInCustomRange);

      if (!dateFiltersPassed) return false;

      if (formValues.search) {
        const normalizedQuery = formValues.search.toLowerCase();
        const normalizedPondName = row.pondName.toLowerCase();
        if (!normalizedPondName.includes(normalizedQuery)) return false;
      }

      return true;
    });
  }, [formValues, JSON.stringify(pondList)]);

  return (
    <>
      {isHarvestSummaryTableVisible && (
        <Collapsible.Root display={{ base: 'none', lg: 'block' }} open={isHarvestSummaryTableVisible}>
          <Collapsible.Content>
            <HarvestSummaryWeeklyAggregation harvestSummaryData={harvestSummaryData} />
          </Collapsible.Content>
        </Collapsible.Root>
      )}
      <MobileLandscapeOnlyModal
        title={currentFarm?.name}
        buttonIcon={<ViewTableIcon />}
        buttonProps={{ mt: 'md-alt' }}
        buttonTitle={trans('t_click_here_to_see_table')}
      >
        <HarvestSummaryWeeklyAggregation harvestSummaryData={harvestSummaryData} />
      </MobileLandscapeOnlyModal>
      <HarvestSummaryTable harvestSummaryData={harvestSummaryData} />
    </>
  );
}
