import { Flex, Table, TableCellProps, Text } from '@chakra-ui/react';
import React, { ReactNode, useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { DailyParametersFormValues } from './parameters-tab';
import { useAppSelector } from '@redux/hooks';
import { getTrans } from '@i18n/get-trans';
import { SwapVertical } from '@icons/swap/swap-vertical';
import { BaseLink } from '@components/base/base-link';
import { useRouter } from 'next/router';
import { slugify } from '@utils/string';
import { FarmDailyParameters } from '@xpertsea/module-farm-sdk';
import { DateTime } from 'luxon';
import { sortArray, sortCompareNumber, sortCompareString } from '@utils/sort-array';
import { DailyParams, useGetDailyDataTrans } from '@screens/settings/hooks/use-get-daily-data-trans';
import { useAnalytics } from '@hooks/use-analytics';
import { actionsName } from '@utils/segment';
import { getPondsDailyParamsForDate } from '@screens/pond/components/modals/helpers/get-ponds-with-daily-params';
import get from 'lodash/get';
import { TableRootContainer } from '@components/table/table-root-container';

const firstColumnWidth = 125;

type TableDataType = {
  pondName: string;
  cycle: string;
  paramsData: Record<string, number>;
  eid: number;
};

export function ParametersSummaryTable({ dailyParameters }: { dailyParameters: FarmDailyParameters[] }) {
  const { trans } = getTrans();

  const { trackAction } = useAnalytics();
  const ponds = useParamsData();
  const { sortConfig, handleSort } = useSort();

  const sortedPonds = useMemo(() => {
    trackAction(actionsName.dailyParamsListSorted, {
      key: sortConfig.field,
      order: sortConfig.direction
    }).then();

    if (sortConfig.field === 'pondName' || sortConfig.field === 'cycle') {
      return sortArray({
        list: ponds,
        sortDir: sortConfig.direction,
        compareFunction: (a, b) => {
          const firstItemValue = get(a, sortConfig.field);
          const secondItemValue = get(b, sortConfig.field);
          return sortCompareString(firstItemValue, secondItemValue);
        }
      });
    }

    return [...ponds].sort((a, b) => {
      return sortCompareNumber({
        firstItem: a.paramsData[sortConfig.field],
        secondItem: b.paramsData[sortConfig.field],
        sortDir: sortConfig.direction,
        hasEmptyValuePriority: false
      });
    });
  }, [sortConfig.field, sortConfig.direction, ponds]);

  const cellWidthValues = (value: number) => ({
    w: `${value}px`,
    minW: `${value}px`,
    maxW: `${value}px`
  });

  const { dailyAbbrevWithUnitsTransMap } = useGetDailyDataTrans();

  return (
    <TableRootContainer firstColumnWidth={firstColumnWidth}>
      <Table.Header>
        <Table.Row>
          <HeaderCell
            cellInfo={{
              name: trans('t_pond'),
              SortComp: <SortIndicator field={'pondName'} sortConfig={sortConfig} handleOnClick={handleSort} />
            }}
            zIndex={1}
            borderTopLeftRadius='2xl'
            position='sticky !important'
            left='0px !important'
            {...cellWidthValues(firstColumnWidth)}
          />

          <HeaderCell
            cellInfo={{
              name: trans('t_cycle'),
              SortComp: <SortIndicator field={'cycle'} sortConfig={sortConfig} handleOnClick={handleSort} />
            }}
          />

          {dailyParameters?.length ? (
            dailyParameters.map((param) => (
              <HeaderCell
                key={param._id}
                cellInfo={{
                  name: dailyAbbrevWithUnitsTransMap[param.name as DailyParams] ?? param.name,
                  SortComp: <SortIndicator field={param._id} sortConfig={sortConfig} handleOnClick={handleSort} />
                }}
              />
            ))
          ) : (
            <HeaderCell cellInfo={{ name: '' }} />
          )}
        </Table.Row>
      </Table.Header>

      <Table.Body>
        {sortedPonds.map((pond, index) => (
          <Row key={index} pond={pond} dailyParameters={dailyParameters} />
        ))}
      </Table.Body>
    </TableRootContainer>
  );
}

type SortConfigType = {
  direction: 'asc' | 'desc';
  field: 'pondName' | 'cycle';
};

function SortIndicator({
  field,
  handleOnClick,
  sortConfig
}: {
  field: SortConfigType['field'];
  handleOnClick: (field: SortConfigType['field']) => void;
  sortConfig: SortConfigType;
}) {
  return (
    <SwapVertical
      cursor='pointer'
      userSelect='none'
      onClick={() => handleOnClick(field)}
      upArrowColor={sortConfig.field === field && sortConfig.direction === 'asc' ? 'text.gray' : 'gray.400'}
      downArrowColor={sortConfig.field === field && sortConfig.direction === 'desc' ? 'text.gray' : 'gray.400'}
    />
  );
}
function HeaderCell(props: TableCellProps & { cellInfo: { name: string; SortComp?: ReactNode } }) {
  const { cellInfo, ...rest } = props;

  return (
    <Table.ColumnHeader {...rest}>
      <Flex align='center' gap='md' justify='space-between'>
        <Text size={{ base: 'label300', lg: 'label200' }}>{cellInfo.name}</Text>
        {!!cellInfo?.SortComp && <>{cellInfo?.SortComp}</>}
      </Flex>
    </Table.ColumnHeader>
  );
}

function Row({ pond, dailyParameters }: { pond: TableDataType; dailyParameters: FarmDailyParameters[] }) {
  const { query } = useRouter();
  const { farmEid } = (query ?? {}) as { farmEid?: string };

  const { pondName, paramsData, cycle, eid } = pond;

  return (
    <Table.Row>
      <Table.Cell
        zIndex={1}
        w={`${firstColumnWidth}px`}
        minW={`${firstColumnWidth}px`}
        maxW={`${firstColumnWidth}px`}
        position='sticky !important'
        left='0px !important'
      >
        {!pondName && '-'}
        {pondName && (
          <BaseLink
            route='/farm/[farmEid]/pond/[pondEid]'
            _hover={{ textDecoration: 'underline' }}
            params={{ farmEid, pondEid: slugify(`${eid}-${pondName}`) }}
          >
            <Text
              size={{ base: 'light300', lg: 'light200' }}
              overflow='hidden'
              textOverflow='ellipsis'
              maxW='105px'
              title={pondName}
            >
              {pondName}
            </Text>
          </BaseLink>
        )}
      </Table.Cell>

      <Table.Cell>
        <Text
          size={{ base: 'light300', lg: 'light200' }}
          overflow='hidden'
          textOverflow='ellipsis'
          maxW='70px'
          title={cycle}
        >
          {cycle ?? '-'}
        </Text>
      </Table.Cell>

      {dailyParameters?.length ? (
        dailyParameters.map((param) => (
          <Table.Cell key={param._id}>
            <Text size={{ base: 'light300', lg: 'light200' }}>{paramsData?.[param._id] ?? '-'}</Text>
          </Table.Cell>
        ))
      ) : (
        <Table.Cell />
      )}
    </Table.Row>
  );
}

const useParamsData = () => {
  const currentFarm = useAppSelector((state) => state.farm?.currentFarm) ?? {};
  const ponds = useAppSelector((state) => state.farm?.currentFarmPonds) ?? [];

  const { watch: globalFormWatch } = useFormContext<DailyParametersFormValues>();
  const { dailyParamDateToShow, searchQuery } = globalFormWatch();

  const dailyParameterDateToShowFormatted = DateTime.fromJSDate(dailyParamDateToShow).toFormat('yyyy-MM-dd');

  const filteredPonds = useMemo(() => {
    if (!searchQuery?.length || !ponds?.length) return ponds;

    return ponds.filter((pond) => pond.name.toLowerCase().includes(searchQuery.toLowerCase()));
  }, [ponds, searchQuery]);

  return useMemo(() => {
    if (!filteredPonds?.length) return [];

    return getPondsDailyParamsForDate(filteredPonds, dailyParameterDateToShowFormatted);
  }, [currentFarm?._id, filteredPonds, dailyParameterDateToShowFormatted]);
};

const useSort = () => {
  const [sortConfig, setSortConfig] = useState<SortConfigType>({
    direction: 'asc',
    field: 'pondName'
  });

  const handleSort = (field: SortConfigType['field']) => {
    setSortConfig((prevConfig) => ({
      field,
      direction: prevConfig.field === field && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  return { sortConfig, handleSort };
};
