import { Dispatch, SetStateAction, useState } from 'react';
import { getTrans } from '@i18n/get-trans';
import { BoxProps, Flex, Text } from '@chakra-ui/react';
import { AcrossAllPondsChart } from '@screens/farm-summary/components/across-all-ponds-chart';
import { CurrentFarmPonds } from '@redux/farm/set-current-farm-ponds';
import { actionsName } from '@utils/segment';
import { useAnalytics } from '@hooks/use-analytics';
import { ChevronDownFilled } from '@icons/chevron-down/chevron-down-filled';

import { CardContainer } from '@components/card-container/card-container';
import { Close } from '@icons/close/close';
import { PondCardViewType } from '@components/pond-card/helper';

import { AdminOrSupervisorWrapper } from '@components/permission-wrappers/admin-or-supervisor-wrapper';
import { useAppSelector } from '@redux/hooks';
import { usePermission } from '@hooks/use-permission';
import isUndefined from 'lodash/isUndefined';
import { ChevronUpFilled } from '@icons/chevron-up/chevron-up-filled';
import { Expand } from '@icons/expand/expand';
import { MenuButton, MenuContent, MenuItem, MenuItemGroup, MenuRoot, MenuSeparator } from '@components/ui/menu';

export type ProductionOptions =
  | 'averageWeight'
  | 'biomass'
  | 'biomassPerHa'
  | 'growth'
  | 'cumulativeFcr'
  | 'feedKgByHa'
  | 'survival'
  | 'dispersion';

export type FinancialOptions =
  | 'costPerPound'
  | 'totalCosts'
  | 'revenuePerPound'
  | 'totalRevenue'
  | 'profitPerPound'
  | 'totalProfit'
  | 'profitPerHaPerDay';

type ProductionOptionValue = { value: ProductionOptions; label: string };
type FinancialOptionValue = { value: FinancialOptions; label: string };
export type SelectedOptionValue = FinancialOptionValue | ProductionOptionValue;

type AcrossAllPondsOptionsData = {
  production: ProductionOptionValue[];
  financials?: FinancialOptionValue[];
};

export interface AcrossAllPondsChartCardProps extends BoxProps {
  currentFarmPonds: CurrentFarmPonds;
  profitProjectionToView: PondCardViewType;
}

export function AcrossAllPondsChartCard(props: AcrossAllPondsChartCardProps) {
  const { currentFarmPonds = [], profitProjectionToView, ...rest } = props;

  const { trans } = getTrans();

  const [selectedAcrossAllPondsOption, setSelectedAcrossAllPondsOption] = useState<SelectedOptionValue>({
    value: 'averageWeight',
    label: trans('t_abw_g')
  });
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <CardContainer {...rest}>
      <Flex justify='space-between' align='center' mb='md-alt' gap='sm-alt'>
        <Flex align='center' gap='md-alt'>
          <AcrossAllPondsSelectVariable
            selectedAcrossAllPondsOption={selectedAcrossAllPondsOption}
            setSelectedAcrossAllPondsOption={setSelectedAcrossAllPondsOption}
          />
          <Text color='text.gray.weak' size='light300'>
            {trans('t_data_shown_across_all_ponds')}
          </Text>
        </Flex>

        {!isExpanded && (
          <Expand
            cursor='pointer'
            w='20px'
            h='20px'
            userSelect='none'
            _hover={{ opacity: 0.8 }}
            onClick={() => setIsExpanded(true)}
          />
        )}
        {isExpanded && (
          <Close
            cursor='pointer'
            w='20px'
            h='20px'
            userSelect='none'
            _hover={{ opacity: 0.8 }}
            onClick={() => setIsExpanded(false)}
          />
        )}
      </Flex>

      <AcrossAllPondsChart
        acrossAllPondsOption={selectedAcrossAllPondsOption}
        currentFarmPonds={currentFarmPonds}
        isExpanded={isExpanded}
        profitProjectionToView={profitProjectionToView}
      />
    </CardContainer>
  );
}

interface AcrossAllPondsSelectVariableProps {
  setSelectedAcrossAllPondsOption: Dispatch<SetStateAction<SelectedOptionValue>>;
  selectedAcrossAllPondsOption: SelectedOptionValue;
}

export function AcrossAllPondsSelectVariable(pros: AcrossAllPondsSelectVariableProps) {
  const { setSelectedAcrossAllPondsOption, selectedAcrossAllPondsOption } = pros;
  const { trackAction } = useAnalytics();
  const { trans } = getTrans();

  const currentFarmId = useAppSelector((state) => state.farm?.currentFarm?._id);
  const productOffering = useAppSelector((state) => state.farm?.currentFarm?.metadata?.productOffering);
  const user = useAppSelector((state) => state.auth.user);

  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};

  const isAdmin = usePermission({
    role: 'admin',
    entity: 'farm',
    entityId: currentFarmId
  });

  const isVisionOnly = productOffering === 'visionOnly';

  const [open, setOpen] = useState(false);

  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);
  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');

  let acrossAllPondsOptions: AcrossAllPondsOptionsData;

  if (isVisionOnly) {
    acrossAllPondsOptions = {
      production: [
        { value: 'averageWeight', label: trans('t_abw_g') },
        { value: 'growth', label: trans('t_growth_monitored_x_week_g_wk', { weeks: 2 }) },
        { value: 'dispersion', label: trans('t_dispersion') }
      ]
    };
  } else {
    const adminOnlyFinancials: FinancialOptionValue[] = isAdmin
      ? [
          { value: 'revenuePerPound', label: isBiomassUnitLbs ? trans('t_rev_lb') : trans('t_rev_kg') },
          { value: 'totalRevenue', label: trans('t_revenue') },
          { value: 'profitPerPound', label: trans('t_profit_per_unit_processed', { unit: unitLabel }) },
          { value: 'totalProfit', label: trans('t_profit') },
          { value: 'profitPerHaPerDay', label: trans('t_profit_include_dry_days_ha_day') }
        ]
      : [];

    acrossAllPondsOptions = {
      production: [
        { value: 'averageWeight', label: trans('t_abw_g') },
        { value: 'biomass', label: isBiomassUnitLbs ? trans('t_biomass_lb') : trans('t_biomass_kg') },
        {
          value: 'biomassPerHa',
          label: isBiomassUnitLbs ? trans('t_biomass_lb_ha') : trans('t_biomass_kg_ha')
        },
        { value: 'growth', label: trans('t_growth_monitored_x_week_g_wk', { weeks: 2 }) },
        { value: 'cumulativeFcr', label: trans('t_fcr_cumulative') },
        { value: 'feedKgByHa', label: trans('t_feed_given_daily_kg_ha') },
        { value: 'survival', label: trans('t_survival') },
        { value: 'dispersion', label: trans('t_dispersion') }
      ],
      financials: [
        { value: 'costPerPound', label: trans('t_cost_per_unit_processed', { unit: unitLabel }) },
        { value: 'totalCosts', label: trans('t_cost_$') },
        ...adminOnlyFinancials
      ]
    };
  }

  return (
    <MenuRoot
      onOpenChange={(value) => setOpen(value.open)}
      positioning={{ offset: { mainAxis: -2, crossAxis: 12 } }}
      highlightedValue={selectedAcrossAllPondsOption.value}
    >
      <MenuButton px={0} h='fit-content' selectVariant='secondary' bgColor='transparent'>
        {selectedAcrossAllPondsOption.label}
        {open ? (
          <ChevronUpFilled ms='xs-alt' color='icon.gray' hasBackground={false} />
        ) : (
          <ChevronDownFilled ms='xs-alt' color='icon.gray' hasBackground={false} />
        )}
      </MenuButton>
      <MenuContent p='md' maxH='55vh' portalled={false}>
        <Text size='label100' mb='md'>
          {trans('t_production')}
        </Text>
        <MenuItemGroup display='flex' flexDirection='column' gap='3xs'>
          {acrossAllPondsOptions.production.map((option) => {
            return (
              <MenuItem
                key={option.value}
                value={option.value}
                onClick={() => {
                  setSelectedAcrossAllPondsOption(option);
                  trackAction(actionsName.farmSummaryChartDropdownItemSelected, { option: option.label }).then();
                }}
              >
                {option.label}
              </MenuItem>
            );
          })}
        </MenuItemGroup>

        {!!acrossAllPondsOptions.financials?.length && (
          <AdminOrSupervisorWrapper>
            <MenuSeparator my='2sm' borderColor='gray.400' />

            <Text size='label100' mb='md'>
              {trans('t_financials')}
            </Text>
            <MenuItemGroup display='flex' flexDirection='column' gap='3xs'>
              {acrossAllPondsOptions.financials?.map((option) => {
                return (
                  <MenuItem
                    key={option.value}
                    value={option.value}
                    onClick={() => {
                      setSelectedAcrossAllPondsOption(option);
                      trackAction(actionsName.farmSummaryChartDropdownItemSelected, { option: option.label }).then();
                    }}
                  >
                    {option.label}
                  </MenuItem>
                );
              })}
            </MenuItemGroup>
          </AdminOrSupervisorWrapper>
        )}
      </MenuContent>
    </MenuRoot>
  );
}
