import { Box, Flex, Skeleton } from '@chakra-ui/react';
import React, { useRef } from 'react';
import { DateTime } from 'luxon';
import { ArrowBigLeftFilled } from '@icons/arrow-big-left/arrow-big-left-filled';
import { FormControlDateInput } from '@components/form/form-control-date-input';
import { ArrowBigRightFilled } from '@icons/arrow-big-right/arrow-big-right-filled';
import { FormProvider, useForm, useFormContext } from 'react-hook-form';
import { CloseFilled } from '@icons/close/close-filled';
import { BaseSearchInput } from '@components/base/base-search-input';
import { ParametersSummaryCustomizeViewModal } from '@screens/farm-summary/components/parameters-summary-customize-view-modal';
import { useGetParametersSummaryCustomizeView } from '@screens/farm-summary/hooks/use-get-parameters-summary-customize-view';
import { ParametersSummaryTable } from '@screens/farm-summary/components/parameters-summary-table';
import { getTrans } from '@i18n/get-trans';
import { useAnalytics } from '@hooks/use-analytics';
import { MenuButton, MenuContent, MenuItem, MenuRoot } from '@components/ui/menu';
import { actionsName } from '@utils/segment';
import { MoreHoriz } from '@icons/more-horiz/more-horiz';
import { ExportDailyParamDataModal } from '@screens/pond/components/modals/export-daily-param-data-modal';
import { FarmDailyParameters } from '@xpertsea/module-farm-sdk';

export type DailyParametersFormValues = { dailyParamDateToShow: Date; searchQuery: string };

export function ParametersTab() {
  const formMethods = useForm<DailyParametersFormValues>({
    defaultValues: {
      dailyParamDateToShow: new Date(),
      searchQuery: ''
    }
  });

  const { selectedParams, onSelectParams, variableLabels, variables, selectedDailyParameters } =
    useGetParametersSummaryCustomizeView();

  return (
    <FormProvider {...formMethods}>
      <DailyParametersForm
        selectedParams={selectedParams}
        onSelectParams={onSelectParams}
        variableLabels={variableLabels}
        variables={variables}
        selectedDailyParameters={selectedDailyParameters}
      />

      {!selectedDailyParameters?.length && (
        <Flex gap='sm-alt' direction='column'>
          <Skeleton w='100%' h='40px' />
          <Skeleton w='100%' h='40px' />
          <Skeleton w='100%' h='40px' />
        </Flex>
      )}
      {!!selectedDailyParameters?.length && <ParametersSummaryTable dailyParameters={selectedDailyParameters} />}
    </FormProvider>
  );
}

interface DailyParametersFormProps {
  selectedParams?: string[];
  onSelectParams: (val: string[]) => void;
  variableLabels: Record<string, string>;
  variables: { label: string; value: string }[];
  selectedDailyParameters: FarmDailyParameters[];
}

function DailyParametersForm(props: DailyParametersFormProps) {
  const { selectedParams, onSelectParams, variableLabels, variables, selectedDailyParameters } = props;
  const {
    control,
    formState: { errors },
    setValue,
    watch,
    register
  } = useFormContext<DailyParametersFormValues>();

  const dailyParamDateToShowWatch = watch('dailyParamDateToShow');
  const search = watch('searchQuery');
  const todayDate = DateTime.local();
  const currentDate = DateTime.fromJSDate(dailyParamDateToShowWatch);
  const prevDateLuxon = currentDate.minus({ days: 1 });
  const nextDateLuxon = currentDate.plus({ days: 1 });
  const isNextDisabled = nextDateLuxon > todayDate;

  return (
    <Flex gap={{ base: 'xs-alt', lg: 'sm-alt' }} align='center' flexWrap='wrap'>
      <BaseSearchInput
        {...register('searchQuery')}
        rightElement={
          search && <CloseFilled cursor='pointer' hasBackground={false} onClick={() => setValue('searchQuery', '')} />
        }
        formControlProps={{
          w: { base: '100%', sm: '140px', md: '250px' }
        }}
      />
      <Flex align='center' gap={{ base: 'xs-alt', lg: 'sm-alt' }} justify='space-between' flex={1}>
        <Flex align='center' gap={{ base: 'xs-alt', lg: 'sm-alt' }} data-cy='daily-parameters-calender'>
          <Flex
            borderRadius='xl'
            h='40px'
            w='44px'
            bgColor='white'
            align='center'
            justify='center'
            px='2sm'
            onClick={() => {
              const newDate = prevDateLuxon.toJSDate();
              setValue('dailyParamDateToShow', newDate);
            }}
          >
            <ArrowBigLeftFilled boxSize='20px' />
          </Flex>

          <FormControlDateInput
            maxW='155px'
            w='152px'
            display='flex'
            alignItems='center'
            justifyContent='center'
            bgColor='white'
            h='40px'
            px='2sm'
            py='sm-alt'
            borderRadius='xl'
            pos='relative'
            control={control}
            placement='bottom'
            displayAsDropdown={true}
            hideDropdownIcon={true}
            dateFormat='MMMM dd'
            id='dailyParamDateToShow'
            name='dailyParamDateToShow'
            inputProps={{ textAlign: 'center' }}
            maxDate={todayDate.toJSDate()}
            error={errors?.dailyParamDateToShow?.message?.toString()}
          />

          <Flex
            cursor={isNextDisabled ? 'not-allowed' : 'pointer'}
            opacity={isNextDisabled ? 0.5 : 1}
            borderRadius='xl'
            h='40px'
            w='44px'
            bgColor='white'
            align='center'
            justify='center'
            px='2sm'
            onClick={() => {
              if (isNextDisabled) return;
              const newDate = nextDateLuxon.toJSDate();
              setValue('dailyParamDateToShow', newDate);
            }}
          >
            <ArrowBigRightFilled boxSize='20px' />
          </Flex>
        </Flex>

        <MoreOptionsMenu
          selectedParams={selectedParams}
          onSelectParams={onSelectParams}
          variableLabels={variableLabels}
          variables={variables}
          selectedDailyParameters={selectedDailyParameters}
          dailyParamDateToShow={DateTime.fromJSDate(dailyParamDateToShowWatch).toFormat('yyyy-MM-dd')}
        />
      </Flex>
    </Flex>
  );
}

interface MoreOptionsMenuProps extends DailyParametersFormProps {
  dailyParamDateToShow: string;
}

function MoreOptionsMenu(props: MoreOptionsMenuProps) {
  const { selectedParams, onSelectParams, variableLabels, variables, selectedDailyParameters, dailyParamDateToShow } =
    props;
  const { trans } = getTrans();
  const { trackAction } = useAnalytics();
  const customizeViewButtonRef = useRef<HTMLDivElement>(null);
  const exportButtonRef = useRef<HTMLDivElement>(null);

  return (
    <Box ms='auto'>
      <ParametersSummaryCustomizeViewModal
        selectedValues={selectedParams}
        onSubmit={onSelectParams}
        variableLabels={variableLabels}
        variables={variables}
      >
        <Box ref={customizeViewButtonRef} display='none'>
          {trans('t_customize_view')}
        </Box>
      </ParametersSummaryCustomizeViewModal>

      <ExportDailyParamDataModal
        dailyParamDateToShow={dailyParamDateToShow}
        selectedDailyParameters={selectedDailyParameters}
      >
        <Box ref={exportButtonRef} display='none'>
          {trans('t_export')}
        </Box>
      </ExportDailyParamDataModal>
      <MenuRoot>
        <MenuButton
          selectVariant='secondary'
          size='sm'
          px={0}
          _hover={{ opacity: '0.8' }}
          analyticsId={actionsName.customizeViewClicked}
          data-cy='more-options-menu-btn'
        >
          <MoreHoriz />
        </MenuButton>

        <MenuContent>
          <MenuItem
            onClick={() => {
              trackAction(actionsName.customizeViewClicked).then();
              customizeViewButtonRef.current?.click();
            }}
            data-cy='customize-view-btn'
            value='customize-view-btn'
          >
            {trans('t_customize_view')}
          </MenuItem>

          <MenuItem
            data-cy='export-btn'
            value='export-btn'
            w='100%'
            onClick={() => {
              exportButtonRef.current?.click();
            }}
          >
            {trans('t_export')}
          </MenuItem>
        </MenuContent>
      </MenuRoot>
    </Box>
  );
}
