import { useMemo } from 'react';
import { DateTime } from 'luxon';
import { getTrans } from '@i18n/get-trans';
import { convertUnitByDivision, convertUnitByMultiplication, formatNumber } from '@utils/number';
import { useAppSelector } from '@redux/hooks';
import { Box, Flex, FlexProps, Heading, Text } from '@chakra-ui/react';
import { getPondState } from '@screens/pond/helpers/get-pond-state';
import { getDaysDiffBetweenDates } from '@screens/farm/helpers/farm-dates';
import { getLastDateRecordValue } from '@screens/population/helpers/population-values';
import { CurrentFarmPonds } from '@redux/farm/set-current-farm-ponds';
import { getGrowthSeries } from '@screens/population/hooks/get-growth-series';
import {
  PondFilterPopoverFormType,
  useGenerateFilterTags
} from '@components/pond-filter-popover/use-generate-filter-tags';
import { OneValueRow } from '@components/summary-drawer/one-row-value';
import { AdminOnlyWrapper } from '@components/permission-wrappers/admin-only-wrapper';
import isString from 'lodash/isString';
import { AdminOrSupervisorWrapper } from '@components/permission-wrappers/admin-or-supervisor-wrapper';
import isUndefined from 'lodash/isUndefined';
import { VisionOnlyGuard } from '@components/permission-wrappers/vision-only-guard';

type CardValues = {
  totalCostsSum: number;
  feedKgByHaSum: number;
  avgFeedKgByHa: number;
  totalRevenueSum: number;
  totalProfitSum: number;
  profitPerHaPerDaySum: number;
  profitPerHaPerDay: number;
  profitPerPoundSum: number;
  avgProfitPerPound: number;
  revenuePerPoundSum: number;
  avgRevenuePerPound: number;
  costPoundHarvest: number;
  costPerPound: number;
  biomassLbsSum: number;
  totalBiomassLbsSum: number;
  totalProcessedBiomassLbsSum: number;
  avg2wkGrowth: number;
  minAvg2wkGrowth: number;
  maxAvg2wkGrowth: number;
  avgFcr: number;
  maxFcr: number;
  minFcr: number;
  avgAbw: number;
  maxAvgAbw: number;
  minAvgAbw: number;
  avgGrowth: number;
  maxGrowth: number;
  minGrowth: number;
  avgSurvivalRate: number;
  avgDaysOfCulture: number;
};

interface ProductionOverviewProps {
  filteredPonds: CurrentFarmPonds;
  farmOverviewFilter?: PondFilterPopoverFormType;
}

export function ProductionOverview(props: ProductionOverviewProps) {
  const { filteredPonds = [], farmOverviewFilter } = props;

  const { trans } = getTrans();
  const farmTimezone = useAppSelector((state) => state.farm.currentFarm?.timezone);
  const lang = useAppSelector((state) => state.app?.lang);
  const user = useAppSelector((state) => state.auth.user);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};

  const filterTags = useGenerateFilterTags({
    cycleDaysLessThan: farmOverviewFilter?.cycleDays?.lessThan,
    cycleDaysMoreThan: farmOverviewFilter?.cycleDays?.moreThan,
    abwLessThan: farmOverviewFilter?.abw?.lessThan,
    abwMoreThan: farmOverviewFilter?.abw?.moreThan,
    flagged: farmOverviewFilter?.flagged,
    aboveTarget: farmOverviewFilter?.aboveTarget,
    onTrack: farmOverviewFilter?.onTrack,
    offTrack: farmOverviewFilter?.offTrack,
    aboveCarryingCapacity: farmOverviewFilter?.aboveCarryingCapacity,
    superVisor: farmOverviewFilter?.superVisor
  });

  const cardValues: CardValues = useMemo(() => {
    const data = filteredPonds?.reduce(
      (acc, pond) => {
        const { currentPopulation, size: pondSize } = pond;

        const { isHarvested, isEmpty } = getPondState({ population: currentPopulation });
        if (isHarvested || isEmpty) return acc;

        const {
          stockedAt,
          survivalRate,
          lastWeekGrowth,
          stockedAtTimezone,
          lastMonitoringDate,
          seedingAverageWeight,
          lastMonitoringMlResult,
          lastMonitoringDateTimezone,
          history
        } = currentPopulation ?? {};

        const {
          cumulativeFcr,
          feedKgByHa,
          profitPerHaPerDay,
          totalProfit,
          revenuePerPound,
          profitPerPound,
          totalRevenue,
          totalCosts,
          biomassLbs,
          totalBiomassLbs,
          totalProcessedBiomassLbs,
          date
        } = history?.[0] ?? {};

        if (isString(date) && isString(stockedAt)) {
          const historyDateLuxon = DateTime.fromISO(date, { zone: farmTimezone });
          const stockedAtLuxon = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone ?? farmTimezone });

          const cycleDays = getDaysDiffBetweenDates({
            baseDate: historyDateLuxon.toFormat('yyyy-MM-dd'),
            dateToCompare: stockedAtLuxon.toFormat('yyyy-MM-dd')
          });

          acc.cycleDaysSum += cycleDays;
        }

        if (pondSize) {
          acc.pondSizeSum += pondSize;
        }

        if (totalCosts) {
          acc.totalCostsSum = acc.totalCostsSum + totalCosts;
        }

        if (feedKgByHa) {
          acc.feedKgByHaCount += 1;
          acc.feedKgByHaSum = acc.feedKgByHaSum + feedKgByHa;
        }

        if (totalRevenue) {
          acc.totalRevenueSum = acc.totalRevenueSum + totalRevenue;
        }

        if (totalProfit) {
          acc.totalProfitSum = acc.totalProfitSum + totalProfit;
        }

        if (profitPerHaPerDay) {
          acc.profitPerHaPerDaySum = acc.profitPerHaPerDaySum + profitPerHaPerDay;
        }

        if (revenuePerPound) {
          acc.revenuePerPoundCount += 1;
          acc.revenuePerPoundSum = acc.revenuePerPoundSum + revenuePerPound;
        }

        if (profitPerPound) {
          acc.profitPerPoundCount += 1;
          acc.profitPerPoundSum = acc.profitPerPoundSum + profitPerPound;
        }

        const { value: lastSurvivalRate } = getLastDateRecordValue(survivalRate);
        const averageWeight = lastMonitoringMlResult?.averageWeight ?? seedingAverageWeight;

        // live biomass
        if (biomassLbs) {
          acc.biomassLbsSum = acc.biomassLbsSum + biomassLbs;
        }

        // total biomass
        if (totalBiomassLbs) {
          acc.totalBiomassLbsSum = acc.totalBiomassLbsSum + totalBiomassLbs;
        }

        // total processed biomass
        if (totalProcessedBiomassLbs) {
          acc.totalProcessedBiomassLbsSum = acc.totalProcessedBiomassLbsSum + totalProcessedBiomassLbs;
        }

        const monitoredAtLuxon = DateTime.fromISO(lastMonitoringDate, {
          zone: lastMonitoringDateTimezone ?? farmTimezone
        });

        if (history?.length) {
          const { series: growthSeries } = getGrowthSeries(history ?? []) ?? {};
          const twoWeekAvgGrowthData = growthSeries?.find((growthSeriesItem) => {
            return growthSeriesItem?.date === monitoredAtLuxon.toFormat('yyyy-MM-dd');
          });
          const twoWeekAvgGrowth = twoWeekAvgGrowthData?.y ?? 0;
          acc.avg2wkGrowth = twoWeekAvgGrowth;
          acc.maxAvg2wkGrowth = acc.maxAvg2wkGrowth
            ? Math.max(acc.maxAvg2wkGrowth, twoWeekAvgGrowth)
            : twoWeekAvgGrowth;
          acc.minAvg2wkGrowth = acc.minAvg2wkGrowth
            ? Math.min(acc.minAvg2wkGrowth, twoWeekAvgGrowth)
            : twoWeekAvgGrowth;
        }

        if (lastWeekGrowth) {
          acc.pondsWithGrowthCount += 1;
          acc.growthSum = acc.growthSum + lastWeekGrowth;
          acc.maxGrowth = acc.maxGrowth ? Math.max(acc.maxGrowth, lastWeekGrowth) : lastWeekGrowth;
          acc.minGrowth = acc.minGrowth ? Math.min(acc.minGrowth, lastWeekGrowth) : lastWeekGrowth;
        }

        const stockedAtLuxon = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone ?? farmTimezone });
        const stockedDaysDiff = getDaysDiffBetweenDates({
          baseDate: monitoredAtLuxon.toFormat('yyyy-MM-dd'),
          dateToCompare: stockedAtLuxon.toFormat('yyyy-MM-dd')
        });

        if (stockedDaysDiff) {
          acc.pondsWithDaysOfCultureCount += 1;
          acc.daysOfCultureSum = acc.daysOfCultureSum + stockedDaysDiff;
        }

        if (averageWeight) {
          acc.pondsWithAbwCount += 1;
          acc.abwSum = acc.abwSum + averageWeight;
          acc.maxAvgAbw = acc.maxAvgAbw ? Math.max(acc.maxAvgAbw, averageWeight) : averageWeight;
          acc.minAvgAbw = acc.minAvgAbw ? Math.min(acc.minAvgAbw, averageWeight) : averageWeight;
        }

        if (lastSurvivalRate) {
          acc.pondsWithSurvivalRateCount += 1;
          acc.survivalRateSum = acc.survivalRateSum + lastSurvivalRate;
        }

        if (cumulativeFcr) {
          acc.pondsWithFcrCount += 1;
          acc.fcrSum = acc.fcrSum + cumulativeFcr;
          acc.maxFcr = acc.maxFcr ? Math.max(acc.maxFcr, cumulativeFcr) : cumulativeFcr;
          acc.minFcr = acc.minFcr ? Math.min(acc.minFcr, cumulativeFcr) : cumulativeFcr;
        }

        return acc;
      },
      {
        pondSizeSum: 0,
        cycleDaysSum: 0,
        totalCostsSum: 0,
        feedKgByHaSum: 0,
        feedKgByHaCount: 0,
        totalRevenueSum: 0,
        totalProfitSum: 0,
        profitPerHaPerDaySum: 0,
        revenuePerPoundSum: 0,
        revenuePerPoundCount: 0,
        profitPerPoundSum: 0,
        profitPerPoundCount: 0,
        costPerLbSum: 0,
        biomassLbsSum: 0,
        totalBiomassLbsSum: 0,
        totalProcessedBiomassLbsSum: 0,
        avg2wkGrowth: 0,
        minAvg2wkGrowth: undefined,
        maxAvg2wkGrowth: undefined,

        fcrSum: 0,
        maxFcr: undefined,
        minFcr: undefined,
        pondsWithFcrCount: 0,

        abwSum: 0,
        maxAvgAbw: undefined,
        minAvgAbw: undefined,
        pondsWithAbwCount: 0,

        growthSum: 0,
        maxGrowth: undefined,
        minGrowth: undefined,
        pondsWithGrowthCount: 0,

        daysOfCultureSum: 0,
        pondsWithDaysOfCultureCount: 0,

        survivalRateSum: 0,
        pondsWithSurvivalRateCount: 0
      }
    );

    return {
      ...data,
      costPoundHarvest: data?.totalCostsSum ? data.totalCostsSum / data.totalBiomassLbsSum : undefined,
      costPerPound: data?.totalCostsSum ? data.totalCostsSum / data.totalProcessedBiomassLbsSum : undefined,
      avgProfitPerPound: data.profitPerPoundSum ? data.profitPerPoundSum / data.profitPerPoundCount : undefined,
      avgRevenuePerPound: data.revenuePerPoundSum ? data.revenuePerPoundSum / data.revenuePerPoundCount : undefined,
      profitPerHaPerDay: data?.totalProfitSum ? data.totalProfitSum / data.cycleDaysSum / data.pondSizeSum : undefined,
      avgFeedKgByHa: data.feedKgByHaSum / data.feedKgByHaCount,
      avgFcr: data.fcrSum / data.pondsWithFcrCount,
      avgAbw: data.abwSum / data.pondsWithAbwCount,
      avgGrowth: data.growthSum / data.pondsWithGrowthCount,
      avgSurvivalRate: data.survivalRateSum / data.pondsWithSurvivalRateCount,
      avgDaysOfCulture: data.daysOfCultureSum / data.pondsWithDaysOfCultureCount
    };
  }, [filteredPonds, farmTimezone]);

  const {
    totalCostsSum,
    avgFeedKgByHa,
    totalRevenueSum,
    totalProfitSum,
    profitPerHaPerDay,
    avgRevenuePerPound,
    avgProfitPerPound,
    costPoundHarvest,
    costPerPound,
    biomassLbsSum,

    avg2wkGrowth,
    minAvg2wkGrowth,
    maxAvg2wkGrowth,

    avgFcr,
    maxFcr,
    minFcr,

    avgAbw,
    maxAvgAbw,
    minAvgAbw,

    avgGrowth,
    maxGrowth,
    minGrowth,

    avgSurvivalRate,
    avgDaysOfCulture
  } = cardValues;

  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);
  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');

  return (
    <Flex gap='md-alt' flexDirection='column'>
      {!!filterTags.length && (
        <Flex gap='sm-alt' align='center' flexWrap='wrap' data-cy='production-overview-tags'>
          <Text size='label300' me='xs-alt'>
            {trans('t_filtered_on')}:
          </Text>
          {filterTags.map((tag, index) => (
            <Flex align='center' minH='20px' borderRadius='4xl' bgColor='white' px='2sm' key={index}>
              {tag.label}
            </Flex>
          ))}
        </Flex>
      )}
      <VisionOnlyGuard>
        <AdminOnlyWrapper>
          <Flex
            align='center'
            p='md'
            gap='xl-alt'
            borderRadius='lg-alt'
            bgColor='bg.brandBlue.weakShade2'
            data-cy='total-sale'
          >
            <Text size='label100'>{trans('t_total_sale_value')}</Text>
            <Heading size='heavy200' color='text.brandBlue'>
              {totalRevenueSum ? formatNumber(totalRevenueSum, { lang, fractionDigits: 0, isCurrency: true }) : '-'}
            </Heading>
          </Flex>
        </AdminOnlyWrapper>

        <Box data-cy='financial-data'>
          <AdminOrSupervisorWrapper>
            <Text size='label200' mb='sm-alt'>
              {trans('t_financial')}
            </Text>
          </AdminOrSupervisorWrapper>
          <AdminOnlyWrapper>
            <OverviewRow
              title={isBiomassUnitLbs ? trans('t_revenue_per_pound') : trans('t_revenue_per_kg')}
              value={
                avgRevenuePerPound
                  ? formatNumber(convertUnitByMultiplication(avgRevenuePerPound, unitsConfig?.biomass), {
                      lang,
                      fractionDigits: 2,
                      isCurrency: true
                    })
                  : '-'
              }
              dataCy='revenuePerUnit'
            />
          </AdminOnlyWrapper>
          <AdminOrSupervisorWrapper>
            <OverviewRow
              title={trans('t_cost_per_unit_harvested', { unit: unitLabel })}
              value={
                costPoundHarvest
                  ? formatNumber(convertUnitByMultiplication(costPoundHarvest, unitsConfig?.biomass), {
                      lang,
                      fractionDigits: 2,
                      isCurrency: true
                    })
                  : '-'
              }
              dataCy='costPoundHarvest'
            />
            <OverviewRow
              title={trans('t_cost_per_unit_processed', { unit: unitLabel })}
              value={
                costPerPound
                  ? formatNumber(convertUnitByMultiplication(costPerPound, unitsConfig?.biomass), {
                      lang,
                      fractionDigits: 2,
                      isCurrency: true
                    })
                  : '-'
              }
              dataCy='processedCost'
            />
            <OverviewRow
              title={trans('t_cost_$')}
              value={totalCostsSum ? formatNumber(totalCostsSum, { lang, fractionDigits: 0, isCurrency: true }) : '-'}
              dataCy='totalCost'
            />
          </AdminOrSupervisorWrapper>
          <AdminOnlyWrapper>
            <OverviewRow
              title={isBiomassUnitLbs ? trans('t_profit_lb') : trans('t_profit_kg')}
              value={
                avgProfitPerPound
                  ? formatNumber(convertUnitByMultiplication(avgProfitPerPound, unitsConfig?.biomass), {
                      lang,
                      fractionDigits: 2,
                      isCurrency: true
                    })
                  : '-'
              }
              dataCy='profitPerUnit'
            />
            <OverviewRow
              title={trans('t_profit_include_dry_days_ha_day')}
              value={
                profitPerHaPerDay ? formatNumber(profitPerHaPerDay, { lang, fractionDigits: 2, isCurrency: true }) : '-'
              }
              dataCy='profitPerHaPerDay'
            />
            <OverviewRow
              title={trans('t_profit')}
              containerProps={{ borderBottom: 'none' }}
              value={totalProfitSum ? formatNumber(totalProfitSum, { lang, fractionDigits: 0, isCurrency: true }) : '-'}
              dataCy='totalProfit'
            />
          </AdminOnlyWrapper>
        </Box>
      </VisionOnlyGuard>
      <Box data-cy='production-data'>
        <Text size='label200' mb='sm-alt'>
          {trans('t_production')}
        </Text>
        <OverviewRow
          title={trans('t_avg_abw_g')}
          value={avgAbw ? formatNumber(avgAbw, { lang, fractionDigits: 2 }) : '-'}
          averageMin={formatNumber(minAvgAbw, { lang, fractionDigits: 2 })}
          averageMax={formatNumber(maxAvgAbw, { lang, fractionDigits: 2 })}
          dataCy='averageABW'
        />
        <OverviewRow
          title={trans('t_avg_2wk_growth')}
          value={avg2wkGrowth ? formatNumber(avg2wkGrowth, { lang, fractionDigits: 2 }) : '-'}
          suffix={trans('t_gram_g')}
          averageMin={formatNumber(minAvg2wkGrowth, { lang, fractionDigits: 2 })}
          averageMax={formatNumber(maxAvg2wkGrowth, { lang, fractionDigits: 2 })}
          dataCy='avgBiweeklyGrowth'
        />
        <OverviewRow
          title={trans('t_avg_weekly_growth')}
          value={avgGrowth ? formatNumber(avgGrowth, { lang, fractionDigits: 2 }) : '-'}
          suffix={trans('t_gram_g')}
          averageMin={formatNumber(minGrowth, { lang, fractionDigits: 2 })}
          averageMax={formatNumber(maxGrowth, { lang, fractionDigits: 2 })}
          dataCy='avgWeeklyGrowth'
        />
        <OverviewRow
          title={trans('t_avg_days_of_culture')}
          value={avgDaysOfCulture ? formatNumber(avgDaysOfCulture, { lang, fractionDigits: 0 }) : '-'}
          suffix={avgDaysOfCulture > 1 ? ` ${trans('t_days')}` : ` ${trans('t_day')}`}
          dataCy='avgDaysOfCulture'
        />
        <VisionOnlyGuard>
          <OverviewRow
            title={trans('t_avg_fcr')}
            value={avgFcr ? formatNumber(avgFcr, { lang, fractionDigits: 1 }) : '-'}
            averageMin={formatNumber(minFcr, { lang, fractionDigits: 1 })}
            averageMax={formatNumber(maxFcr, { lang, fractionDigits: 1 })}
            dataCy='avgFcr'
          />
          <OverviewRow
            title={trans('t_avg_survival')}
            value={
              avgSurvivalRate ? formatNumber(avgSurvivalRate, { lang, fractionDigits: 0, isPercentage: true }) : '-'
            }
            dataCy='avgSurvival'
          />
          <OverviewRow
            title={trans('t_kg_over_ha_over_day')}
            value={avgFeedKgByHa ? formatNumber(avgFeedKgByHa, { lang, fractionDigits: 0 }) : '-'}
            suffix={` ${trans('t_kg_over_ha_over_day')}`}
            dataCy='kgOverHaOverDay'
          />
          <OverviewRow
            containerProps={{ borderBottom: 'none' }}
            title={trans('t_biomass_in_pond_unit', { unit: unitLabel })}
            value={
              biomassLbsSum
                ? formatNumber(convertUnitByDivision(biomassLbsSum, unitsConfig?.biomass), { lang, fractionDigits: 0 })
                : '-'
            }
            suffix={` ${isBiomassUnitLbs ? trans('t_lb') : trans('t_kg')}`}
            dataCy='totalBiomass'
          />
        </VisionOnlyGuard>
      </Box>
    </Flex>
  );
}

type OverviewRowProps = {
  title: string;
  value: string | number;
  suffix?: string;
  containerProps?: FlexProps;
  averageMin?: string | number;
  averageMax?: string | number;
  dataCy: string;
};

function OverviewRow(props: OverviewRowProps) {
  const { title, value, averageMin, averageMax, suffix, containerProps, dataCy } = props;
  const trimmedValue = value?.toString()?.replace(/-/g, '');
  const suffixValue = trimmedValue && suffix ? suffix : '';

  return (
    <OneValueRow
      label={title}
      value={
        <>
          {`${value}${suffixValue}`}{' '}
          {!!averageMin && !!averageMax && (
            <Text as='span' fontWeight='normal'>
              [{averageMin}
              {suffixValue} - {averageMax}
              {suffixValue}]
            </Text>
          )}
        </>
      }
      data-cy={dataCy}
      {...containerProps}
    />
  );
}
