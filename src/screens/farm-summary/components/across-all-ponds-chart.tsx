'use client';
import { useMemo } from 'react';
import { DateTime } from 'luxon';
import { getTrans } from '@i18n/get-trans';
import { convertUnitByDivision, convertUnitByMultiplication, formatNumber } from '@utils/number';
import { useAppSelector } from '@redux/hooks';
import type { Options } from 'highcharts';
import { getPondState } from '@screens/pond/helpers/get-pond-state';
import { getDaysDiffBetweenDates } from '@screens/farm/helpers/farm-dates';
import { generateBiomassValues, getLastDateRecordValue } from '@screens/population/helpers/population-values';
import { CurrentFarmPonds } from '@redux/farm/set-current-farm-ponds';
import { renderToString } from 'react-dom/server';
import {
  FinancialOptions,
  ProductionOptions,
  SelectedOptionValue
} from '@screens/farm-summary/components/across-all-ponds-chart-card';
import { indicatorsColorMap, PondCardViewType } from '@components/pond-card/helper';
import { EnumPopulationHarvestPlanIndicatorsAbwCurrentColor } from '@xpertsea/module-farm-sdk';
import isUndefined from 'lodash/isUndefined';
import { HighchartsNextComp } from '@components/base/highcharts-next-comp';
import { useToken } from '@chakra-ui/react';

type AcrossAllPondsChartProps = {
  acrossAllPondsOption: SelectedOptionValue;
  currentFarmPonds: CurrentFarmPonds;
  isExpanded?: boolean;
  profitProjectionToView: PondCardViewType;
  height?: number;
};

type AllOptions = ProductionOptions | FinancialOptions;

export function AcrossAllPondsChart(props: AcrossAllPondsChartProps) {
  const { acrossAllPondsOption, currentFarmPonds, isExpanded, profitProjectionToView, height } = props;

  const { trans } = getTrans();
  const [gray400, gray800, brandBlue100] = useToken('colors', ['gray.400', 'gray.800', 'brandBlue.100']);
  const [fontFamily] = useToken('fonts', ['body']);
  const { lang } = useAppSelector((state) => state.app);
  const user = useAppSelector((state) => state.auth.user);
  const { timezone: farmTimezone } = useAppSelector((state) => state.farm.currentFarm);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};

  const keysNotToIncludeBefore21Days: AllOptions[] = ['profitPerPound', 'profitPerHaPerDay', 'totalProfit'];
  const { value: variableKey, label: variableLabel } = acrossAllPondsOption ?? {};
  const defaultGrayColor = gray800;

  const chartData = useMemo(() => {
    const chartData = [];
    for (const pond of currentFarmPonds) {
      const { currentPopulation, size: pondSize, name: pondName } = pond;

      const { isHarvested, isEmpty } = getPondState({ population: currentPopulation });
      if (isHarvested || isEmpty) continue;

      const {
        history,
        stockedAt,
        survivalRate,
        seedingQuantity,
        stockedAtTimezone,
        lastMonitoringDate,
        seedingAverageWeight,
        lastMonitoringMlResult,
        lastMonitoringDateTimezone,
        harvestPlan
      } = currentPopulation ?? {};

      const { indicators } = harvestPlan ?? {};

      const indicatorsMap: Partial<Record<AllOptions, EnumPopulationHarvestPlanIndicatorsAbwCurrentColor>> = {
        averageWeight: indicators?.abw?.[profitProjectionToView]?.color,
        biomassPerHa: indicators?.biomassLbsByHa?.[profitProjectionToView]?.color,
        biomass: indicators?.biomassLbsByHa?.[profitProjectionToView]?.color,
        cumulativeFcr: indicators?.fcr?.[profitProjectionToView]?.color,
        survival: indicators?.survivalRate?.[profitProjectionToView]?.color,
        growth: indicators?.growth?.[profitProjectionToView]?.color,
        costPerPound: indicators?.costPerPound?.[profitProjectionToView]?.color,
        profitPerHaPerDay: indicators?.profitPerHaPerDay?.[profitProjectionToView]?.color
      };

      const {
        cumulativeFcr,
        feedKgByHa,
        costPerPound,
        totalCosts,
        revenuePerPound,
        totalRevenue,
        profitPerPound,
        totalProfit,
        profitPerHaPerDay,
        growthTwoWeeks
      } = history?.[0] ?? {};

      const stockedAtLuxon = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone ?? farmTimezone });
      const monitoredAtLuxon = DateTime.fromISO(lastMonitoringDate, {
        zone: lastMonitoringDateTimezone ?? farmTimezone
      });

      const cycleDays = getDaysDiffBetweenDates({
        baseDate: monitoredAtLuxon.toFormat('yyyy-MM-dd'),
        dateToCompare: stockedAtLuxon.toFormat('yyyy-MM-dd')
      });

      const isBefore21DaysVariable = cycleDays < 21 && keysNotToIncludeBefore21Days.includes(variableKey);

      if (isBefore21DaysVariable) continue;

      const lastMonitoringABW = lastMonitoringMlResult?.averageWeight;
      const averageWeight = lastMonitoringABW ?? seedingAverageWeight;

      const { value: lastSurvivalRate } = getLastDateRecordValue(survivalRate);

      const { biomassLbs, biomassLbsHa } = generateBiomassValues({
        pondSize,
        averageWeight,
        seedingQuantity,
        survivalRate: lastSurvivalRate
      });

      const yValueMap: Record<AllOptions, number> = {
        averageWeight: averageWeight,
        biomass: convertUnitByDivision(biomassLbs, unitsConfig?.biomass),
        biomassPerHa: convertUnitByDivision(biomassLbsHa, unitsConfig?.biomass),
        growth: growthTwoWeeks,
        cumulativeFcr,
        feedKgByHa,
        survival: lastSurvivalRate,
        dispersion: lastMonitoringMlResult?.weightCv,
        costPerPound: convertUnitByMultiplication(costPerPound, unitsConfig?.biomass),
        totalCosts,
        revenuePerPound: convertUnitByMultiplication(revenuePerPound, unitsConfig?.biomass),
        totalRevenue,
        profitPerPound: convertUnitByMultiplication(profitPerPound, unitsConfig?.biomass),
        totalProfit,
        profitPerHaPerDay
      };

      const y = yValueMap[variableKey];

      const hasValue = y || y === 0;

      if (!hasValue) continue;

      chartData.push({
        y,
        x: cycleDays,
        marker: {
          fillColor: indicatorsColorMap[indicatorsMap[variableKey]] ?? defaultGrayColor
        },
        custom: {
          averageWeight,
          biomass: biomassLbs,
          biomassPerHa: biomassLbsHa,
          growth: growthTwoWeeks,
          cumulativeFcr,
          feedKgByHa,
          survival: lastSurvivalRate,
          dispersion: lastMonitoringMlResult?.weightCv,
          costPerPound,
          totalCosts,
          revenuePerPound,
          totalRevenue,
          profitPerPound,
          totalProfit,
          profitPerHaPerDay,
          pondName
        }
      });
    }
    return chartData;
  }, [currentFarmPonds, farmTimezone, variableKey, isExpanded, lang]);

  const defaultTextStyle = {
    fontSize: isExpanded ? '12px' : '10px',
    fontWeight: '600',
    color: defaultGrayColor
  };

  const expandedHeight = isExpanded ? 440 : 210;

  const options: Options = {
    chart: {
      type: 'bubble',
      animation: false,
      spacing: [6, 0, 0, 0],
      backgroundColor: brandBlue100,
      style: { fontFamily, height: height ?? expandedHeight }
    },
    title: { text: undefined },
    plotOptions: {
      series: {
        animation: false,
        marker: {
          lineWidth: 0,
          fillOpacity: 1,
          states: {
            hover: {
              lineWidth: 0
            }
          }
        }
      },
      bubble: {
        maxSize: 12
      }
    },
    xAxis: {
      min: 0,
      tickInterval: 7,
      tickWidth: 0,
      lineWidth: isExpanded ? 1 : 0,
      lineColor: gray400,
      gridLineWidth: 0,
      labels: { enabled: true, style: defaultTextStyle },
      title: {
        text: trans('t_days_of_culture'),
        margin: 10,
        style: defaultTextStyle
      }
    },
    yAxis: {
      tickWidth: 0,
      lineWidth: isExpanded ? 1 : 0,
      lineColor: gray400,
      gridLineWidth: 0.5,
      gridLineColor: gray400,
      gridLineDashStyle: 'LongDash',
      labels: {
        style: defaultTextStyle,
        formatter: (props) => {
          const variableKeyFormatSpecMap: Record<
            typeof variableKey,
            { fractionDigits: number; isCurrency?: boolean; multiplyBy?: number; shouldAddZeros?: boolean }
          > = {
            profitPerHaPerDay: { fractionDigits: 2, isCurrency: true, shouldAddZeros: false },
            profitPerPound: { fractionDigits: 2, isCurrency: true, shouldAddZeros: false },
            costPerPound: { fractionDigits: 2, isCurrency: true, shouldAddZeros: false },
            revenuePerPound: { fractionDigits: 2, isCurrency: true, shouldAddZeros: false },
            totalCosts: { fractionDigits: 0, isCurrency: true, shouldAddZeros: false },
            totalRevenue: { fractionDigits: 0, isCurrency: true, shouldAddZeros: false },
            totalProfit: { fractionDigits: 0, isCurrency: true, shouldAddZeros: false },
            biomass: { fractionDigits: 0, shouldAddZeros: false },
            biomassPerHa: { fractionDigits: 0, shouldAddZeros: false },
            growth: { fractionDigits: 2, shouldAddZeros: false },
            cumulativeFcr: { fractionDigits: 2, shouldAddZeros: false },
            feedKgByHa: { fractionDigits: 2, shouldAddZeros: false },
            survival: { fractionDigits: 1, multiplyBy: 100, shouldAddZeros: false },
            dispersion: { fractionDigits: 1, multiplyBy: 100, shouldAddZeros: false },
            averageWeight: { fractionDigits: 2, shouldAddZeros: false }
          };
          const variableSpecs = variableKeyFormatSpecMap[variableKey];
          const value = variableSpecs?.multiplyBy
            ? (props.value as number) * variableSpecs.multiplyBy
            : (props.value as number);
          if (!variableSpecs) return '-';
          return formatNumber(value, {
            ...variableSpecs,
            lang
          }) as string;
        }
      },
      title: {
        text: variableLabel,
        margin: 35,
        style: defaultTextStyle
      }
    },
    series: [{ type: 'bubble', data: chartData }],
    tooltip: {
      padding: 0,
      useHTML: true,
      shadow: false,
      shape: 'rect',
      backgroundColor: 'transparent',
      formatter: function (this): string {
        type Point = (typeof chartData)[0];
        const {
          cumulativeFcr,
          growth,
          biomass,
          pondName,
          survival,
          dispersion,
          biomassPerHa,
          feedKgByHa,
          averageWeight,
          costPerPound,
          totalCosts,
          revenuePerPound,
          totalRevenue,
          profitPerPound,
          totalProfit,
          profitPerHaPerDay
        } = (this as unknown as Point).custom;

        const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);
        const unitTitle = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');
        const unitsTitle = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');
        const unitOverHaTitle = isBiomassUnitLbs ? trans('t_lb_over_ha') : trans('t_kg_over_ha');
        return renderToString(
          <div
            style={{
              padding: '10px 16px',
              backgroundColor: 'white',
              borderRadius: '8px',
              minWidth: '131px',
              minHeight: '54px',
              fontSize: '12px',
              fontWeight: 600,
              color: defaultGrayColor,
              lineHeight: '12px'
            }}
          >
            <p style={{ marginBottom: '10px' }}>{pondName}</p>
            <div style={{ display: 'flex', gap: '4px' }}>
              <p>{variableLabel}:</p>
              <p>
                {variableKey === 'averageWeight' ? formatNumber(averageWeight, { fractionDigits: 2, lang }) : ''}
                {variableKey === 'biomass'
                  ? `${formatNumber(convertUnitByDivision(biomass, unitsConfig?.biomass), { fractionDigits: 0, lang })} ${unitsTitle}`
                  : ''}
                {variableKey === 'biomassPerHa'
                  ? `${formatNumber(convertUnitByDivision(biomassPerHa, unitsConfig?.biomass), { fractionDigits: 0, lang })} ${unitOverHaTitle}`
                  : ''}
                {variableKey === 'growth'
                  ? `${formatNumber(growth, { fractionDigits: 2, lang })} ${trans('t_gram_g')}`
                  : ''}
                {variableKey === 'cumulativeFcr' ? formatNumber(cumulativeFcr, { fractionDigits: 2, lang }) : ''}
                {variableKey === 'feedKgByHa' ? formatNumber(feedKgByHa, { lang, fractionDigits: 2 }) : ''}
                {variableKey === 'survival'
                  ? formatNumber(survival, { lang, fractionDigits: 0, isPercentage: true })
                  : ''}
                {variableKey === 'dispersion'
                  ? `${formatNumber(dispersion, { lang, fractionDigits: 1, isPercentage: true })} ${trans('t_cv')}`
                  : ''}
                {variableKey === 'costPerPound'
                  ? `${formatNumber(convertUnitByMultiplication(costPerPound, unitsConfig?.biomass), { fractionDigits: 2, lang, isCurrency: true })} /${unitTitle}`
                  : ''}
                {variableKey === 'totalCosts'
                  ? `${formatNumber(totalCosts, { fractionDigits: 0, lang, isCurrency: true })}`
                  : ''}
                {variableKey === 'revenuePerPound'
                  ? `${formatNumber(convertUnitByMultiplication(revenuePerPound, unitsConfig?.biomass), { fractionDigits: 2, lang, isCurrency: true })} /${unitTitle}`
                  : ''}
                {variableKey === 'totalRevenue'
                  ? `${formatNumber(totalRevenue, { fractionDigits: 0, lang, isCurrency: true })}`
                  : ''}
                {variableKey === 'profitPerPound'
                  ? `${formatNumber(convertUnitByMultiplication(profitPerPound, unitsConfig?.biomass), { fractionDigits: 2, lang, isCurrency: true })} /${unitTitle}`
                  : ''}
                {variableKey === 'totalProfit'
                  ? `${formatNumber(totalProfit, { fractionDigits: 0, lang, isCurrency: true })}`
                  : ''}
                {variableKey === 'profitPerHaPerDay'
                  ? `${formatNumber(profitPerHaPerDay, { fractionDigits: 2, lang, isCurrency: true })} ${trans('t_over_ha_over_day')}`
                  : ''}
              </p>
            </div>
          </div>
        );
      }
    }
  };

  return (
    <HighchartsNextComp
      options={options}
      containerProps={{
        style: { width: '100%', height: height ? `${height}px` : `${expandedHeight}px` }
      }}
    />
  );
}
