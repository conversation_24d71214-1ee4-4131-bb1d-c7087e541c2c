import { getTrans } from '@i18n/get-trans';
import { MoreHoriz } from '@icons/more-horiz/more-horiz';
import { SwapVertical } from '@icons/swap/swap-vertical';

import { Box, BoxProps, Flex, FlexProps, IconButton, Separator, Table, Text } from '@chakra-ui/react';
import React, { ReactNode, RefObject, useMemo, useRef, useState } from 'react';
import { useAppSelector } from '@redux/hooks';
import { DateTime } from 'luxon';
import { formatNumber, isNumber } from '@utils/number';
import { BaseFormCheckbox } from '@components/form/base-form-checkbox';
import { sortCompareString } from '@utils/sort-array';
import { Controller, useFormContext } from 'react-hook-form';
import 'react-datepicker/dist/react-datepicker.css';
import { BaseDateRangeSelector } from '@components/base/base-date-range-selector';
import { HarvestsInfoType } from '@screens/pond/components/pond-harvests-tab/pond-harvests-helpers';
import sumBy from 'lodash/sumBy';
import map from 'lodash/map';
import uniq from 'lodash/uniq';
import { usePondPartialHarvests } from '@screens/pond/components/pond-harvests-tab/use-pond-partial-harvests';
import { useReloadCurrentFarmPonds } from '@screens/farm/hooks/use-reload-current-farm-ponds';
import { SectionLoader } from '@components/loaders/section-loader';
import {
  DeleteHarvestModal,
  RecordHarvestModal
} from '@screens/pond/components/pond-harvests-tab/harvests-table-components';
import { HarvestSummaryTableCustomizeViewModal } from '@screens/farm-summary/components/harvest-summary-table-customize-view-modal';
import { useGetHarvestSummaryCustomizeViewVariables } from '@screens/farm-summary/hooks/use-get-harvest-summary-customize-view-variables';
import {
  HarvestSummaryViewVariable,
  useGetHarvestSummaryVariableLabels
} from '@screens/farm-summary/helpers/harvest-summary';
import { SettingsAdjustFilled } from '@icons/settings-adjust/settings-adjust-filled';
import { MenuButton, MenuContent, MenuItem, MenuRoot } from '@components/ui/menu';
import { useAnalytics } from '@hooks/use-analytics';
import { actionsName } from '@utils/segment';
import { BaseLink } from '@components/base/base-link';
import { slugify } from '@utils/string';
import { useRouter } from 'next/router';
import { gramsInKg, sqPerHectare } from '@utils/constants';
import { EditFinalHarvestPlanModal } from '@screens/population/components/harvest-modals/edit-final-harvest-plan-modal';
import { AddEditPartialHarvestPlanModal } from '@screens/population/components/harvest-modals/add-edit-partial-harvest-plan-modal';
import { HarvestTableFilterFormValues } from '@screens/farm-summary/components/harvest-tab';
import { BaseSearchInput } from '@components/base/base-search-input';
import { CloseFilled } from '@icons/close/close-filled';

const lastColumnWidth = 69;
const firstColumnWidth = 125;

type SortConfig = {
  direction: 'asc' | 'desc';
  field: HarvestSummaryViewVariable | 'pondName' | 'cycle' | 'harvestDate';
};

type HarvestSummaryTableProps = {
  harvestSummaryData: HarvestsInfoType[];
};

export function HarvestSummaryTable(props: HarvestSummaryTableProps) {
  const { harvestSummaryData } = props;

  const { trans } = getTrans();
  const lang = useAppSelector((state) => state.app.lang);

  const { trackAction } = useAnalytics();
  const currentFarmPonds = useAppSelector((state) => state.farm.currentFarmPonds);
  const isLoadingCurrentFarmPonds = useAppSelector((state) => state.farm.isLoadingCurrentFarmPonds);

  const containerRef = useRef<HTMLDivElement>(null);

  const { watch, register, control, setValue } = useFormContext<HarvestTableFilterFormValues>();

  const formValues = watch();

  const isCustomRangeSelected = !!(
    formValues.dateFilters.customRange?.startDate && formValues.dateFilters.customRange?.endDate
  );

  const isNext30dOnlySelected = formValues.dateFilters.next30d && !formValues.dateFilters.past30d;
  const isPast30dOnlySelected = formValues.dateFilters.past30d && !formValues.dateFilters.next30d;

  let customRangeInputLabel = trans('t_custom');
  if (isCustomRangeSelected) {
    const endDateLuxon = DateTime.fromJSDate(formValues.dateFilters.customRange.endDate);
    const statDateLuxon = DateTime.fromJSDate(formValues.dateFilters.customRange.startDate);

    if (statDateLuxon.month === endDateLuxon.month) {
      customRangeInputLabel = `${statDateLuxon.toFormat('MMM dd', { locale: lang })}-${endDateLuxon.toFormat('dd')}`;
    } else {
      customRangeInputLabel = `${statDateLuxon.toFormat('MMM dd', { locale: lang })} - ${endDateLuxon.toFormat('MMM dd')}`;
    }
  }

  const [sortConfig, setSortConfig] = useState<SortConfig>({
    direction: 'asc',
    field: 'harvestDate'
  });

  const harvestSummaryViewVariables = useGetHarvestSummaryCustomizeViewVariables();
  const harvestSummaryVariableLabels = useGetHarvestSummaryVariableLabels();

  const handleSort = (field: SortConfig['field']) => {
    setSortConfig((prevConfig) => {
      const sortOption: SortConfig = {
        field,
        direction: prevConfig.field === field && prevConfig.direction === 'asc' ? 'desc' : 'asc'
      };

      trackAction(actionsName.harvestSummaryListSorted, {
        key: sortOption.field,
        order: sortOption.direction
      });
      return sortOption;
    });
  };

  const harvestSummaryDataSorted = useMemo(() => {
    return harvestSummaryData.sort((a, b) => {
      if (sortConfig.field === 'harvestType') {
        const aValue = a?.priceList?.defaultHarvestType;
        const bValue = b?.priceList?.defaultHarvestType;
        if (aValue === bValue) return 0;

        if (aValue == null) return 1;
        if (bValue == null) return -1;

        const result = aValue < bValue ? -1 : 1;
        return sortConfig.direction === 'desc' ? result : -result;
      }

      const aValue = a[sortConfig.field];
      const bValue = b[sortConfig.field];

      if (aValue === bValue) return 0;

      if (aValue == null) return 1;
      if (bValue == null) return -1;

      if (sortConfig.field === 'harvestDate') {
        const aDate = DateTime.fromISO(aValue as string);
        const bDate = DateTime.fromISO(bValue as string);
        return sortConfig.direction === 'desc'
          ? aDate.toMillis() - bDate.toMillis()
          : bDate.toMillis() - aDate.toMillis();
      }

      if (sortConfig.field === 'selectedProcessorId') {
        const aProcessor = a.processorOptions?.find((item) => item.value === aValue);
        const bProcessor = b.processorOptions?.find((item) => item.value === bValue);
        return sortConfig.direction === 'desc'
          ? sortCompareString(aProcessor?.label, bProcessor?.label)
          : sortCompareString(bProcessor?.label, aProcessor?.label);
      }

      if (sortConfig.field === 'pondName') {
        return sortConfig.direction === 'desc'
          ? sortCompareString(aValue as string, bValue as string)
          : sortCompareString(bValue as string, aValue as string);
      }

      const result = aValue < bValue ? -1 : 1;
      return sortConfig.direction === 'desc' ? result : -result;
    });
  }, [harvestSummaryData, formValues, sortConfig]);

  const {
    totalPonds,
    plannedCount,
    recordedCount,
    finalCount,
    partialCount,
    animalsHarvested,
    animalsHarvestedHa,
    animalsHarvestedM2,
    averageWeight,
    biomassByHa,
    days,
    revenuePerPoundHarvested,
    revenuePerPoundProcessed,
    sumBiomass,
    sumRevenue
  } = useMemo(() => {
    const pondIds = uniq(map(harvestSummaryDataSorted, 'pondId'));

    const ponds = currentFarmPonds?.filter((pond) => pondIds.includes(pond._id)) ?? [];

    const sumPondSize = sumBy(ponds, 'size');
    const sumRevenue = sumBy(harvestSummaryDataSorted, 'revenue');
    const sumBiomass = sumBy(harvestSummaryDataSorted, 'biomass');
    const sumCycleDays = sumBy(harvestSummaryDataSorted, 'cycleDays');
    const animalsHarvested = sumBy(harvestSummaryDataSorted, 'animalsHarvested');
    const sumBiomassProcessed = sumBy(harvestSummaryDataSorted, 'biomassProcessed');

    return {
      totalPonds: pondIds.length,
      days: sumCycleDays / harvestSummaryDataSorted.length,
      averageWeight: (sumBiomass / 2.2 / animalsHarvested) * gramsInKg,
      sumRevenue,
      sumBiomass,
      biomassByHa: sumBiomass / sumPondSize,
      revenuePerPoundHarvested: sumRevenue / sumBiomass,
      revenuePerPoundProcessed: sumRevenue / sumBiomassProcessed,
      animalsHarvested,
      animalsHarvestedHa: animalsHarvested / sumPondSize,
      animalsHarvestedM2: animalsHarvested / sumPondSize / sqPerHectare,
      finalCount: harvestSummaryDataSorted.filter((row) => row.type === 'final').length,
      partialCount: harvestSummaryDataSorted.filter((row) => row.type === 'partial').length,
      plannedCount: harvestSummaryDataSorted.filter((row) => row.state === 'planned').length,
      recordedCount: harvestSummaryDataSorted.filter((row) => row.state === 'recorded').length
    };
  }, [currentFarmPonds, harvestSummaryDataSorted]);

  function SortIndicator({ field }: { field: SortConfig['field'] }) {
    return (
      <SwapVertical
        cursor='pointer'
        userSelect='none'
        _hover={{ opacity: 0.8 }}
        onClick={() => handleSort(field)}
        upArrowColor={sortConfig.field === field && sortConfig.direction === 'asc' ? 'text.gray' : 'gray.400'}
        downArrowColor={sortConfig.field === field && sortConfig.direction === 'desc' ? 'text.gray' : 'gray.400'}
      />
    );
  }

  const footerValueMap: Record<HarvestSummaryViewVariable, ReactNode> = {
    type: (
      <Text size='label200' color='gray.700' lineHeight='24px'>
        {trans('t_count_final_count_partial', {
          finalCount: formatNumber(finalCount, { lang, fractionDigits: 0 }),
          partialCount: formatNumber(partialCount, { lang, fractionDigits: 0 })
        })}
      </Text>
    ),
    state: (
      <Text size='label200' color='gray.700' lineHeight='24px'>
        {trans('t_count_plan_count_recorded', {
          planCount: formatNumber(plannedCount, { lang, fractionDigits: 0 }),
          recordedCount: formatNumber(recordedCount, { lang, fractionDigits: 0 })
        })}
      </Text>
    ),
    cycleDays: (
      <Text size='label200' color='gray.700' lineHeight='24px'>
        {isNumber(days) ? (
          <>
            {days < 2 && trans('t_count_day', { count: formatNumber(days, { lang, fractionDigits: 0 }) })}
            {days >= 2 && trans('t_count_days', { count: formatNumber(days, { lang, fractionDigits: 0 }) })}
          </>
        ) : (
          '-'
        )}
      </Text>
    ),
    harvestType: (
      <Text size='label200' color='gray.700' lineHeight='24px'>
        -
      </Text>
    ),
    averageWeight: (
      <Text size='label200' color='gray.700' lineHeight='24px'>
        {isNumber(averageWeight) ? formatNumber(averageWeight, { lang, fractionDigits: 2 }) : '-'}
      </Text>
    ),
    revenue: (
      <Text size='label200' color='gray.700' lineHeight='24px'>
        {isNumber(sumRevenue) ? formatNumber(sumRevenue, { lang, isCurrency: true, fractionDigits: 0 }) : '-'}
      </Text>
    ),
    revenuePerPound: (
      <Text size='label200' color='gray.700' lineHeight='24px'>
        {isNumber(revenuePerPoundProcessed)
          ? formatNumber(revenuePerPoundProcessed, { lang, isCurrency: true, fractionDigits: 2 })
          : '-'}
      </Text>
    ),
    revenuePoundHarvest: (
      <Text size='label200' color='gray.700' lineHeight='24px'>
        {isNumber(revenuePerPoundHarvested)
          ? formatNumber(revenuePerPoundHarvested, { lang, isCurrency: true, fractionDigits: 2 })
          : '-'}
      </Text>
    ),
    biomass: (
      <Text size='label200' color='gray.700' lineHeight='24px'>
        {isNumber(sumBiomass) ? formatNumber(sumBiomass, { lang, fractionDigits: 0 }) : '-'}
      </Text>
    ),
    biomassPerHa: (
      <Text size='label200' color='gray.700' lineHeight='24px'>
        {isNumber(biomassByHa) ? formatNumber(biomassByHa, { lang, fractionDigits: 0 }) : '-'}
      </Text>
    ),
    animalsHarvested: (
      <Text size='label200' color='gray.700' lineHeight='24px'>
        {isNumber(animalsHarvested) ? formatNumber(animalsHarvested, { lang, fractionDigits: 0 }) : '-'}
      </Text>
    ),
    animalsHarvestedHa: (
      <Text size='label200' color='gray.700' lineHeight='24px'>
        {isNumber(animalsHarvestedHa) ? formatNumber(animalsHarvestedHa, { lang, fractionDigits: 0 }) : '-'}
      </Text>
    ),
    animalsHarvestedM2: (
      <Text size='label200' color='gray.700' lineHeight='24px'>
        {isNumber(animalsHarvestedM2) ? formatNumber(animalsHarvestedM2, { lang, fractionDigits: 2 }) : '-'}
      </Text>
    ),
    selectedProcessorId: (
      <Text size='label200' color='gray.700' lineHeight='24px'>
        -
      </Text>
    ),
    survival: (
      <Text size='label200' color='gray.700' lineHeight='24px'>
        -
      </Text>
    )
  };

  return (
    <>
      <Flex
        flexDir={{ base: 'column', xl: 'row' }}
        align={{ base: 'flex-start', xl: 'center' }}
        justify='space-between'
        gap={{ base: 'md-alt', xl: 'sm-alt' }}
        flexWrap='wrap'
      >
        <Flex align='center' justify='space-between' w={{ base: '100%', xl: 'auto' }} flex={1} gap='sm-alt'>
          <BaseSearchInput
            {...register('search')}
            rightElement={
              formValues?.search && (
                <CloseFilled cursor='pointer' hasBackground={false} onClick={() => setValue('search', '')} />
              )
            }
            formControlProps={{ w: { base: '100%', lg: '300px' } }}
          />
          <CustomizeViewButton display={{ base: 'block', xl: 'none' }} />
        </Flex>
        <Flex
          flexDir={{ base: 'column', xl: 'row' }}
          align={{ base: 'flex-start', xl: 'center' }}
          gap='sm-alt'
          w={{ base: '100%', xl: 'auto' }}
          justify={{ base: 'flex-start', xl: 'space-between' }}
        >
          <Flex
            w={{ base: '100%', xl: 'auto' }}
            flexDir={{ base: 'column', xl: 'row' }}
            h={{ base: 'auto', xl: '40px' }}
            bgColor='white'
            rounded={{ base: 'xl', xl: 'full' }}
            align={{ base: 'flex-start', xl: 'center' }}
            px='md'
            py={{ base: 'md', xl: 0 }}
            gap='md'
            overflowX='scroll'
          >
            <Flex align='center' gap='sm'>
              <Text size='label200' color='gray.500' minW={{ base: '54px', xl: 'auto' }}>
                {trans('t_type')}:
              </Text>
              <Controller
                name='typeFilters.final'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      id='typeFilters.final'
                      label={trans('t_final')}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />

              <Controller
                name='typeFilters.partial'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      id='typeFilters.partial'
                      label={trans('t_partial')}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />
            </Flex>
            <Separator
              display={{ base: 'none', xl: 'block' }}
              orientation='vertical'
              borderLeftWidth='0.5px'
              borderColor='gray.400'
              h='20px'
            />
            <Flex align='center' gap='sm'>
              <Text size='label200' color='gray.500' minW={{ base: '54px', xl: 'auto' }}>
                {trans('t_status')}:
              </Text>
              <Controller
                name='stateFilters.notRecorded'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      id='stateFilters.notRecorded'
                      label={trans('t_planned')}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />

              <Controller
                name='stateFilters.recorded'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      id='stateFilters.recorded'
                      label={trans('t_recorded')}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />
            </Flex>
            <Separator
              display={{ base: 'none', xl: 'block' }}
              orientation='vertical'
              borderLeftWidth='0.5px'
              borderColor='gray.400'
              h='20px'
            />
            <Flex align='center' gap='sm'>
              <Text size='label200' color='gray.500' minW={{ base: '54px', xl: 'auto' }}>
                {trans('t_date')}:
              </Text>
              <Controller
                name='dateFilters.next30d'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      id='dateFilters.next30d'
                      label={trans('t_next_30_d')}
                      disabled={isCustomRangeSelected || isNext30dOnlySelected}
                      checked={value}
                      onCheckedChange={({ checked }) => {
                        if (isCustomRangeSelected || isNext30dOnlySelected) return;
                        onChange(checked);
                        if (checked) {
                          setValue('dateFilters.customRange.startDate', undefined);
                          setValue('dateFilters.customRange.endDate', undefined);
                        }
                      }}
                    />
                  );
                }}
              />

              <Controller
                name='dateFilters.past30d'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      id='dateFilters.past30d'
                      label={trans('t_past_30_d')}
                      disabled={isCustomRangeSelected || isPast30dOnlySelected}
                      checked={value}
                      onCheckedChange={({ checked }) => {
                        if (isCustomRangeSelected || isPast30dOnlySelected) return;
                        onChange(checked);
                        if (checked) {
                          setValue('dateFilters.customRange.startDate', undefined);
                          setValue('dateFilters.customRange.endDate', undefined);
                        }
                      }}
                    />
                  );
                }}
              />

              <BaseDateRangeSelector
                endDate={formValues.dateFilters.customRange?.endDate}
                startDate={formValues.dateFilters.customRange?.startDate}
                onChange={([start, end]) => {
                  setValue('dateFilters.customRange.startDate', start);
                  setValue('dateFilters.customRange.endDate', end);
                  setValue('dateFilters.next30d', false);
                  setValue('dateFilters.past30d', false);
                }}
                customInput={
                  <BaseFormCheckbox
                    id='customRange'
                    label={customRangeInputLabel}
                    checked={isCustomRangeSelected}
                    onCheckedChange={(details) => {
                      // trigger set values only if the checkbox is unchecked
                      if (details.checked) return;

                      setValue('dateFilters.customRange.endDate', undefined);
                      setValue('dateFilters.customRange.startDate', undefined);
                      setValue('dateFilters.next30d', true);
                      setValue('dateFilters.past30d', true);
                    }}
                  />
                }
              />
            </Flex>
          </Flex>
          <CustomizeViewButton display={{ base: 'none', xl: 'block' }} />
        </Flex>
      </Flex>
      {harvestSummaryDataSorted.length ? (
        <Box position='relative' mb='2xl' rounded='2xl' ref={containerRef}>
          <Box
            rounded='2xl'
            position='relative'
            _after={{
              content: '""',
              position: 'absolute',
              top: 0,
              zIndex: 2,
              width: '16px',
              right: '-16px',
              height: '100%',
              backgroundColor: 'gray.100'
            }}
          >
            <SectionLoader isLoading={isLoadingCurrentFarmPonds} zIndex={5} rounded='2xl' />
            <Box
              rounded='2xl'
              overflowX='auto'
              overflowY='auto'
              w='100%'
              maxH='calc(100vh - 290px)'
              minH='min-content'
              _before={{
                top: 0,
                left: 0,
                zIndex: 3,
                content: '""',
                height: '100%',
                position: 'absolute',
                boxShadow: 'elevation.200',
                borderTopLeftRadius: '2xl',
                borderBottomLeftRadius: '2xl',
                width: `${firstColumnWidth}px`,
                pointerEvents: 'none'
              }}
              _after={{
                top: 0,
                right: 0,
                zIndex: 3,
                content: '""',
                position: 'absolute',
                height: '100%',
                boxShadow: 'elevation.200',
                borderTopRightRadius: '2xl',
                width: `${lastColumnWidth}px`,
                borderBottomRightRadius: '2xl',
                pointerEvents: 'none'
              }}
            >
              <Table.Root
                backgroundColor='transparent'
                borderSpacing='0'
                borderCollapse='separate'
                css={{
                  whiteSpace: 'nowrap',
                  '& td': { py: 'sm-alt', backgroundColor: 'white' },
                  '& thead': { top: 0, zIndex: 2, position: 'sticky' },
                  '& tbody tr:nth-last-child(2) td': { borderBottom: 0 },
                  '& th': { py: 'sm', backgroundColor: 'squidInkPowder.100' },
                  '& tr:hover td': { backgroundColor: 'squidInkPowder.100' },
                  '& tbody tr:last-of-type': { bottom: 0, zIndex: 2, position: 'sticky' },
                  '& tbody tr:last-of-type td': {
                    borderBottom: 0,
                    borderTop: '0.5px solid',
                    borderTopColor: 'gray.400',
                    backgroundColor: 'squidInkPowder.100'
                  },
                  '& td, th': {
                    px: 'sm-alt',
                    border: 'none',
                    borderRight: '0.5px solid',
                    borderBottom: '0.5px solid',
                    borderRightColor: 'gray.400',
                    borderBottomColor: 'gray.400',
                    '&:nth-last-child(2)': { borderRight: 0 },
                    '&:not([style*="position: sticky"])': { left: 0, position: 'relative' },
                    '&:last-child': {
                      borderRight: 'none',
                      borderLeft: '0.5px solid',
                      borderLeftColor: 'gray.400'
                    }
                  }
                }}
              >
                <Table.Header>
                  <Table.Row>
                    <Table.ColumnHeader
                      zIndex={1}
                      borderTopLeftRadius='2xl'
                      w={`${firstColumnWidth}px`}
                      minW={`${firstColumnWidth}px`}
                      maxW={`${firstColumnWidth}px`}
                      position='sticky !important'
                      left='0px !important'
                    >
                      <HeaderContainer>
                        <Text size='label200'>{trans('t_pond')}</Text>
                        <SortIndicator field='pondName' />
                      </HeaderContainer>
                    </Table.ColumnHeader>
                    <Table.ColumnHeader>
                      <HeaderContainer>
                        <Text size='label200'>{trans('t_cycle')}</Text>
                        <SortIndicator field='cycle' />
                      </HeaderContainer>
                    </Table.ColumnHeader>
                    <Table.ColumnHeader>
                      <HeaderContainer>
                        <Text size='label200'>{trans('t_harvest_date')}</Text>
                        <SortIndicator field='harvestDate' />
                      </HeaderContainer>
                    </Table.ColumnHeader>
                    {harvestSummaryViewVariables.map((variable) => (
                      <Table.ColumnHeader key={variable}>
                        <HeaderContainer>
                          <Text size='label200'>{harvestSummaryVariableLabels[variable]}</Text>
                          <SortIndicator field={variable} />
                        </HeaderContainer>
                      </Table.ColumnHeader>
                    ))}
                    <Table.ColumnHeader
                      zIndex={1}
                      textAlign='center'
                      roundedTopRight='2xl'
                      minW={`${lastColumnWidth}px`}
                      maxW={`${lastColumnWidth}px`}
                      width={`${lastColumnWidth}px`}
                      position='sticky !important'
                      left='unset !important'
                      right='0px !important'
                    >
                      <Text size='label200' textAlign='center'>
                        {trans('t_actions')}
                      </Text>
                    </Table.ColumnHeader>
                  </Table.Row>
                </Table.Header>
                <Table.Body>
                  {harvestSummaryDataSorted.map((rowData, index) => (
                    <Row key={index} data={rowData} harvestIndex={index} rowElements={containerRef} />
                  ))}
                  <Table.Row>
                    <Table.Cell
                      zIndex={1}
                      w={`${firstColumnWidth}px`}
                      minW={`${firstColumnWidth}px`}
                      maxW={`${firstColumnWidth}px`}
                      position='sticky !important'
                      left='0px !important'
                    >
                      <Text size='label200' lineHeight='24px'>
                        {trans('t_count_ponds', { count: totalPonds })}
                      </Text>
                    </Table.Cell>
                    <Table.Cell>
                      <Text size='label200' lineHeight='24px'>
                        {trans('t_count_cycles', { count: totalPonds })}
                      </Text>
                    </Table.Cell>
                    <Table.Cell>
                      <Text size='label200' lineHeight='24px'>
                        -
                      </Text>
                    </Table.Cell>
                    {harvestSummaryViewVariables?.map((variable) => (
                      <Table.Cell key={variable}>{footerValueMap[variable]}</Table.Cell>
                    ))}
                    <Table.Cell
                      zIndex={0}
                      textAlign='center'
                      borderBottomRightRadius='2xl'
                      position='sticky !important'
                      right='0px !important'
                      borderRight='0.5px solid {colors.gray.400}'
                    >
                      <Text size='label200' color='gray.700' />
                    </Table.Cell>
                  </Table.Row>
                </Table.Body>
              </Table.Root>
            </Box>
          </Box>
        </Box>
      ) : (
        <Text size='label200' color='gray.700' textAlign='center' my='2md'>
          {trans('t_no_data_to_display')}
        </Text>
      )}
    </>
  );
}

type RowProps = {
  harvestIndex: number;
  rowElements: RefObject<HTMLDivElement>;
  data?: HarvestsInfoType;
};

function Row(props: RowProps) {
  const { data, harvestIndex, rowElements } = props;

  const { query } = useRouter();
  const { farmEid } = (query ?? {}) as { farmEid?: string };

  const {
    pondEid,
    survival,
    cycle,
    type,
    state,
    harvestDate,
    cycleDays,
    harvestId,
    isPlanned,
    revenue,
    populationId,
    revenuePerPound,
    revenuePoundHarvest,
    averageWeight,
    biomass,
    biomassPerHa,
    animalsHarvested,
    animalsHarvestedM2,
    animalsHarvestedHa,
    selectedProcessorId,
    processorOptions,
    priceList,
    enablesEstimation,
    survivalAtRecord,
    pondName,
    pondId,
    pondSize
  } = data ?? {};

  const { defaultHarvestType: harvestType } = priceList ?? {};

  const processor = processorOptions?.find((item) => item.value === selectedProcessorId);

  const { trans } = getTrans();

  const { trackAction } = useAnalytics();
  const lang = useAppSelector((state) => state.app.lang);
  const currentFarmPonds = useAppSelector((state) => state.farm.currentFarmPonds);

  const harvestSummaryViewVariables = useGetHarvestSummaryCustomizeViewVariables();

  const { population } = useMemo(() => {
    const pond = currentFarmPonds?.find((pond) => pond.currentPopulationId === populationId);
    const { currentPopulation } = pond ?? {};

    return { population: currentPopulation };
  }, [populationId, currentFarmPonds]);

  const { reloadCurrentFarmPonds } = useReloadCurrentFarmPonds();
  const { isLoading, onDeletePartialHarvest, onRecordPartialHarvest } = usePondPartialHarvests({
    population,
    shouldReloadCurrentPopulation: false
  });

  const isRecorded = state === 'recorded';
  const isPartial = type === 'partial';
  const harvestTypeLabel = harvestType === 'headon' ? trans('t_head_on') : trans('t_headless');
  const statusLabel = isRecorded ? trans('t_recorded') : trans('t_planned');
  const typeLabel = isPartial ? trans('t_partial') : trans('t_final');

  const valueMap: Record<HarvestSummaryViewVariable, ReactNode> = {
    type: (
      <Text size='light200' color='gray.700'>
        {type ? typeLabel : '-'}
      </Text>
    ),
    state: (
      <Text size='light200' color='gray.700'>
        {state ? statusLabel : '-'}
      </Text>
    ),
    selectedProcessorId: (
      <Text color='gray.700' size='light200'>
        {processor?.label || '-'}
      </Text>
    ),
    harvestType: (
      <Text size='light200' color='gray.700'>
        {harvestType ? harvestTypeLabel : '-'}
      </Text>
    ),
    averageWeight: (
      <Text size='light200' color='gray.700'>
        {isNumber(averageWeight) ? formatNumber(averageWeight, { lang, fractionDigits: 2 }) : '-'}
      </Text>
    ),
    revenue: (
      <Text size='light200' color='gray.700'>
        {isNumber(revenue) ? formatNumber(revenue, { lang, isCurrency: true, fractionDigits: 0 }) : '-'}
      </Text>
    ),
    revenuePerPound: (
      <Text size='light200' color='gray.700'>
        {isNumber(revenuePerPound) ? formatNumber(revenuePerPound, { lang, isCurrency: true, fractionDigits: 2 }) : '-'}
      </Text>
    ),
    cycleDays: (
      <Text size='light200' color='gray.700'>
        {isNumber(cycleDays) ? (
          <>
            {cycleDays < 2 && trans('t_count_day', { count: formatNumber(cycleDays, { lang, fractionDigits: 0 }) })}
            {cycleDays >= 2 && trans('t_count_days', { count: formatNumber(cycleDays, { lang, fractionDigits: 0 }) })}
          </>
        ) : (
          '-'
        )}
      </Text>
    ),
    revenuePoundHarvest: (
      <Text size='light200' color='gray.700'>
        {isNumber(revenuePoundHarvest)
          ? formatNumber(revenuePoundHarvest, { lang, isCurrency: true, fractionDigits: 2 })
          : '-'}
      </Text>
    ),
    survival: (
      <Text size='light200' color='gray.700'>
        {isNumber(survival) ? formatNumber(survival, { lang, fractionDigits: 0, isPercentage: true }) : '-'}
      </Text>
    ),
    biomass: (
      <Text size='light200' color='gray.700'>
        {isNumber(biomass) ? formatNumber(biomass, { lang, fractionDigits: 0 }) : '-'}
      </Text>
    ),
    biomassPerHa: (
      <Text size='light200' color='gray.700'>
        {isNumber(biomassPerHa) ? formatNumber(biomassPerHa, { lang, fractionDigits: 0 }) : '-'}
      </Text>
    ),
    animalsHarvested: (
      <Text size='light200' color='gray.700'>
        {isNumber(animalsHarvested) ? formatNumber(animalsHarvested, { lang, fractionDigits: 0 }) : '-'}
      </Text>
    ),
    animalsHarvestedHa: (
      <Text size='light200' color='gray.700'>
        {isNumber(animalsHarvestedHa) ? formatNumber(animalsHarvestedHa, { lang, fractionDigits: 0 }) : '-'}
      </Text>
    ),
    animalsHarvestedM2: (
      <Text size='light200' color='gray.700'>
        {isNumber(animalsHarvestedM2) ? formatNumber(animalsHarvestedM2, { lang, fractionDigits: 2 }) : '-'}
      </Text>
    )
  };
  const harvestDateFormatted = harvestDate
    ? DateTime.fromISO(harvestDate).toFormat('LLL dd, yyyy', { locale: lang })
    : harvestDate;
  return (
    <Table.Row>
      <Table.Cell
        zIndex={1}
        w={`${firstColumnWidth}px`}
        minW={`${firstColumnWidth}px`}
        maxW={`${firstColumnWidth}px`}
        position='sticky !important'
        left='0px !important'
      >
        {!pondName && '-'}
        {pondName && (
          <BaseLink
            route='/farm/[farmEid]/pond/[pondEid]'
            _hover={{ textDecoration: 'underline' }}
            params={{ farmEid, pondEid: slugify(`${pondEid}-${pondName}`) }}
          >
            <Text size='light200' overflow='hidden' textOverflow='ellipsis' maxW='105px' title={pondName}>
              {pondName}
            </Text>
          </BaseLink>
        )}
      </Table.Cell>
      <Table.Cell>
        <Text size='light200' overflow='hidden' textOverflow='ellipsis' maxW='70px' title={cycle}>
          {cycle ?? '-'}
        </Text>
      </Table.Cell>
      <Table.Cell>
        <Text size='light200' overflow='hidden' textOverflow='ellipsis' maxW='134px' title={harvestDateFormatted}>
          {harvestDateFormatted ?? '-'}
        </Text>
      </Table.Cell>
      {harvestSummaryViewVariables?.map((variable) => (
        <Table.Cell key={variable}>{valueMap[variable]}</Table.Cell>
      ))}
      <Table.Cell textAlign='center' position='sticky !important' right='0px !important'>
        <MenuRoot
          positioning={{
            offset: {
              mainAxis: 2,
              crossAxis: 6
            }
          }}
          closeOnSelect={false}
        >
          <MenuButton
            px={0}
            h='24px'
            size='sm'
            onClick={() => {
              trackAction(actionsName.farmSummaryMeatballClicked, { index: harvestIndex }).then();
            }}
            selectVariant='secondary'
            _hover={{ opacity: '0.8' }}
          >
            <MoreHoriz />
          </MenuButton>
          <MenuContent portalRef={rowElements}>
            <RecordHarvestModal
              pondId={pondId}
              pondName={pondName}
              pondSize={pondSize}
              revenue={revenue}
              isLoading={isLoading}
              population={population}
              harvestDate={harvestDate}
              harvestAbw={averageWeight}
              harvestAmount={biomass}
              priceList={priceList}
              survivalAtRecord={survivalAtRecord}
              isEdit={isRecorded}
              isPartial={isPartial}
              enablesEstimation={enablesEstimation}
              selectedProcessorId={selectedProcessorId}
              isInPondListView={true}
              isInPondView={false}
              harvestId={harvestId}
              onSuccess={reloadCurrentFarmPonds}
              onRecordPartialHarvest={onRecordPartialHarvest}
            >
              <MenuItem
                value='record'
                onClick={() => {
                  trackAction(actionsName.harvestPondClicked, {
                    index: harvestIndex,
                    isPlanned,
                    itemName: isPlanned ? 'record' : 'edit',
                    trigger: 'farm-summary-harvest-action-button',
                    state: isRecorded ? `Edit recorded ${typeLabel} harvest` : `Record ${typeLabel} harvest`
                  }).then();
                }}
              >
                {isPlanned ? trans('t_record') : trans('t_edit')}
              </MenuItem>
            </RecordHarvestModal>

            {isPlanned && (
              <>
                {!isPartial && (
                  <EditFinalHarvestPlanModal
                    pondId={pondId}
                    pondName={pondName}
                    pondSize={pondSize}
                    population={population}
                    isInPondListView={true}
                    isInPondView={false}
                    triggerContainerProps={{ width: 'unset' }}
                  >
                    <MenuItem
                      value='edit-harvest-plan-pond-btn'
                      data-cy='edit-harvest-plan-pond-btn'
                      onClick={() => {
                        trackAction(actionsName.editHarvestPlanClicked, {
                          pondId,
                          pondName,
                          trigger: 'farm-summary-edit-harvest-plan-action-button',
                          state: 'edit final harvest plan'
                        }).then();
                      }}
                    >
                      {trans('t_edit')}
                    </MenuItem>
                  </EditFinalHarvestPlanModal>
                )}
                {isPartial && (
                  <AddEditPartialHarvestPlanModal
                    pondId={pondId}
                    pondName={pondName}
                    pondSize={pondSize}
                    isInPondListView={true}
                    isInPondView={false}
                    triggerContainerProps={{ width: 'unset' }}
                    defaultValues={{
                      date: harvestDate,
                      processorId: selectedProcessorId,
                      priceList,
                      lbsHarvested: biomass
                    }}
                    harvestId={harvestId}
                  >
                    <MenuItem
                      value='edit-partial-harvest-plan-pond-btn'
                      data-cy='edit-partial-harvest-plan-pond-btn'
                      onClick={() => {
                        trackAction(actionsName.editPartialHarvestPlanClicked, {
                          pondId,
                          pondName,
                          trigger: 'farm-summary-edit-partial-harvest-plan-action-button',
                          state: 'edit partial harvest plan',
                          harvestId,
                          harvestDate
                        }).then();
                      }}
                    >
                      {trans('t_edit')}
                    </MenuItem>
                  </AddEditPartialHarvestPlanModal>
                )}
              </>
            )}

            {isPartial && (
              <DeleteHarvestModal
                pondName={pondName}
                isPlanned={isPlanned}
                onConfirm={() => {
                  onDeletePartialHarvest(harvestId, state === 'recorded', reloadCurrentFarmPonds);
                }}
              >
                <MenuItem
                  value='delete'
                  onClick={() => {
                    trackAction(actionsName.farmSummaryMeatballItemClicked, {
                      index: harvestIndex,
                      isPlanned,
                      itemName: 'delete'
                    }).then();
                  }}
                >
                  {trans('t_delete')}
                </MenuItem>
              </DeleteHarvestModal>
            )}
          </MenuContent>
        </MenuRoot>
      </Table.Cell>
    </Table.Row>
  );
}

function HeaderContainer(props: FlexProps) {
  return <Flex align='center' gap='md' justify='space-between' {...props} />;
}

function CustomizeViewButton(props: BoxProps) {
  return (
    <HarvestSummaryTableCustomizeViewModal {...props}>
      <IconButton h='40px' w='40px' bgColor='white' aria-label='filter-button' _focus={{ outline: 'none' }}>
        <SettingsAdjustFilled w='20px' h='20px' transform='scale(1, -1)' />
      </IconButton>
    </HarvestSummaryTableCustomizeViewModal>
  );
}
