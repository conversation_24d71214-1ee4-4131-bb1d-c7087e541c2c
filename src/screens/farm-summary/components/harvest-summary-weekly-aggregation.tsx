import { Box, BoxProps, Flex, Grid, IconButton, IconButtonProps, Text } from '@chakra-ui/react';
import { ChevronLeftFilled } from '@icons/chevron-left/chevron-left-filled';
import { ChevronRightFilled } from '@icons/chevron-right/chevron-right-filled';
import { CardContainer } from '@components/card-container/card-container';
import { useMemo, useState } from 'react';
import { DateTime } from 'luxon';
import { useAppSelector } from '@redux/hooks';
import { getTrans } from '@i18n/get-trans';
import { formatNumber } from '@utils/number';
import { HarvestsInfoType } from '@screens/pond/components/pond-harvests-tab/pond-harvests-helpers';
import isUndefined from 'lodash/isUndefined';
import { usePermission } from '@hooks/use-permission';

type AggregateWeekDataParams = {
  weekKey: string;
  isFinal: boolean;
  biomassToAdd: number;
  revenueToAdd: number;
  isPlannedPastHarvest: boolean;
};

type HarvestSummaryDataItem = {
  biomass: number;
  revenue: number;
  hasPlannedPastHarvest?: boolean;
  harvests: { final: number; partial: number };
};

interface HarvestSummaryWeeklyAggregationProps extends BoxProps {
  harvestSummaryData: HarvestsInfoType[];
}

export function HarvestSummaryWeeklyAggregation(props: HarvestSummaryWeeklyAggregationProps) {
  const { harvestSummaryData, ...rest } = props;

  const { trans } = getTrans();
  const ITEMS_PER_PAGE = 6;

  const lang = useAppSelector((state) => state.app.lang);
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);
  const currentFarmPonds = useAppSelector((state) => state.farm.currentFarmPonds);
  const user = useAppSelector((state) => state.auth.user);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);
  const isAdmin = usePermission({ role: 'admin', entity: 'farm', entityId: currentFarm?._id });
  const [currentPageIndex, setCurrentPageIndex] = useState(0);

  const tableData = useMemo(() => {
    const data = new Map<string, HarvestSummaryDataItem>();

    const getWeekStart = (date: string) => {
      return DateTime.fromISO(date).startOf('week').startOf('day');
    };

    const formatDateRange = (startDate: DateTime) => {
      const endDate = startDate.plus({ days: 6 });
      return `${startDate.setLocale(lang).toFormat('MMM d')} - ${endDate.setLocale(lang).toFormat('MMM d')}`;
    };

    const aggregateWeekData = (params: AggregateWeekDataParams) => {
      const { weekKey, isFinal, revenueToAdd, biomassToAdd, isPlannedPastHarvest } = params;

      const currentWeekData: HarvestSummaryDataItem = data.get(weekKey) || {
        biomass: 0,
        revenue: 0,
        harvests: { final: 0, partial: 0 }
      };

      data.set(weekKey, {
        biomass: currentWeekData.biomass + (biomassToAdd ?? 0),
        revenue: currentWeekData.revenue + (revenueToAdd ?? 0),
        hasPlannedPastHarvest: currentWeekData.hasPlannedPastHarvest || isPlannedPastHarvest,
        harvests: {
          final: currentWeekData.harvests.final + (isFinal ? 1 : 0),
          partial: currentWeekData.harvests.partial + (isFinal ? 0 : 1)
        }
      });
    };

    harvestSummaryData?.map((harvest) => {
      const { type, state, biomass, revenue, harvestDate } = harvest ?? {};

      const todayDateLuxon = DateTime.local();
      const harvestDateLuxon = DateTime.fromISO(harvestDate);
      const isHarvestInPast = harvestDateLuxon.startOf('day') < todayDateLuxon.startOf('day');

      const weekStart = getWeekStart(harvestDate);

      aggregateWeekData({
        biomassToAdd: biomass,
        revenueToAdd: revenue,
        isFinal: type === 'final',
        weekKey: formatDateRange(weekStart),
        isPlannedPastHarvest: isHarvestInPast && state === 'planned'
      });
    });

    return Array.from(data.entries())
      .map(([weekRange, items]) => ({ weekRange, ...items }))
      .sort((a, b) => {
        const dateA = DateTime.fromFormat(a.weekRange.split(' - ')[0], 'MMM d', { locale: 'en' });
        const dateB = DateTime.fromFormat(b.weekRange.split(' - ')[0], 'MMM d', { locale: 'en' });
        return dateA.toMillis() - dateB.toMillis();
      });
  }, [currentFarm, currentFarmPonds, harvestSummaryData]);

  const totalPages = Math.ceil(tableData.length / ITEMS_PER_PAGE);

  const paginatedData = useMemo(() => {
    const startIndex = currentPageIndex * ITEMS_PER_PAGE;
    return tableData.slice(startIndex, startIndex + ITEMS_PER_PAGE);
  }, [tableData, currentPageIndex]);

  const handlePrevious = () => {
    setCurrentPageIndex((prev) => Math.max(0, prev - 1));
  };

  const handleNext = () => {
    setCurrentPageIndex((prev) => Math.min(totalPages - 1, prev + 1));
  };

  const gridTemplate = 'minmax(auto, 174px) 39px repeat(6, 1fr) 32px';

  const rows = [
    {
      label: isBiomassUnitLbs ? trans('t_biomass_lb') : trans('t_biomass_kg'),
      render: (weekData: HarvestSummaryDataItem) => {
        const { biomass, hasPlannedPastHarvest } = weekData ?? {};
        return `${formatNumber(biomass, { lang, fractionDigits: 0 })}${hasPlannedPastHarvest ? '*' : ''}`;
      }
    },
    ...(isAdmin
      ? [
          {
            label: trans('t_revenue'),
            render: (weekData: HarvestSummaryDataItem) => {
              const { revenue, hasPlannedPastHarvest } = weekData ?? {};
              return `${formatNumber(revenue, { lang, isCurrency: true, fractionDigits: 0 })}${hasPlannedPastHarvest ? '*' : ''}`;
            }
          }
        ]
      : []),
    {
      label: trans('t_type'),
      render: (weekData: HarvestSummaryDataItem) =>
        trans('t_count_final_count_partial', {
          finalCount: weekData.harvests.final,
          partialCount: weekData.harvests.partial
        })
    }
  ];

  return (
    <CardContainer py='sm-alt' bgColor='brandBlue.100' {...rest}>
      <Grid>
        <Grid minH='40px' templateColumns={gridTemplate} gap='xs-alt' alignItems='center'>
          <Flex flexDir='column' gap='xs-alt'>
            <Text size='label200'>{trans('t_harvest_summary')}</Text>
          </Flex>
          <NavigationButton direction='previous' onClick={handlePrevious} disabled={currentPageIndex === 0} />
          {paginatedData.map((weekData) => (
            <Flex key={weekData.weekRange} align='center' justify='center' bg='white' rounded='xl' h='40px'>
              <Text size='label200' textAlign='center'>
                {weekData.weekRange}
              </Text>
            </Flex>
          ))}
          <NavigationButton direction='next' onClick={handleNext} disabled={currentPageIndex >= totalPages - 1} />
        </Grid>
        {rows.map(({ label, render }) => (
          <Grid
            py='xs'
            key={label}
            minH='36px'
            gap='xs-alt'
            alignItems='center'
            borderBottom={label !== trans('t_harvests') ? '0.5px solid' : undefined}
            borderBottomColor='gray.400'
            templateColumns={gridTemplate}
          >
            <Text size='label200'>{label}</Text>
            <Box />
            {paginatedData.map((weekData) => (
              <Text key={weekData.weekRange} textAlign='center' size='light200'>
                {render(weekData)}
              </Text>
            ))}
            <Box />
          </Grid>
        ))}
      </Grid>
    </CardContainer>
  );
}

type NavigationButtonProps = Omit<IconButtonProps, 'aria-label' | 'icon'> & {
  direction: 'previous' | 'next';
};

function NavigationButton({ direction, ...props }: NavigationButtonProps) {
  const DirectionIcon = direction === 'previous' ? ChevronLeftFilled : ChevronRightFilled;

  return (
    <IconButton
      w='32px'
      h='32px'
      userSelect='none'
      aria-label={direction}
      bgColor='brandBlue.200'
      _hover={{ opacity: 0.8 }}
      _focus={{ outline: 'none' }}
      _disabled={{ opacity: 0.5 }}
      {...props}
    >
      <DirectionIcon w='20px' h='20px' color='black' bgColor='brandBlue.200' />
    </IconButton>
  );
}
