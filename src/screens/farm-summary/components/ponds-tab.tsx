import { getTrans } from '@i18n/get-trans';
import { Dispatch, RefObject, SetStateAction, useEffect, useRef, useState } from 'react';
import { formatNumber } from '@utils/number';
import { GridIcon } from '@icons/grid/grid-icon';
import { SwapVertical } from '@icons/swap/swap-vertical';
import { BaseButton } from '@components/base/base-button';
import { ViewTableIcon } from '@icons/view-table/view-table-icon';
import { OverlayLoader } from '@components/loaders/overlay-loader';
import { ArrowBigRight } from '@icons/arrow-big-right/arrow-big-right';
import { AlertRowContainer } from '@components/alert/alert-row-container';
import { HideOnMobileView } from '@components/mobile-view/hide-on-mobile-view';
import { getCacheItem, setCacheItem, sevenDaysCacheTime } from '@utils/local-cache';
import { ExclamationMarkSolid } from '@icons/exclamation-mark/exclamation-mark-solid';
import { PondFilterPopover } from '@components/pond-filter-popover/pond-filter-popover';
import { DataUpdateAlert, SortByKeys, SortByOptions } from '@screens/farm-summary/farm-summary-screen';
import { Box, Collapsible, Flex, SimpleGrid, Table, TableColumnHeaderProps, Text, TextProps } from '@chakra-ui/react';
import {
  AcrossAllPondsChartCard,
  AcrossAllPondsSelectVariable,
  SelectedOptionValue
} from '@screens/farm-summary/components/across-all-ponds-chart-card';
import { useRouter } from 'next/router';
import { getWebViewType } from '@utils/get-web-view-type';
import { CurrentFarmPonds } from '@redux/farm/set-current-farm-ponds';
import { IndicatorOption, IndicatorValues, PondCardViewType } from '@components/pond-card/helper';
import { FarmSummaryViewVariable, getPondViewVariableLabels } from '@screens/farm-summary/helpers/pond-list';
import { useAppSelector } from '@redux/hooks';
import { getPondState } from '@screens/pond/helpers/get-pond-state';
import { DateTime } from 'luxon';
import { getDaysDiffBetweenDates } from '@screens/farm/helpers/farm-dates';
import { PondCard } from '@components/pond-card/pond-card';
import { useAnalytics } from '@hooks/use-analytics';
import { FarmSummaryCustomizeViewModal } from '@screens/farm-summary/components/farm-summary-customize-view-modal';
import { AddEditPondModal } from '@screens/pond/components/modals/add-edit-pond-modal';
import { ExportPondDataModal } from '@screens/pond/components/modals/export-pond-data-modal';
import { actionsName } from '@utils/segment';
import { MoreHoriz } from '@icons/more-horiz/more-horiz';
import { BaseMenu } from '@components/base/base-menu';
import { useGetDeviceDimensions } from '@hooks/use-get-device-dimensions';
import { MobileLandscapeOnlyModal } from '@components/mobile-view/mobile-landscape-only-modal';
import { AcrossAllPondsChart } from '@screens/farm-summary/components/across-all-ponds-chart';
import { PondFilterPopoverFormType } from '@components/pond-filter-popover/use-generate-filter-tags';
import { MenuButton, MenuContent, MenuItem, MenuRoot } from '@components/ui/menu';
import { TableContainer } from '@components/ui/table-container';
import { FilterTags } from '@components/pond-filter-popover/filter-tags';
import { CloseFilled } from '@icons/close/close-filled';
import { BaseSearchInput } from '@components/base/base-search-input';
import { VisionOnlyGuard } from '@components/permission-wrappers/vision-only-guard';

export const pondsTableFirstColumnWidth = 226;

type PondsTabProps = {
  isLoading: boolean;

  isChartVisible: boolean;
  filteredPonds: CurrentFarmPonds;

  selectedIndicatingStatus: IndicatorOption;
  setSelectedIndicatingStatus: Dispatch<SetStateAction<IndicatorOption>>;

  search: string;
  setSearch: Dispatch<SetStateAction<string>>;

  sortBy: SortByOptions;
  setSortBy: Dispatch<SetStateAction<SortByOptions>>;

  profitProjectionToView: PondCardViewType;
  setProfitProjectionToView: Dispatch<SetStateAction<PondCardViewType>>;

  estimatedValuesCounter: number;
  aboveCapacityThreshold: number;
  pondsAboveCarryingCapacity: number;

  farmOverviewFilter: PondFilterPopoverFormType;
  setFarmOverviewFilter: Dispatch<SetStateAction<PondFilterPopoverFormType>>;

  indicatingStatusOptions: IndicatorOption[];
  pondViewVariables: FarmSummaryViewVariable[];
};

export function PondsTab(props: PondsTabProps) {
  const {
    search,
    sortBy,
    isLoading,
    setSortBy,
    setSearch,
    filteredPonds,
    isChartVisible,
    pondViewVariables,
    farmOverviewFilter,
    setFarmOverviewFilter,
    estimatedValuesCounter,
    aboveCapacityThreshold,
    profitProjectionToView,
    indicatingStatusOptions,
    selectedIndicatingStatus,
    setProfitProjectionToView,
    pondsAboveCarryingCapacity,
    setSelectedIndicatingStatus
  } = props;

  const { trans } = getTrans();
  const { trackAction } = useAnalytics();

  const lang = useAppSelector((state) => state.app.lang);
  const user = useAppSelector((state) => state.auth.user);
  const currentFarm = useAppSelector((state) => state.farm?.currentFarm);

  const { _id: farmId, name: farmName } = currentFarm ?? {};
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};

  const { isWebViewTypeMobile } = getWebViewType();
  const { query } = useRouter();
  const { listView } = (query ?? {}) as { listView: 'card' | 'list' };

  const tableContainerRef = useRef<HTMLDivElement>(null);

  const pondViewVariableLabels = getPondViewVariableLabels({ unitsConfig });

  const [isGridView, setIsGridView] = useState(false);

  useEffect(() => {
    getCacheItem<boolean>('gridView').then((res) => {
      //this handling is made for mobile app to open in web view with desired list view type.
      if (listView === 'list' || listView === 'card') {
        if (listView === 'list') {
          setIsGridView(false);
        } else {
          setIsGridView(true);
        }
      } else {
        setIsGridView(isWebViewTypeMobile ? true : !!res);
      }
    });
  }, [listView, isWebViewTypeMobile]);

  const handleSortBy = (value: SortByKeys) => {
    trackAction(actionsName.pondListViewSortClicked, { sortColumn: value }).then();

    setSortBy((prev) => {
      const newSortBy: SortByOptions = { key: value, order: 'asc' };
      if (prev.order === 'asc' && prev.key === value) {
        newSortBy.order = 'desc';
      }
      return newSortBy;
    });
  };

  const commonTableHeaderStyles: TableColumnHeaderProps = {
    color: 'gray.800',
    p: 0,
    border: 'none',
    position: 'sticky',
    top: 0,
    zIndex: 10
  };

  const commonHeaderTextStyle: TextProps = {
    px: 'sm-alt',
    py: 'sm',
    borderRight: '0.5px solid',
    borderColor: 'border.gray',
    bgColor: 'bg.squidInkPowder.weak',
    size: 'label200',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 'md',
    cursor: 'pointer',
    lineHeight: '20px'
  };

  return (
    <>
      <HideOnMobileView>
        <Flex flexDir='column' gap='sm-alt'>
          <DataUpdateAlert />
          {pondsAboveCarryingCapacity > 0 && (
            <AlertRowContainer status='error'>
              <ExclamationMarkSolid color='icon.semanticRed' w='20px' h='20px' />

              <Flex flex={1} flexDir={['column', 'row', 'row']} gap='sm-alt' align='center' flexWrap='wrap'>
                <Text size='label200'>
                  {pondsAboveCarryingCapacity >= 2 &&
                    trans('t_x_ponds_are_above_carrying_capacity', {
                      count: formatNumber(pondsAboveCarryingCapacity, { lang, fractionDigits: 0 })
                    })}
                  {pondsAboveCarryingCapacity < 2 &&
                    trans('t_x_ponds_are_above_carrying_capacity_singular', {
                      count: formatNumber(pondsAboveCarryingCapacity, { lang, fractionDigits: 0 })
                    })}
                </Text>

                <BaseButton
                  variant='link'
                  size='sm'
                  px={0}
                  onClick={() =>
                    setFarmOverviewFilter((prev) => ({
                      ...prev,
                      aboveCarryingCapacity: aboveCapacityThreshold,
                      aboveCarryingCapacityFlag: true
                    }))
                  }
                  fontSize={{ base: 'sm', sm: 'md' }}
                >
                  {trans('t_show_ponds_with_a_carrying_capacity')}
                  <ArrowBigRight color='button.link' width='20px' height='20px' />
                </BaseButton>
              </Flex>
            </AlertRowContainer>
          )}
        </Flex>
      </HideOnMobileView>

      <AcrossAllPondsMobileChartModal
        farmName={farmName}
        filteredPonds={filteredPonds}
        profitProjectionToView={profitProjectionToView}
      />

      {isChartVisible && (
        <Collapsible.Root open={isChartVisible}>
          <Collapsible.Content overflow='visible'>
            <AcrossAllPondsChartCard
              bgColor='brandBlue.100'
              display={{ base: 'none', lg: 'block' }}
              currentFarmPonds={filteredPonds}
              profitProjectionToView={profitProjectionToView}
              data-cy='ponds-chart-card'
            />
          </Collapsible.Content>
        </Collapsible.Root>
      )}

      <Flex
        justify='space-between'
        gap='sm-alt'
        flexWrap={{ base: 'wrap', lg: 'nowrap' }}
        data-cy='production-ponds-options-container'
      >
        <BaseSearchInput
          value={search}
          rightElement={search && <CloseFilled cursor='pointer' hasBackground={false} onClick={() => setSearch('')} />}
          onChange={(event) => setSearch(event.target.value)}
          formControlProps={{ w: { base: '100%', lg: '230px' } }}
          data-cy='production-ponds-search'
        />

        <Flex align='center' gap='sm-alt' flexWrap='wrap' flex={{ base: '1', xl: 'unset' }}>
          <VisionOnlyGuard>
            <IndicatingStatusSelector
              setValue={setSelectedIndicatingStatus}
              indicatingStatusOptions={indicatingStatusOptions}
              selectedIndicatingStatus={selectedIndicatingStatus}
            />

            <PondViewSelector
              setProfitProjectionToView={setProfitProjectionToView}
              profitProjectionToView={profitProjectionToView}
            />
          </VisionOnlyGuard>

          <PondFilterPopover
            defaultValues={farmOverviewFilter}
            onApplyFilter={(filters) => {
              setCacheItem('farmSummaryFilters', filters, sevenDaysCacheTime).then();
              setFarmOverviewFilter(filters);
            }}
            alignSelf='flex-end'
            data-cy='ponds-list-filters'
          />
          <Flex align='center' gap='sm-alt' ms='auto'>
            {!isWebViewTypeMobile && (
              <Flex p='3xs' align='center' justify='center' bgColor='bg.gray.strong' borderRadius='4xl'>
                <Flex
                  borderRadius='4xl'
                  p='xs'
                  justify='center'
                  align='center'
                  cursor='pointer'
                  bgColor={isGridView ? 'bg.gray.strong' : 'white'}
                  onClick={() => {
                    setIsGridView(false);
                    setCacheItem('gridView', false, sevenDaysCacheTime).then();
                  }}
                  data-cy='list-view-btn'
                >
                  <ViewTableIcon color='icon.gray' />
                </Flex>
                <Flex
                  borderRadius='4xl'
                  p='xs'
                  justify='center'
                  align='center'
                  cursor='pointer'
                  bgColor={isGridView ? 'white' : 'bg.gray.strong'}
                  onClick={() => {
                    setIsGridView(true);
                    setSortBy({ key: 'pondName', order: 'asc' });
                    setCacheItem('gridView', true, sevenDaysCacheTime).then();
                  }}
                  data-cy='grid-view-btn'
                >
                  <GridIcon />
                </Flex>
              </Flex>
            )}
            <MoreOptionsMenu farmId={farmId} statusIndicatorValue={selectedIndicatingStatus?.value} />
          </Flex>
        </Flex>
      </Flex>

      <FilterTags
        cycleDaysLessThan={farmOverviewFilter?.cycleDays?.lessThan}
        cycleDaysMoreThan={farmOverviewFilter?.cycleDays?.moreThan}
        abwLessThan={farmOverviewFilter?.abw?.lessThan}
        abwMoreThan={farmOverviewFilter?.abw?.moreThan}
        flagged={farmOverviewFilter?.flagged}
        aboveTarget={farmOverviewFilter?.aboveTarget}
        onTrack={farmOverviewFilter?.onTrack}
        offTrack={farmOverviewFilter?.offTrack}
        aboveCarryingCapacity={farmOverviewFilter?.aboveCarryingCapacity}
        superVisor={farmOverviewFilter?.superVisor}
        onApplyFilter={(filters) => {
          setCacheItem('farmSummaryFilters', filters, sevenDaysCacheTime).then();
          setFarmOverviewFilter(filters);
        }}
        mb='sm-alt'
      />

      <OverlayLoader isLoading={isLoading} zIndex={40} />

      <Flex flexDirection='column' gap='sm-alt' data-cy='production-ponds-list' mb='2xl'>
        {!isGridView && (
          <>
            {!!estimatedValuesCounter && (
              <Text size='label200' color='text.gray.disabled'>
                {trans('t_using_imputed_values_for_calculations')}
              </Text>
            )}
            <Box pos='relative'>
              <TableContainer
                w='100%'
                maxH='calc(100vh - 340px)'
                minH='225px'
                rounded='2xl'
                overflowY='auto'
                ref={tableContainerRef}
                data-cy='ponds-list-list-view'
                _before={{
                  top: 0,
                  left: 0,
                  zIndex: 20,
                  content: '""',
                  height: '100%',
                  position: 'absolute',
                  boxShadow: 'elevation.200',
                  borderTopLeftRadius: '2xl',
                  borderBottomLeftRadius: '2xl',
                  width: `${pondsTableFirstColumnWidth}px`,
                  pointerEvents: 'none'
                }}
              >
                <Table.Root backgroundColor='unset' borderCollapse='collapse'>
                  <Table.Header>
                    <Table.Row borderBottom='0.5px solid' borderColor='border.gray'>
                      <Table.ColumnHeader
                        {...commonTableHeaderStyles}
                        w={`${pondsTableFirstColumnWidth}px`}
                        minW={`${pondsTableFirstColumnWidth}px`}
                        left={0}
                        zIndex={11}
                        onClick={() => handleSortBy('pondName')}
                      >
                        <Text
                          {...commonHeaderTextStyle}
                          justifyContent='flex-end'
                          borderTopStartRadius='xl'
                          gap='2xl-alt'
                        >
                          {trans('t_pond')}
                          <SwapVertical
                            upArrowColor={sortBy.key === 'pondName' && sortBy.order === 'asc' ? 'text.gray' : undefined}
                            downArrowColor={
                              sortBy.key === 'pondName' && sortBy.order === 'desc' ? 'text.gray' : undefined
                            }
                          />
                        </Text>
                      </Table.ColumnHeader>

                      {pondViewVariables.map((variable, index) => {
                        const isLastItem = index === pondViewVariables.length - 1;
                        return (
                          <Table.ColumnHeader
                            key={variable}
                            {...commonTableHeaderStyles}
                            onClick={() => handleSortBy(variable)}
                          >
                            <Text
                              {...commonHeaderTextStyle}
                              borderTopEndRadius={isLastItem ? 'xl' : '0'}
                              borderRight={isLastItem ? 'none' : '0.5px solid'}
                            >
                              {pondViewVariableLabels[variable]}
                              <SwapVertical
                                upArrowColor={
                                  sortBy.key === variable && sortBy.order === 'asc' ? 'text.gray' : undefined
                                }
                                downArrowColor={
                                  sortBy.key === variable && sortBy.order === 'desc' ? 'text.gray' : undefined
                                }
                              />
                            </Text>
                          </Table.ColumnHeader>
                        );
                      })}
                    </Table.Row>
                  </Table.Header>
                  <Table.Body>
                    {!filteredPonds.length && (
                      <Table.Row>
                        <Table.Cell colSpan={pondViewVariables.length + 2}>
                          <Text textAlign='center'>{trans('t_no_data_to_display')} </Text>
                        </Table.Cell>
                      </Table.Row>
                    )}
                    <PondList
                      selectedIndicatingStatus={selectedIndicatingStatus}
                      filteredPonds={filteredPonds}
                      viewVariables={pondViewVariables}
                      isGridView={isGridView}
                      profitProjectionToView={profitProjectionToView}
                      tableContainerRef={tableContainerRef}
                    />
                  </Table.Body>
                </Table.Root>
              </TableContainer>
            </Box>
          </>
        )}
        {!filteredPonds.length && isGridView && (
          <Text textAlign='center' mt='md' data-cy='ponds-list-no-data'>
            {trans('t_no_data_to_display')}
          </Text>
        )}
        {isGridView && (
          <SimpleGrid columns={[1, 2, 2, 3, 3, 4]} gapX='sm-alt' gapY='md' data-cy='ponds-list-grid-view'>
            <PondList
              selectedIndicatingStatus={selectedIndicatingStatus}
              filteredPonds={filteredPonds}
              isGridView={isGridView}
              viewVariables={pondViewVariables}
              profitProjectionToView={profitProjectionToView}
            />
          </SimpleGrid>
        )}
      </Flex>
    </>
  );
}

interface IndicatingStatusSelectorProps {
  setValue: Dispatch<SetStateAction<IndicatorOption>>;
  indicatingStatusOptions: IndicatorOption[];
  selectedIndicatingStatus: IndicatorOption;
}

function IndicatingStatusSelector(props: IndicatingStatusSelectorProps) {
  const { setValue, indicatingStatusOptions, selectedIndicatingStatus } = props;
  const { trans } = getTrans();
  const { trackAction } = useAnalytics();

  const mainIndicatingStatusOptions = indicatingStatusOptions.filter((item) => !item.isDisabled);
  const additionalIndicatingStatusOptions = indicatingStatusOptions.filter((item) => item.isDisabled);
  const additionalOptionGroup = additionalIndicatingStatusOptions.length
    ? { title: `${trans('t_not_selected_in_pond_card')}:`, options: additionalIndicatingStatusOptions }
    : undefined;

  return (
    <BaseMenu
      titlePrefix={`${trans('t_indicating_status')}: `}
      placeholder={trans('t_select_option')}
      selectedOption={selectedIndicatingStatus}
      options={mainIndicatingStatusOptions}
      additionalOptionGroup={additionalOptionGroup}
      onItemSelect={(option) => {
        const value = option.value;
        trackAction(actionsName.pondListViewIndicatingStatusClicked, { indicatingStatus: value }).then();
        setCacheItem('farmIndicatingStatusSelector', option, sevenDaysCacheTime).then();
        setValue(option as IndicatorOption);
      }}
      w={{ base: '100%', md: 'auto' }}
      data-cy='production-insight-indicator-selector'
    />
  );
}

interface AcrossAllPondsMobileChartModalProps {
  filteredPonds: CurrentFarmPonds;
  profitProjectionToView: PondCardViewType;
  farmName?: string;
}

function AcrossAllPondsMobileChartModal(props: AcrossAllPondsMobileChartModalProps) {
  const { filteredPonds, profitProjectionToView, farmName } = props;
  const { trans } = getTrans();
  const [selectedAcrossAllPondsOption, setSelectedAcrossAllPondsOption] = useState<SelectedOptionValue>({
    value: 'averageWeight',
    label: trans('t_abw_g')
  });

  const deviceDimensions = useGetDeviceDimensions();

  const isPortrait = deviceDimensions.height > deviceDimensions.width;

  const chartHeight = isPortrait ? deviceDimensions.width - 100 : deviceDimensions.height - 100;

  return (
    <MobileLandscapeOnlyModal
      title={farmName}
      buttonTitle={trans('t_click_here_to_see_graph')}
      headerActionComponent={
        <AcrossAllPondsSelectVariable
          selectedAcrossAllPondsOption={selectedAcrossAllPondsOption}
          setSelectedAcrossAllPondsOption={setSelectedAcrossAllPondsOption}
        />
      }
    >
      <Box px='md'>
        <Box rounded='2xl' py='sm-alt' bgColor='brandBlue.100'>
          <AcrossAllPondsChart
            acrossAllPondsOption={selectedAcrossAllPondsOption}
            currentFarmPonds={filteredPonds}
            isExpanded={false}
            profitProjectionToView={profitProjectionToView}
            height={chartHeight}
          />
        </Box>
      </Box>
    </MobileLandscapeOnlyModal>
  );
}

interface PondListProps {
  filteredPonds: CurrentFarmPonds;
  selectedIndicatingStatus: IndicatorOption;
  isGridView: boolean;
  viewVariables: FarmSummaryViewVariable[];
  profitProjectionToView: PondCardViewType;
  tableContainerRef?: RefObject<HTMLDivElement>;
}

function PondList(props: PondListProps) {
  const {
    filteredPonds,
    selectedIndicatingStatus,
    isGridView,
    viewVariables,
    profitProjectionToView,
    tableContainerRef
  } = props;
  const lang = useAppSelector((state) => state.app.lang);

  const currentFarmSlug = useAppSelector((state) => state.farm?.currentFarmSlug);
  const currentFarm = useAppSelector((state) => state.farm?.currentFarm);
  const { timezone: farmTimezone, _id: farmId } = currentFarm ?? {};

  return (
    <>
      {filteredPonds.map((pond) => {
        const { currentPopulation, name: pondName, _id: pondId, size: pondSize, eid: pondEid } = pond;
        const { isNewPond, isHarvested, hasMonitoring } = getPondState({ population: currentPopulation });
        const {
          _id: populationId,
          stockedAt,
          stockedAtTimezone,
          seedingAverageWeight,
          lastMonitoringDate,
          history,
          symmetry,
          harvestPlan,
          metadata,
          lastMonitoringMlResult,
          cycleInformation,
          productionPrediction,
          estimatedFeed,
          estimatedSurvival,
          stockingCostsMillar,
          manualAverageWeights,
          partialHarvest,
          seedingQuantity,
          lastMonitoringDistance
        } = currentPopulation ?? {};
        const {
          projection,
          indicators,
          harvestType: oldHarvestType,
          processorPriceList,
          harvestDate
        } = harvestPlan ?? {};
        const harvestType = processorPriceList?.defaultHarvestType ?? oldHarvestType;

        const lastMonitoringDateLuxon = DateTime.fromISO(lastMonitoringDate, { zone: farmTimezone });
        const lastMonitoringDateFormatted = lastMonitoringDateLuxon.toFormat('yyyy-MM-dd');
        const lastMonitoringTotalAnimals = lastMonitoringMlResult?.total;
        const isFlagged = metadata?.isFlagged;
        const manuallyMonitored = metadata?.manuallyMonitored;
        const hasSurvivalEstimatedValues = !!estimatedSurvival?.[lastMonitoringDateFormatted];
        const hasFeedEstimatedValues = !!estimatedFeed?.[lastMonitoringDateFormatted];

        const lastHistoryData = history?.[0];
        const firstHistoryData = history?.[history.length - 1];

        const stockedAtLuxon = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone ?? farmTimezone });

        const cycleDays = getDaysDiffBetweenDates({
          baseDate: lastMonitoringDateFormatted,
          dateToCompare: stockedAtLuxon.toFormat('yyyy-MM-dd')
        });

        const { plannedHarvestIdx, optimalHarvestIdx, profitProjectionData } = projection ?? {};
        const plannedOptimalHarvestDate = profitProjectionData?.[optimalHarvestIdx]?.date;

        const productionPredictionHarvestDate = productionPrediction?.find((p) => p.date === harvestDate);
        const plannedProfitProjection = profitProjectionData?.[plannedHarvestIdx]?.[harvestType as 'headon'];

        const isLastPondCard = filteredPonds[filteredPonds.length - 1]?._id === pondId;

        return (
          <PondCard
            key={pondId}
            selectedIndicatingStatus={selectedIndicatingStatus}
            indicators={indicators}
            pondId={pondId}
            pondName={pondName}
            pondEid={pondEid}
            farmEid={currentFarmSlug}
            farmId={farmId}
            farmTimezone={farmTimezone}
            isFlagged={isFlagged}
            populationId={populationId}
            manualAverageWeights={manualAverageWeights}
            lastHistory={{ ...lastHistoryData, cycleDays }}
            plannedProfitProjection={plannedProfitProjection}
            profitProjectionView={profitProjectionToView}
            productionPredictionHarvestDate={productionPredictionHarvestDate}
            optimalHarvestDate={plannedOptimalHarvestDate}
            viewVariables={viewVariables}
            manuallyMonitored={manuallyMonitored}
            symmetry={symmetry}
            lastMonitoringTotalAnimals={lastMonitoringTotalAnimals}
            seedingAverageWeight={seedingAverageWeight}
            cycleInformation={cycleInformation}
            isNewPond={isNewPond}
            isHarvested={isHarvested}
            hasMonitoring={hasMonitoring}
            pondSize={pondSize}
            stockedAtDate={stockedAtLuxon.toFormat('LLL dd, yyyy', { locale: lang })}
            seedingQuantity={seedingQuantity}
            partialHarvest={partialHarvest}
            firstMonitoring={firstHistoryData}
            hasSurvivalEstimatedValues={hasSurvivalEstimatedValues}
            hasFeedEstimatedValues={hasFeedEstimatedValues}
            harvestPlanDate={harvestDate}
            isGridView={isGridView}
            stockingCostsMillar={stockingCostsMillar}
            isLastPondCard={isLastPondCard}
            tableContainerRef={tableContainerRef}
            lastMonitoringDistance={lastMonitoringDistance}
          />
        );
      })}
    </>
  );
}

function MoreOptionsMenu(props: { farmId: string; statusIndicatorValue: IndicatorValues }) {
  const { farmId, statusIndicatorValue } = props;
  const { trans } = getTrans();
  const { trackAction } = useAnalytics();
  const customizeViewButtonRef = useRef<HTMLDivElement>(null);
  const newPondButtonRef = useRef<HTMLDivElement>(null);
  const exportButtonRef = useRef<HTMLDivElement>(null);

  return (
    <Box ms='auto'>
      <FarmSummaryCustomizeViewModal statusIndicatorValue={statusIndicatorValue}>
        <Box ref={customizeViewButtonRef} display='none'>
          {trans('t_customize_view')}
        </Box>
      </FarmSummaryCustomizeViewModal>
      <AddEditPondModal farmId={farmId} isInPondListView>
        <Box ref={newPondButtonRef} display='none'>
          {trans('t_new_pond')}
        </Box>
      </AddEditPondModal>
      <ExportPondDataModal selectedIndicatingStatus={{ value: statusIndicatorValue } as IndicatorOption}>
        <Box ref={exportButtonRef} display='none'>
          {trans('t_export')}
        </Box>
      </ExportPondDataModal>
      <MenuRoot>
        <MenuButton
          selectVariant='secondary'
          size='sm'
          px={0}
          _hover={{ opacity: '0.8' }}
          analyticsId={actionsName.customizeViewClicked}
          data-cy='more-options-menu-btn'
        >
          <MoreHoriz />
        </MenuButton>

        <MenuContent>
          <MenuItem
            onClick={() => {
              trackAction(actionsName.customizeViewClicked).then();
              customizeViewButtonRef.current?.click();
            }}
            data-cy='customize-view-btn'
            value='customize-view-btn'
          >
            {trans('t_customize_view')}
          </MenuItem>

          <MenuItem
            data-cy='create-new-pond-btn'
            value='create-new-pond-btn'
            w='100%'
            onClick={() => {
              trackAction(actionsName.pondListViewNewPondClicked).then();
              newPondButtonRef.current?.click();
            }}
          >
            {trans('t_new_pond')}
          </MenuItem>

          <MenuItem
            data-cy='export-btn'
            value='export-btn'
            w='100%'
            onClick={() => {
              exportButtonRef.current?.click();
            }}
          >
            {trans('t_export')}
          </MenuItem>
        </MenuContent>
      </MenuRoot>
    </Box>
  );
}

interface PondViewSelectorProps {
  setProfitProjectionToView: Dispatch<SetStateAction<PondCardViewType>>;
  profitProjectionToView: PondCardViewType;
}

function PondViewSelector(props: PondViewSelectorProps) {
  const { setProfitProjectionToView, profitProjectionToView } = props;
  const { trans } = getTrans();
  const { trackAction } = useAnalytics();

  const options: { label: string; value: PondCardViewType }[] = [
    { label: trans('t_current_view'), value: 'current' },
    { label: trans('t_expected_at_harvest'), value: 'harvest' }
  ];
  const selectedOption = options.find((option) => option.value === profitProjectionToView);

  const updateProfitProjectToView = (value: PondCardViewType) => {
    trackAction(actionsName.profitProjectionToViewSummaryClicked, { profitProjectionToView: value }).then();
    setCacheItem('farmSummaryPondView', value, sevenDaysCacheTime).then();
    setProfitProjectionToView(value);
  };

  return (
    <BaseMenu
      w={{ base: '100%', md: 'auto' }}
      selectedOption={selectedOption}
      options={options}
      onItemSelect={(option) => updateProfitProjectToView(option.value as PondCardViewType)}
      data-cy='pond-view-selector'
    />
  );
}
