import { Flex } from '@chakra-ui/react';
import { useSetFarmBookRefererInRedux } from '@components/farm-book/hooks/use-set-farm-book-referer-in-redux';
import { FarmSelector } from '@screens/farm/components/farm-selector';
import { FarmDataUpdater } from '@screens/data-updater/components/farm-data-updater';

export function DataUpdaterScreen() {
  useSetFarmBookRefererInRedux('dataUpdater');

  return (
    <Flex direction='column' gap='md-alt' p='md' minH='100vh' data-cy='data-updater-screen'>
      <FarmSelector extraQuery={{ view: undefined }} />
      <FarmDataUpdater />
    </Flex>
  );
}
