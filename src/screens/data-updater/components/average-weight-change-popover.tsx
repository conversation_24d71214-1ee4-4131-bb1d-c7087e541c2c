import { Box, BoxProps, Flex, Text } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { formatNumber } from '@utils/number';
import { useAppSelector } from '@redux/hooks';
import { ContentCopyCheck } from '@icons/content-copy-check';
import { HoverCardArrow, HoverCardContent, HoverCardRoot, HoverCardTrigger } from '@components/ui/hover-card';
import { useState } from 'react';

type AverageWeightChangePopoverProps = {
  updatedBy: string;
  containerProps?: BoxProps;
  modelAverageWeight: number;
};

export function AverageWeightChangePopover(props: AverageWeightChangePopoverProps) {
  const { updatedBy, containerProps, modelAverageWeight } = props;

  const { trans } = getTrans();

  const lang = useAppSelector((state) => state.app.lang);
  const [open, setOpen] = useState(false);

  return (
    <HoverCardRoot
      open={open}
      onOpenChange={(value) => setOpen(value.open)}
      positioning={{ placement: 'bottom' }}
      lazyMount
      unmountOnExit
    >
      <HoverCardTrigger asChild>
        <Box
          onMouseOver={() => {
            setOpen(true);
          }}
          onMouseLeave={() => {
            setOpen(false);
          }}
          {...containerProps}
        >
          <ContentCopyCheck />
        </Box>
      </HoverCardTrigger>
      <HoverCardContent minW='164px' maxW='fit-content' py='sm-alt' px='md'>
        <HoverCardArrow />

        <Flex flexDir='column' gap='sm-alt' textAlign='left'>
          <Text size='label300' color='text.gray.weak'>
            {trans('t_kampi_weight_x_g', {
              averageWeight: formatNumber(modelAverageWeight, { lang, fractionDigits: 2 })
            })}
          </Text>
          {!!updatedBy && (
            <Text size='label300' color='text.gray.weak'>
              {trans('t_updated_by_name', { name: updatedBy })}
            </Text>
          )}
        </Flex>
      </HoverCardContent>
    </HoverCardRoot>
  );
}
