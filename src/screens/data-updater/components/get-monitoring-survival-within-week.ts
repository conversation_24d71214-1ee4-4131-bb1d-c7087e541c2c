import { Population } from '@xpertsea/module-farm-sdk';
import keyBy from 'lodash/keyBy';

export function getLastMonitoringWithinWeek(weekDays: string[], history: Population['history']) {
  let lastMonitoringDate = '';
  let isMonitoringWithinWeek = false;

  if (!weekDays?.length || !history) return { lastMonitoringDate, isMonitoringWithinWeek };

  const historyHashmap = keyBy(history, 'date');
  for (let i = weekDays.length; i >= 0; i--) {
    const date = weekDays[i];
    if (historyHashmap[date]) {
      isMonitoringWithinWeek = true;
      lastMonitoringDate = date;
      break;
    }
  }

  return { lastMonitoringDate, isMonitoringWithinWeek };
}

export function getLastSurvivalWithinWeek(weekDays: string[], survivalRate: Population['survivalRate']) {
  let lastSurvival = '';
  let survival = undefined;

  if (!survivalRate || !weekDays?.length) return { lastSurvival, survival };

  for (let i = weekDays.length; i >= 0; i--) {
    const date = weekDays[i];
    if (survivalRate[date]) {
      lastSurvival = date;
      survival = survivalRate[date];
      break;
    }
  }

  return { lastSurvival, survival };
}
