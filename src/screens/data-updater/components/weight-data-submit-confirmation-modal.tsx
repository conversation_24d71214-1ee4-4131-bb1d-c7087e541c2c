import { ReactNode, useMemo } from 'react';
import { BaseButton } from '@components/base/base-button';
import { Box, List, Text, useDisclosure } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON><PERSON>ooter, <PERSON>alog<PERSON>eader, DialogRoot, DialogTitle } from '@components/ui/dialog';

type WeightDataSubmitConfirmationModalProps = {
  children: ReactNode;
  onConfirm: () => void;
  dirtyFields: Partial<
    Readonly<{
      [p: string]: {
        [p: string]: boolean;
      };
    }>
  >;
};

export function WeightDataSubmitConfirmationModal(props: WeightDataSubmitConfirmationModalProps) {
  const { children, dirtyFields, onConfirm } = props;

  const { trans } = getTrans();
  const { open, onOpen, onClose } = useDisclosure();
  const currentFarmPonds = useAppSelector((state) => state.farm.currentFarmPonds);

  const pondNames = useMemo<string[]>(() => {
    if (!open) return;

    const data = [];
    for (const [populationId] of Object.entries(dirtyFields)) {
      const pond = currentFarmPonds?.find((pond) => pond.currentPopulationId === populationId);
      data.push(pond?.name);
    }
    return data;
  }, [open, dirtyFields, currentFarmPonds]);

  return (
    <DialogRoot
      open={open}
      onOpenChange={(e) => {
        e.open ? onOpen() : onClose();
      }}
      size='lg'
      restoreFocus={false}
      motionPreset='slide-in-top'
    >
      <Box onClick={onOpen}>{children}</Box>

      <DialogContent rounded='2xl' shadow='elevation.400' bgColor='bg.gray.medium'>
        <DialogHeader pt='lg' pb={0}>
          <DialogTitle textStyle='headline.300.heavy'> {trans('t_manual_weight_confirmation')}</DialogTitle>
        </DialogHeader>

        <DialogBody gap='md' py='2lg'>
          <Text size='label100' lineHeight='20px' color='text.gray.weak' mb='md-alt'>
            {trans('t_manual_weight_confirmation_description')}
          </Text>
          <Text size='label100' lineHeight='20px' color='text.gray.weak' mb='md-alt'>
            {trans('t_overwrite_weights')}
          </Text>
          {pondNames?.length > 0 && (
            <List.Root ms='lg'>
              {pondNames.map((pondName, index) => (
                <List.Item key={index} textStyle='label.100' lineHeight='24px' color='text.gray.weak'>
                  {pondName}
                </List.Item>
              ))}
            </List.Root>
          )}
        </DialogBody>
        <DialogFooter gap='sm-alt' pb='lg' pt={0}>
          <BaseButton w='fit-content' variant='secondary' onClick={onClose}>
            {trans('t_cancel')}
          </BaseButton>
          <BaseButton
            w='fit-content'
            onClick={() => {
              onConfirm();
              onClose();
            }}
          >
            {trans('t_confirm')}
          </BaseButton>
        </DialogFooter>
      </DialogContent>
    </DialogRoot>
  );
}
