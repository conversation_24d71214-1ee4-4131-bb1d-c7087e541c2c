import { Box, Flex, Text } from '@chakra-ui/react';
import { CurrentFarmPonds } from '@redux/farm/set-current-farm-ponds';
import { useFieldArray, useForm, useFormContext } from 'react-hook-form';
import { FormValues } from '@screens/data-updater/components/farm-data-updater';
import { DateTime } from 'luxon';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { useReloadCurrentFarmPonds } from '@screens/farm/hooks/use-reload-current-farm-ponds';
import { useBulkUpdatePopulation } from '@screens/group-summary/hooks/use-bulk-update-population';
import { HotTableComponent } from '@components/hot-table/hot-table-component';
import { SetStateAction, useEffect, useMemo } from 'react';
import { numberTransform, useYupSchema, Yup, yupFormResolver, YupResolverType } from '@utils/yup';
import { BaseButton } from '@components/base/base-button';
import keyBy from 'lodash/keyBy';
import { formatNumber, isNumber } from '@utils/number';
import type { CellChange, ChangeSource } from 'handsontable/common';
import { getDigitsFromString } from '@utils/string';
import { v1BulkUpdatePopulationType } from '@xpertsea/module-farm-sdk';
import { HotTableStylesWrapper } from '@components/hot-table/hot-table-styles-wrapper';
// @ts-expect-error waiting handson table update
// eslint-disable-next-line import/no-unresolved
import { CellProperties } from 'handsontable/settings';
import { EditOutlinedIcon } from '@icons/edit/edit-outlined-icon';
import { BaseIconButton } from '@components/base/base-icon-button';

type FormDataEntryType = {
  ponds: { name: string; survival: number; populationId: string; hasMonitor: boolean; estimated: boolean }[];
};

interface Props {
  ponds: CurrentFarmPonds;
  isEditing: boolean;
  setIsEditing: (value: SetStateAction<boolean>) => void;
}

export function SurvivalData(props: Props) {
  const { ponds, isEditing, setIsEditing } = props;
  const { watch: globalFormWatch } = useFormContext<FormValues>();

  const survivalDateToShow = globalFormWatch('survivalDateToShow');
  const selectedDate = DateTime.fromJSDate(survivalDateToShow).toFormat('yyyy-MM-dd');

  const { trans } = getTrans();
  const lang = useAppSelector((state) => state.app.lang);
  const { reloadCurrentFarmPonds } = useReloadCurrentFarmPonds();
  const [{ isLoading }, bulkUpdatePopulation] = useBulkUpdatePopulation();

  const { initialData, hasAtLeastOne, hasEstimatedSurvival } = useMemo(() => {
    if (!ponds.length) return;

    let hasAtLeastOne = false;
    let hasEstimatedSurvival = false;

    const initialData = ponds.map((pond) => {
      const { name, currentPopulation } = pond;
      const { _id: populationId, estimatedSurvival, history, survivalRate } = currentPopulation ?? {};

      let survival: number;
      let hasMonitor = false;
      let estimated = false;
      if (history?.length) {
        const survivalRateData = survivalRate?.[selectedDate];
        const monitoringData = history.find((ele) => ele.date === selectedDate);
        if (monitoringData) {
          hasMonitor = true;
          hasAtLeastOne = true;

          estimated = !!estimatedSurvival?.[monitoringData.date];
          hasEstimatedSurvival = hasEstimatedSurvival || estimated;
        }
        if (isNumber(survivalRateData)) {
          survival = survivalRateData;
        }
      }

      return {
        name,
        survival,
        populationId,
        hasMonitor,
        estimated
      };
    });

    return { initialData, hasAtLeastOne, hasEstimatedSurvival };
  }, [ponds, selectedDate]);

  const { schema } = useYupSchema({
    ponds: Yup.array().of(
      Yup.object().shape({
        survival: Yup.number()
          .min(0)
          .max(1, trans('t_field_should_be_less_or_equal_to_x', { field: trans('t_survival'), max: 90 }))
          .transform(numberTransform)
          .nullable()
          .label('t_survival')
      })
    )
  });

  const {
    control,
    handleSubmit,
    trigger,
    reset,
    formState: { errors }
  } = useForm<FormDataEntryType>({
    mode: 'onChange',
    resolver: yupFormResolver(schema) as YupResolverType<FormDataEntryType>,
    defaultValues: { ponds: [] }
  });

  const { fields: formFields, update: updateFormFields } = useFieldArray<FormDataEntryType>({
    control,
    name: 'ponds'
  });

  const gridData = useMemo(() => {
    return formFields?.map((row) => {
      const { name, survival, hasMonitor, estimated } = row;
      const survivalValue = survival ? formatNumber(survival, { fractionDigits: 0, lang, isPercentage: true }) : '-';
      const survivalString = hasMonitor ? survivalValue : trans('t_no_monitoring');

      return {
        pondName: name,
        survival: survivalString,
        hasMonitor,
        estimated
      };
    });
  }, [formFields, isEditing]);

  useEffect(() => {
    if (!initialData?.length) return;

    reset({ ponds: initialData });
  }, [JSON.stringify(initialData)]);

  const cellsErrors = useMemo(() => {
    const pondErrors = errors?.ponds;

    return Array.isArray(pondErrors)
      ? pondErrors.flatMap((error, index) => {
          if (!error?.survival) return [];

          return [null, error?.survival?.message]
            .flatMap((errorMsg, colIndex) => {
              if (!errorMsg) return { row: index, col: colIndex, valid: true };
              return {
                row: index,
                col: colIndex,
                valid: false,
                comment: {
                  value: errorMsg,
                  style: { width: 180, height: 70 },
                  readOnly: true
                }
              };
            })
            .filter((val) => !!val);
        })
      : [];
  }, [errors?.ponds]);

  const generateCellProperties = (row: number, col: number) => {
    const cellProps = {} as Pick<CellProperties, 'readOnly' | 'className' | 'comment'>;

    const hasMonitor = gridData[row]?.['hasMonitor'];
    const estimated = gridData[row]?.['estimated'];
    if (!hasMonitor) {
      cellProps.readOnly = true;
      cellProps.className = 'htReadyOnly';
    }
    if (col === 1 && estimated) {
      cellProps.comment = {
        value: trans('t_using_imputed_values'),
        readOnly: true,
        style: { width: 155, height: 50 }
      };
    }

    return cellProps;
  };

  const handleCancel = () => {
    reset({ ponds: initialData });
    setIsEditing(false);
  };

  const handleAfterChange = (changes: CellChange[], source: ChangeSource) => {
    if (source === 'loadData' || !changes?.length) return;
    changes.forEach((change) => {
      const [row, column, _, newValue] = change;
      if (column === 'survival') {
        const value = getDigitsFromString(newValue) / 100;
        updateFormFields(row, {
          ...formFields[row],
          survival: value || undefined
        });
      }
    });
    trigger('ponds').then();
  };

  const handleOnSubmit = handleSubmit(async () => {
    const initialHashmap = keyBy(initialData, 'populationId');

    const populations: v1BulkUpdatePopulationType[] = [];

    formFields.forEach(({ populationId, hasMonitor, survival }) => {
      const originalPondData = initialHashmap[populationId];
      if (hasMonitor && survival !== originalPondData.survival && survival) {
        populations.push({
          filter: { populationId },
          set: { survivalRate: { [selectedDate]: survival } }
        });
      }
    });

    if (!populations.length) return;
    bulkUpdatePopulation({
      params: { populations },
      successCallback: () => {
        reloadCurrentFarmPonds();

        setIsEditing(false);
      }
    });
  });

  return (
    <HotTableStylesWrapper
      flex={1}
      mt={{ base: 'xl', lg: '0' }}
      bg='white'
      rounded='2xl'
      p='md-alt'
      as='form'
      onSubmit={handleOnSubmit}
      pos='relative'
      css={{ '& .handsontable .htEdit': { backgroundColor: isEditing ? 'white' : 'gray.200' } }}
      data-cy='survival-data'
    >
      <Flex
        align='center'
        gap='md'
        justify='space-between'
        flexWrap='wrap'
        pos='absolute'
        top='-60px'
        insetEnd={0}
        data-cy='survival-data-actions'
      >
        {isEditing ? (
          <Flex gap='sm' mb='sm'>
            <BaseButton colorPalette='black' variant='ghost' onClick={handleCancel} data-cy='cancel-btn'>
              {trans('t_cancel')}
            </BaseButton>
            <BaseButton borderRadius='full' type='submit' loading={isLoading} data-cy='save-btn'>
              {trans('t_save_changes')}
            </BaseButton>
          </Flex>
        ) : (
          <BaseIconButton onClick={() => setIsEditing(true)} disabled={!hasAtLeastOne} data-cy='edit-survival-data-btn'>
            <EditOutlinedIcon />
          </BaseIconButton>
        )}
      </Flex>

      <Flex flexDir='column' bgColor='white' h='100%'>
        <Box mb='lg'>
          <Text size='label100' mb='xs'>
            {trans('t_survival')}
          </Text>
          <Text size='light300' color='text.gray.weak' data-cy='survival-data-table-message'>
            {trans('t_only_add_survival_when_a_monitoring_has_been_taken')}
          </Text>
        </Box>

        {hasEstimatedSurvival && (
          <Text size='label300' color='text.gray.disabled' mb='xs'>
            {trans('t_using_imputed_values')}
          </Text>
        )}

        <Box maxW='400px' h='100%' data-cy='survival-data-table'>
          <HotTableComponent
            height='100%'
            data={gridData}
            licenseKey='non-commercial-and-evaluation'
            contextMenu={{ items: { copy: {}, cut: {}, undo: {}, redo: {} } }}
            cells={generateCellProperties}
            comments
            afterChange={handleAfterChange}
            cell={cellsErrors}
            rowHeaders={false}
            columns={[
              { data: 'pondName', editor: false, readOnly: true, renderer: 'html' },
              { data: 'survival', readOnly: !isEditing, className: 'htEdit', editor: isEditing ? 'numeric' : false }
            ]}
            colHeaders={[trans('t_pond'), trans('t_survival')]}
          />
        </Box>
      </Flex>
    </HotTableStylesWrapper>
  );
}
