import { Icon } from '@chakra-ui/react';
import { FaCaretDown, FaCaretUp } from 'react-icons/fa';
import { MenuButton, MenuButtonProps, MenuContent, MenuItem, MenuRoot } from '@components/ui/menu';
import { useState } from 'react';

type WeekRangesMenuProps = {
  selectedDateRange: string;
  weekRanges: string[];
  shouldShowArrow?: boolean;
  onChange: (val: string) => void;
  buttonProps?: MenuButtonProps;
};

export function WeekRangesMenu(props: WeekRangesMenuProps) {
  const { selectedDateRange, weekRanges, buttonProps, shouldShowArrow = true, onChange } = props;
  const [open, setOpen] = useState(false);
  return (
    <MenuRoot onOpenChange={(e) => setOpen(e.open)} highlightedValue={selectedDateRange}>
      <MenuButton data-cy='weekly-range-selector-btn' {...buttonProps}>
        {selectedDateRange}
        {shouldShowArrow && (
          <>
            {open ? <Icon as={FaCaretUp} boxSize='16px' ms='md' /> : <Icon as={FaCaretDown} boxSize='16px' ms='md' />}
          </>
        )}
      </MenuButton>
      <MenuContent minWidth='260px' maxHeight='380px' overflow='scroll' data-cy='weekly-range-list'>
        {weekRanges.map((value) => (
          <MenuItem
            value={value}
            key={value}
            onClick={() => {
              onChange(value);
            }}
          >
            {value}
          </MenuItem>
        ))}
      </MenuContent>
    </MenuRoot>
  );
}
