import { Flex, Text } from '@chakra-ui/react';
import { SetStateAction, useEffect, useMemo } from 'react';
import { getTrans } from '@i18n/get-trans';
import { BaseButton } from '@components/base/base-button';
import { CurrentFarmPonds } from '@redux/farm/set-current-farm-ponds';
import { useYupSchema, Yup, yupFormResolver, YupResolverType } from '@utils/yup';
import { FieldArrayWithId, useFieldArray, useForm } from 'react-hook-form';
import { useBulkUpdatePopulation } from '@screens/group-summary/hooks/use-bulk-update-population';
import { useReloadCurrentFarmPonds } from '@screens/farm/hooks/use-reload-current-farm-ponds';
import { getBulkUpdatedPopulations } from '@screens/data-updater/helper/get-bulk-updated-populations';
import {
  useGetWeeklyTableUpdaterData,
  WeeklyFarmDataEntryType
} from '@screens/data-updater/hooks/use-get-weekly-table-updater-data';
import { getFeedDataForWeekDays } from '@screens/data-updater/helper/get-feed-data-for-week';
import { useAppSelector } from '@redux/hooks';
import { HotTableStylesWrapper } from '@components/hot-table/hot-table-styles-wrapper';
import { HotTableComponent } from '@components/hot-table/hot-table-component';
import { EditOutlinedIcon } from '@icons/edit/edit-outlined-icon';
import { BaseIconButton } from '@components/base/base-icon-button';

interface WeeklyDataTableUpdaterProps {
  isEditing: boolean;
  setIsEditing: (value: SetStateAction<boolean>) => void;
  ponds: CurrentFarmPonds;
  currentWeekDays: string[];
}

export function WeeklyDataTableUpdater(props: WeeklyDataTableUpdaterProps) {
  const { isEditing, setIsEditing, currentWeekDays, ponds } = props;
  const { trans } = getTrans();

  const [{ isLoading }, bulkUpdatePopulation] = useBulkUpdatePopulation();
  const { reloadCurrentFarmPonds } = useReloadCurrentFarmPonds();
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);

  const { initialData, hasEstimatedValues } = useMemo(() => {
    if (!ponds?.length) return;

    const { feedTypes } = currentFarm ?? {};
    const activeFeedTypes = feedTypes?.filter((ele) => ele.status !== 'inactive');

    let hasEstimatedValues = false;

    const initialData = ponds.map((pond) => {
      const { name: pondName, currentPopulation } = pond;
      const { feedData, _id: populationId, stockedAt, estimatedFeed } = currentPopulation ?? {};

      const { otherDirectCost, feedGiven } = getFeedDataForWeekDays(feedData, currentWeekDays, activeFeedTypes) ?? {};
      const isEstimatedFeed = currentWeekDays?.some((day) => !!estimatedFeed?.[day]);

      hasEstimatedValues = hasEstimatedValues || isEstimatedFeed;

      return {
        pondName,
        otherDirectCost,
        feedGiven,
        isEstimatedFeed,
        populationInfo: {
          populationId,
          feedData,
          originalOtherDirectCost: otherDirectCost,
          originalFeedGiven: feedGiven,
          stockedAt
        }
      };
    });

    return { initialData, hasEstimatedValues };
  }, [ponds, currentWeekDays, currentFarm]);

  const { schema } = useYupSchema({ ponds: Yup.array() });

  const { control, handleSubmit, reset } = useForm<WeeklyFarmDataEntryType>({
    mode: 'onChange',
    resolver: yupFormResolver(schema) as YupResolverType<WeeklyFarmDataEntryType>,
    defaultValues: { ponds: [] }
  });

  const { fields: formFields, update: updateFormFields } = useFieldArray<WeeklyFarmDataEntryType>({
    control,
    name: 'ponds'
  });

  useEffect(() => {
    if (!initialData?.length) return;
    reset({ ponds: initialData });
  }, [JSON.stringify(initialData)]);

  const { gridData, columns, cellHeaders, changedFields, setChangedFields, handleAfterChange, generateCellProperties } =
    useGetWeeklyTableUpdaterData({
      isEditing,
      formFields: formFields as FieldArrayWithId<WeeklyFarmDataEntryType, 'ponds'>[],
      updateFormFields
    });

  const handleCancel = () => {
    reset({ ponds: initialData });
    setIsEditing(false);
    setChangedFields({});
  };

  const handleOnSubmit = handleSubmit(async () => {
    const bulkPopulationData = Object.values(changedFields);

    const populations = getBulkUpdatedPopulations(bulkPopulationData, currentWeekDays);
    if (!populations?.length) {
      setIsEditing(false);
      return;
    }
    bulkUpdatePopulation({
      params: { populations },
      successCallback: () => {
        reloadCurrentFarmPonds();

        setIsEditing(false);
      }
    });
  });

  return (
    <Flex flex={1} direction='column' gap='sm-alt' mt={{ base: 'xl', lg: '0' }}>
      {hasEstimatedValues && (
        <Text size='label300' color='text.gray.disabled'>
          {trans('t_using_imputed_values')}
        </Text>
      )}
      <HotTableStylesWrapper
        flex={1}
        as='form'
        onSubmit={handleOnSubmit}
        pos='relative'
        css={{ '& .handsontable .htEdit': { backgroundColor: isEditing ? 'white' : 'gray.200' } }}
        data-cy='weekly-data-table'
      >
        <Flex
          align='center'
          gap='md'
          justify='space-between'
          flexWrap='wrap'
          pos='absolute'
          top={hasEstimatedValues ? '-82px' : '-60px'}
          insetEnd={0}
        >
          {isEditing ? (
            <Flex gap='sm' data-cy='weekly-data-table-actions'>
              <BaseButton colorPalette='black' variant='ghost' onClick={handleCancel} data-cy='cancel-btn'>
                {trans('t_cancel')}
              </BaseButton>
              <BaseButton borderRadius='full' type='submit' loading={isLoading} data-cy='save-btn'>
                {trans('t_save_changes')}
              </BaseButton>
            </Flex>
          ) : (
            <BaseIconButton onClick={() => setIsEditing(true)} data-cy='edit-weekly-data'>
              <EditOutlinedIcon />
            </BaseIconButton>
          )}
        </Flex>

        <HotTableComponent
          data={gridData}
          height='calc(100% - 53px)'
          licenseKey='non-commercial-and-evaluation'
          contextMenu={{ items: { copy: {}, cut: {}, undo: {}, redo: {} } }}
          cells={generateCellProperties}
          comments
          afterChange={handleAfterChange}
          rowHeaders={false}
          columns={columns}
          colHeaders={cellHeaders}
        />
      </HotTableStylesWrapper>
    </Flex>
  );
}
