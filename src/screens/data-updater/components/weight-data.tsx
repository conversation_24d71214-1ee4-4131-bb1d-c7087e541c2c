import { getTrans } from '@i18n/get-trans';
import { Box, Flex, Grid, GridItem, Text } from '@chakra-ui/react';
import { useAppSelector } from '@redux/hooks';
import { formatNumber, isEqualNumber } from '@utils/number';
import { BaseButton } from '@components/base/base-button';
import { useState } from 'react';
import get from 'lodash/get';
import { useForm, useFormContext } from 'react-hook-form';
import { ArrowReverseDuotoneIcon } from '@icons/arrow-reverse/arrow-reverse-duotone-icon';
import { WeightDataSubmitConfirmationModal } from '@screens/data-updater/components/weight-data-submit-confirmation-modal';
import { useBulkUpdatePopulation } from '@screens/group-summary/hooks/use-bulk-update-population';
import { useReloadCurrentFarmPonds } from '@screens/farm/hooks/use-reload-current-farm-ponds';
import { DateTime } from 'luxon';
import { FormValues } from '@screens/data-updater/components/farm-data-updater';
import has from 'lodash/has';
import { CurrentFarmPonds } from '@redux/farm/set-current-farm-ponds';
import isNull from 'lodash/isNull';
import { AverageWeightChangePopover } from '@screens/data-updater/components/average-weight-change-popover';
import { useRouter } from 'next/router';
import { BaseFormNumberInput } from '@components/form/base-form-number-input';
import { NumberFormatValues } from 'react-number-format';
import { EditOutlinedIcon } from '@icons/edit/edit-outlined-icon';
import { BaseIconButton } from '@components/base/base-icon-button';

export type BulkUpdateManualWeightsFormType = {
  [populationId: string]: { [date: string]: number };
};

type WeightDataProps = {
  ponds: CurrentFarmPonds;
};

export function WeightData(props: WeightDataProps) {
  const { ponds } = props;

  const { query } = useRouter();
  const { pondEid } = query as { pondEid: string };
  const pondEidNumber = Number(pondEid?.split('-')[0]);

  const { watch: globalFormWatch } = useFormContext<FormValues>();

  const monitoringDateToShowWatch = globalFormWatch('monitoringDateToShow');
  const monitoringDateToShow = DateTime.fromJSDate(monitoringDateToShowWatch).toFormat('yyyy-MM-dd');

  const { trans } = getTrans();
  const [isEditing, setIsEditing] = useState<boolean>();

  const lang = useAppSelector((state) => state.app.lang);

  const { reloadCurrentFarmPonds } = useReloadCurrentFarmPonds();
  const [{ isLoading }, bulkUpdatePopulation] = useBulkUpdatePopulation();
  //todo @maradona Refactor this component to use only 1 list instead of two loops.
  const {
    watch,
    reset,
    setValue,
    setError,
    clearErrors,
    handleSubmit,
    control,
    formState: { isDirty, errors, dirtyFields }
  } = useForm<BulkUpdateManualWeightsFormType>({ defaultValues: {} });

  const manualWeightsDataWatch = watch();

  const onSubmit = handleSubmit((data) => {
    const onlyChangedFields = Object.keys(data).filter((item) => !!dirtyFields?.[item]);
    const transformedData = onlyChangedFields.map((populationId) => {
      const manualAverageWeights = Object.keys(data[populationId]).map((date) => ({
        date: date,
        averageWeight: data[populationId][date]
      }));

      return {
        filter: { populationId: populationId },
        set: { manualAverageWeights: manualAverageWeights }
      };
    });

    bulkUpdatePopulation({
      params: { populations: transformedData },
      successCallback() {
        reloadCurrentFarmPonds();
        setIsEditing(false);
      }
    });
  });

  return (
    <Flex pos='relative' flexDir='column' mt={{ base: 'xl', lg: '0' }}>
      <Flex pos='absolute' right={0} gap='sm-alt' top='-60px' data-cy='weight-data-actions'>
        {!isEditing && (
          <BaseIconButton onClick={() => setIsEditing(true)} data-cy='edit-weight-data-btn'>
            <EditOutlinedIcon />
          </BaseIconButton>
        )}
        {isEditing && (
          <>
            <BaseButton
              variant='secondary'
              colorPalette='black'
              onClick={() => {
                reset();
                setIsEditing(false);
              }}
              data-cy='cancel-btn'
            >
              {trans('t_cancel')}
            </BaseButton>
            <WeightDataSubmitConfirmationModal onConfirm={onSubmit} dirtyFields={dirtyFields}>
              <BaseButton loading={isLoading} disabled={!isDirty || !!Object.keys(errors).length} data-cy='save-btn'>
                {trans('t_save_changes')}
              </BaseButton>
            </WeightDataSubmitConfirmationModal>
          </>
        )}
      </Flex>
      <Flex flexDir='column' gap='md-alt' bgColor='white' p='md' rounded='2xl' data-cy='weight-data-table'>
        <Flex align='center' gap='md-alt'>
          <Text size='label100'>{trans('t_abw_g')}</Text>
          <Text size='light300' color='text.gray.weak' data-cy='weight-data-table-message'>
            {trans('t_only_override_weights_when_a_monitoring_has_been_taken')}
          </Text>
        </Flex>
        <Flex flexDir='column' p='md'>
          <Grid templateColumns='130px 180px' gap='sm-alt'>
            <GridItem
              h='40px'
              display='flex'
              rounded='xl'
              alignItems='center'
              justifyContent='center'
              bgColor='gray.100'
            >
              <Text size='label200'>{trans('t_pond')}</Text>
            </GridItem>
            <GridItem
              h='40px'
              display='flex'
              rounded='xl'
              justifyContent='center'
              bgColor='gray.100'
              alignItems='center'
            >
              <Text size='label200'>{trans('t_abw_g')}</Text>
            </GridItem>
          </Grid>
          <Box
            maxH='calc(100vh - 290px)'
            overflowY='auto'
            maxW='fit-content'
            overflowX='hidden'
            data-cy='ponds-weight-data'
          >
            {!isEditing &&
              ponds?.map((pond, pondIndex) => {
                const { _id: pondId, name: pondName, currentPopulation, currentPopulationId } = pond;
                const { history, manualAverageWeights } = currentPopulation ?? {};

                const historyItem = history?.find((historyItem) => historyItem.date === monitoringDateToShow);
                const manualAverageWeight = manualAverageWeights?.find(
                  (manualAverageWeight) => manualAverageWeight.date === monitoringDateToShow
                );
                const averageWeight = manualAverageWeight?.averageWeight ?? historyItem?.modelAverageWeight;

                const hasPopulation = !!currentPopulationId;
                const hasMonitoring = !!historyItem?.averageWeight;
                const isLastItem = pondIndex === ponds.length - 1;

                const isManualAndKampiAverageWeightsEqual = isEqualNumber({
                  fractionDigits: 2,
                  num2: averageWeight,
                  num1: historyItem?.modelAverageWeight
                });

                return (
                  <Grid
                    h='40px'
                    gap='sm-alt'
                    key={pondId}
                    borderBottom='0.5px solid'
                    borderBottomColor='gray.200'
                    templateColumns='130px 180px'
                    {...(isLastItem && { borderBottom: 'none' })}
                    {...(pondEidNumber && pondEidNumber === pond?.eid && { bg: 'yellow.200' })}
                    data-cy={`pond-${pond._id}`}
                  >
                    <GridItem display='flex' alignItems='center' justifyContent='center'>
                      <Text size='label200' data-cy='pond-name'>
                        {pondName}
                      </Text>
                    </GridItem>
                    <GridItem
                      display='flex'
                      alignItems='center'
                      justifyContent='center'
                      gap='2xs'
                      pos='relative'
                      data-cy='pond-average-weight'
                    >
                      {hasPopulation && hasMonitoring && (
                        <>
                          {!isManualAndKampiAverageWeightsEqual && (
                            <AverageWeightChangePopover
                              updatedBy={manualAverageWeight?.updatedBy}
                              containerProps={{ position: 'absolute', left: 43 }}
                              modelAverageWeight={historyItem?.modelAverageWeight}
                            />
                          )}
                          {!!averageWeight && (
                            <Text size='label200' data-cy='pond-average-weight-value'>
                              {formatNumber(averageWeight, { lang, fractionDigits: 2 })}
                            </Text>
                          )}
                        </>
                      )}
                      {hasPopulation && !hasMonitoring && (
                        <Text size='label200' data-cy='pond-has-no-monitoring'>
                          {trans('t_no_monitoring')}
                        </Text>
                      )}
                      {!hasPopulation && (
                        <Text size='label200' data-cy='pond-has-no-population'>
                          -
                        </Text>
                      )}
                    </GridItem>
                  </Grid>
                );
              })}
            {isEditing &&
              ponds?.map((pond, index) => {
                const { name: pondName, currentPopulation, currentPopulationId } = pond ?? {};
                const { history, manualAverageWeights } = currentPopulation ?? {};

                const historyItem = history?.find((historyItem) => historyItem.date === monitoringDateToShow);
                const manualAverageWeight = manualAverageWeights?.find(
                  (manualAverageWeight) => manualAverageWeight.date === monitoringDateToShow
                );

                const isLastItem = index === ponds.length - 1;

                const hasPopulation = !!currentPopulationId;
                const hasMonitoring = !!historyItem?.averageWeight;
                const decimalSeparator = (1.1).toLocaleString(lang).substring(1, 2);

                const fieldKey: `${string}.${string}` = `${currentPopulationId}.${monitoringDateToShow}`;
                const fieldValue = get(manualWeightsDataWatch, fieldKey) as unknown as number;

                const isFieldExistInsideFormData = has(manualWeightsDataWatch, fieldKey);

                const defaultValue = manualAverageWeight?.averageWeight ?? historyItem?.modelAverageWeight;
                const isManualAndKampiAverageWeightsEqual = isEqualNumber({
                  fractionDigits: 2,
                  num1: historyItem?.modelAverageWeight,
                  num2: fieldValue ?? defaultValue
                });

                const fieldError = get(errors, fieldKey);

                const maxManualAverageWeight = historyItem?.modelAverageWeight + 7;

                let finalFieldValue;

                // Value is not yet inside the form data
                if (!isFieldExistInsideFormData) {
                  finalFieldValue = manualAverageWeight?.averageWeight ?? historyItem?.modelAverageWeight;
                }

                // User updated form data with custom value
                if (isFieldExistInsideFormData && !isNull(fieldValue)) {
                  finalFieldValue = fieldValue;
                }

                // User reverted to model average weight
                if (isFieldExistInsideFormData && isNull(fieldValue)) {
                  finalFieldValue = historyItem?.modelAverageWeight;
                }

                return (
                  <Grid
                    h={fieldError?.message ? '72px' : '60px'}
                    gap='sm-alt'
                    key={pond._id}
                    borderBottom='0.5px solid'
                    borderBottomColor='gray.200'
                    templateColumns='130px 390px'
                    {...(isLastItem && { borderBottom: 'none' })}
                    {...(pondEidNumber && pondEidNumber === pond?.eid && { bg: 'yellow.200' })}
                    data-cy={`pond-${pond._id}`}
                  >
                    <GridItem display='flex' alignItems='center' justifyContent='center'>
                      <Text size='label200' data-cy='pond-name'>
                        {pondName}
                      </Text>
                    </GridItem>
                    <GridItem
                      display='flex'
                      flexDirection='column'
                      alignSelf='center'
                      gap='2xs'
                      data-cy='pond-average-weight'
                    >
                      {!hasPopulation && (
                        <Text size='label200' w='180px' textAlign='center' data-cy='pond-has-no-population'>
                          -
                        </Text>
                      )}
                      {hasPopulation && !hasMonitoring && (
                        <Text size='label200' w='180px' textAlign='center' data-cy='pond-has-no-monitoring'>
                          {trans('t_no_monitoring')}
                        </Text>
                      )}
                      {hasPopulation && hasMonitoring && (
                        <>
                          <Flex align='center' justify='space-between'>
                            <Flex align='center' gap='2xs' ms='28px'>
                              <BaseFormNumberInput
                                w='124px'
                                textAlign='center'
                                name={fieldKey}
                                id={fieldKey}
                                control={control}
                                numericFormatProps={{
                                  decimalScale: 2,
                                  allowNegative: false,
                                  fixedDecimalScale: false,
                                  decimalSeparator,
                                  thousandSeparator: decimalSeparator === '.' ? ',' : '.',
                                  value: finalFieldValue,
                                  onValueChange: (data: NumberFormatValues) => {
                                    const newValue = data.floatValue;

                                    if (isNull(fieldValue)) return;

                                    setValue(fieldKey, newValue, { shouldDirty: true });
                                  },
                                  onBlur: () => {
                                    // We want the validation to be triggered only when the
                                    // input focus is lost
                                    if (fieldValue > maxManualAverageWeight) {
                                      setError(fieldKey, { message: trans('t_invalid_average_weight') });
                                    } else {
                                      clearErrors(fieldKey);
                                    }
                                  }
                                }}
                                invalid={!!fieldError?.message}
                                data-cy='pond-edit-manual-weight-input'
                              />
                              {!isManualAndKampiAverageWeightsEqual && !isNull(fieldValue) && (
                                <AverageWeightChangePopover
                                  updatedBy={manualAverageWeight?.updatedBy}
                                  modelAverageWeight={historyItem?.modelAverageWeight}
                                />
                              )}
                            </Flex>
                            {!isManualAndKampiAverageWeightsEqual && !isNull(fieldValue) && (
                              <BaseButton
                                me='2xs'
                                size='sm'
                                variant='ghost'
                                lineHeight='12px'
                                color='text.gray'
                                onClick={() => {
                                  clearErrors(fieldKey);
                                  setValue(fieldKey, null, { shouldDirty: true });
                                }}
                                data-cy='revert-to-kampi-weight_btn'
                              >
                                <ArrowReverseDuotoneIcon w='16px' h='16px' /> {trans('t_revert_to_kampi_weight')}
                              </BaseButton>
                            )}
                          </Flex>
                          {!!fieldError?.message && (
                            <Text ms='28px' size='label300' color='semanticRed.600' data-cy='average-weight-error'>
                              {fieldError?.message?.toString()}
                            </Text>
                          )}
                        </>
                      )}
                    </GridItem>
                  </Grid>
                );
              })}
          </Box>
        </Flex>
      </Flex>
    </Flex>
  );
}
