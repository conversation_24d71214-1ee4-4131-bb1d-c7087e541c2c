import { CurrentFarmPonds } from '@redux/farm/set-current-farm-ponds';
import { SetStateAction, useEffect, useMemo } from 'react';
import { FormValues } from './farm-data-updater';
import { FieldArrayWithId, useFieldArray, useForm, useFormContext } from 'react-hook-form';
import { Flex, Text } from '@chakra-ui/react';
import { DateTime } from 'luxon';
import { getTrans } from '@i18n/get-trans';
import { useBulkUpdatePopulation } from '@screens/group-summary/hooks/use-bulk-update-population';
import { useReloadCurrentFarmPonds } from '@screens/farm/hooks/use-reload-current-farm-ponds';
import { useAppSelector } from '@redux/hooks';
import { useYupSchema, Yup, yupFormResolver, YupResolverType } from '@utils/yup';
import {
  DailyFarmDataEntryType,
  DailyTableDataType,
  useGetDailyTableUpdaterData
} from '../hooks/use-get-daily-table-updater';
import { BaseButton } from '@components/base/base-button';
import { HotTableComponent } from '@components/hot-table/hot-table-component';
import { HotTableStylesWrapper } from '@components/hot-table/hot-table-styles-wrapper';
import { getFeedDataForDay } from '@screens/data-updater/helper/get-feed-data-for-week';
import { getBulkUpdatedPopulationsDailyBased } from '@screens/data-updater/helper/get-bulk-updated-population-daily-based';
import { EditOutlinedIcon } from '@icons/edit/edit-outlined-icon';
import { BaseIconButton } from '@components/base/base-icon-button';

interface Props {
  isEditing: boolean;
  setIsEditing: (value: SetStateAction<boolean>) => void;
  ponds: CurrentFarmPonds;
}

export function DailyDataTableUpdater(props: Props) {
  const { isEditing, setIsEditing, ponds } = props;
  const { trans } = getTrans();

  const [{ isLoading }, bulkUpdatePopulation] = useBulkUpdatePopulation();
  const { reloadCurrentFarmPonds } = useReloadCurrentFarmPonds();
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);

  const { watch: globalFormWatch } = useFormContext<FormValues>();
  const dailyDataDateToShowWatch = globalFormWatch('dailyDataDateToShow');
  const dailyDataDateToShowFormatted = DateTime.fromJSDate(dailyDataDateToShowWatch).toFormat('yyyy-MM-dd');

  const { initialData, estimatedValueCounter } = useMemo(() => {
    if (!ponds?.length) return;

    const { feedTypes } = currentFarm ?? {};

    let estimatedValueCounter = 0;

    const initialData: DailyTableDataType = ponds.map((pond) => {
      const { name, currentPopulation } = pond;

      const { feedData, _id: populationId, stockedAt, estimatedFeed } = currentPopulation ?? {};

      const activeFeedTypes = feedTypes?.filter((ele) => ele.status !== 'inactive');

      const { feedGiven, otherDirectCost } =
        getFeedDataForDay({ feedData, day: dailyDataDateToShowFormatted, feedTypes: activeFeedTypes }) ?? {};

      const isEstimatedFeed = !!estimatedFeed?.[dailyDataDateToShowFormatted];
      if (isEstimatedFeed) {
        estimatedValueCounter++;
      }

      return {
        pondName: name,
        otherDirectCost,
        feedGiven,
        isEstimatedFeed,
        populationInfo: {
          populationId,
          feedData,
          originalOtherDirectCost: otherDirectCost,
          originalFeedGiven: feedGiven,
          stockedAt
        }
      };
    });

    return { initialData, estimatedValueCounter };
  }, [ponds, currentFarm, dailyDataDateToShowFormatted]);

  const { schema } = useYupSchema({
    ponds: Yup.array()
  });

  const { control, handleSubmit, trigger, reset } = useForm<DailyFarmDataEntryType>({
    mode: 'onChange',
    resolver: yupFormResolver(schema) as YupResolverType<DailyFarmDataEntryType>,
    defaultValues: { ponds: [] }
  });

  const { fields: formFields, update: updateFormFields } = useFieldArray<DailyFarmDataEntryType>({
    control,
    name: 'ponds'
  });

  useEffect(() => {
    if (!initialData?.length) return;
    reset({ ponds: initialData });
  }, [JSON.stringify(initialData)]);

  const { cellHeaders, changedFields, columns, generateCellProperties, gridData, handleAfterChange, setChangedFields } =
    useGetDailyTableUpdaterData({
      isEditing,
      updateFormFields,
      trigger,
      formFields: formFields as FieldArrayWithId<DailyFarmDataEntryType, 'ponds'>[],
      selectedDay: dailyDataDateToShowFormatted
    });

  const handleCancel = () => {
    reset({ ponds: initialData });
    setIsEditing(false);
    setChangedFields({});
  };

  const handleOnSubmit = handleSubmit(async () => {
    const bulkPopulationData = Object.values(changedFields);

    const populations = getBulkUpdatedPopulationsDailyBased(bulkPopulationData, dailyDataDateToShowFormatted);
    if (!populations?.length) {
      setIsEditing(false);
      return;
    }
    bulkUpdatePopulation({
      params: { populations },
      successCallback: () => {
        reloadCurrentFarmPonds();

        setIsEditing(false);
      }
    });
  });
  const hasEstimatedValues = estimatedValueCounter > 0;
  return (
    <Flex flex={1} direction='column' gap='sm-alt' mt={{ base: 'xl', lg: '0' }}>
      {hasEstimatedValues && (
        <Text size='label300' color='text.gray.disabled'>
          {trans('t_using_imputed_values')}
        </Text>
      )}

      <HotTableStylesWrapper
        flex={1}
        as='form'
        onSubmit={handleOnSubmit}
        pos='relative'
        css={{ '& .handsontable .htEdit': { backgroundColor: isEditing ? 'white' : 'gray.200' } }}
        data-cy='daily-data-table'
      >
        <Flex
          align='center'
          gap='md'
          justify='space-between'
          flexWrap='wrap'
          pos='absolute'
          top={hasEstimatedValues ? '-82px' : '-60px'}
          insetEnd={0}
        >
          {isEditing ? (
            <Flex gap='sm' mb='sm' data-cy='daily-data-table-actions'>
              <BaseButton colorPalette='black' variant='ghost' onClick={handleCancel} data-cy='cancel-btn'>
                {trans('t_cancel')}
              </BaseButton>
              <BaseButton type='submit' loading={isLoading} data-cy='save-btn'>
                {trans('t_save_changes')}
              </BaseButton>
            </Flex>
          ) : (
            <BaseIconButton onClick={() => setIsEditing(true)} data-cy='edit-daily-data-btn'>
              <EditOutlinedIcon />
            </BaseIconButton>
          )}
        </Flex>

        <HotTableComponent
          data={gridData}
          height='calc(100% - 53px)'
          licenseKey='non-commercial-and-evaluation'
          contextMenu={{ items: { copy: {}, cut: {}, undo: {}, redo: {} } }}
          cells={generateCellProperties}
          comments
          afterChange={handleAfterChange}
          rowHeaders={false}
          columns={columns}
          colHeaders={cellHeaders}
        />
      </HotTableStylesWrapper>
    </Flex>
  );
}
