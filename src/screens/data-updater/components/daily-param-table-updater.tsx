import { Flex } from '@chakra-ui/react';
import { SetStateAction, useEffect, useMemo } from 'react';
import { HotTableComponent } from '@components/hot-table/hot-table-component';
import { getTrans } from '@i18n/get-trans';
import { BaseButton } from '@components/base/base-button';
import { CurrentFarmPonds } from '@redux/farm/set-current-farm-ponds';
import { numberTransform, useYupSchema, Yup, yupFormResolver, YupResolverType } from '@utils/yup';
import { FieldArrayWithId, useFieldArray, useForm, useFormContext } from 'react-hook-form';
import { useBulkUpdatePopulation } from '@screens/group-summary/hooks/use-bulk-update-population';
import { useReloadCurrentFarmPonds } from '@screens/farm/hooks/use-reload-current-farm-ponds';
import {
  DailyParamTableEntryType,
  useGetDailyParamTableUpdaterData
} from '@screens/data-updater/hooks/use-get-daily-param-table-updater-data';
import { useAppSelector } from '@redux/hooks';
import { FormValues } from '@screens/data-updater/components/farm-data-updater';
import { DateTime } from 'luxon';
import { v1BulkUpdatePopulationType } from '@xpertsea/module-farm-sdk';
import { HotTableStylesWrapper } from '@components/hot-table/hot-table-styles-wrapper';
import { EditOutlinedIcon } from '@icons/edit/edit-outlined-icon';
import { BaseIconButton } from '@components/base/base-icon-button';

interface Props {
  isEditing: boolean;
  setIsEditing: (value: SetStateAction<boolean>) => void;
  ponds: CurrentFarmPonds;
}

export function DailyParamTableUpdater(props: Props) {
  const { isEditing, setIsEditing, ponds } = props;
  const { trans } = getTrans();
  const { watch: globalFormWatch } = useFormContext<FormValues>();
  const dailyParameterDateToShowWatch = globalFormWatch('dailyParamDateToShow');
  const dailyParameterDateToShowFormatted = DateTime.fromJSDate(dailyParameterDateToShowWatch).toFormat('yyyy-MM-dd');

  const currentFarm = useAppSelector((state) => state.farm.currentFarm);

  const [{ isLoading }, bulkUpdatePopulation] = useBulkUpdatePopulation();
  const { reloadCurrentFarmPonds } = useReloadCurrentFarmPonds();

  const initialData = useMemo(() => {
    if (!ponds?.length) return;

    return ponds.map((pond) => {
      const { name, currentPopulation } = pond;
      const {
        stockedAt,
        stockedAtTimezone,
        _id: populationId,
        dailyParameters: populationDailyParameters
      } = currentPopulation ?? {};

      const stockedAtLuxon = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone }).startOf('day');
      const currentDailyParamDateLuxon = DateTime.fromISO(dailyParameterDateToShowFormatted).startOf('day');
      const isSelectedDateBiggerThanStockingDate = stockedAtLuxon.isValid
        ? currentDailyParamDateLuxon.diff(stockedAtLuxon, 'days').days >= 0
        : false;
      const dailyParametersParamsOnSelectedDay = populationDailyParameters?.find((param) => {
        return param.date === dailyParameterDateToShowFormatted;
      })?.params;

      return {
        pondName: name,
        dailyParametersParams: dailyParametersParamsOnSelectedDay,
        isSelectedDateBiggerThanStockingDate,
        populationId
      };
    });
  }, [ponds, currentFarm, dailyParameterDateToShowFormatted]);

  const { schema } = useYupSchema({
    ponds: Yup.array().of(
      Yup.object().shape({
        survival: Yup.number()
          .max(1, trans('t_field_should_be_less_or_equal_to_x', { field: trans('t_survival'), max: 90 }))
          .transform(numberTransform)
          .nullable()
          .label('t_survival')
      })
    )
  });

  const {
    control,
    handleSubmit,
    formState: { errors },
    trigger,
    reset
  } = useForm<DailyParamTableEntryType>({
    mode: 'onChange',
    resolver: yupFormResolver(schema) as YupResolverType<DailyParamTableEntryType>,
    defaultValues: { ponds: [] }
  });

  const { fields: formFields, update: updateFormFields } = useFieldArray<DailyParamTableEntryType>({
    control,
    name: 'ponds'
  });

  useEffect(() => {
    if (!initialData?.length) return;
    reset({ ponds: initialData });
  }, [JSON.stringify(initialData)]);

  const { gridData, columns, cellHeaders, changedFields, setChangedFields, handleAfterChange, generateCellProperties } =
    useGetDailyParamTableUpdaterData({
      isEditing,
      formFields: formFields as FieldArrayWithId<DailyParamTableEntryType, 'ponds'>[],
      errors,
      updateFormFields,
      trigger
    });

  const handleCancel = () => {
    reset({ ponds: initialData });
    setIsEditing(false);
    setChangedFields({});
  };

  const handleOnSubmit = handleSubmit(async () => {
    const bulkPopulationData = Object.values(changedFields);
    const bulkPopulationRequest: v1BulkUpdatePopulationType[] = bulkPopulationData.map((data) => {
      const { populationId, dailyParametersParams } = data;
      return {
        filter: { populationId },
        set: {
          dailyParameters: [
            {
              date: dailyParameterDateToShowFormatted,
              params: dailyParametersParams
            }
          ]
        }
      };
    });

    bulkUpdatePopulation({
      params: { populations: bulkPopulationRequest },
      successCallback: () => {
        reloadCurrentFarmPonds();
        setIsEditing(false);
      }
    });
  });

  return (
    <HotTableStylesWrapper
      flex={1}
      as='form'
      mt={{ base: 'xl', lg: '0' }}
      onSubmit={handleOnSubmit}
      pos='relative'
      css={{ '& .handsontable .htEdit': { backgroundColor: isEditing ? 'white' : 'gray.200' } }}
      data-cy='daily-param-table'
    >
      <Flex align='center' gap='md' justify='space-between' flexWrap='wrap' pos='absolute' top='-60px' insetEnd={0}>
        {isEditing ? (
          <Flex gap='sm' mb='sm' data-cy='daily-params-actions'>
            <BaseButton colorPalette='black' variant='ghost' onClick={handleCancel} data-cy='cancel-btn'>
              {trans('t_cancel')}
            </BaseButton>
            <BaseButton borderRadius='full' type='submit' loading={isLoading} data-cy='save-btn'>
              {trans('t_save_changes')}
            </BaseButton>
          </Flex>
        ) : (
          <BaseIconButton onClick={() => setIsEditing(true)} data-cy='edit-daily-params-btn'>
            <EditOutlinedIcon />
          </BaseIconButton>
        )}
      </Flex>
      <HotTableComponent
        data={gridData}
        height='calc(100% - 53px)'
        licenseKey='non-commercial-and-evaluation'
        contextMenu={{ items: { copy: {}, cut: {}, undo: {}, redo: {} } }}
        cells={generateCellProperties}
        comments
        afterChange={handleAfterChange}
        rowHeaders={false}
        columns={columns}
        colHeaders={cellHeaders}
      />
    </HotTableStylesWrapper>
  );
}
