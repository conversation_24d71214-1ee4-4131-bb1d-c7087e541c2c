import { Box, Flex, FlexProps, SimpleGrid, Skeleton } from '@chakra-ui/react';
import { useAppSelector } from '@redux/hooks';
import { SetStateAction, useEffect, useMemo, useState } from 'react';
import { sortArray, sortCompareString } from '@utils/sort-array';
import get from 'lodash/get';
import { SomethingWentWrong } from '@components/errors/something-went-wrong';
import { NoPondsInYourFarmCard } from '@screens/group-summary/components/no-ponds-in-your-farm-card';
import { CurrentFarmPonds } from '@redux/farm/set-current-farm-ponds';
import { useWeekDates } from '@screens/data-updater/helper/use-week-dates';
import { WeekRangesMenu } from '@screens/data-updater/components/week-range-menu';
import { EditDateRangeModal } from '@screens/data-updater/components/edit-date-range-modal';
import { WeeklyDataTableUpdater } from '@screens/data-updater/components/weekly-data-table-updater';
import { getTrans } from '@i18n/get-trans';
import { useAnalytics } from '@hooks/use-analytics';
import { actionsName } from '@utils/segment';
import { BaseMenu } from '@components/base/base-menu';
import { useRouter } from 'next/router';
import { goToUrl } from '@utils/url';
import { WeightData } from '@screens/data-updater/components/weight-data';
import { FormControlDateInput } from '@components/form/form-control-date-input';
import { FormProvider, useForm, useFormContext } from 'react-hook-form';
import { DateTime } from 'luxon';
import { ArrowBigLeftFilled } from '@icons/arrow-big-left/arrow-big-left-filled';
import { ArrowBigRightFilled } from '@icons/arrow-big-right/arrow-big-right-filled';
import { DailyParamTableUpdater } from '@screens/data-updater/components/daily-param-table-updater';
import { DailyDataTableUpdater } from './daily-data-table-updater';
import { SurvivalData } from './survival-data';
import { DataUpdaterLinks } from '@screens/data-updater/components/data-updater-links';
import { VisionOnlyGuard } from '@components/permission-wrappers/vision-only-guard';

export function FarmDataUpdater() {
  const { currentFarmPonds, isLoadingCurrentFarmPonds, isFinishedOnceLoadingCurrentFarmPonds, currentFarmPondsError } =
    useAppSelector((state) => state.farm);

  const pondsGridData = useMemo(() => {
    if (!currentFarmPonds?.length) return;

    return sortArray({
      list: currentFarmPonds?.filter((pond) => !!pond?.name),
      sortDir: 'asc',
      compareFunction: (a, b) => {
        const firstItemValue = get(a, 'name');
        const secondItemValue = get(b, 'name');
        return sortCompareString(firstItemValue, secondItemValue);
      }
    });
  }, [currentFarmPonds]);

  const isLoading = isLoadingCurrentFarmPonds || !isFinishedOnceLoadingCurrentFarmPonds;

  if (isLoading) return <TableLoadingSkeleton />;

  if (currentFarmPondsError) return <SomethingWentWrong />;

  if (!pondsGridData) return <NoPondsInYourFarmCard />;

  return (
    <Flex flex={1} gap='md-alt' direction='column' data-cy='farm-data-entry'>
      <PageWrapper ponds={pondsGridData} />
    </Flex>
  );
}
export type FormValues = {
  monitoringDateToShow: Date;
  dailyParamDateToShow: Date;
  dailyDataDateToShow: Date;
  survivalDateToShow: Date;
};

function PageWrapper({ ponds }: { ponds: CurrentFarmPonds }) {
  const { selectedDateRange, weekRanges, setSelectedDateRange, currentWeekDays } = useWeekDates();
  const [isEditingWeeklyData, setIsEditingWeeklyData] = useState(false);
  const [isEditingDailyData, setIsEditingDailyData] = useState(false);
  const [isEditingDailyParams, setIsEditingDailyParams] = useState(false);
  const [isEditingSurvivalParams, setIsEditingSurvivalParams] = useState(false);

  const { query } = useRouter();
  const { view: queryView, date: defaultDate } = query as { view: ViewType; date: string };

  const productOffering = useAppSelector((state) => state.farm?.currentFarm?.metadata?.productOffering);
  const isVisionOnly = productOffering === 'visionOnly';

  const viewDefaultValue = isVisionOnly ? 'dailyParameter' : 'weeklyData';
  const view = queryView || viewDefaultValue;

  const formMethods = useForm<FormValues>({
    defaultValues: {
      monitoringDateToShow: new Date(),
      dailyParamDateToShow: new Date(),
      dailyDataDateToShow: new Date()
    }
  });

  const isFirstDateRange = weekRanges?.[0] === selectedDateRange;
  const isLastDateRange = weekRanges?.[weekRanges?.length - 1] === selectedDateRange;
  const isFeedOrSurvivalView = view === 'dailyData' || view === 'weeklyData' || view === 'survivalData';
  return (
    <FormProvider {...formMethods}>
      <Flex align='center' gap='md-alt' flexWrap='wrap'>
        <DataUpdaterLinks />
        <Flex align='center' gap='md-alt' flexWrap='wrap'>
          <DataUpdaterViewSelector />
          {view === 'dailyParameter' && <DailyParametersCalendar setIsEditing={setIsEditingDailyParams} />}
          {view === 'weightData' && <WeightDataCalendar defaultDate={defaultDate} />}
          <VisionOnlyGuard>
            {view === 'survivalData' && <SurvivalDataCalendar setIsEditing={setIsEditingSurvivalParams} />}
          </VisionOnlyGuard>
        </Flex>

        <VisionOnlyGuard>
          {view === 'weeklyData' && (
            <Flex align='center' gap='sm-alt'>
              <ArrowContainer
                isDisabled={isLastDateRange}
                {...(!isLastDateRange && {
                  onClick: () => {
                    const item = weekRanges.findIndex((item) => item === selectedDateRange);
                    setSelectedDateRange(weekRanges[item + 1]);
                  }
                })}
              >
                <ArrowBigLeftFilled />
              </ArrowContainer>
              <Flex align='center' h='40px' justify='center' bgColor='white' borderRadius='xl'>
                <WeekRangesMenu
                  shouldShowArrow={false}
                  selectedDateRange={selectedDateRange}
                  buttonProps={{
                    borderRightRadius: 0,
                    borderLeftRadius: 'xl',
                    _focus: { outline: 'none' },
                    _hover: { bgColor: 'gray.200' }
                  }}
                  weekRanges={weekRanges}
                  onChange={(val) => {
                    setSelectedDateRange(val);
                    setIsEditingWeeklyData(false);
                  }}
                />
                <Box w='0.5px' bgColor='gray.200' h='full' />
                <EditDateRangeModal
                  weekRange={selectedDateRange}
                  onMenuOpen={() => setIsEditingWeeklyData(false)}
                  containerProps={{
                    px: 'md',
                    h: 'full',
                    cursor: 'pointer',
                    borderRightRadius: 'xl',
                    _hover: { bgColor: 'gray.200' }
                  }}
                />
              </Flex>
              <ArrowContainer
                isDisabled={isFirstDateRange}
                {...(!isFirstDateRange && {
                  onClick: () => {
                    const item = weekRanges.findIndex((item) => item === selectedDateRange);
                    setSelectedDateRange(weekRanges[item - 1]);
                  }
                })}
              >
                <ArrowBigRightFilled />
              </ArrowContainer>
            </Flex>
          )}
          {view === 'dailyData' && <DailyDataCalendar setIsEditing={setIsEditingDailyData} />}
        </VisionOnlyGuard>
      </Flex>

      {view === 'dailyParameter' && (
        <DailyParamTableUpdater isEditing={isEditingDailyParams} setIsEditing={setIsEditingDailyParams} ponds={ponds} />
      )}

      {view === 'weightData' && <WeightData ponds={ponds} />}

      <VisionOnlyGuard showWarning={isFeedOrSurvivalView}>
        {view === 'dailyData' && (
          <DailyDataTableUpdater isEditing={isEditingDailyData} setIsEditing={setIsEditingDailyData} ponds={ponds} />
        )}
        {view === 'weeklyData' && (
          <WeeklyDataTableUpdater
            isEditing={isEditingWeeklyData}
            setIsEditing={setIsEditingWeeklyData}
            ponds={ponds}
            currentWeekDays={currentWeekDays}
          />
        )}

        {view === 'survivalData' && (
          <SurvivalData ponds={ponds} isEditing={isEditingSurvivalParams} setIsEditing={setIsEditingSurvivalParams} />
        )}
      </VisionOnlyGuard>
    </FormProvider>
  );
}

function DailyParametersCalendar(props: { setIsEditing: (value: SetStateAction<boolean>) => void }) {
  const { setIsEditing } = props;
  const {
    control,
    formState: { errors },
    setValue,
    watch
  } = useFormContext<FormValues>();
  const dailyParamDateToShowWatch = watch('dailyParamDateToShow');
  const todayDate = DateTime.local();
  const currentDate = DateTime.fromJSDate(dailyParamDateToShowWatch);
  const prevDateLuxon = currentDate.minus({ days: 1 });
  const nextDateLuxon = currentDate.plus({ days: 1 });
  const isNextDisabled = nextDateLuxon > todayDate;

  useEffect(() => {
    if (!setIsEditing) return;
    setIsEditing(false);
  }, [dailyParamDateToShowWatch]);

  return (
    <Flex align='center' gap='sm-alt' data-cy='daily-parameters-calender'>
      <Flex
        borderRadius='xl'
        h='40px'
        bgColor='white'
        align='center'
        justify='center'
        px='2sm'
        onClick={() => {
          const newDate = prevDateLuxon.toJSDate();
          setValue('dailyParamDateToShow', newDate);
        }}
      >
        <ArrowBigLeftFilled />
      </Flex>

      <Flex w='155px' align='center' justify='center' bgColor='white' h='40px' px='lg' borderRadius='xl' pos='relative'>
        <FormControlDateInput
          control={control}
          placement='bottom'
          displayAsDropdown={true}
          hideDropdownIcon={true}
          dateFormat='MMMM dd'
          id='dailyParamDateToShow'
          name='dailyParamDateToShow'
          inputProps={{ textAlign: 'center' }}
          maxDate={todayDate.toJSDate()}
          error={errors?.dailyParamDateToShow?.message?.toString()}
        />
      </Flex>
      <Flex
        cursor={isNextDisabled ? 'not-allowed' : 'pointer'}
        opacity={isNextDisabled ? 0.5 : 1}
        borderRadius='xl'
        h='40px'
        bgColor='white'
        align='center'
        justify='center'
        px='2sm'
        onClick={() => {
          if (isNextDisabled) return;
          const newDate = nextDateLuxon.toJSDate();
          setValue('dailyParamDateToShow', newDate);
        }}
      >
        <ArrowBigRightFilled />
      </Flex>
    </Flex>
  );
}

function DailyDataCalendar(props: { setIsEditing: (value: SetStateAction<boolean>) => void }) {
  const { setIsEditing } = props;
  const {
    control,
    formState: { errors },
    setValue,
    watch
  } = useFormContext<FormValues>();
  const dailyDataDateToShowWatch = watch('dailyDataDateToShow');
  const todayDate = DateTime.local();
  const currentDate = DateTime.fromJSDate(dailyDataDateToShowWatch);
  const minDate = todayDate.minus({ days: 150 });
  const prevDateLuxon = currentDate.minus({ days: 1 });
  const nextDateLuxon = currentDate.plus({ days: 1 });
  const isNextDisabled = nextDateLuxon > todayDate;
  const isPrevDisabled = currentDate < minDate;

  useEffect(() => {
    if (!setIsEditing) return;
    setIsEditing(false);
  }, [dailyDataDateToShowWatch]);

  return (
    <Flex align='center' gap='sm-alt' data-cy='daily-data-calender'>
      <Flex
        cursor={isPrevDisabled ? 'not-allowed' : 'pointer'}
        opacity={isPrevDisabled ? 0.5 : 1}
        borderRadius='xl'
        h='40px'
        bgColor='white'
        align='center'
        justify='center'
        px='2sm'
        onClick={() => {
          if (isPrevDisabled) return;
          const newDate = prevDateLuxon.toJSDate();
          setValue('dailyDataDateToShow', newDate);
        }}
      >
        <ArrowBigLeftFilled />
      </Flex>

      <Flex w='155px' align='center' justify='center' bgColor='white' h='40px' px='lg' borderRadius='xl' pos='relative'>
        <FormControlDateInput
          control={control}
          placement='bottom'
          displayAsDropdown={true}
          hideDropdownIcon={true}
          dateFormat='MMMM dd'
          id='dailyDataDateToShow'
          name='dailyDataDateToShow'
          inputProps={{ textAlign: 'center' }}
          maxDate={todayDate.toJSDate()}
          minDate={minDate.toJSDate()}
          error={errors?.dailyDataDateToShow?.message?.toString()}
        />
      </Flex>
      <Flex
        cursor={isNextDisabled ? 'not-allowed' : 'pointer'}
        opacity={isNextDisabled ? 0.5 : 1}
        borderRadius='xl'
        h='40px'
        bgColor='white'
        align='center'
        justify='center'
        px='2sm'
        onClick={() => {
          if (isNextDisabled) return;
          const newDate = nextDateLuxon.toJSDate();
          setValue('dailyDataDateToShow', newDate);
        }}
      >
        <ArrowBigRightFilled />
      </Flex>
    </Flex>
  );
}

function WeightDataCalendar({ defaultDate }: { defaultDate?: string }) {
  const {
    control,
    formState: { errors },
    setValue
  } = useFormContext<FormValues>();
  const currentFarmPonds = useAppSelector((state) => state.farm?.currentFarmPonds);
  const monitoringDates = useMemo(() => {
    const data: string[] = [];

    for (const pond of currentFarmPonds) {
      const { currentPopulation } = pond;
      const { history = [] } = currentPopulation ?? {};

      for (const historyItem of history ?? []) {
        const { date } = historyItem;
        if (!data.includes(date)) data.push(date);
      }
    }

    return data
      ?.map((monitoringDate) => DateTime.fromISO(monitoringDate).toJSDate())
      ?.sort((a: Date, b: Date) => a.getTime() - b.getTime());
  }, [currentFarmPonds]);

  useEffect(() => {
    if (!monitoringDates) return;

    setValue(
      'monitoringDateToShow',
      defaultDate ? DateTime.fromISO(defaultDate).toJSDate() : monitoringDates[monitoringDates.length - 1]
    );
  }, [monitoringDates]);

  return (
    <Flex
      align='center'
      justify='center'
      bgColor='white'
      h='40px'
      ps='md'
      pe='xs'
      rounded='4xl'
      data-cy='weight-data-calendar'
    >
      <FormControlDateInput
        control={control}
        placement='bottom-end'
        displayAsDropdown={true}
        dateFormat='MMM dd, yyyy'
        id='monitoringDateToShow'
        name='monitoringDateToShow'
        highlightDates={monitoringDates}
        error={errors?.monitoringDateToShow?.message?.toString()}
      />
    </Flex>
  );
}

function SurvivalDataCalendar(props: { setIsEditing: (value: SetStateAction<boolean>) => void }) {
  const { setIsEditing } = props;
  const {
    control,
    formState: { errors },
    setValue,
    watch
  } = useFormContext<FormValues>();
  const todayDate = DateTime.local();

  const currentFarmPonds = useAppSelector((state) => state.farm?.currentFarmPonds);
  const monitoringDates = useMemo(() => {
    const data: string[] = [];

    for (const pond of currentFarmPonds) {
      const { currentPopulation } = pond;
      const { history = [] } = currentPopulation ?? {};

      for (const historyItem of history ?? []) {
        const { date } = historyItem;
        if (!data.includes(date)) data.push(date);
      }
    }

    return data
      ?.map((monitoringDate) => DateTime.fromISO(monitoringDate).toJSDate())
      ?.sort((a: Date, b: Date) => a.getTime() - b.getTime());
  }, [currentFarmPonds]);

  useEffect(() => {
    if (!monitoringDates) return;
    setValue('survivalDateToShow', monitoringDates[monitoringDates.length - 1]);
  }, [monitoringDates]);

  const survivalDateToShowWatch = watch('survivalDateToShow');

  useEffect(() => {
    if (!setIsEditing) return;
    setIsEditing(false);
  }, [survivalDateToShowWatch]);

  return (
    <Flex
      align='center'
      justify='center'
      bgColor='white'
      h='40px'
      ps='md'
      pe='xs'
      rounded='4xl'
      data-cy='survival-data-calendar'
    >
      <FormControlDateInput
        control={control}
        placement='bottom-end'
        displayAsDropdown={true}
        dateFormat='MMM dd, yyyy'
        id='survivalDateToShow'
        name='survivalDateToShow'
        highlightDates={monitoringDates}
        error={errors?.survivalDateToShow?.message?.toString()}
        maxDate={todayDate.toJSDate()}
      />
    </Flex>
  );
}

type ViewType = 'weeklyData' | 'dailyParameter' | 'weightData' | 'dailyData' | 'survivalData';

function DataUpdaterViewSelector() {
  const { trans } = getTrans();
  const { trackAction } = useAnalytics();
  const { query, route } = useRouter();
  const { view: queryView } = query as { view: ViewType };

  const productOffering = useAppSelector((state) => state.farm?.currentFarm?.metadata?.productOffering);
  const isVisionOnly = productOffering === 'visionOnly';

  const viewDefaultValue = isVisionOnly ? 'dailyParameter' : 'weeklyData';
  const view = queryView || viewDefaultValue;

  const options: { label: string; value: ViewType }[] = isVisionOnly
    ? [
        { label: trans('t_daily_parameters'), value: 'dailyParameter' },
        { label: trans('t_abw_g'), value: 'weightData' }
      ]
    : [
        { label: trans('t_feed'), value: 'weeklyData' },
        { label: trans('t_daily_parameters'), value: 'dailyParameter' },
        { label: trans('t_abw_g'), value: 'weightData' },
        { label: trans('t_survival'), value: 'survivalData' }
      ];

  const isFeed = view === 'dailyData' || view === 'weeklyData';
  const selectedOption = isFeed
    ? options.find((option) => option.value === 'weeklyData')
    : options.find((option) => option.value === view);

  const updateProfitProjectToView = (value: ViewType) => {
    trackAction(actionsName.farmDataUpdaterViewChanged, { view: value }).then();
    goToUrl({ route, params: { ...query, view: value } });
  };

  const feedOptions: { label: string; value: ViewType }[] = [
    { label: trans('t_weekly'), value: 'weeklyData' },
    { label: trans('t_daily'), value: 'dailyData' }
  ];
  const selectedFeedOption = feedOptions.find((option) => option.value === view);

  return (
    <>
      <BaseMenu
        selectedOption={selectedOption}
        options={options}
        onItemSelect={(option) => updateProfitProjectToView(option.value as ViewType)}
        data-cy='data-updater-selector'
        placeholder={trans('t_select')}
      />
      {!!selectedOption && isFeed && (
        <BaseMenu
          selectedOption={selectedFeedOption}
          options={feedOptions}
          onItemSelect={(option) => updateProfitProjectToView(option.value as ViewType)}
          data-cy='feed-tabs-options'
          placeholder={trans('t_select')}
        />
      )}
    </>
  );
}

function TableLoadingSkeleton() {
  return (
    <Flex gap='sm' direction='column' w='100%' data-cy='data-updater-loader'>
      <Flex justify='space-between' align='center' gap='md-alt' flexWrap='wrap'>
        <Flex gap='lg' flexWrap='wrap'>
          <Skeleton h='32px' w='250px' borderRadius='full' />
          <Skeleton h='32px' w='250px' borderRadius='full' />
        </Flex>
        <Skeleton ms='auto' h='32px' w='110px' borderRadius='full' />
      </Flex>

      <SimpleGrid columns={3} gap='sm'>
        <Skeleton h='32px' w='100%' />
        <Skeleton h='32px' w='100%' />
        <Skeleton h='32px' w='100%' />
        <Skeleton h='32px' w='100%' />
        <Skeleton h='32px' w='100%' />
        <Skeleton h='32px' w='100%' />
        <Skeleton h='32px' w='100%' />
        <Skeleton h='32px' w='100%' />
        <Skeleton h='32px' w='100%' />
        <Skeleton h='32px' w='100%' />
        <Skeleton h='32px' w='100%' />
        <Skeleton h='32px' w='100%' />
        <Skeleton h='32px' w='100%' />
        <Skeleton h='32px' w='100%' />
        <Skeleton h='32px' w='100%' />
        <Skeleton h='32px' w='100%' />
        <Skeleton h='32px' w='100%' />
        <Skeleton h='32px' w='100%' />
      </SimpleGrid>
    </Flex>
  );
}

function ArrowContainer(props: FlexProps & { isDisabled?: boolean }) {
  const { isDisabled, ...rest } = props;

  return (
    <Flex
      px='2sm'
      h='40px'
      align='center'
      bgColor='white'
      cursor='pointer'
      justify='center'
      borderRadius='xl'
      _hover={{ bgColor: 'gray.200' }}
      {...(isDisabled && { opacity: 0.5, cursor: 'not-allowed', _hover: { bgColor: 'white' } })}
      {...rest}
    />
  );
}
