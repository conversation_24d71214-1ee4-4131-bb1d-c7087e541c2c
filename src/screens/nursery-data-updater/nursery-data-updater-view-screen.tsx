import { Flex } from '@chakra-ui/react';
import { PartialPageLoader } from '@components/loaders/partial-page-loader';
import { useAppSelector } from '@redux/hooks';
import { NurseryDataUpdater } from '@screens/nursery-data-updater/components/nursery-data-updater-table';
import { FarmSelector } from '@screens/farm/components/farm-selector';

export function NurseryDataUpdaterScreen() {
  const { currentFarm } = useAppSelector((state) => state.farm);
  const { _id: farmId } = currentFarm ?? {};

  if (!farmId) return <PartialPageLoader />;

  return (
    <Flex direction='column' gap='md-alt' p='md' minH='100vh' data-cy='nursery-data-updater-screen'>
      <FarmSelector />
      <NurseryDataUpdater farmId={farmId} />
    </Flex>
  );
}
