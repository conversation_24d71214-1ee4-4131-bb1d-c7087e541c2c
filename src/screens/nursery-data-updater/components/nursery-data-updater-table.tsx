import { Flex, SimpleGrid, Skeleton } from '@chakra-ui/react';
import { useListNurseriesApi } from '@screens/nursery/hooks/use-list-nurseries-api';
import { useMemo, useState } from 'react';
import { NoNurseriesInYourFarmCard } from '@screens/nursery/components/no-nurseries-in-your-farm-card';
import { useWeekDates } from '@screens/data-updater/helper/use-week-dates';
import { useAppSelector } from '@redux/hooks';
import { getFeedDataForWeekDays } from '@screens/data-updater/helper/get-feed-data-for-week';
import { WeekRangesMenu } from '@screens/data-updater/components/week-range-menu';
import { EditDateRangeModal } from '@screens/data-updater/components/edit-date-range-modal';
import { NurseryTableUpdater } from '@screens/nursery-data-updater/components/nursery-table-updater';
import { NurseryType } from '@screens/nursery/hooks/nursery-population-fields';
import { DataUpdaterLinks } from '@screens/data-updater/components/data-updater-links';

export function NurseryDataUpdater({ farmId }: { farmId: string }) {
  const [{ isLoading: isLoadingNurseries, data, isFinishedOnce }, listNurseries] = useListNurseriesApi({
    params: { filter: { farmId: [farmId] } }
  });
  const isLoading = isLoadingNurseries || !isFinishedOnce;
  const onUpdate = () => listNurseries({ params: { filter: { farmId: [farmId] } } });

  if (isLoading) return <TableLoadingSkeleton />;

  if (!data?.length) return <NoNurseriesInYourFarmCard />;

  const nurseries = data.filter((ele) => ele.population);

  return <PageWrapper nurseries={nurseries} onUpdate={onUpdate} />;
}

function PageWrapper({ nurseries, onUpdate }: { nurseries: NurseryType[]; onUpdate: () => void }) {
  const { currentFarm } = useAppSelector((state) => state.farm);
  const { selectedDateRange, weekRanges, setSelectedDateRange, currentWeekDays } = useWeekDates();
  const [isEditing, setIsEditing] = useState(false);

  const initialData = useMemo(() => {
    if (!nurseries?.length) return;

    return nurseries.map((nursery) => {
      const { name, population, _id } = nursery;
      const { feedData, harvest, stockedAt } = population ?? {};
      const feedTypes = currentFarm?.feedTypes?.filter((ele) => ele.status !== 'inactive');
      const { otherDirectCost, feedGiven } = getFeedDataForWeekDays(feedData, currentWeekDays, feedTypes) ?? {};

      return {
        pondName: name,
        otherDirectCost,
        feedGiven,
        populationInfo: {
          nurseryId: _id,
          feedData,
          stockedAt,
          originalOtherDirectCost: otherDirectCost,
          originalFeedGiven: feedGiven,
          isHarvested: !!harvest?.date
        }
      };
    });
  }, [nurseries, currentWeekDays]);

  return (
    <>
      <Flex align='center' gap='md-alt' flexWrap='wrap'>
        <DataUpdaterLinks />
        <Flex align='center' gap='xs'>
          <WeekRangesMenu
            selectedDateRange={selectedDateRange}
            weekRanges={weekRanges}
            onChange={(val) => {
              setSelectedDateRange(val);
              setIsEditing(false);
            }}
          />
          <EditDateRangeModal weekRange={selectedDateRange} onMenuOpen={() => setIsEditing(false)} />
        </Flex>
      </Flex>

      <NurseryTableUpdater
        isEditing={isEditing}
        setIsEditing={setIsEditing}
        currentWeekDays={currentWeekDays}
        initialData={initialData}
        onUpdate={onUpdate}
      />
    </>
  );
}

function TableLoadingSkeleton() {
  const arrayOfSkeletonNumbers = new Array(12).fill(0);

  return (
    <Flex gap='sm' direction='column' w='100%' mt='sm' data-cy='nursery-data-updater-loader'>
      <Flex gap='md' align='center' justify='space-between' flexWrap='wrap'>
        <Skeleton h='32px' w='250px' rounded='full' />
        <Skeleton ms='auto' h='32px' w='110px' rounded='full' />
      </Flex>

      <SimpleGrid columns={3} gap='sm'>
        {arrayOfSkeletonNumbers.map((_, index) => (
          <Skeleton h='32px' w='100%' key={index} />
        ))}
      </SimpleGrid>
    </Flex>
  );
}
