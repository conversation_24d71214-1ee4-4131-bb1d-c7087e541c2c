import { Flex } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { useYupSchema, Yup, yupFormResolver, YupResolverType } from '@utils/yup';
import { PopulationFeedData } from '@xpertsea/module-farm-sdk';
import { SetStateAction, useEffect } from 'react';
import { FieldArrayWithId, useFieldArray, useForm } from 'react-hook-form';
import { useNurseryTableUpdaterData } from '@screens/nursery-data-updater/components/use-nursery-table-update-data';
import { BaseButton } from '@components/base/base-button';
import { HotTableComponent } from '@components/hot-table/hot-table-component';
import { getBulkUpdatedNurseries } from '@screens/nursery-data-updater/helper/get-bulk-updated-nurseries';
import { useBulkUpdateNursery } from '@screens/nursery-data-updater/hooks/use-bulk-update-nurseries';
import { getDaysDiffBetweenDates } from '@screens/farm/helpers/farm-dates';
import { HotTableStylesWrapper } from '@components/hot-table/hot-table-styles-wrapper';
// @ts-expect-error waiting handson table update
// eslint-disable-next-line import/no-unresolved
import { CellProperties } from 'handsontable/settings';
import { EditOutlinedIcon } from '@icons/edit/edit-outlined-icon';
import { BaseIconButton } from '@components/base/base-icon-button';

type TableDataType = {
  pondName: string;
  otherDirectCost: number;
  feedGiven: { feedId: string; totalLbs: number }[];
  populationInfo: {
    nurseryId: string;
    feedData: PopulationFeedData[];
    originalOtherDirectCost: number;
    originalFeedGiven: { feedId: string; totalLbs: number }[];
    stockedAt: string;
    isHarvested: boolean;
  };
}[];
export type FarmNurseryDataEntryType = { ponds: TableDataType };

interface Props {
  isEditing: boolean;
  setIsEditing: (value: SetStateAction<boolean>) => void;
  currentWeekDays: string[];
  initialData: TableDataType;
  onUpdate: () => void;
}

export function NurseryTableUpdater(props: Props) {
  const { isEditing, initialData, currentWeekDays, onUpdate, setIsEditing } = props;
  const { trans } = getTrans();

  const [{ isLoading }, bulkUpdateNursery] = useBulkUpdateNursery();

  const { schema } = useYupSchema({
    ponds: Yup.array().of(Yup.object().shape({}))
  });
  const {
    control,
    handleSubmit,
    formState: { errors },
    trigger,
    reset
  } = useForm<FarmNurseryDataEntryType>({
    mode: 'onChange',
    resolver: yupFormResolver(schema) as YupResolverType<FarmNurseryDataEntryType>,
    defaultValues: { ponds: [] }
  });

  const { fields: formFields, update: updateFormFields } = useFieldArray<FarmNurseryDataEntryType>({
    control,
    name: 'ponds'
  });

  useEffect(() => {
    if (!initialData?.length) return;
    reset({ ponds: initialData });
  }, [initialData]);

  const { gridData, cellHeaders, columns, changedFields, handleAfterChange, setChangedFields } =
    useNurseryTableUpdaterData({
      isEditing,
      formFields: formFields as FieldArrayWithId<FarmNurseryDataEntryType, 'ponds'>[],
      errors,
      updateFormFields,
      trigger
    });

  const handleCancel = () => {
    reset({ ponds: initialData });
    setIsEditing(false);
    setChangedFields({});
  };

  const generateCellProperties = (row: number) => {
    const cellProps = {} as Pick<CellProperties, 'readOnly' | 'className' | 'comment'>;
    const stockedAt = gridData[row]?.['stockedAt'];
    const isHarvested = gridData[row]?.['isHarvested'];
    const baseDate = currentWeekDays?.[0];

    if (!stockedAt || !baseDate) return cellProps;

    const daysDiff = getDaysDiffBetweenDates({ dateToCompare: stockedAt, baseDate });
    if (daysDiff <= -7 || isHarvested) {
      cellProps.readOnly = true;
      cellProps.className = 'htReadyOnly';
    }
    return cellProps;
  };

  const handleOnSubmit = handleSubmit(async () => {
    const bulkPopulationData = Object.values(changedFields);

    const bulkNurseries = getBulkUpdatedNurseries(bulkPopulationData, currentWeekDays);

    bulkUpdateNursery({
      params: { nurseries: bulkNurseries },
      successCallback: () => {
        onUpdate();
        setIsEditing(false);
      }
    });
  });

  return (
    <HotTableStylesWrapper
      h='100%'
      as='form'
      mt={{ base: 'xl', lg: '0' }}
      onSubmit={handleOnSubmit}
      pos='relative'
      css={{ '& .handsontable .htEdit': { backgroundColor: isEditing ? 'white' : 'gray.200' } }}
      data-cy='nursery-feed-data-table'
    >
      <Flex align='center' gap='md' justify='space-between' flexWrap='wrap' pos='absolute' top='-59px' insetEnd={0}>
        {isEditing ? (
          <Flex gap='sm' data-cy='nursery-feed-data-table-actions'>
            <BaseButton colorPalette='black' variant='ghost' onClick={handleCancel} data-cy='cancel-btn'>
              {trans('t_cancel')}
            </BaseButton>
            <BaseButton type='submit' loading={isLoading} data-cy='save-btn'>
              {trans('t_save_changes')}
            </BaseButton>
          </Flex>
        ) : (
          <BaseIconButton onClick={() => setIsEditing(true)} data-cy='edit-nursery-feed-data'>
            <EditOutlinedIcon />
          </BaseIconButton>
        )}
      </Flex>

      <HotTableComponent
        data={gridData}
        height='calc(100% - 53px)'
        licenseKey='non-commercial-and-evaluation'
        contextMenu={{ items: { copy: {}, cut: {}, undo: {}, redo: {} } }}
        cells={generateCellProperties}
        comments
        afterChange={handleAfterChange}
        rowHeaders={false}
        columns={columns}
        colHeaders={cellHeaders}
      />
    </HotTableStylesWrapper>
  );
}
