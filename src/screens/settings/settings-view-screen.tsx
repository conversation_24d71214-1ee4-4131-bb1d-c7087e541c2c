import { Box, Flex, Tabs, TabsContentProps, Text } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { Production } from '@screens/settings/components/production';
import { Hatcheries } from '@screens/settings/components/hatcheries';
import { Genetic } from '@screens/settings/components/genetic';
import { useRouter } from 'next/router';
import { goToUrl } from '@utils/url';
import { HtmlHead } from '@components/html/html-head';
import { StockingDefaults } from '@screens/settings/components/stocking-defaults';
import { Projections } from '@screens/settings/components/projections';
import { WeeklyProgramming } from '@screens/settings/components/weekly-programming';
import { FarmSelector } from '@screens/farm/components/farm-selector';
import { FeedTable } from '@screens/settings/components/feed-table';
import { Autofeeders } from '@screens/settings/components/autofeeders';
import { Aerators } from '@screens/settings/components/aerators';
import { ProcessorPriceLists } from '@screens/settings/components/processor-price-lists';
import { FeedTypes } from '@screens/settings/components/feed-types';
import { usePermission } from '@hooks/use-permission';
import { useAnalytics } from '@hooks/use-analytics';
import { actionsName } from '@utils/segment';
import { OverlayLoader } from '@components/loaders/overlay-loader';
import { DailyData } from '@screens/settings/components/daily-data';
import { AdminOrSupervisorWrapper } from '@components/permission-wrappers/admin-or-supervisor-wrapper';
import { Notifications } from './components/notifications';
import { AdminOrSupervisorOrOperationWrapper } from '@components/permission-wrappers/admin-or-supervisor-or-operation-wrapper';

type TabsOption =
  | 'production'
  | 'nurseries'
  | 'processors'
  | 'equipment'
  | 'feed'
  | 'dailyData'
  | 'projections'
  | 'notifications';

const selectedTabStyles = {
  color: 'white',
  position: 'relative',
  bgColor: 'squidInkPowder.800'
};
const tabPanelStyles: Omit<TabsContentProps, 'value'> = {
  p: 'md',
  mb: 'md',
  display: 'flex',
  flexDirection: 'column',
  gap: 'md-alt',
  rounded: '2xl',
  bgColor: 'gray.100',
  shadow: 'elevation.100'
};

export function SettingsViewScreen() {
  const { trackAction } = useAnalytics();
  const { trans } = getTrans();
  const { query, pathname: route } = useRouter();
  const { tab: queryTab = 'production' } = query as { tab: TabsOption };

  const currentFarm = useAppSelector((state) => state.farm?.currentFarm);
  const isLoadingFarmData = useAppSelector((state) => state.farm?.isLoadingFarmData);

  const isVisionOnlyFarm = currentFarm?.metadata?.productOffering === 'visionOnly';
  const isAdmin = usePermission({ role: 'admin', entity: 'farm', entityId: currentFarm?._id });
  const isUser = usePermission({ role: 'user', entity: 'farm', entityId: currentFarm?._id });

  if (isLoadingFarmData) return <OverlayLoader isLoading={isLoadingFarmData} />;

  if (!currentFarm)
    return (
      <Flex align='center' justify='center' minH='100vh' minW='100vw' direction='column'>
        <Box>
          <Text as='h1' fontSize='3xl' fontWeight='bold' mb={6}>
            {trans('t_select_farm_to_continue')}
          </Text>
          <FarmSelector mb='sm-alt' placeholder={trans('t_select_option')} />
        </Box>
      </Flex>
    );

  const tabsTrans = {
    production: trans('t_production'),
    nurseries: trans('t_nurseries'),
    processors: trans('t_processors'),
    equipment: trans('t_pond_setup'),
    feed: trans('t_feed'),
    dailyData: trans('t_daily_parameters'),
    projections: trans('t_projections'),
    notifications: trans('t_notifications')
  };

  const adminTabs: TabsOption[] = [
    'production',
    'nurseries',
    'processors',
    'equipment',
    'feed',
    'dailyData',
    'projections',
    'notifications'
  ];

  const visionOnlyTabs: TabsOption[] = ['production', 'dailyData', 'notifications'];

  let tabs: TabsOption[];
  if (isVisionOnlyFarm) {
    tabs = visionOnlyTabs;
  } else {
    tabs = isAdmin
      ? adminTabs
      : adminTabs.filter((tab) => {
          if (isUser) {
            const tabsToHideForUser = ['processors', 'notifications', 'projections'];
            return !tabsToHideForUser.includes(tab);
          }
          //tabs to hide for supervisor and operation
          const tabsToHideForNonAdmin = ['processors', 'notifications'];
          return !tabsToHideForNonAdmin.includes(tab);
        });
  }

  const isValidTab = tabs.includes(queryTab);

  return (
    <Flex flexDir='column' gap='md-alt' p='md'>
      <FarmSelector />

      <Flex direction='column' data-cy='settings-screen' key={currentFarm?._id}>
        <Tabs.Root
          variant='plain'
          value={isValidTab ? queryTab : 'production'}
          onValueChange={(e) => {
            const tab = e.value;
            trackAction(actionsName.settingsTabSelected, { tab }).then();
            goToUrl({ route, params: { ...query, tab } });
          }}
        >
          <Tabs.List gap='sm-alt' border='none' ms='lg' minH='auto' flexWrap='wrap'>
            {tabs.map((tab) => (
              <Tabs.Trigger
                value={tab}
                key={tab}
                px='md-alt'
                border='none'
                h='32px'
                color='gray.800'
                bgColor='gray.200'
                textStyle='label.100'
                borderTopRadius='2xl'
                borderBottomRadius='none'
                transition='none'
                _hover={selectedTabStyles}
                _selected={selectedTabStyles}
                _active={{ backgroundColor: 'none' }}
                data-cy={`setting-${tab}-tab`}
              >
                {tabsTrans[tab]}
              </Tabs.Trigger>
            ))}
          </Tabs.List>

          <Tabs.Content value='production' {...tabPanelStyles}>
            {(!isValidTab || queryTab === 'production') && (
              <>
                <HtmlHead
                  title={`${trans('t_production')} | ${trans('t_configuration')} | ${currentFarm?.name}`}
                  description={`${trans('t_production')} | ${trans('t_configuration')} | ${currentFarm?.name}`}
                />
                <WeeklyProgramming />
                {!isVisionOnlyFarm && (
                  <AdminOrSupervisorWrapper>
                    <Production />
                  </AdminOrSupervisorWrapper>
                )}

                {!isVisionOnlyFarm && (
                  <AdminOrSupervisorOrOperationWrapper>
                    <StockingDefaults />
                  </AdminOrSupervisorOrOperationWrapper>
                )}
              </>
            )}
          </Tabs.Content>
          <Tabs.Content value='nurseries' {...tabPanelStyles}>
            {queryTab === 'nurseries' && (
              <>
                <HtmlHead
                  title={`${trans('t_nurseries')} | ${trans('t_configuration')} | ${currentFarm?.name}`}
                  description={`${trans('t_nurseries')} | ${trans('t_configuration')} | ${currentFarm?.name}`}
                />
                <FunctionListChangesAlert />
                <Flex direction={{ base: 'column', md: 'row' }} justify='center' gap='sm-alt'>
                  <Hatcheries />
                  <Genetic />
                </Flex>
              </>
            )}
          </Tabs.Content>
          {isAdmin && (
            <Tabs.Content value='processors' {...tabPanelStyles}>
              {queryTab === 'processors' && (
                <>
                  <HtmlHead
                    title={`${trans('t_processors')} | ${trans('t_configuration')} | ${currentFarm?.name}`}
                    description={`${trans('t_processors')} | ${trans('t_configuration')} | ${currentFarm?.name}`}
                  />
                  <FunctionListChangesAlert />
                  <ProcessorPriceLists />
                </>
              )}
            </Tabs.Content>
          )}

          <Tabs.Content value='equipment' {...tabPanelStyles}>
            {queryTab === 'equipment' && (
              <>
                <HtmlHead
                  title={`${trans('t_pond_setup')} | ${trans('t_configuration')} | ${currentFarm?.name}`}
                  description={`${trans('t_pond_setup')} | ${trans('t_configuration')} | ${currentFarm?.name}`}
                />
                <FunctionListChangesAlert />
                <Flex direction={{ base: 'column', md: 'row' }} justify='center' gap='sm-alt'>
                  <Autofeeders />
                  <Aerators />
                </Flex>
              </>
            )}
          </Tabs.Content>
          <Tabs.Content value='feed' {...tabPanelStyles}>
            {queryTab === 'feed' && (
              <>
                <HtmlHead
                  title={`${trans('t_feed')} | ${trans('t_configuration')} | ${currentFarm?.name}`}
                  description={`${trans('t_feed')} | ${trans('t_configuration')} | ${currentFarm?.name}`}
                />
                <FunctionListChangesAlert />
                <FeedTable />
                <AdminOrSupervisorWrapper>
                  <FeedTypes />
                </AdminOrSupervisorWrapper>
              </>
            )}
          </Tabs.Content>
          <Tabs.Content value='dailyData' {...tabPanelStyles}>
            {queryTab === 'dailyData' && (
              <>
                <HtmlHead
                  title={`${trans('t_daily_parameters')} | ${trans('t_configuration')} | ${currentFarm?.name}`}
                  description={`${trans('t_daily_parameters')} | ${trans('t_configuration')} | ${currentFarm?.name}`}
                />
                <DailyData />
              </>
            )}
          </Tabs.Content>
          <Tabs.Content value='projections' {...tabPanelStyles}>
            {(!isValidTab || queryTab === 'projections') && (
              <>
                <HtmlHead
                  title={`${trans('t_projections')} | ${trans('t_configuration')} | ${currentFarm?.name}`}
                  description={`${trans('t_projections')} | ${trans('t_configuration')} | ${currentFarm?.name}`}
                />
                <Projections />
              </>
            )}
          </Tabs.Content>
          <Tabs.Content value='notifications' {...tabPanelStyles}>
            {queryTab === 'notifications' && (
              <>
                <HtmlHead
                  title={`${trans('t_notifications')} | ${trans('t_configuration')} | ${currentFarm?.name}`}
                  description={`${trans('t_notifications')} | ${trans('t_configuration')} | ${currentFarm?.name}`}
                />
                <Notifications />
              </>
            )}
          </Tabs.Content>
        </Tabs.Root>
      </Flex>
    </Flex>
  );
}

function FunctionListChangesAlert() {
  const { trans } = getTrans();
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);
  const groupName = currentFarm?.groupName;

  const farmsData = useAppSelector((state) => state.farm?.farmsData);
  const groupFarms = groupName ? farmsData?.filter((farm) => farm?.groupName === groupName) : [currentFarm];
  return (
    <Box
      fontSize='md'
      py='sm-alt'
      px='md-alt'
      border='1px solid'
      borderColor='semanticYellow.600'
      bgColor='semanticYellow.100'
      borderRadius='base'
    >
      <Text mb='xs' fontWeight={500}>
        {trans('t_changes_will_apply_to_all_farms')}
      </Text>
      <Flex gap='xs' flexWrap='wrap'>
        {groupFarms?.map((farm, index) => {
          return (
            <Text key={farm.eid}>
              {farm.name}
              {index === groupFarms?.length - 1 ? '' : ','}
            </Text>
          );
        })}
      </Flex>
    </Box>
  );
}
