import {
  Box,
  chakra,
  DialogBodyProps,
  DialogContentProps,
  Flex,
  Skeleton,
  Text,
  useDisclosure
} from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { ReactNode } from 'react';
import { useFarmSectionUpdate } from '@screens/settings/hooks/use-farm-section-update';
import { BaseButton } from '@components/base/base-button';
import { useAppSelector } from '@redux/hooks';
import { useForm } from 'react-hook-form';
import { useYupSchema, Yup, YupResolverType } from '@utils/yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { actionsName } from '@utils/segment';
import { FarmDailyParameters } from '@xpertsea/module-farm-sdk';
import { EditIcon } from '@icons/edit/edit-icon';
import { PlusFilled } from '@icons/plus/plus-filled';
import { useAnalytics } from '@hooks/use-analytics';
import { BaseFormInput } from '@components/form/base-form-input';
import { DialogBody, DialogContent, DialogHeader, DialogRoot, DialogTitle } from '@components/ui/dialog';
import { HarvestFormOneRow } from '@screens/pond/components/modals/harvest-form-one-row';
import { FormControlReactSelect } from '@components/form/form-control-react-select';
import { harvestSelectControlStyles } from '@screens/pond/components/modals/helpers/chakra-form-styles';
import { DailyDataSwitch } from '@screens/settings/components/daily-data-switch';
import { DailyParams, useGetDailyDataTrans } from '@screens/settings/hooks/use-get-daily-data-trans';
import { AdminOrSupervisorOrOperationWrapper } from '@components/permission-wrappers/admin-or-supervisor-or-operation-wrapper';
import { TrashcanXIcon } from '@icons/trash-can/trash-can-x-icon';

type FarmDailyParametersItem = Omit<FarmDailyParameters, '__typename'>;

type FormValues = { dailyParameter: FarmDailyParametersItem };

const modalContentProps: DialogContentProps = {
  rounded: '2xl',
  px: 'lg',
  pt: 'lg',
  pb: '64px',
  bgColor: 'bg.gray.medium'
};

const modalBodyProps: DialogBodyProps = {
  p: 'md',
  bgColor: 'white',
  borderRadius: '2xl',
  mb: '2lg'
};

const maxAllowedCustomFields = 50;

export function DailyData() {
  const { trans } = getTrans();
  const { trackAction } = useAnalytics();

  const { currentFarm, isLoadingFarmsData } = useAppSelector((state) => state.farm);

  const { dailyParameters, _id: farmId } = currentFarm ?? {};
  const farmDailyParams: FarmDailyParametersItem[] = dailyParameters?.filter((type) => type.status === 'active') ?? [];

  const sortedFarmDailyParams = [...farmDailyParams].sort((a, b) => {
    if (a.isDefault && !b.isDefault) return -1;
    if (!a.isDefault && b.isDefault) return 1;
    return 0;
  });

  const { dailyWithUnitsTransMap, unitOptions } = useGetDailyDataTrans();

  const maxAllowedFields = maxAllowedCustomFields + Object.keys(dailyWithUnitsTransMap).length;
  const isMaxFieldsReached = farmDailyParams.length >= maxAllowedFields;

  return (
    <Flex flexDir='column' gap='md-alt'>
      <Text textStyle='label.100' color='gray.800' py='sm-alt'>
        {trans('t_daily_parameter_title')}
      </Text>
      {isLoadingFarmsData && (
        <Flex gap='sm-alt' direction='column'>
          <Skeleton height='25px' />
          <Skeleton height='25px' />
          <Skeleton height='25px' />
        </Flex>
      )}
      {!isLoadingFarmsData && (
        <Flex flexDir='column' gap='md'>
          <Flex flexDir='column' gap='md' maxH='464px' overflowY='scroll'>
            {sortedFarmDailyParams.map((dailyParam) => {
              const { name, unit, _id, isDefault } = dailyParam;
              const dailyParamDefaultName = dailyWithUnitsTransMap[name as DailyParams];
              return (
                <Flex
                  key={_id}
                  bgColor='white'
                  minH='60px'
                  px='sm-alt'
                  rounded='2xl'
                  align='center'
                  justify='space-between'
                  gap='sm-alt'
                >
                  <Flex align='center' gap='sm-alt'>
                    <DailyDataSwitch farmId={farmId} dailyParam={dailyParam} farmDailyParams={farmDailyParams} />

                    <Text textStyle='paragraph.100.heavy' color='gray.800'>
                      {dailyParamDefaultName ?? name}{' '}
                      {!dailyParamDefaultName && unit
                        ? `(${unitOptions.find((t) => t.value === unit)?.label ?? unit})`
                        : ''}
                    </Text>
                  </Flex>
                  <AdminOrSupervisorOrOperationWrapper>
                    {!isDefault && (
                      <Flex align='center' gap='md'>
                        <AddEditParameterModal
                          unitOptions={unitOptions}
                          farmId={farmId}
                          dailyParam={dailyParam}
                          farmDailyParams={farmDailyParams}
                        >
                          <EditIcon cursor='pointer' w='24px' h='24px' />
                        </AddEditParameterModal>
                        <DeleteParameterModal
                          farmId={farmId}
                          dailyParam={dailyParam}
                          farmDailyParams={farmDailyParams}
                        />
                      </Flex>
                    )}
                  </AdminOrSupervisorOrOperationWrapper>
                </Flex>
              );
            })}
          </Flex>
          <AdminOrSupervisorOrOperationWrapper>
            {!isMaxFieldsReached && (
              <AddEditParameterModal
                unitOptions={unitOptions}
                farmId={farmId}
                dailyParam={{ name: '' }}
                farmDailyParams={farmDailyParams}
              >
                <BaseButton
                  px={0}
                  variant='link'
                  _hover={{ textDecoration: 'none' }}
                  size='md'
                  color='gray.800'
                  onClick={() => {
                    trackAction(actionsName.farmConfigDailyParamsAddItemClicked).then();
                  }}
                >
                  <PlusFilled color='white' bgColor='brandBlue.600' /> {trans('t_add_parameter')}
                </BaseButton>
              </AddEditParameterModal>
            )}
            <Text size='label200'>
              {trans('t_x_daily_parameters_created', { count: farmDailyParams.length, total: maxAllowedFields })}
            </Text>
          </AdminOrSupervisorOrOperationWrapper>
        </Flex>
      )}
    </Flex>
  );
}

type DeleteParameterProps = {
  farmId: string;
  dailyParam: FarmDailyParametersItem;
  farmDailyParams: FarmDailyParametersItem[];
};

function DeleteParameterModal(props: DeleteParameterProps) {
  const { dailyParam, farmDailyParams, farmId } = props;
  const { trans } = getTrans();
  const { trackAction } = useAnalytics();

  const { open, onClose, onOpen } = useDisclosure();

  const { isLoading: isUpdating, handleUpdateFarm } = useFarmSectionUpdate();

  const handleCancel = () => {
    onClose();
    trackAction(actionsName.farmConfigDailyParamsRemoveItemCancel).then();
  };

  const handleSubmit = () => {
    const dailyParams = farmDailyParams.filter((param) => param._id !== dailyParam._id);
    handleUpdateFarm(
      {
        filter: {
          farmIds: [farmId]
        },
        set: {
          dailyParameters: dailyParams
        }
      },
      () => {
        onClose();
        trackAction(actionsName.farmConfigDailyParamsRemoveItemSave).then();
      }
    );
  };

  return (
    <DialogRoot
      open={open}
      onOpenChange={(e) => (e.open ? onOpen() : handleCancel())}
      placement='center'
      size='lg'
      restoreFocus={false}
      preventScroll={true}
    >
      <TrashcanXIcon cursor='pointer' onClick={onOpen} />
      <DialogContent p='lg' rounded='2xl'>
        <DialogHeader p={0}>
          <DialogTitle textStyle='headline.300.heavy'>{trans('t_delete_confirmation')}</DialogTitle>
        </DialogHeader>
        <DialogBody px='3xs' py='2lg' overflow='scroll'>
          <Text size='label100' mb='lg'>
            {trans('t_delete_daily_param_alert', { name: dailyParam.name })}
          </Text>
          <Text size='label100'>{trans('t_are_you_sure_you_want_to_continue')}</Text>
        </DialogBody>
        <Flex align='center' gap='sm-alt' justify='flex-end'>
          <BaseButton variant='secondary' w='max-content' onClick={handleCancel} loading={isUpdating}>
            {trans('t_cancel')}
          </BaseButton>
          <BaseButton type='submit' w='max-content' loading={isUpdating} onClick={handleSubmit}>
            {trans('t_delete')}
          </BaseButton>
        </Flex>
      </DialogContent>
    </DialogRoot>
  );
}

type AddEditParameterModalProps = {
  farmId: string;
  dailyParam: FarmDailyParametersItem;
  farmDailyParams: FarmDailyParametersItem[];
  children: ReactNode;
  unitOptions: { value: string; label: string }[];
};

function AddEditParameterModal(props: AddEditParameterModalProps) {
  const { dailyParam, children, farmDailyParams, farmId, unitOptions } = props;
  const { trans } = getTrans();
  const { trackAction } = useAnalytics();

  const isEdit = !!dailyParam?._id;

  const { open, onClose, onOpen } = useDisclosure();

  const { isLoading: isUpdating, handleUpdateFarm } = useFarmSectionUpdate();

  const { schema } = useYupSchema({
    dailyParameter: Yup.object().shape({
      name: Yup.string().required(trans('t_required')).label('t_name'),
      unit: Yup.string().required(trans('t_required')).label('t_unit'),
      time: Yup.string().required(trans('t_required')).label('t_time_of_day')
    })
  });

  const timeOptions = [
    { value: 'AM', label: 'AM' },
    { value: 'PM', label: 'PM' },
    { value: 'anytime', label: trans('t_anytime') }
  ];

  const {
    control,
    register,
    setError,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<FormValues>({
    resolver: yupResolver(schema) as YupResolverType<FormValues>,
    defaultValues: { dailyParameter: dailyParam }
  });

  const handleOnCancel = () => {
    reset({ dailyParameter: dailyParam });
    onClose();
    trackAction(actionsName.farmConfigDailyParamsCancelClicked).then();
  };

  const handleOnSubmit = handleSubmit((data: FormValues) => {
    const { dailyParameter } = data;
    const theNameAlreadyExist = farmDailyParams.some(
      (param) => param.name.toLowerCase() === dailyParameter.name.toLowerCase() && param._id !== dailyParameter._id
    );

    if (theNameAlreadyExist) {
      setError('dailyParameter.name', {
        type: 'manual',
        message: trans('t_param_name_already_exists')
      });
      return;
    }

    const dailyParams = [...farmDailyParams];

    if (isEdit) {
      const index = dailyParams.findIndex((param) => param._id === dailyParameter._id);
      dailyParams[index] = dailyParameter;
    } else {
      dailyParams.push({
        ...dailyParameter,
        status: 'active'
      });
    }

    handleUpdateFarm(
      {
        filter: { farmIds: [farmId] },
        set: { dailyParameters: dailyParams }
      },
      () => {
        !isEdit && reset({ dailyParameter: { name: '', unit: undefined, time: undefined } }, { keepDirtyValues: true });
        onClose();

        trackAction(actionsName.farmConfigDailyParamsSaveClicked).then();
      }
    );
  });

  return (
    <DialogRoot
      open={open}
      onOpenChange={(e) => {
        e.open ? onOpen() : handleOnCancel();
      }}
      restoreFocus={false}
      size='lg'
    >
      <Box onClick={onOpen}>{children}</Box>

      <DialogContent data-cy='add-parameter-modal' overflow='visible' {...modalContentProps}>
        <DialogTitle mb='2lg'>
          {isEdit ? trans('t_edit') : trans('t_add')} {trans('t_parameter')}
        </DialogTitle>
        <DialogBody {...modalBodyProps}>
          <chakra.form
            display='flex'
            flexDirection='column'
            gap='sm-alt'
            onSubmit={(event) => {
              event.stopPropagation();
              handleOnSubmit(event).then();
            }}
            noValidate
            data-cy='add-parameter-form'
          >
            <HarvestFormOneRow label={trans('t_name')}>
              <BaseFormInput
                w='188px'
                id='dailyParameter.name'
                name='dailyParameter.name'
                placeholder={trans('t_type_name')}
                required
                formControlProps={{ w: '100%' }}
                {...register('dailyParameter.name')}
                error={errors?.dailyParameter?.name?.message}
              />
            </HarvestFormOneRow>
            <HarvestFormOneRow label={trans('t_unit')}>
              <FormControlReactSelect
                id='dailyParameter.unit'
                name='dailyParameter.unit'
                control={control}
                placeholder={`${trans('t_select')}...`}
                options={unitOptions}
                error={errors?.dailyParameter?.unit?.message}
                isSearchable={false}
                selectControlStyles={harvestSelectControlStyles}
              />
            </HarvestFormOneRow>
            <HarvestFormOneRow label={trans('t_time_of_day')} borderBottom='none' pb={0}>
              <FormControlReactSelect
                id='dailyParameter.time'
                name='dailyParameter.time'
                control={control}
                placeholder={`${trans('t_select')}...`}
                options={timeOptions}
                error={errors?.dailyParameter?.time?.message}
                isSearchable={false}
                selectControlStyles={harvestSelectControlStyles}
              />
            </HarvestFormOneRow>

            <Flex justify='flex-end' gap='sm-alt' pos='absolute' bottom='24px' insetEnd='24px'>
              <BaseButton
                variant='secondary'
                bgColor='transparent'
                onClick={() => {
                  reset();
                  handleOnCancel();
                }}
              >
                {trans('t_cancel')}
              </BaseButton>
              <BaseButton
                type={'submit'}
                loading={isUpdating}
                onClick={() => {
                  isEdit && onOpen();
                }}
              >
                {trans('t_save')}
              </BaseButton>
            </Flex>
          </chakra.form>
        </DialogBody>
      </DialogContent>
    </DialogRoot>
  );
}
