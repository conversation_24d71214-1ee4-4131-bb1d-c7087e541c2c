import { Flex, Separator, Text } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { BaseFormNumberInput, BaseFormNumberInputProps } from '@components/form/base-form-number-input';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { formatDate } from '@utils/date';
import { numberTransform, useYupSchema, Yup, yupFormResolver, YupResolverType } from '@utils/yup';
import { RefObject, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { SettingBaseCard } from '@screens/settings/components/setting-base-card';
import { InfoIconToolTip } from '@components/tooltip/info-icon-tool-tip';
import {
  formActionCancelButtonStyles,
  formActionContainerStyles,
  formActionSubmitButtonStyles
} from '@screens/settings/constants/setting-base-card';
import { useFarmSectionUpdate } from '@screens/settings/hooks/use-farm-section-update';
import { AdminOnlyWrapper } from '@components/permission-wrappers/admin-only-wrapper';
import { actionsName } from '@utils/segment';
import { useAnalytics } from '@hooks/use-analytics';
import isUndefined from 'lodash/isUndefined';
import { convertUnitByDivision, convertUnitByMultiplication } from '@utils/number';

const defaultMaxDaysOfProduction = 250;

const defaultMaxWeight = 60;

const defaultMaxFcr = 3;
const defaultMaxProfitHa = 10000;
const defaultMaxCostOverhead = 10000;
const defaultMinValue = 0;

interface ProductionProps {
  successCallback?: () => void;
  submitRef?: RefObject<HTMLButtonElement | null>;
}

export function Production(props: ProductionProps) {
  const { trackAction } = useAnalytics();
  const { successCallback, submitRef } = props;
  const user = useAppSelector((state) => state.auth.user);
  const lang = useAppSelector((state) => state.app.lang);

  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);

  const [isEditing, setIsEditing] = useState(false);
  const [forceRender, setForceRender] = useState(0);
  const { currentFarm, isLoading: isLoadingUpdateFarm, handleUpdateFarm } = useFarmSectionUpdate();

  const { trans } = getTrans();
  const { email = '', firstName = '', lastName = '' } = user ?? {};
  const userName = firstName || lastName ? `${firstName ?? ''} ${lastName ?? ''}` : (email ?? '');
  const { growthTarget, _id: farmId } = currentFarm ?? {};
  const {
    weight,
    productionDays,
    fcr,
    costOverhead,
    costPerPound,
    profitPerHaPerDay,
    updatedAt,
    updatedBy,
    biomassLbsHa
  } = growthTarget ?? {};

  const { schema } = useYupSchema({
    weight: Yup.number()
      .transform(numberTransform)
      .positive()
      .min(defaultMinValue)
      .max(defaultMaxWeight)
      .required()
      .label('t_harvest_weight_g'),
    productionDays: Yup.number()
      .transform(numberTransform)
      .positive()
      .min(defaultMinValue)
      .max(defaultMaxDaysOfProduction)
      .required()
      .label('t_days_of_production'),
    fcr: Yup.number().transform(numberTransform).positive().max(defaultMaxFcr).required().label('t_fcr'),
    biomassLbsHa: Yup.number()
      .transform(numberTransform)
      .positive()
      .min(defaultMinValue)
      .required()
      .label('t_survival'),
    costPerPound: Yup.number()
      .transform(numberTransform)
      .positive()
      .min(defaultMinValue)
      .max(300)
      .required()
      .label(isBiomassUnitLbs ? 't_cost_per_pound' : 't_cost_per_kg'),
    profitPerHaPerDay: Yup.number()
      .transform(numberTransform)
      .positive()
      .min(defaultMinValue)
      .max(defaultMaxProfitHa)
      .nullable()
      .label('t_profit_include_dry_days_ha_day'),
    costOverhead: Yup.number()
      .transform(numberTransform)
      .positive()
      .min(defaultMinValue)
      .max(defaultMaxCostOverhead)
      .required()
      .label('t_overhead_cost_ha_day')
  });

  type FormValues = Parameters<typeof handleUpdateFarm>[0]['set']['growthTarget'];
  const { reset, control, handleSubmit } = useForm<FormValues>({
    resolver: yupFormResolver(schema) as YupResolverType<FormValues>
  });

  const handleCancel = () => {
    reset({
      weight,
      productionDays,
      fcr,
      biomassLbsHa: convertUnitByDivision(biomassLbsHa, unitsConfig?.biomass),
      costPerPound: convertUnitByMultiplication(costPerPound, unitsConfig?.biomass),
      profitPerHaPerDay,
      costOverhead
    });
    setIsEditing(false);
    trackAction(actionsName.farmConfigProductionTargetsCancelClicked).then();
  };

  const onSubmit = handleSubmit((data: FormValues) => {
    handleUpdateFarm(
      {
        filter: {
          farmIds: [farmId]
        },
        set: {
          growthTarget: {
            ...data,
            updatedAt: new Date().toISOString(),
            updatedBy: userName,
            costPerPound: convertUnitByDivision(data?.costPerPound, unitsConfig?.biomass),
            biomassLbsHa: convertUnitByMultiplication(data?.biomassLbsHa, unitsConfig?.biomass)
          }
        }
      },
      () => {
        trackAction(actionsName.farmConfigProductionTargetsSaveClicked).then();
        setIsEditing(false);
        setForceRender((val) => val + 1);
        successCallback?.();
      }
    );
  });

  useEffect(() => {
    if (!farmId) return;

    handleCancel();
  }, [farmId]);

  const commonInputProps: Omit<BaseFormNumberInputProps, 'name' | 'control'> = {
    maxW: '100px',
    css: { '& .chakra-form__error-message': { whiteSpace: 'nowrap' } },
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: { base: 'md', sm: 'lg', md: 'xl' },
    formLabelProps: { m: 0, minW: { base: '150px', sm: '195px' } },
    inputProps: {
      disabled: !isEditing,
      textAlign: 'center',
      w: { base: '80px', sm: '100px' },
      size: 'sm',
      paddingRight: 'sm'
    }
  };

  return (
    <SettingBaseCard
      key={forceRender}
      title={trans('t_production_targets')}
      pos='relative'
      as='form'
      data-cy='production-targets-settings-card'
      noValidate
      onSubmit={onSubmit}
      isActive={isEditing}
      actionComponent={
        <>
          {!isEditing && (
            <BaseButton
              variant='link'
              size='sm'
              onClick={() => {
                setIsEditing(true);
                trackAction(actionsName.farmConfigProductionTargetsEditClicked).then();
              }}
              data-cy='edit-btn'
              fontWeight={500}
            >
              {trans('t_edit')}
            </BaseButton>
          )}
        </>
      }
    >
      {updatedAt && (
        <Text fontSize='md' mb='md-alt' color='gray.700'>
          {updatedBy
            ? trans('t_last_updated_on_by', {
                date: formatDate({ locale: lang, date: updatedAt, format: 'LLL dd, yyyy' }),
                name: updatedBy
              })
            : `${trans('t_last_updated_on')} ${formatDate({ locale: lang, date: updatedAt, format: 'LLL dd, yyyy' })}`}
        </Text>
      )}

      <Flex direction='column' gap='sm-alt'>
        <Flex align='center' gap='xs-alt'>
          <Text fontSize='md' fontWeight={500}>
            {trans('t_target_production_cycle')}
          </Text>
          <InfoIconToolTip>
            <Text>{trans('t_target_config_tooltip')}</Text>
          </InfoIconToolTip>
        </Flex>

        <BaseFormNumberInput
          required
          name='weight'
          control={control}
          label={trans('t_harvest_weight_g')}
          {...commonInputProps}
          numericFormatProps={{ fixedDecimalScale: true, decimalScale: 1 }}
        />

        <BaseFormNumberInput
          required
          control={control}
          name='productionDays'
          label={trans('t_cycle_length')}
          {...commonInputProps}
          numericFormatProps={{ decimalScale: 0, suffix: ` ${trans('t_days')}` }}
        />

        <BaseFormNumberInput
          required
          control={control}
          name='fcr'
          label={trans('t_fcr')}
          {...commonInputProps}
          numericFormatProps={{ decimalScale: 2 }}
        />

        <BaseFormNumberInput
          required
          control={control}
          name='biomassLbsHa'
          label={isBiomassUnitLbs ? trans('t_biomass_lb_ha') : trans('t_biomass_kg_ha')}
          {...commonInputProps}
          numericFormatProps={{
            decimalScale: 0,
            suffix: ` ${isBiomassUnitLbs ? trans('t_lb_over_ha') : trans('t_kg_over_ha')}`
          }}
        />

        <BaseFormNumberInput
          required
          control={control}
          name='costPerPound'
          label={isBiomassUnitLbs ? trans('t_cost_per_pound') : trans('t_cost_per_kg')}
          {...commonInputProps}
          numericFormatProps={{ fixedDecimalScale: true, decimalScale: 2, prefix: '$' }}
        />

        <AdminOnlyWrapper>
          <BaseFormNumberInput
            required
            control={control}
            name='profitPerHaPerDay'
            label={trans('t_profit_include_dry_days_ha_day')}
            {...commonInputProps}
            numericFormatProps={{ fixedDecimalScale: true, decimalScale: 2, prefix: '$' }}
          />
        </AdminOnlyWrapper>
      </Flex>

      <Separator my='md' />
      <Flex direction='column' gap='sm-alt'>
        <Text>{trans('t_cost')}</Text>

        <BaseFormNumberInput
          required
          name='costOverhead'
          control={control}
          label={
            <Flex align='center' gap='xs-alt'>
              <Text fontSize='md'>{trans('t_overhead_cost_ha_day')}</Text>
              <InfoIconToolTip>
                <Text lineHeight='base'>{trans('t_overhead_cost_ha_day_tooltip')}</Text>
              </InfoIconToolTip>
            </Flex>
          }
          {...commonInputProps}
          numericFormatProps={{ fixedDecimalScale: true, decimalScale: 2, prefix: '$' }}
        />
      </Flex>

      {isEditing && (
        <Flex {...formActionContainerStyles} data-cy='harvest-optimization-actions'>
          <BaseButton {...formActionCancelButtonStyles} data-cy='cancel-btn' onClick={handleCancel}>
            {trans('t_cancel')}
          </BaseButton>
          <BaseButton {...formActionSubmitButtonStyles} type='submit' loading={isLoadingUpdateFarm} data-cy='save-btn'>
            {trans('t_save_changes')}
          </BaseButton>
        </Flex>
      )}
      {!!submitRef && (
        <BaseButton ref={submitRef} type='submit' display='none'>
          {trans('t_save_changes')}
        </BaseButton>
      )}
    </SettingBaseCard>
  );
}
