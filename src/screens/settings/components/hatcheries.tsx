import { chakra, Flex, Input, Text } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { BaseFormInput } from '@components/form/base-form-input';
import { yupResolver } from '@hookform/resolvers/yup';
import { getTrans } from '@i18n/get-trans';
import { useYupSchema, Yup, YupResolverType } from '@utils/yup';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import { useFarmSectionUpdate } from '@screens/settings/hooks/use-farm-section-update';
import { SettingBaseCard } from '@screens/settings/components/setting-base-card';
import {
  formActionCancelButtonStyles,
  formActionContainerStyles,
  formActionSubmitButtonStyles
} from '@screens/settings/constants/setting-base-card';

import { AddCircleIcon } from '@screens/settings/icons/add-circle-icon';
import { CancelSmallMinorIcon } from '@screens/settings/icons/cancel-small-minor-icon';
import { useListFarmsApi } from '@screens/farm/hooks/use-list-farms-api';
import { GroupConfigConfirmationModal } from '@screens/settings/components/group-config-confirmation-modal';
import { SectionLoader } from '@components/loaders/section-loader';
import { useSetFarmBookRefererInRedux } from '@components/farm-book/hooks/use-set-farm-book-referer-in-redux';
import { useAnalytics } from '@hooks/use-analytics';
import { actionsName } from '@utils/segment';

type HatcheryType = { name: string };

export function Hatcheries() {
  const { trans } = getTrans();

  const { trackAction } = useAnalytics();
  useSetFarmBookRefererInRedux('nurseries');
  const [isEditing, setIsEditing] = useState(false);
  const [forceRender, setForceRender] = useState(0);
  const [isConfirming, setIsConfirming] = useState(false);

  const { currentFarm, farmList, isLoading: isFarmUpdating, handleUpdateFarm } = useFarmSectionUpdate();
  const [{ isLoading: areFarmsLoading }, listFarmsApi] = useListFarmsApi();
  const currentFarmFromList = farmList?.find((farm) => farm._id === currentFarm?._id) ?? currentFarm;

  const hatchery = currentFarmFromList?.otherConfig?.hatchery ?? [];

  const handeOnSubmit = (hatchery: HatcheryType[], selectedFarmIds: string[], cb?: () => void) => {
    handleUpdateFarm(
      {
        filter: {
          farmIds: selectedFarmIds
        },
        set: {
          otherConfig: {
            ...currentFarmFromList?.otherConfig,
            hatchery
          }
        }
      },
      () => {
        listFarmsApi({
          params: {
            page: { size: 1000, current: 1 },
            sort: [{ field: 'name', order: 'asc' }]
          }
        });
        cb?.();
        trackAction(actionsName.farmConfigHatcheriesSaveClicked).then();
        setIsEditing(false);
        setForceRender((val) => val + 1);
        setIsConfirming(false);
      }
    );
  };
  const isLoading = isFarmUpdating || areFarmsLoading;
  return (
    <SettingBaseCard
      title={trans('t_lab_source')}
      pos='relative'
      flex={1}
      isActive={isEditing}
      actionComponent={
        <>
          {!isEditing && (
            <BaseButton
              size='sm'
              variant='link'
              disabled={isEditing}
              onClick={() => {
                setIsEditing(true);
                trackAction(actionsName.farmConfigHatcheriesEditClicked).then();
              }}
              fontWeight={500}
            >
              {trans('t_edit')}
            </BaseButton>
          )}
        </>
      }
    >
      <SectionLoader isLoading={isLoading} />

      {!hatchery.length && !isEditing && <Text fontSize='md'>{trans('t_no_lab_names')}</Text>}

      <Form
        key={forceRender}
        defaultValues={hatchery}
        isLoading={isLoading}
        isEditing={isEditing}
        onSubmit={handeOnSubmit}
        onCancel={() => {
          setIsEditing(false);
          trackAction(actionsName.farmConfigHatcheriesCancelClicked).then();
        }}
        isConfirming={isConfirming}
        setIsConfirming={setIsConfirming}
      />
    </SettingBaseCard>
  );
}

type FormPropsType = {
  isEditing: boolean;
  defaultValues?: HatcheryType[];
  isLoading?: boolean;
  onSubmit: (value: HatcheryType[], selectedFarmIds: string[], cb?: () => void) => void;
  onCancel: () => void;
  isConfirming: boolean;
  setIsConfirming: Dispatch<SetStateAction<boolean>>;
};

type FormValues = { hatchery: HatcheryType[] };

function Form(props: FormPropsType) {
  const { isEditing, defaultValues, isLoading, onSubmit, onCancel, isConfirming, setIsConfirming } = props;
  const { trans } = getTrans();
  const [inputValue, setInputValue] = useState('');
  const { schema } = useYupSchema({
    hatchery: Yup.array().of(
      Yup.object().shape({
        name: Yup.string().required().label('t_name')
      })
    )
  });

  const {
    register,
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<FormValues>({
    resolver: yupResolver(schema) as YupResolverType<FormValues>,
    defaultValues: { hatchery: defaultValues }
  });

  useEffect(() => {
    if (!defaultValues?.length) return;
    reset({ hatchery: defaultValues });
  }, [defaultValues]);

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'hatchery'
  });

  const handleFormSubmit = (selectedFarmIds: string[]) => {
    handleSubmit((data: FormValues) => {
      onSubmit(data.hatchery, selectedFarmIds);
    })();
  };

  const handleCancel = () => {
    reset({ hatchery: defaultValues });
    onCancel();
  };

  const hasOneItem = fields.length === 1;
  const isMax = fields.length === 100;

  return (
    <chakra.form noValidate>
      <GroupConfigConfirmationModal
        isLoading={isLoading}
        isOpen={isConfirming}
        onClose={() => setIsConfirming(false)}
        onSubmit={handleFormSubmit}
      />
      {!isMax && isEditing && (
        <Flex gap='sm-alt'>
          <Input
            required
            id='name'
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            size='sm'
            borderRadius='base'
            mb={fields.length > 0 ? 'md-alt' : 0}
            disabled={!isEditing}
            placeholder={trans('t_add_lab_name')}
            onKeyUp={(event) => {
              if (event.key === 'Enter' && inputValue) {
                append({ name: inputValue });
                setInputValue('');
              }
            }}
          />
          <AddCircleIcon
            cursor={inputValue ? 'pointer' : 'not-allowed'}
            color={inputValue ? 'gray.700' : 'gray.400'}
            boxSize='20px'
            mt='xs-alt'
            _hover={{ opacity: 0.8 }}
            onClick={() => {
              if (!inputValue) return;
              append({ name: inputValue });
              setInputValue('');
            }}
          />
        </Flex>
      )}

      <Flex flexDirection='column' gap='sm-alt'>
        {fields.map((field, index) => {
          return (
            <Flex key={field.id} gap='sm-alt'>
              <BaseFormInput
                required
                formControlProps={{ w: '100%' }}
                id={field.id}
                key={field.id}
                name={`hatchery.${index}.name`}
                size='sm'
                error={errors?.hatchery?.[index]?.name?.message}
                {...register(`hatchery.${index}.name`)}
                disabled
                _disabled={{
                  bgColor: 'gray.200',
                  color: 'text.gray',
                  w: '100%',
                  border: 'none',
                  borderRadius: 'base',
                  cursor: 'default'
                }}
              />
              {isEditing && !hasOneItem && (
                <CancelSmallMinorIcon
                  mt='2xs'
                  cursor='pointer'
                  color='primary'
                  _hover={{ opacity: 0.8 }}
                  onClick={() => remove(index)}
                />
              )}
            </Flex>
          );
        })}
      </Flex>
      {errors?.hatchery?.message && (
        <Text color='red.500' fontSize='md'>
          {errors?.hatchery?.message}
        </Text>
      )}
      {isEditing && (
        <Flex {...formActionContainerStyles}>
          <BaseButton {...formActionCancelButtonStyles} onClick={handleCancel} disabled={isLoading}>
            {trans('t_cancel')}
          </BaseButton>
          <BaseButton
            {...formActionSubmitButtonStyles}
            onClick={() => setIsConfirming(true)}
            disabled={!fields?.length}
            loading={isLoading}
          >
            {trans('t_save_changes')}
          </BaseButton>
        </Flex>
      )}
    </chakra.form>
  );
}
