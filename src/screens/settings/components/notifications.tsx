import { chakra, Field, Flex, FlexProps, InputProps, Text } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { BaseFormCheckbox } from '@components/form/base-form-checkbox';
import { BaseFormNumberInput } from '@components/form/base-form-number-input';
import { getTrans } from '@i18n/get-trans';
import { numberTransform, useYupSchema, Yup, yupFormResolver, YupResolverType } from '@utils/yup';
import { Controller, DeepPartial, useForm } from 'react-hook-form';
import { useAppSelector } from '@redux/hooks';
import { TestFunction } from 'yup';
import { Switch } from '@components/ui/switch';
import { useFarmSectionUpdate } from '../hooks/use-farm-section-update';

type preferenceConfigType =
  | 'amDissolvedOxygen'
  | 'pmDissolvedOxygen'
  | 'nearingCarryingCapacity'
  | 'reachingCarryingCapacity'
  | 'amWaterTemperature'
  | 'pmWaterTemperature'
  | 'temperatureChange';

type notificationPreferencesType = {
  userType: 'admin' | 'supervisor';
  preferences: {
    type: preferenceConfigType;
    enabled: boolean;
    emailNotification: boolean;
    pushNotification: boolean;
    minValue: number;
    maxValue: number;
  }[];
};

const getDefaultValues = (data: notificationPreferencesType[]): DeepPartial<FormType> => {
  if (!data?.length) return {};

  const defaultValues: FormType = {} as FormType;

  const addOrUpdateDefaultValue = (
    userType: notificationPreferencesType['userType'],
    preference: notificationPreferencesType['preferences'][0]
  ) => {
    const { type, enabled, emailNotification, pushNotification, minValue, maxValue } = preference;

    if (defaultValues[type]) {
      if (userType === 'admin') defaultValues[type].admin = true;
      if (userType === 'supervisor') defaultValues[type].supervisor = true;
      return;
    }

    const sharedData = {
      enabled,
      admin: userType === 'admin',
      supervisor: userType === 'supervisor',
      emailNotification,
      pushNotification
    };

    if (type === 'amDissolvedOxygen') {
      defaultValues['amDissolvedOxygen'] = { ...sharedData, below: minValue, above: maxValue };
    }

    if (type === 'pmDissolvedOxygen') {
      defaultValues['pmDissolvedOxygen'] = { ...sharedData, below: minValue, above: maxValue };
    }

    if (type === 'nearingCarryingCapacity') {
      defaultValues['nearingCarryingCapacity'] = { ...sharedData, days: maxValue };
    }

    if (type === 'reachingCarryingCapacity') {
      defaultValues['reachingCarryingCapacity'] = { ...sharedData, pushNotification };
    }

    if (type === 'amWaterTemperature') {
      defaultValues['amWaterTemperature'] = { ...sharedData, below: minValue, above: maxValue };
    }
    if (type === 'pmWaterTemperature') {
      defaultValues['pmWaterTemperature'] = { ...sharedData, below: minValue, above: maxValue };
    }

    if (type === 'temperatureChange') {
      defaultValues['temperatureChange'] = { ...sharedData, temperature: maxValue };
    }
  };

  data.forEach(({ userType, preferences }) => {
    preferences.forEach((preference) => addOrUpdateDefaultValue(userType, preference));
  });

  return defaultValues;
};

export const notificationsAndRuleSelected: TestFunction = (options, { createError, path }) => {
  const { trans } = getTrans();
  const { enabled, emailNotification, pushNotification, admin, supervisor } = options as Partial<
    FormType['amDissolvedOxygen']
  >;

  if (!enabled) return true;

  if (!emailNotification && !pushNotification) {
    return createError({ path, message: trans('t_notifications_and_rule_error') });
  }

  if (!admin && !supervisor) {
    return createError({ path, message: trans('t_notifications_and_rule_error') });
  }

  return true;
};

export const belowFilterTest: TestFunction = (below, { createError, parent, path }) => {
  const { trans } = getTrans();

  if (!parent.enabled) return true;

  if (!below && !parent.above)
    return createError({
      path,
      message: trans('yup_is_required', { label: trans('t_below') })
    });

  if (!below || !parent.above) return true;

  if (Number(below) >= Number(parent.above)) {
    return createError({
      path,
      message: trans('t_yup_should_be_less_than_max', { label: '', max: parent.above })
    });
  }

  return true;
};

type FormType = {
  amDissolvedOxygen: {
    enabled: boolean;
    emailNotification: boolean;
    pushNotification: boolean;
    admin: boolean;
    supervisor: boolean;
    below: number | string;
    above: number | string;
  };
  pmDissolvedOxygen: {
    enabled: boolean;
    emailNotification: boolean;
    pushNotification: boolean;
    admin: boolean;
    supervisor: boolean;
    below: number | string;
    above: number | string;
  };
  nearingCarryingCapacity: {
    enabled: boolean;
    emailNotification: boolean;
    pushNotification: boolean;
    admin: boolean;
    supervisor: boolean;
    days: number | string;
  };
  reachingCarryingCapacity: {
    enabled: boolean;
    emailNotification: boolean;
    pushNotification: boolean;
    admin: boolean;
    supervisor: boolean;
  };
  amWaterTemperature: {
    enabled: boolean;
    emailNotification: boolean;
    pushNotification: boolean;
    admin: boolean;
    supervisor: boolean;
    below: number | string;
    above: number | string;
  };
  pmWaterTemperature: {
    enabled: boolean;
    emailNotification: boolean;
    pushNotification: boolean;
    admin: boolean;
    supervisor: boolean;
    below: number | string;
    above: number | string;
  };
  temperatureChange: {
    enabled: boolean;
    emailNotification: boolean;
    pushNotification: boolean;
    admin: boolean;
    supervisor: boolean;
    temperature: number | string;
  };
};

export function Notifications() {
  const { trans } = getTrans();

  const { currentFarm } = useAppSelector((state) => state.farm);
  const { isLoading, handleUpdateFarm } = useFarmSectionUpdate();

  const isVisionOnlyFarm = currentFarm?.metadata?.productOffering === 'visionOnly';

  const defaultValues = getDefaultValues(currentFarm?.otherConfig?.notificationPreferences ?? []);

  const { schema } = useYupSchema({
    amDissolvedOxygen: Yup.object()
      .shape({
        enabled: Yup.boolean(),
        emailNotification: Yup.boolean().label('t_less_than'),
        pushNotification: Yup.boolean(),
        admin: Yup.boolean(),
        supervisor: Yup.boolean(),
        below: Yup.number().transform(numberTransform).nullable().min(0).test(belowFilterTest).label('t_below'),
        above: Yup.number().transform(numberTransform).nullable().min(0).label('t_above')
      })
      .test(notificationsAndRuleSelected),

    pmDissolvedOxygen: Yup.object()
      .shape({
        enabled: Yup.boolean(),
        emailNotification: Yup.boolean().label('t_less_than'),
        pushNotification: Yup.boolean(),
        admin: Yup.boolean(),
        supervisor: Yup.boolean(),
        below: Yup.number().transform(numberTransform).nullable().min(0).test(belowFilterTest).label('t_below'),
        above: Yup.number().transform(numberTransform).nullable().min(0).label('t_above')
      })
      .test(notificationsAndRuleSelected),

    nearingCarryingCapacity: Yup.object()
      .shape({
        enabled: Yup.boolean(),
        emailNotification: Yup.boolean(),
        pushNotification: Yup.boolean(),
        admin: Yup.boolean(),
        supervisor: Yup.boolean(),
        days: Yup.number().when('enabled', {
          is: true,
          then: () => Yup.number().transform(numberTransform).nullable().required().min(1).label('t_days'),
          otherwise: () => Yup.number().transform(numberTransform).nullable().label('t_days')
        })
      })
      .test(notificationsAndRuleSelected),

    reachingCarryingCapacity: Yup.object()
      .shape({
        enabled: Yup.boolean(),
        emailNotification: Yup.boolean(),
        pushNotification: Yup.boolean(),
        admin: Yup.boolean(),
        supervisor: Yup.boolean()
      })
      .test(notificationsAndRuleSelected),

    amWaterTemperature: Yup.object()
      .shape({
        enabled: Yup.boolean(),
        emailNotification: Yup.boolean(),
        pushNotification: Yup.boolean(),
        admin: Yup.boolean(),
        supervisor: Yup.boolean(),
        below: Yup.number().transform(numberTransform).nullable().min(0).test(belowFilterTest).label('t_below'),
        above: Yup.number().transform(numberTransform).nullable().min(0).label('t_above')
      })
      .test(notificationsAndRuleSelected),

    pmWaterTemperature: Yup.object()
      .shape({
        enabled: Yup.boolean(),
        emailNotification: Yup.boolean(),
        pushNotification: Yup.boolean(),
        admin: Yup.boolean(),
        supervisor: Yup.boolean(),
        below: Yup.number().transform(numberTransform).nullable().min(0).test(belowFilterTest).label('t_below'),
        above: Yup.number().transform(numberTransform).nullable().min(0).label('t_above')
      })
      .test(notificationsAndRuleSelected),

    temperatureChange: Yup.object()
      .shape({
        enabled: Yup.boolean(),
        emailNotification: Yup.boolean(),
        pushNotification: Yup.boolean(),
        admin: Yup.boolean(),
        supervisor: Yup.boolean(),
        temperature: Yup.number().when('enabled', {
          is: true,
          then: () => Yup.number().transform(numberTransform).nullable().required().min(0).label('t_temperature'),
          otherwise: () => Yup.number().transform(numberTransform).nullable().min(0).label('t_temperature')
        })
      })
      .test(notificationsAndRuleSelected)
  });

  const {
    control,
    watch,
    setValue,
    handleSubmit,
    formState: { errors }
  } = useForm<FormType>({
    mode: 'onChange',
    defaultValues,
    resolver: yupFormResolver(schema) as YupResolverType<FormType>
  });

  const onSubmit = (data: FormType) => {
    const adminPreferences = [];
    const supervisorPreferences = [];

    const {
      amDissolvedOxygen,
      pmDissolvedOxygen,
      nearingCarryingCapacity,
      reachingCarryingCapacity,
      amWaterTemperature,
      pmWaterTemperature,
      temperatureChange
    } = data;

    if (amDissolvedOxygen.admin || amDissolvedOxygen.supervisor) {
      const { admin, supervisor, enabled, emailNotification, pushNotification, below, above } = amDissolvedOxygen;

      const hasRangeError = below >= above;
      const preferences = {
        type: 'amDissolvedOxygen',
        enabled,
        emailNotification,
        pushNotification,
        minValue: !hasRangeError ? below : null,
        maxValue: !hasRangeError ? above : null
      };

      if (admin) adminPreferences.push(preferences);
      if (supervisor) supervisorPreferences.push(preferences);
    }

    if (pmDissolvedOxygen.admin || pmDissolvedOxygen.supervisor) {
      const { admin, supervisor, enabled, emailNotification, pushNotification, below, above } = pmDissolvedOxygen;

      const hasRangeError = below >= above;
      const preferences = {
        type: 'pmDissolvedOxygen',
        enabled,
        emailNotification,
        pushNotification,
        minValue: !hasRangeError ? below : null,
        maxValue: !hasRangeError ? above : null
      };

      if (admin) adminPreferences.push(preferences);
      if (supervisor) supervisorPreferences.push(preferences);
    }

    if (nearingCarryingCapacity.admin || nearingCarryingCapacity.supervisor) {
      const { admin, supervisor, enabled, emailNotification, pushNotification, days } = nearingCarryingCapacity;
      const preferences = {
        type: 'nearingCarryingCapacity',
        enabled,
        emailNotification,
        pushNotification,
        maxValue: days
      };
      if (admin) adminPreferences.push(preferences);
      if (supervisor) supervisorPreferences.push(preferences);
    }

    if (reachingCarryingCapacity.admin || reachingCarryingCapacity.supervisor) {
      const { admin, supervisor, enabled, emailNotification, pushNotification } = reachingCarryingCapacity;
      const preferences = { type: 'reachingCarryingCapacity', enabled, emailNotification, pushNotification };
      if (admin) adminPreferences.push(preferences);
      if (supervisor) supervisorPreferences.push(preferences);
    }

    if (amWaterTemperature.admin || amWaterTemperature.supervisor) {
      const { admin, supervisor, enabled, emailNotification, pushNotification, below, above } = amWaterTemperature;
      const hasRangeError = below >= above;
      const preferences = {
        type: 'amWaterTemperature',
        enabled,
        emailNotification,
        pushNotification,
        minValue: !hasRangeError ? below : null,
        maxValue: !hasRangeError ? above : null
      };

      if (admin) adminPreferences.push(preferences);
      if (supervisor) supervisorPreferences.push(preferences);
    }

    if (pmWaterTemperature.admin || pmWaterTemperature.supervisor) {
      const { admin, supervisor, enabled, emailNotification, pushNotification, below, above } = pmWaterTemperature;
      const hasRangeError = below >= above;
      const preferences = {
        type: 'pmWaterTemperature',
        enabled,
        emailNotification,
        pushNotification,
        minValue: !hasRangeError ? below : null,
        maxValue: !hasRangeError ? above : null
      };

      if (admin) adminPreferences.push(preferences);
      if (supervisor) supervisorPreferences.push(preferences);
    }

    if (temperatureChange.admin || temperatureChange.supervisor) {
      const { admin, supervisor, enabled, emailNotification, pushNotification, temperature } = temperatureChange;
      const preferences = {
        type: 'temperatureChange',
        enabled,
        emailNotification,
        pushNotification,
        maxValue: temperature
      };

      if (admin) adminPreferences.push(preferences);
      if (supervisor) supervisorPreferences.push(preferences);
    }

    handleUpdateFarm({
      filter: { farmIds: [currentFarm._id] },
      set: {
        otherConfig: {
          ...currentFarm.otherConfig,
          notificationPreferences: [
            { userType: 'admin', preferences: adminPreferences },
            { userType: 'supervisor', preferences: supervisorPreferences }
          ]
        }
      }
    });
  };

  const amDissolvedOxygenChecked = watch('amDissolvedOxygen.enabled');
  const pmDissolvedOxygenChecked = watch('pmDissolvedOxygen.enabled');
  const nearingCarryingCapacityChecked = watch('nearingCarryingCapacity.enabled');
  const carryingCapacityReachedChecked = watch('reachingCarryingCapacity.enabled');
  const amWaterTemperatureChecked = watch('amWaterTemperature.enabled');
  const pmWaterTemperatureChecked = watch('pmWaterTemperature.enabled');

  const resetAmDissolvedValues = () => {
    setValue('amDissolvedOxygen.below', '');
    setValue('amDissolvedOxygen.above', '');
  };

  const resetPmDissolvedValues = () => {
    setValue('pmDissolvedOxygen.below', '');
    setValue('pmDissolvedOxygen.above', '');
  };

  const resetAmWaterTemperatureValues = () => {
    setValue('amWaterTemperature.below', '');
    setValue('amWaterTemperature.above', '');
  };

  const resetPmWaterTemperatureValues = () => {
    setValue('pmWaterTemperature.below', '');
    setValue('pmWaterTemperature.above', '');
  };

  const inputProps: InputProps = { w: '100px', ps: 'xs', size: 'md', rounded: 'lg-alt' };

  return (
    <chakra.form display='flex' flexDirection='column' gap='md' onSubmit={handleSubmit(onSubmit)}>
      <SectionRowContainer>
        <SectionHeaderRowContainer>
          <Controller
            name='amDissolvedOxygen.enabled'
            control={control}
            render={({ field: { name, value, onChange, onBlur } }) => (
              <Field.Root w='max-content' invalid={!!errors.amDissolvedOxygen?.enabled?.message}>
                <Switch
                  name={name}
                  checked={value}
                  onCheckedChange={({ checked }) => onChange(checked)}
                  inputProps={{ onBlur }}
                  thumbProps={{ w: '15px', h: '15px' }}
                  controlProps={{
                    bg: amDissolvedOxygenChecked ? 'icon.gray' : 'icon.gray.disabled',
                    pt: '2.5px',
                    ps: amDissolvedOxygenChecked ? '3xs' : '2xs'
                  }}
                >
                  <Text size='heavy100'>{trans('t_dissolved_am_oxygen_unit')}</Text>
                </Switch>
              </Field.Root>
            )}
          />

          <CheckboxGroupContainer>
            <CheckboxRowContainer>
              <Controller
                name='amDissolvedOxygen.emailNotification'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      display={'none'}
                      id='dissolvedOxygen.emailNotification'
                      label={
                        <Text size='light100' fontWeight={400}>
                          {trans('t_email')}
                        </Text>
                      }
                      formControlProps={{ w: '100%', display: 'flex', flexDir: 'column', justifyContent: 'center' }}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />
              <Controller
                name='amDissolvedOxygen.pushNotification'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      id='amDissolvedOxygen.pushNotification'
                      label={
                        <Text size='light100' fontWeight={400}>
                          {trans('t_push')}
                        </Text>
                      }
                      formControlProps={{ w: '100%', display: 'flex', flexDir: 'column', justifyContent: 'center' }}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />
            </CheckboxRowContainer>

            <CheckboxRowContainer>
              <Controller
                name='amDissolvedOxygen.admin'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      id='amDissolvedOxygen.admin'
                      label={
                        <Text size='light100' fontWeight={400}>
                          {trans('t_admin')}
                        </Text>
                      }
                      formControlProps={{ w: '100%', display: 'flex', flexDir: 'column', justifyContent: 'center' }}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />

              <Controller
                name='amDissolvedOxygen.supervisor'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      id='amDissolvedOxygen.supervisor'
                      label={
                        <Text size='light100' whiteSpace='nowrap'>
                          {trans('t_operations_supervisor')}
                        </Text>
                      }
                      formControlProps={{ w: '100%', display: 'flex', flexDir: 'column', justifyContent: 'center' }}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />
            </CheckboxRowContainer>
          </CheckboxGroupContainer>
        </SectionHeaderRowContainer>

        <InputsContainer>
          <Text as='span' size='label200'>
            {trans('t_below')}
          </Text>
          <BaseFormNumberInput
            control={control}
            name='amDissolvedOxygen.below'
            w='100px'
            numericFormatProps={{ decimalScale: 0 }}
            inputRightElement={
              <Text size='label200' color='gray.700'>
                {trans('t_mg_per_L')}
              </Text>
            }
            inputProps={{ ...inputProps, paddingEnd: '58px', placeholder: trans('t_min') }}
            disabled={!amDissolvedOxygenChecked}
          />

          <Text as='span' size='label200'>
            {trans('t_above')}
          </Text>

          <BaseFormNumberInput
            control={control}
            name='amDissolvedOxygen.above'
            w='100px'
            numericFormatProps={{ decimalScale: 0 }}
            inputRightElement={
              <Text size='label200' color='gray.700'>
                {trans('t_mg_per_L')}
              </Text>
            }
            inputProps={{ ...inputProps, paddingEnd: '58px', placeholder: trans('t_max') }}
            disabled={!amDissolvedOxygenChecked}
          />

          <BaseButton size='sm' variant='link' onClick={resetAmDissolvedValues} disabled={!amDissolvedOxygenChecked}>
            {trans('t_clear')}
          </BaseButton>
        </InputsContainer>

        {errors?.amDissolvedOxygen && (
          <Text size='label200' color='red.400' pt='xs' textAlign='end'>
            {errors.amDissolvedOxygen.message}
          </Text>
        )}
      </SectionRowContainer>

      <SectionRowContainer>
        <SectionHeaderRowContainer>
          <Controller
            name='pmDissolvedOxygen.enabled'
            control={control}
            render={({ field: { name, value, onChange, onBlur } }) => (
              <Field.Root w='max-content' invalid={!!errors.amDissolvedOxygen?.enabled?.message}>
                <Switch
                  name={name}
                  checked={value}
                  onCheckedChange={({ checked }) => onChange(checked)}
                  inputProps={{ onBlur }}
                  thumbProps={{ w: '15px', h: '15px' }}
                  controlProps={{
                    bg: pmDissolvedOxygenChecked ? 'icon.gray' : 'icon.gray.disabled',
                    pt: '2.5px',
                    ps: pmDissolvedOxygenChecked ? '3xs' : '2xs'
                  }}
                >
                  <Text size='heavy100'>{trans('t_dissolved_pm_oxygen_unit')}</Text>
                </Switch>
              </Field.Root>
            )}
          />

          <CheckboxGroupContainer>
            <CheckboxRowContainer>
              <Controller
                name='pmDissolvedOxygen.emailNotification'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      display={'none'}
                      id='pmDissolvedOxygen.emailNotification'
                      label={
                        <Text size='light100' fontWeight={400}>
                          {trans('t_email')}
                        </Text>
                      }
                      formControlProps={{ w: '100%', display: 'flex', flexDir: 'column', justifyContent: 'center' }}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />
              <Controller
                name='pmDissolvedOxygen.pushNotification'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      id='pmDissolvedOxygen.pushNotification'
                      label={
                        <Text size='light100' fontWeight={400}>
                          {trans('t_push')}
                        </Text>
                      }
                      formControlProps={{ w: '100%', display: 'flex', flexDir: 'column', justifyContent: 'center' }}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />
            </CheckboxRowContainer>

            <CheckboxRowContainer>
              <Controller
                name='pmDissolvedOxygen.admin'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      id='pmDissolvedOxygen.admin'
                      label={
                        <Text size='light100' fontWeight={400}>
                          {trans('t_admin')}
                        </Text>
                      }
                      formControlProps={{ w: '100%', display: 'flex', flexDir: 'column', justifyContent: 'center' }}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />

              <Controller
                name='pmDissolvedOxygen.supervisor'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      id='pmDissolvedOxygen.supervisor'
                      label={
                        <Text size='light100' whiteSpace='nowrap'>
                          {trans('t_operations_supervisor')}
                        </Text>
                      }
                      formControlProps={{ w: '100%', display: 'flex', flexDir: 'column', justifyContent: 'center' }}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />
            </CheckboxRowContainer>
          </CheckboxGroupContainer>
        </SectionHeaderRowContainer>

        <InputsContainer>
          <Text as='span' size='label200'>
            {trans('t_below')}
          </Text>
          <BaseFormNumberInput
            control={control}
            name='pmDissolvedOxygen.below'
            w='100px'
            numericFormatProps={{ decimalScale: 0 }}
            inputRightElement={
              <Text size='label200' color='gray.700'>
                {trans('t_mg_per_L')}
              </Text>
            }
            inputProps={{ ...inputProps, paddingEnd: '58px', placeholder: trans('t_min') }}
            disabled={!pmDissolvedOxygenChecked}
          />

          <Text as='span' size='label200'>
            {trans('t_above')}
          </Text>

          <BaseFormNumberInput
            control={control}
            name='pmDissolvedOxygen.above'
            w='100px'
            numericFormatProps={{ decimalScale: 0 }}
            inputRightElement={
              <Text size='label200' color='gray.700'>
                {trans('t_mg_per_L')}
              </Text>
            }
            inputProps={{ ...inputProps, paddingEnd: '58px', placeholder: trans('t_max') }}
            disabled={!pmDissolvedOxygenChecked}
          />

          <BaseButton size='sm' variant='link' onClick={resetPmDissolvedValues} disabled={!amDissolvedOxygenChecked}>
            {trans('t_clear')}
          </BaseButton>
        </InputsContainer>

        {errors?.pmDissolvedOxygen && (
          <Text size='label200' color='red.400' pt='xs' textAlign='end'>
            {errors.pmDissolvedOxygen.message}
          </Text>
        )}
      </SectionRowContainer>

      <SectionRowContainer hidden={isVisionOnlyFarm}>
        <SectionHeaderRowContainer>
          <Controller
            name='nearingCarryingCapacity.enabled'
            control={control}
            render={({ field: { name, value, onChange, onBlur } }) => (
              <Field.Root w='max-content' invalid={!!errors.nearingCarryingCapacity?.enabled?.message}>
                <Switch
                  name={name}
                  checked={value}
                  onCheckedChange={({ checked }) => onChange(checked)}
                  inputProps={{ onBlur }}
                  thumbProps={{ w: '15px', h: '15px' }}
                  controlProps={{
                    bg: nearingCarryingCapacityChecked ? 'icon.gray' : 'icon.gray.disabled',
                    pt: '2.5px',
                    ps: nearingCarryingCapacityChecked ? '3xs' : '2xs'
                  }}
                >
                  <Text size='heavy100'>{trans('t_nearing_carrying_capacity')}</Text>
                </Switch>
              </Field.Root>
            )}
          />

          <CheckboxGroupContainer>
            <CheckboxRowContainer>
              <Controller
                name='nearingCarryingCapacity.emailNotification'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      display={'none'}
                      id='nearingCarryingCapacity.emailNotification'
                      label={
                        <Text size='light100' fontWeight={400}>
                          {trans('t_email')}
                        </Text>
                      }
                      formControlProps={{ w: '100%', display: 'flex', flexDir: 'column', justifyContent: 'center' }}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />

              <Controller
                name='nearingCarryingCapacity.pushNotification'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      id='nearingCarryingCapacity.pushNotification'
                      label={
                        <Text size='light100' fontWeight={400}>
                          {trans('t_push')}
                        </Text>
                      }
                      formControlProps={{ w: '100%', display: 'flex', flexDir: 'column', justifyContent: 'center' }}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />
            </CheckboxRowContainer>

            <CheckboxRowContainer>
              <Controller
                name='nearingCarryingCapacity.admin'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      id='nearingCarryingCapacity.admin'
                      label={
                        <Text size='light100' fontWeight={400}>
                          {trans('t_admin')}
                        </Text>
                      }
                      formControlProps={{ w: '100%', display: 'flex', flexDir: 'column', justifyContent: 'center' }}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />
              <Controller
                name='nearingCarryingCapacity.supervisor'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      id='nearingCarryingCapacity.supervisor'
                      label={
                        <Text size='light100' whiteSpace='nowrap'>
                          {trans('t_operations_supervisor')}
                        </Text>
                      }
                      formControlProps={{ w: '100%', display: 'flex', flexDir: 'column', justifyContent: 'center' }}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />
            </CheckboxRowContainer>
          </CheckboxGroupContainer>
        </SectionHeaderRowContainer>

        <InputsContainer>
          <Text as='span' size='label200'>
            {trans('t_in')}
          </Text>
          <BaseFormNumberInput
            control={control}
            name='nearingCarryingCapacity.days'
            w='100px'
            numericFormatProps={{ decimalScale: 0 }}
            inputRightElement={
              <Text size='label200' color='gray.700'>
                {trans('t_days')}
              </Text>
            }
            inputProps={inputProps}
          />

          <BaseButton size='sm' variant='link' onClick={() => setValue('nearingCarryingCapacity.days', '')}>
            {trans('t_clear')}
          </BaseButton>
        </InputsContainer>

        {errors?.nearingCarryingCapacity && (
          <Text size='label200' color='red.400' pt='xs' textAlign='end'>
            {errors.nearingCarryingCapacity.message}
          </Text>
        )}
      </SectionRowContainer>

      <SectionRowContainer hidden={isVisionOnlyFarm}>
        <SectionHeaderRowContainer>
          <Controller
            name='reachingCarryingCapacity.enabled'
            control={control}
            render={({ field: { name, value, onChange, onBlur } }) => (
              <Field.Root w='max-content' invalid={!!errors.reachingCarryingCapacity?.enabled?.message}>
                <Switch
                  name={name}
                  checked={value}
                  onCheckedChange={({ checked }) => onChange(checked)}
                  inputProps={{ onBlur }}
                  thumbProps={{ w: '15px', h: '15px' }}
                  controlProps={{
                    bg: carryingCapacityReachedChecked ? 'icon.gray' : 'icon.gray.disabled',
                    pt: '2.5px',
                    ps: carryingCapacityReachedChecked ? '3xs' : '2xs'
                  }}
                >
                  <Text size='heavy100'>{trans('t_carrying_capacity_reached')}</Text>
                </Switch>
              </Field.Root>
            )}
          />

          <CheckboxGroupContainer>
            <CheckboxRowContainer>
              <Controller
                name='reachingCarryingCapacity.emailNotification'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      display={'none'}
                      id='reachingCarryingCapacity.emailNotification'
                      label={
                        <Text size='light100' fontWeight={400}>
                          {trans('t_email')}
                        </Text>
                      }
                      formControlProps={{ w: '100%', display: 'flex', flexDir: 'column', justifyContent: 'center' }}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />

              <Controller
                name='reachingCarryingCapacity.pushNotification'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      id='reachingCarryingCapacity.pushNotification'
                      label={
                        <Text size='light100' fontWeight={400}>
                          {trans('t_push')}
                        </Text>
                      }
                      formControlProps={{ w: '100%', display: 'flex', flexDir: 'column', justifyContent: 'center' }}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />
            </CheckboxRowContainer>

            <CheckboxRowContainer>
              <Controller
                name='reachingCarryingCapacity.admin'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      id='reachingCarryingCapacity.admin'
                      label={
                        <Text size='light100' fontWeight={400}>
                          {trans('t_admin')}
                        </Text>
                      }
                      formControlProps={{ w: '100%', display: 'flex', flexDir: 'column', justifyContent: 'center' }}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />

              <Controller
                name='reachingCarryingCapacity.supervisor'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      id='reachingCarryingCapacity.supervisor'
                      label={
                        <Text size='light100' whiteSpace='nowrap'>
                          {trans('t_operations_supervisor')}
                        </Text>
                      }
                      formControlProps={{ w: '100%', display: 'flex', flexDir: 'column', justifyContent: 'center' }}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />
            </CheckboxRowContainer>
          </CheckboxGroupContainer>
        </SectionHeaderRowContainer>

        {errors?.reachingCarryingCapacity && (
          <Text size='label200' color='red.400' pt='xs' textAlign='end'>
            {errors.reachingCarryingCapacity.message}
          </Text>
        )}
      </SectionRowContainer>

      <SectionRowContainer>
        <SectionHeaderRowContainer>
          <Controller
            name='amWaterTemperature.enabled'
            control={control}
            render={({ field: { name, value, onChange, onBlur } }) => (
              <Field.Root w='max-content' invalid={!!errors.amWaterTemperature?.enabled?.message}>
                <Switch
                  name={name}
                  checked={value}
                  onCheckedChange={({ checked }) => onChange(checked)}
                  inputProps={{ onBlur }}
                  thumbProps={{ w: '15px', h: '15px' }}
                  controlProps={{
                    bg: amWaterTemperatureChecked ? 'icon.gray' : 'icon.gray.disabled',
                    pt: '2.5px',
                    ps: amWaterTemperatureChecked ? '3xs' : '2xs'
                  }}
                >
                  <Text size='heavy100'>{trans('t_water_am_temperature_unit')}</Text>
                </Switch>
              </Field.Root>
            )}
          />

          <CheckboxGroupContainer>
            <CheckboxRowContainer>
              <Controller
                name='amWaterTemperature.emailNotification'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      display={'none'}
                      id='amWaterTemperature.emailNotification'
                      label={
                        <Text size='light100' fontWeight={400}>
                          {trans('t_email')}
                        </Text>
                      }
                      formControlProps={{ w: '100%', display: 'flex', flexDir: 'column', justifyContent: 'center' }}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />

              <Controller
                name='amWaterTemperature.pushNotification'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      id='amWaterTemperature.pushNotification'
                      label={
                        <Text size='light100' fontWeight={400}>
                          {trans('t_push')}
                        </Text>
                      }
                      formControlProps={{ w: '100%', display: 'flex', flexDir: 'column', justifyContent: 'center' }}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />
            </CheckboxRowContainer>

            <CheckboxRowContainer>
              <Controller
                name='amWaterTemperature.admin'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      id='amWaterTemperature.admin'
                      label={
                        <Text size='light100' fontWeight={400}>
                          {trans('t_admin')}
                        </Text>
                      }
                      formControlProps={{ w: '100%', display: 'flex', flexDir: 'column', justifyContent: 'center' }}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />

              <Controller
                name='amWaterTemperature.supervisor'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      id='amWaterTemperature.supervisor'
                      label={
                        <Text size='light100' whiteSpace='nowrap'>
                          {trans('t_operations_supervisor')}
                        </Text>
                      }
                      formControlProps={{ w: '100%', display: 'flex', flexDir: 'column', justifyContent: 'center' }}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />
            </CheckboxRowContainer>
          </CheckboxGroupContainer>
        </SectionHeaderRowContainer>

        <InputsContainer>
          <Text as='span' size='label200'>
            {trans('t_below')}
          </Text>
          <BaseFormNumberInput
            control={control}
            name='amWaterTemperature.below'
            w='100px'
            numericFormatProps={{ decimalScale: 0 }}
            inputRightElement={
              <Text size='label200' color='gray.700'>
                {trans('t_degree_celsius')}
              </Text>
            }
            inputProps={{ ...inputProps, paddingEnd: '58px', placeholder: trans('t_min') }}
            disabled={!amWaterTemperatureChecked}
          />

          <Text as='span' size='label200'>
            {trans('t_above')}
          </Text>

          <BaseFormNumberInput
            control={control}
            name='amWaterTemperature.above'
            w='100px'
            numericFormatProps={{ decimalScale: 0 }}
            inputRightElement={
              <Text size='label200' color='gray.700'>
                {trans('t_degree_celsius')}
              </Text>
            }
            inputProps={{ ...inputProps, paddingEnd: '58px', placeholder: trans('t_max') }}
            disabled={!amWaterTemperatureChecked}
          />

          <BaseButton
            size='sm'
            variant='link'
            onClick={resetAmWaterTemperatureValues}
            disabled={!amWaterTemperatureChecked}
          >
            {trans('t_clear')}
          </BaseButton>
        </InputsContainer>

        {errors?.amWaterTemperature && (
          <Text size='label200' color='red.400' pt='xs' textAlign='end'>
            {errors.amWaterTemperature.message}
          </Text>
        )}
      </SectionRowContainer>

      <SectionRowContainer>
        <SectionHeaderRowContainer>
          <Controller
            name='pmWaterTemperature.enabled'
            control={control}
            render={({ field: { name, value, onChange, onBlur } }) => (
              <Field.Root w='max-content' invalid={!!errors.pmWaterTemperature?.enabled?.message}>
                <Switch
                  name={name}
                  checked={value}
                  onCheckedChange={({ checked }) => onChange(checked)}
                  inputProps={{ onBlur }}
                  thumbProps={{ w: '15px', h: '15px' }}
                  controlProps={{
                    bg: pmWaterTemperatureChecked ? 'icon.gray' : 'icon.gray.disabled',
                    pt: '2.5px',
                    ps: pmWaterTemperatureChecked ? '3xs' : '2xs'
                  }}
                >
                  <Text size='heavy100'>{trans('t_water_pm_temperature_unit')}</Text>
                </Switch>
              </Field.Root>
            )}
          />

          <CheckboxGroupContainer>
            <CheckboxRowContainer>
              <Controller
                name='pmWaterTemperature.emailNotification'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      display={'none'}
                      id='pmWaterTemperature.emailNotification'
                      label={
                        <Text size='light100' fontWeight={400}>
                          {trans('t_email')}
                        </Text>
                      }
                      formControlProps={{ w: '100%', display: 'flex', flexDir: 'column', justifyContent: 'center' }}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />

              <Controller
                name='pmWaterTemperature.pushNotification'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      id='pmWaterTemperature.pushNotification'
                      label={
                        <Text size='light100' fontWeight={400}>
                          {trans('t_push')}
                        </Text>
                      }
                      formControlProps={{ w: '100%', display: 'flex', flexDir: 'column', justifyContent: 'center' }}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />
            </CheckboxRowContainer>

            <CheckboxRowContainer>
              <Controller
                name='pmWaterTemperature.admin'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      id='pmWaterTemperature.admin'
                      label={
                        <Text size='light100' fontWeight={400}>
                          {trans('t_admin')}
                        </Text>
                      }
                      formControlProps={{ w: '100%', display: 'flex', flexDir: 'column', justifyContent: 'center' }}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />

              <Controller
                name='pmWaterTemperature.supervisor'
                control={control}
                render={({ field: { value, onChange, ...rest } }) => {
                  return (
                    <BaseFormCheckbox
                      {...rest}
                      id='pmWaterTemperature.supervisor'
                      label={
                        <Text size='light100' whiteSpace='nowrap'>
                          {trans('t_operations_supervisor')}
                        </Text>
                      }
                      formControlProps={{ w: '100%', display: 'flex', flexDir: 'column', justifyContent: 'center' }}
                      checked={value}
                      onCheckedChange={({ checked }) => onChange(checked)}
                    />
                  );
                }}
              />
            </CheckboxRowContainer>
          </CheckboxGroupContainer>
        </SectionHeaderRowContainer>

        <InputsContainer>
          <Text as='span' size='label200'>
            {trans('t_below')}
          </Text>
          <BaseFormNumberInput
            control={control}
            name='pmWaterTemperature.below'
            w='100px'
            numericFormatProps={{ decimalScale: 0 }}
            inputRightElement={
              <Text size='label200' color='gray.700'>
                {trans('t_degree_celsius')}
              </Text>
            }
            inputProps={{ ...inputProps, paddingEnd: '58px', placeholder: trans('t_min') }}
            disabled={!pmWaterTemperatureChecked}
          />

          <Text as='span' size='label200'>
            {trans('t_above')}
          </Text>

          <BaseFormNumberInput
            control={control}
            name='pmWaterTemperature.above'
            w='100px'
            numericFormatProps={{ decimalScale: 0 }}
            inputRightElement={
              <Text size='label200' color='gray.700'>
                {trans('t_degree_celsius')}
              </Text>
            }
            inputProps={{ ...inputProps, paddingEnd: '58px', placeholder: trans('t_max') }}
            disabled={!pmWaterTemperatureChecked}
          />

          <BaseButton
            size='sm'
            variant='link'
            onClick={resetPmWaterTemperatureValues}
            disabled={!pmWaterTemperatureChecked}
          >
            {trans('t_clear')}
          </BaseButton>
        </InputsContainer>

        {errors?.pmWaterTemperature && (
          <Text size='label200' color='red.400' pt='xs' textAlign='end'>
            {errors.pmWaterTemperature.message}
          </Text>
        )}
      </SectionRowContainer>

      <BaseButton type='submit' loading={isLoading}>
        {trans('t_submit')}
      </BaseButton>
    </chakra.form>
  );
}
function InputsContainer(props: FlexProps) {
  return (
    <Flex
      direction={{ base: 'column', sm: 'row' }}
      align={{ base: 'flex-start', sm: 'center' }}
      gap='xs-alt'
      {...props}
    />
  );
}

function CheckboxGroupContainer(props: FlexProps) {
  return <Flex flexWrap='wrap' gap={{ base: 'lg' }} {...props} />;
}

function CheckboxRowContainer(props: FlexProps) {
  return <Flex align='center' gap='md' {...props} />;
}

function SectionHeaderRowContainer(props: FlexProps) {
  return (
    <Flex
      flexDirection={{ base: 'column', lg: 'row' }}
      align={{ base: 'flex-start', lg: 'center' }}
      justify={{ base: 'flex-start', lg: 'space-between' }}
      minH='40px'
      gap='md'
      {...props}
    />
  );
}

function SectionRowContainer(props: FlexProps) {
  return <Flex bg='white' p='sm-alt' direction='column' gap='sm-alt' rounded='2xl' {...props} />;
}
