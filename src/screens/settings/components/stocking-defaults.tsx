import { Flex, Text } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { BaseFormNumberInput, BaseFormNumberInputProps } from '@components/form/base-form-number-input';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { formatDate } from '@utils/date';
import { numberTransform, useYupSchema, Yup, yupFormResolver, YupResolverType } from '@utils/yup';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { SettingBaseCard } from '@screens/settings/components/setting-base-card';
import {
  formActionCancelButtonStyles,
  formActionContainerStyles,
  formActionSubmitButtonStyles
} from '@screens/settings/constants/setting-base-card';
import { useFarmSectionUpdate } from '@screens/settings/hooks/use-farm-section-update';
import { useAnalytics } from '@hooks/use-analytics';
import { actionsName } from '@utils/segment';
import { getPercentageValue } from '@utils/number';

const defaultMinValue = 0;
const defaultMaxValue = 100;
const defaultMaxDensity = 1000;
const defaultMaxDryDays = 30;
const defaultMaxLarvaOrderTime = 50;

type FormValues = {
  dryDays: number;
  growthDensity: number;
  daysInNursery: number;
  nurserySurvivalRate: number;
  larvaOrderTime: number;
  avgStockingWeight: number;
};

interface StockingDefaultsProps {
  hasActions?: boolean;
}

export function StockingDefaults(props: StockingDefaultsProps) {
  const { hasActions = true } = props;
  const { trackAction } = useAnalytics();
  const [isEditing, setIsEditing] = useState(false);
  const [forceRender, setForceRender] = useState(0);
  const { currentFarm, isLoading: isLoadingUpdateFarm, handleUpdateFarm } = useFarmSectionUpdate();

  const { trans } = getTrans();
  const user = useAppSelector((state) => state.auth.user);
  const lang = useAppSelector((state) => state.app.lang);
  const { email = '', firstName = '', lastName = '' } = user ?? {};
  const userName = firstName || lastName ? `${firstName ?? ''} ${lastName ?? ''}` : (email ?? '');
  const { stockingConfig, otherConfig, _id: farmId } = currentFarm ?? {};
  const { summary } = stockingConfig ?? {};
  const { stockingSummary } = otherConfig ?? {};
  const { updatedAt, updatedBy } = stockingSummary ?? {};
  const { dryDays, growthDensity, daysInNursery, nurserySurvivalRate, larvaOrderTime, avgStockingWeight } =
    summary ?? {};

  const { schema } = useYupSchema({
    dryDays: Yup.number()
      .transform(numberTransform)
      .positive()
      .min(defaultMinValue)
      .max(defaultMaxDryDays)
      .nullable()
      .label('t_dry_days'),
    growthDensity: Yup.number()
      .transform(numberTransform)
      .positive()
      .min(defaultMinValue)
      .max(defaultMaxDensity)
      .nullable()
      .label('t_stocking_density_m2'),
    daysInNursery: Yup.number()
      .transform(numberTransform)
      .positive()
      .min(defaultMinValue)
      .max(defaultMaxValue)
      .nullable()
      .label('t_days_in_nursery'),
    nurserySurvivalRate: Yup.number()
      .transform(numberTransform)
      .positive()
      .min(defaultMinValue)
      .max(defaultMaxValue)
      .nullable()
      .label('t_nursery_survival'),
    larvaOrderTime: Yup.number()
      .transform(numberTransform)
      .positive()
      .min(defaultMinValue)
      .max(defaultMaxLarvaOrderTime)
      .nullable()
      .label('t_larva_order_time'),
    avgStockingWeight: Yup.number()
      .transform(numberTransform)
      .positive()
      .min(defaultMinValue)
      .max(defaultMaxLarvaOrderTime)
      .nullable()
      .label('t_stocking_weight_g')
  });

  const { reset, control, handleSubmit } = useForm<FormValues>({
    resolver: yupFormResolver(schema) as YupResolverType<FormValues>
  });

  const handleCancel = () => {
    reset({
      dryDays: dryDays ?? NaN,
      growthDensity: growthDensity ?? NaN,
      daysInNursery: daysInNursery ?? NaN,
      nurserySurvivalRate: getPercentageValue(nurserySurvivalRate, { fallBackValue: NaN }),
      larvaOrderTime: larvaOrderTime ?? NaN,
      avgStockingWeight: avgStockingWeight ?? NaN
    });
    setIsEditing(false);
    trackAction(actionsName.farmConfigStockingDefaultsCancelClicked).then();
  };

  useEffect(() => {
    if (!farmId) return;
    handleCancel();
  }, [farmId]);

  const onSubmit = handleSubmit((data: FormValues) => {
    const nurserySurvivalRate = data.nurserySurvivalRate ? data.nurserySurvivalRate / 100 : undefined;
    handleUpdateFarm(
      {
        filter: {
          farmIds: [farmId]
        },
        set: {
          otherConfig: {
            ...currentFarm?.otherConfig,
            stockingSummary: {
              updatedAt: new Date().toISOString(),
              updatedBy: userName
            }
          },
          stockingConfig: {
            ...stockingConfig,
            summary: {
              ...summary,
              ...data,
              nurserySurvivalRate
            }
          }
        }
      },
      () => {
        trackAction(actionsName.farmConfigStockingDefaultsSaveClicked).then();
        setIsEditing(false);
        setForceRender((val) => val + 1);
      }
    );
  });

  const commonInputProps: Omit<BaseFormNumberInputProps, 'name' | 'control'> = {
    maxW: '100px',
    css: { '& .chakra-form__error-message': { whiteSpace: 'nowrap' } },
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: { base: 'md', sm: 'lg', md: 'xl' },
    formLabelProps: { m: 0, minW: { base: '150px', sm: '195px' } },
    inputProps: {
      disabled: !isEditing,
      textAlign: 'center',
      w: { base: '80px', sm: '100px' },
      size: 'sm',
      paddingRight: 'sm'
    }
  };

  return (
    <SettingBaseCard
      title={trans('t_stocking_defaults')}
      key={forceRender}
      pos='relative'
      as='form'
      data-cy='stocking-defaults-settings-card'
      noValidate
      onSubmit={onSubmit}
      isActive={isEditing}
      actionComponent={
        <>
          {!isEditing && hasActions && (
            <BaseButton
              variant='link'
              size='sm'
              onClick={() => {
                setIsEditing(true);
                trackAction(actionsName.farmConfigStockingDefaultsEditClicked).then();
              }}
              data-cy='edit-btn'
              fontWeight={500}
            >
              {trans('t_edit')}
            </BaseButton>
          )}
        </>
      }
    >
      {updatedAt && (
        <Text fontSize='md' mb='md' color='gray.700'>
          {updatedBy
            ? trans('t_last_updated_on_by', {
                date: formatDate({ locale: lang, date: updatedAt, format: 'LLL dd, yyyy' }),
                name: updatedBy
              })
            : `${trans('t_last_updated_on')} ${formatDate({ locale: lang, date: updatedAt, format: 'LLL dd, yyyy' })}`}
        </Text>
      )}

      <Flex direction='column' gap='sm-alt'>
        <BaseFormNumberInput
          required
          name='dryDays'
          control={control}
          label={trans('t_dry_days')}
          {...commonInputProps}
          numericFormatProps={{ decimalScale: 0, suffix: ` ${trans('t_days')}` }}
        />

        <BaseFormNumberInput
          required
          name='avgStockingWeight'
          control={control}
          label={trans('t_avg_stocking_weight_g')}
          {...commonInputProps}
          numericFormatProps={{ decimalScale: 2 }}
        />

        <BaseFormNumberInput
          required
          name='growthDensity'
          control={control}
          label={trans('t_stocking_density_m2')}
          {...commonInputProps}
          numericFormatProps={{ fixedDecimalScale: true, decimalScale: 2 }}
        />

        <BaseFormNumberInput
          required
          name='daysInNursery'
          control={control}
          label={trans('t_days_in_nursery')}
          {...commonInputProps}
          numericFormatProps={{ decimalScale: 0, suffix: ` ${trans('t_days')}` }}
        />
        <BaseFormNumberInput
          required
          name='nurserySurvivalRate'
          control={control}
          label={trans('t_nursery_survival')}
          {...commonInputProps}
          numericFormatProps={{ fixedDecimalScale: true, decimalScale: 1, suffix: '%' }}
        />
        <BaseFormNumberInput
          required
          name='larvaOrderTime'
          control={control}
          label={trans('t_larva_order_time')}
          {...commonInputProps}
          numericFormatProps={{ decimalScale: 0, suffix: ` ${trans('t_days')}` }}
        />
      </Flex>

      <Flex {...formActionContainerStyles} data-cy='stocking-defaults-actions'>
        {isEditing && (
          <>
            <BaseButton {...formActionCancelButtonStyles} data-cy='cancel-btn' onClick={handleCancel}>
              {trans('t_cancel')}
            </BaseButton>
            <BaseButton
              {...formActionSubmitButtonStyles}
              type='submit'
              loading={isLoadingUpdateFarm}
              data-cy='save-btn'
            >
              {trans('t_save_changes')}
            </BaseButton>
          </>
        )}
      </Flex>
    </SettingBaseCard>
  );
}
