import { Box, BoxProps, chakra, Flex, Text, useDisclosure } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { BaseFormInput } from '@components/form/base-form-input';
import { HotTableDynamicComponent } from '@components/hot-table/hot-table-dynamic';
import { getTrans } from '@i18n/get-trans';
import { formatNumber, isNumber } from '@utils/number';
import { getDigitsFromString } from '@utils/string';
import { numberTransform, useYupSchema, Yup, yupFormResolver, YupResolverType } from '@utils/yup';
import { ReactNode } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import { CancelSmallMinorIcon } from '@screens/settings/icons/cancel-small-minor-icon';
import { AddIcon } from '@screens/settings/icons/add-icon';
import { FarmFeedTable } from '@xpertsea/module-farm-sdk';
import { OmitRecursively } from '@utils/types';
import { type HotTableProps } from '@handsontable/react-wrapper';
import { DialogBody, DialogContent, DialogFooter, DialogHeader, DialogRoot, DialogTitle } from '@components/ui/dialog';

export type FeedTableItem = OmitRecursively<FarmFeedTable, '__typename'> & { id?: string };

type FeedTableFormModalProps = {
  defaultValues?: FeedTableItem;
  feedTableList?: FeedTableItem[];
  onSubmit: (feedTable: FeedTableItem) => void;
  children: ReactNode;
  childrenBoxProps?: BoxProps;
  isEdit?: boolean;
};

export function FeedTableFormModal(props: FeedTableFormModalProps) {
  const { children, childrenBoxProps } = props;
  const { onOpen, onClose, open } = useDisclosure();

  return (
    <DialogRoot
      open={open}
      onOpenChange={(e) => (e.open ? onOpen() : onClose())}
      size='lg'
      restoreFocus={false}
      scrollBehavior='inside'
      closeOnInteractOutside={false}
    >
      <Box {...childrenBoxProps} onClick={onOpen}>
        {children}
      </Box>

      {open && <Form {...props} onClose={onClose} />}
    </DialogRoot>
  );
}

interface FeedTableFormData {
  name: string;
  table: { averageWeight: number; biomassToFeed: number }[];
}

const defaultTable: FeedTableFormData = { name: null, table: [{ averageWeight: null, biomassToFeed: null }] };

function Form(props: Partial<FeedTableFormModalProps> & { onClose: () => void }) {
  const { defaultValues, onSubmit, feedTableList, onClose, isEdit } = props;
  const { trans } = getTrans();

  const { schema } = useYupSchema({
    name: Yup.string().required().max(50).label('t_name'),
    table: Yup.array()
      .of(
        Yup.object().shape({
          averageWeight: Yup.number().transform(numberTransform).required().positive().max(60).label('t_required'),
          biomassToFeed: Yup.number().transform(numberTransform).positive().max(100).required().label('t_required')
        })
      )
      .test('unique', trans('t_no_field_duplicated_values', { field: trans('t_abw_g') }), (values) => {
        const hasEmptyValues = values.some((feedTable) => !feedTable.averageWeight);
        if (hasEmptyValues || values.length <= 1) return true;

        const seen = new Set();
        return !values.some((currentObject) => {
          return seen.size === seen.add(currentObject.averageWeight).size;
        });
      })
  });

  const defaultTablePercentValues = defaultValues?.table?.map((field) => {
    const { biomassToFeed } = field;
    const biomassToFeedFormatted = isNumber(biomassToFeed)
      ? (formatNumber(biomassToFeed, { fractionDigits: 1, asNumber: true, isPercentage: true }) as number)
      : biomassToFeed;
    return { ...field, biomassToFeed: biomassToFeedFormatted };
  });

  const {
    control,
    register,
    handleSubmit,
    formState: { errors },
    setError,
    trigger
  } = useForm<FeedTableFormData>({
    mode: 'onChange',
    resolver: yupFormResolver(schema) as YupResolverType<FeedTableFormData>,
    defaultValues: {
      name: defaultValues?.name ?? defaultTable.name,
      table: defaultTablePercentValues?.length ? defaultTablePercentValues : defaultTable.table
    }
  });

  const {
    fields: feedTableFields,
    append: appendFeedFields,
    remove: removeFeedFields,
    replace
  } = useFieldArray<FeedTableFormData>({
    control,
    name: 'table'
  });

  const gridData = feedTableFields.map((field) => [
    field.averageWeight,
    `${isNumber(field.biomassToFeed) ? formatNumber(field.biomassToFeed, { fractionDigits: 1 }) + '%' : field.biomassToFeed || ''}`
  ]);
  const hasAllValues = feedTableFields.every((field) => {
    const hasAbw = field.averageWeight || field.averageWeight === 0;
    const hasBiomass = field.biomassToFeed || field.biomassToFeed === 0;
    return hasAbw && hasBiomass;
  });

  const cellsErrors = Array.isArray(errors?.table)
    ? errors?.table?.flatMap((error, index) => {
        if (!error?.averageWeight && !error?.biomassToFeed) return [];
        return [error?.averageWeight?.message, error?.biomassToFeed?.message]
          .flatMap((errorMsg: string, colIndex): HotTableProps['cell'][0] => {
            if (!errorMsg) return null;
            return {
              row: index,
              col: colIndex,
              valid: false,
              comment: {
                value: errorMsg
              }
            };
          })
          .filter((val) => !!val);
      })
    : [];

  const handleOnSubmit = handleSubmit((data: FeedTableFormData) => {
    const duplicatedName = feedTableList?.some(
      (ele) => ele.name.trim().toLocaleLowerCase() === data.name.trim().toLocaleLowerCase()
    );

    if (duplicatedName && data.name !== defaultValues?.name) {
      return setError('name', { message: trans('t_duplicated_table_name') });
    }

    const updatedPercentData = data.table.map((field) => {
      const biomassToFeed = field.biomassToFeed;
      return { ...field, biomassToFeed: isNumber(biomassToFeed) ? biomassToFeed / 100 : biomassToFeed };
    });

    onSubmit({
      ...defaultValues,
      ...data,
      table: updatedPercentData
    });
    onClose();
  });

  return (
    <DialogContent rounded='2xl'>
      <chakra.form display='contents' noValidate onSubmit={handleOnSubmit}>
        <DialogHeader pb='md' display='flex' flexDirection='column' gap='md'>
          <DialogTitle>
            {trans(isEdit ? 't_edit' : 't_add')} {trans('t_feed_table')}
          </DialogTitle>

          <BaseFormInput
            autoFocus={true}
            {...register('name')}
            error={errors?.name?.message}
            id='name'
            w='90%'
            size='sm'
            borderRadius='base'
            required
          />
          {errors?.table?.message && (
            <Text color='red.500' fontSize='md' fontWeight='normal'>
              {errors?.table?.message?.toString()}
            </Text>
          )}
        </DialogHeader>
        <DialogBody display='flex' flexDirection='column' gap='sm-alt' pt='md'>
          <Flex
            gap='sm-alt'
            css={{
              '& .handsontable': {
                boxShadow: '0px 1px 4px -1px rgba(0, 0, 0, 0.15)'
              },
              '&  table.htCore': {
                fontSize: 'md',
                textAlign: 'center'
              },
              '& .handsontable tr:first-of-type th': {
                borderTop: 'none'
              },
              '& .handsontable tr:first-of-type th div': {
                py: 'xs'
              },
              '& .handsontable tr td': {
                py: 'xs-alt'
              },
              '& .handsontable tr:first-of-type th:first-of-type': {
                borderTopStartRadius: 'base'
              },
              '& .handsontable tr:first-of-type th:last-of-type': {
                borderTopEndRadius: 'base',
                borderEnd: 'none'
              },
              '& .handsontable tr:last-of-type td': {
                borderBottom: 'none'
              },
              '& .handsontable tr:last-of-type td:first-of-type': {
                borderBottomStartRadius: 'base'
              },
              '& .handsontable tr:last-of-type td:last-of-type': {
                borderBottomEndRadius: 'base'
              },
              '& .handsontable th': {
                backgroundColor: 'gray.300',
                fontWeight: '500',
                fontSize: 'sm',
                borderStart: 'none'
              },
              '& .handsontable td': {
                borderStart: 'none'
              },
              '& .handsontable tr  td:last-of-type': {
                borderEnd: 'none'
              },
              '& .handsontable .htCommentCell:after': {
                borderTopColor: 'red.500'
              },
              '& .handsontable td.htInvalid': {
                backgroundColor: 'transparent !important',
                border: '1px solid',
                borderColor: 'red.500'
              }
            }}
            alignItems='flex-start'
          >
            <HotTableDynamicComponent
              width='90%'
              data={gridData}
              comments
              contextMenu={{
                items: {
                  copy: {},
                  cut: {},
                  row_above: {},
                  row_below: {},
                  remove_row: {
                    disabled: gridData.length <= 1
                  },
                  undo: {},
                  redo: {}
                }
              }}
              cell={cellsErrors}
              colHeaders={[trans('t_abw_g'), trans('t_of_biomass_to_feed')]}
              onChange={(data) => {
                if (!data) return;

                const fieldsData = data.map((field) => {
                  const abwValue =
                    (formatNumber(getDigitsFromString(`${field[0]}`), {
                      fractionDigits: 2,
                      asNumber: true
                    }) as number) || null;
                  const biomassValue =
                    (formatNumber(getDigitsFromString(`${field[1]}`), {
                      fractionDigits: 2,
                      asNumber: true
                    }) as number) || null;

                  return { averageWeight: abwValue, biomassToFeed: biomassValue };
                });

                replace(fieldsData);
                trigger('table').then();
              }}
            />
            <Flex direction='column' mt='36px'>
              {feedTableFields.map((item, index) => {
                const hasOneItem = gridData.length === 1;
                if (hasOneItem) return null;

                return (
                  <Flex key={item.id} minH='33px' alignItems='center'>
                    <CancelSmallMinorIcon
                      cursor='pointer'
                      color='primary'
                      _hover={{ opacity: 0.8 }}
                      onClick={() => removeFeedFields(index)}
                    />
                  </Flex>
                );
              })}
            </Flex>
          </Flex>
          <BaseButton
            variant='link'
            size='sm'
            w='max-content'
            disabled={!hasAllValues || !!errors?.table?.message}
            onClick={() => {
              if (!hasAllValues || !!errors?.table?.message) return;
              appendFeedFields({ averageWeight: null, biomassToFeed: null });
            }}
          >
            <AddIcon /> {trans('t_add')}
          </BaseButton>
        </DialogBody>
        <DialogFooter>
          <BaseButton size='sm' color='text.gray' variant='link' marginInlineEnd='md' onClick={onClose}>
            {trans('t_cancel')}
          </BaseButton>
          <BaseButton size='sm' type='submit' color='white'>
            {trans('t_save_changes')}
          </BaseButton>
        </DialogFooter>
      </chakra.form>
    </DialogContent>
  );
}
