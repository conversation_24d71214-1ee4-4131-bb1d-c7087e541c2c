import { chakra, Flex, InputProps, StackProps, SystemStyleObject, Text } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { BaseFormInput } from '@components/form/base-form-input';
import { BaseFormNumberInput } from '@components/form/base-form-number-input';
import { DeleteXIcon } from '@icons/delete-x-icon';
import { useAnalytics } from '@hooks/use-analytics';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { RemoveFeedPalletsConfirmation } from '@screens/settings/components/remove-feed-pallets-confirmation';
import { actionsName } from '@utils/segment';
import { numberTransform, useYupSchema, Yup, yupFormResolver, YupResolverType } from '@utils/yup';
import { FarmFeedPalletInput } from '@xpertsea/module-farm-sdk';
import { ReactNode, useEffect } from 'react';
import { Control, FieldArrayMethodProps, Path, useFieldArray, useForm, useWatch } from 'react-hook-form';

import {
  formActionCancelButtonStyles,
  formActionContainerStyles,
  formActionSubmitButtonStyles
} from '@screens/settings/constants/setting-base-card';

import { formatDate } from '@utils/date';

const defaultMinWeight = 0.1;
const defaultMaxWeight = 50;
const defaultFeedPallets: FarmFeedPalletInput[] = [{ weight: null, palletSize: null, feedType: null, cost: null }];

export type FeedPalletsFormValues = { feedPallets: FarmFeedPalletInput[] };

interface FeedPalletsFormProps extends SystemStyleObject {
  onSubmit(feedPallets: FeedPalletsFormValues): unknown;

  onCancel(isDirty: boolean): unknown;

  onDelete(): unknown;

  isDisabled?: boolean;
  formHeader?: ReactNode;
  inputWidth?: string;
  isLoading?: boolean;
  actionContainerProps?: StackProps;
  isFlex?: boolean;
}

export function FeedPalletsForm(props: FeedPalletsFormProps) {
  const {
    onSubmit,
    onCancel,
    onDelete,
    isDisabled = false,
    formHeader,
    isLoading,
    actionContainerProps = {},
    inputWidth = '150px',
    isFlex = false,
    ...rest
  } = props;
  const { trans } = getTrans();
  const { currentFarm, currentPopulation } = useAppSelector((state) => state.farm);
  const { lang } = useAppSelector((state) => state.app);

  const { trackAction } = useAnalytics();

  const { schema } = useYupSchema({
    feedPallets: Yup.array()
      .of(
        Yup.object().shape({
          weight: Yup.number()
            .transform(numberTransform)
            .positive()
            .min(defaultMinWeight)
            .max(defaultMaxWeight)
            .required(trans('t_required'))
            .label('t_abw_g'),
          palletSize: Yup.number()
            .transform(numberTransform)
            .positive()
            .required(trans('t_required'))
            .label('t_pallet_size'),
          feedType: Yup.string().label('t_feed_type'),
          cost: Yup.number().transform(numberTransform).positive().label('t_cost').nullable()
        })
      )
      .test('unique', trans('t_practical_analysis_error'), (values) => {
        const hasEmptyValues = values.some((pallet) => !pallet.weight || !pallet.palletSize);
        if (hasEmptyValues) return true;
        return values.every((pallet, _, arr) => {
          const weightIndex = arr.findIndex((item) => item.weight === pallet.weight && item !== pallet);
          const palletSizeIndex = arr.findIndex((p) => p.palletSize === pallet.palletSize && p !== pallet);
          return weightIndex === -1 && palletSizeIndex === -1;
        });
      })
      .test('weight', trans('t_weight_error'), (values) => {
        const hasEmptyValues = values.some((pallet) => !pallet.weight || !pallet.palletSize);
        if (hasEmptyValues) return true;
        return values.every((value, index) => {
          if (index === 0) {
            return true;
          }

          const previousWeight = values[index - 1].weight;

          return value.weight > previousWeight;
        });
      })
  });

  const {
    reset,
    control,
    handleSubmit,
    formState: { errors, isDirty },
    register
  } = useForm<FeedPalletsFormValues>({
    resolver: yupFormResolver(schema) as YupResolverType<FeedPalletsFormValues>,
    defaultValues: {
      feedPallets: defaultFeedPallets
    }
  });
  const {
    fields: feedPalletsFields,
    append: appendPallet,
    remove: removePallet,
    replace
  } = useFieldArray({
    control,
    name: 'feedPallets'
  });

  useEffect(() => {
    if (!currentFarm?._id || !currentFarm?.feedPallet?.length) return;

    replace(currentFarm.feedPallet);
  }, [currentFarm?._id, currentFarm?.feedPallet]);

  useEffect(() => {
    if (!isDisabled) return;

    reset({
      feedPallets: currentFarm?.feedPallet ?? defaultFeedPallets
    });
  }, [isDisabled]);

  const handleFormSubmit = handleSubmit((data: FeedPalletsFormValues) => {
    onSubmit(data);
  });

  const handleFormCancel = () => {
    trackAction(actionsName.feedPalletCancelClicked, { addCurrentPopulation: true }).then();
    onCancel(isDirty);
  };

  const handleFormDelete = () => {
    trackAction(actionsName.feedPalletRemoveClicked, { addCurrentPopulation: true }).then();
    onDelete();
  };

  const isNew = currentFarm?.feedPallet?.[0]?.weight == null;
  const updatedAt = currentFarm?.feedPallet?.[0]?.updatedAt;
  const updatedBy = currentFarm?.feedPallet?.[0]?.updatedBy;

  const inputStyles: InputProps = {
    borderRadius: 'base',
    borderColor: 'gray.700',
    borderWidth: '0.5px',
    _hover: {
      borderWidth: '1px'
    },
    _disabled: { border: 'none', bgColor: 'transparent' },
    textAlign: 'center',
    size: 'sm',
    h: '32px'
  };

  return (
    <chakra.form data-cy='feed-pallet-settings-form' noValidate onSubmit={handleFormSubmit} {...rest}>
      {updatedAt && (
        <Text fontSize='md' mb='md-alt' color='gray.700'>
          {updatedBy
            ? trans('t_last_updated_on_by', {
                date: formatDate({ locale: lang, date: updatedAt, format: 'LLL dd, yyyy' }),
                name: updatedBy
              })
            : `${trans('t_last_updated_on')} ${formatDate({ locale: lang, date: updatedAt, format: 'LLL dd, yyyy' })}`}
        </Text>
      )}
      <Flex flexDirection='column' gap='lg'>
        {formHeader}
        {errors?.feedPallets?.root?.message && (
          <Text color='red.500' fontSize='md'>
            {errors?.feedPallets?.root?.message}
          </Text>
        )}
        <Flex fontSize='md' flexDirection='column' gap='xs'>
          <Flex ps='sm-alt' gap='sm' align='flex-end'>
            <chakra.label w={inputWidth} minW={inputWidth} htmlFor='feedPallets.0.weight'>
              {trans('t_abw_g')}
            </chakra.label>
            <chakra.label w={inputWidth} minW={inputWidth} htmlFor='feedPallets.0.palletSize'>
              {trans('t_pallet_size')}
            </chakra.label>
            <chakra.label w={inputWidth} minW={inputWidth} htmlFor='feedPallets.0.feedType'>
              {trans('t_feed_type')}
            </chakra.label>
            <chakra.label w={inputWidth} minW={inputWidth} htmlFor='feedPallets.0.cost'>
              {trans('t_cost_$')} / {trans('t_kg')}
            </chakra.label>
          </Flex>
          {feedPalletsFields.map((field, index) => {
            const isLastElement = index === feedPalletsFields.length - 1;
            const isMaxLength = feedPalletsFields.length === 5;
            const hasOneItem = feedPalletsFields.length === 1;
            return (
              <Flex
                borderRadius='base'
                w='max-content'
                bgColor='gray.100'
                gap='sm'
                px='sm-alt'
                py='2xs'
                key={field.id}
                align='flex-start'
              >
                <BaseFormNumberInput
                  w={inputWidth}
                  minW={inputWidth}
                  required
                  id='weight'
                  name={`feedPallets.${index}.weight`}
                  control={control}
                  inputProps={{ ...inputStyles, disabled: isDisabled, textAlign: 'end' }}
                  numericFormatProps={{ decimalScale: 2, fixedDecimalScale: true }}
                  inputRightElement={
                    <Text as='span' fontSize='md'>
                      g
                    </Text>
                  }
                />
                <BaseFormNumberInput
                  w={inputWidth}
                  minW={inputWidth}
                  required
                  control={control}
                  name={`feedPallets.${index}.palletSize`}
                  id='palletSize'
                  numericFormatProps={{ decimalScale: 2, fixedDecimalScale: true }}
                  inputProps={{ ...inputStyles, disabled: isDisabled, textAlign: 'end' }}
                  inputRightElement={
                    <Text as='span' fontSize='md'>
                      mm
                    </Text>
                  }
                />
                <BaseFormInput
                  id={`feedPallets.${index}.feedType`}
                  w={inputWidth}
                  minW={inputWidth}
                  required
                  name={`feedPallets.${index}.feedType`}
                  {...register(`feedPallets.${index}.feedType`)}
                  disabled={isDisabled}
                  formControlProps={{
                    w: inputWidth,
                    minW: inputWidth
                  }}
                  {...inputStyles}
                />
                <BaseFormNumberInput
                  w={inputWidth}
                  minW={inputWidth}
                  required
                  id='cost'
                  name={`feedPallets.${index}.cost`}
                  control={control}
                  inputProps={{ ...inputStyles, disabled: isDisabled, textAlign: 'end' }}
                  numericFormatProps={{ decimalScale: 1, fixedDecimalScale: true }}
                  error={errors?.feedPallets?.[index]?.cost?.message?.toString()}
                  inputRightElement={
                    <>
                      <Text as='span' fontSize='md'>
                        $
                      </Text>
                      {!isDisabled && (
                        <Flex insetStart='xl' pos='absolute' align='center'>
                          {!hasOneItem && (
                            <DeleteXIcon
                              mx='xs'
                              cursor='pointer'
                              onClick={() => {
                                removePallet(index);
                              }}
                            />
                          )}
                          {isLastElement && !isMaxLength && (
                            <AddFeedPalletButton
                              appendPallet={appendPallet}
                              control={control}
                              name={[`feedPallets.${index}.palletSize`, `feedPallets.${index}.weight`]}
                            />
                          )}
                        </Flex>
                      )}
                    </>
                  }
                />
              </Flex>
            );
          })}
        </Flex>
      </Flex>
      {!isDisabled && (
        <Flex
          gap='md'
          data-cy='feed-pallet-actions'
          {...(!isFlex && formActionContainerStyles)}
          {...actionContainerProps}
        >
          {!isNew && (
            <RemoveFeedPalletsConfirmation onConfirm={() => handleFormDelete()} isFlex={isFlex}>
              <BaseButton fontSize='md' flex={isFlex ? 1 : null} variant='link'>
                {trans('t_remove')}
              </BaseButton>
            </RemoveFeedPalletsConfirmation>
          )}
          <BaseButton
            {...formActionCancelButtonStyles}
            flex={isFlex ? 1 : null}
            data-cy='cancel-btn'
            onClick={handleFormCancel}
          >
            {trans('t_cancel')}
          </BaseButton>
          <BaseButton
            type='submit'
            {...formActionSubmitButtonStyles}
            flex={isFlex ? 1 : null}
            loading={isLoading}
            data-cy='save-btn'
            onClick={() => {
              trackAction(actionsName.feedPalletSaveClicked, { addCurrentPopulation: true }).then();
            }}
          >
            {trans('t_save_changes')}
          </BaseButton>
        </Flex>
      )}
    </chakra.form>
  );
}

interface AddFeedPalletButtonProps<T> {
  appendPallet: (value: FarmFeedPalletInput | FarmFeedPalletInput[], options?: FieldArrayMethodProps) => void;
  control: Control<T>;
  name: Path<T> | string | string[];
}

function AddFeedPalletButton<T>(props: AddFeedPalletButtonProps<T>) {
  const { appendPallet, control, name } = props;
  const values = useWatch({
    control,
    name: name as Path<T>
  });
  const { trans } = getTrans();
  const { trackAction } = useAnalytics();

  const hasValues = (values as number[]).every((value) => value);

  return (
    <BaseButton
      border='1px solid'
      borderColor='gray.100'
      size='sm'
      w='76px'
      disabled={!hasValues}
      onClick={() => {
        if (!hasValues) return;
        appendPallet({ weight: null, palletSize: null, cost: null });
        trackAction(actionsName.feedPalletAddClicked, { addCurrentPopulation: true }).then();
      }}
    >
      {trans('t_add')}
    </BaseButton>
  );
}
