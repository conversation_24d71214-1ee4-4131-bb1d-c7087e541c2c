import { Box, Flex, HStack, Icon, MenuItem, Tag, Text } from '@chakra-ui/react';
import { BaseFormInput } from '@components/form/base-form-input';
import { PageScreenHeader } from '@components/layout/page-screen-header';
import { ITableContentProps, TableComponent } from '@components/table/table-component';
import { usePermission } from '@hooks/use-permission';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { CreateUserModal, NewUserModalProps } from '@screens/user/components/create-user-modal';
import { EditUserAccessModal, EditUserAccessModalProps } from '@screens/user/components/edit-user-access-modal';
import { useListUsersBulkApi } from '@screens/user/hooks/use-list-users-bulk-api';
import { appLinks } from '@utils/app-links';
import { parseCookies, setCookie } from '@utils/cookie';
import { getDomainTld, goToUrl } from '@utils/url';
import { Invitation } from '@xpertsea/module-user-sdk';
import { btoa } from 'isomorphic-base64';
import cloneDeep from 'lodash/cloneDeep';
import debounce from 'lodash/debounce';
import isNull from 'lodash/isNull';
import orderBy from 'lodash/orderBy';
import { useRouter } from 'next/router';
import { ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import { MdMoreHoriz } from 'react-icons/md';
import { DateTime } from 'luxon';
import { FarmSelector } from '@screens/farm/components/farm-selector';
import { useSetFarmBookRefererInRedux } from '@components/farm-book/hooks/use-set-farm-book-referer-in-redux';
import { AdminOnlyWrapper } from '@components/permission-wrappers/admin-only-wrapper';
import { MenuButton, MenuContent, MenuRoot } from '@components/ui/menu';
import { BaseFormCheckbox } from '@components/form/base-form-checkbox';
import { Tooltip } from '@components/ui/tooltip';
import { SearchDuoIcon } from '@icons/search/search-duo-icon';

export function UserListScreen() {
  const { route, query } = useRouter();
  useSetFarmBookRefererInRedux('myTeam');
  const { trans } = getTrans();

  const { show_all_users: showAllUsersCookie } = parseCookies();
  const showAllUsers = showAllUsersCookie === '1';
  const {
    q = '',
    sortBy = 'lastSeenAt',
    sortDir = 'desc'
  } = query as { q: string; sortBy?: string; sortDir?: 'asc' | 'desc' };

  const lang = useAppSelector((state) => state.app.lang);
  const user = useAppSelector((state) => state.auth.user);
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);
  const isAdmin = usePermission({ role: 'admin', entity: 'farm', entityId: currentFarm?._id });

  const [{ data, isLoading, isFinishedOnce }, listUsersBulkApi] = useListUsersBulkApi();
  const [userIdToEditAccess, setUserIdToEditAccess] = useState<string>();

  const [userToEditAccess, setUserToEditAccess] = useState<(typeof data.users)[0]>();
  const [isEditUserAccessModalOpen, setIsEditUserAccessModalOpen] = useState<boolean>();
  const [rnd, setRnd] = useState<string>();

  const filteredUsers = useMemo<typeof data.users>(() => {
    if (!data) return;

    let users = cloneDeep(data.users);

    users = users.map((user) => {
      // Remove deactivated memberships
      user.memberships = user.memberships.filter((membership) => {
        if (!isNull(membership.metadata) && membership?.metadata?.hideInUserList) {
          return false;
        }

        return !(!isNull(membership?.isDeactivated) && membership?.isDeactivated);
      });

      return user;
    });

    users = users.filter((user) => {
      if (!user.memberships.length && !user.invitations.length) return false;

      const fullName = `${user.firstName ? user.firstName : ''}${user.lastName ? ` ${user.lastName}` : ''}`;

      if ('invited'.includes(q.toLowerCase()) && user.__typename === 'Invitation') {
        return true;
      }

      return (
        'active'.includes(q.toLowerCase()) ||
        fullName.toLowerCase().includes(q.toLowerCase()) ||
        user.email.toLowerCase().includes(q.toLowerCase()) ||
        user.memberships.some((membership) => {
          return data.farmsMap[membership.entityId]?.name.toLowerCase().includes(q.toLowerCase());
        }) ||
        user.invitations.some((invitation) => {
          return data.farmsMap[invitation.entityId]?.name.toLowerCase().includes(q.toLowerCase());
        })
      );
    });

    if (!showAllUsers && currentFarm?._id) {
      users = users.filter((user) => {
        const userHasCurrentFarmInvitations = user.invitations.some(
          (invitation) => invitation.entityId === currentFarm._id
        );
        const userHasCurrentFarmMemberships = user.memberships.some(
          (membership) => membership.entityId === currentFarm._id
        );

        return userHasCurrentFarmInvitations || userHasCurrentFarmMemberships;
      });
    }

    return orderBy(users, [sortBy], [sortDir]);
  }, [data, query, showAllUsers, currentFarm?._id, rnd]);

  useEffect(() => {
    onReloadList();
  }, []);

  const onReloadList = useCallback(() => {
    listUsersBulkApi({
      params: {
        page: { size: 1000 }
      }
    });
  }, []);

  const onEditUserAccess = useCallback((row: (typeof data.users)[0]) => {
    setUserToEditAccess(row);
    setIsEditUserAccessModalOpen(!isEditUserAccessModalOpen);
  }, []);

  const onEditUserAccessFromCreateUserModal = useCallback(
    (userId: string) => {
      const userToEditAccess = data.users.find((user) => user._id === userId);
      onEditUserAccess(userToEditAccess);
    },
    [data?.users]
  );

  const onEditUserAccessModalClose = useCallback(() => {
    setUserToEditAccess(undefined);
    setUserIdToEditAccess(undefined);
    setIsEditUserAccessModalOpen(false);
  }, []);

  const onHeaderClick = useCallback(
    (accessor: string) => {
      goToUrl({
        route,
        params: {
          ...query,
          sortBy: accessor,
          sortDir: sortDir === 'asc' ? 'desc' : 'asc'
        }
      });
    },
    [query]
  );

  const rolesHash: Record<string, string> = {
    user: trans('t_user'),
    admin: trans('t_admin'),
    supervisor: trans('t_operations_supervisor_can_see'),
    operation: trans('t_production_supervisor')
  };

  const columns = useMemo<ITableContentProps<(typeof data.users)[0]>['columns']>(() => {
    return [
      {
        Header: trans('t_name'),
        isSortable: true,
        accessor: 'firstName',
        onHeaderClick,
        thProps: { width: '12%' },
        Cell: (_: unknown, row) => {
          return <Text fontWeight={500}>{row.firstName ? `${row.firstName} ${row.lastName || ''}` : '-'}</Text>;
        }
      },
      {
        Header: trans('t_phone_number'),
        accessor: 'contactPhoneNumber',
        thProps: { width: '15%' },
        Cell: (contactPhoneNumber: string) => <Text>{contactPhoneNumber || '-'}</Text>
      },
      {
        Header: trans('t_email'),
        accessor: 'email',
        thProps: { width: '15%' },
        Cell: (email: string) => <Text>{email || '-'}</Text>
      },
      {
        onHeaderClick,
        accessor: 'status',
        Header: trans('t_status'),
        thProps: { width: '15%' },
        Cell: (_: unknown, row) => {
          switch (row.__typename) {
            case 'User':
              return (
                <HStack>
                  <Box bg='green.500' w='8px' h='8px' rounded='lg' />
                  <Text>{trans('t_active')}</Text>
                </HStack>
              );
            case 'Invitation':
              return (
                <HStack data-cy='user-status' data-email={row.email}>
                  <Text>{trans('t_invited')}</Text>
                  <Text>
                    {row?.createdAt
                      ? `(${DateTime.fromISO(row.createdAt, { zone: currentFarm.timezone }).toFormat('MMM dd, yyyy', {
                          locale: lang
                        })})`
                      : ''}
                  </Text>
                </HStack>
              );
            default:
              return <Text>-</Text>;
          }
        }
      },
      {
        Header: trans('t_access'),
        Cell: (_: unknown, row) => {
          const isXpertseaUser = row.email.includes('@xpertsea.com');

          return (
            <Flex
              data-cy={row.email}
              data-total-memberships={row.memberships.length}
              data-total-invitations={row.invitations.length}
              flexWrap='wrap'
              gap='2xs'
            >
              {row.memberships.map((membership) => {
                return (
                  <Tag.Root
                    size='sm'
                    display='flex'
                    gap='2xs'
                    color='white'
                    key={membership._id}
                    backgroundColor='gray.500'
                    borderRadius='sm'
                    px='2xs'
                    fontSize='md'
                    minHeight='22px'
                    cursor='text'
                    userSelect='text'
                    {...(isXpertseaUser && { backgroundColor: 'gray.200', color: 'black' })}
                  >
                    <Tag.Label>
                      {!!data.farmsMap[membership.entityId]?.name && (
                        <Text
                          textTransform='capitalize'
                          fontWeight='600'
                          title={data.farmsMap[membership.entityId]?.name}
                        >
                          {data.farmsMap[membership.entityId]?.name}
                        </Text>
                      )}
                    </Tag.Label>
                    {(membership.role === 'admin' || membership.role === 'supervisor') && (
                      <Tag.EndElement boxSize='min-content' textTransform='capitalize' fontStyle='italic'>
                        {rolesHash[membership.role] ?? membership.role}
                      </Tag.EndElement>
                    )}
                  </Tag.Root>
                );
              })}

              {row.invitations.map((invitation) => {
                const { _id, code } = invitation;
                const basePath = appLinks('insights');
                const url = `${basePath}/view-invitation?i=${btoa(_id)}&h=1&c=${btoa(code)}`;

                return (
                  <InvitationTagWrapper key={invitation._id} invitation={invitation} isUser={row.__typename === 'User'}>
                    <Tag.Root
                      px='2xs'
                      size='sm'
                      display='flex'
                      fontSize='md'
                      gap='2xs'
                      color='white'
                      minHeight='22px'
                      borderRadius='sm'
                      data-email={row.email}
                      backgroundColor='gray.500'
                      data-cy='view-invitation-url'
                      data-view-invitation-url={url}
                      {...(isXpertseaUser && { backgroundColor: 'gray.200', color: 'black' })}
                    >
                      <Tag.Label>
                        <Text textTransform='capitalize' fontWeight='600'>
                          {data.farmsMap[invitation.entityId]?.name}
                        </Text>
                        {invitation.role === 'admin' && (
                          <Text fontWeight='400' textTransform='capitalize' fontStyle='italic'>
                            {rolesHash[invitation.role]}
                          </Text>
                        )}
                      </Tag.Label>
                    </Tag.Root>
                  </InvitationTagWrapper>
                );
              })}
            </Flex>
          );
        }
      },
      {
        onHeaderClick,
        accessor: 'lastSeenAt',
        thProps: { width: '12%' },
        Header: trans('t_last_active'),
        Cell: (_: unknown, row) => {
          if (row.__typename !== 'User' || !row?.lastSeenAt) return <Text>-</Text>;

          return (
            <Text>
              {DateTime.fromISO(row.lastSeenAt, { zone: currentFarm.timezone }).toFormat('MMM dd, yyyy HH:mm', {
                locale: lang
              })}
            </Text>
          );
        }
      },
      {
        Cell: (_: unknown, row) => {
          return (
            <AdminOnlyWrapper>
              <MenuRoot>
                <MenuButton variant='link' disabled={!isAdmin || row._id === user?._id}>
                  <Icon asChild fontSize='lg' color='gray.700'>
                    <MdMoreHoriz />
                  </Icon>
                </MenuButton>
                <MenuContent>
                  <MenuItem value={row.email}>
                    <Text
                      p='6px 12px'
                      m='-6px -12px'
                      w='fill-available'
                      data-cy='edit-access'
                      data-email={row.email}
                      onClick={() => onEditUserAccess(row)}
                    >
                      {trans('t_edit_access')}
                    </Text>
                  </MenuItem>
                </MenuContent>
              </MenuRoot>
            </AdminOnlyWrapper>
          );
        }
      }
    ];
  }, [filteredUsers, userIdToEditAccess, isAdmin, user?._id, lang]);

  const title = showAllUsers
    ? trans('t_users_at_all_farms')
    : trans('t_users_at_farm', { farmName: currentFarm?.name });

  return (
    <Flex direction='column' gap='md-alt' p='md' mb='xl-alt'>
      <FarmSelector />
      <EditUserAccessModal
        onReloadList={onReloadList}
        isOpen={isEditUserAccessModalOpen}
        onClose={onEditUserAccessModalClose}
        user={userToEditAccess as EditUserAccessModalProps['user']}
        farmsMap={data?.farmsMap as EditUserAccessModalProps['farmsMap']}
      />
      <PageScreenHeader
        title={title}
        actionsProps={{ justify: 'flex-end', flex: { base: 0.8, '2xl': 0.6 } }}
        titleProps={{ color: 'gray.700', fontWeight: 500, fontSize: 'xl' }}
        actions={
          <>
            <BaseFormCheckbox
              flexShrink={0}
              defaultChecked={showAllUsers}
              disabled={isLoading}
              onCheckedChange={(val) => {
                setCookie(undefined, 'show_all_users', val.checked ? '1' : '0', {
                  path: '/',
                  domain: getDomainTld(),
                  expires: new Date(Date.now() + 31536000000)
                });
                setRnd(`${Date.now()}`);
              }}
            >
              {trans('t_show_users_all_farms')}
            </BaseFormCheckbox>
            <BaseFormInput
              type='search'
              defaultValue={q}
              onChange={debounce((event) => {
                goToUrl({
                  route: '/users',
                  params: { ...query, q: event.target.value }
                });
              }, 250)}
              id='search'
              borderRadius='full'
              outline='none'
              bgColor='white'
              placeholder={trans('t_search_by_user_farm_or_status')}
              leftElement={<SearchDuoIcon />}
              formControlProps={{ flex: { base: 1, '2xl': 0.7 } }}
            />
            {isFinishedOnce && isAdmin && (
              <CreateUserModal
                farmsMap={data?.farmsMap as NewUserModalProps['farmsMap']}
                onReloadList={onReloadList}
                onEditUserAccess={onEditUserAccessFromCreateUserModal}
              />
            )}
          </>
        }
      />
      <Box
        css={{
          table: { borderWidth: 0 },
          td: { verticalAlign: 'top' },
          '& tbody td': { borderColor: 'gray.200' },
          '& tbody tr:last-child td:last-child': { borderBottomRightRadius: 'md' },
          '& tbody tr:last-child td:first-of-type': { borderBottomLeftRadius: 'md' },
          '& thead th': {
            bgColor: 'white',
            borderTop: '1px solid',
            borderColor: 'gray.200',
            color: 'gray.700'
          },
          '& thead th:last-of-type, tbody td:last-of-type': {
            borderRight: '1px solid',
            borderColor: 'gray.200'
          },
          '& thead th:first-of-type, tbody td:first-of-type': {
            borderLeft: '1px solid',
            borderColor: 'gray.200'
          },
          '& tbody tr:last-of-type td': { borderBottom: '1px solid', borderColor: 'gray.200' },
          '& tbody td:first-of-type': { borderRight: '1px solid', borderRightColor: 'gray.100 !important' }
        }}
      >
        <TableComponent
          data={filteredUsers}
          noDataMessage={trans('t_no_data_to_display')}
          columns={columns}
          sortBy={sortBy}
          sortDir={sortDir}
          isLoading={isLoading}
        />
      </Box>
    </Flex>
  );
}

type InvitationTagWrapper = { invitation: Invitation; children: ReactNode; isUser: boolean };

function InvitationTagWrapper(props: InvitationTagWrapper) {
  const { children, invitation, isUser } = props;

  const { trans } = getTrans();
  const { lang } = useAppSelector((state) => state.app);
  const { currentFarm } = useAppSelector((state) => state.farm);

  if (!isUser) return children;

  const invitedAtFormatted = DateTime.fromISO(invitation.createdAt, { zone: currentFarm.timezone }).toFormat(
    'MMM dd, yyyy',
    {
      locale: lang
    }
  );

  return (
    <Tooltip content={`${trans('t_invited')} (${invitedAtFormatted})`} showArrow>
      <Box cursor='pointer'>{children}</Box>
    </Tooltip>
  );
}
