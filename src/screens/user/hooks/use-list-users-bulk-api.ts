import { IApiHookInput, ICallbackParams, IHookInputType, useApiHookWrapper } from '@hooks/use-api-hook-wrapper';
import { getTrans } from '@i18n/get-trans';
import { addToastAction } from '@redux/app';
import { moduleFarm } from '@sdks/module-farm';
import { moduleUser } from '@sdks/module-user';
import { throwErrorMsg } from '@utils/errors';
import { Head, QueryRequest, UserMembership, v1ListUsersOutputRequest } from '@xpertsea/module-user-sdk';
import filter from 'lodash/filter';
import find from 'lodash/find';
import groupBy from 'lodash/groupBy';
import keyBy from 'lodash/keyBy';
import map from 'lodash/map';

async function mountFn(props: ICallbackParams<Input>) {
  const { input } = props;
  const { params } = input;

  // List users for a farm
  const { data: v1ListUsersData, errors: v1ListUsersErrors } = await moduleUser.query({
    v1ListUsers: [
      params,
      {
        pageSize: 1,
        totalPages: 1,
        currentPage: 1,
        totalRecords: 1,
        users: {
          _id: 1,
          companyId: 1,
          contactPhoneNumber: 1,
          createdAt: 1,
          eid: 1,
          email: 1,
          emailVerifiedAt: 1,
          firstName: 1,
          gender: 1,
          isAccountVerified: 1,
          isLogInDisabled: 1,
          lastName: 1,
          logInMobileVerifiedAt: 1,
          logInPhoneNumber: 1,
          lastSeenAt: 1,
          metadata: 1,
          password: 1,
          preferences: {
            language: 1,
            timezone: 1,
            viewFields: 1,
            unitsConfig: 1
          },
          profileImageUrl: 1,
          updatedAt: 1,
          __typename: 1
        }
      }
    ]
  });
  if (v1ListUsersErrors) {
    throwErrorMsg(v1ListUsersErrors);
  }

  const { data: v1ListEntityInvitationsData, errors: v1ListEntityInvitationsErrors } = await moduleUser.query({
    v1ListEntityInvitations: [
      {
        page: {
          size: 1000
        },
        filter: {
          entity: 'farm',
          status: 'pending',
          ...(params?.filter?.entityId ? { entityId: params.filter.entityId } : {})
        }
      },
      {
        metadata: 1,
        pageSize: 1,
        totalPages: 1,
        currentPage: 1,
        totalRecords: 1,
        invitations: {
          _id: 1,
          firstName: 1,
          lastName: 1,
          acceptedAt: 1,
          canceledAt: 1,
          code: 1,
          createdAt: 1,
          email: 1,
          entity: 1,
          entityId: 1,
          expiredAt: 1,
          invitedBy: 1,
          message: 1,
          metadata: 1,
          phoneNumber: 1,
          rejectedAt: 1,
          role: 1,
          status: 1,
          updatedAt: 1,
          __typename: 1
        }
      }
    ]
  });
  if (v1ListEntityInvitationsErrors) {
    throwErrorMsg(v1ListEntityInvitationsErrors);
  }

  const { data: v1ListUserMembershipData, errors: v1ListUserMembershipErrors } = await moduleUser.query({
    v1ListUserMembership: [
      {
        page: { size: 1000 },
        filter: {
          userId: map(v1ListUsersData.v1ListUsers.users, '_id'),
          ...(params?.filter?.entityId ? { entityId: params.filter.entityId } : {})
        }
      },
      {
        pageSize: 1,
        totalPages: 1,
        currentPage: 1,
        totalRecords: 1,
        userMemberships: {
          __typename: 1,
          _id: 1,
          createdAt: 1,
          createdBy: 1,
          entity: 1,
          entityId: 1,
          inviteId: 1,
          isDeactivated: 1,
          metadata: 1,
          role: 1,
          updatedAt: 1,
          userId: 1
        }
      }
    ]
  });
  if (v1ListUserMembershipErrors) {
    throwErrorMsg(v1ListUserMembershipErrors);
  }

  // Attach invitations and membership with each user
  const users = v1ListUsersData.v1ListUsers.users.map((user) => {
    // User Invitations
    const invitations = filter(v1ListEntityInvitationsData.v1ListEntityInvitations.invitations, (invitation) => {
      return user.email ? invitation.email === user.email : false;
    });

    // User memberships
    const memberships = v1ListUserMembershipData.v1ListUserMembership.userMemberships.filter((userMembership) => {
      return userMembership?.userId === user._id && !userMembership?.isDeactivated;
    });

    return {
      ...user,
      invitations,
      memberships
    };
  });

  // Non registered users
  const invitationsByEmail = groupBy(
    filter(v1ListEntityInvitationsData.v1ListEntityInvitations.invitations, (invitation) => {
      // This will return only non-registered users
      return !find(v1ListUsersData.v1ListUsers.users, {
        email: invitation.email
      });
    }),
    'email'
  );
  const invitations = Object.entries(invitationsByEmail).map(([_, invitations]) => {
    const { firstName, lastName } = find(invitations, (invitation) => !!invitation?.firstName?.length) || {};

    return {
      ...invitations[0],
      firstName,
      lastName,
      invitations,
      memberships: [] as UserMembership[]
    };
  });

  const { data: moduleFarmData, errors: moduleFarmErrors } = await moduleFarm.query({
    v1ListFarms: [
      {
        page: {
          size: 1000
        }
      },
      {
        __typename: 1,
        currentPage: 1,
        farms: {
          __typename: 1,
          _id: 1,
          address: {
            city: 1,
            country: 1,
            label: 1,
            phone: 1,
            plusCode: 1,
            state: 1,
            street1: 1,
            street2: 1,
            zip: 1
          },
          companyId: 1,
          contact: {
            email: 1,
            phone: 1
          },
          createdAt: 1,
          createdBy: 1,
          deletedAt: 1,
          deletedBy: 1,
          description: 1,
          eid: 1,
          geoLocation: {
            coordinates: 1,
            type: 1
          },
          iconUrl: 1,
          industry: 1,
          isDeleted: 1,
          language: 1,
          legalName: 1,
          groupName: 1,
          metadata: 1,
          name: 1,
          status: 1,
          taxId: 1,
          timezone: 1,
          updatedAt: 1
        },
        metadata: 1,
        pageSize: 1,
        totalPages: 1,
        totalRecords: 1
      }
    ]
  });
  if (moduleFarmErrors) {
    throwErrorMsg(moduleFarmErrors);
  }

  return {
    users: [...users, ...invitations],
    farmsMap: keyBy(moduleFarmData.v1ListFarms.farms, '_id')
  };
}

async function errorFn(props: ICallbackParams<Input>) {
  const { dispatch, error } = props;
  const { trans } = getTrans();
  dispatch(addToastAction({ type: 'error', title: trans('t_error'), msg: error }));
}

type Input = IApiHookInput<Head<QueryRequest['v1ListUsers']>, v1ListUsersOutputRequest>;
type Output = Awaited<ReturnType<typeof mountFn>>;

export function useListUsersBulkApi(props?: IHookInputType<Input, Output>) {
  return useApiHookWrapper<Input, Output>({
    mountFn,
    errorFn,
    initialInput: props,
    unmountFn: undefined,
    initialIsLoading: !!props,
    skipInitialApiCallOnEmptyInputs: true,
    __t: props?.__t, // cache buster key
    __cacheTTL: props?.__cacheTTL, // cache ttl in seconds
    __cachePrefix: props?.__cachePrefix // cache key prefix
  });
}
