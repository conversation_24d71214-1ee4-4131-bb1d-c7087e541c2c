import { IApiHookInput, ICallbackParams, IHookInputType, useApiHookWrapper } from '@hooks/use-api-hook-wrapper';
import { getTrans } from '@i18n/get-trans';
import { addToastAction } from '@redux/app';
import { moduleUser } from '@sdks/module-user';
import { throwErrorMsg } from '@utils/errors';
import { Head, QueryRequest, v1ListUsersOutputRequest } from '@xpertsea/module-user-sdk';
import keyBy from 'lodash/keyBy';
import map from 'lodash/map';

async function mountFn(props: ICallbackParams<Input>) {
  const { input } = props;
  const {
    params,
    fields = {
      currentPage: true,
      pageSize: true,
      totalPages: true,
      totalRecords: true,
      users: {
        _id: true,
        companyId: true,
        contactPhoneNumber: true,
        createdAt: true,
        eid: true,
        email: true,
        emailVerifiedAt: true,
        firstName: true,
        gender: true,
        isAccountVerified: true,
        isLogInDisabled: true,
        lastName: true,
        logInPhoneNumber: true,
        logInMobileVerifiedAt: true,
        profileImageUrl: true,
        updatedAt: true,
        metadata: true
      }
    }
  } = input;

  const { data, errors } = await moduleUser.query({
    v1ListUsers: [params, fields]
  });
  if (errors) {
    throwErrorMsg(errors);
  }

  const userIds = map(data?.v1ListUsers?.users, '_id');

  if (!userIds.length) {
    return {
      ...data.v1ListUsers,
      userMembershipsMap: {}
    };
  }

  const { data: v1ListUserMembershipData, errors: v1ListUserMembershipDataErrors } = await moduleUser.query({
    v1ListUserMembership: [
      {
        page: { size: 10000 },
        filter: { userId: userIds, entityId: params.filter.entityId }
      },
      { userMemberships: { _id: true, userId: true, role: true, isDeactivated: true, metadata: true } }
    ]
  });

  if (v1ListUserMembershipDataErrors) throw new Error(v1ListUserMembershipDataErrors[0].message);

  const activeMemberships = v1ListUserMembershipData.v1ListUserMembership.userMemberships?.filter(
    (item) => !item.isDeactivated
  );

  return {
    ...data.v1ListUsers,
    userMembershipsMap: keyBy(activeMemberships, 'userId')
  };
}

async function errorFn(props: ICallbackParams<Input>) {
  const { dispatch, error } = props;
  const { trans } = getTrans();
  dispatch(addToastAction({ type: 'error', title: trans('t_error'), msg: error }));
}

type Input = IApiHookInput<Head<QueryRequest['v1ListUsers']>, v1ListUsersOutputRequest>;
type Output = Awaited<ReturnType<typeof mountFn>>;

export function useListUsersApi(props?: IHookInputType<Input, Output>) {
  return useApiHookWrapper({
    mountFn,
    errorFn,
    initialInput: props,
    initialIsLoading: !!props,
    skipInitialApiCallOnEmptyInputs: true,
    unmountFn: undefined,
    __t: props?.__t, // cache buster key
    __cachePrefix: props?.__cachePrefix, // cache key prefix
    __cacheTTL: props?.__cacheTTL // cache ttl in seconds
  });
}
