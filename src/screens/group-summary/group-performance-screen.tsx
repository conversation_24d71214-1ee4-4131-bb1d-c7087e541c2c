import { Box, Flex, Heading, Skeleton, Text } from '@chakra-ui/react';
import { useSetFarmBookRefererInRedux } from '@components/farm-book/hooks/use-set-farm-book-referer-in-redux';
import { useAppSelector } from '@redux/hooks';
import { getTrans } from '@i18n/get-trans';
import { useListPopulationsApi } from '@screens/population/hooks/use-list-populations-api';
import { useRouter } from 'next/router';
import { goToUrl } from '@utils/url';
import { MenuButton, MenuContent, MenuItem, MenuRoot } from '@components/ui/menu';
import { ChevronDownFilled } from '@icons/chevron-down/chevron-down-filled';
import { Checkbox } from '@components/ui/checkbox';
import isEqual from 'lodash/isEqual';
import { SectionLoader } from '@components/loaders/section-loader';
import React, { useEffect, useMemo } from 'react';
import { harvestCommonGql } from '@screens/population/apis/common-queries';
import { DateTime } from 'luxon';
import {
  getLastTwoMonthsMeanByVariable,
  getMeanByVariableDiff,
  PerformanceVariable,
  useGetPerformanceVariableOptions
} from '@screens/group-summary/helpers/group-performance';
import { GroupPerformanceChart } from '@screens/group-summary/components/group-performance-chart';
import { BreakDownPerformanceChart } from '@screens/group-summary/components/break-down-performance-chart';
import { GroupPerformanceCycleTable } from '@screens/group-summary/components/group-performance-cycle-table';
import { actionsName } from '@utils/segment';
import { useEnableTechnicalFeatures } from '@hooks/use-is-xpertsea-user';
import { useAnalytics } from '@hooks/use-analytics';
import { PopulationType } from '@redux/farm/set-current-population';
import { isNumber } from '@utils/number';
import { Tooltip } from '@components/ui/tooltip';

export function GroupPerformanceScreen() {
  useSetFarmBookRefererInRedux('groupSummary');
  const { trans } = getTrans();
  const { query } = useRouter();
  const { farmIds } = (query ?? {}) as { farmIds?: string };
  const user = useAppSelector((state) => state.auth.user);
  const farms = useAppSelector((state) => state?.farm?.farmsData);
  const allFarmIds = farms?.map((farm) => farm._id) ?? [];
  const isLoadingFarms = useAppSelector((state) => state?.farm?.isLoadingFarmsData);
  const enableTechnicalFeatures = useEnableTechnicalFeatures();

  const [{ isLoading: isLoadingPonds, data: listPopulationsData }, listPopulations] = useListPopulationsApi();

  const { firstName, lastName } = user ?? {};
  const endDateLuxon = DateTime.local().endOf('month');
  const endDate = endDateLuxon.toISODate();
  const startDate = endDateLuxon.minus({ months: 11 }).startOf('month').toISODate();

  const populations = listPopulationsData?.populations ?? [];
  const ponds = listPopulationsData?.ponds ?? [];
  useEffect(() => {
    if (!allFarmIds?.length || (typeof farmIds === 'string' && !farmIds)) return;

    const selectedFarmIds = typeof farmIds !== 'undefined' ? farmIds.split('-').filter((value) => value) : allFarmIds;

    listPopulations({
      loadRelated: true,
      params: {
        page: { size: 1000, current: 1 },
        filter: {
          isFinalHarvestOnly: true,
          farmId: selectedFarmIds,
          harvestTo: endDate,
          harvestFrom: startDate
        }
      },
      fields: {
        populations: {
          _id: 1,
          cycle: 1,
          pondId: 1,
          stockedAt: 1,
          farmId: 1,
          stockedAtTimezone: 1,
          seedingQuantity: 1,
          lastMonitoringDate: 1,
          isMonitoringStarted: 1,
          seedingAverageWeight: 1,
          lastMonitoringStatus: 1,
          stockingCostsMillar: 1,
          geneticName: 1,
          kampiMortality: 1,
          hatcheryName: 1,
          cycleInformation: { target: { cycleLength: 1 }, equipment: { numberOfAutoFeeders: 1 } },
          harvest: harvestCommonGql
        }
      }
    });
  }, [farmIds, startDate, endDate, JSON.stringify(allFarmIds)]);

  if (!enableTechnicalFeatures) {
    return null;
  }

  if (isLoadingFarms) {
    return (
      <Flex flexDirection='column' gap='sm' bgColor='bg.gray.medium' p='md' data-cy='group-performance-screen'>
        <Flex align='center' gap='2xl' mb='md-alt'>
          <Skeleton h='30px' w='175px' />
          <Skeleton h='40px' w='215px' rounded='full' />
        </Flex>
        <Flex align='center' gap='md'>
          {new Array(7).fill(0).map((_, index) => {
            return <Skeleton key={index} h='40px' w='70px' rounded='full' />;
          })}
        </Flex>
        <Flex gap='sm-alt'>
          <Skeleton flex={1} h='340px' rounded='2xl' />
          <Skeleton flex={1} h='340px' rounded='2xl' />
        </Flex>
        <Skeleton w='100%' h='340px' rounded='2xl' />
      </Flex>
    );
  }

  if (!farms?.length) {
    return (
      <Flex data-cy='group-performance-screen' p='md' bgColor='bg.gray.medium' align='center' gap='lg-alt'>
        <Heading size='heavy300'>
          {trans('t_hello')}
          {firstName || lastName ? `, ${firstName ?? ''} ${lastName ?? ''}` : ''}!
        </Heading>
        {trans('t_no_farms_found')}
      </Flex>
    );
  }
  return (
    <Flex
      pos='relative'
      flexDirection='column'
      gap='sm'
      bgColor='bg.gray.medium'
      p='md'
      data-cy='group-performance-screen'
    >
      <SectionLoader isLoading={isLoadingPonds} />
      <Flex align='center' gap='2xl' mb='md-alt'>
        <Heading size='heavy300'>
          {trans('t_hello')}
          {firstName || lastName ? `, ${firstName ?? ''} ${lastName ?? ''}` : ''}!
        </Heading>
        <FarmSelector />
      </Flex>
      <VariableSelector populations={populations} />
      <Flex gap='sm-alt' flexDirection={{ base: 'column', lg: 'row' }}>
        <GroupPerformanceChart populations={populations} />
        <BreakDownPerformanceChart populations={populations} />
      </Flex>
      <GroupPerformanceCycleTable populations={populations} ponds={ponds} />
    </Flex>
  );
}

function VariableSelector(props: { populations: PopulationType[] }) {
  const { populations } = props;
  const { route, query } = useRouter();
  const { variable: queryVariable = 'profitPerHaPerDay' } = (query ?? {}) as { variable?: PerformanceVariable };
  const { trackAction } = useAnalytics();
  const lang = useAppSelector((state) => state.app.lang);

  const { trans } = getTrans();

  const variables: PerformanceVariable[] = [
    'profitPerHaPerDay',
    'costPoundHarvest',
    'adjustedFcr',
    'survival',
    'totalBiomassLbsByHa',
    'totalRevenuePoundHarvest',
    'growthLinear',
    'rofi'
  ];

  const labels = useGetPerformanceVariableOptions();
  const lastTwoMonthsMeanVariables = useMemo(() => {
    return getLastTwoMonthsMeanByVariable({
      populations,
      variables
    });
  }, [populations]);

  return (
    <Flex direction='column' gap='3md'>
      <Box>
        <Heading mb='xs' size='heavy300'>
          {trans('t_farm_breakdown')}
        </Heading>
        <Text size='label300' color='text.gray.disabled'>
          {trans('t_farm_breakdown_description')}
        </Text>
      </Box>
      <Flex align='center' gap='sm-alt' flexWrap='wrap'>
        {variables.map((variable) => {
          const outline = '1px solid {colors.border.gray.hover}';
          const activeStyles = variable === queryVariable ? { outline } : {};
          const variableOptions = labels[variable];
          const { currentMean, previousMean, currentDateLuxon, prevDateLuxon, diffMean, color, bgColor } =
            getMeanByVariableDiff({
              variable,
              means: lastTwoMonthsMeanVariables,
              options: variableOptions,
              lang
            });
          const variableLabel = variableOptions?.title ?? variable;

          return (
            <Tooltip
              key={variable}
              content={
                <>
                  {!currentMean && <Text size='label300'>{trans('t_no_harvests_recorded_this_month')}</Text>}
                  {!!currentMean && (
                    <Flex direction='column' gap='xs'>
                      <Text size='label300'>{variableLabel}</Text>
                      <Text size='label300'>
                        {currentDateLuxon.toFormat('MMMM yyyy')}: {currentMean}
                      </Text>
                      <Text size='label300'>
                        {prevDateLuxon.toFormat('MMMM yyyy')}: {previousMean}
                      </Text>
                      {!!diffMean && (
                        <Text size='label300'>
                          {trans('t_change')}: {diffMean}
                        </Text>
                      )}
                    </Flex>
                  )}
                </>
              }
            >
              <Flex
                p='md'
                cursor='pointer'
                bgColor='white'
                direction='column'
                minW='187px'
                gap='xs'
                rounded='xl'
                _hover={{ outline }}
                {...activeStyles}
                onClick={() => {
                  goToUrl({ route, params: { ...query, variable } });
                  trackAction(actionsName.groupPerformanceVariableClicked, { variable: variableLabel }).then();
                }}
              >
                <Text size='label400'>{variableLabel}</Text>

                <Flex align='center' gap='sm'>
                  <Heading size='heavy300'>{isNumber(currentMean) ? currentMean : '-'}</Heading>
                  {!!diffMean && (
                    <Text p='2xs' color={color} bgColor={bgColor} rounded='base' size='label400'>
                      {diffMean}
                    </Text>
                  )}
                </Flex>
              </Flex>
            </Tooltip>
          );
        })}
      </Flex>
    </Flex>
  );
}

function FarmSelector() {
  const { trans } = getTrans();
  const { route, query } = useRouter();
  const { farmIds } = (query ?? {}) as { farmIds?: string };

  const farms = useAppSelector((state) => state.farm?.farmsData);
  const allFarmIds = farms?.map((farm) => farm._id) ?? [];
  const selectedFarmIds = typeof farmIds !== 'undefined' ? farmIds.split('-') : allFarmIds;

  const handleFarmToggle = (farmId: string, checked: boolean) => {
    if (farmId === 'all') {
      goToUrl({ route, params: { ...query, farmIds: checked ? undefined : [] } });
    } else {
      const updatedFarmIds = checked ? [...selectedFarmIds, farmId] : selectedFarmIds.filter((id) => id !== farmId);
      const farmIdsToShow = updatedFarmIds.filter((value) => value);
      goToUrl({ route, params: { ...query, farmIds: farmIdsToShow.length ? farmIdsToShow.join('-') : [] } });
    }
  };
  const isAllFarmsSelected = isEqual(allFarmIds, selectedFarmIds);

  return (
    <MenuRoot closeOnSelect={false} data-cy='dashboard-farm-selector'>
      <MenuButton minW='215px' justifyContent='space-between'>
        {trans('t_filter_farms')} <ChevronDownFilled hasBackground={false} color='icon.gray' ms='xs-alt' />
      </MenuButton>
      <MenuContent>
        <MenuItem value='all' p={0}>
          <Checkbox
            boxSize='100%'
            p='xs'
            checked={isAllFarmsSelected}
            onCheckedChange={(e) => handleFarmToggle('all', !!e.checked)}
          >
            {trans('t_all_farms')}
          </Checkbox>
        </MenuItem>
        {farms?.map((farm) => {
          return (
            <MenuItem p={0} key={farm._id} value={farm._id}>
              <Checkbox
                boxSize='100%'
                p='xs'
                checked={isAllFarmsSelected || selectedFarmIds.includes(farm._id)}
                onCheckedChange={(e) => handleFarmToggle(farm._id, !!e.checked)}
              >
                {farm.name}
              </Checkbox>
            </MenuItem>
          );
        })}
      </MenuContent>
    </MenuRoot>
  );
}
