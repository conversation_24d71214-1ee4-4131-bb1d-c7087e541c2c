import { BoxProps, Flex, Text } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { useState } from 'react';
import { AllFarmsAtHarvestChart } from '@screens/group-summary/components/all-farms-at-harvest-chart';
import { GetFarmSummaryData } from '@screens/group-summary/hooks/use-farm-summary-data';
import { CardContainer } from '@components/card-container/card-container';
import { BaseMenu } from '@components/base/base-menu';
import { usePermission } from '@hooks/use-permission';
import { useAppSelector } from '@redux/hooks';
import { ChartTimeFilter, ChartTimeFilterProps } from '@screens/group-summary/components/chart-time-filter';

export type AllFarmsAtHarvestChartType = 'revenue' | 'biomass';
type AllFarmsAtHarvestSelectOptions = { value: AllFarmsAtHarvestChartType; label: string };
interface RevenueAtHarvestProps extends BoxProps {
  farmsMap: GetFarmSummaryData['farmsMap'];
}

export function AllFarmsAtHarvest(props: RevenueAtHarvestProps) {
  const { farmsMap, ...rest } = props;
  const farmsData = useAppSelector((state) => state.farm?.farmsData);
  const { trans } = getTrans();

  const isAdmin = usePermission({
    role: 'admin',
    entity: 'farm',
    entityIds: farmsData?.map((farm) => farm._id)
  });

  const graphOptions = [
    ...(isAdmin ? [{ value: 'revenue', label: trans('t_revenue_at_harvest') }] : []),
    { value: 'biomass', label: trans('t_biomass_at_harvest') }
  ];
  const defaultChartType = isAdmin ? 'revenue' : 'biomass';
  const [chartTimeFilter, setChartTimeFilter] = useState<ChartTimeFilterProps['chartTimeFilter']>('weekly');
  const [chartType, setChartType] = useState<AllFarmsAtHarvestChartType>(defaultChartType);
  return (
    <CardContainer pt='sm-alt' {...rest}>
      <Flex
        flexDir={['column', 'row', 'row']}
        align={['flex-start', 'center', 'center']}
        gap='md'
        justify='space-between'
      >
        {graphOptions.length > 1 && (
          <BaseMenu
            h='40px'
            w='auto'
            variant='link'
            color='text.gray'
            options={graphOptions}
            _active={{ color: 'dark' }}
            _hover={{ textDecor: 'none' }}
            selectedOption={graphOptions.find((option) => option.value === chartType)}
            onItemSelect={(option: AllFarmsAtHarvestSelectOptions) => {
              setChartType(option.value);
            }}
          />
        )}
        {graphOptions.length === 1 && (
          <Text size='label100' lineHeight='20px'>
            {graphOptions[0].label}
          </Text>
        )}

        <ChartTimeFilter chartTimeFilter={chartTimeFilter} setChartTimeFilter={setChartTimeFilter} />
      </Flex>
      <AllFarmsAtHarvestChart farmsMap={farmsMap} chartTimeFilter={chartTimeFilter} chartType={chartType} />
    </CardContainer>
  );
}
