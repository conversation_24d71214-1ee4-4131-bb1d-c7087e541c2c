import { Box, Flex, Heading, Text } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { convertUnitByDivision, convertUnitByMultiplication, formatNumber, isNumber } from '@utils/number';
import { useAppSelector } from '@redux/hooks';
import { useMemo } from 'react';
import flatMap from 'lodash/flatMap';
import { GetFarmSummaryData, PondInsideFarmSummary } from '@screens/group-summary/hooks/use-farm-summary-data';
import min from 'lodash/min';
import max from 'lodash/max';
import sum from 'lodash/sum';
import isString from 'lodash/isString';
import { AdminOnlyWrapper } from '@components/permission-wrappers/admin-only-wrapper';
import { usePermission } from '@hooks/use-permission';
import {
  PondFilterPopoverFormType,
  useGenerateFilterTags
} from '@components/pond-filter-popover/use-generate-filter-tags';
import { getGroupSummaryLabelMap } from '@screens/group-summary/helpers/group-view';
import { OneValueRow } from '@components/summary-drawer/one-row-value';
import { DateTime } from 'luxon';
import { getDaysDiffBetweenDates } from '@screens/farm/helpers/farm-dates';

type GroupSummaryProps = {
  farmsMap: Pick<GetFarmSummaryData, 'farmsMap'>;
  farmOverviewFilter: PondFilterPopoverFormType;
};

export function GroupSummary(props: GroupSummaryProps) {
  const { farmsMap, farmOverviewFilter } = props;

  const { trans } = getTrans();

  const lang = useAppSelector((state) => state.app.lang);
  const user = useAppSelector((state) => state.auth.user);
  const farmsData = useAppSelector((state) => state.farm?.farmsData);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};

  const isAdmin = usePermission({
    role: 'admin',
    entity: 'farm',
    entityIds: farmsData?.map((farm) => farm._id)
  });

  const isSupervisor = usePermission({
    role: 'supervisor',
    entity: 'farm',
    entityIds: farmsData?.map((farm) => farm._id)
  });

  const isAdminOrSupervisor = isAdmin || isSupervisor;

  const summaryLabelMap = getGroupSummaryLabelMap({ unitsConfig });

  const filterTags = useGenerateFilterTags({
    cycleDaysLessThan: farmOverviewFilter?.cycleDays?.lessThan,
    cycleDaysMoreThan: farmOverviewFilter?.cycleDays?.moreThan,
    abwLessThan: farmOverviewFilter?.abw?.lessThan,
    abwMoreThan: farmOverviewFilter?.abw?.moreThan,
    flagged: farmOverviewFilter?.flagged,
    aboveTarget: farmOverviewFilter?.aboveTarget,
    onTrack: farmOverviewFilter?.onTrack,
    offTrack: farmOverviewFilter?.offTrack,
    aboveCarryingCapacity: farmOverviewFilter?.aboveCarryingCapacity,
    superVisor: farmOverviewFilter?.superVisor
  });

  const groupSummaryData = useMemo(() => {
    const allPonds: PondInsideFarmSummary[] = flatMap(farmsMap, 'ponds');

    const dryPonds = sum(flatMap(farmsMap, 'inactivePondsCount'));
    const stockedPonds = sum(flatMap(farmsMap, 'activePondsCount'));

    //financials
    const totalRevenueArray: number[] = [];
    const totalProfitArray: number[] = [];
    const totalAccumulatedCostsArray: number[] = [];
    const revenuePerPoundArray: number[] = [];

    //abw
    const abwArray: number[] = [];
    const growth1WeekAvgArray: number[] = [];
    const growth2WeekAvgArray: number[] = [];
    const growth3WeekAvgArray: number[] = [];
    //biomass
    const biomassLbsArray: number[] = [];
    const totalBiomassLbsArray: number[] = [];
    const totalProcessedBiomassLbsArray: number[] = [];

    //feed
    const feedKgByHaArray: number[] = [];
    const totalFeedGivenKgArray: number[] = [];
    const totalFeedGivenLbsArray: number[] = [];

    const survivalArray: number[] = [];

    const fcrCumulativeArray: number[] = [];
    const pondSizeArray: number[] = [];
    const cycleDaysArray: number[] = [];

    let totalWeightedBiomass = 0;

    for (const pond of allPonds) {
      const { pondSize, lastHistoryData, farmTimezone, stockedAt, stockedAtTimezone } = pond;
      const {
        averageWeight,
        totalProfit,
        totalRevenue,
        cumulativeFcr,
        revenuePerPound,
        biomassLbs,
        totalBiomassLbs,
        totalProcessedBiomassLbs,
        biomassLbsByHa,
        feedKgByHa,
        totalCosts,
        weeklyGrowth,
        growthTwoWeeks,
        growthThreeWeeks,
        survivalWithPartialHarvest,
        totalFeedGivenKg,
        totalFeedGivenLbs,
        date
      } = lastHistoryData ?? {};

      if (isString(date) && isString(stockedAt)) {
        const historyDateLuxon = DateTime.fromISO(date, { zone: farmTimezone });
        const stockedAtLuxon = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone ?? farmTimezone });

        const cycleDays = getDaysDiffBetweenDates({
          baseDate: historyDateLuxon.toFormat('yyyy-MM-dd'),
          dateToCompare: stockedAtLuxon.toFormat('yyyy-MM-dd')
        });

        cycleDaysArray.push(cycleDays);
      }

      //financials
      if (isNumber(pondSize)) pondSizeArray.push(pondSize);
      if (isNumber(totalRevenue)) totalRevenueArray.push(totalRevenue);
      if (isNumber(totalProfit)) totalProfitArray.push(totalProfit);
      if (isNumber(totalCosts)) totalAccumulatedCostsArray.push(totalCosts);
      if (isNumber(revenuePerPound)) revenuePerPoundArray.push(revenuePerPound);

      //abw
      if (isNumber(averageWeight)) abwArray.push(averageWeight);
      if (isNumber(weeklyGrowth)) growth1WeekAvgArray.push(weeklyGrowth);
      if (isNumber(growthTwoWeeks)) growth2WeekAvgArray.push(growthTwoWeeks);
      if (isNumber(growthThreeWeeks)) growth3WeekAvgArray.push(growthThreeWeeks);

      //biomass
      if (isNumber(biomassLbs)) biomassLbsArray.push(biomassLbs); // live biomass
      if (isNumber(biomassLbsByHa) && isNumber(pondSize)) totalWeightedBiomass += pondSize * biomassLbsByHa;
      if (isNumber(totalBiomassLbs)) totalBiomassLbsArray.push(totalBiomassLbs); // total biomass
      if (isNumber(totalProcessedBiomassLbs)) totalProcessedBiomassLbsArray.push(totalProcessedBiomassLbs); // total processed biomass

      //feed
      if (isNumber(feedKgByHa)) feedKgByHaArray.push(feedKgByHa);
      if (isNumber(totalFeedGivenKg)) totalFeedGivenKgArray.push(totalFeedGivenKg);
      if (isNumber(totalFeedGivenLbs)) totalFeedGivenLbsArray.push(totalFeedGivenLbs);

      //survival
      if (isNumber(cumulativeFcr)) fcrCumulativeArray.push(cumulativeFcr);
      if (isNumber(survivalWithPartialHarvest)) survivalArray.push(survivalWithPartialHarvest);
    }

    const totalPondSize = sum(pondSizeArray);
    const totalCycleDays = sum(cycleDaysArray);
    const totalBiomassLbs = sum(biomassLbsArray); // live biomass

    //financials
    const totalRevenue = sum(totalRevenueArray);
    const totalProfit = sum(totalProfitArray);
    const totalAccumulatedCosts = sum(totalAccumulatedCostsArray);
    const costPoundHarvest = totalAccumulatedCosts / sum(totalBiomassLbsArray);
    const costPerPoundProcessed = totalAccumulatedCosts / sum(totalProcessedBiomassLbsArray);
    const avgRevenuePerPound = sum(revenuePerPoundArray) / revenuePerPoundArray.length;
    const profitPerHaPerDay = totalProfit / totalCycleDays / totalPondSize;

    //abw
    const minAbw = min(abwArray);
    const maxAbw = max(abwArray);
    const avgAbw = sum(abwArray) / abwArray.length;
    const growth1WeekAvg = sum(growth1WeekAvgArray) / growth1WeekAvgArray.length;
    const growth2WeekAvg = sum(growth2WeekAvgArray) / growth2WeekAvgArray.length;
    const growth3WeekAvg = sum(growth3WeekAvgArray) / growth3WeekAvgArray.length;

    //biomass
    const biomassLbs = totalBiomassLbs;
    const totalBiomassLbsPerHa = totalWeightedBiomass / totalPondSize;
    const biomassLbsPerHaPerDay = totalBiomassLbs / totalCycleDays / totalPondSize;

    //feed
    const avgKgPerHaPerDay = sum(feedKgByHaArray) / feedKgByHaArray.length;
    const avgTotalFeedGivenKg = sum(totalFeedGivenKgArray) / totalFeedGivenKgArray.length;
    const avgTotalFeedGivenLb = sum(totalFeedGivenLbsArray) / totalFeedGivenLbsArray.length;

    //survival
    const avgFcrCumulative = sum(fcrCumulativeArray) / fcrCumulativeArray.length;
    const avgSurvival = sum(survivalArray) / survivalArray.length;

    const totalProfitValue = isAdmin ? formatNumber(totalProfit, { lang, fractionDigits: 0, isCurrency: true }) : 'N/A';
    const totalRevenueValue = isAdmin
      ? formatNumber(totalRevenue, { lang, fractionDigits: 0, isCurrency: true })
      : 'N/A';
    const costPerPoundProcessedValue = isAdminOrSupervisor
      ? formatNumber(convertUnitByMultiplication(costPerPoundProcessed, unitsConfig?.biomass), {
          lang,
          fractionDigits: 2,
          isCurrency: true
        })
      : 'N/A';

    const costPoundHarvestValue = isAdminOrSupervisor
      ? formatNumber(convertUnitByMultiplication(costPoundHarvest, unitsConfig?.biomass), {
          lang,
          fractionDigits: 2,
          isCurrency: true
        })
      : 'N/A';

    const totalAccumulatedCostsValue = isAdminOrSupervisor
      ? formatNumber(totalAccumulatedCosts, { lang, fractionDigits: 0, isCurrency: true })
      : 'N/A';
    const avgRevenuePerPoundValue = isAdmin
      ? formatNumber(convertUnitByMultiplication(avgRevenuePerPound, unitsConfig?.biomass), {
          lang,
          fractionDigits: 2,
          isCurrency: true
        })
      : 'N/A';
    const profitPerHaPerDayValue = isAdmin
      ? formatNumber(profitPerHaPerDay, { lang, fractionDigits: 2, isCurrency: true })
      : 'N/A';

    return {
      staticValues: {
        dryPonds,
        stockedPonds,
        totalRevenue: totalRevenue ? totalRevenueValue : '-'
      },
      totalProfit: totalProfit ? totalProfitValue : '-',
      totalCost: totalAccumulatedCosts ? totalAccumulatedCostsValue : '-',
      costPerPound: costPerPoundProcessed ? costPerPoundProcessedValue : '-',
      costPoundHarvest: costPoundHarvest ? costPoundHarvestValue : '-',
      revenuePerPound: avgRevenuePerPound ? avgRevenuePerPoundValue : '-',
      profitPerHaPerDay: profitPerHaPerDay ? profitPerHaPerDayValue : '-',
      averageWeight: avgAbw
        ? `${formatNumber(avgAbw, { lang, fractionDigits: 2 })} [${formatNumber(minAbw, { lang, fractionDigits: 2 })} - ${formatNumber(maxAbw, { lang, fractionDigits: 2 })}]`
        : '-',
      growth1WeekAvg: growth1WeekAvg ? `${formatNumber(growth1WeekAvg, { lang, fractionDigits: 2 })}` : '-',
      growth2WeekAvg: growth2WeekAvg ? `${formatNumber(growth2WeekAvg, { lang, fractionDigits: 2 })}` : '-',
      growth3WeekAvg: growth3WeekAvg ? `${formatNumber(growth3WeekAvg, { lang, fractionDigits: 2 })}` : '-',
      totalBiomassLbs: biomassLbs
        ? formatNumber(convertUnitByDivision(biomassLbs, unitsConfig?.biomass), { lang, fractionDigits: 0 })
        : '-',
      biomassLbsPerHa: totalBiomassLbsPerHa
        ? formatNumber(convertUnitByDivision(totalBiomassLbsPerHa, unitsConfig?.biomass), { lang, fractionDigits: 0 })
        : '-',
      biomassLbsPerHaPerDay: biomassLbsPerHaPerDay
        ? formatNumber(convertUnitByDivision(biomassLbsPerHaPerDay, unitsConfig?.biomass), { lang, fractionDigits: 1 })
        : '-',
      avgKgPerHaPerDay: avgKgPerHaPerDay ? `${formatNumber(avgKgPerHaPerDay, { lang, fractionDigits: 1 })}` : '-',
      avgTotalFeedGivenKg: avgTotalFeedGivenKg ? formatNumber(avgTotalFeedGivenKg, { lang, fractionDigits: 0 }) : '-',
      avgTotalFeedGivenLb: avgTotalFeedGivenLb ? formatNumber(avgTotalFeedGivenLb, { lang, fractionDigits: 0 }) : '-',
      avgFcrCumulative: avgFcrCumulative ? `${formatNumber(avgFcrCumulative, { lang, fractionDigits: 2 })}` : '-',
      avgSurvival: avgSurvival ? formatNumber(avgSurvival, { lang, fractionDigits: 0, isPercentage: true }) : '-'
    };
  }, [farmsMap]);
  const { totalRevenue, dryPonds, stockedPonds } = groupSummaryData.staticValues;
  return (
    <Flex flexDirection='column' css={{ '& > div:last-child': { borderBottom: 'none' } }}>
      {!!filterTags.length && (
        <Flex gap='sm-alt' align='center' flexWrap='wrap'>
          <Text size='label300' me='xs-alt'>
            {trans('t_filtered_on')}:
          </Text>
          {filterTags.map((tag, index) => (
            <Flex align='center' minH='20px' borderRadius='4xl' bgColor='white' px='2sm' key={index}>
              {tag.label}
            </Flex>
          ))}
        </Flex>
      )}
      <AdminOnlyWrapper isAllFarms>
        <Flex align='center' p='md' gap='xl-alt' borderRadius='lg-alt' bgColor='bg.brandBlue.weakShade2' mb='md-alt'>
          <Text size='label100'>{trans('t_total_sale_value')}</Text>
          <Heading size='heavy200' color='text.brandBlue' letterSpacing='unset'>
            {totalRevenue}
          </Heading>
        </Flex>
      </AdminOnlyWrapper>

      <OneValueRow
        label={trans('t_total_number_of_ponds')}
        value={formatNumber(dryPonds + stockedPonds, { lang, fractionDigits: 0 })}
        border='none'
      />
      <Box borderBottom='0.5px solid' borderColor='border.gray'>
        <OneValueRow
          label={trans('t_stocked_ponds')}
          value={formatNumber(stockedPonds, { lang, fractionDigits: 0 })}
          border='none'
          ps='lg-alt'
        />
        <OneValueRow
          label={trans('t_dry_ponds')}
          value={formatNumber(dryPonds, { lang, fractionDigits: 0 })}
          border='none'
          ps='lg-alt'
        />
      </Box>
      {Object.entries(groupSummaryData).map(([key, value]) => {
        const label = summaryLabelMap[key as keyof typeof summaryLabelMap];
        if (!label || typeof value === 'object' || value === 'N/A') return null;
        return <OneValueRow key={key} label={label} value={value} />;
      })}
    </Flex>
  );
}
