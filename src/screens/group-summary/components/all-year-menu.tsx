import { Dispatch, SetStateAction } from 'react';
import { getTrans } from '@i18n/get-trans';
import { getLast12MonthsBoundaryOptions } from '@screens/group-summary/helpers/group-performance';
import { BaseMenu } from '@components/base/base-menu';

interface AllYearMenuProps {
  selectedMonth: string;
  setSelectedMonth: Dispatch<SetStateAction<string>>;
}
export function AllYearMenu(props: AllYearMenuProps) {
  const { selectedMonth, setSelectedMonth } = props;

  const { trans } = getTrans();

  const options = [{ label: trans('t_all_year'), value: '' }, ...getLast12MonthsBoundaryOptions()];
  const selectedOption = options.find((option) => option.value === selectedMonth) ?? options[0];
  return (
    <BaseMenu
      selectedOption={selectedOption}
      options={options}
      onItemSelect={(option) => setSelectedMonth(option.value)}
      data-cy='group-performance-all-year-menu'
    />
  );
}
