import { Flex, FlexProps } from '@chakra-ui/react';
import { useAnalytics } from '@hooks/use-analytics';
import { getTrans } from '@i18n/get-trans';
import { actionsName } from '@utils/segment';

export type ChartTimeFilterProps = {
  containerProps?: FlexProps;
  chartTimeFilter?: 'weekly' | 'monthly';
  setChartTimeFilter: (value: ChartTimeFilterProps['chartTimeFilter']) => void;
};

export function ChartTimeFilter(props: ChartTimeFilterProps) {
  const { containerProps, chartTimeFilter, setChartTimeFilter } = props;

  const { trackAction } = useAnalytics();
  const { trans } = getTrans();

  return (
    <Flex h='40px' px='3xs' gap='2xs' rounded='4xl' align='center' bgColor='bg.gray.strong' {...containerProps}>
      {[
        {
          title: trans('t_weekly'),
          isActive: chartTimeFilter === 'weekly',
          onClick: () => {
            setChartTimeFilter('weekly');
            trackAction(actionsName.revenueAtHarvestChartIntervalClicked, { timeFilter: 'weekly' });
          }
        },
        {
          title: trans('t_monthly'),
          isActive: chartTimeFilter === 'monthly',
          onClick: () => {
            setChartTimeFilter('monthly');
            trackAction(actionsName.revenueAtHarvestChartIntervalClicked, { timeFilter: 'monthly' });
          }
        }
      ].map((item, index) => (
        <Flex
          key={index}
          minW='84px'
          align='center'
          rounded='4xl'
          justify='center'
          cursor='pointer'
          onClick={item.onClick}
          textStyle='button.100'
          px='2sm'
          py='sm-alt'
          {...(item.isActive && {
            bgColor: 'white'
          })}
        >
          {item.title}
        </Flex>
      ))}
    </Flex>
  );
}
