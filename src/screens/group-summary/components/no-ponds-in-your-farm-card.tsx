import { Flex, FlexProps, Text } from '@chakra-ui/react';
import { AddEditPondModal } from '@screens/pond/components/modals/add-edit-pond-modal';
import { BaseButton } from '@components/base/base-button';
import { actionsName } from '@utils/segment';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';

export function NoPondsInYourFarmCard(props: FlexProps) {
  const { trans } = getTrans();

  const farmId = useAppSelector((state) => state.farm?.currentFarm?._id);

  return (
    <Flex
      p='md'
      gap='sm-alt'
      maxW='467px'
      rounded='xl'
      direction='column'
      bgColor='bg.gray.medium'
      {...props}
      data-cy='ponds-list-no-data'
    >
      <Text fontWeight={500} fontSize='lg'>
        {trans('t_no_ponds_in_your_farm')}
      </Text>
      <Text fontWeight={400} fontSize='md'>
        {trans('t_create_a_new_pond')}
      </Text>
      <AddEditPondModal farmId={farmId} isInPondListView={true}>
        <BaseButton
          px='xs'
          size='sm'
          minW='90px'
          fontSize='md'
          fontWeight={500}
          data-cy='create-new-pond-btn'
          analyticsId={actionsName.createNewPondClicked}
        >
          {trans('t_new_pond')}
        </BaseButton>
      </AddEditPondModal>
    </Flex>
  );
}
