import { convertUnitByDivision, formatNumber } from '@utils/number';
import { useMemo } from 'react';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import type { Options } from 'highcharts';
import { GetFarmSummaryData } from '@screens/group-summary/hooks/use-farm-summary-data';
import { DateTime } from 'luxon';
import sumBy from 'lodash/sumBy';
import groupBy from 'lodash/groupBy';

import { AllFarmsAtHarvestChartType } from '@screens/group-summary/components/all-farms-at-harvest';
import isUndefined from 'lodash/isUndefined';
import { HighchartsNextComp } from '@components/base/highcharts-next-comp';
import { useToken } from '@chakra-ui/react';
import { ChartTimeFilterProps } from '@screens/group-summary/components/chart-time-filter';
import {
  FilterObject,
  getTimeFilterKey,
  sortChartDataByTimeFilterKeys,
  timeFilterKeyToTitle
} from '@screens/group-summary/helpers/revenue-at-harvest-chart';

type RevenueAtHarvestChartProps = {
  farmsMap: GetFarmSummaryData['farmsMap'];
  chartTimeFilter: ChartTimeFilterProps['chartTimeFilter'];
  chartType: AllFarmsAtHarvestChartType;
};

export function AllFarmsAtHarvestChart(props: RevenueAtHarvestChartProps) {
  const { farmsMap, chartTimeFilter, chartType } = props;

  const { trans } = getTrans();
  const [gray200, gray400, gray800, graphBrandBlue] = useToken('colors', [
    'gray.200',
    'gray.400',
    'gray.800',
    'graphBrandBlue'
  ]);
  const [fontFamily] = useToken('fonts', ['body']);

  const lang = useAppSelector((state) => state.app.lang);
  const user = useAppSelector((state) => state.auth.user);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const { biomass: biomassUnit } = unitsConfig ?? {};
  const isBiomassUnitLbs = biomassUnit === 'lbs' || isUndefined(biomassUnit);

  const chartData = useMemo(() => {
    const chartDataByTimeFilter: Record<string, FilterObject[]> = {};

    for (const [farmId, farm] of Object.entries(farmsMap)) {
      const { ponds, farmName, farmTimezone } = farm;

      for (const pond of ponds) {
        const { profitProjectionOnPlannedHarvestDate, harvestDate, productionPredictionHarvestDate } = pond;

        if (!harvestDate) continue;

        let y;
        if (chartType === 'biomass') {
          y = convertUnitByDivision(productionPredictionHarvestDate?.biomassLbs, biomassUnit);
        } else {
          y = profitProjectionOnPlannedHarvestDate?.totalRevenue;
        }

        const key = getTimeFilterKey({ date: harvestDate, farmTimezone, chartTimeFilter });
        chartDataByTimeFilter[key] = chartDataByTimeFilter[key] || [];

        chartDataByTimeFilter[key].push({
          y,
          custom: { farmId, farmName },
          x: DateTime.fromISO(harvestDate, { zone: farmTimezone }).toMillis()
        });
      }
    }

    const sortedData = sortChartDataByTimeFilterKeys({ chartTimeFilter, chartDataByTimeFilter });

    return Object.keys(sortedData).map((key, index) => {
      const data = sortedData[key];
      const totalValue = sumBy(data, 'y');

      const dataGroupedByFarm = groupBy(data, 'custom.farmId');
      const name = timeFilterKeyToTitle({ key, lang, chartTimeFilter });

      return {
        name,
        x: index,
        y: totalValue,
        custom: {
          key,
          name,
          totalValue,
          farms: Object.entries(dataGroupedByFarm).map(([_, farmData]) => {
            const farmTotalValue = sumBy(farmData, 'y');

            return {
              farmTotalValue: farmTotalValue,
              farmName: farmData[0].custom.farmName
            };
          })
        }
      };
    });
  }, [farmsMap, chartTimeFilter, chartType, biomassUnit, lang]);

  const defaultGrayColor = gray800;
  const defaultTextStyle = { fontSize: '10px', fontWeight: '600', color: defaultGrayColor };

  const options: Options = {
    title: { text: undefined },
    chart: {
      type: 'column',
      animation: false,
      spacing: [30, 0, 0, 0],
      style: { fontFamily, height: 203 }
    },
    plotOptions: {
      column: {
        color: graphBrandBlue,
        pointWidth: 24,
        borderRadius: 6,
        animation: false,
        groupPadding: 0.45,
        pointPadding: chartTimeFilter === 'weekly' ? 0.19 : 0.29
      }
    },
    xAxis: {
      tickWidth: 0,
      lineWidth: 0,
      type: 'category',
      gridLineWidth: 0,
      categories: chartData?.map((item) => item.name),
      labels: {
        style: defaultTextStyle
      },
      title: {
        margin: 10,
        text: trans('t_harvest_date'),
        style: defaultTextStyle
      }
    },
    yAxis: {
      tickWidth: 0,
      lineWidth: 0,
      gridLineWidth: 0.5,
      gridLineColor: gray400,
      gridLineDashStyle: 'LongDash',
      title: {
        margin: 10,
        text:
          chartType === 'biomass'
            ? isBiomassUnitLbs
              ? trans('t_biomass_lb')
              : trans('t_biomass_kg')
            : trans('t_revenue'),
        style: defaultTextStyle
      },
      labels: {
        style: defaultTextStyle,
        formatter: function (): string {
          const isBiomass = chartType === 'biomass';
          return `${isBiomass ? '' : '$'}${formatNumber(this.value as number, { lang, compact: true, fractionDigits: 2, isCurrency: !isBiomass })}`;
        }
      }
    },
    series: [
      {
        type: 'column',
        data: chartData
      }
    ],
    tooltip: {
      padding: 0,
      shadow: false,
      useHTML: true,
      shape: 'rect',
      backgroundColor: 'transparent',
      formatter: function (this): string {
        const { custom } = this as unknown as {
          custom: {
            name: string;
            key: string;
            totalValue: number;
            farms: { farmTotalValue: number; farmName: string }[];
          };
        };

        const { totalValue, farms, name: xAxisName } = custom ?? {};

        let title;
        if (chartType === 'biomass') title = isBiomassUnitLbs ? trans('t_biomass_lb') : trans('t_biomass_kg');
        if (chartType === 'revenue') title = trans('t_revenue');

        let totalString;
        if (chartType === 'biomass') {
          totalString = `${formatNumber(convertUnitByDivision(totalValue, biomassUnit), { lang, fractionDigits: 0 })} ${isBiomassUnitLbs ? trans('t_lb') : trans('t_kg')}`;
        }
        if (chartType === 'revenue') {
          totalString = formatNumber(totalValue, { lang, isCurrency: true, fractionDigits: 0 });
        }

        return `
          <div style="padding: 10px 16px; font-size: 12px; font-weight: 600;background-color: ${gray200}; border-radius: 8px;  min-width: 152px; line-height: 16px; color: ${defaultGrayColor};">
            <p style=" margin-bottom: 10px;">${title} ${xAxisName}:</p>
            <p style=" margin-bottom: 10px;">${trans('t_total')}: ${totalString}</p>

            <div style="display: flex; flex-direction: column; gap: 10px;">
              ${farms
                ?.map((item) => {
                  let farmValueFormatted;
                  if (chartType === 'biomass') {
                    farmValueFormatted = `${formatNumber(convertUnitByDivision(item.farmTotalValue, biomassUnit), { lang, fractionDigits: 0 })} ${isBiomassUnitLbs ? trans('t_lb') : trans('t_kg')}`;
                  }
                  if (chartType === 'revenue') {
                    farmValueFormatted = formatNumber(item.farmTotalValue, {
                      lang,
                      fractionDigits: 0,
                      isCurrency: true
                    });
                  }

                  return `
                  <div style="display: flex; flex-direction: row; gap: 4px; font-size: 12px; font-weight: 500;">
                    <p>${item.farmName}:</p>
                    <p>${farmValueFormatted ?? '-'}</p>
                  </div>
                `;
                })
                .join('')}
            </div>
          </div>
        `;
      }
    }
  };

  return <HighchartsNextComp options={options} containerProps={{ style: { width: '100%', height: '203px' } }} />;
}
