import { ReactNode, useEffect } from 'react';
import { Box, chakra, Flex, Heading, Text, useDisclosure } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { BaseButton } from '@components/base/base-button';
import { GroupViewVariable, useGetGroupViewVariables } from '@screens/group-summary/hooks/use-get-group-view-variables';
import { BaseFormCheckbox } from '@components/form/base-form-checkbox';
import { useFieldArray, useForm } from 'react-hook-form';
import { useAppSelector } from '@redux/hooks';
import { useUpdateUserPreferencesApi } from '@screens/my-account/hooks/use-update-user-preferences';
import { useAnalytics } from '@hooks/use-analytics';
import { actionsName } from '@utils/segment';
import { DialogBody, DialogContent, DialogFooter, DialogHeader, DialogRoot, DialogTitle } from '@components/ui/dialog';

type CustomizeGroupViewModalProps = {
  children: ReactNode;
  defaultValues: FormType;
};

type FormType = { variables: { value: GroupViewVariable }[] };

export function CustomizeGroupViewModal(props: CustomizeGroupViewModalProps) {
  const { children, defaultValues } = props;
  const user = useAppSelector((state) => state.auth.user);
  const [{ isLoading: isUserLoading }, updateUser] = useUpdateUserPreferencesApi();
  const { preferences: prevPreferences } = user ?? {};
  const { viewFields } = prevPreferences ?? {};

  const { trans } = getTrans();
  const groupViewVariables = useGetGroupViewVariables();

  const { open, onOpen, onClose } = useDisclosure();
  const { trackAction } = useAnalytics();
  const maxSelected = 7;

  const { reset, control, watch, setValue, handleSubmit } = useForm<FormType>({ defaultValues });
  const { append, remove } = useFieldArray({ control, name: 'variables' });

  useEffect(() => {
    if (!open) return;
    setValue('variables', defaultValues.variables);
  }, [JSON.stringify(defaultValues?.variables ?? []), open]);

  const variablesWatch = watch('variables');
  const variablesArray = variablesWatch?.map((variable) => variable.value) ?? [];

  const isVariableChecked = (variable: GroupViewVariable) => variablesArray.includes(variable);

  const getVariableIndex = (name: string) => variablesWatch.findIndex((variable) => variable.value === name);

  const isVariableDisabled = (variable: GroupViewVariable) => {
    return variablesArray.length >= maxSelected && !isVariableChecked(variable);
  };

  const onVariableChange = (checked: boolean, variable: GroupViewVariable) => {
    if (checked) append({ value: variable });
    else remove(getVariableIndex(variable));
  };

  const isSubmitDisabled = variablesArray.length < 1;
  const onSubmit = handleSubmit((data: FormType) => {
    if (isSubmitDisabled) return;
    updateUser({
      params: {
        preferences: {
          viewFields: {
            ...viewFields,
            groupViewVariables: data.variables.map((variable) => variable.value)
          }
        }
      },
      successCallback: () => {
        onClose();
        trackAction(actionsName.customizeViewSaveSubmitted, {
          variables: data.variables.map((variable) => variable.value)
        }).then();
      }
    });
  });

  const groupVariables = Object.entries(groupViewVariables);

  return (
    <DialogRoot
      open={open}
      onOpenChange={(e) => {
        e.open ? onOpen() : onClose();
      }}
      scrollBehavior='inside'
      placement='center'
    >
      <Box onClick={onOpen}>{children}</Box>

      <DialogContent minH={{ base: '100vh', md: 'auto' }} borderRadius='2xl' boxShadow='elevation.400'>
        <chakra.form display='contents' onSubmit={onSubmit} noValidate>
          <DialogHeader px='md' pt='md' pb='2lg'>
            <DialogTitle
              display='flex'
              gap={{ base: 'xs-alt', md: 'xl' }}
              flexDirection={{ base: 'column', md: 'row' }}
              minW={{ base: 'auto', md: '370px', lg: '423px' }}
              justifyContent={{ base: 'initial', md: 'space-between' }}
            >
              <Box>
                <Heading size='heavy300'>{trans('t_customize_view')}</Heading>
                <Text size='light300' mt='xs' color='text.gray.weak'>
                  {trans('t_select_up_to_x_variables_you_want', { count: maxSelected })}
                </Text>
              </Box>

              <Text size='heavy300' alignSelf='flex-end'>
                {trans('t_selected', { selected: variablesArray.length, total: maxSelected })}
              </Text>
            </DialogTitle>
          </DialogHeader>
          <DialogBody display='flex' flexDirection='column' px='md' pt={0} pb={0}>
            {groupVariables.map(([key, groupViewVariableItem], index) => {
              const { title, subtitle, variables } = groupViewVariableItem;
              const isFirstElement = index === 0;
              const isLast = index === groupVariables.length - 1;
              return (
                <Flex
                  key={key}
                  flexDir='column'
                  gap='md'
                  py='2lg'
                  {...(isFirstElement && { pt: '2sm' })}
                  borderBottom={!isLast ? '1px solid' : 'none'}
                  borderColor='border.gray'
                >
                  <Flex direction='column' gap='sm-alt'>
                    <Text size='label200'>{title}</Text>
                    {subtitle && (
                      <Text size='light300' color='text.gray.weak'>
                        {subtitle}
                      </Text>
                    )}
                  </Flex>

                  {variables.map((variable) => {
                    const variableValue = variable?.value;
                    return (
                      <BaseFormCheckbox
                        id={variableValue}
                        key={variableValue}
                        label={variable.label}
                        formControlProps={{
                          w: '100%',
                          display: 'flex',
                          flexDir: 'column',
                          justifyContent: 'center'
                        }}
                        checked={isVariableChecked(variableValue)}
                        disabled={isVariableDisabled(variableValue)}
                        onCheckedChange={(change) => onVariableChange(change.checked as boolean, variableValue)}
                      />
                    );
                  })}
                </Flex>
              );
            })}
          </DialogBody>
          <DialogFooter gap='sm-alt' py='sm-alt' px='md' shadow='elevation.200' roundedBottom='2xl'>
            <BaseButton
              variant='secondary'
              w='max-content'
              analyticsId={actionsName.customizeViewCancelClicked}
              onClick={() => {
                onClose();
                reset(defaultValues);
              }}
            >
              {trans('t_cancel')}
            </BaseButton>
            <BaseButton
              analyticsId={actionsName.customizeViewSaveClicked}
              type='submit'
              w='max-content'
              loading={isUserLoading}
              disabled={isSubmitDisabled}
            >
              {trans('t_apply')}
            </BaseButton>
          </DialogFooter>
        </chakra.form>
      </DialogContent>
    </DialogRoot>
  );
}
