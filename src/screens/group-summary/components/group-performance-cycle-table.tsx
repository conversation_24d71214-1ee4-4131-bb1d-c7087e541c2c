import { TableRootContainer } from '@components/table/table-root-container';
import { Flex, Heading, Table, TableColumnHeaderProps, Text } from '@chakra-ui/react';
import { CycleComparisonCustomizeMenu } from '@screens/cycle-comparison/components/cycle-comparison-customize-menu';
import { useCycleComparisonSelectedVariables } from '@screens/cycle-comparison/hooks/use-selected-variables';
import { getTrans } from '@i18n/get-trans';
import { AllYearMenu } from '@screens/group-summary/components/all-year-menu';
import React, { JSX, ReactNode, useMemo, useState } from 'react';
import {
  CycleComparisonVariableType,
  useGetCycleVariableLabels
} from '@screens/cycle-comparison/utils/cycle-comparison-variables';
import { CurrentFarmPonds } from '@redux/farm/set-current-farm-ponds';
import { PopulationType } from '@redux/farm/set-current-population';
import { DateTime } from 'luxon';
import keyBy from 'lodash/keyBy';
import { useAppSelector } from '@redux/hooks';
import { BaseLink } from '@components/base/base-link';
import { slugify } from '@utils/string';
import { PopulationHarvest } from '@xpertsea/module-farm-sdk';
import { convertUnitByDivision, convertUnitByMultiplication, formatNumber, isNumber } from '@utils/number';
import { SwapVertical } from '@icons/swap/swap-vertical';
import { sortArray, sortCompareNumber, sortCompareString } from '@utils/sort-array';
import get from 'lodash/get';

const firstColumnWidth = 145;
const GROUP_CYCLE_VARIABLES_KEY = 'groupCycleVariables';

type SortDirection = 'asc' | 'desc';
type SortBy = Exclude<CycleComparisonVariableType, 'cycleDays'> | 'pondName' | 'farmName' | 'daysOfCulture';
type SortConfig = { sortDir: SortDirection; sortBy: SortBy };
type SortingMap = Partial<Record<SortBy, keyof PopulationType>>;

const populationSortNumericMap: SortingMap = {
  costMillar: 'stockingCostsMillar',
  stockingWeight: 'seedingAverageWeight'
};

const populationSortStringMap: SortingMap = {
  nauplii: 'geneticName',
  hatcheries: 'hatcheryName'
};

interface GroupPerformanceCycleTableProps {
  ponds: CurrentFarmPonds;
  populations: PopulationType[];
}
export function GroupPerformanceCycleTable(props: GroupPerformanceCycleTableProps) {
  const { ponds, populations } = props;
  const { trans } = getTrans();

  const [selectedMonth, setSelectedMonth] = useState<string>(DateTime.local().startOf('month').toFormat('yyyy-MM-dd'));
  const [sortConfig, setSortConfig] = useState<SortConfig>({ sortDir: 'asc', sortBy: 'harvestDate' });
  const [selectedVariables, handleOnSelectVariables] = useCycleComparisonSelectedVariables(GROUP_CYCLE_VARIABLES_KEY);
  const variableLabels = useGetCycleVariableLabels();
  const farms = useAppSelector((state) => state?.farm?.farmsData);

  const { pondsHashMap, farmsHashMap, sortedPopulations } = useMemo(() => {
    const filteredPopulations = selectedMonth
      ? populations.filter((population) => {
          const date = population.harvest.date;
          if (!date) return false;
          const harvestDate = date.includes('GMT') ? new Date(date).toISOString() : date;
          const month = DateTime.fromISO(harvestDate).startOf('month').toFormat('yyyy-MM-dd');
          return month === selectedMonth && population.pondId && population.farmId;
        })
      : populations.filter((population) => population.pondId && population.farmId);

    const pondsHashMap = keyBy(ponds, '_id');
    const farmsHashMap = keyBy(farms, '_id');

    const sortPopulationList = () => {
      if (sortConfig.sortBy === 'pondName') {
        return sortArray({
          list: filteredPopulations,
          sortDir: sortConfig.sortDir,
          compareFunction: (a, b) => {
            const firstItemValue = get(pondsHashMap[a.pondId], 'name');
            const secondItemValue = get(pondsHashMap[b.pondId], 'name');
            return sortCompareString(firstItemValue, secondItemValue);
          }
        });
      }

      if (sortConfig.sortBy === 'pondSize') {
        return sortArray({
          list: filteredPopulations,
          compareFunction: (a, b) => {
            const firstItemValue = get(pondsHashMap[a.pondId], 'size');
            const secondItemValue = get(pondsHashMap[b.pondId], 'size');
            return sortCompareNumber({
              firstItem: firstItemValue,
              secondItem: secondItemValue,
              sortDir: sortConfig.sortDir,
              hasEmptyValuePriority: false
            });
          }
        });
      }

      if (sortConfig.sortBy === 'farmName') {
        return sortArray({
          list: filteredPopulations,
          sortDir: sortConfig.sortDir,
          compareFunction: (a, b) => {
            const firstItemValue = get(farmsHashMap[a.farmId], 'name');
            const secondItemValue = get(farmsHashMap[b.farmId], 'name');
            return sortCompareString(firstItemValue, secondItemValue);
          }
        });
      }

      if (sortConfig.sortBy === 'harvestDate') {
        return sortArray({
          list: filteredPopulations,
          compareFunction: (a, b) => {
            const firstItemValue = get(a, 'harvest.date');
            const secondItemValue = get(b, 'harvest.date');
            const firstItemHarvestDate = firstItemValue?.includes('GMT')
              ? new Date(firstItemValue).toISOString()
              : firstItemValue;
            const secondItemHarvestDate = secondItemValue?.includes('GMT')
              ? new Date(secondItemValue).toISOString()
              : secondItemValue;

            const dateA = DateTime.fromISO(firstItemHarvestDate);
            const dateB = DateTime.fromISO(secondItemHarvestDate);

            return sortCompareNumber({
              firstItem: dateA.toMillis(),
              secondItem: dateB.toMillis(),
              sortDir: sortConfig.sortDir,
              hasEmptyValuePriority: false
            });
          }
        });
      }

      if (sortConfig.sortBy === 'stockingDate') {
        return sortArray({
          list: filteredPopulations,
          compareFunction: (a, b) => {
            const { stockedAt: stockedAtA, stockedAtTimezone: stockedAtTimezoneA } = a ?? {};
            const { stockedAt: stockedAtB, stockedAtTimezone: stockedAtTimezoneB } = b ?? {};

            const firstItemDate = stockedAtA?.includes('GMT') ? new Date(stockedAtA).toISOString() : stockedAtA;
            const secondItemDate = stockedAtB?.includes('GMT') ? new Date(stockedAtB).toISOString() : stockedAtB;

            const dateA = DateTime.fromISO(firstItemDate, { zone: stockedAtTimezoneA });
            const dateB = DateTime.fromISO(secondItemDate, { zone: stockedAtTimezoneB });

            return sortCompareNumber({
              firstItem: dateA.toMillis(),
              secondItem: dateB.toMillis(),
              sortDir: sortConfig.sortDir,
              hasEmptyValuePriority: false
            });
          }
        });
      }

      if (sortConfig.sortBy === 'numberOfAutofeeders' || sortConfig.sortBy === 'autoFeederPerHa') {
        return sortArray({
          list: filteredPopulations,
          compareFunction: (a, b) => {
            let firstItemValue = get(a, 'cycleInformation.equipment.numberOfAutoFeeders');
            let secondItemValue = get(b, 'cycleInformation.equipment.numberOfAutoFeeders');

            if (sortConfig.sortBy === 'autoFeederPerHa') {
              const firstItemSize = get(pondsHashMap[a.pondId], 'size');
              const secondItemSize = get(pondsHashMap[b.pondId], 'size');
              firstItemValue =
                isNumber(firstItemValue) && firstItemSize ? firstItemValue / firstItemSize : firstItemValue;
              secondItemValue =
                isNumber(secondItemValue) && secondItemSize ? secondItemValue / secondItemSize : secondItemValue;
            }

            return sortCompareNumber({
              firstItem: firstItemValue,
              secondItem: secondItemValue,
              sortDir: sortConfig.sortDir,
              hasEmptyValuePriority: false
            });
          }
        });
      }

      if (sortConfig.sortBy === 'stockingQuantity' || sortConfig.sortBy === 'stockingQuantityByHa') {
        return sortArray({
          list: filteredPopulations,
          compareFunction: (a, b) => {
            let firstItemValue = get(a, 'seedingQuantity');
            let secondItemValue = get(b, 'seedingQuantity');

            if (sortConfig.sortBy === 'stockingQuantityByHa') {
              const firstItemSize = get(pondsHashMap[a.pondId], 'size');
              const secondItemSize = get(pondsHashMap[b.pondId], 'size');
              firstItemValue = isNumber(firstItemValue) && firstItemSize ? firstItemValue / firstItemSize : 0;
              secondItemValue = isNumber(secondItemValue) && secondItemSize ? secondItemValue / secondItemSize : 0;
            }

            return sortCompareNumber({
              firstItem: firstItemValue,
              secondItem: secondItemValue,
              sortDir: sortConfig.sortDir,
              hasEmptyValuePriority: false
            });
          }
        });
      }

      if (sortConfig.sortBy === 'productionDays') {
        return sortArray({
          list: filteredPopulations,
          compareFunction: (a, b) => {
            const { dryDaysBeforeStocking: dryDaysBeforeStockingA, cycleInformation: cycleInformationA } = a ?? {};
            const { dryDaysBeforeStocking: dryDaysBeforeStockingB, cycleInformation: cycleInformationB } = b ?? {};

            const cycleLengthA = cycleInformationA?.target?.cycleLength;
            const cycleLengthB = cycleInformationB?.target?.cycleLength;
            const firstItemValue = isNumber(cycleLengthA) ? cycleLengthA + (dryDaysBeforeStockingA ?? 0) : 0;
            const secondItemValue = isNumber(cycleLengthB) ? cycleLengthB + (dryDaysBeforeStockingB ?? 0) : 0;
            return sortCompareNumber({
              firstItem: firstItemValue,
              secondItem: secondItemValue,
              sortDir: sortConfig.sortDir,
              hasEmptyValuePriority: false
            });
          }
        });
      }

      if (populationSortNumericMap[sortConfig.sortBy]) {
        return sortArray({
          list: filteredPopulations,
          compareFunction: (a, b) => {
            const firstItemValue = get(a, populationSortNumericMap[sortConfig.sortBy]);
            const secondItemValue = get(b, populationSortNumericMap[sortConfig.sortBy]);

            return sortCompareNumber({
              firstItem: firstItemValue,
              secondItem: secondItemValue,
              sortDir: sortConfig.sortDir,
              hasEmptyValuePriority: false
            });
          }
        });
      }

      if (populationSortStringMap[sortConfig.sortBy]) {
        return sortArray({
          list: filteredPopulations,
          sortDir: sortConfig.sortDir,
          compareFunction: (a, b) => {
            const firstItemValue = get(a, populationSortStringMap[sortConfig.sortBy]);
            const secondItemValue = get(b, populationSortStringMap[sortConfig.sortBy]);
            return sortCompareString(firstItemValue, secondItemValue);
          }
        });
      }

      return sortArray({
        list: filteredPopulations,
        compareFunction: (a, b) => {
          const firstItemValue = get(a, `harvest.${sortConfig.sortBy}`);
          const secondItemValue = get(b, `harvest.${sortConfig.sortBy}`);
          return sortCompareNumber({
            firstItem: firstItemValue,
            secondItem: secondItemValue,
            sortDir: sortConfig.sortDir,
            hasEmptyValuePriority: false
          });
        }
      });
    };

    const sortedPopulations = sortPopulationList();

    return { pondsHashMap, farmsHashMap, sortedPopulations };
  }, [selectedMonth, populations, ponds, farms, sortConfig.sortBy, sortConfig.sortDir]);

  const cellWidthValues = (value: number) => ({
    w: `${value}px`,
    minW: `${value}px`,
    maxW: `${value}px`
  });

  return (
    <Flex direction='column' gap='sm-alt' borderRadius='2xl' bgColor='white' p='md' mb='xl'>
      <Flex align='center' justify='space-between' flexWrap='wrap' gap='sm-alt'>
        <Heading size='heavy300'>{trans('t_completed_cycles')}</Heading>
        <Flex align='center' gap='sm-alt'>
          <AllYearMenu selectedMonth={selectedMonth} setSelectedMonth={setSelectedMonth} />
          <CycleComparisonCustomizeMenu
            selectedVariables={selectedVariables}
            onSelectVariables={handleOnSelectVariables}
          />
        </Flex>
      </Flex>

      <TableRootContainer firstColumnWidth={firstColumnWidth} hasShadow={sortedPopulations.length > 4}>
        <Table.Header>
          <Table.Row>
            <HeaderCell
              zIndex={1}
              borderTopLeftRadius='2xl'
              position='sticky !important'
              left='0px !important'
              field='pondName'
              sortBy={sortConfig.sortBy}
              sortDir={sortConfig.sortDir}
              {...cellWidthValues(firstColumnWidth)}
              onClick={() => {
                setSortConfig((prev) => ({
                  sortBy: 'pondName',
                  sortDir: prev.sortBy === 'pondName' && prev.sortDir === 'asc' ? 'desc' : 'asc'
                }));
              }}
            >
              {trans('t_pond')}
            </HeaderCell>
            <HeaderCell
              field='farmName'
              sortBy={sortConfig.sortBy}
              sortDir={sortConfig.sortDir}
              onClick={() => {
                setSortConfig((prev) => ({
                  sortBy: 'farmName',
                  sortDir: prev.sortBy === 'farmName' && prev.sortDir === 'asc' ? 'desc' : 'asc'
                }));
              }}
            >
              {trans('t_farm')}
            </HeaderCell>
            {selectedVariables.map((variable) => {
              const label = variableLabels[variable];
              const sortVariable = variable === 'cycleDays' ? 'daysOfCulture' : variable;
              return (
                <HeaderCell
                  sortBy={sortConfig.sortBy}
                  sortDir={sortConfig.sortDir}
                  field={sortVariable}
                  key={variable}
                  onClick={() => {
                    setSortConfig((prev) => ({
                      sortBy: sortVariable,
                      sortDir: prev.sortBy === sortVariable && prev.sortDir === 'asc' ? 'desc' : 'asc'
                    }));
                  }}
                >
                  {label ?? variable}
                </HeaderCell>
              );
            })}
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {!sortedPopulations.length && (
            <Table.Row>
              <Table.Cell
                position='sticky !important'
                left='0px !important'
                zIndex={1}
                {...cellWidthValues(firstColumnWidth)}
              >
                <Text>{trans('t_no_data_to_display')}</Text>
              </Table.Cell>
            </Table.Row>
          )}
          {sortedPopulations.map((population) => {
            const pond = pondsHashMap[population.pondId];
            const farm = farmsHashMap[pond?.farmId];
            const pondName = pond?.name;
            const pondEid = pond?.eid;
            const pondSize = pond?.size;
            const farmName = farm?.name;
            const farmEid = farm?.eid;
            const {
              stockingCostsMillar,
              stockedAt,
              stockedAtTimezone,
              seedingQuantity,
              seedingAverageWeight,
              cycleInformation,
              dryDaysBeforeStocking,
              geneticName,
              hatcheryName,
              harvest,
              _id
            } = population;
            const { target, equipment } = cycleInformation ?? {};
            const cycleLength = target?.cycleLength;
            const numberOfAutoFeeders = equipment?.numberOfAutoFeeders;
            return (
              <Table.Row key={_id}>
                <Table.Cell
                  position='sticky !important'
                  left='0px !important'
                  zIndex={1}
                  {...cellWidthValues(firstColumnWidth)}
                >
                  {!pondName && '-'}
                  {pondName && (
                    <BaseLink
                      route='/farm/[farmEid]/pond/[pondEid]'
                      _hover={{ textDecoration: 'underline' }}
                      params={{
                        farmEid: slugify(`${farmEid}-${farmName}`),
                        pondEid: slugify(`${pondEid}-${pondName}`)
                      }}
                    >
                      <Text
                        size={{ base: 'light300', lg: 'light200' }}
                        overflow='hidden'
                        textOverflow='ellipsis'
                        maxW='115px'
                        title={pondName}
                      >
                        {pondName}
                      </Text>
                    </BaseLink>
                  )}
                </Table.Cell>
                <Table.Cell>
                  <Text size='label200'>{farm?.name ?? '-'}</Text>
                </Table.Cell>
                {selectedVariables.map((variable) => {
                  return (
                    <CycleVariableMapper
                      key={variable}
                      variable={variable}
                      harvest={harvest}
                      cycleLength={cycleLength}
                      pondSize={pondSize}
                      hatcheryName={hatcheryName}
                      geneticName={geneticName}
                      stockedAt={stockedAt}
                      stockedAtTimezone={stockedAtTimezone}
                      seedingQuantity={seedingQuantity}
                      seedingAverageWeight={seedingAverageWeight}
                      stockingCostsMillar={stockingCostsMillar}
                      dryDaysBeforeStocking={dryDaysBeforeStocking}
                      numberOfAutoFeeders={numberOfAutoFeeders}
                    />
                  );
                })}
              </Table.Row>
            );
          })}
        </Table.Body>
      </TableRootContainer>
    </Flex>
  );
}
interface HeaderCellProps extends TableColumnHeaderProps {
  sortBy: SortBy;
  sortDir: SortDirection;
  field: SortBy;
}
function HeaderCell(props: HeaderCellProps) {
  const { children, sortBy, sortDir, field, ...rest } = props;

  return (
    <Table.ColumnHeader cursor='pointer' {...rest}>
      <Text size='label200' display='flex' alignItems='center' gap='sm-alt' justifyContent='space-between'>
        {children}
        <SwapVertical
          upArrowColor={sortBy === field && sortDir === 'asc' ? 'text.gray' : undefined}
          downArrowColor={sortBy === field && sortDir === 'desc' ? 'text.gray' : undefined}
        />
      </Text>
    </Table.ColumnHeader>
  );
}
interface CycleVariableMapperProps {
  variable: CycleComparisonVariableType;
  harvest: PopulationHarvest;
  stockingCostsMillar: number;
  stockedAt: string;
  stockedAtTimezone: string;
  seedingQuantity: number;
  pondSize: number;
  seedingAverageWeight: number;
  cycleLength: number;
  dryDaysBeforeStocking: number;
  numberOfAutoFeeders: number;
  geneticName: string;
  hatcheryName: string;
}
function CycleVariableMapper(props: CycleVariableMapperProps) {
  const {
    variable,
    harvest,
    stockingCostsMillar,
    stockedAt,
    stockedAtTimezone,
    seedingQuantity,
    pondSize,
    seedingAverageWeight,
    cycleLength,
    dryDaysBeforeStocking,
    numberOfAutoFeeders,
    geneticName,
    hatcheryName
  } = props;
  const lang = useAppSelector((state) => state.app.lang);
  const user = useAppSelector((state) => state.auth.user);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const biomassUnit = unitsConfig?.biomass;
  const { trans } = getTrans();

  const {
    hasEstimatedValues,
    profitPerHaPerDay,
    totalProfit,
    rofi,
    profitPoundHarvest,
    costPerPound,
    costPoundHarvest,
    totalRevenuePound,
    totalRevenuePoundHarvest,
    totalRevenue,
    totalCosts,
    stockingCosts,
    cumulativeFeedCosts,
    cumulativeOverheadCosts,
    overheadCostDryDays,
    overheadCostProductiveDays,
    accumulatedOtherDirectCosts,
    averageWeight,
    growthLinear,
    growthDaily,
    survival,
    totalBiomassLbs,
    totalBiomassLbsByHa,
    totalAnimalsHarvested,
    animalsHarvestedM2,
    animalsHarvestedHa,
    totalPartialHarvestBiomassLbs,
    lbsHarvested,
    numberOfPartialHarvest,
    cumulativeFcr,
    adjustedFcr,
    totalFeedGivenKg,
    feedKgHaDay,
    feedCostPerKg,
    date,
    daysOfCulture
  } = harvest;
  const harvestDate = date?.includes('GMT') ? new Date(date).toISOString() : date;
  const stockingDate = stockedAt?.includes('GMT') ? new Date(stockedAt).toISOString() : stockedAt;
  const variablesHash: Record<CycleComparisonVariableType, JSX.Element> = {
    profitPerHaPerDay: (
      <EstimatedValueWrapper hasEstimatedValues={isNumber(profitPerHaPerDay) && hasEstimatedValues}>
        {isNumber(profitPerHaPerDay)
          ? formatNumber(profitPerHaPerDay, { lang, fractionDigits: 2, isCurrency: true })
          : '-'}
      </EstimatedValueWrapper>
    ),
    totalProfit: (
      <EstimatedValueWrapper hasEstimatedValues={isNumber(totalProfit) && hasEstimatedValues}>
        {isNumber(totalProfit) ? formatNumber(totalProfit, { lang, fractionDigits: 0, isCurrency: true }) : '-'}
      </EstimatedValueWrapper>
    ),
    profitPoundHarvest: (
      <EstimatedValueWrapper hasEstimatedValues={isNumber(profitPoundHarvest) && hasEstimatedValues}>
        {isNumber(profitPoundHarvest)
          ? formatNumber(convertUnitByMultiplication(profitPoundHarvest, biomassUnit), {
              lang,
              fractionDigits: 2,
              isCurrency: true
            })
          : '-'}
      </EstimatedValueWrapper>
    ),
    costPerPound: (
      <EstimatedValueWrapper>
        {isNumber(costPerPound)
          ? formatNumber(convertUnitByMultiplication(costPerPound, biomassUnit), {
              lang,
              fractionDigits: 2,
              isCurrency: true
            })
          : '-'}
      </EstimatedValueWrapper>
    ),
    costPoundHarvest: (
      <EstimatedValueWrapper>
        {isNumber(costPoundHarvest)
          ? formatNumber(convertUnitByMultiplication(costPoundHarvest, biomassUnit), {
              lang,
              fractionDigits: 2,
              isCurrency: true
            })
          : '-'}
      </EstimatedValueWrapper>
    ),
    totalRevenuePound: (
      <EstimatedValueWrapper hasEstimatedValues={isNumber(totalRevenuePound) && hasEstimatedValues}>
        {isNumber(totalRevenuePound)
          ? formatNumber(convertUnitByMultiplication(totalRevenuePound, biomassUnit), {
              lang,
              fractionDigits: 2,
              isCurrency: true
            })
          : '-'}
      </EstimatedValueWrapper>
    ),
    totalRevenuePoundHarvest: (
      <EstimatedValueWrapper hasEstimatedValues={isNumber(totalRevenuePoundHarvest) && hasEstimatedValues}>
        {isNumber(totalRevenuePoundHarvest)
          ? formatNumber(convertUnitByMultiplication(totalRevenuePoundHarvest, biomassUnit), {
              lang,
              fractionDigits: 2,
              isCurrency: true
            })
          : '-'}
      </EstimatedValueWrapper>
    ),
    totalRevenue: (
      <EstimatedValueWrapper hasEstimatedValues={isNumber(totalRevenue) && hasEstimatedValues}>
        {isNumber(totalRevenue) ? formatNumber(totalRevenue, { lang, fractionDigits: 0, isCurrency: true }) : '-'}
      </EstimatedValueWrapper>
    ),
    rofi: (
      <EstimatedValueWrapper>
        {isNumber(rofi) ? formatNumber(rofi, { lang, isPercentage: true, fractionDigits: 0 }) : '-'}
      </EstimatedValueWrapper>
    ),
    totalCosts: (
      <EstimatedValueWrapper>
        {isNumber(totalCosts) ? formatNumber(totalCosts, { lang, fractionDigits: 0, isCurrency: true }) : '-'}
      </EstimatedValueWrapper>
    ),
    stockingCosts: (
      <EstimatedValueWrapper>
        {isNumber(stockingCosts) ? formatNumber(stockingCosts, { lang, fractionDigits: 0, isCurrency: true }) : '-'}
      </EstimatedValueWrapper>
    ),
    cumulativeFeedCosts: (
      <EstimatedValueWrapper>
        {isNumber(cumulativeFeedCosts)
          ? formatNumber(cumulativeFeedCosts, { lang, fractionDigits: 0, isCurrency: true })
          : '-'}
      </EstimatedValueWrapper>
    ),
    cumulativeOverheadCosts: (
      <EstimatedValueWrapper>
        {isNumber(cumulativeOverheadCosts)
          ? formatNumber(cumulativeOverheadCosts, { lang, fractionDigits: 0, isCurrency: true })
          : '-'}
      </EstimatedValueWrapper>
    ),
    overheadCostDryDays: (
      <EstimatedValueWrapper>
        {isNumber(overheadCostDryDays)
          ? formatNumber(overheadCostDryDays, { lang, fractionDigits: 0, isCurrency: true })
          : '-'}
      </EstimatedValueWrapper>
    ),
    overheadCostProductiveDays: (
      <EstimatedValueWrapper>
        {isNumber(overheadCostProductiveDays)
          ? formatNumber(overheadCostProductiveDays, { lang, fractionDigits: 0, isCurrency: true })
          : '-'}
      </EstimatedValueWrapper>
    ),
    accumulatedOtherDirectCosts: (
      <EstimatedValueWrapper>
        {isNumber(accumulatedOtherDirectCosts)
          ? formatNumber(accumulatedOtherDirectCosts, { lang, fractionDigits: 0, isCurrency: true })
          : '-'}
      </EstimatedValueWrapper>
    ),
    averageWeight: (
      <EstimatedValueWrapper>
        {isNumber(averageWeight) ? formatNumber(averageWeight, { lang, fractionDigits: 1 }) : '-'}
      </EstimatedValueWrapper>
    ),
    growthLinear: (
      <EstimatedValueWrapper>
        {isNumber(growthLinear) ? formatNumber(growthLinear, { lang, fractionDigits: 2 }) : '-'}
      </EstimatedValueWrapper>
    ),
    growthDaily: (
      <EstimatedValueWrapper>
        {isNumber(growthDaily) ? formatNumber(growthDaily, { lang, fractionDigits: 2 }) : '-'}
      </EstimatedValueWrapper>
    ),
    survival: (
      <EstimatedValueWrapper>
        {isNumber(survival) ? formatNumber(survival, { lang, isPercentage: true, fractionDigits: 0 }) : '-'}
      </EstimatedValueWrapper>
    ),
    totalBiomassLbs: (
      <EstimatedValueWrapper>
        {isNumber(totalBiomassLbs)
          ? formatNumber(convertUnitByDivision(totalBiomassLbs, biomassUnit), { lang, fractionDigits: 0 })
          : '-'}
      </EstimatedValueWrapper>
    ),
    totalBiomassLbsByHa: (
      <EstimatedValueWrapper>
        {isNumber(totalBiomassLbsByHa)
          ? formatNumber(convertUnitByDivision(totalBiomassLbsByHa, biomassUnit), { lang, fractionDigits: 0 })
          : '-'}
      </EstimatedValueWrapper>
    ),
    totalAnimalsHarvested: (
      <EstimatedValueWrapper>
        {isNumber(totalAnimalsHarvested) ? formatNumber(totalAnimalsHarvested, { lang, fractionDigits: 0 }) : '-'}
      </EstimatedValueWrapper>
    ),
    animalsHarvestedM2: (
      <EstimatedValueWrapper>
        {isNumber(animalsHarvestedM2) ? formatNumber(animalsHarvestedM2, { lang, fractionDigits: 2 }) : '-'}
      </EstimatedValueWrapper>
    ),
    animalsHarvestedHa: (
      <EstimatedValueWrapper>
        {isNumber(animalsHarvestedHa) ? formatNumber(animalsHarvestedHa, { lang, fractionDigits: 0 }) : '-'}
      </EstimatedValueWrapper>
    ),
    totalPartialHarvestBiomassLbs: (
      <EstimatedValueWrapper>
        {isNumber(totalPartialHarvestBiomassLbs)
          ? formatNumber(convertUnitByDivision(totalPartialHarvestBiomassLbs, biomassUnit), {
              lang,
              fractionDigits: 0
            })
          : '-'}
      </EstimatedValueWrapper>
    ),
    lbsHarvested: (
      <EstimatedValueWrapper>
        {isNumber(lbsHarvested)
          ? formatNumber(convertUnitByDivision(lbsHarvested, unitsConfig?.biomass), { lang, fractionDigits: 0 })
          : '-'}
      </EstimatedValueWrapper>
    ),
    numberOfPartialHarvest: (
      <EstimatedValueWrapper>{isNumber(numberOfPartialHarvest) ? numberOfPartialHarvest : '-'}</EstimatedValueWrapper>
    ),
    cumulativeFcr: (
      <EstimatedValueWrapper>
        {isNumber(cumulativeFcr) ? formatNumber(cumulativeFcr, { lang, fractionDigits: 2 }) : '-'}
      </EstimatedValueWrapper>
    ),
    adjustedFcr: (
      <EstimatedValueWrapper>
        {isNumber(adjustedFcr) ? formatNumber(adjustedFcr, { lang, fractionDigits: 2 }) : '-'}
      </EstimatedValueWrapper>
    ),
    totalFeedGivenKg: (
      <EstimatedValueWrapper>
        {isNumber(totalFeedGivenKg) ? formatNumber(totalFeedGivenKg, { lang, fractionDigits: 0 }) : '-'}
      </EstimatedValueWrapper>
    ),
    feedKgHaDay: (
      <EstimatedValueWrapper>
        {isNumber(feedKgHaDay) ? formatNumber(feedKgHaDay, { lang, fractionDigits: 0 }) : '-'}
      </EstimatedValueWrapper>
    ),
    feedCostPerKg: (
      <EstimatedValueWrapper>
        {isNumber(feedCostPerKg) ? formatNumber(feedCostPerKg, { lang, fractionDigits: 2 }) : '-'}
      </EstimatedValueWrapper>
    ),
    costMillar: (
      <EstimatedValueWrapper>
        {isNumber(stockingCostsMillar)
          ? formatNumber(stockingCostsMillar, { fractionDigits: 2, lang, isCurrency: true })
          : '-'}
      </EstimatedValueWrapper>
    ),
    stockingDate: (
      <EstimatedValueWrapper>
        {stockingDate
          ? DateTime.fromISO(stockingDate, { zone: stockedAtTimezone }).toFormat('LLL dd, yyyy', {
              locale: lang
            })
          : '-'}
      </EstimatedValueWrapper>
    ),
    harvestDate: (
      <EstimatedValueWrapper>
        {harvestDate ? DateTime.fromISO(harvestDate).toFormat('LLL dd, yyyy', { locale: lang }) : '-'}
      </EstimatedValueWrapper>
    ),
    stockingQuantity: (
      <EstimatedValueWrapper>
        {isNumber(seedingQuantity) ? formatNumber(seedingQuantity, { lang, fractionDigits: 0 }) : '-'}
      </EstimatedValueWrapper>
    ),
    stockingQuantityByHa: (
      <EstimatedValueWrapper>
        {isNumber(seedingQuantity) && pondSize
          ? formatNumber(seedingQuantity / pondSize, { lang, fractionDigits: 0 })
          : '-'}
      </EstimatedValueWrapper>
    ),
    stockingWeight: (
      <EstimatedValueWrapper>
        {isNumber(seedingAverageWeight) ? formatNumber(seedingAverageWeight, { lang, fractionDigits: 2 }) : '-'}
      </EstimatedValueWrapper>
    ),
    pondSize: (
      <EstimatedValueWrapper>
        {pondSize ? `${formatNumber(pondSize, { lang, fractionDigits: 2 })} ${trans('t_ha')}` : '-'}
      </EstimatedValueWrapper>
    ),
    cycleDays: (
      <EstimatedValueWrapper>
        {isNumber(daysOfCulture)
          ? trans('t_days_d', { count: formatNumber(daysOfCulture, { lang, fractionDigits: 0 }) })
          : '-'}
      </EstimatedValueWrapper>
    ),
    productionDays: (
      <EstimatedValueWrapper>
        {isNumber(cycleLength)
          ? trans('t_days_d', {
              count: formatNumber(cycleLength + (dryDaysBeforeStocking ?? 0), { lang, fractionDigits: 0 })
            })
          : '-'}
      </EstimatedValueWrapper>
    ),
    numberOfAutofeeders: (
      <EstimatedValueWrapper>
        {isNumber(numberOfAutoFeeders) ? formatNumber(numberOfAutoFeeders, { lang, fractionDigits: 0 }) : '-'}
      </EstimatedValueWrapper>
    ),
    autoFeederPerHa: (
      <EstimatedValueWrapper>
        {isNumber(numberOfAutoFeeders) && pondSize
          ? formatNumber(numberOfAutoFeeders / pondSize, { lang, fractionDigits: 2 })
          : '-'}
      </EstimatedValueWrapper>
    ),
    nauplii: <EstimatedValueWrapper>{geneticName || '-'}</EstimatedValueWrapper>,
    hatcheries: <EstimatedValueWrapper>{hatcheryName || '-'}</EstimatedValueWrapper>
  };

  return variablesHash[variable];
}

interface EstimatedValueWrapperProps {
  children: ReactNode;
  hasEstimatedValues?: boolean;
}
function EstimatedValueWrapper(props: EstimatedValueWrapperProps) {
  const { children, hasEstimatedValues = false } = props;

  if (!hasEstimatedValues) {
    return (
      <Table.Cell>
        <Text size='label200'>{children}</Text>
      </Table.Cell>
    );
  }

  return (
    <Table.Cell>
      <Flex
        w='max-content'
        align='center'
        justify='center'
        bg={hasEstimatedValues ? 'bg.gray.strong' : 'transparent'}
        px='md'
        h='24px'
        rounded='full'
      >
        <Text size='label200'>{children}*</Text>
      </Flex>
    </Table.Cell>
  );
}
