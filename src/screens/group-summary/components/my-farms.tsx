import { getTrans } from '@i18n/get-trans';
import { Flex, Heading, IconButton, SimpleGrid, Skeleton, Text } from '@chakra-ui/react';
import { slugify } from '@utils/string';
import { BaseLink } from '@components/base/base-link';
import { GetFarmSummaryData, PondInsideFarmSummary } from '@screens/group-summary/hooks/use-farm-summary-data';
import { Dispatch, SetStateAction, useState } from 'react';
import { sortArray, sortCompareString } from '@utils/sort-array';
import get from 'lodash/get';
import { CustomizeGroupViewModal } from '@screens/group-summary/components/customize-group-view-modal';
import { actionsName } from '@utils/segment';
import { useAnalytics } from '@hooks/use-analytics';
import { BaseMenu } from '@components/base/base-menu';
import { ExclamationMarkSolid } from '@icons/exclamation-mark/exclamation-mark-solid';
import { Aquarius } from '@icons/aquarius/aquarius';
import { CardContainer } from '@components/card-container/card-container';
import { ClearNightSolid } from '@icons/clear-night/clear-night-solid';
import { ChevronDownFilled } from '@icons/chevron-down/chevron-down-filled';
import { ChevronUpFilled } from '@icons/chevron-up/chevron-up-filled';
import { FarmAlertKey } from '@components/alert/helper';
import { MissingDataAlertRow } from '@components/alert/missing-data-alert-row';
import { IndicatingKeys, IndicatorOption, IndicatorValues, PondCardViewType } from '@components/pond-card/helper';
import { AlertIconContainer } from '@components/alert/alert-icon-container';
import { PondCard } from '@components/pond-card/pond-card';
import { TrafficConeFilled } from '@icons/traffic-cone/traffic-cone-filled';
import { StarFilled } from '@icons/star/star-filled';
import { CheckFilled } from '@icons/check/check-filled';
import { setCacheItem, sevenDaysCacheTime } from '@utils/local-cache';
import { InView } from 'react-intersection-observer';
import { GroupViewVariable } from '@screens/group-summary/hooks/use-get-group-view-variables';
import { formatNumber } from '@utils/number';
import { useAppSelector } from '@redux/hooks';
import { SettingsAdjustFilled } from '@icons/settings-adjust/settings-adjust-filled';
import { PondFilterPopover } from '@components/pond-filter-popover/pond-filter-popover';
import { PondFilterPopoverFormType } from '@components/pond-filter-popover/use-generate-filter-tags';
import { FilterTags } from '@components/pond-filter-popover/filter-tags';

type FarmsSummaryProps = {
  farmsMap: GetFarmSummaryData['farmsMap'];
  setIndicatingStatus: Dispatch<SetStateAction<IndicatorValues>>;
  selectedIndicatingStatus: IndicatorOption;
  indicatingStatusOptions: IndicatorOption[];
  profitProjectionToView: PondCardViewType;
  setProfitProjectionToView: Dispatch<SetStateAction<PondCardViewType>>;
  customizeViewVariables: GroupViewVariable[];
  farmOverviewFilter: PondFilterPopoverFormType;
  setFarmOverviewFilter: Dispatch<SetStateAction<PondFilterPopoverFormType>>;
};

export function MyFarms(props: FarmsSummaryProps) {
  const {
    farmsMap,
    setIndicatingStatus,
    selectedIndicatingStatus,
    indicatingStatusOptions,
    setProfitProjectionToView,
    profitProjectionToView,
    customizeViewVariables,
    farmOverviewFilter,
    setFarmOverviewFilter
  } = props;
  const { trans } = getTrans();

  return (
    <Flex px='md' flexDir='column' gap='md-alt'>
      <Flex align='center' justify='space-between' flexWrap='wrap' gap='sm'>
        <Heading size='heavy300' letterSpacing='none'>
          {trans('t_my_farms')}
        </Heading>
        <Flex align='center' gap='sm-alt' flexWrap='wrap' w={{ base: '100%', md: 'auto' }}>
          <IndicatingStatusSelector
            setIndicatingStatus={setIndicatingStatus}
            selectedIndicatingStatus={selectedIndicatingStatus}
            indicatingStatusOptions={indicatingStatusOptions}
          />
          <PondViewSelector
            setProfitProjectionToView={setProfitProjectionToView}
            profitProjectionToView={profitProjectionToView}
          />

          <PondFilterPopover defaultValues={farmOverviewFilter} onApplyFilter={setFarmOverviewFilter} />

          <MoreOptionsMenu customizeViewVariables={customizeViewVariables} />
        </Flex>
      </Flex>

      <FilterTags
        cycleDaysLessThan={farmOverviewFilter?.cycleDays?.lessThan}
        cycleDaysMoreThan={farmOverviewFilter?.cycleDays?.moreThan}
        abwLessThan={farmOverviewFilter?.abw?.lessThan}
        abwMoreThan={farmOverviewFilter?.abw?.moreThan}
        flagged={farmOverviewFilter?.flagged}
        aboveTarget={farmOverviewFilter?.aboveTarget}
        onTrack={farmOverviewFilter?.onTrack}
        offTrack={farmOverviewFilter?.offTrack}
        aboveCarryingCapacity={farmOverviewFilter?.aboveCarryingCapacity}
        superVisor={farmOverviewFilter?.superVisor}
        onApplyFilter={setFarmOverviewFilter}
      />

      <FarmCardContainer
        farmsMap={farmsMap}
        profitProjectionToView={profitProjectionToView}
        selectedIndicatingStatus={selectedIndicatingStatus}
        customizeViewVariables={customizeViewVariables}
      />
    </Flex>
  );
}

interface MoreOptionsMenuProps {
  customizeViewVariables: GroupViewVariable[];
}

function MoreOptionsMenu(props: MoreOptionsMenuProps) {
  const { customizeViewVariables } = props;

  return (
    <CustomizeGroupViewModal defaultValues={{ variables: customizeViewVariables.map((item) => ({ value: item })) }}>
      <IconButton
        h='40px'
        w='40px'
        bgColor='white'
        aria-label='filter-button'
        _focus={{ outline: 'none' }}
        css={{
          '&:hover svg.first-path': { fill: 'brandBlue.300' },
          '&:hover svg.second-path': { fill: 'graphBrandBlue' }
        }}
      >
        <SettingsAdjustFilled w='20px' h='20px' transform='scale(1, -1)' />
      </IconButton>
    </CustomizeGroupViewModal>
  );
}

interface IndicatingStatusSelectorProps {
  setIndicatingStatus: Dispatch<SetStateAction<IndicatorValues>>;
  indicatingStatusOptions: IndicatorOption[];
  selectedIndicatingStatus: IndicatorOption;
}

function IndicatingStatusSelector(props: IndicatingStatusSelectorProps) {
  const { setIndicatingStatus, indicatingStatusOptions, selectedIndicatingStatus } = props;
  const { trans } = getTrans();
  const { trackAction } = useAnalytics();

  const mainIndicatingStatusOptions = indicatingStatusOptions.filter((item) => !item.isDisabled);
  const additionalIndicatingStatusOptions = indicatingStatusOptions.filter((item) => item.isDisabled);
  const additionalOptionGroup = additionalIndicatingStatusOptions.length
    ? {
        title: `${trans('t_not_selected_in_pond_card')}:`,
        options: additionalIndicatingStatusOptions
      }
    : undefined;

  return (
    <BaseMenu
      titlePrefix={`${trans('t_indicating_status')}: `}
      selectedOption={selectedIndicatingStatus}
      options={mainIndicatingStatusOptions}
      additionalOptionGroup={additionalOptionGroup}
      onItemSelect={(option) => {
        const value = option.value as IndicatorValues;
        trackAction(actionsName.farmGroupViewIndicatingStatusClicked, { indicatingStatus: value }).then();
        setCacheItem('dashboardIndicatingStatusSelector', value, sevenDaysCacheTime).then();
        setIndicatingStatus(value);
      }}
      w={{ base: '100%', md: 'auto' }}
    />
  );
}

interface PondViewSelectorProps {
  setProfitProjectionToView: Dispatch<SetStateAction<PondCardViewType>>;
  profitProjectionToView: PondCardViewType;
}

function PondViewSelector(props: PondViewSelectorProps) {
  const { setProfitProjectionToView, profitProjectionToView } = props;
  const { trans } = getTrans();
  const { trackAction } = useAnalytics();

  const options: { label: string; value: PondCardViewType }[] = [
    { label: trans('t_current_view'), value: 'current' },
    { label: trans('t_expected_at_harvest'), value: 'harvest' }
  ];
  const selectedOption = options.find((option) => option.value === profitProjectionToView);

  const updateProfitProjectToView = (value: PondCardViewType) => {
    trackAction(actionsName.profitProjectionToViewClicked, { profitProjectionToView: value }).then();
    setCacheItem('dashboardPondView', value, sevenDaysCacheTime).then();
    setProfitProjectionToView(value);
  };

  return (
    <BaseMenu
      selectedOption={selectedOption}
      options={options}
      onItemSelect={(option) => updateProfitProjectToView(option.value as PondCardViewType)}
    />
  );
}

interface FarmCardContainerProps extends Pick<FarmsSummaryProps, 'farmsMap'> {
  profitProjectionToView: PondCardViewType;
  selectedIndicatingStatus: IndicatorOption;
  customizeViewVariables: GroupViewVariable[];
}

function FarmCardContainer(props: FarmCardContainerProps) {
  const { farmsMap, customizeViewVariables, profitProjectionToView, selectedIndicatingStatus } = props;
  const farmsData = Object.entries(farmsMap);

  return (
    <Flex flexDir='column' gap='md'>
      {farmsData.map(([farmId, farmData]) => {
        const { farmName, eid, ponds, alertKey, activePondsCount, farmTimezone, countPondsOverWeekFromOptimal } =
          farmData;

        if (!ponds?.length) return null;

        const farmEid = slugify(`${eid}-${farmName}`);

        const sortedPonds = sortArray({
          list: ponds,
          sortDir: 'asc',
          compareFunction: (a, b) => {
            const firstItemValue = get(a, 'pondName');
            const secondItemValue = get(b, 'pondName');
            return sortCompareString(firstItemValue, secondItemValue);
          }
        });

        return (
          <FarmCard
            key={farmId}
            customizeViewVariables={customizeViewVariables}
            profitProjectionToView={profitProjectionToView}
            selectedIndicatingStatus={selectedIndicatingStatus}
            farmName={farmName}
            alertKey={alertKey}
            farmEid={farmEid}
            farmId={farmId}
            sortedPonds={sortedPonds}
            activePondsCount={activePondsCount}
            farmTimezone={farmTimezone}
            countPondsOverWeekFromOptimal={countPondsOverWeekFromOptimal}
          />
        );
      })}
    </Flex>
  );
}

interface FarmCardProps {
  farmName: string;
  alertKey: FarmAlertKey;
  farmEid: string;
  farmId: string;
  sortedPonds: PondInsideFarmSummary[];
  activePondsCount: number;
  farmTimezone: string;
  countPondsOverWeekFromOptimal: number;
  profitProjectionToView: PondCardViewType;
  selectedIndicatingStatus: IndicatorOption;
  customizeViewVariables: GroupViewVariable[];
}

function FarmCard(props: FarmCardProps) {
  const {
    farmName,
    alertKey,
    farmEid,
    farmId,
    sortedPonds,
    selectedIndicatingStatus,
    customizeViewVariables,
    activePondsCount,
    farmTimezone,
    countPondsOverWeekFromOptimal,
    profitProjectionToView
  } = props;

  const lang = useAppSelector((state) => state.app.lang);

  const [isCollapsed, setIsCollapsed] = useState<boolean>(false);

  const selectedIndicatingStatusValue = selectedIndicatingStatus?.value;
  const { trans } = getTrans();

  let greenCounter = 0;
  let redCounter = 0;
  let blueCounter = 0;

  let estimatedSurvivalRateCounter = 0;
  let estimatedFeedCounter = 0;

  sortedPonds.forEach((pond) => {
    const { indicators, hasFeedEstimatedValues, hasSurvivalEstimatedValues } = pond;

    const indicatingStatusKeyMap: Record<IndicatorValues, IndicatingKeys> = {
      profitPerHaPerDay: 'profitPerHaPerDay',
      costPerPound: 'costPerPound',
      weeklyGrowth: 'growth',
      growth1WeekAvg: 'growth',
      growthLinear: 'growthLinear',
      averageWeight: 'abw',
      survivalRate: 'survivalRate',
      survival: 'survivalRate',
      fcr: 'fcr',
      fcrCumulative: 'fcr'
    };
    const indicatorKey = indicatingStatusKeyMap[selectedIndicatingStatusValue];
    const indicatorValue = indicators?.[indicatorKey]?.[profitProjectionToView]?.color;

    if (hasSurvivalEstimatedValues) {
      estimatedSurvivalRateCounter++;
    }
    if (hasFeedEstimatedValues) {
      estimatedFeedCounter++;
    }

    if (indicatorValue === 'green') {
      greenCounter++;
    }
    if (indicatorValue === 'red') {
      redCounter++;
    }
    if (indicatorValue === 'blue') {
      blueCounter++;
    }
  });

  return (
    <CardContainer display='flex' flexDirection='column' gap='md' p={0}>
      <Flex
        px='md'
        pt='md'
        pb={isCollapsed ? 'md' : 0}
        direction='column'
        gap='sm-alt'
        cursor='pointer'
        onClick={() => setIsCollapsed((prev) => !prev)}
      >
        <Flex align='center' justify='space-between'>
          <BaseLink route='/farm/[farmEid]' textStyle='label.100' params={{ farmEid }} userSelect='none'>
            {farmName ?? '-'}
          </BaseLink>
          {isCollapsed ? (
            <ChevronDownFilled color='icon.gray' hasBackground={false} />
          ) : (
            <ChevronUpFilled color='icon.gray' hasBackground={false} />
          )}
        </Flex>

        <Flex gap='sm-alt' align='center' flexWrap='wrap'>
          {activePondsCount && (
            <Flex
              py='xs'
              px='md'
              gap='sm-alt'
              align='center'
              borderRadius='4xl'
              bgColor='bg.gray.medium'
              w='max-content'
              h='40px'
            >
              <Aquarius w='20px' h='20px' color='icon.gray' />
              <Text size='label200' userSelect='none'>
                {activePondsCount < 2 &&
                  trans('t_count_ponds_singular', {
                    count: formatNumber(activePondsCount, { lang, fractionDigits: 0 })
                  })}
                {activePondsCount >= 2 &&
                  trans('t_count_ponds_plural', { count: formatNumber(activePondsCount, { lang, fractionDigits: 0 }) })}
              </Text>
              {!!greenCounter && (
                <Flex
                  userSelect='none'
                  textStyle='label.200'
                  align='center'
                  gap='xs-alt'
                  bgColor='white'
                  borderRadius='full'
                  px='xs-alt'
                  py='3xs'
                >
                  <StarFilled /> {formatNumber(greenCounter, { lang, fractionDigits: 0 })}
                </Flex>
              )}
              {!!blueCounter && (
                <Flex
                  userSelect='none'
                  textStyle='label.200'
                  align='center'
                  gap='xs-alt'
                  bgColor='white'
                  borderRadius='full'
                  px='xs-alt'
                  py='3xs'
                >
                  <CheckFilled w='20px' h='20px' bgColor='bg.brandBlue.weakShade1' color='icon.brandBlue' />
                  {formatNumber(blueCounter, { lang, fractionDigits: 0 })}
                </Flex>
              )}
              {!!redCounter && (
                <Flex
                  userSelect='none'
                  textStyle='label.200'
                  align='center'
                  gap='xs-alt'
                  bgColor='white'
                  borderRadius='full'
                  px='xs-alt'
                  py='3xs'
                >
                  <TrafficConeFilled /> {formatNumber(redCounter, { lang, fractionDigits: 0 })}
                </Flex>
              )}
            </Flex>
          )}
          {!!countPondsOverWeekFromOptimal && isCollapsed && (
            <AlertIconContainer status='info'>
              <ClearNightSolid color='icon.brandBlue' />
            </AlertIconContainer>
          )}
          {!!alertKey && isCollapsed && (
            <AlertIconContainer status='error'>
              <ExclamationMarkSolid color='icon.semanticRed' />
            </AlertIconContainer>
          )}
        </Flex>
      </Flex>

      {!isCollapsed && (
        <Flex flexDir='column' gap='md' px='md' pb='md'>
          <MissingDataAlertRow
            alertKey={alertKey}
            farmEid={farmEid}
            hasEstimatedData={!!estimatedFeedCounter || !!estimatedSurvivalRateCounter}
          />

          <SimpleGrid columns={[1, 2, 2, 3, 3, 4]} gapX='sm-alt' gapY='md'>
            {sortedPonds.map((pond) => {
              const {
                pondId,
                pondEid,
                pondName,
                isFlagged,
                populationId,
                lastHistoryData,
                profitProjectionOnPlannedHarvestDate,
                productionPredictionHarvestDate,
                optimalHarvestDate,
                indicators,
                manuallyMonitored,
                symmetry,
                lastMonitoringTotalAnimals,
                seedingAverageWeight,
                cycleInformation,
                hasSurvivalEstimatedValues,
                hasFeedEstimatedValues,
                harvestDate,
                manualAverageWeights
              } = pond;

              return (
                <InView rootMargin='50px' fallbackInView triggerOnce key={pondId}>
                  {({ inView, ref }) => (
                    <div ref={ref}>
                      {!inView && <Skeleton h='250px' borderRadius='xl' />}
                      {inView && (
                        <PondCard
                          manualAverageWeights={manualAverageWeights}
                          selectedIndicatingStatus={selectedIndicatingStatus}
                          indicators={indicators}
                          pondId={pondId}
                          pondName={pondName}
                          profitProjectionView={profitProjectionToView}
                          pondEid={pondEid}
                          farmEid={farmEid}
                          farmId={farmId}
                          farmTimezone={farmTimezone}
                          isFlagged={isFlagged}
                          populationId={populationId}
                          lastHistory={lastHistoryData}
                          plannedProfitProjection={profitProjectionOnPlannedHarvestDate}
                          productionPredictionHarvestDate={productionPredictionHarvestDate}
                          optimalHarvestDate={optimalHarvestDate}
                          viewVariables={customizeViewVariables}
                          manuallyMonitored={manuallyMonitored}
                          symmetry={symmetry}
                          lastMonitoringTotalAnimals={lastMonitoringTotalAnimals}
                          seedingAverageWeight={seedingAverageWeight}
                          cycleInformation={cycleInformation}
                          hasSurvivalEstimatedValues={hasSurvivalEstimatedValues}
                          hasFeedEstimatedValues={hasFeedEstimatedValues}
                          harvestPlanDate={harvestDate}
                          hasMonitoring
                        />
                      )}
                    </div>
                  )}
                </InView>
              );
            })}
          </SimpleGrid>
        </Flex>
      )}
    </CardContainer>
  );
}
