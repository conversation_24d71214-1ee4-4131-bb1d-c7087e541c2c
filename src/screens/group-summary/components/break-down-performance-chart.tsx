import { PopulationType } from '@redux/farm/set-current-population';
import { getTrans } from '@i18n/get-trans';
import { useRouter } from 'next/router';
import {
  getMedianByFarmId,
  PerformanceVariable,
  useGetPerformanceVariableOptions
} from '@screens/group-summary/helpers/group-performance';
import { useAppSelector } from '@redux/hooks';
import { Flex, Heading, Text, useToken } from '@chakra-ui/react';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { HighchartsNextComp, HighchartsRef } from '@components/base/highcharts-next-comp';
import type { Options } from 'highcharts';
import { renderToString } from 'react-dom/server';
import { formatNumber } from '@utils/number';
import { AllYearMenu } from '@screens/group-summary/components/all-year-menu';
import { DateTime } from 'luxon';

interface BreakDownPerformanceChartProps {
  populations: PopulationType[];
}

export function BreakDownPerformanceChart(props: BreakDownPerformanceChartProps) {
  const { populations } = props;
  const { trans } = getTrans();
  const { query } = useRouter();
  const { variable: queryVariable = 'profitPerHaPerDay' } = (query ?? {}) as { variable?: PerformanceVariable };
  const [selectedMonth, setSelectedMonth] = useState<string>('');
  const lang = useAppSelector((state) => state.app.lang);
  const farms = useAppSelector((state) => state.farm?.farmsData);

  const labels = useGetPerformanceVariableOptions();
  const [gray200, gray400, gray800, brandGreen800] = useToken('colors', [
    'gray.200',
    'gray.400',
    'gray.800',
    'brandGreen.800'
  ]);
  const [fontFamily] = useToken('fonts', ['body']);

  const {
    title: variableTitle = queryVariable,
    fractionDigits: variableFractionDigits,
    isCurrency: variableIsCurrency,
    isPercentage: variableIsPercentage,
    convertUnit: variableConvertUnit,
    unit: variableUnit
  } = labels[queryVariable] ?? {};

  const series = useMemo(() => {
    const filteredPopulations = selectedMonth
      ? populations.filter((population) => {
          if (!population.harvest?.date) return false;
          const month = DateTime.fromISO(population.harvest.date).startOf('month').toFormat('yyyy-MM-dd');
          return month === selectedMonth;
        })
      : populations;
    return getMedianByFarmId({
      populations: filteredPopulations,
      variable: queryVariable,
      convertUnit: variableConvertUnit,
      unit: variableUnit,
      farms
    });
  }, [
    JSON.stringify(populations ?? []),
    JSON.stringify(farms ?? []),
    queryVariable,
    variableUnit,
    variableConvertUnit,
    selectedMonth
  ]);

  const chartComponent = useRef<HighchartsRef>(null);

  const defaultGrayColor = gray800;
  const defaultTextStyle = {
    fontSize: '12px',
    fontWeight: '600',
    color: defaultGrayColor,
    lineHeight: '12px'
  };

  const options: Options = {
    chart: {
      spacing: [6, 1, -1, -6],
      style: {
        fontFamily
      },
      animation: false,
      height: 360
    },
    plotOptions: {
      column: {
        maxPointWidth: 34,
        minPointLength: 5,
        borderRadius: 6,
        pointPadding: 0,
        borderWidth: 1,
        groupPadding: 0,
        shadow: false,
        dataLabels: {
          padding: 2,
          enabled: true,
          style: defaultTextStyle,
          formatter: function (this) {
            return formatNumber(this.y, {
              lang,
              fractionDigits: variableFractionDigits,
              compact: !!variableTitle,
              isPercentage: variableIsPercentage,
              isCurrency: variableIsCurrency
            }) as string;
          }
        }
      }
    },
    tooltip: {
      padding: 0,
      shadow: false,
      useHTML: true,
      borderRadius: 8,
      backgroundColor: gray200,
      formatter: function (this): string {
        const { custom, y, name } = this as unknown as {
          custom: { count: number };
          y: number;
          name: string;
        };
        const { count } = custom ?? {};

        return renderToString(
          <div
            style={{
              padding: '10px 16px',
              fontSize: '12px',
              fontWeight: 600,
              backgroundColor: gray200,
              borderRadius: '8px',
              minWidth: '138px',
              lineHeight: '16px',
              color: defaultGrayColor
            }}
          >
            <p style={{ marginBottom: '10px', fontSize: '14px', fontWeight: 'bold' }}>{name}</p>
            <p>
              {variableTitle}:{' '}
              {formatNumber(y, {
                lang,
                fractionDigits: variableFractionDigits,
                isCurrency: variableIsCurrency,
                isPercentage: variableIsPercentage
              })}
            </p>
            <p>
              {trans('t_#_of_cycles')}: {count}
            </p>
          </div>
        );
      }
    },
    title: {
      text: ''
    },
    xAxis: {
      type: 'category',
      labels: {
        style: defaultTextStyle,
        distance: 10
      }
    },
    yAxis: {
      tickWidth: 0,
      lineWidth: 0,
      tickAmount: 4,
      gridLineWidth: 0.5,
      gridLineColor: gray400,
      gridLineDashStyle: 'LongDash',
      labels: {
        enabled: false
      },
      title: { text: undefined }
    },
    series: [
      {
        type: 'column',
        zoneAxis: 'x',
        color: brandGreen800,
        data: series
      }
    ]
  };
  const optionStringify = JSON.stringify(options ?? []);

  useEffect(() => {
    if (!chartComponent?.current?.chart) return;
    chartComponent.current.chart.redraw();
  }, [optionStringify, chartComponent?.current]);

  return (
    <Flex p='md' bgColor='white' rounded='2xl' flexDirection='column' gap='md' flex={1}>
      <Flex align='center' justify='space-between'>
        <Heading size='heavy300'>{trans('t_breakdown')}</Heading>
        <AllYearMenu selectedMonth={selectedMonth} setSelectedMonth={setSelectedMonth} />
      </Flex>
      {!series.length && <Text>{trans('t_no_cycles_completed')}</Text>}
      <HighchartsNextComp
        ref={chartComponent}
        options={options}
        key={optionStringify}
        containerProps={{ style: { width: '100%', height: '360px' } }}
      />
    </Flex>
  );
}
