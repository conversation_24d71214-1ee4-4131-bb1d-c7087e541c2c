import { PopulationType } from '@redux/farm/set-current-population';
import { getTrans } from '@i18n/get-trans';
import { useRouter } from 'next/router';
import {
  getSeriesLastXMonths,
  GroupPerformanceSeriesPeriod,
  PerformanceVariable,
  useGetPerformanceVariableOptions
} from '@screens/group-summary/helpers/group-performance';
import { useAppSelector } from '@redux/hooks';
import { Flex, Heading, Text, useToken } from '@chakra-ui/react';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { HighchartsNextComp, HighchartsRef } from '@components/base/highcharts-next-comp';
import type { Options } from 'highcharts';
import { renderToString } from 'react-dom/server';
import { formatNumber } from '@utils/number';
import { DateTime } from 'luxon';
import { BaseMenu } from '@components/base/base-menu';
import { useAnalytics } from '@hooks/use-analytics';
import { actionsName } from '@utils/segment';

interface GroupPerformanceChartProps {
  populations: PopulationType[];
}

export function GroupPerformanceChart(props: GroupPerformanceChartProps) {
  const { populations } = props;
  const { trans } = getTrans();
  const { query } = useRouter();
  const { variable: queryVariable = 'profitPerHaPerDay' } = (query ?? {}) as { variable?: PerformanceVariable };
  const lang = useAppSelector((state) => state.app.lang);
  const [selectedPeriod, setSelectedPeriod] = useState<GroupPerformanceSeriesPeriod>('12');
  const { trackAction } = useAnalytics();

  const filterOptions = [
    { label: trans('t_all_year'), value: '12' },
    { label: trans('t_past_x_months', { months: 6 }), value: '6' },
    { label: trans('t_past_x_months', { months: 3 }), value: '3' }
  ];
  const selectedOption = filterOptions.find((option) => option.value === selectedPeriod);

  const labels = useGetPerformanceVariableOptions();
  const [gray200, gray400, gray800, graphBrandBlue] = useToken('colors', [
    'gray.200',
    'gray.400',
    'gray.800',
    'graphBrandBlue'
  ]);
  const [fontFamily] = useToken('fonts', ['body']);
  const {
    title: variableTitle = queryVariable,
    fractionDigits: variableFractionDigits,
    isCurrency: variableIsCurrency,
    isPercentage: variableIsPercentage,
    convertUnit: variableConvertUnit,
    unit: variableUnit
  } = labels[queryVariable] ?? {};

  const series = useMemo(() => {
    return getSeriesLastXMonths({
      populations,
      variable: queryVariable,
      convertUnit: variableConvertUnit,
      unit: variableUnit,
      period: selectedPeriod
    });
  }, [JSON.stringify(populations), queryVariable, variableConvertUnit, variableUnit, selectedPeriod]);

  const chartComponent = useRef<HighchartsRef>(null);

  const defaultGrayColor = gray800;
  const defaultTextStyle = {
    fontSize: '12px',
    fontWeight: '600',
    color: defaultGrayColor,
    lineHeight: '12px'
  };

  const options: Options = {
    chart: {
      spacing: [6, 1, -1, -6],
      style: {
        fontFamily
      },
      animation: false,
      height: 340
    },
    plotOptions: {
      series: { states: { inactive: { enabled: false } } },
      line: {
        lineWidth: 1.5,
        animation: false,
        showInLegend: false,
        marker: { enabled: true, symbol: 'circle', radius: 5 }
      }
    },
    tooltip: {
      padding: 0,
      shadow: false,
      useHTML: true,
      borderRadius: 8,
      backgroundColor: gray200,
      formatter: function (this): string {
        const { custom } = this as unknown as {
          custom: { count: number };
        };
        const { count } = custom ?? {};

        return renderToString(
          <div
            style={{
              padding: '10px 16px',
              fontSize: '12px',
              fontWeight: 600,
              backgroundColor: gray200,
              borderRadius: '8px',
              minWidth: '138px',
              lineHeight: '16px',
              color: defaultGrayColor
            }}
          >
            {this.options.x && (
              <p style={{ marginBottom: '10px', fontSize: '14px', fontWeight: 'bold' }}>
                {DateTime.fromISO(this.options.x as string).toFormat('MMM yyyy')}
              </p>
            )}
            <p>
              {variableTitle}:{' '}
              {formatNumber(this.y, {
                lang,
                fractionDigits: variableFractionDigits,
                isCurrency: variableIsCurrency,
                isPercentage: variableIsPercentage
              })}
            </p>
            <p>
              {trans('t_#_of_cycles')}: {count}
            </p>
          </div>
        );
      }
    },
    title: { text: undefined },
    xAxis: {
      tickPixelInterval: 10,
      type: 'datetime',
      labels: {
        style: defaultTextStyle,
        distance: 10
      }
    },
    yAxis: {
      tickWidth: 0,
      lineWidth: 0,
      tickAmount: 4,
      gridLineWidth: 0.5,
      gridLineColor: gray400,
      gridLineDashStyle: 'LongDash',
      title: { text: variableTitle }, //selected variable
      labels: {
        distance: 10,
        style: defaultTextStyle,
        formatter(this): string {
          const value = this.value as number;
          return formatNumber(value, {
            lang,
            fractionDigits: variableFractionDigits,
            compact: !!variableTitle,
            isPercentage: variableIsPercentage,
            isCurrency: variableIsCurrency
          }) as string;
        }
      }
    },
    series: [
      {
        type: 'line',
        zoneAxis: 'x',
        color: graphBrandBlue,
        data: series
      }
    ]
  };
  const optionStringify = JSON.stringify(options ?? []);

  useEffect(() => {
    if (!chartComponent?.current?.chart) return;
    chartComponent.current.chart.redraw();
  }, [optionStringify, chartComponent?.current]);

  return (
    <Flex p='md' bgColor='white' rounded='2xl' flexDirection='column' gap='sm-alt' flex={1}>
      <Flex align='center' justify='space-between'>
        <Heading size='heavy300'>{trans('t_group_performance')}</Heading>
        <BaseMenu
          selectedOption={selectedOption}
          options={filterOptions}
          onItemSelect={(option) => {
            setSelectedPeriod(option.value as GroupPerformanceSeriesPeriod);
            trackAction(actionsName.groupPerformancePeriodSelected, {
              period: `${option.value} months`,
              variable: variableTitle
            }).then();
          }}
          data-cy='group-performance-all-year-menu'
        />
      </Flex>

      <Text size='label200' color='text.gray.disabled' mb='sm-alt'>
        {trans('t_average_x_for_completed_cycles_across_farms', { variable: variableTitle })}
      </Text>
      <HighchartsNextComp
        ref={chartComponent}
        options={options}
        key={optionStringify}
        containerProps={{ style: { width: '100%', height: '340px' } }}
      />
    </Flex>
  );
}
