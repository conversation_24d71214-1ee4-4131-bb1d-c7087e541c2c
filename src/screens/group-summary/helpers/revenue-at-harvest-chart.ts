import { DateTime } from 'luxon';
import { ChartTimeFilterProps } from '@screens/group-summary/components/chart-time-filter';

export function getTimeFilterKey(params: {
  date: string;
  farmTimezone: string;
  chartTimeFilter: ChartTimeFilterProps['chartTimeFilter'];
}) {
  const { date, farmTimezone, chartTimeFilter } = params;

  let key;
  if (chartTimeFilter === 'monthly') {
    const monthStart = DateTime.fromISO(date, { zone: farmTimezone }).setLocale('en-US').startOf('month');
    key = `${monthStart.year}-${monthStart.monthShort}`;
  }

  if (chartTimeFilter === 'weekly') {
    const weekStart = DateTime.fromISO(date, { zone: farmTimezone }).setLocale('en-US').startOf('week');
    const weekEnd = weekStart.plus({ days: 6 });
    key = `${weekStart.year}-${weekStart.monthShort}-${weekStart.day} ${weekEnd.year}-${weekEnd.monthShort}-${weekEnd.day}`;
  }

  return key;
}

export type FilterObject = {
  y: number;
  x: number;
  custom: {
    farmId?: string;
    farmName?: string;
    totalRevenue?: number;
    pondId?: string;
    pondName?: string;
    biomassLbs?: number;
  };
};

export function sortChartDataByTimeFilterKeys(params: {
  chartDataByTimeFilter: Record<string, FilterObject[]>;
  chartTimeFilter: ChartTimeFilterProps['chartTimeFilter'];
}) {
  const { chartTimeFilter, chartDataByTimeFilter } = params;

  const sortedKeys = Object.keys(chartDataByTimeFilter).sort((a: string, b: string) => {
    let aMillis;
    let bMillis;

    if (chartTimeFilter === 'monthly') {
      aMillis = DateTime.fromFormat(a, 'yyyy-MMM').setLocale('en-US').toMillis();
      bMillis = DateTime.fromFormat(b, 'yyyy-MMM').setLocale('en-US').toMillis();
    }

    if (chartTimeFilter === 'weekly') {
      const [aWeekStart] = a.split(' ');
      const [bWeekStart] = b.split(' ');

      aMillis = DateTime.fromFormat(`${aWeekStart}`, 'yyyy-MMM-d').setLocale('en-US').toMillis();
      bMillis = DateTime.fromFormat(`${bWeekStart}`, 'yyyy-MMM-d').setLocale('en-US').toMillis();
    }

    return aMillis - bMillis;
  });
  return sortedKeys.reduce(
    (obj, key) => {
      obj[key] = chartDataByTimeFilter[key];
      return obj;
    },
    {} as Record<string, FilterObject[]>
  );
}

export function timeFilterKeyToTitle(params: {
  key: string;
  lang: string;
  chartTimeFilter: ChartTimeFilterProps['chartTimeFilter'];
}) {
  const { key, lang, chartTimeFilter } = params;

  let title;
  if (chartTimeFilter === 'monthly') {
    title = DateTime.fromFormat(key, 'yyyy-MMM').toFormat('MMM yyyy', { locale: lang });
  }

  if (chartTimeFilter === 'weekly') {
    const [weekStart, weekEnd] = key.split(' ');
    const weekEndLuxon = DateTime.fromFormat(weekEnd, 'yyyy-MMM-d');
    const weekStartLuxon = DateTime.fromFormat(weekStart, 'yyyy-MMM-d');
    const isMonthSame = weekStartLuxon.month === weekEndLuxon.month;

    title = `${weekStartLuxon.toFormat('MMM d', { locale: lang })}-${weekEndLuxon.toFormat(isMonthSame ? 'd' : 'MMM d', { locale: lang })}`;
  }

  return title;
}
