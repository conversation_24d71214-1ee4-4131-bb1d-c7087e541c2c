import { getTrans } from '@i18n/get-trans';
import { formatNumber } from '@utils/number';
import isUndefined from 'lodash/isUndefined';

export function getCycleDaysFormatted({ number, lang }: { number: number; lang: string }) {
  const { trans } = getTrans();

  const cycleDaysLessThanFormatted = formatNumber(number, { lang, fractionDigits: 0 });

  if (number < 2) {
    return `${cycleDaysLessThanFormatted} ${trans('t_day')}`;
  }

  return `${cycleDaysLessThanFormatted} ${trans('t_days')}`;
}

export function getGroupSummaryLabelMap(params: { unitsConfig: { biomass: 'lbs' | 'kg' } }) {
  const { unitsConfig } = params;

  const { trans } = getTrans();

  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);
  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');
  return {
    averageWeight: trans('t_abw_g'),
    growth1WeekAvg: trans('t_growth_monitored_x_week_g_wk', { weeks: 1 }),
    growth2WeekAvg: trans('t_growth_monitored_x_week_g_wk', { weeks: 2 }),
    growth3WeekAvg: trans('t_growth_monitored_x_week_g_wk', { weeks: 3 }),
    avgFcrCumulative: trans('t_avg_fcr_cumulative'),
    avgKgPerHaPerDay: trans('t_avg_kg_ha_day'),
    avgTotalFeedGivenKg: trans('t_avg_total_feed_given_kg'),
    avgTotalFeedGivenLb: trans('t_avg_total_feed_given_lb'),
    avgSurvival: trans('t_avg_survival'),
    totalBiomassLbs: isBiomassUnitLbs ? trans('t_biomass_lb_total') : trans('t_biomass_kg_total'),
    biomassLbsPerHa: isBiomassUnitLbs ? trans('t_biomass_lb_ha') : trans('t_biomass_kg_ha'),
    biomassLbsPerHaPerDay: isBiomassUnitLbs ? trans('t_biomass_lb_ha_day') : trans('t_biomass_kg_ha_day'),
    profitPerHaPerDay: trans('t_profit_include_dry_days_ha_day'),
    totalProfit: trans('t_profit'),
    totalRevenue: trans('t_revenue'),
    revenuePerPound: isBiomassUnitLbs ? trans('t_revenue_per_pound') : trans('t_revenue_per_kg'),
    totalCost: trans('t_cost_$'),
    costPerPound: trans('t_cost_per_unit_processed', { unit: unitLabel }),
    costPoundHarvest: trans('t_cost_per_unit_harvested', { unit: unitLabel })
  };
}
