import { PopulationType } from '@redux/farm/set-current-population';
import { DateTime } from 'luxon';
import mean from 'lodash/mean';
import keyBy from 'lodash/keyBy';
import { CycleComparisonVariableType } from '@screens/cycle-comparison/utils/cycle-comparison-variables';
import { convertUnitByDivision, convertUnitByMultiplication, formatNumber } from '@utils/number';
import { useAppSelector } from '@redux/hooks';
import isUndefined from 'lodash/isUndefined';
import { getTrans } from '@i18n/get-trans';
import { FarmsDataType } from '@redux/farm/set-farms-data';
import { ThemeProps } from '@chakra-ui/react';

type ConvertUnitFunction = typeof convertUnitByDivision | typeof convertUnitByMultiplication;
type Unit = 'lbs' | 'kg';

export type PerformanceVariable = Extract<
  CycleComparisonVariableType,
  | 'profitPerHaPerDay'
  | 'costPoundHarvest'
  | 'adjustedFcr'
  | 'survival'
  | 'totalBiomassLbsByHa'
  | 'totalRevenuePoundHarvest'
  | 'growthLinear'
  | 'rofi'
>;

type PerformanceVariableBuckets = Record<string, Record<PerformanceVariable, number[]>>;

type PerformanceVariableMeans = Record<string, Record<PerformanceVariable, number>>;

interface GetLastTwoMonthsMeanByVariableArgs {
  populations: PopulationType[];
  variables: PerformanceVariable[];
}
export function getLastTwoMonthsMeanByVariable(args: GetLastTwoMonthsMeanByVariableArgs) {
  const { populations, variables } = args;
  const now = DateTime.local().startOf('month');
  const prev = now.minus({ months: 1 });
  const nowFormated = now.toFormat('yyyy-MM-dd');
  const prevFormated = prev.toFormat('yyyy-MM-dd');
  const validMonths = [nowFormated, prevFormated];

  const buckets: PerformanceVariableBuckets = {};

  for (const month of validMonths) {
    buckets[month] = variables.reduce(
      (acc, key) => {
        acc[key] = [];
        return acc;
      },
      {} as Record<PerformanceVariable, number[]>
    );
  }

  for (const pop of populations) {
    const date = pop.harvest?.date;
    if (!date) continue;

    const harvestDate = date.includes('GMT') ? new Date(date).toISOString() : date;
    const month = DateTime.fromISO(harvestDate).startOf('month').toFormat('yyyy-MM-dd');
    if (!validMonths.includes(month)) continue;

    for (const key of variables) {
      const v = pop.harvest[key];
      if (typeof v === 'number') {
        buckets[month][key].push(v);
      }
    }
  }

  const means: PerformanceVariableMeans = {};
  for (const month of validMonths) {
    const monthBucket = buckets[month];
    const monthMeans = {} as Record<PerformanceVariable, number>;

    for (const key of variables) {
      const vals = monthBucket[key];
      monthMeans[key] = vals.length > 0 ? mean(vals) : undefined;
    }

    means[month] = monthMeans;
  }

  return means;
}

type ColorScheme = {
  positive: { color: ThemeProps['color']; bgColor: ThemeProps['bgColor'] };
  negative: { color: ThemeProps['color']; bgColor: ThemeProps['bgColor'] };
};

interface GetMeanByVariableDiffArgs {
  means: PerformanceVariableMeans;
  variable: PerformanceVariable;
  options: VariableOptions;
  lang: string;
}

export function getMeanByVariableDiff(args: GetMeanByVariableDiffArgs) {
  const { means, variable, options, lang } = args;
  const now = DateTime.local().startOf('month');
  const prev = now.minus({ months: 1 });
  const nowFormatted = now.toFormat('yyyy-MM-dd');
  const prevFormatted = prev.toFormat('yyyy-MM-dd');

  const rawCurrentMean = means[nowFormatted][variable];
  const rawPreviousMean = means[prevFormatted][variable];
  const { convertUnit, unit, moreIsWorse, fractionDigits, isCurrency, isPercentage } = options ?? {};

  const currentMean = convertUnit ? convertUnit(rawCurrentMean, unit) : rawCurrentMean;
  const previousMean = convertUnit ? convertUnit(rawPreviousMean, unit) : rawPreviousMean;

  const formattedCurrentMean = formatNumber(currentMean, {
    lang,
    fractionDigits,
    isPercentage,
    isCurrency
  });

  const formattedPrevMean = formatNumber(previousMean, {
    lang,
    fractionDigits,
    isPercentage,
    isCurrency
  });

  const diffMean = currentMean - previousMean;

  if (!diffMean) {
    return {
      currentMean: formattedCurrentMean,
      previousMean: formattedPrevMean,
      currentDateLuxon: now,
      prevDateLuxon: prev
    };
  }

  const colorScheme: ColorScheme = {
    positive: { color: 'text.brandGreen', bgColor: 'bg.brandGreen.weakShade2' },
    negative: { color: 'text.semanticRed', bgColor: 'bg.shrimpyPinky.weak' }
  };

  const isIncrease = currentMean > previousMean;

  const isImprovement = moreIsWorse ? !isIncrease : isIncrease;

  const { color, bgColor } = isImprovement ? colorScheme.positive : colorScheme.negative;

  return {
    currentMean: formattedCurrentMean,
    previousMean: formattedPrevMean,
    diffMean: formatNumber(diffMean, {
      lang,
      fractionDigits,
      isPercentage,
      isCurrency
    }),
    currentDateLuxon: now,
    prevDateLuxon: prev,
    color,
    bgColor
  };
}

export type GroupPerformanceSeriesPeriod = '12' | '6' | '3';
type GetSeriesLastXMonthsArgs = {
  populations: PopulationType[];
  variable: PerformanceVariable;
  convertUnit: ConvertUnitFunction;
  unit: Unit;
  period: GroupPerformanceSeriesPeriod;
};

export function getSeriesLastXMonths(args: GetSeriesLastXMonthsArgs) {
  const { populations, variable, convertUnit, unit, period } = args;

  const buckets = new Map<string, number[]>();
  for (const population of populations) {
    const date = population.harvest.date;

    if (!date) continue;

    const harvestDate = date.includes('GMT') ? new Date(date).toISOString() : date;
    const month = DateTime.fromISO(harvestDate).startOf('month').toFormat('yyyy-MM-dd');

    const val = population.harvest[variable];
    if (typeof val !== 'number') continue;
    const value = convertUnit ? convertUnit(val, unit) : val;

    const arr = buckets.get(month);
    if (arr) arr.push(value);
    else buckets.set(month, [value]);
  }

  const periodNumber = Number(period);
  const endDateLuxon = DateTime.local().startOf('month');
  const start = endDateLuxon.minus({ months: periodNumber - 1 });

  return Array.from({ length: periodNumber }, (_, i) => {
    const month = start.plus({ months: i }).toFormat('yyyy-MM-dd');
    const vals = buckets.get(month) ?? [];
    const y = vals.length ? mean(vals) : null;

    return { x: month, y, custom: { count: vals.length } };
  });
}

type GetMedianByFarmIdArgs = {
  populations: PopulationType[];
  variable: PerformanceVariable;
  convertUnit: ConvertUnitFunction;
  unit: Unit;
  farms: FarmsDataType[];
};

export function getMedianByFarmId(args: GetMedianByFarmIdArgs) {
  const { populations, variable, convertUnit, unit, farms } = args;
  const buckets = new Map<string, number[]>();

  for (const population of populations) {
    const farmId = population.farmId;
    const val = population.harvest[variable];
    if (typeof val !== 'number' || !farmId) continue;
    const value = convertUnit ? convertUnit(val, unit) : val;
    const arr = buckets.get(farmId);
    if (arr) arr.push(value);
    else buckets.set(farmId, [value]);
  }

  const farmsById = keyBy(farms, '_id');
  const series = Array.from(buckets, ([farmId, vals]) => {
    return {
      name: farmsById[farmId]?.name ?? farmId,
      y: vals.length ? mean(vals) : null,
      custom: { count: vals.length }
    };
  });

  return series.sort((a, b) => (b.y ?? 0) - (a.y ?? 0));
}

type VariableOptions = {
  title: string;
  isCurrency?: boolean;
  isPercentage?: boolean;
  moreIsWorse?: boolean;
  fractionDigits: number;
  convertUnit?: typeof convertUnitByDivision | typeof convertUnitByMultiplication;
  unit?: 'lbs' | 'kg';
};

type PerformanceVariableOptions = Record<PerformanceVariable, VariableOptions>;

export function useGetPerformanceVariableOptions(): PerformanceVariableOptions {
  const { trans } = getTrans();
  const user = useAppSelector((state) => state.auth.user);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};

  const unit = unitsConfig?.biomass;
  const isBiomassUnitLbs = unit === 'lbs' || isUndefined(unit);
  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');

  return {
    profitPerHaPerDay: {
      title: trans('t_profit_include_dry_days_ha_day'),
      isCurrency: true,
      fractionDigits: 2
    },
    costPoundHarvest: {
      title: trans('t_cost_per_unit_harvested', { unit: unitLabel }),
      isCurrency: true,
      fractionDigits: 2,
      convertUnit: convertUnitByMultiplication,
      unit,
      moreIsWorse: true
    },
    adjustedFcr: {
      title: trans('t_fcr_adjusted'),
      fractionDigits: 2,
      moreIsWorse: true
    },
    survival: {
      title: trans('t_survival_include_harvests'),
      isPercentage: true,
      fractionDigits: 1
    },
    totalBiomassLbsByHa: {
      title: trans('t_biomass_include_harvests_unit_ha', { unit: unitLabel }),
      fractionDigits: 0,
      convertUnit: convertUnitByDivision,
      unit
    },
    totalRevenuePoundHarvest: {
      title: trans('t_revenue_per_unit_harvested', { unit: unitLabel }),
      isCurrency: true,
      fractionDigits: 2,
      convertUnit: convertUnitByMultiplication,
      unit
    },
    growthLinear: {
      title: trans('t_growth_g_wk'),
      fractionDigits: 2
    },
    rofi: {
      title: trans('t_rofi'),
      isPercentage: true,
      fractionDigits: 1
    }
  };
}

export function getLast12MonthsBoundaryOptions() {
  const end = DateTime.local().endOf('month');

  return Array.from({ length: 12 }).map((_, i) => {
    const month = end.minus({ months: i });
    return {
      label: month.toFormat('MMMM yyyy'),
      value: month.startOf('month').toFormat('yyyy-MM-dd')
    };
  });
}
