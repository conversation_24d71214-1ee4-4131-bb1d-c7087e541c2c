import { useEffect, useState } from 'react';
import { useAppSelector } from '@redux/hooks';
import { DateTime } from 'luxon';
import { getPondState } from '@screens/pond/helpers/get-pond-state';
import { sortArray, sortCompareString } from '@utils/sort-array';
import get from 'lodash/get';
import { getDaysDiffBetweenDates } from '@screens/farm/helpers/farm-dates';
import { generateBiomassValues, getLastDateRecordValue } from '@screens/population/helpers/population-values';
import { getTrans } from '@i18n/get-trans';
import { exportAsXLSX } from '@utils/files';
import snakeCase from 'lodash/snakeCase';

export type ProfitViewDataItem = {
  pondId: string;
  pondEid: number;
  pondSize: number;
  pondName: string;
  timezone: string;
  lastMonitoringDate: string;
  lastMonitoringABW: number;
  daysOfCulture: number;
  lastMonitoringBiomass: number;
  lastMonitoringDateTimezone: string;
  profitProjectionOnHarvestDate: {
    totalProfit: number;
    totalRevenue: number;
    totalAccumulatedCosts: number;
    date: string;
  };
};

export type GetProfitViewData = {
  isLoading: boolean;
  onDownload?: () => void;
  isCurrentFarmHasPonds?: boolean;
  profitViewData?: ProfitViewDataItem[];
};

export function useProfitViewData() {
  const { trans } = getTrans();

  const currentFarm = useAppSelector((state) => state.farm.currentFarm);
  const currentFarmPonds = useAppSelector((state) => state.farm.currentFarmPonds);
  const isLoadingCurrentFarmPonds = useAppSelector((state) => state.farm.isLoadingCurrentFarmPonds);

  const [data, setData] = useState<GetProfitViewData>({ isLoading: true });

  useEffect(() => {
    setData({ isLoading: true });
    if (isLoadingCurrentFarmPonds) return;

    if (!currentFarmPonds?.length) {
      setData({ isLoading: false, isCurrentFarmHasPonds: false });
      return;
    }

    const asyncFn = async (): Promise<GetProfitViewData> => {
      const { name: currentFarmName, timezone } = currentFarm;

      const filteredCurrentFarmPonds = currentFarmPonds?.filter((pond) => {
        const { isEmpty } = getPondState({ population: pond?.currentPopulation });
        return !isEmpty;
      });

      const sortedCurrentFarmPonds = sortArray({
        sortDir: 'asc',
        list: filteredCurrentFarmPonds,
        compareFunction: (a, b) => {
          const firstItemValue = get(a, 'name');
          const secondItemValue = get(b, 'name');
          return sortCompareString(firstItemValue, secondItemValue);
        }
      });

      const profitViewData = sortedCurrentFarmPonds?.map((currentFarmPond): ProfitViewDataItem => {
        const { _id: pondId, eid: pondEid, name: pondName, size: pondSize, currentPopulation } = currentFarmPond;
        const {
          stockedAt,
          survivalRate,
          seedingQuantity,
          harvestPlan,
          productionPrediction,
          stockedAtTimezone,
          lastMonitoringDate,
          lastMonitoringMlResult,
          lastMonitoringDateTimezone
        } = currentPopulation ?? {};
        const { averageWeight: lastMonitoringABW } = lastMonitoringMlResult ?? {};

        const stockedAtLuxon = DateTime.fromISO(stockedAt, { zone: stockedAtTimezone });
        const daysOfCulture = getDaysDiffBetweenDates({
          dateToCompare: stockedAtLuxon.toFormat('yyyy-MM-dd'),
          baseDate: DateTime.local({ zone: stockedAtTimezone }).toFormat('yyyy-MM-dd')
        });

        const { projection, harvestType: oldHarvestType, processorPriceList } = harvestPlan ?? {};
        const harvestType = processorPriceList?.defaultHarvestType ?? oldHarvestType;

        const { profitProjectionData, plannedHarvestIdx } = projection ?? {};
        const plannedHarvestDate = harvestPlan?.harvestDate;
        const plannedProfitProjection = profitProjectionData?.[plannedHarvestIdx]?.[harvestType as 'headon'];
        const productionPredictionHarvestDate = productionPrediction?.find((p) => p.date === harvestPlan?.harvestDate);

        const { value: survival } = getLastDateRecordValue(survivalRate);
        const { biomassLbs: lastMonitoringBiomass } = generateBiomassValues({
          pondSize,
          seedingQuantity,
          survivalRate: survival,
          ignoreABWValidation: true,
          averageWeight: lastMonitoringABW
        });

        return {
          pondId,
          pondEid,
          pondName,
          pondSize,
          timezone,
          daysOfCulture,
          lastMonitoringABW,
          lastMonitoringDate,
          lastMonitoringBiomass,
          lastMonitoringDateTimezone,
          profitProjectionOnHarvestDate: {
            date: plannedHarvestDate,
            totalProfit: plannedProfitProjection?.totalProfit,
            totalRevenue: plannedProfitProjection?.totalRevenue,
            totalAccumulatedCosts: productionPredictionHarvestDate?.totalCosts
          }
        };
      });

      const onDownload = () => {
        const columnHeaders = [
          trans('t_pond'),
          trans('t_size'),
          trans('t_days'),
          trans('t_abw_g'),
          trans('t_last_monitoring'),
          trans('t_current_biomass'),
          trans('t_planned_final_harvest'),
          trans('t_projected_profit'),
          trans('t_projected_revenue'),
          trans('t_cost_to_date_estimated')
        ];

        const list = profitViewData?.map((item) => {
          const lastMonitoringDateLuxon = DateTime.fromISO(item?.lastMonitoringDate, {
            zone: item?.lastMonitoringDateTimezone
          });

          return {
            [trans('t_pond')]: item?.pondName,
            [trans('t_size')]: item?.pondSize,
            [trans('t_days')]: item?.daysOfCulture,
            [trans('t_abw_g')]: item?.lastMonitoringABW,
            [trans('t_last_monitoring')]: item?.lastMonitoringDate
              ? lastMonitoringDateLuxon.toFormat('LLL dd, yyyy')
              : '',
            [trans('t_current_biomass')]: item?.lastMonitoringBiomass,
            [trans('t_planned_final_harvest')]: item?.profitProjectionOnHarvestDate?.date,
            [trans('t_projected_profit')]: item?.profitProjectionOnHarvestDate?.totalProfit,
            [trans('t_projected_revenue')]: item?.profitProjectionOnHarvestDate?.totalRevenue,
            [trans('t_cost_to_date_estimated')]: item?.profitProjectionOnHarvestDate?.totalAccumulatedCosts
          };
        });

        const todayLuxon = DateTime.local({ zone: timezone });
        exportAsXLSX({
          list,
          columnHeaders,
          columnWidth: 15,
          fileName: snakeCase(
            `${currentFarmName?.toLowerCase()}_${todayLuxon.toFormat('yyyy-MM-dd')}.xlsx`.slice(0, 30)
          )
        });
      };

      return { isLoading: false, isCurrentFarmHasPonds: true, profitViewData, onDownload };
    };

    asyncFn()
      .then((e) => {
        setData(e);
      })
      .catch((e) => {
        console.error(e);
        setData({ isLoading: false, isCurrentFarmHasPonds: currentFarmPonds?.length > 0 });
      });
  }, [isLoadingCurrentFarmPonds, currentFarm?._id]);

  return data;
}
