import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { usePermission } from '@hooks/use-permission';
import isUndefined from 'lodash/isUndefined';

export type GroupViewVariable =
  | 'profitPerHaPerDay'
  | 'profitPerPound'
  | 'costPoundHarvest'
  | 'costPerPound'
  | 'revenuePerPound'
  | 'averageWeight'
  | 'survivalRate'
  | 'survivalFeed'
  | 'totalBiomass'
  | 'biomassPerHa'
  | 'weeklyGrowth'
  | 'growthLinear'
  | 'fcr'
  | 'adjustedFcr'
  | 'daysUntilHarvest'
  | 'daysOffFromOptimal';

type GroupViewCustomizeVariableOption = { value: GroupViewVariable; label: string };
type GroupViewSections = 'financials' | 'production' | 'other';

type GetGroupViewVariablesReturn = Record<
  GroupViewSections,
  { title: string; subtitle?: string; variables: GroupViewCustomizeVariableOption[] }
>;

export function useGetGroupViewVariables(): GetGroupViewVariablesReturn {
  const { trans } = getTrans();
  const user = useAppSelector((state) => state.auth.user);
  const farmData = useAppSelector((state) => state.farm.farmsData);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const farmIds = farmData?.map((farm) => farm._id);

  const isAdmin = usePermission({
    role: 'admin',
    entity: 'farm',
    entityIds: farmIds
  });

  const isSupervisor = usePermission({
    role: 'supervisor',
    entity: 'farm',
    entityIds: farmIds
  });

  const isUser = !isAdmin && !isSupervisor;
  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);
  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');
  const adminFinancials: GroupViewCustomizeVariableOption[] = isAdmin
    ? [
        { label: trans('t_profit_include_dry_days_ha_day'), value: 'profitPerHaPerDay' },
        {
          label: isBiomassUnitLbs ? trans('t_profit_lb') : trans('t_profit_kg'),
          value: 'profitPerPound'
        },
        {
          label: isBiomassUnitLbs ? trans('t_revenue_per_pound') : trans('t_revenue_per_kg'),
          value: 'revenuePerPound'
        }
      ]
    : [];
  const financials = isUser
    ? ({} as GetGroupViewVariablesReturn)
    : ({
        financials: {
          title: trans('t_financials'),
          variables: [
            ...adminFinancials,
            {
              label: trans('t_cost_per_unit_harvested', { unit: unitLabel }),
              value: 'costPoundHarvest'
            },
            {
              label: trans('t_cost_per_unit_processed', { unit: unitLabel }),
              value: 'costPerPound'
            }
          ]
        }
      } as GetGroupViewVariablesReturn);
  return {
    ...financials,
    production: {
      title: trans('t_production'),
      subtitle: trans('t_data_shown_from_last_monitoring'),
      variables: [
        { label: trans('t_abw_g'), value: 'averageWeight' },
        { label: trans('t_survival'), value: 'survivalRate' },
        { label: trans('t_kampi_survival'), value: 'survivalFeed' },
        {
          label: isBiomassUnitLbs ? trans('t_biomass_lb') : trans('t_biomass_kg'),
          value: 'totalBiomass'
        },
        {
          label: isBiomassUnitLbs ? trans('t_biomass_lb_ha') : trans('t_biomass_kg_ha'),
          value: 'biomassPerHa'
        },
        { label: trans('t_growth_monitored_x_week_g_wk', { weeks: 1 }), value: 'weeklyGrowth' },
        { label: trans('t_growth_g_wk'), value: 'growthLinear' },
        { label: trans('t_fcr'), value: 'fcr' },
        { label: trans('t_fcr_adjusted'), value: 'adjustedFcr' }
      ]
    },
    other: {
      title: trans('t_other'),
      subtitle: trans('t_data_shown_based_on_current_date'),
      variables: [
        { label: trans('t_days_until_harvest'), value: 'daysUntilHarvest' },
        { label: trans('t_optimal_harvest'), value: 'daysOffFromOptimal' }
      ]
    }
  };
}
