import { useAppSelector } from '@redux/hooks';
import { IndicatorOption, IndicatorValues } from '@components/pond-card/helper';
import { getTrans } from '@i18n/get-trans';
import { GroupViewVariable } from '@screens/group-summary/hooks/use-get-group-view-variables';
import isUndefined from 'lodash/isUndefined';

type CustomizeViewVariableProps = {
  isAdmin: boolean;
  isSupervisor: boolean;
};

export function useGetDashboardCustomizeViewVariables({ isAdmin, isSupervisor }: CustomizeViewVariableProps) {
  const user = useAppSelector((state) => state.auth.user);

  const { preferences } = user ?? {};
  const { viewFields } = preferences ?? {};

  const groupViewVariables: GroupViewVariable[] = viewFields?.groupViewVariables;

  if (isAdmin) {
    const adminDefaultCustomizeViewVariables: GroupViewVariable[] = [
      'profitPerHaPerDay',
      'averageWeight',
      'costPerPound'
    ];
    return groupViewVariables?.length ? groupViewVariables : adminDefaultCustomizeViewVariables;
  }

  const forbiddenVariables: GroupViewVariable[] = ['profitPerHaPerDay', 'profitPerPound', 'revenuePerPound'];

  if (isSupervisor) {
    const superVisorDefaultCustomizeViewVariables: GroupViewVariable[] = [
      'survivalRate',
      'averageWeight',
      'costPerPound'
    ];
    if (!groupViewVariables?.length) return superVisorDefaultCustomizeViewVariables;
    return groupViewVariables.filter((variable) => {
      return !forbiddenVariables.includes(variable);
    });
  }

  const forbiddenUserVariables: GroupViewVariable[] = [...forbiddenVariables, 'costPerPound', 'costPoundHarvest'];
  const userDefaultVariables: GroupViewVariable[] = ['averageWeight', 'survivalRate', 'fcr'];

  if (!groupViewVariables?.length) return userDefaultVariables;

  return groupViewVariables.filter((variable) => {
    return !forbiddenUserVariables.includes(variable);
  });
}

export function useGetDashboardStatusIndicator({ isAdmin, isSupervisor }: CustomizeViewVariableProps) {
  const { trans } = getTrans();

  const user = useAppSelector((state) => state.auth.user);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};

  const customizeViewVariables = useGetDashboardCustomizeViewVariables({ isAdmin, isSupervisor });

  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);
  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');
  const indicatingStatusOptions: IndicatorOption[] = [
    {
      label: trans('t_profit_include_dry_days_ha_day'),
      value: 'profitPerHaPerDay',
      isDisabled: !customizeViewVariables.includes('profitPerHaPerDay')
    },
    {
      label: trans('t_cost_per_unit_processed', { unit: unitLabel }),
      value: 'costPerPound',
      isDisabled: !customizeViewVariables.includes('costPerPound')
    },
    {
      label: trans('t_abw_g'),
      value: 'averageWeight',
      isDisabled: !customizeViewVariables.includes('averageWeight')
    },
    {
      label: trans('t_growth_monitored_x_week_g_wk', { weeks: 1 }),
      value: 'weeklyGrowth',
      isDisabled: !customizeViewVariables.includes('weeklyGrowth')
    },
    {
      label: trans('t_growth_g_wk'),
      value: 'growthLinear',
      isDisabled: !customizeViewVariables.includes('growthLinear')
    },
    {
      label: trans('t_survival'),
      value: 'survivalRate',
      isDisabled: !customizeViewVariables.includes('survivalRate')
    },
    { label: trans('t_fcr'), value: 'fcr', isDisabled: !customizeViewVariables.includes('fcr') }
  ];

  const forbiddenVariables: IndicatorValues[] = ['profitPerHaPerDay'];
  const userForbiddenVariables: IndicatorValues[] = ['costPerPound', ...forbiddenVariables];

  const firstNotDisabledStatusIndicator = indicatingStatusOptions.find((option) => {
    if (isAdmin) {
      return !option.isDisabled;
    }

    if (isSupervisor) {
      return !option.isDisabled && !forbiddenVariables.includes(option.value);
    }

    return !option.isDisabled && !userForbiddenVariables.includes(option.value);
  })?.value;

  const filteredIndicatingStatusOptions = indicatingStatusOptions.filter((option) => {
    if (isAdmin) {
      return true;
    }

    if (isSupervisor) {
      return !forbiddenVariables.includes(option.value);
    }

    return !userForbiddenVariables.includes(option.value);
  });

  return { firstNotDisabledStatusIndicator, indicatingStatusOptions: filteredIndicatingStatusOptions };
}
