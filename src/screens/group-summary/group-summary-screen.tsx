import { Box, Flex, FlexProps, Heading, Skeleton } from '@chakra-ui/react';
import { GroupSummary } from '@screens/group-summary/components/group-summary';
import { useListPondsApi } from '@screens/pond/hooks/use-list-ponds-api';
import { useEffect, useState } from 'react';
import { useFarmSummaryData } from '@screens/group-summary/hooks/use-farm-summary-data';
import { AllFarmsAtHarvest } from '@screens/group-summary/components/all-farms-at-harvest';
import { useSetFarmBookRefererInRedux } from '@components/farm-book/hooks/use-set-farm-book-referer-in-redux';
import { useAppSelector } from '@redux/hooks';
import { OverlayLoader } from '@components/loaders/overlay-loader';
import { getTrans } from '@i18n/get-trans';
import { SummaryDrawer } from '@components/summary-drawer/summary-drawer';
import { PondFilterPopoverFormType } from '@components/pond-filter-popover/use-generate-filter-tags';
import { getWebViewType } from '@utils/get-web-view-type';
import { getCacheItem } from '@utils/local-cache';
import { IndicatorValues, PondCardViewType } from '@components/pond-card/helper';
import {
  useGetDashboardCustomizeViewVariables,
  useGetDashboardStatusIndicator
} from '@screens/group-summary/hooks/use-get-dashboard-status-indicator';
import { usePermission } from '@hooks/use-permission';
import { MyFarms } from '@screens/group-summary/components/my-farms';

export function GroupSummaryScreen() {
  const { trans } = getTrans();
  const user = useAppSelector((state) => state.auth.user);

  const [{ isLoading: isLoadingPonds }, listPondsApi] = useListPondsApi();
  useSetFarmBookRefererInRedux('groupSummary');
  const { isWebViewTypeMobile } = getWebViewType();

  const farmData = useAppSelector((state) => state.farm.farmsData);
  const farmIds = farmData?.map((farm) => farm._id);

  const isAdmin = usePermission({
    role: 'admin',
    entity: 'farm',
    entityIds: farmIds
  });

  const isSupervisor = usePermission({
    role: 'supervisor',
    entity: 'farm',
    entityIds: farmIds
  });

  const customizeViewVariables = useGetDashboardCustomizeViewVariables({ isAdmin, isSupervisor });

  const { indicatingStatusOptions, firstNotDisabledStatusIndicator } = useGetDashboardStatusIndicator({
    isAdmin,
    isSupervisor
  });
  const [farmOverviewFilter, setFarmOverviewFilter] = useState<PondFilterPopoverFormType>();
  const [profitProjectionToView, setProfitProjectionToView] = useState<PondCardViewType>('current');
  const [indicatingStatus, setIndicatingStatus] = useState<IndicatorValues>(firstNotDisabledStatusIndicator);

  const { farmsMap, isLoading } = useFarmSummaryData({
    filters: farmOverviewFilter,
    selectedIndicatingStatus: indicatingStatus,
    profitProjectionToView
  });

  const selectedIndicatingStatusOption = indicatingStatusOptions.find((ele) => ele.value === indicatingStatus);

  useEffect(() => {
    getCacheItem<IndicatorValues>('dashboardIndicatingStatusSelector').then((valueFromCache) => {
      const valueFromOptions = indicatingStatusOptions.find((ele) => ele.value === valueFromCache);
      const profitPerHaPerDayOption = indicatingStatusOptions.find((ele) => ele.value === 'profitPerHaPerDay');
      const averageWeightOption = indicatingStatusOptions.find((ele) => ele.value === 'averageWeight');
      const costPerPoundOption = indicatingStatusOptions.find((ele) => ele.value === 'costPerPound');

      if (valueFromOptions && !valueFromOptions.isDisabled) {
        setIndicatingStatus(valueFromCache);
      } else if (profitPerHaPerDayOption && !profitPerHaPerDayOption.isDisabled && isAdmin) {
        setIndicatingStatus(profitPerHaPerDayOption.value);
      } else if (costPerPoundOption && !costPerPoundOption.isDisabled && isSupervisor) {
        setIndicatingStatus(costPerPoundOption.value);
      } else if (averageWeightOption && !averageWeightOption.isDisabled) {
        setIndicatingStatus(averageWeightOption.value);
      } else {
        setIndicatingStatus(firstNotDisabledStatusIndicator);
      }
    });
  }, [JSON.stringify(customizeViewVariables ?? []), isAdmin]);

  useEffect(() => {
    getCacheItem<PondCardViewType>('dashboardPondView').then((valueFromCache) => {
      if (valueFromCache) setProfitProjectionToView(valueFromCache);
    });
  }, []);

  useEffect(() => {
    if (!farmData?.length) return;
    fetchPonds();
  }, [farmData]);

  const fetchPonds = (successCallback?: () => void) => {
    listPondsApi({
      successCallback,
      loadRelated: true,
      isSummaryData: true,
      shouldListAllPonds: true,
      params: {
        filter: { farmId: farmIds, isArchived: false },
        page: { current: 1, size: 10000 }
      }
    });
  };

  if (isLoading) {
    return (
      <Container gap='md-alt' p='md'>
        <Flex align='center' justify='space-between' gap='sm-alt' flexWrap='wrap'>
          <Skeleton h='44px' w='185px' />
          <Flex align='center' gap='sm-alt' ms='auto'>
            <Skeleton h='44px' w='100px' borderRadius='full' />
            <Skeleton h='44px' w='185px' borderStartRadius='full' borderEndRadius={0} me='-md' />
          </Flex>
        </Flex>

        <Skeleton h='203px' w='100%' mb='md-alt' />
        <Flex align='center' justify='space-between' flexWrap='wrap' gap='md'>
          <Skeleton h='32px' w='87px' />
          <Flex align='center' gap='sm-alt' flexWrap='wrap' w={{ base: '100%', md: 'auto' }}>
            <Skeleton h='44px' w='220px' borderRadius='full' />
            <Skeleton h='44px' w='160px' borderRadius='full' />
            <Skeleton h='10px' w='24px' ms='auto' />
          </Flex>
        </Flex>
        <Skeleton h='600px' w='100%' />
      </Container>
    );
  }
  const { firstName, lastName } = user ?? {};

  return (
    <Container px={0} py='md' data-cy='group-summary-screen'>
      <OverlayLoader isLoading={isLoadingPonds} />
      <Flex px='md' align='center' justify='space-between' gap='sm-alt' flexWrap='wrap' mb='md-alt'>
        <Heading size='heavy300' letterSpacing='none'>
          {trans('t_hello')}
          {firstName || lastName ? `, ${firstName ?? ''} ${lastName ?? ''}` : ''}!
        </Heading>

        {!isWebViewTypeMobile && (
          <Box display={{ base: 'none', lg: 'block' }}>
            <SummaryDrawer title={trans('t_group_summary')}>
              <GroupSummary farmsMap={farmsMap} farmOverviewFilter={farmOverviewFilter} />
            </SummaryDrawer>
          </Box>
        )}
      </Flex>

      {!isWebViewTypeMobile && (
        <Box px='md' mt='sm-alt' mb='md-alt' ms='auto' display={{ base: 'block', lg: 'none' }}>
          <SummaryDrawer title={trans('t_group_summary')}>
            <GroupSummary farmsMap={farmsMap} farmOverviewFilter={farmOverviewFilter} />
          </SummaryDrawer>
        </Box>
      )}

      <AllFarmsAtHarvest mx='md' mb='md-alt' mt={isWebViewTypeMobile ? 'sm-alt' : 0} farmsMap={farmsMap} />

      <MyFarms
        farmsMap={farmsMap}
        customizeViewVariables={customizeViewVariables}
        profitProjectionToView={profitProjectionToView}
        setProfitProjectionToView={setProfitProjectionToView}
        indicatingStatusOptions={indicatingStatusOptions}
        setIndicatingStatus={setIndicatingStatus}
        selectedIndicatingStatus={selectedIndicatingStatusOption}
        farmOverviewFilter={farmOverviewFilter}
        setFarmOverviewFilter={setFarmOverviewFilter}
      />
    </Container>
  );
}

function Container(props: FlexProps) {
  return (
    <Flex flexDirection='column' bgColor='bg.gray.medium' overflowX='scroll' className='hideScrollbar' {...props} />
  );
}
