import { formatNumber } from '@utils/number';
import { getTrans } from '@i18n/get-trans';

export type WeightUnitType = 'pls' | 'g';

type TransformWeightWithUnitReturnType = {
  weight: number | string;
  unit: WeightUnitType;
  unitString?: string;
  weightString: string;
};

export function transformWeightWithUnit({
  lang,
  weightInGrams,
  unit
}: {
  lang: string;
  weightInGrams: number;
  unit: string;
}): TransformWeightWithUnitReturnType {
  const { trans } = getTrans();

  if (!weightInGrams) {
    return { weight: '-', unit: undefined, weightString: '-' };
  }

  if (unit === 'pls') {
    const weight = formatNumber(1 / weightInGrams, { lang, fractionDigits: 2 });
    const weightString = `${weight} ${trans('t_pls_per_g')}`;
    return { weight, unit: 'pls', unitString: trans('t_pls_per_g'), weightString };
  }
  const weight = formatNumber(weightInGrams, { lang, fractionDigits: 2 });
  const weightString = `${weight} ${trans('t_gram_g')}`;
  return { weight: weight, unit: 'g', unitString: trans('t_gram_g'), weightString };
}
