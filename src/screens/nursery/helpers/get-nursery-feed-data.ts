import { convertPoundsToKg, formatNumber } from '@utils/number';
import { FarmFeedTypes, PopulationFeedData } from '@xpertsea/module-farm-sdk';
import keyBy from 'lodash/keyBy';

const roundNumber = (n: number): number => formatNumber(n, { fractionDigits: 0, asNumber: true }) as number;

const getFeedData = ({ feedData, feedTypes }: GetNurseryFeedDataProps) => {
  if (!feedData || !feedData?.length || !feedTypes || !feedTypes?.length)
    return { totalFeedGiven: 0, totalOtherDirectCost: 0, totalFeedCost: 0 };

  const feedTypesHashmap = keyBy(feedTypes, '_id');

  const feedGivenByType: Record<string, number> = {};

  let totalOtherDirectCost = 0;
  feedData.map((ele) => {
    const { feed, otherDirectCost } = ele;

    feed.forEach(({ feedTypeId, totalLbs }) => {
      if (feedTypeId in feedGivenByType) {
        feedGivenByType[feedTypeId] += totalLbs || 0;
      } else {
        feedGivenByType[feedTypeId] = totalLbs || 0;
      }
    });
    totalOtherDirectCost += otherDirectCost || 0;
  });

  let totalFeedGiven = 0;
  let totalFeedCost = 0;
  Object.entries(feedGivenByType).forEach(([feedTypeId, totalFeedGive]) => {
    const feedInfo = feedTypesHashmap[feedTypeId];
    if (!feedInfo?.costPerKg) return;

    const feedGivenInKg = convertPoundsToKg(totalFeedGive);
    totalFeedGiven += feedGivenInKg;
    totalFeedCost += feedGivenInKg * feedInfo.costPerKg;
  });

  return {
    totalFeedGiven: roundNumber(totalFeedGiven),
    totalOtherDirectCost: roundNumber(totalOtherDirectCost),
    totalFeedCost: roundNumber(totalFeedCost)
  };
};

type GetNurseryFeedDataProps = { feedData: PopulationFeedData[]; feedTypes: FarmFeedTypes[] };

export function getNurseryFeedData({ feedData, feedTypes }: GetNurseryFeedDataProps) {
  const { totalFeedGiven, totalOtherDirectCost, totalFeedCost } = getFeedData({ feedData, feedTypes }) ?? {};

  const totalCosts = totalOtherDirectCost + totalFeedCost;

  return {
    totalFeedGiven,
    totalCosts
  };
}
