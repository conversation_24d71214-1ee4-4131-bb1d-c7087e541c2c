import { Box, Flex, Table, Text } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { EditNurseryStockingModal } from '@screens/nursery/components/modals/edit-nursery-stocking-modal';
import { useAppSelector } from '@redux/hooks';
import { BaseButton } from '@components/base/base-button';
import { formatNumber, isNumber } from '@utils/number';
import { DateTime } from 'luxon';
import { NurseryPopulationType, NurseryType } from '@screens/nursery/hooks/nursery-population-fields';
import { AdminOrSupervisorWrapper } from '@components/permission-wrappers/admin-or-supervisor-wrapper';
import { TableContainer } from '@components/ui/table-container';

interface Props {
  nursery: NurseryType;
  population: NurseryPopulationType;
}

export function NurseryStocking(props: Props) {
  const { nursery, population } = props;
  const { _id: nurseryId, name: nurseryName, size: nurserySize, isArchived } = nursery ?? {};
  const { harvest, hatcheryName, geneticName, seedingQuantity, stockedAt, seedingAverageWeight, stockingCostsMillar } =
    population ?? {};

  const lang = useAppSelector((state) => state.app.lang);

  const { trans } = getTrans();

  return (
    <Box>
      <Flex justify='space-between' align='center' mb='md'>
        <Text fontSize='lg' fontWeight={600}>
          {trans('t_stocking')}
        </Text>
        {!harvest?.date && !isArchived && (
          <EditNurseryStockingModal
            nurseryId={nurseryId}
            nurseryName={nurseryName}
            nurserySize={nurserySize}
            population={nursery.population}
          >
            <BaseButton variant='link' size='sm' fontWeight={500}>
              {trans('t_edit')}
            </BaseButton>
          </EditNurseryStockingModal>
        )}
      </Flex>

      <TableContainer>
        <Table.Root>
          <Table.Header>
            <Table.Row border='none'>
              <TableHeaderCell title={trans('t_lab_source')} />
              <TableHeaderCell title={trans('t_nauplii_source')} />
              <TableHeaderCell title={trans('t_animals_stocked')} />
              <TableHeaderCell title={trans('t_stocking_date')} />
              <TableHeaderCell title={trans('t_stocking_weight_g')} />
              <AdminOrSupervisorWrapper>
                <TableHeaderCell title={trans('t_cost_millar')} />
              </AdminOrSupervisorWrapper>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            <Table.Row>
              <Table.Cell textAlign='center' pb={0} border='none'>
                <Text>{hatcheryName || '-'}</Text>
              </Table.Cell>
              <Table.Cell textAlign='center' pb={0} border='none'>
                <Text>{geneticName || '-'}</Text>
              </Table.Cell>
              <Table.Cell textAlign='center' pb={0} border='none'>
                <Text>
                  {isNumber(seedingQuantity) ? formatNumber(seedingQuantity, { lang, fractionDigits: 0 }) : '-'}
                </Text>
              </Table.Cell>
              <Table.Cell textAlign='center' pb={0} border='none'>
                <Text>{stockedAt ? DateTime.fromISO(stockedAt).toFormat('MMM dd, yyyy', { locale: lang }) : '-'}</Text>
              </Table.Cell>
              <Table.Cell textAlign='center' pb={0} border='none'>
                <Text>
                  {isNumber(seedingAverageWeight)
                    ? formatNumber(seedingAverageWeight, { lang, fractionDigits: 2 })
                    : '-'}
                </Text>
              </Table.Cell>
              <AdminOrSupervisorWrapper>
                <Table.Cell textAlign='center' pb={0} border='none'>
                  <Text>
                    {isNumber(stockingCostsMillar)
                      ? formatNumber(stockingCostsMillar, { lang, fractionDigits: 2, isCurrency: true })
                      : '-'}
                  </Text>
                </Table.Cell>
              </AdminOrSupervisorWrapper>
            </Table.Row>
          </Table.Body>
        </Table.Root>
      </TableContainer>
    </Box>
  );
}

function TableHeaderCell({ title }: { title: string }) {
  return (
    <Table.ColumnHeader border='none' textAlign='center' p='0'>
      <Text bgColor='gray.100' py='xs-alt' borderRadius='base' mx='3xs' fontSize='md' fontWeight={400}>
        {title}
      </Text>
    </Table.ColumnHeader>
  );
}
