import { Box, Flex, Table, TableColumnHeaderProps, Text } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { FormControlReactSelect } from '@components/form/form-control-react-select';
import { PartialPageLoader } from '@components/loaders/partial-page-loader';
import { yupResolver } from '@hookform/resolvers/yup';
import { getTrans } from '@i18n/get-trans';
import { CurrentFarmPonds } from '@redux/farm/set-current-farm-ponds';
import { useAppSelector } from '@redux/hooks';
import { transformWeightWithUnit } from '@screens/nursery/helpers/transform-weight-with-unit';
import { NurseryPopulationType } from '@screens/nursery/hooks/nursery-population-fields';
import { useUpdateNurseryApi } from '@screens/nursery/hooks/use-update-nursery-api';
import { AddIcon } from '@screens/settings/icons/add-icon';
import { formatNumber, isNumber } from '@utils/number';
import { useYupSchema, Yup, YupResolverType } from '@utils/yup';
import { DateTime } from 'luxon';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import { TableContainer } from '@components/ui/table-container';
import { TrashcanXIcon } from '@icons/trash-can/trash-can-x-icon';

interface Props {
  nurseryId: string;
  population: NurseryPopulationType;
}

export function DestinationSummary({ nurseryId, population }: Props) {
  const { harvest } = population ?? {};
  const {
    currentFarmPonds,
    isLoadingCurrentFarmPonds,
    currentFarm: { timezone: currentFarmTimezone }
  } = useAppSelector((state) => state.farm);

  const isHarvested = harvest?.date;

  if (isLoadingCurrentFarmPonds) {
    return (
      <Box h='300px' bg='white'>
        <PartialPageLoader />
      </Box>
    );
  }

  return (
    <Box flexDir='column' bg='white' rounded='2xl' px='md' pb='sm-alt'>
      {isHarvested ? (
        <PlannedDestinationTable
          harvest={harvest}
          farmPonds={currentFarmPonds}
          currentFarmTimezone={currentFarmTimezone}
        />
      ) : (
        <StockingDestinationsTable
          key={population?._id}
          population={population}
          farmPonds={currentFarmPonds}
          nurseryId={nurseryId}
        />
      )}
    </Box>
  );
}

function PlannedDestinationTable({
  harvest,
  farmPonds,
  currentFarmTimezone
}: {
  harvest: NurseryPopulationType['harvest'];
  farmPonds: CurrentFarmPonds;
  currentFarmTimezone: string;
}) {
  const { date: harvestAt } = harvest ?? {};
  const { trans } = getTrans();

  const { lang } = useAppSelector((state) => state.app);

  const transfers = harvest?.transfers ?? [];

  const totalAnimals =
    transfers?.reduce((acc, ele) => {
      return acc + ele.value;
    }, 0) ?? 0;

  return (
    <Flex flexDir='column' gap='sm-alt'>
      <Text size='label100' py='md'>
        {trans('t_destination')}: {trans('t_planned_stocking')}
      </Text>

      <TableContainer>
        <Table.Root>
          <TableHeader />
          <Table.Body>
            {transfers.map((record, index) => {
              const { pondId, value, avgWeight, totalWeight, avgWeightUnit } = record;

              const pond = farmPonds?.find((ele) => ele._id === pondId) ?? {};
              const percentageOfTotal = formatNumber(value / totalAnimals, {
                lang,
                fractionDigits: 0,
                isPercentage: true
              });

              const { weightString } = transformWeightWithUnit({ lang, weightInGrams: avgWeight, unit: avgWeightUnit });
              const isLastItem = index === transfers.length - 1;

              return (
                <Table.Row
                  borderBottom='0.5px solid'
                  borderColor='gray.200'
                  key={record.pondId}
                  {...(isLastItem && { borderBottom: 'none' })}
                >
                  <Table.Cell px='0' border='none'>
                    <Text size='label200'>{pond?.name ? pond.name : '-'}</Text>
                  </Table.Cell>
                  <Table.Cell px='0' border='none'>
                    <Text size='label200'>
                      {harvestAt
                        ? DateTime.fromISO(harvestAt, { zone: currentFarmTimezone }).toFormat('MMM dd, yyyy', {
                            locale: lang
                          })
                        : '-'}
                    </Text>
                  </Table.Cell>
                  <Table.Cell px='0' border='none'>
                    <Text size='label200'>{formatNumber(value, { fractionDigits: 0, lang })}</Text>
                  </Table.Cell>
                  <Table.Cell px='0' border='none'>
                    <Text size='label200'>
                      {isNumber(totalWeight)
                        ? `${formatNumber(totalWeight, { fractionDigits: 0, lang })} ${trans('t_kg')}`
                        : '-'}
                    </Text>
                  </Table.Cell>
                  <Table.Cell px='0' border='none'>
                    <Text size='label200'>{weightString}</Text>
                  </Table.Cell>
                  <Table.Cell px='0' border='none'>
                    <Text size='label200'>{percentageOfTotal || '-'}</Text>
                  </Table.Cell>
                </Table.Row>
              );
            })}
          </Table.Body>
        </Table.Root>
      </TableContainer>
    </Flex>
  );
}

type FormValues = { destinations: { pondId: string }[] };

function StockingDestinationsTable({
  nurseryId,
  population,
  farmPonds
}: {
  nurseryId: string;
  population: NurseryPopulationType;
  farmPonds: CurrentFarmPonds;
}) {
  const { trans } = getTrans();
  const { reload } = useRouter();

  const [{ isLoading }, updateNursery] = useUpdateNurseryApi();
  const [isEditing, setIsEditing] = useState(false);

  const defaultValues: { pondId: string }[] = population?.pondIds?.map((ele: string) => ({ pondId: ele }));

  const { schema } = useYupSchema({
    destinations: Yup.array().of(
      Yup.object().shape({
        pondId: Yup.string().label('t_pond').required()
      })
    )
  });

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isDirty },
    watch
  } = useForm<FormValues>({
    resolver: yupResolver(schema) as YupResolverType<FormValues>,
    defaultValues: {
      destinations: defaultValues?.length ? defaultValues : [{ pondId: '' }]
    }
  });

  const { fields, append, remove } = useFieldArray({ control, name: 'destinations' });

  const destinationsPondsWatch = watch('destinations');

  const onCancel = () => {
    reset();
    setIsEditing(false);
  };

  const canDelete = isEditing && fields?.length > 1;
  const canAddDestinations = isEditing && farmPonds?.length > destinationsPondsWatch.length;

  const onSubmit = handleSubmit((data: FormValues) => {
    updateNursery({
      params: {
        filter: { nurseryId },
        set: {
          population: {
            ...population,
            pondIds: data.destinations.map((ele) => ele.pondId)
          }
        }
      },
      successCallback: () => {
        onCancel();
        reload();
      }
    });
  });

  return (
    <Flex flexDir='column' gap='sm-alt' as='form' onSubmit={onSubmit}>
      <Flex align='center' justify='space-between' py='md'>
        <Text size='label100' lineHeight='20px'>
          {trans('t_destination')}: {trans('t_stocking')}
        </Text>
        {!isEditing && (
          <BaseButton variant='link' size='sm' onClick={() => setIsEditing(true)}>
            {trans('t_edit')}
          </BaseButton>
        )}
      </Flex>

      <Table.Root>
        <TableHeader hasDelete />
        <Table.Body>
          {fields.map((field, i) => {
            const { name: pondName } = farmPonds.find((ele) => ele._id === field.pondId) ?? {};

            const options = farmPonds?.reduce((acc, pond) => {
              const existingIdIndex = destinationsPondsWatch.findIndex((ele) => ele.pondId === pond._id);
              const isSelectedBefore = existingIdIndex >= 0 && existingIdIndex !== i;
              if (!isSelectedBefore) {
                acc.push({ label: pond.name, value: pond._id });
              }
              return acc;
            }, []);

            const isLastItem = i === fields.length - 1;

            return (
              <Table.Row
                borderBottom='0.5px solid'
                borderBottomColor='gray.200'
                key={field.id}
                {...(isLastItem && { borderBottom: 'none' })}
              >
                <Table.Cell px='0' w='250px'>
                  <Box pe='md'>
                    {isEditing ? (
                      <FormControlReactSelect
                        id={`destinations.${i}.pondId`}
                        name={`destinations.${i}.pondId`}
                        control={control}
                        options={options}
                        error={errors?.destinations?.[i]?.pondId?.message}
                        selectControlStyles={{ minHeight: '32px' }}
                      />
                    ) : (
                      <Text size='label200'>{pondName}</Text>
                    )}
                  </Box>
                </Table.Cell>
                <Table.Cell px='0'> - </Table.Cell>
                <Table.Cell px='0'> - </Table.Cell>
                <Table.Cell px='0'> - </Table.Cell>
                <Table.Cell px='0'> - </Table.Cell>
                <Table.Cell px='0'> - </Table.Cell>
                <Table.Cell>
                  {canDelete && (
                    <BaseButton variant='link' onClick={() => remove(i)} color='black'>
                      <TrashcanXIcon />
                    </BaseButton>
                  )}
                </Table.Cell>
              </Table.Row>
            );
          })}
        </Table.Body>
      </Table.Root>

      {canAddDestinations && (
        <Flex justify='space-between' align='center'>
          <BaseButton
            variant='link'
            _hover={{ textDecor: 'none' }}
            color='text.gray'
            my='md'
            onClick={() => append({ pondId: '' })}
          >
            <Flex align='center' justify='center' bg='bg.brandBlue' rounded='full' boxSize='20px' color='white' me='xs'>
              <AddIcon boxSize='18px' />
            </Flex>
            <Text textDecor='none'>{trans('t_add_destination')}</Text>
          </BaseButton>

          <Flex gap='md'>
            <BaseButton size='sm' variant='secondary' onClick={onCancel} disabled={isLoading}>
              {trans('t_cancel')}
            </BaseButton>
            <BaseButton size='sm' type='submit' disabled={!isDirty} loading={isLoading}>
              {trans('t_save')}
            </BaseButton>
          </Flex>
        </Flex>
      )}
    </Flex>
  );
}

function TableHeader({ hasDelete }: { hasDelete?: boolean }) {
  const { trans } = getTrans();

  return (
    <Table.Header>
      <Table.Row border='none'>
        <TableHeaderCell title={trans('t_destination')} />
        <TableHeaderCell title={trans('t_transfer_date')} />
        <TableHeaderCell title={trans('t_animals')} />
        <TableHeaderCell title={trans('t_biomass_kg')} />
        <TableHeaderCell title={trans('t_abw')} />
        <TableHeaderCell title={trans('t_animals_%')} />
        {hasDelete && <TableHeaderCell title='' />}
      </Table.Row>
    </Table.Header>
  );
}

function TableHeaderCell({ title }: { title: string } & TableColumnHeaderProps) {
  return (
    <Table.ColumnHeader border='none' px='0' py='xs-alt'>
      <Text size='label300' color='text.gray.disabled'>
        {title}
      </Text>
    </Table.ColumnHeader>
  );
}
