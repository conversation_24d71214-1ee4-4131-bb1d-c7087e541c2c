import { Flex, Table, TableColumnHeaderProps, Text } from '@chakra-ui/react';
import { AdminOrSupervisorWrapper } from '@components/permission-wrappers/admin-or-supervisor-wrapper';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { getDaysDiffBetweenDates } from '@screens/farm/helpers/farm-dates';
import { NurseryPopulationType } from '@screens/nursery/hooks/nursery-population-fields';
import { convertPoundsToKg, formatNumber, isNumber } from '@utils/number';
import { FarmFeedTypes, Population, PopulationFeedData } from '@xpertsea/module-farm-sdk';
import keyBy from 'lodash/keyBy';
import { DateTime } from 'luxon';
import { TableContainer } from '@components/ui/table-container';
import { gramsInKg } from '@utils/constants';

const roundNumber = (n: number): number => formatNumber(n, { fractionDigits: 0, asNumber: true }) as number;

function calculateFeedData({ feedData, feedTypes }: { feedData: PopulationFeedData[]; feedTypes: FarmFeedTypes[] }) {
  let totalOtherDirectCost = 0,
    totalFeedGiven = 0,
    totalFeedCost = 0,
    averageCostPerKg = 0;

  if (!feedData?.length || !feedTypes?.length)
    return { totalFeedGiven, totalOtherDirectCost, averageCostPerKg, totalFeedCost };

  const feedTypesHashmap = keyBy(feedTypes, '_id');
  const feedGivenByType: Record<string, number> = {};

  feedData.forEach(({ feed, otherDirectCost }) => {
    feed.forEach(({ feedTypeId, totalLbs }) => {
      if (feedTypeId in feedGivenByType) {
        feedGivenByType[feedTypeId] += totalLbs || 0;
      } else {
        feedGivenByType[feedTypeId] = totalLbs || 0;
      }
      totalFeedGiven += convertPoundsToKg(totalLbs);
    });
    totalOtherDirectCost += otherDirectCost || 0;
  });

  Object.entries(feedGivenByType).forEach(([feedTypeId, feedGivenByType]) => {
    const { costPerKg } = feedTypesHashmap[feedTypeId] ?? {};
    if (!costPerKg) return;

    const feedGivenInKg = convertPoundsToKg(feedGivenByType);
    totalFeedCost += feedGivenInKg * costPerKg;
    averageCostPerKg += (feedGivenInKg / totalFeedGiven) * costPerKg;
  });

  return {
    totalFeedGiven: roundNumber(totalFeedGiven),
    totalOtherDirectCost: roundNumber(totalOtherDirectCost),
    totalFeedCost: roundNumber(totalFeedCost),
    averageCostPerKg
  };
}

interface Props {
  population: NurseryPopulationType;
  nurserySize: number;
}
export function PastCycleSummary(props: Props) {
  const { population, nurserySize } = props;

  const { trans } = getTrans();
  const lang = useAppSelector((state) => state.app.lang);
  const { feedTypes, growthTarget } = useAppSelector((state) => state.farm.currentFarm) ?? {};

  const { harvest, seedingAverageWeight, seedingQuantity, stockingCostsMillar } = population ?? {};
  const { transfers, dryDays } = harvest ?? {};

  if (!harvest?.date) return null;

  const { costOverhead } = growthTarget ?? {};
  const feedData = population?.feedData as Population['feedData'];

  const { totalFeedGiven, averageCostPerKg, totalOtherDirectCost, totalFeedCost } = calculateFeedData({
    feedData,
    feedTypes
  });

  const cycleDays = getDaysDiffBetweenDates({
    baseDate: harvest.date,
    dateToCompare: population.stockedAt
  });

  const { totalTransferredAnimals, totalTransferredWeight } = transfers?.reduce(
    (acc, t) => {
      const { totalWeight, value } = t;
      acc.totalTransferredWeight += totalWeight ?? 0;
      acc.totalTransferredAnimals += value ?? 0;
      return acc;
    },
    { totalTransferredAnimals: 0, totalTransferredWeight: 0 }
  ) ?? { totalTransferredAnimals: 0, totalTransferredWeight: 0 };

  const weightAtTransfer = (totalTransferredWeight / totalTransferredAnimals) * gramsInKg;

  const growthDaily = (weightAtTransfer - seedingAverageWeight) / cycleDays;

  const fcrCumulative =
    totalFeedGiven / (totalTransferredWeight - (seedingQuantity * seedingAverageWeight) / gramsInKg);

  const totalIndirectCost = costOverhead * (cycleDays + dryDays) * nurserySize;
  const totalStockingCost = (stockingCostsMillar * seedingQuantity) / gramsInKg;

  const totalCost = totalOtherDirectCost + totalIndirectCost + totalFeedCost + totalStockingCost;
  const costMillarAtTransfer = totalCost / (totalTransferredAnimals / gramsInKg);

  return (
    <Flex flexDir='column' bg='white' px='md' rounded='2xl' gap='sm-alt' pb='sm-alt'>
      <Text size='label100' lineHeight='20px' py='md'>
        {trans('t_past_cycle_summary')}
      </Text>
      <TableContainer>
        <Table.Root>
          <Table.Header>
            <Table.Row border='none'>
              <TableHeaderCell title={trans('t_transfer_date')} />
              <TableHeaderCell title={trans('t_feed_given_kg')} />
              <AdminOrSupervisorWrapper>
                <TableHeaderCell title={trans('t_cost_$')} />
                <TableHeaderCell title={trans('t_feed_cost_per_kg')} />
              </AdminOrSupervisorWrapper>
              <TableHeaderCell title={trans('t_abw_at_transfer')} />
              <TableHeaderCell title={trans('t_days_of_culture')} />
              <TableHeaderCell title={trans('t_growth_g_day')} />
              <AdminOrSupervisorWrapper>
                <TableHeaderCell title={trans('t_cost_millar')} />
              </AdminOrSupervisorWrapper>
              <TableHeaderCell title={trans('t_fcr_cumulative')} />
            </Table.Row>
          </Table.Header>

          <Table.Body>
            <Table.Row>
              <Table.Cell px='0' py='md' border='none'>
                <Text size='label200'>
                  {harvest?.date ? DateTime.fromISO(harvest.date).toFormat('LLL dd, yyyy', { locale: lang }) : '-'}
                </Text>
              </Table.Cell>
              <Table.Cell px='0' py='md' border='none'>
                <Text size='label200'>
                  {totalFeedGiven
                    ? `${formatNumber(totalFeedGiven, { fractionDigits: 0, lang })} ${trans('t_kg')}`
                    : '-'}
                </Text>
              </Table.Cell>
              <AdminOrSupervisorWrapper>
                <Table.Cell px='0' py='md' border='none'>
                  <Text size='label200'>
                    {totalCost ? formatNumber(totalCost, { fractionDigits: 0, lang, isCurrency: true }) : '-'}
                  </Text>
                </Table.Cell>
                <Table.Cell px='0' py='md' border='none'>
                  <Text size='label200'>
                    {isNumber(averageCostPerKg)
                      ? formatNumber(averageCostPerKg, { fractionDigits: 2, lang, isCurrency: true })
                      : '-'}
                  </Text>
                </Table.Cell>
              </AdminOrSupervisorWrapper>
              <Table.Cell px='0' py='md' border='none'>
                <Text size='label200'>
                  {isNumber(weightAtTransfer)
                    ? `${formatNumber(weightAtTransfer, { fractionDigits: 2, lang })} ${trans('t_gram_g')}`
                    : '-'}
                </Text>
              </Table.Cell>
              <Table.Cell px='0' py='md-alt' border='none'>
                <Text size='label200'>
                  {isNumber(cycleDays) ? formatNumber(cycleDays, { fractionDigits: 0, lang }) : '-'}
                </Text>
              </Table.Cell>
              <Table.Cell px='0' py='md' border='none'>
                <Text size='label200'>
                  {isNumber(growthDaily)
                    ? trans('t_growth_g_over_d', { growth: formatNumber(growthDaily, { fractionDigits: 4, lang }) })
                    : '-'}
                </Text>
              </Table.Cell>
              <AdminOrSupervisorWrapper>
                <Table.Cell px='0' py='md' border='none'>
                  <Text size='label200'>
                    {isNumber(costMillarAtTransfer)
                      ? formatNumber(costMillarAtTransfer, { fractionDigits: 2, lang, isCurrency: true })
                      : '-'}
                  </Text>
                </Table.Cell>
              </AdminOrSupervisorWrapper>
              <Table.Cell px='0' py='md' border='none'>
                <Text size='label200'>
                  {isNumber(fcrCumulative) ? formatNumber(fcrCumulative, { fractionDigits: 2, lang }) : '-'}
                </Text>
              </Table.Cell>
            </Table.Row>
          </Table.Body>
        </Table.Root>
      </TableContainer>
    </Flex>
  );
}
function TableHeaderCell({ title }: { title: string } & TableColumnHeaderProps) {
  return (
    <Table.ColumnHeader border='none' py='xs-alt' px='0'>
      <Text size='label300' color='text.gray.disabled'>
        {title}
      </Text>
    </Table.ColumnHeader>
  );
}
