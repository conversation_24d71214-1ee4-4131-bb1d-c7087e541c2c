import { Box, Flex, Stack, Text } from '@chakra-ui/react';
import { EmptyState } from '@components/errors/empty-state';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { formatNumber, isNumber } from '@utils/number';
import { sortArray } from '@utils/sort-array';
import { Population } from '@xpertsea/module-farm-sdk';
import { DateTime } from 'luxon';
import { useMemo } from 'react';
import { DestinationSummary } from '@screens/nursery/components/nursery-overview/destination-summary';
import { NurseryPopulationType } from '@screens/nursery/hooks/nursery-population-fields';
import { getNurseryFeedData } from '@screens/nursery/helpers/get-nursery-feed-data';
import { PastCycleSummary } from '@screens/nursery/components/nursery-overview/past-cycle-summary';
import { NurseryAlerts } from './nursery-alerts';
import { AdminOrSupervisorWrapper } from '@components/permission-wrappers/admin-or-supervisor-wrapper';

interface Props {
  nurseryId: string;
  nurserySize: number;
  nurseryName: string;
  population: NurseryPopulationType;
}

export function NurseryOverview(props: Props) {
  const { nurseryId, nurserySize, nurseryName, population } = props;

  const isHarvested = !!population?.harvest?.date;

  return (
    <Stack gap='md-alt'>
      <NurseryAlerts
        population={population}
        nurseryId={nurseryId}
        nurseryName={nurseryName}
        nurserySize={nurserySize}
      />
      {!isHarvested ? (
        <CostFeedSummary population={population} />
      ) : (
        <PastCycleSummary nurserySize={nurserySize} population={population} />
      )}
      <DestinationSummary nurseryId={nurseryId} population={population} />
    </Stack>
  );
}

function CostFeedSummary({ population }: { population: NurseryPopulationType }) {
  const feedData = population?.feedData as Population['feedData'];

  const { trans } = getTrans();
  const lang = useAppSelector((state) => state.app.lang);
  const { feedTypes } = useAppSelector((state) => state.farm.currentFarm) ?? {};

  const filteredFeedData = useMemo(() => {
    if (!feedData?.length) return [];

    return sortArray({
      list: feedData,
      sortDir: 'asc',
      compareFunction: (a, b) => {
        const aDate = DateTime.fromISO(a.date);
        const bDate = DateTime.fromISO(b.date);
        return bDate.startOf('day') <= aDate.startOf('day') ? -1 : 1;
      }
    });
  }, [feedData]);

  const { totalCosts, totalFeedGiven } = getNurseryFeedData({ feedData: filteredFeedData, feedTypes });

  return (
    <Flex flexDir='column' bg='white' rounded='2xl' gap='sm-alt'>
      <Text size='label100' lineHeight='20px' p='md'>
        {trans('t_summary')}
      </Text>
      {!filteredFeedData?.length ? (
        <EmptyState minH='100px' />
      ) : (
        <Box px='md'>
          <Flex align='center' color='text.gray.disabled' py='xs-alt'>
            <Text size='label300' flex='0 0 200px'>
              {trans('t_variables')}
            </Text>
            <Text size='label300'>
              {filteredFeedData?.[0]?.date
                ? DateTime.fromISO(filteredFeedData[0]?.date).toFormat('MMMM dd', { locale: lang })
                : ''}
            </Text>
          </Flex>

          <Flex align='center' borderBottom='0.5px solid' borderColor='gray.200' py='md'>
            <Text flex='0 0 200px' size='label200'>
              {trans('t_feed_given_kg')}
            </Text>
            <Text size='label200'>
              {totalFeedGiven ? `${formatNumber(totalFeedGiven, { lang, fractionDigits: 0 })} ${trans('t_kg')}` : '-'}
            </Text>
          </Flex>

          <AdminOrSupervisorWrapper>
            <Flex align='center' py='md'>
              <Text flex='0 0 200px' size='label200'>
                {trans('t_cost_$')}
              </Text>
              <Text size='label200'>
                {isNumber(totalCosts) ? formatNumber(totalCosts, { lang, fractionDigits: 0, isCurrency: true }) : '-'}
              </Text>
            </Flex>
          </AdminOrSupervisorWrapper>
        </Box>
      )}
    </Flex>
  );
}
