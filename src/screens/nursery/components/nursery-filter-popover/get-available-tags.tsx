import { ReactNode } from 'react';
import { Flex, FlexProps, Text, TextProps } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { ChevronLeftFilled } from '@icons/chevron-left/chevron-left-filled';
import { ChevronRightFilled } from '@icons/chevron-right/chevron-right-filled';
import { getCycleDaysFormatted } from '@screens/group-summary/helpers/group-view';
import { NurseriesFilterPopoverFormType } from '@screens/nursery/components/nursery-filter-popover/nurseries-filter-popover';

type TagType = {
  label: ReactNode;
  changedValues: Partial<NurseriesFilterPopoverFormType>;
};

function LabelContainer(props: FlexProps) {
  return <Flex align='center' gap='2xs' {...props} />;
}
function LabelText(props: TextProps) {
  return <Text size='label300' color='text.gray.weak' {...props} />;
}

export const getAvailableTags = (filters: Partial<NurseriesFilterPopoverFormType>, lang: string) => {
  const { trans } = getTrans();
  const availableTags: TagType[] = [];

  if (filters.flagged) {
    availableTags.push({
      label: <LabelText data-cy='filterTag-flagged'>{trans('t_flagged_ponds')}</LabelText>,
      changedValues: { flagged: false }
    });
  }

  if (filters.cycleDays?.lessThan && !filters.cycleDays?.moreThan) {
    const cycleDaysFormatted = getCycleDaysFormatted({ number: filters.cycleDays?.lessThan, lang });
    availableTags.push({
      label: (
        <LabelContainer data-cy='filterTag-cycleDays'>
          <LabelText>{trans('t_days_of_culture')}</LabelText>
          <ChevronLeftFilled hasBackground={false} boxSize='20px' />
          <LabelText data-cy='cycleDays-lessThan-value'>{cycleDaysFormatted}</LabelText>
        </LabelContainer>
      ),
      changedValues: { cycleDays: undefined }
    });
  }

  if (filters.cycleDays?.moreThan && !filters.cycleDays?.lessThan) {
    const cycleDaysFormatted = getCycleDaysFormatted({ number: filters.cycleDays?.moreThan, lang });
    availableTags.push({
      label: (
        <LabelContainer data-cy='filterTag-cycleDays'>
          <LabelText>{trans('t_days_of_culture')}</LabelText>
          <ChevronRightFilled hasBackground={false} boxSize='20px' />
          <LabelText data-cy='cycleDays-moreThan-value'>{cycleDaysFormatted}</LabelText>
        </LabelContainer>
      ),
      changedValues: { cycleDays: undefined }
    });
  }

  if (filters.cycleDays?.moreThan && filters.cycleDays?.lessThan) {
    const cycleDaysMoreThanFormatted = getCycleDaysFormatted({ number: filters.cycleDays?.moreThan, lang });
    const cycleDaysLessThanFormatted = getCycleDaysFormatted({ number: filters.cycleDays?.lessThan, lang });
    availableTags.push({
      label: (
        <LabelContainer data-cy='filterTag-cycleDays'>
          <LabelText data-cy='cycleDays-moreThan-value'>{cycleDaysMoreThanFormatted}</LabelText>
          <ChevronLeftFilled hasBackground={false} boxSize='20px' />
          <LabelText>{trans('t_days_of_culture')}</LabelText>
          <ChevronLeftFilled hasBackground={false} boxSize='20px' />
          <LabelText data-cy='cycleDays-lessThan-value'>{cycleDaysLessThanFormatted}</LabelText>
        </LabelContainer>
      ),
      changedValues: { cycleDays: undefined }
    });
  }

  return availableTags;
};
