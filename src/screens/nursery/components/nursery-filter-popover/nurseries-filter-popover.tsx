import { Box, Flex, Icon<PERSON>utton, Separator, SimpleGrid, Stack, Text, useDisclosure } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { BaseFormCheckbox } from '@components/form/base-form-checkbox';
import { BaseFormNumberInput } from '@components/form/base-form-number-input';
import { lessThanFilterTest, moreThanFilterTest } from '@components/pond-filter-popover/pond-filter-popover-helpers';
import { LessThanMoreThan } from '@components/pond-filter-popover/use-generate-filter-tags';
import { getTrans } from '@i18n/get-trans';
import { numberTransform, useYupSchema, Yup, yupFormResolver, YupResolverType } from '@utils/yup';
import { ReactNode } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { PopoverBody, PopoverContent, PopoverRoot, PopoverTrigger } from '@components/ui/popover';
import { FilterIcon } from '@icons/filter-icon';

interface Props {
  defaultValues?: NurseriesFilterPopoverFormType;
  onApply: (data: NurseriesFilterPopoverFormType) => void;
}

export function NurseriesFilterPopover(props: Props) {
  const { defaultValues, onApply } = props;
  const { trans } = getTrans();

  const { onOpen, onClose, open } = useDisclosure();

  return (
    <PopoverRoot
      positioning={{ placement: 'bottom-end' }}
      open={open}
      onOpenChange={(e) => (e.open ? onOpen() : onClose())}
      lazyMount
      unmountOnExit
    >
      <PopoverTrigger asChild>
        <IconButton
          bgColor={open ? 'brandBlue.100' : 'white'}
          aria-label='filter-button'
          _focus={{ outline: 'none' }}
          data-cy='nursery-filters-btn'
        >
          <FilterIcon
            w='20px'
            h='20px'
            position='relative'
            top='1px'
            color={open ? 'graphBrandBlue' : undefined}
            _hover={{ color: 'graphBrandBlue' }}
          />
        </IconButton>
      </PopoverTrigger>
      <PopoverContent
        width={{ base: 'auto', md: '424px' }}
        border='none'
        rounded='2xl'
        bgColor='white'
        boxShadow='elevation.400'
      >
        <PopoverBody display='flex' flexDir='column' gap='2lg' px={0} py='md'>
          <Text size='label100' lineHeight='20px' px={{ base: 'sm-alt', md: 'md' }}>
            {trans('t_filter')}
          </Text>
          <Form onClose={onClose} onApply={onApply} defaultValues={defaultValues} />
        </PopoverBody>
      </PopoverContent>
    </PopoverRoot>
  );
}

export type NurseriesFilterPopoverFormType = {
  flagged: boolean;
  cycleDays: LessThanMoreThan;
};

function Form(props: Props & { onClose: () => void }) {
  const { onClose, onApply, defaultValues } = props;
  const { trans } = getTrans();

  const filterRowSchema = Yup.object().shape({
    lessThan: Yup.number().transform(numberTransform).nullable().min(0).test(lessThanFilterTest).label('t_less_than'),
    moreThan: Yup.number().transform(numberTransform).nullable().min(0).test(moreThanFilterTest).label('t_more_than')
  });

  const { schema } = useYupSchema({
    flagged: Yup.boolean(),
    cycleDays: filterRowSchema
  });

  const { watch, control, setValue, handleSubmit } = useForm<NurseriesFilterPopoverFormType>({
    mode: 'onChange',
    defaultValues,
    resolver: yupFormResolver(schema) as YupResolverType<NurseriesFilterPopoverFormType>
  });

  const cycleDaysWatch = watch('cycleDays');

  const onSubmit = (data: NurseriesFilterPopoverFormType) => {
    onApply(data);
    onClose();
  };

  return (
    <Box as='form' onSubmit={handleSubmit(onSubmit)} px={{ base: 'sm-alt', md: 'md' }} data-cy='nursery-filters-form'>
      <Box mb='lg'>
        <Stack gap='md'>
          <Text size='label200'>{trans('t_show_ponds_that_are')}</Text>
          <Controller
            name='flagged'
            control={control}
            render={({ field: { value, onChange, ...rest } }) => {
              return (
                <BaseFormCheckbox
                  {...rest}
                  id='flagged'
                  label={trans('t_flagged_ponds')}
                  data-cy='flaggedInput'
                  checked={value}
                  onCheckedChange={({ checked }) => onChange(checked)}
                />
              );
            }}
          />
        </Stack>

        <Separator borderColor='border.gray' my='lg' />

        <RowContainer
          title={trans('t_days_of_culture')}
          rightElement={
            <BaseButton
              variant='link'
              size='sm'
              textStyle='label.200'
              disabled={!cycleDaysWatch?.moreThan && !cycleDaysWatch?.lessThan}
              onClick={() => {
                setValue('cycleDays.lessThan', NaN);
                setValue('cycleDays.moreThan', NaN);
              }}
              data-cy='clear-btn'
            >
              {trans('t_clear')}
            </BaseButton>
          }
        >
          <SimpleGrid columns={2} gap='md'>
            <BaseFormNumberInput
              control={control}
              w='100%'
              name='cycleDays.moreThan'
              numericFormatProps={{ decimalScale: 0 }}
              inputRightElement={trans('t_days')}
              inputProps={{ placeholder: trans('t_min') }}
            />
            <BaseFormNumberInput
              control={control}
              w='100%'
              name='cycleDays.lessThan'
              numericFormatProps={{ decimalScale: 0 }}
              inputRightElement={trans('t_days')}
              inputProps={{ placeholder: trans('t_max') }}
            />
          </SimpleGrid>
        </RowContainer>
      </Box>

      <Flex align='center' gap='sm-alt' justify='flex-end' data-cy='form-actions-btn'>
        <BaseButton variant='secondary' onClick={onClose} data-cy='cancel-btn'>
          {trans('t_cancel')}
        </BaseButton>
        <BaseButton type='submit' data-cy='apply-btn'>
          {trans('t_apply')}
        </BaseButton>
      </Flex>
    </Box>
  );
}

function RowContainer({
  title,
  children,
  rightElement
}: {
  title?: string;
  children: ReactNode;
  rightElement?: ReactNode;
}) {
  return (
    <Flex align='flex-start'>
      <Text w='100px' size='label200' flexShrink={0} mt='sm-alt'>
        {title}
      </Text>
      <Box flexGrow={1}>{children}</Box>
      <Box w='50px' textAlign='end' flexShrink={0} mt='sm-alt'>
        {rightElement}
      </Box>
    </Flex>
  );
}
