import { Flex } from '@chakra-ui/react';
import React from 'react';
import { NurseriesFilterPopoverFormType } from './nurseries-filter-popover';
import { useAppSelector } from '@redux/hooks';
import { CloseFilled } from '@icons/close/close-filled';
import { getAvailableTags } from '@screens/nursery/components/nursery-filter-popover/get-available-tags';

interface Props {
  filters: Partial<NurseriesFilterPopoverFormType>;
  onRemove: (v: Partial<NurseriesFilterPopoverFormType>) => void;
}

export function NurseriesFilterTags(props: Props) {
  const { filters, onRemove } = props;
  const lang = useAppSelector((state) => state.app.lang);
  const availableTags = getAvailableTags(filters, lang);
  if (!availableTags?.length) return null;
  return (
    <Flex align='center' gap='sm' data-cy='filter-tags' flexWrap='wrap'>
      {availableTags.map(({ label, changedValues }, i) => {
        return (
          <Flex
            key={i}
            px='sm'
            h='24px'
            gap='xs'
            fontSize='md'
            align='center'
            rounded='full'
            fontWeight={400}
            bgColor='bg.gray.medium'
            border='1px solid'
            borderColor='border.gray'
            flexShrink={0}
          >
            {label}
            <CloseFilled
              boxSize='16px'
              cursor='pointer'
              _hover={{ opacity: 0.7 }}
              onClick={() => onRemove({ ...filters, ...changedValues })}
              data-cy='remove-filter'
            />
          </Flex>
        );
      })}
    </Flex>
  );
}
