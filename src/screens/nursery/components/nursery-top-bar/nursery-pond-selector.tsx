import { Flex, Heading, Skeleton, Text } from '@chakra-ui/react';
import { BaseLink } from '@components/base/base-link';
import { useListNurseriesApi } from '@screens/nursery/hooks/use-list-nurseries-api';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { slugify } from '@utils/string';
import { NurseryType } from '@screens/nursery/hooks/nursery-population-fields';
import { ChevronUpFilled } from '@icons/chevron-up/chevron-up-filled';
import { ChevronDownFilled } from '@icons/chevron-down/chevron-down-filled';
import { MenuButton, MenuContent, MenuItem, MenuRoot } from '@components/ui/menu';

interface Props {
  nurseryName: string;
  nurseryEid: number;
  farmId: string;
}

export function NurseryPondSelector({ nurseryName, nurseryEid, farmId }: Props) {
  const [{ isLoading, data, isFinishedOnce }, listNurseries] = useListNurseriesApi();

  const [open, setOpen] = useState(false);

  const getList = () => {
    if (data?.length) return;
    listNurseries({
      params: { filter: { farmId: [farmId] } },
      fields: { nurseries: { _id: 1, eid: 1, name: 1 } }
    });
  };
  return (
    <MenuRoot highlightedValue={nurseryEid.toString()} onOpenChange={(e) => setOpen(e.open)}>
      <MenuButton
        variant='ghost'
        size='sm'
        data-cy='pond-name'
        _hover={{}}
        _active={{}}
        flex={{ base: 1, lg: 'unset' }}
      >
        <Heading as='span' display='block' overflow='hidden' textOverflow='ellipsis'>
          {nurseryName}
        </Heading>
        {open ? (
          <ChevronUpFilled hasBackground={false} color='icon.gray' />
        ) : (
          <ChevronDownFilled hasBackground={false} color='icon.gray' />
        )}
      </MenuButton>
      <MenuContent maxH='40vh' minW='220px' overflow='auto' px='xs' py='sm' rounded='2xl'>
        {open && <PondsMenuList isLoading={isLoading || !isFinishedOnce} onOpen={getList} nurseries={data} />}
      </MenuContent>
    </MenuRoot>
  );
}

type PondsMenuListProps = {
  isLoading: boolean;
  onOpen: () => void;
  nurseries: NurseryType[];
};

function PondsMenuList({ onOpen, isLoading, nurseries }: PondsMenuListProps) {
  const { query } = useRouter();

  useEffect(() => {
    onOpen();
  }, []);

  if (isLoading) {
    return (
      <Flex direction='column' gap='xs'>
        <Skeleton h='20px' />
        <Skeleton h='20px' />
        <Skeleton h='20px' />
        <Skeleton h='20px' />
      </Flex>
    );
  }

  return (
    <>
      <MenuItem value='' display='none' />
      {nurseries?.map((ele) => {
        return (
          <MenuItem value={ele.eid?.toString()} key={ele._id} mb='2xs' autoFocus={false}>
            <Text fontSize='md' flex={1} pos='relative'>
              <BaseLink
                key={ele._id}
                route='/farm/[farmEid]/nursery/[nurseryEid]'
                params={{
                  farmEid: query.farmEid,
                  nurseryEid: slugify(`${ele.eid}-${ele.name}`),
                  tab: undefined
                }}
                display='block'
              >
                {ele.name}
              </BaseLink>
            </Text>
          </MenuItem>
        );
      })}
    </>
  );
}
