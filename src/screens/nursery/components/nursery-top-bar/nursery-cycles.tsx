import { Box, Heading, Text } from '@chakra-ui/react';
import { BaseLink } from '@components/base/base-link';
import { getTrans } from '@i18n/get-trans';
import { ChevronDownFilled } from '@icons/chevron-down/chevron-down-filled';
import { ChevronUpFilled } from '@icons/chevron-up/chevron-up-filled';
import { useAppSelector } from '@redux/hooks';
import { NurseryType } from '@screens/nursery/hooks/nursery-population-fields';
import { slugify } from '@utils/string';
import { MenuButton, MenuContent, MenuItem, MenuRoot } from '@components/ui/menu';
import { useState } from 'react';

export function NurseryCyclesSelector({ nursery, populationId }: { nursery: NurseryType; populationId?: string }) {
  const { population, previousPopulations } = nursery;
  const { trans } = getTrans();
  const [open, setOpen] = useState(false);
  const { currentFarmSlug } = useAppSelector((state) => state.farm);

  const selectedPopulation = previousPopulations?.find((ele) => ele._id === populationId) ?? population;

  const isPast = !!selectedPopulation?.harvest?.date;

  if (!selectedPopulation) return;

  if (!previousPopulations?.length) {
    return <MenuHeading cycle={selectedPopulation.cycle} isPast={isPast} />;
  }

  return (
    <MenuRoot onOpenChange={(e) => setOpen(e.open)}>
      <MenuButton
        variant='ghost'
        _hover={{}}
        _active={{}}
        size='sm'
        data-cy='pond-name'
        flex={{ base: 1, lg: 'unset' }}
      >
        <MenuHeading cycle={selectedPopulation.cycle} isPast={isPast} />
        {open ? (
          <ChevronUpFilled hasBackground={false} color='icon.gray' />
        ) : (
          <ChevronDownFilled hasBackground={false} color='icon.gray' />
        )}
      </MenuButton>
      <MenuContent maxH='40vh' minW='220px' overflow='auto' px='xs' py='sm' rounded='2xl'>
        {open && (
          <>
            <MenuItem value='' display='none' />

            {previousPopulations?.map(({ _id, cycle }) => (
              <MenuItem value={_id} autoFocus={false} key={_id} p='0'>
                <Text fontSize='md' flex={1} pos='relative' p='0'>
                  <BaseLink
                    route='/farm/[farmEid]/nursery/[nurseryEid]/population/[populationId]'
                    params={{
                      farmEid: currentFarmSlug,
                      nurseryEid: slugify(`${nursery.eid}-${nursery.name}`),
                      populationId: _id
                    }}
                    display='block'
                    py='sm'
                    px='xs'
                    _focusVisible={{ outline: 'none' }}
                  >
                    {trans('t_cycle')} {cycle}
                  </BaseLink>
                </Text>
              </MenuItem>
            ))}
            {population && (
              <MenuItem value={population.cycle} autoFocus={false} p='0'>
                <Text fontSize='md' flex={1} pos='relative' p='0'>
                  <BaseLink
                    route='/farm/[farmEid]/nursery/[nurseryEid]/population/[populationId]'
                    params={{
                      farmEid: currentFarmSlug,
                      nurseryEid: nursery.eid,
                      populationId: population._id
                    }}
                    display='block'
                    py='sm'
                    px='xs'
                    _focusVisible={{ outline: 'none' }}
                  >
                    {trans('t_cycle')} {population.cycle}
                  </BaseLink>
                </Text>
              </MenuItem>
            )}
          </>
        )}
      </MenuContent>
    </MenuRoot>
  );
}

function MenuHeading({ cycle, isPast }: { cycle: string | number; isPast?: boolean }) {
  const { trans } = getTrans();
  return (
    <Heading as='span' display='block' overflow='hidden' textOverflow='ellipsis'>
      {trans('t_cycle')} {cycle}{' '}
      {isPast && (
        <Box as='span' color='text.gray.disabled'>
          ({trans('t_past')})
        </Box>
      )}
    </Heading>
  );
}
