import { Box, Flex, Text } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { getDaysDiffBetweenDates } from '@screens/farm/helpers/farm-dates';
import { NurseryPopulationType, NurseryType } from '@screens/nursery/hooks/nursery-population-fields';
import { formatNumber, isNumber } from '@utils/number';
import { DateTime } from 'luxon';
import { useMemo } from 'react';
import { useAppSelector } from '@redux/hooks';
import { AdminOrSupervisorWrapper } from '@components/permission-wrappers/admin-or-supervisor-wrapper';
import { sqPerHectare } from '@utils/constants';

interface Props {
  nursery: NurseryType;
  population: NurseryPopulationType;
}

export function NurserySummary(props: Props) {
  const { nursery, population } = props;
  const { trans } = getTrans();
  const { size } = nursery ?? {};
  const {
    stockedAt,
    harvest,
    stockedAtTimezone,
    stockedType,
    stockingCostsMillar,
    hatcheryName,
    geneticName,
    seedingQuantity,
    seedingAverageWeight,
    seedingAverageWeightUnit,
    stockingAreaDensity,
    invoicedQuantity
  } = population || {};
  const { date: harvestDate } = harvest ?? {};

  const lang = useAppSelector((state) => state.app.lang);

  const stockedAtLuxon = DateTime.fromISO(stockedAt);
  const daysOfCulture = useMemo(() => {
    const baseDate = !harvestDate ? DateTime.local({ zone: stockedAtTimezone }) : stockedAtLuxon;
    const days = getDaysDiffBetweenDates({
      dateToCompare: stockedAtLuxon.toFormat('yyyy-MM-dd'),
      baseDate: baseDate.toFormat('yyyy-MM-dd')
    });

    return days;
  }, [stockedAt, harvestDate]);

  const stockingDensityOverM2 = stockingAreaDensity ? stockingAreaDensity / sqPerHectare : 0;

  return (
    <Box fontWeight={600}>
      <Box mb='lg'>
        <Text mb='sm-alt'>{trans('t_summary')}</Text>
        <OneRow title={trans('t_pond_size')} value={size ? `${size} ${trans('t_ha')}` : '-'} />
        <OneRow
          title={trans('t_days_of_culture')}
          value={daysOfCulture ? ` ${trans('t_count_days', { count: daysOfCulture })}` : '-'}
        />
      </Box>
      <Text mb='sm-alt'>{trans('t_stocking')}</Text>
      <OneRow
        title={trans('t_type_of_stocking')}
        value={stockedType === 'transfer' ? trans('t_transfer') : stockedType === 'direct' ? trans('t_direct') : '-'}
      />
      <AdminOrSupervisorWrapper>
        <OneRow
          title={trans('t_cost_millar')}
          value={
            isNumber(stockingCostsMillar)
              ? formatNumber(stockingCostsMillar, { lang, fractionDigits: 2, isCurrency: true })
              : '-'
          }
        />
      </AdminOrSupervisorWrapper>
      <OneRow title={trans('t_lab_source')} value={hatcheryName || '-'} />
      <OneRow title={trans('t_nauplii_source')} value={geneticName || '-'} />
      <OneRow
        title={trans('t_stocking_density_ha')}
        value={isNumber(stockingAreaDensity) ? formatNumber(stockingAreaDensity, { lang, fractionDigits: 0 }) : '-'}
      />
      <OneRow
        title={trans('t_stocking_density_m2')}
        value={isNumber(stockingDensityOverM2) ? formatNumber(stockingDensityOverM2, { lang, fractionDigits: 1 }) : '-'}
      />
      <OneRow
        title={trans('t_stocking_date')}
        value={stockedAtLuxon.isValid ? stockedAtLuxon.toFormat('LLL dd, yyyy', { locale: lang }) : '-'}
      />
      <OneRow
        title={trans('t_animals_stocked')}
        value={isNumber(seedingQuantity) ? formatNumber(seedingQuantity, { lang, fractionDigits: 0 }) : '-'}
      />
      <OneRow
        title={`${trans('t_stocking_weight')} ${seedingAverageWeightUnit}`}
        value={isNumber(seedingAverageWeight) ? formatNumber(seedingAverageWeight, { lang, fractionDigits: 2 }) : '-'}
      />
      <OneRow
        title={trans('t_invoiced_quantity')}
        value={isNumber(invoicedQuantity) ? formatNumber(invoicedQuantity, { lang, fractionDigits: 0 }) : '-'}
      />
    </Box>
  );
}

function OneRow({ title, value }: { title: string; value: string | number }) {
  return (
    <Flex justify='space-between' align='center' py='sm-alt' borderBottom='0.5px solid' borderColor='border.gray'>
      <Text>{title}</Text>
      <Text>{value}</Text>
    </Flex>
  );
}
