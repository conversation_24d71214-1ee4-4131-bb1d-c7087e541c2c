import { Box, Flex, FlexProps, Icon, Image, SimpleGrid, Text } from '@chakra-ui/react';
import { BaseLink } from '@components/base/base-link';
import { slugify } from '@utils/string';
import { BaseButton } from '@components/base/base-button';
import { actionsName } from '@utils/segment';
import { getTrans } from '@i18n/get-trans';
import { AddNurseryStockingModal } from '@screens/nursery/components/add-nursery-stocking-modal';
import { FlagSolid } from '@icons/flag/flag-solid';
import { FlagOutlined } from '@icons/flag/flag-outlined';
import { useAppSelector } from '@redux/hooks';
import { formatNumber, isNumber } from '@utils/number';
import { useState } from 'react';
import { useUpdateNurseryApi } from '@screens/nursery/hooks/use-update-nursery-api';
import { NurseryPopulationType } from '@screens/nursery/hooks/nursery-population-fields';
import { getNurseryFeedData } from '@screens/nursery/helpers/get-nursery-feed-data';
import { AdminOrSupervisorWrapper } from '@components/permission-wrappers/admin-or-supervisor-wrapper';
import { BaseSpinner } from '@components/base/base-spinner';

interface NurseryCardProps {
  nurseryId: string;
  nurseryName: string;
  nurseryEid: number;
  farmEid: string;
  nurserySize?: number;
  population?: NurseryPopulationType;
  destinations?: string;
}

export function NurseryCard(props: NurseryCardProps) {
  const { trans } = getTrans();

  const { nurseryId, nurseryName, nurseryEid, farmEid, nurserySize, population, destinations } = props;

  const isNewNursery = !Object.keys(population || {}).length;
  const isHarvested = population?.harvest?.date;

  const [{ isLoading }, updateNursery] = useUpdateNurseryApi();
  const [isFlagged, setIsFlagged] = useState<boolean>(!!population?.metadata?.isFlagged as boolean);

  const onUpdateFlag = () => {
    if (isLoading) return;
    updateNursery({
      params: {
        filter: { nurseryId },
        set: {
          population: {
            ...population,
            metadata: { ...population.metadata, isFlagged: !isFlagged }
          }
        }
      },
      successCallback: () => {
        setIsFlagged((val) => !val);
      }
    });
  };

  if (isNewNursery || isHarvested) {
    return (
      <Flex
        minH='190px'
        bgColor='white'
        flexDirection='column'
        p='md'
        borderRadius='2xl'
        pos='relative'
        boxShadow='elevation.100'
        gap='md'
        position='relative'
      >
        <BaseLink
          w='min-content'
          _hover={{ textDecoration: 'underline' }}
          route='/farm/[farmEid]/nursery/[nurseryEid]'
          params={{ farmEid, nurseryEid: slugify(`${nurseryEid}-${nurseryName}`) }}
        >
          <Text size='label100'>{nurseryName}</Text>
        </BaseLink>
        <Box position='absolute' top='42px' left={0} w='100%'>
          <Flex
            direction='column'
            gap='sm-alt'
            align='center'
            textAlign='center'
            data-cy='new-nursery-empty-state'
            flexWrap='wrap'
            m='auto'
            maxW='250px'
          >
            <Image w='56px' src={'/assets/insight-icon.png'} />

            <Text size='label300'>{trans('t_stock_your_nursery_to_start_getting')}</Text>
            <Box alignSelf='center'>
              <AddNurseryStockingModal
                nurseryId={nurseryId}
                farmEid={farmEid}
                nurseryEid={nurseryEid}
                nurseryName={nurseryName}
                nurserySize={nurserySize}
              >
                <BaseButton
                  size='md'
                  data-cy='stock-nursery-btn'
                  analyticsId={actionsName.nurseryListViewStockNurseryClicked}
                >
                  {trans('t_stock_pond')}
                </BaseButton>
              </AddNurseryStockingModal>
            </Box>
          </Flex>
        </Box>
      </Flex>
    );
  }

  return (
    <Flex
      flexDirection='column'
      gap='md-alt'
      p='md'
      borderRadius='2xl'
      boxShadow='elevation.100'
      bgColor='white'
      minH='190px'
    >
      <Flex align='center' gap='md' justify='space-between'>
        <BaseLink
          _hover={{ textDecoration: 'underline' }}
          route='/farm/[farmEid]/nursery/[nurseryEid]'
          params={{ farmEid, nurseryEid: slugify(`${nurseryEid}-${nurseryName}`) }}
        >
          <Text size='label100'>{nurseryName}</Text>
        </BaseLink>
        {isLoading && <BaseSpinner borderWidth='2px' size='sm' />}
        {!isLoading && (
          <Icon
            cursor='pointer'
            boxSize='20px'
            as={isFlagged ? FlagSolid : FlagOutlined}
            onClick={onUpdateFlag}
            _hover={{ opacity: 0.8 }}
          />
        )}
      </Flex>
      <NurseryCards population={population} destinations={destinations} />
    </Flex>
  );
}

function NurseryCards({ population, destinations }: { population: NurseryPopulationType; destinations: string }) {
  const feedData = population?.feedData ?? [];
  const lang = useAppSelector((state) => state.app.lang);
  const { feedTypes } = useAppSelector((state) => state.farm.currentFarm) ?? {};

  const { trans } = getTrans();

  const { totalCosts, totalFeedGiven } = getNurseryFeedData({ feedData, feedTypes });

  return (
    <Box>
      <SimpleGrid columns={2} gap='sm-alt' mb='sm-alt'>
        <AdminOrSupervisorWrapper>
          <SummaryCard>
            <Text size='label300'>{trans('t_cost_$')}:</Text>
            <Text size='label300'>
              {isNumber(totalCosts) ? formatNumber(totalCosts, { lang, fractionDigits: 0, isCurrency: true }) : '-'}
            </Text>
          </SummaryCard>
        </AdminOrSupervisorWrapper>

        <SummaryCard>
          <Text size='label300'>{trans('t_feed_given_kg')}:</Text>
          <Text size='label300'>
            {totalFeedGiven ? `${formatNumber(totalFeedGiven, { lang, fractionDigits: 0 })} ${trans('t_kg')}` : '-'}
          </Text>
        </SummaryCard>
      </SimpleGrid>

      <SummaryCard>
        <Text size='label300'>{trans('t_destination')}:</Text>
        <Text size='label300'>{destinations || '-'}</Text>
      </SummaryCard>
    </Box>
  );
}

function SummaryCard(props: FlexProps) {
  return (
    <Flex
      h='54px'
      p='sm-alt'
      gap='sm-alt'
      justify='center'
      direction='column'
      borderRadius='lg'
      border='1px solid'
      borderColor='border.gray.weak'
      position='relative'
      {...props}
    />
  );
}
