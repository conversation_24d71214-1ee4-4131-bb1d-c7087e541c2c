import { Box, BoxProps, chakra, Flex, FlexProps, InputProps, Text, useDisclosure } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { BaseFormNumberInput } from '@components/form/base-form-number-input';
import { FormControlDateInput } from '@components/form/form-control-date-input';
import { yupResolver } from '@hookform/resolvers/yup';
import { useAnalytics } from '@hooks/use-analytics';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { formatNumber } from '@utils/number';
import { actionsName } from '@utils/segment';
import { numberTransform, useYupSchema, Yup, YupResolverType } from '@utils/yup';
import { ReactNode } from 'react';
import { Control, FieldErrors, useFieldArray, useForm, UseFormWatch } from 'react-hook-form';
import { FormControlReactSelect } from '@components/form/form-control-react-select';
import { useRouter } from 'next/router';
import { useUpdateNurseryApi } from '@screens/nursery/hooks/use-update-nursery-api';
import { AddIcon } from '@screens/settings/icons/add-icon';
import { RiDeleteBin2Fill } from 'react-icons/ri';
import { NurseryPopulationType } from '@screens/nursery/hooks/nursery-population-fields';
import { AdminOrSupervisorWrapper } from '@components/permission-wrappers/admin-or-supervisor-wrapper';
import { DialogBody, DialogContent, DialogFooter, DialogHeader, DialogRoot, DialogTitle } from '@components/ui/dialog';

type StockFormValues = {
  cycle: string;
  stockedAt: Date;
  seedingAverageWeight: number;
  seedingAverageWeightUnit: string;
  seedingQuantity: number;
  stockingCostsMillar: number;
  hatcheryName?: string;
  geneticName?: string;
  destinationsPonds: { pondId: string }[];
  invoicedQuantity?: number;
  overheadCostsPerHaPerDay?: number;
};

const dateToBeTwoYearsLess = new Date();
dateToBeTwoYearsLess.setFullYear(dateToBeTwoYearsLess.getFullYear() - 2);

interface EditStockingModalProps {
  nurseryId: string;
  nurseryName: string;
  nurserySize: number;
  population: NurseryPopulationType;
  children: ReactNode;
  containerProps?: BoxProps;
}

export function EditNurseryStockingModal(props: EditStockingModalProps) {
  const { children, nurseryId, nurseryName, nurserySize, population, containerProps = {} } = props;

  const { trans } = getTrans();

  /// Hooks
  const { reload } = useRouter();
  const { trackAction } = useAnalytics();
  const { onOpen, onClose, open } = useDisclosure();
  const [{ isLoading: isUpdating }, updateNursery] = useUpdateNurseryApi();

  /// Redux
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);

  /// FORM DATA
  const { timezone } = currentFarm ?? {};
  const {
    cycle,
    seedingAverageWeight,
    seedingQuantity,
    stockedAt,
    stockingCostsMillar,
    hatcheryName,
    geneticName,
    invoicedQuantity,
    pondIds = [],
    seedingAverageWeightUnit,
    overheadCostsPerHaPerDay
  } = population ?? {};

  const defaultValues: StockFormValues = {
    cycle,
    stockingCostsMillar,
    stockedAt: stockedAt ?? new Date(),
    seedingAverageWeight:
      !seedingAverageWeightUnit || seedingAverageWeightUnit === 'g' ? seedingAverageWeight : 1 / seedingAverageWeight,
    seedingAverageWeightUnit: seedingAverageWeightUnit ?? 'g',
    seedingQuantity,
    invoicedQuantity,
    hatcheryName,
    geneticName,
    destinationsPonds: pondIds?.length ? pondIds.map((pondId: string) => ({ pondId })) : [{ pondId: '' }],
    overheadCostsPerHaPerDay
  };

  const defaultMinValue = 0;
  const defaultMaxCostMillar = 50_000;

  const { schema } = useYupSchema({
    cycle: Yup.string().required(trans('t_required')).label('t_cycle_number'),
    stockedAt: Yup.date()
      .transform(numberTransform)
      .min(dateToBeTwoYearsLess, trans('t_min_allowed_stocked'))
      .required(trans('t_required'))
      .label('t_stocked_at'),
    seedingAverageWeight: Yup.number()
      .transform(numberTransform)
      .positive()
      .nullable()
      .required(trans('t_required'))
      .label('t_stocking_average_weight'),
    seedingAverageWeightUnit: Yup.string().required().label('t_average_wight_unit'),
    seedingQuantity: Yup.number()
      .transform(numberTransform)
      .positive()
      .nullable()
      .required(trans('t_required'))
      .label('t_animals_stocked'),
    stockingCostsMillar: Yup.number()
      .transform(numberTransform)
      .min(defaultMinValue, trans('t_min_x', { min: defaultMinValue }))
      .max(defaultMaxCostMillar, trans('t_max_x', { max: defaultMaxCostMillar }))
      .nullable()
      .label('t_cost_millar'),
    hatcheryName: Yup.string().nullable().label('t_hatchery'),
    geneticName: Yup.string().nullable().label('t_genetic'),
    invoicedQuantity: Yup.number().nullable().transform(numberTransform).label('t_invoiced_quantity_opt'),
    destinationsPonds: Yup.array().of(
      Yup.object().shape({
        pondId: Yup.string().required().label('t_pond')
      })
    ),
    overheadCostsPerHaPerDay: Yup.number()
      .transform(numberTransform)
      .positive()
      .nullable()
      .label('t_overhead_cost_ha_day')
  });

  const {
    handleSubmit,
    control,
    formState: { errors },
    watch,
    reset
  } = useForm<StockFormValues>({
    resolver: yupResolver(schema) as YupResolverType<StockFormValues>,
    defaultValues
  });

  /// Handlers
  const handleReload = () => {
    reload();
  };

  const handleModalOpen = () => {
    trackAction(actionsName.stockPondClicked, { nurseryName, nurseryId }).then();
    onOpen();
  };

  const handleModalClose = () => {
    reset(defaultValues);
    onClose();
  };

  const onSubmit = (data: StockFormValues) => {
    const { seedingQuantity, seedingAverageWeightUnit } = data;
    const { destinationsPonds, seedingAverageWeight: seedingAverageWeightFromData, ...rest } = data;
    const stockingDensity = seedingQuantity ? seedingQuantity / nurserySize : null;

    const seedingAverageWeight =
      seedingAverageWeightUnit === 'g' ? seedingAverageWeightFromData : 1 / seedingAverageWeightFromData;

    trackAction(actionsName.nurseryEditStock, { nurseryName, nurseryId }).then();
    updateNursery({
      params: {
        filter: { nurseryId },
        set: {
          population: {
            ...population,
            ...rest,
            seedingAverageWeight,
            stockingAreaDensity: stockingDensity,
            stockedAtTimezone: timezone,
            pondIds: destinationsPonds.map((ele) => ele.pondId)
          }
        }
      },
      successCallback: () => {
        onClose();
        handleReload();
      }
    });
  };

  return (
    <DialogRoot
      open={open}
      onOpenChange={(e) => {
        e.open ? handleModalOpen() : handleModalClose();
      }}
      size='lg'
      restoreFocus={false}
    >
      <Box
        {...containerProps}
        onClick={(e) => {
          e.stopPropagation();
          handleModalOpen();
        }}
      >
        {children}
      </Box>

      <DialogContent
        overflow='visible'
        closeTriggerProps={{ top: '4px' }}
        data-cy='stock-nursery-modal'
        rounded='2xl'
        width='446px'
      >
        <DialogHeader px='0' pt='sm-alt' pb={0}>
          <DialogTitle w='full'>
            <Text pb='sm-alt' ps='sm-alt' mb='sm-alt' borderBottom='0.5px solid' borderColor='border.gray'>
              {trans('t_stocking')}
            </Text>
            <Text ps='sm-alt'>
              {nurseryName} ({nurserySize} {trans('t_ha')})
            </Text>
          </DialogTitle>
        </DialogHeader>
        <chakra.form display='contents' onSubmit={handleSubmit(onSubmit)} noValidate>
          <DialogBody px='sm-alt' py='sm-alt'>
            <StockingFormFields errors={errors} control={control} watch={watch} nurserySize={nurserySize} />
          </DialogBody>
          <DialogFooter gap='sm-alt' px='sm-alt' pb='sm-alt' pt={0}>
            <BaseButton
              color='gray.700'
              variant='link'
              size='sm'
              fontWeight={500}
              onClick={handleModalClose}
              analyticsId={actionsName.addStockingCancelClicked}
              analyticsData={{ nurseryId, nurseryName }}
              data-cy='cancel-btn'
            >
              {trans('t_cancel')}
            </BaseButton>

            <BaseButton
              type='submit'
              size='sm'
              fontSize='md'
              fontWeight={500}
              loading={isUpdating}
              analyticsId={actionsName.addStockingSaveClicked}
              analyticsData={{ nurseryId, nurseryName }}
              data-cy='save-btn'
            >
              {trans('t_save_changes')}
            </BaseButton>
          </DialogFooter>
        </chakra.form>
      </DialogContent>
    </DialogRoot>
  );
}

const formContainerStyles: BoxProps = {
  display: 'flex',
  alignItems: 'center',
  flexDirection: 'row',
  justifyContent: 'space-between',
  bgColor: 'gray.100',
  px: 'sm-alt',
  py: 'xs-alt',
  borderRadius: '4px',
  gap: 'sm'
};
const inputStyles: InputProps = {
  textAlign: 'center',
  size: 'sm',
  w: '100px',
  bgColor: 'gray.100'
};
const labelStyles = { m: 0 };
const selectControlStyles = {
  backgroundColor: '#f2f2f7',
  borderRadius: '4px',
  borderColor: '#48484A',
  borderWidth: '0.5px',
  minWidth: '180px',
  minHeight: '32px',
  padding: '0px',
  ':hover': {
    borderWidth: '0.5px',
    borderColor: '#48484A'
  }
};

interface StockingFormFields {
  control: Control<StockFormValues>;
  errors: FieldErrors<StockFormValues>;
  watch: UseFormWatch<StockFormValues>;
  nurserySize: number;
}

function StockingFormFields(props: StockingFormFields) {
  const { control, errors, watch, nurserySize } = props;
  const { trans } = getTrans();
  const lang = useAppSelector((state) => state.app.lang);
  const watchedSeedingQuantity = watch('seedingQuantity');
  const stockingDensity = watchedSeedingQuantity
    ? formatNumber(watchedSeedingQuantity / nurserySize, { fractionDigits: 0, lang })
    : null;
  return (
    <Flex direction='column' gap='xs-alt'>
      <BaseFormNumberInput
        name='cycle'
        required={true}
        control={control}
        label={trans('t_cycle_number')}
        inputProps={{ ...inputStyles, textAlign: 'start', minW: '180px' }}
        numericFormatProps={{ decimalScale: 0 }}
        formLabelProps={labelStyles}
        inputContainerProps={{ w: 'auto' }}
        {...formContainerStyles}
      />

      <Flex {...(formContainerStyles as FlexProps)}>
        <Text asChild>
          <label htmlFor='stockedAt'>{trans('t_stocking_date')}</label>
        </Text>
        <FormControlDateInput
          id='stockedAt'
          name='stockedAt'
          error={errors?.stockedAt?.message}
          control={control}
          inputProps={inputStyles}
          labelProps={labelStyles}
          maxW='180px'
          maxDate={new Date()}
          dateFormat='MMM dd, yyyy'
          placement='left-end'
        />
      </Flex>

      <Flex {...(formContainerStyles as FlexProps)}>
        <Text>{trans('t_type_of_stocking')}</Text>
        <Text>{trans('t_direct')}</Text>
      </Flex>

      <DestinationsPonds control={control} errors={errors} watch={watch} />

      <BaseFormNumberInput
        control={control}
        name='seedingQuantity'
        required
        inputProps={{ ...inputStyles, textAlign: 'start', minW: '180px' }}
        formLabelProps={labelStyles}
        label={trans('t_animals_stocked')}
        numericFormatProps={{ decimalScale: 0 }}
        inputContainerProps={{ w: 'auto' }}
        {...formContainerStyles}
      />

      <BaseFormNumberInput
        control={control}
        name='invoicedQuantity'
        required
        inputProps={{ ...inputStyles, textAlign: 'start', minW: '180px' }}
        formLabelProps={labelStyles}
        label={trans('t_invoiced_quantity_opt')}
        numericFormatProps={{ decimalScale: 0 }}
        inputContainerProps={{ w: 'auto' }}
        {...formContainerStyles}
      />

      <Flex {...(formContainerStyles as FlexProps)}>
        <Text fontSize='md' fontWeight={400}>
          {trans('t_stocking_density_ha')}
        </Text>
        <Text w='100px' textAlign='center' fontSize='md'>
          {stockingDensity ?? '-'}
        </Text>
      </Flex>

      <HatcherySelectContainer control={control} error={errors?.hatcheryName?.message} />

      <GeneticSelectContainer control={control} error={errors?.geneticName?.message} />

      <StockingWeightContainer control={control} errors={errors} />

      <AdminOrSupervisorWrapper>
        <BaseFormNumberInput
          control={control}
          name='stockingCostsMillar'
          required
          inputProps={{ ...inputStyles, minW: '180px' }}
          formLabelProps={labelStyles}
          label={trans('t_cost_millar')}
          numericFormatProps={{ decimalScale: 2, prefix: '$', fixedDecimalScale: true }}
          inputContainerProps={{ w: 'auto' }}
          {...formContainerStyles}
        />

        <BaseFormNumberInput
          name='overheadCostsPerHaPerDay'
          required={true}
          control={control}
          label={trans('t_overhead_cost_ha_day')}
          inputProps={{ ...inputStyles, textAlign: 'start', minW: '180px' }}
          numericFormatProps={{ decimalScale: 2, prefix: '$', fixedDecimalScale: true }}
          formLabelProps={labelStyles}
          inputContainerProps={{ w: 'auto' }}
          {...formContainerStyles}
        />
      </AdminOrSupervisorWrapper>
    </Flex>
  );
}

interface SelectContainerProps {
  control: Control<StockFormValues>;
  error?: string;
  watch?: UseFormWatch<StockFormValues>;
  errors?: FieldErrors<StockFormValues>;
}

function HatcherySelectContainer(props: SelectContainerProps) {
  const { control, error } = props;
  const { trans } = getTrans();

  const currentFarm = useAppSelector((state) => state.farm?.currentFarm);

  const { otherConfig } = currentFarm ?? {};

  const { hatchery } = otherConfig ?? {};
  const hatcheryOptions = hatchery?.map((item: { name: string }) => ({
    label: item.name,
    value: item.name
  }));

  return (
    <Box {...formContainerStyles}>
      <Text asChild>
        <label htmlFor='hatcheryName'>{trans('t_lab_source')}</label>
      </Text>

      <FormControlReactSelect
        isOptional
        id='hatcheryName'
        name='hatcheryName'
        control={control}
        placeholder=' '
        options={hatcheryOptions}
        error={error}
        isSearchable={false}
        labelProps={labelStyles}
        formControlProps={{ w: selectControlStyles.minWidth }}
        selectControlStyles={selectControlStyles}
      />
    </Box>
  );
}

function GeneticSelectContainer(props: SelectContainerProps) {
  const { control, error } = props;
  const { trans } = getTrans();

  const currentFarm = useAppSelector((state) => state.farm?.currentFarm);

  const { otherConfig } = currentFarm ?? {};

  const { genetic } = otherConfig ?? {};
  const geneticOptions = genetic?.map((item: { name: string }) => ({
    label: item.name,
    value: item.name
  }));

  return (
    <Box {...formContainerStyles}>
      <Text asChild>
        <label htmlFor='geneticName'>{trans('t_nauplii_source')}</label>
      </Text>

      <FormControlReactSelect
        isOptional
        id='geneticName'
        name='geneticName'
        control={control}
        placeholder=' '
        options={geneticOptions}
        error={error}
        isSearchable={false}
        labelProps={labelStyles}
        formControlProps={{ w: selectControlStyles.minWidth }}
        selectControlStyles={selectControlStyles}
      />
    </Box>
  );
}

function StockingWeightContainer(props: SelectContainerProps) {
  const { control, errors } = props;
  const { trans } = getTrans();
  return (
    <Flex {...(formContainerStyles as FlexProps)}>
      <Text>{trans('t_stocking_weight')}</Text>

      <Flex align='center' justify='flex-end'>
        <Box flex='0 0 100px'>
          <BaseFormNumberInput
            required
            control={control}
            name='seedingAverageWeight'
            inputProps={{ size: 'sm', rounded: 'base' }}
            numericFormatProps={{ decimalScale: 2, fixedDecimalScale: true }}
            error={errors?.seedingAverageWeight?.message}
            inputContainerProps={{ w: 'auto' }}
            {...formContainerStyles}
          />
        </Box>
        <Box flex='0 0 90px'>
          <FormControlReactSelect
            isOptional
            id='seedingAverageWeightUnit'
            name='seedingAverageWeightUnit'
            control={control}
            options={[
              { label: trans('t_gram_g'), value: 'g' },
              { label: trans('t_pls_per_g'), value: 'pls' }
            ]}
            isSearchable={false}
            labelProps={labelStyles}
            selectControlStyles={{ ...selectControlStyles, minWidth: 'auto' }}
            error={errors?.seedingAverageWeightUnit?.message}
          />
        </Box>
      </Flex>
    </Flex>
  );
}

function DestinationsPonds(props: SelectContainerProps) {
  const { control, errors, watch } = props;
  const { trans } = getTrans();
  const currentFarmPonds = useAppSelector((state) => state.farm?.currentFarmPonds);

  const { fields, append, remove } = useFieldArray({ control, name: 'destinationsPonds' });
  const destinationsPondsWatch = watch('destinationsPonds');

  const hasMoreThanOneItem = fields?.length > 1;
  const canAddDestinations = currentFarmPonds?.length > destinationsPondsWatch.length;

  return (
    <Flex {...(formContainerStyles as FlexProps)} alignItems='flex-start'>
      <Flex align='center' gap='sm-alt'>
        <Text>{trans('t_destination_ponds')}</Text>
        {canAddDestinations && (
          <BaseButton variant='link' onClick={() => append({ pondId: '' })}>
            <Flex align='center' justify='center' bg='bg.brandBlue' rounded='full' boxSize='16px' color='white' me='xs'>
              <AddIcon boxSize='14px' />
            </Flex>
          </BaseButton>
        )}
      </Flex>
      <Box>
        {fields.map((field, i) => {
          const options = currentFarmPonds?.reduce((acc, pond) => {
            const existingIdIndex = destinationsPondsWatch.findIndex((ele) => ele.pondId === pond._id);
            const isSelectedBefore = existingIdIndex >= 0 && existingIdIndex !== i;
            if (!isSelectedBefore) {
              acc.push({ label: pond.name, value: pond._id });
            }
            return acc;
          }, []);

          return (
            <Flex align='center' gap='sm' mb='sm' key={field.id}>
              <FormControlReactSelect
                isOptional
                id={`destinationsPonds.${i}.pondId`}
                name={`destinationsPonds.${i}.pondId`}
                control={control}
                options={options}
                isSearchable={false}
                error={errors?.destinationsPonds?.[i]?.pondId?.message}
                labelProps={labelStyles}
                formControlProps={{ w: selectControlStyles.minWidth }}
                selectControlStyles={selectControlStyles}
              />
              {hasMoreThanOneItem && (
                <BaseButton variant='link' size='sm' onClick={() => remove(i)} color='black'>
                  <RiDeleteBin2Fill />
                </BaseButton>
              )}
            </Flex>
          );
        })}
      </Box>
    </Flex>
  );
}
