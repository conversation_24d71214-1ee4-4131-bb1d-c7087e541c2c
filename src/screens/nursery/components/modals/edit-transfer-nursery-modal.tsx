import { Box, chakra, Flex, Table, TableCellProps, Text } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { BaseFormNumberInput } from '@components/form/base-form-number-input';
import { FormControlDateInput } from '@components/form/form-control-date-input';
import { FormControlReactSelect } from '@components/form/form-control-react-select';
import { yupResolver } from '@hookform/resolvers/yup';
import { getTrans } from '@i18n/get-trans';
import { numberTransform, useYupSchema, Yup, YupResolverType } from '@utils/yup';
import { DateTime } from 'luxon';
import { BaseSyntheticEvent, ReactNode, useMemo, useState } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import { useHarvestNurseryApi } from '@screens/nursery/hooks/use-harvest-nursery';
import { useAppSelector } from '@redux/hooks';
import { useRouter } from 'next/router';
import { NurseryPopulationType } from '@screens/nursery/hooks/nursery-population-fields';
import { parsePercent } from '@screens/monitoring/helpers/weight-distribution';
import { formatNumber } from '@utils/number';
import { WeightUnitType } from '@screens/nursery/helpers/transform-weight-with-unit';
import { NurserySummaryInfo, NurserySummaryRow } from '@screens/nursery/components/modals/nursery-summary-info';
import { RiDeleteBin2Fill } from 'react-icons/ri';
import { AddIcon } from '@screens/settings/icons/add-icon';
import { DialogBody, DialogContent, DialogHeader, DialogRoot, DialogTitle } from '@components/ui/dialog';
import { gramsInKg } from '@utils/constants';
import { AdminOrSupervisorWrapper } from '@components/permission-wrappers/admin-or-supervisor-wrapper';

enum HARVEST_TYPES {
  TOTAL_ANIMALS = 'totalAnimals',
  TOTAL_WEIGHT = 'totalWeight'
}

interface Props {
  nurseryId: string;
  nurseryName: string;
  nurserySize: number;
  population: NurseryPopulationType;
  children: ReactNode;
}

export function EditTransferNurseryModal(props: Props) {
  const { children, ...rest } = props;
  const [open, setOpen] = useState(false);

  return (
    <DialogRoot open={open} onOpenChange={(e) => setOpen(e.open)} size='cover' restoreFocus={false}>
      <Box onClick={() => setOpen(true)}>{children}</Box>

      <Form {...rest} />
    </DialogRoot>
  );
}

type FormValues = {
  destinations: {
    pondId: string;
    transferType: string;
    transferValue: number;
    avgWeight: number;
    totalWeight: number;
    avgWeightUnit: WeightUnitType;
  }[];
  transferDate: string;
  dryDays: number;
};

const transferTypeDefaultOption: FormValues['destinations'][0] = {
  pondId: undefined,
  transferType: HARVEST_TYPES.TOTAL_ANIMALS,
  transferValue: undefined,
  avgWeight: undefined,
  totalWeight: undefined,
  avgWeightUnit: 'g'
};

type FormProps = {
  nurseryId: string;
  nurseryName: string;
  nurserySize: number;
  population: NurseryPopulationType;
};

function Form(props: FormProps) {
  const { nurseryId, nurseryName, nurserySize, population } = props;
  const { stockedAt, seedingQuantity, harvest } = population ?? {};

  const ponds = useAppSelector((state) => state.farm?.currentFarmPonds);

  // Nursery ponds are permitted to transfer 150% of their seeding amount, based on Mikie's request.
  const maxNumberOfAnimalsToTransfer = seedingQuantity * 1.5;

  const { trans } = getTrans();
  const { reload } = useRouter();

  const lang = useAppSelector((state) => state.app.lang);

  const [totalCosts, setTotalCosts] = useState(0);
  const [{ isLoading }, harvestNursery] = useHarvestNurseryApi();

  const transferTypeOptions = [
    { label: trans('t_animals'), value: HARVEST_TYPES.TOTAL_ANIMALS },
    { label: trans('t_biomass_kg'), value: HARVEST_TYPES.TOTAL_WEIGHT }
  ];

  const { schema } = useYupSchema({
    destinations: Yup.array()
      .of(
        Yup.object().shape({
          pondId: Yup.string().label('t_destination').required(),
          transferType: Yup.string().label('t_select_input').required(),
          transferValue: Yup.number().when('transferType', {
            is: HARVEST_TYPES.TOTAL_ANIMALS,
            then: () => Yup.number().transform(numberTransform).required().label('t_animals'),
            otherwise: () => Yup.number().transform(numberTransform).nullable().label('t_animals')
          }),
          avgWeight: Yup.number().required().label('t_abw').transform(numberTransform),
          avgWeightUnit: Yup.string().required().label('t_unit'),
          totalWeight: Yup.number().when('transferType', {
            is: HARVEST_TYPES.TOTAL_WEIGHT,
            then: () => Yup.number().transform(numberTransform).required().label('t_biomass_kg'),
            otherwise: () => Yup.number().transform(numberTransform).nullable().label('t_biomass_kg')
          })
        })
      )
      .test((value, { createError, path }) => {
        let total = 0;
        value.forEach((ele, i) => {
          const { transferType, transferValue } = ele;

          if (transferType === HARVEST_TYPES.TOTAL_ANIMALS) {
            total += transferValue;
          } else if (transferType === HARVEST_TYPES.TOTAL_WEIGHT) {
            const { value } = getTotalAnimals(i);
            total += value;
          }
        });

        if (total >= maxNumberOfAnimalsToTransfer) {
          return createError({
            path,
            message: trans('t_total_transferred_animals_should_be_less_than', {
              value: formatNumber(maxNumberOfAnimalsToTransfer, { lang, fractionDigits: 0 })
            })
          });
        }

        return true;
      }),
    transferDate: Yup.string().required().label('t_transfer_date'),
    dryDays: Yup.number().transform(numberTransform).required().label('t_dry_days')
  });

  const {
    control,
    handleSubmit,
    formState: { errors },
    trigger,
    watch
  } = useForm<FormValues>({
    resolver: yupResolver(schema) as YupResolverType<FormValues>,
    defaultValues: {
      destinations: harvest.transfers?.map((transfer) => ({
        pondId: transfer.pondId,
        transferType: transfer.type,
        transferValue: transfer.value,
        avgWeight: transfer.avgWeightUnit === 'pls' ? 1 / transfer.avgWeight : transfer.avgWeight,
        avgWeightUnit: (transfer.avgWeightUnit as WeightUnitType) || 'g',
        totalWeight: transfer.totalWeight
      })),
      transferDate: harvest.date,
      dryDays: harvest.dryDays
    }
  });

  const { fields, append, remove } = useFieldArray({ control, name: 'destinations' });
  const destinationsWatch = watch('destinations');
  const transferDateWatch = watch('transferDate');
  const dryDaysWatch = watch('dryDays');

  const daysTillTransferDate = useMemo(() => {
    if (!transferDateWatch || !stockedAt) return;

    const transferDate = DateTime.fromJSDate(new Date(transferDateWatch));
    const stockedAtDate = DateTime.fromISO(stockedAt);
    return transferDate.startOf('day').diff(stockedAtDate.startOf('day'), 'days').days;
  }, [transferDateWatch, stockedAt]);

  const getWeightInGrams = ({ avgWeight, avgWeightUnit }: { avgWeight: number; avgWeightUnit: WeightUnitType }) =>
    avgWeightUnit === 'pls' ? 1 / avgWeight : avgWeight;

  const getTotalWeight = (i: number): { text: string; value: number } => {
    const { avgWeight, transferValue, avgWeightUnit } = destinationsWatch[i];
    if (typeof avgWeight != 'number' || typeof transferValue != 'number') return { text: '-', value: undefined };

    const avgWeightInG = getWeightInGrams({ avgWeight, avgWeightUnit });
    const totalWeight = (avgWeightInG * transferValue) / gramsInKg;
    return { text: `${totalWeight} ${trans('t_kg')}`, value: totalWeight };
  };

  const getTotalAnimals = (i: number): { text: string; value: number } => {
    const { avgWeight, totalWeight, avgWeightUnit } = destinationsWatch[i];
    if (typeof avgWeight != 'number' || typeof totalWeight != 'number') return { text: '-', value: undefined };

    const avgWeightInG = getWeightInGrams({ avgWeight, avgWeightUnit });

    const calculatedTotal = (totalWeight * gramsInKg) / avgWeightInG;
    return { text: `${formatNumber(calculatedTotal, { lang, fractionDigits: 0 })}`, value: calculatedTotal };
  };

  const onSubmit = handleSubmit((data: FormValues) => {
    const harvestAtDate = DateTime.fromJSDate(new Date(data.transferDate));

    const transfers = data.destinations.map((record, i) => {
      const { transferType, transferValue, avgWeight, pondId, totalWeight, avgWeightUnit } = record;

      const calculatedValues: Pick<FormValues['destinations'][0], 'totalWeight' | 'transferValue' | 'avgWeight'> = {
        totalWeight,
        transferValue,
        avgWeight: getWeightInGrams({ avgWeight, avgWeightUnit })
      };

      if (transferType === HARVEST_TYPES.TOTAL_ANIMALS) {
        calculatedValues.totalWeight = getTotalWeight(i).value;
      } else if (transferType === HARVEST_TYPES.TOTAL_WEIGHT) {
        calculatedValues.transferValue = getTotalAnimals(i).value;
      }

      const costsMillar =
        (totalCosts * (calculatedValues.transferValue / totalAnimalsForAllDestinations)) /
        (calculatedValues.transferValue / gramsInKg);

      return {
        pondId,
        type: transferType,
        value: calculatedValues.transferValue,
        avgWeight: calculatedValues.avgWeight ?? 0,
        totalWeight: calculatedValues.totalWeight ?? 0,
        costsMillar: costsMillar ? +formatNumber(costsMillar, { fractionDigits: 2, asNumber: true }) : 0,
        avgWeightUnit
      };
    });

    harvestNursery({
      params: {
        nurseryId,
        harvest: {
          dryDays: data.dryDays,
          date: harvestAtDate.toISO(),
          transfers
        }
      },
      successCallback: reload
    });
  });

  const getTotalAnimalsForAllDestinations = () => {
    let totalAnimals = 0;
    destinationsWatch.every((ele, i) => {
      const total = ele.transferType === HARVEST_TYPES.TOTAL_ANIMALS ? ele.transferValue : getTotalAnimals(i).value;

      if (typeof total !== 'number') {
        totalAnimals = undefined;
        return false;
      }

      totalAnimals += total;
      return true;
    });
    return { totalAnimals, survival: totalAnimals ? parsePercent(totalAnimals / seedingQuantity) : undefined };
  };
  const { totalAnimals: totalAnimalsForAllDestinations, survival } = getTotalAnimalsForAllDestinations();
  const triggerErrors = async () => {
    return await trigger();
  };

  const isOneField = fields?.length < 2;
  const canAddDestinations = ponds?.length > destinationsWatch.length;

  return (
    <DialogContent bg='bg.gray.medium' p='lg'>
      <chakra.form display='contents'>
        <DialogHeader p='0' my='lg'>
          <Flex align='center' justify='space-between' flex={1}>
            <DialogTitle>
              {trans('t_edit_transfer')} {nurseryName}
            </DialogTitle>

            <TransferConfirmationModal onConfirm={onSubmit} triggerErrors={triggerErrors}>
              <BaseButton loading={isLoading}>{trans('t_edit_transfer')}</BaseButton>
            </TransferConfirmationModal>
          </Flex>
        </DialogHeader>

        <DialogBody p='0'>
          <Box bg='white' p='md' mb='lg'>
            <Text fontSize='lg' fontWeight={600} mb='lg'>
              {trans('t_destination')}: {trans('t_stocking')}
            </Text>
            <Table.Root>
              <Table.Row>
                <Table.Header>
                  <Table.Row border='none'>
                    <TableHeaderCell title={trans('t_destination')} />
                    <TableHeaderCell title={trans('t_select_input')} />
                    <TableHeaderCell title={trans('t_animals')} />
                    <TableHeaderCell title={trans('t_abw')} />
                    <TableHeaderCell title={trans('t_biomass_kg')} />
                    <TableHeaderCell title={trans('t_animals_%')} />
                  </Table.Row>
                </Table.Header>

                <Table.Body>
                  {fields.map((field, i) => {
                    const options = ponds?.reduce((acc, pond) => {
                      const existingIdIndex = destinationsWatch.findIndex((ele) => ele.pondId === pond._id);
                      const isSelectedBefore = existingIdIndex >= 0 && existingIdIndex !== i;
                      if (!isSelectedBefore) {
                        acc.push({ label: pond.name, value: pond._id });
                      }
                      return acc;
                    }, []);

                    const isTotalAnimals = destinationsWatch[i]?.transferType === HARVEST_TYPES.TOTAL_ANIMALS;
                    const isTotalWeight = destinationsWatch[i]?.transferType === HARVEST_TYPES.TOTAL_WEIGHT;
                    const totalAnimalsValue = isTotalAnimals
                      ? destinationsWatch[i].transferValue
                      : getTotalAnimals(i).value;
                    return (
                      <Table.Row borderBottom='0.5px solid {colors.border.gray' key={field.id}>
                        <TableBodyCell>
                          <FormControlReactSelect
                            id={`destinations.${i}.pondId`}
                            name={`destinations.${i}.pondId`}
                            control={control}
                            options={options}
                            error={errors?.destinations?.[i]?.pondId?.message}
                            isSearchable={false}
                            selectControlStyles={{ minHeight: '32px' }}
                          />
                        </TableBodyCell>

                        <TableBodyCell>
                          <FormControlReactSelect
                            id={`destinations.${i}.transferType`}
                            name={`destinations.${i}.transferType`}
                            control={control}
                            options={transferTypeOptions}
                            error={errors?.destinations?.[i]?.transferType?.message}
                            isSearchable={false}
                            formControlProps={{ minW: '200px' }}
                            selectControlStyles={{ minHeight: '32px' }}
                          />
                        </TableBodyCell>

                        <TableBodyCell>
                          <Box w='120px'>
                            {isTotalAnimals ? (
                              <BaseFormNumberInput
                                name={`destinations.${i}.transferValue`}
                                control={control}
                                inputProps={{ size: 'sm', _disabled: { bg: 'none' } }}
                                numericFormatProps={{ decimalScale: 0 }}
                                error={errors?.destinations?.[i]?.transferValue?.message}
                              />
                            ) : (
                              <Text px='sm-alt' fontSize='sm'>
                                {getTotalAnimals(i).text}
                              </Text>
                            )}
                          </Box>
                        </TableBodyCell>

                        <TableBodyCell>
                          <Flex gap='sm'>
                            <BaseFormNumberInput
                              name={`destinations.${i}.avgWeight`}
                              control={control}
                              inputProps={{ size: 'sm' }}
                              numericFormatProps={{ decimalScale: 3 }}
                              error={errors?.destinations?.[i]?.avgWeight?.message}
                            />
                            <FormControlReactSelect
                              id={`destinations.${i}.avgWeightUnit`}
                              name={`destinations.${i}.avgWeightUnit`}
                              control={control}
                              options={[
                                { label: trans('t_gram_g'), value: 'g' },
                                { label: trans('t_pls_per_g'), value: 'pls' }
                              ]}
                              error={errors?.destinations?.[i]?.avgWeightUnit?.message}
                              isSearchable={false}
                              selectControlStyles={{ minHeight: '32px' }}
                            />
                          </Flex>
                        </TableBodyCell>

                        <TableBodyCell>
                          <Box w='120px'>
                            {isTotalWeight ? (
                              <BaseFormNumberInput
                                name={`destinations.${i}.totalWeight`}
                                control={control}
                                inputProps={{ size: 'sm' }}
                                numericFormatProps={{ decimalScale: 2 }}
                                error={errors?.destinations?.[i]?.totalWeight?.message}
                                inputRightElement={trans('t_kg')}
                              />
                            ) : (
                              <Text px='sm-alt' fontSize='sm'>
                                {getTotalWeight(i).text}
                              </Text>
                            )}
                          </Box>
                        </TableBodyCell>

                        <TableBodyCell>
                          <Text>
                            {totalAnimalsForAllDestinations
                              ? formatNumber(totalAnimalsValue / totalAnimalsForAllDestinations, {
                                  lang,
                                  fractionDigits: 0,
                                  isPercentage: true
                                })
                              : '-'}
                          </Text>
                        </TableBodyCell>

                        <TableBodyCell>
                          {!isOneField && (
                            <BaseButton variant='link' onClick={() => remove(i)} color='black'>
                              <RiDeleteBin2Fill />
                            </BaseButton>
                          )}
                        </TableBodyCell>
                      </Table.Row>
                    );
                  })}
                </Table.Body>
              </Table.Row>
            </Table.Root>

            {errors?.destinations?.root && (
              <Text color='error' mt='sm'>
                {errors.destinations.root.message}
              </Text>
            )}

            {canAddDestinations && <AddDestinationButton onClick={() => append(transferTypeDefaultOption)} />}
          </Box>

          <Box bg='white' p='md'>
            <Text fontSize='lg' fontWeight={600} mb='lg'>
              {trans('t_transfer_summary')}
            </Text>

            <NurserySummaryRow title={trans('t_transfer_date')}>
              <FormControlDateInput
                error={errors?.transferDate?.message}
                id='transferDate'
                control={control}
                name='transferDate'
                minDate={stockedAt}
                maxDate={DateTime.now().endOf('day').toJSDate()}
                placement='bottom'
                dateFormat='MMM dd, yyyy'
                placeholder={trans('t_harvest_date')}
                inputProps={{ size: 'sm' }}
              />
            </NurserySummaryRow>

            <NurserySummaryRow title={trans('t_end_of_cycle_survival')} midValue={survival ? `${survival}%` : '-'}>
              <Text>
                {totalAnimalsForAllDestinations
                  ? `${formatNumber(totalAnimalsForAllDestinations, { lang, fractionDigits: 0 })} / ${formatNumber(seedingQuantity, { lang, fractionDigits: 0 })}`
                  : '-'}
              </Text>
            </NurserySummaryRow>

            <NurserySummaryRow
              title={trans('t_cycle_length')}
              midValue={daysTillTransferDate ? `${daysTillTransferDate} ${trans('t_day_s')}` : '-'}
            >
              <BaseFormNumberInput
                name='dryDays'
                control={control}
                inputProps={{ size: 'sm', _disabled: { bg: 'none' } }}
                numericFormatProps={{ decimalScale: 0 }}
                error={errors?.dryDays?.message}
                inputRightElement={<Text fontSize='sm'>{trans('t_dry_days')}</Text>}
                inputRightElementProps={{ w: 'fit-content', pe: 'xs' }}
              />
            </NurserySummaryRow>
            <AdminOrSupervisorWrapper>
              <NurserySummaryInfo
                nurserySize={nurserySize}
                population={population}
                cycleDays={daysTillTransferDate}
                dryDays={dryDaysWatch}
                totalAnimalsForAllDestinations={totalAnimalsForAllDestinations}
                onTotalCostsChanges={(v) => setTotalCosts(v)}
              />
            </AdminOrSupervisorWrapper>
          </Box>
        </DialogBody>
      </chakra.form>
    </DialogContent>
  );
}

function AddDestinationButton({ onClick }: { onClick: () => void }) {
  const { trans } = getTrans();

  return (
    <Box>
      <BaseButton variant='link' _hover={{ textDecor: 'none' }} color='text.gray' my='md' onClick={onClick}>
        <Flex align='center' justify='center' bg='bg.brandBlue' rounded='full' boxSize='20px' color='white' me='xs'>
          <AddIcon boxSize='18px' />
        </Flex>
        <Text textDecor='none'>{trans('t_add_destination')}</Text>
      </BaseButton>
    </Box>
  );
}

function TableBodyCell(props: TableCellProps) {
  return <Table.Cell ps='0' py='sm-alt' pe='md' {...props} />;
}

function TableHeaderCell({ title }: { title: string }) {
  return (
    <Table.ColumnHeader textStyle='paragraph.200.light' border='none' p='0'>
      {title}
    </Table.ColumnHeader>
  );
}

function TransferConfirmationModal({
  children,
  onConfirm,
  triggerErrors
}: {
  children: ReactNode;
  onConfirm: (e?: BaseSyntheticEvent) => Promise<void>;
  triggerErrors: () => Promise<boolean>;
}) {
  const { trans } = getTrans();
  const [open, setOpen] = useState(false);
  const handleOpen = () => {
    triggerErrors().then((noErrors) => {
      if (!noErrors) return;
      setOpen(true);
    });
  };
  const handleClose = () => setOpen(false);
  return (
    <DialogRoot open={open} onOpenChange={(e) => (e.open ? handleOpen() : handleClose())} size='lg'>
      <Box onClick={handleOpen}>{children}</Box>

      <DialogContent rounded='2xl' shadow='elevation.400' bgColor='bg.gray.medium'>
        <DialogHeader>
          <DialogTitle textStyle='headline.300.heavy'>{trans('t_edit_transfer_confirmation')}</DialogTitle>
        </DialogHeader>
        <DialogBody py='lg'>
          <Box mb='2lg'>
            <Text size='label100' color='text.gray.weak' mb='lg' lineHeight={1.4}>
              {trans('t_edit_transfer_confirmation_msg')}
            </Text>

            <Text size='label100' color='text.gray.weak'>
              {trans('t_transfer_confirmation_question')}
            </Text>
          </Box>

          <Flex justify='flex-end' gap='sm-alt'>
            <BaseButton
              onClick={() => {
                onConfirm();
                handleClose();
              }}
            >
              {trans('t_edit_transfer')}
            </BaseButton>
          </Flex>
        </DialogBody>
      </DialogContent>
    </DialogRoot>
  );
}
