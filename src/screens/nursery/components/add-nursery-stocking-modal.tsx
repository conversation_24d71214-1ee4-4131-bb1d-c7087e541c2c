import { Box, BoxProps, chakra, Flex, FlexProps, Text, useDisclosure } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { BaseFormNumberInput } from '@components/form/base-form-number-input';
import { FormControlDateInput } from '@components/form/form-control-date-input';
import { yupResolver } from '@hookform/resolvers/yup';
import { useAnalytics } from '@hooks/use-analytics';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { formatNumber, isNumber } from '@utils/number';
import { actionsName } from '@utils/segment';
import { numberTransform, useYupSchema, Yup, YupResolverType } from '@utils/yup';
import { DateTime } from 'luxon';
import { ReactNode } from 'react';
import { Control, FieldErrors, useFieldArray, useForm, UseFormWatch } from 'react-hook-form';
import { FormControlReactSelect } from '@components/form/form-control-react-select';
import { labelStyles, selectControlStyles } from '@screens/pond/components/modals/helpers/chakra-form-styles';
import { useStockNurseryApi } from '@screens/nursery/hooks/use-stock-nursery-api';
import { useRouter } from 'next/router';
import { AdminOrSupervisorWrapper } from '@components/permission-wrappers/admin-or-supervisor-wrapper';
import { PlusFilled } from '@icons/plus/plus-filled';
import { DialogBody, DialogContent, DialogFooter, DialogHeader, DialogRoot } from '@components/ui/dialog';
import { Tooltip } from '@components/ui/tooltip';
import { InfoFilledIcon } from '@icons/info/info-filled-icon';
import { sqPerHectare } from '@utils/constants';
import { TrashcanXIcon } from '@icons/trash-can/trash-can-x-icon';

const dateToBeTwoYearsLess = new Date();
dateToBeTwoYearsLess.setFullYear(dateToBeTwoYearsLess.getFullYear() - 2);
const destinationsPondsDefaultValue = [{ pondId: '' }];

interface AddStockingModalProps {
  nurseryId: string;
  farmEid: string;
  nurseryEid: number;
  nurseryName: string;
  nurserySize: number;
  children: ReactNode;
  containerProps?: BoxProps;
}

type StockFormValues = {
  cycle?: number;
  stockedAt?: Date;
  seedingAverageWeight?: number;
  seedingAverageWeightUnit: string;
  seedingQuantity?: number;
  dryDaysBeforeStocking?: number;
  stockingCostsMillar?: number;
  hatcheryName?: string;
  geneticName?: string;
  destinationsPonds: { pondId: string }[];
  invoicedQuantity?: number;
  overheadCostsPerHaPerDay?: number;
};

interface SelectContainerProps {
  control: Control<StockFormValues>;
  error?: string;
  watch?: UseFormWatch<StockFormValues>;
  errors?: FieldErrors<StockFormValues>;
}

export function AddNurseryStockingModal(props: AddStockingModalProps) {
  const { children, nurseryId, nurseryName, nurserySize, containerProps = {} } = props;

  const { trans } = getTrans();
  const { reload } = useRouter();

  /// Hooks

  const { trackAction } = useAnalytics();
  const { onOpen, onClose, open } = useDisclosure();
  const [{ isLoading: isCreating }, stockNursery] = useStockNurseryApi();

  /// Redux
  const lang = useAppSelector((state) => state.app.lang);
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);

  /// FORM DATA
  const { stockingConfig, timezone, growthTarget } = currentFarm ?? {};
  const { costOverhead } = growthTarget ?? {};

  const { summary } = stockingConfig ?? {};
  const { growthDensity, avgStockingWeight } = summary ?? {};

  const totalSQ = nurserySize * sqPerHectare;

  const defaultValues: StockFormValues = {
    cycle: null,
    stockingCostsMillar: null,
    stockedAt: DateTime.local().toJSDate(),
    seedingAverageWeight: avgStockingWeight,
    seedingAverageWeightUnit: 'pls',
    seedingQuantity: growthDensity ? growthDensity * totalSQ : null,
    destinationsPonds: destinationsPondsDefaultValue,
    overheadCostsPerHaPerDay: costOverhead
  };

  const defaultMinValue = 0;
  const defaultMaxCostMillar = 50000;

  const { schema } = useYupSchema({
    cycle: Yup.string().required(trans('t_required')).label('t_cycle_number'),
    stockedAt: Yup.date()
      .transform(numberTransform)
      .min(dateToBeTwoYearsLess, trans('t_min_allowed_stocked'))
      .required(trans('t_required'))
      .label('t_stocked_at'),
    seedingAverageWeight: Yup.number()
      .transform(numberTransform)
      .positive()
      .nullable()
      .required(trans('t_required'))
      .label('t_stocking_average_weight'),
    seedingAverageWeightUnit: Yup.string().required().label('t_average_wight_unit'),

    seedingQuantity: Yup.number()
      .transform(numberTransform)
      .positive()
      .nullable()
      .required(trans('t_required'))
      .label('t_animals_stocked'),
    stockingCostsMillar: Yup.number()
      .transform(numberTransform)
      .min(defaultMinValue, trans('t_min_x', { min: defaultMinValue }))
      .max(defaultMaxCostMillar, trans('t_max_x', { max: defaultMaxCostMillar }))
      .nullable()
      .label('t_cost_millar'),
    hatcheryName: Yup.string().nullable().label('t_hatchery'),
    geneticName: Yup.string().nullable().label('t_genetic'),
    invoicedQuantity: Yup.number().nonNullable().label('t_invoiced_quantity_opt'),
    destinationsPonds: Yup.array().of(
      Yup.object().shape({
        pondId: Yup.string().required().label('t_pond')
      })
    ),
    overheadCostsPerHaPerDay: Yup.number()
      .transform(numberTransform)
      .positive()
      .nullable()
      .label('t_overhead_cost_ha_day')
  });

  const {
    handleSubmit,
    control,
    formState: { errors },
    watch,
    reset
  } = useForm<StockFormValues>({
    resolver: yupResolver(schema) as YupResolverType<StockFormValues>,
    defaultValues
  });

  /// Handlers
  const handleReload = () => {
    reload();
  };

  const handleModalOpen = () => {
    trackAction(actionsName.stockPondClicked, { nurseryName, nurseryId }).then();
    onOpen();
  };

  const handleModalClose = () => {
    reset(defaultValues);
    onClose();
  };

  const onSubmit = (data: StockFormValues) => {
    const { cycle, seedingQuantity, seedingAverageWeightUnit, overheadCostsPerHaPerDay } = data;
    const { destinationsPonds, seedingAverageWeight: seedingAverageWeightFromData, ...rest } = data;

    const stockingAreaDensity = seedingQuantity ? seedingQuantity / nurserySize : null;
    const seedingAverageWeight =
      seedingAverageWeightUnit === 'g' ? seedingAverageWeightFromData : 1 / seedingAverageWeightFromData;

    trackAction(actionsName.nurseryStocked, { nurseryName, nurseryId }).then();

    stockNursery({
      params: {
        nurseryId,
        ...rest,
        seedingAverageWeight,
        stockedType: 'direct',
        stockingAreaDensity,
        cycle: cycle.toString(),
        stockedAtTimezone: timezone,
        pondIds: destinationsPonds.map((ele) => ele.pondId),
        overheadCostsPerHaPerDay
      },
      successCallback: () => {
        onClose();
        handleReload();
      }
    });
  };

  return (
    <DialogRoot
      open={open}
      onOpenChange={(e) => (e.open ? handleModalOpen() : handleModalClose())}
      size='lg'
      restoreFocus={false}
    >
      <Box
        {...containerProps}
        onClick={(e) => {
          e.stopPropagation();
          handleModalOpen();
        }}
      >
        {children}
      </Box>

      <DialogContent data-cy='stock-pond-modal' rounded='2xl' bgColor='bg.gray.medium' p='lg'>
        <chakra.form display='contents' onSubmit={handleSubmit(onSubmit)} noValidate>
          <DialogHeader display='flex' p={0} gap='2xs' mb='2lg'>
            <Text size='label100'>{trans('t_stock_nursery')}</Text>
            <Text size='label100'>{nurseryName}</Text>
            <Text size='label100'>
              ({formatNumber(nurserySize, { lang, fractionDigits: 2 })} {trans('t_ha')})
            </Text>
          </DialogHeader>

          <DialogBody p={0}>
            <StockingFormFields errors={errors} control={control} watch={watch} nurserySize={nurserySize} />
          </DialogBody>
          <DialogFooter gap='md' p={0} mt='2lg'>
            <BaseButton
              type='submit'
              loading={isCreating}
              analyticsId={actionsName.addStockingSaveClicked}
              analyticsData={{ nurseryId, nurseryName }}
              data-cy='save-btn'
            >
              {trans('t_save_changes')}
            </BaseButton>
          </DialogFooter>
        </chakra.form>
      </DialogContent>
    </DialogRoot>
  );
}

interface StockingFormFields {
  control: Control<StockFormValues>;
  errors: FieldErrors<StockFormValues>;
  watch: UseFormWatch<StockFormValues>;
  nurserySize: number;
}

function StockingFormFields(props: StockingFormFields) {
  const { control, errors, watch, nurserySize } = props;
  const { trans } = getTrans();
  const lang = useAppSelector((state) => state.app.lang);
  const watchedSeedingQuantity = watch('seedingQuantity');

  return (
    <Flex direction='column' gap='sm-alt' bg='white' p='md' rounded='2xl'>
      <FormOneRow label={trans('t_cycle_number')} isInvalid={!!errors?.cycle}>
        <BaseFormNumberInput
          name='cycle'
          required
          control={control}
          numericFormatProps={{ decimalScale: 0 }}
          inputProps={{ maxW: '124px' }}
        />
      </FormOneRow>
      <FormOneRow label={trans('t_stocking_date')} isInvalid={!!errors?.stockedAt}>
        <FormControlDateInput
          id='stockedAt'
          name='stockedAt'
          error={errors?.stockedAt?.message}
          control={control}
          maxW='175px'
          maxDate={new Date()}
          dateFormat='MMM dd, yyyy'
          placeholder={trans('t_select')}
          placement='left-end'
        />
      </FormOneRow>

      <DestinationsPonds control={control} errors={errors} watch={watch} />

      <FormOneRow label={trans('t_type_of_stocking')}>
        <Text size='label200'>{trans('t_direct')}</Text>
      </FormOneRow>

      <FormOneRow label={trans('t_animals_stocked')} isInvalid={!!errors?.seedingQuantity}>
        <BaseFormNumberInput
          control={control}
          name='seedingQuantity'
          required
          numericFormatProps={{ decimalScale: 0 }}
          inputProps={{ maxW: '124px' }}
        />
      </FormOneRow>

      <FormOneRow label={trans('t_invoiced_quantity_opt')} infoToolTip={trans('t_quantity_from_hatcheries')}>
        <BaseFormNumberInput
          control={control}
          name='invoicedQuantity'
          required
          numericFormatProps={{ decimalScale: 0 }}
          inputProps={{ maxW: '124px' }}
        />
      </FormOneRow>

      <FormOneRow label={trans('t_stocking_density_ha')}>
        <Text w='180px' textAlign='right' size='label200'>
          {isNumber(watchedSeedingQuantity)
            ? trans('t_count_per_ha', {
                count: formatNumber(watchedSeedingQuantity / nurserySize, { lang, fractionDigits: 0 })
              })
            : '-'}
        </Text>
      </FormOneRow>

      <HatcherySelectContainer control={control} error={errors?.hatcheryName?.message} />

      <GeneticSelectContainer control={control} error={errors?.geneticName?.message} />

      <StockingWeightContainer control={control} errors={errors} />
      <AdminOrSupervisorWrapper>
        <FormOneRow label={trans('t_cost_millar')} borderBottom='none'>
          <BaseFormNumberInput
            control={control}
            name='stockingCostsMillar'
            required
            inputProps={{ maxW: '124px' }}
            numericFormatProps={{ decimalScale: 2, prefix: '$', fixedDecimalScale: true }}
          />
        </FormOneRow>

        <FormOneRow label={trans('t_overhead_cost_ha_day')} borderBottom='none'>
          <BaseFormNumberInput
            control={control}
            name='overheadCostsPerHaPerDay'
            required
            inputProps={{ maxW: '124px' }}
            numericFormatProps={{ decimalScale: 2, prefix: '$', fixedDecimalScale: true }}
          />
        </FormOneRow>
      </AdminOrSupervisorWrapper>
    </Flex>
  );
}

function HatcherySelectContainer(props: SelectContainerProps) {
  const { control, error } = props;
  const { trans } = getTrans();

  const currentFarm = useAppSelector((state) => state.farm?.currentFarm);

  const { otherConfig } = currentFarm ?? {};

  const { hatchery } = otherConfig ?? {};
  const hatcheryOptions = hatchery?.map((item: { name: string }) => ({
    label: item.name,
    value: item.name
  }));

  return (
    <FormOneRow label={trans('t_lab_source')} isInvalid={!!error}>
      <FormControlReactSelect
        isOptional
        id='hatcheryName'
        name='hatcheryName'
        control={control}
        placeholder={trans('t_select')}
        options={hatcheryOptions}
        error={error}
        isSearchable={false}
        selectControlStyles={{ minHeight: '32px' }}
        formControlProps={{ w: selectControlStyles.minWidth }}
      />
    </FormOneRow>
  );
}

function GeneticSelectContainer(props: SelectContainerProps) {
  const { control, error } = props;
  const { trans } = getTrans();

  const currentFarm = useAppSelector((state) => state.farm?.currentFarm);

  const { otherConfig } = currentFarm ?? {};

  const { genetic } = otherConfig ?? {};
  const geneticOptions = genetic?.map((item: { name: string }) => ({
    label: item.name,
    value: item.name
  }));

  return (
    <FormOneRow label={trans('t_nauplii_source')} isInvalid={!!error}>
      <FormControlReactSelect
        isOptional
        id='geneticName'
        name='geneticName'
        control={control}
        placeholder={trans('t_select')}
        options={geneticOptions}
        error={error}
        isSearchable={false}
        labelProps={labelStyles}
        selectControlStyles={{ minHeight: '32px' }}
        formControlProps={{ w: selectControlStyles.minWidth }}
      />
    </FormOneRow>
  );
}

function StockingWeightContainer(props: SelectContainerProps) {
  const { control, errors } = props;
  const { trans } = getTrans();
  return (
    <FormOneRow label={trans('t_stocking_weight')}>
      <Flex align='center' justify='flex-end'>
        <Box flex='0 0 80px' mr='sm'>
          <BaseFormNumberInput
            required
            control={control}
            name='seedingAverageWeight'
            numericFormatProps={{ decimalScale: 2, fixedDecimalScale: true }}
            inputProps={{ placeholder: trans('t_enter') }}
            error={errors?.seedingAverageWeight?.message}
          />
        </Box>
        <Box flex='0 0 90px'>
          <FormControlReactSelect
            isOptional
            id='seedingAverageWeightUnit'
            name='seedingAverageWeightUnit'
            control={control}
            options={[
              { label: trans('t_gram_g'), value: 'g' },
              { label: trans('t_pls_per_g'), value: 'pls' }
            ]}
            isSearchable={false}
            selectControlStyles={{ minWidth: 'auto', minHeight: '32px' }}
            error={errors?.seedingAverageWeightUnit?.message}
          />
        </Box>
      </Flex>
    </FormOneRow>
  );
}

function DestinationsPonds(props: SelectContainerProps) {
  const { control, errors, watch } = props;
  const { trans } = getTrans();
  const currentFarmPonds = useAppSelector((state) => state.farm?.currentFarmPonds);

  const { fields, append, remove } = useFieldArray({ control, name: 'destinationsPonds' });
  const destinationsPondsWatch = watch('destinationsPonds');

  const hasMoreThanOneItem = fields?.length > 1;
  const canAddDestinations = currentFarmPonds?.length > destinationsPondsWatch.length;

  return (
    <FormOneRow
      alignItems='flex-start'
      label={
        <Flex align='center' gap='sm-alt'>
          <Text>{trans('t_destination_ponds')}</Text>
          {canAddDestinations && (
            <BaseButton variant='link' onClick={() => append(destinationsPondsDefaultValue[0])}>
              <PlusFilled me='xs' color='white' bgColor='bg.brandBlue' />
            </BaseButton>
          )}
        </Flex>
      }
    >
      <>
        {fields.map((field, i) => {
          const options = currentFarmPonds
            ?.slice()
            ?.sort((a, b) => a.name.localeCompare(b.name))
            ?.reduce((acc, pond) => {
              const existingIdIndex = destinationsPondsWatch.findIndex((ele) => ele.pondId === pond._id);
              const isSelectedBefore = existingIdIndex >= 0 && existingIdIndex !== i;

              if (!isSelectedBefore) {
                acc.push({ label: pond.name, value: pond._id });
              }
              return acc;
            }, []);

          return (
            <Flex align='center' gap='xs' mb='xs' key={field.id}>
              <FormControlReactSelect
                isOptional
                id={`destinationsPonds.${i}.pondId`}
                name={`destinationsPonds.${i}.pondId`}
                control={control}
                options={options}
                isSearchable={false}
                placeholder={trans('t_select')}
                selectControlStyles={{ minWidth: '175px', minHeight: '32px' }}
                error={errors?.destinationsPonds?.[i]?.pondId?.message}
              />
              {hasMoreThanOneItem && (
                <BaseButton variant='link' size='sm' onClick={() => remove(i)} color='black'>
                  <TrashcanXIcon />
                </BaseButton>
              )}
            </Flex>
          );
        })}
      </>
    </FormOneRow>
  );
}

interface FormOneRowProps extends FlexProps {
  isInvalid?: boolean;
  label: string | ReactNode;
  subTitle?: string;
  infoToolTip?: string;
}

function FormOneRow(props: FormOneRowProps) {
  const { isInvalid, label, children, subTitle, infoToolTip, ...rest } = props;
  const { trans } = getTrans();
  const isLabelString = typeof label === 'string';
  return (
    <Flex
      pb='sm-alt'
      alignItems='center'
      borderBottom='0.5px solid'
      borderColor='border.gray.weak'
      justify='space-between'
      align={subTitle || isInvalid ? 'flex-start' : 'center'}
      {...rest}
    >
      <Box>
        <Flex align='center' gap='sm-alt'>
          {isLabelString && <Text size='label200'>{label}</Text>}
          {!isLabelString && label}

          {infoToolTip && (
            <Tooltip content={trans('t_quantity_from_hatcheries')}>
              <InfoFilledIcon />
            </Tooltip>
          )}
        </Flex>
        {subTitle && (
          <Text size='label300' maxW='201px' mt='sm-alt' color='text.gray.weak'>
            {subTitle}
          </Text>
        )}
      </Box>

      <Box>{children}</Box>
    </Flex>
  );
}
