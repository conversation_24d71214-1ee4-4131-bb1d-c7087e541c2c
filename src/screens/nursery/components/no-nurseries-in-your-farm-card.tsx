import { Flex, FlexProps, Text } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { actionsName } from '@utils/segment';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { AddEditNurseryModal } from '@screens/pond/components/modals/add-edit-nursery-modal';

export function NoNurseriesInYourFarmCard(props: FlexProps) {
  const { trans } = getTrans();

  const { currentFarm } = useAppSelector((state) => state.farm);
  const { _id: farmId } = currentFarm ?? {};

  return (
    <Flex
      p='md'
      mt='sm-alt'
      gap='sm-alt'
      maxW='467px'
      rounded='xl'
      direction='column'
      bgColor='white'
      {...props}
      data-cy='ponds-list-no-data'
    >
      <Text fontWeight={500} fontSize='lg'>
        {trans('t_no_nurseries_in_your_farm')}
      </Text>
      <Text fontWeight={400} fontSize='md'>
        {trans('t_create_a_new_nursery')}
      </Text>
      <AddEditNurseryModal farmId={farmId}>
        <BaseButton
          px='xs'
          size='sm'
          minW='90px'
          fontSize='md'
          fontWeight={500}
          data-cy='create-new-nursery-btn'
          analyticsId={actionsName.createNewNurseryClicked}
        >
          {trans('t_create_nursery_pond')}
        </BaseButton>
      </AddEditNurseryModal>
    </Flex>
  );
}
