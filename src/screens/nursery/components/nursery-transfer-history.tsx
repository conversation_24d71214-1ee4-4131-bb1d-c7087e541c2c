import { Table, Text } from '@chakra-ui/react';
import { EmptyState } from '@components/errors/empty-state';
import { getTrans } from '@i18n/get-trans';
import { NurseryPopulationType } from '@screens/nursery/hooks/nursery-population-fields';
import { NurseryPopulation } from '@xpertsea/module-farm-sdk';
import { useAppSelector } from '@redux/hooks';
import keyBy from 'lodash/keyBy';
import { DateTime } from 'luxon';
import { formatNumber } from '@utils/number';
import { CurrentFarmPonds } from '@redux/farm/set-current-farm-ponds';
import { transformWeightWithUnit } from '@screens/nursery/helpers/transform-weight-with-unit';
import { AdminOrSupervisorWrapper } from '@components/permission-wrappers/admin-or-supervisor-wrapper';
import { DeepPartial } from 'react-hook-form';
import { TableContainer } from '@components/ui/table-container';

interface Props {
  population: NurseryPopulationType;
}

type TableDataType = {
  harvestDate: string;
  transferPercentage: number;
  transferredTo: string;
  survivalRate: number;
  numberOfAnimalsTransferred: number;
  costsMillar: number;
  avgWeight: number;
  avgWeightUnit: string;
};

const getTransfersPerPopulation = ({
  population,
  pondsHashmap
}: {
  population: DeepPartial<NurseryPopulation>;
  pondsHashmap: Record<string, CurrentFarmPonds[0]>;
}) => {
  if (!population?.harvest?.transfers?.length) return [];

  const { harvest } = population;
  const { date: harvestDate, transfers } = harvest ?? {};

  const { totalAnimals } = transfers.reduce(
    (acc, ele) => {
      acc.totalAnimals += ele.value || 0;
      return acc;
    },
    { totalAnimals: 0 }
  );

  return transfers.map(({ value: numberOfAnimalsTransferred, pondId, costsMillar, avgWeight, avgWeightUnit }) => ({
    harvestDate,
    costsMillar,
    avgWeight,
    numberOfAnimalsTransferred,
    transferPercentage: numberOfAnimalsTransferred / totalAnimals,
    transferredTo: pondsHashmap[pondId]?.name,
    survivalRate: totalAnimals / population.seedingQuantity,
    avgWeightUnit
  }));
};

export function NurseryTransferHistory(props: Props) {
  const { population } = props;
  const { trans } = getTrans();
  const { currentFarmPonds } = useAppSelector((state) => state.farm);

  const tableData = getTransfersPerPopulation({ population, pondsHashmap: keyBy(currentFarmPonds, '_id') });

  return (
    <>
      <Text fontSize='lg' fontWeight={600} mb='md'>
        {trans('t_transfer_history')}
      </Text>

      {!tableData?.length ? (
        <EmptyState minH='100px' bgColor='white' />
      ) : (
        <TransferHistoryTable tableData={tableData} />
      )}
    </>
  );
}

function TransferHistoryTable({ tableData }: { tableData: TableDataType[] }) {
  const { trans } = getTrans();

  const lang = useAppSelector((state) => state.app.lang);

  return (
    <TableContainer>
      <Table.Root>
        <Table.Header>
          <Table.Row border='none'>
            <TableHeaderCell title={trans('t_transfer_date')} />
            <TableHeaderCell title={trans('t_animals_%')} />
            <TableHeaderCell title={trans('t_destination')} />
            <TableHeaderCell title={trans('t_survival')} />
            <TableHeaderCell title={trans('t_animals')} />
            <AdminOrSupervisorWrapper>
              <TableHeaderCell title={trans('t_cost_millar')} />
            </AdminOrSupervisorWrapper>
            <TableHeaderCell title={trans('t_abw')} />
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {tableData.map((row, i) => {
            const isLast = tableData.length - 1 === i;
            const {
              harvestDate,
              transferPercentage,
              transferredTo,
              survivalRate,
              numberOfAnimalsTransferred,
              costsMillar,
              avgWeight,
              avgWeightUnit
            } = row;

            const { weightString } = transformWeightWithUnit({ lang, weightInGrams: avgWeight, unit: avgWeightUnit });

            return (
              <Table.Row borderBottom='0.5px solid' borderBottomColor={isLast ? 'transparent' : 'border.gray'} key={i}>
                <Table.Cell textAlign='center' border='none'>
                  {harvestDate ? DateTime.fromISO(harvestDate).toFormat('LLL dd, yyyy', { locale: lang }) : ''}
                </Table.Cell>
                <Table.Cell textAlign='center' border='none'>
                  {transferPercentage
                    ? formatNumber(transferPercentage, { lang, fractionDigits: 0, isPercentage: true })
                    : '-'}
                </Table.Cell>
                <Table.Cell textAlign='center' border='none'>
                  {transferredTo || '-'}
                </Table.Cell>
                <Table.Cell textAlign='center' border='none'>
                  {survivalRate ? formatNumber(survivalRate, { lang, fractionDigits: 0, isPercentage: true }) : '-'}
                </Table.Cell>
                <Table.Cell textAlign='center' border='none'>
                  {numberOfAnimalsTransferred
                    ? formatNumber(numberOfAnimalsTransferred, { lang, fractionDigits: 0 })
                    : '-'}
                </Table.Cell>
                <AdminOrSupervisorWrapper>
                  <Table.Cell textAlign='center' border='none'>
                    {costsMillar ? formatNumber(costsMillar, { lang, fractionDigits: 2 }) : '-'}
                  </Table.Cell>
                </AdminOrSupervisorWrapper>
                <Table.Cell textAlign='center' border='none'>
                  {weightString}
                </Table.Cell>
              </Table.Row>
            );
          })}
        </Table.Body>
      </Table.Root>
    </TableContainer>
  );
}

function TableHeaderCell({ title }: { title: string }) {
  return (
    <Table.ColumnHeader border='none' textAlign='center' p='0'>
      <Text bgColor='gray.100' py='xs' borderRadius='4px' mx='2xs' fontSize='md' fontWeight={400} lineHeight='16px'>
        {title}
      </Text>
    </Table.ColumnHeader>
  );
}
