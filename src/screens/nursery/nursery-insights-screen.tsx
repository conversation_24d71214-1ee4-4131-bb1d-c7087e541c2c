import { useSetFarmBookRefererInRedux } from '@components/farm-book/hooks/use-set-farm-book-referer-in-redux';
import { Flex, SimpleGrid, Skeleton, Text } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { useEffect, useMemo, useState } from 'react';
import { OverlayLoader } from '@components/loaders/overlay-loader';
import { FarmSelector } from '@screens/farm/components/farm-selector';
import { NoNurseriesInYourFarmCard } from '@screens/nursery/components/no-nurseries-in-your-farm-card';
import { useListNurseriesApi } from '@screens/nursery/hooks/use-list-nurseries-api';
import { NurseryCard } from '@screens/nursery/components/nursery-card';
import { actionsName } from '@utils/segment';
import { MoreHoriz } from '@icons/more-horiz/more-horiz';
import { useAnalytics } from '@hooks/use-analytics';
import { AddEditNurseryModal } from '@screens/pond/components/modals/add-edit-nursery-modal';
import keyBy from 'lodash/keyBy';
import { MenuButton, MenuContent, MenuItem, MenuRoot } from '@components/ui/menu';
import { CloseFilled } from '@icons/close/close-filled';
import { BaseSearchInput } from '@components/base/base-search-input';
import {
  NurseriesFilterPopover,
  NurseriesFilterPopoverFormType
} from '@screens/nursery/components/nursery-filter-popover/nurseries-filter-popover';
import { applyCycleDaysFilter } from '@screens/nursery/components/nursery-filter-popover/nurseries-filter-helpers';
import { NurseriesFilterTags } from '@screens/nursery/components/nursery-filter-popover/nurseries-filter-tags';

const NURSERIES_FILTERS = 'NurseriesFilters';

export function NurseryInsightsScreen() {
  useSetFarmBookRefererInRedux('farmSummary');

  const { trans } = getTrans();
  const { trackAction } = useAnalytics();

  const [{ isLoading: isLoadingNurseries, data: nurseriesData, isFinishedOnce }, listNurseries] = useListNurseriesApi();

  const { currentFarm, currentFarmSlug, currentFarmPonds } = useAppSelector((state) => state.farm);
  const { _id: farmId } = currentFarm ?? {};

  const pondsHashmap = currentFarmPonds?.length ? keyBy(currentFarmPonds, '_id') : {};

  const [filters, setFilters] = useState<Partial<NurseriesFilterPopoverFormType>>({});

  const updateFilter = (data: Partial<NurseriesFilterPopoverFormType>) => {
    setFilters(data);
    localStorage.setItem(NURSERIES_FILTERS, JSON.stringify(data ?? {}));
  };

  useEffect(() => {
    const savedFilters = localStorage.getItem(NURSERIES_FILTERS);

    if (savedFilters) {
      setFilters(JSON.parse(savedFilters ?? ''));
    } else {
      localStorage.setItem(NURSERIES_FILTERS, JSON.stringify(filters ?? {}));
    }
  }, []);

  useEffect(() => {
    if (!farmId) return;

    listNurseries({
      params: {
        filter: {
          farmId: [farmId]
        }
      }
    });
  }, [farmId]);

  const [search, setSearch] = useState<string>();

  const filteredNurseries = useMemo(() => {
    if (!nurseriesData) return [];

    let filteredPondsBySearch = nurseriesData;

    if (search?.length) {
      filteredPondsBySearch = nurseriesData.filter((nursery) =>
        nursery.name?.toLowerCase()?.includes(search.toLowerCase())
      );
    }

    if (filters) {
      const { flagged: flaggedFilter, cycleDays: cycleDaysFilter } = filters;

      filteredPondsBySearch = filteredPondsBySearch.filter((pond) => {
        const { population } = pond;
        const { metadata } = population ?? {};

        const isMatchingFlagFilter = !flaggedFilter || metadata?.isFlagged;

        const isInCycleDaysRange = applyCycleDaysFilter({
          farmTimezone: currentFarm?.timezone,
          population,
          cycleDaysFilter
        });

        return isMatchingFlagFilter && isInCycleDaysRange;
      });
    }

    return filteredPondsBySearch;
  }, [nurseriesData, search, filters]);

  if (isLoadingNurseries || !isFinishedOnce) return <PageLoader />;

  if (!nurseriesData?.length) {
    return (
      <Flex
        p='md'
        gap='md-alt'
        direction='column'
        id='nursery-list'
        data-cy='nursery-list-data'
        bgColor='bg.gray.medium'
      >
        <FarmSelector />
        <NoNurseriesInYourFarmCard id='nursery-list' />
      </Flex>
    );
  }

  return (
    <Flex p='md' gap='md-alt' direction='column' id='nursery-list' data-cy='nursery-list-data' bgColor='bg.gray.medium'>
      <OverlayLoader isLoading={isLoadingNurseries} />
      <FarmSelector />

      <Flex align='center' justify='space-between' gap='sm-alt' flexWrap='wrap'>
        <BaseSearchInput
          value={search}
          rightElement={search && <CloseFilled cursor='pointer' hasBackground={false} onClick={() => setSearch('')} />}
          onChange={(event) => setSearch(event.target.value)}
          data-cy='nurseries-search-input'
        />

        <Flex align='center' gap='sm-alt' flexWrap='wrap' w={{ base: '100%', md: 'auto' }}>
          <NurseriesFilterPopover defaultValues={filters as NurseriesFilterPopoverFormType} onApply={updateFilter} />

          <MenuRoot closeOnSelect={false}>
            <MenuButton
              h='24px'
              selectVariant='secondary'
              size='sm'
              px={0}
              ms='auto'
              _hover={{ opacity: 0.8 }}
              analyticsId={actionsName.customizeViewClicked}
              data-cy='nurseries-actions-btn'
            >
              <MoreHoriz />
            </MenuButton>

            <MenuContent>
              <AddEditNurseryModal farmId={farmId}>
                <MenuItem
                  data-cy='create-new-nursery-btn'
                  w='100%'
                  value='create-new-nursery'
                  onClick={() => trackAction(actionsName.nurseryListViewNewNurseryClicked)}
                >
                  {trans('t_create_nursery_pond')}
                </MenuItem>
              </AddEditNurseryModal>
            </MenuContent>
          </MenuRoot>
        </Flex>
      </Flex>

      <NurseriesFilterTags filters={filters} onRemove={updateFilter} />

      <Flex flexDirection='column' gap='sm-alt' data-cy='nurseries-list' mb='2xl'>
        {!filteredNurseries.length ? (
          <Text textAlign='center' mt='md' data-cy='nurseries-list-no-data'>
            {trans('t_no_data_to_display')}
          </Text>
        ) : (
          <SimpleGrid columns={[1, 2, 2, 3, 4]} gapX='sm-alt' gapY='md' data-cy='nurseries-list-data'>
            {filteredNurseries.map((nursery) => {
              const { population, name: nurseryName, _id: nurseryId, size: nurserySize, eid: nurseryEid } = nursery;
              const destinationsNames: string[] = population?.pondIds?.reduce((acc, pondId) => {
                if (pondsHashmap[pondId]) acc.push(pondsHashmap[pondId].name);
                return acc;
              }, [] as string[]);

              return (
                <NurseryCard
                  key={nurseryId}
                  nurseryId={nurseryId}
                  nurseryName={nurseryName}
                  nurseryEid={nurseryEid}
                  farmEid={currentFarmSlug}
                  nurserySize={nurserySize}
                  population={population}
                  destinations={destinationsNames?.join(', ')}
                />
              );
            })}
          </SimpleGrid>
        )}
      </Flex>
    </Flex>
  );
}

function PageLoader() {
  const arrayOfSkeletonNumbers = new Array(7).fill(0);

  return (
    <Flex direction='column' gap='sm' px='md' py='md-alt' data-cy='nursery-list-skeleton-loader'>
      <Skeleton height='74px' />
      <Skeleton h='44px' w='90px' alignSelf='flex-end' />
      <Flex gap='sm-alt'>
        <Skeleton flex={1} h='234px' borderRadius='base' />
        <Skeleton flex={1} h='234px' borderRadius='base' />
      </Flex>
      {arrayOfSkeletonNumbers.map((_, index) => (
        <Skeleton key={index} height='70px' />
      ))}
    </Flex>
  );
}
