import { moduleFarm } from '@sdks/module-farm';
import { Head, QueryRequest, v1GetPopulationDetailsOutputRequest } from '@xpertsea/module-farm-sdk';
import {
  harvestCommonGql,
  harvestPlanCommonGql,
  historyGqlData,
  partialHarvestCommonGql,
  partialHarvestPlannedCommonGql,
  productionPredictionCommonGql
} from '@screens/population/apis/common-queries';

export async function getPopulationDetailsApi({
  params,
  fields = {
    population: {
      _id: 1,
      farmId: 1,
      pondId: 1,
      cycle: 1,
      stockedAt: 1,
      updatedAt: 1,
      stockedAtTimezone: 1,
      stockingAreaDensity: 1,
      hatcheryName: 1,
      geneticName: 1,
      kampiMortality: 1,
      averageHatcheryWeight: 1,
      hatcheryHarvestDate: 1,
      seedingQuantity: 1,
      seedingAverageWeight: 1,
      nurserySources: { _id: 1, nurseryId: 1, nurseryPopulationId: 1 },
      stockingType: 1,
      superVisorId: 1,
      lastMonitoringIds: 1,
      lastMonitoringStatus: 1,
      lastMonitoringDate: 1,
      isMonitoringStarted: 1,
      stockingCostsMillar: 1,
      dryDaysBeforeStocking: 1,
      manualAverageWeights: { date: 1, updatedBy: 1, averageWeight: 1 },
      lastMonitoringDistance: 1,
      cycleInformation: {
        carryingCapacity: 1,
        target: {
          cycleLength: 1,
          biomassToHarvest: 1,
          fcr: 1,
          harvestWeight: 1,
          survival: 1,
          type: 1
        },
        equipment: {
          numberOfAerators: 1,
          numberOfAutoFeeders: 1,
          autoFeederBrand: 1,
          aerationHaPa: 1,
          autoFeederProtocol: 1,
          feedBrand: 1,
          aeratorType: 1
        },
        projection: {
          overheadCostsPerHaPerDay: 1,
          updatedAt: 1,
          projectedGrowth: { days: 1, grams: 1, type: 1 },
          projectedFeed: {
            expectedFeed: 1,
            farmFeedTableId: 1,
            farmFeedTypes: {
              id: 1,
              percentage: 1
            },
            feedAggressionMultiplier: 1,
            type: 1
          },
          projectedSurvival: {
            dailyMortalityPercent: 1,
            expectedTargetSurvival: 1,
            expectedTargetSurvivalDays: 1,
            projectedSurvivalType: 1,
            updatedAt: 1
          }
        }
      },
      lastFcr: 1,
      lastMonitoringMlResult: {
        total: 1,
        averageWeight: 1,
        weightDistribution: 1,
        weightCv: 1,
        preProcessedImageUrl: 1,
        originalImageUrl: 1
      },
      productionPrediction: productionPredictionCommonGql,
      productionCycleComparison: {
        date: 1,
        averageWeight: 1,
        growthLinear: 1,
        growthDaily: 1,
        weeklyGrowth: 1,
        growthTwoWeeks: 1,
        growthThreeWeeks: 1,
        growth4w: 1,
        cumulativeFcr: 1,
        weeklyFcr: 1,
        adjustedFcr: 1,
        feedKgByHa: 1,
        weeklyFeedGiven: 1,
        totalFeedGivenKg: 1,
        totalFeedGivenLbs: 1,
        feedAsBiomassPercent: 1,
        survival: 1,
        totalBiomassLbs: 1,
        totalBiomassLbsByHa: 1,
        biomassLbs: 1,
        biomassLbsByHa: 1,
        animalsRemainingM2: 1,
        animalsRemainingHa: 1,
        survivalWithPartialHarvest: 1,
        profitPerHaPerDay: 1,
        profitPerPound: 1,
        profitPoundHarvest: 1,
        revenuePerPound: 1,
        revenuePoundHarvest: 1,
        totalProfit: 1,
        rofi: 1,
        totalRevenuePoundHarvest: 1,
        totalRevenuePound: 1,
        totalRevenue: 1,
        liveTotalRevenue: 1,
        totalCosts: 1,
        costPerPound: 1,
        costPoundHarvest: 1,
        stockingCosts: 1,
        cumulativeFeedCosts: 1,
        cumulativeOverheadCosts: 1,
        overheadCostDryDays: 1,
        overheadCostProductiveDays: 1,
        feedCostPerKg: 1
      },
      history: historyGqlData,
      historyAggregationDate: 1,
      lastWeekGrowth: 1,
      lastWeeklyGrowthDays: 1,
      prediction: {
        date: 1,
        averageWeight: 1
      },
      predictionDate: 1,
      predictionStatus: 1,
      growthBandsStatus: 1,
      growthTarget: {
        weight: 1,
        productionDays: 1,
        updatedAt: 1,
        updatedBy: 1
      },
      growthBands: {
        alert: {
          isRed: 1,
          isYellow: 1,
          isGreen: 1,
          caseNumber: 1,
          green: {
            date: 1,
            weight: 1
          },
          yellow: {
            date: 1,
            weight: 1
          },
          red: {
            date: 1,
            weight: 1
          },
          lastPrediction: {
            date: 1,
            averageWeight: 1
          }
        },
        targetWeightPredictedDate: 1,
        growthBandsError: 1,
        targetWeightPredictedDateError: 1,
        startDate: 1,
        targetDate: 1,
        targetWeight: 1,
        averages: {
          date: 1,
          weight: 1
        },
        ranges: {
          date: 1,
          max: 1,
          min: 1
        },
        updatedAt: 1
      },
      survivalRate: 1,
      metadata: 1,
      estimatedSurvival: 1,
      estimatedFeed: 1,
      createdAt: 1,
      isDeleted: 1,
      deletedAt: 1,
      deletedBy: 1,
      createdBy: 1,
      symmetry: {
        alert: {
          isGreen: 1,
          isYellow: 1,
          isRed: 1
        },
        averageWeight: 1,
        estimatedRevenueLoss: 1,
        heuristicAverageWeight: 1,
        lessThanAvgPercent: 1,
        lessThanHeuristicAvgPercent: 1,
        heuristicGramsBelowABW: 1,
        type: 1
      },
      partialHarvest: partialHarvestCommonGql,
      partialHarvestPlanned: partialHarvestPlannedCommonGql,
      harvest: harvestCommonGql,
      harvestPlan: harvestPlanCommonGql,
      notes: {
        note: 1,
        createdAt: 1
      },
      feedData: {
        date: 1,
        feed: {
          totalLbs: 1,
          feedTypeId: 1
        },
        totalLbs: 1,
        otherDirectCost: 1
      },
      dailyParameters: {
        _id: 1,
        date: 1,
        params: {
          _id: 1,
          paramId: 1,
          value: 1
        }
      }
    }
  } as v1GetPopulationDetailsOutputRequest,
  addFields, // todo handle addFields with merge deep
  removeFields // todo handle removeFields with remove deep
}: {
  params: Head<QueryRequest['v1GetPopulationDetails']>;
  fields?: v1GetPopulationDetailsOutputRequest;
  addFields?: v1GetPopulationDetailsOutputRequest;
  removeFields?: v1GetPopulationDetailsOutputRequest;
}) {
  const { data, errors } = await moduleFarm.query({
    v1GetPopulationDetails: [params, fields]
  });

  return { data, errors };
}
