import {
  PopulationHarvestCommercialClassBreakdownRequest,
  PopulationHarvestPlanIndicatorsRequest,
  PopulationHarvestPlanProjectionProfitProjectionDataRequest,
  PopulationHarvestPlanRequest,
  PopulationHarvestRequest,
  PopulationHistoryRequest,
  PopulationPartialHarvestRequest,
  PopulationProductionPredictionRequest
} from '@xpertsea/module-farm-sdk';

export const profitProjectionData: PopulationHarvestPlanProjectionProfitProjectionDataRequest = {
  date: 1,
  headon: {
    biomassLbs: 1,
    totalRevenue: 1,
    revenuePerPound: 1,
    revenuePoundHarvest: 1,
    totalRevenuePound: 1,
    totalRevenuePoundHarvest: 1,
    biomassKg: 1,
    profitPerHaPerDay: 1,
    totalProfit: 1,
    rofi: 1,
    costPerPound: 1,
    profitPerPound: 1,
    profitPoundHarvest: 1,
    liveTotalRevenue: 1,
    biomassProcessedLbs: 1
  },
  headless: {
    biomassLbs: 1,
    totalRevenue: 1,
    revenuePerPound: 1,
    revenuePoundHarvest: 1,
    totalRevenuePound: 1,
    totalRevenuePoundHarvest: 1,
    biomassKg: 1,
    profitPerHaPerDay: 1,
    totalProfit: 1,
    rofi: 1,
    costPerPound: 1,
    profitPerPound: 1,
    profitPoundHarvest: 1,
    liveTotalRevenue: 1,
    biomassProcessedLbs: 1
  }
};

export const productionPredictionCommonGql: PopulationProductionPredictionRequest = {
  date: 1,
  daysOfCulture: 1,
  averageWeight: 1,
  growthLinear: 1,
  growthDaily: 1,
  weeklyGrowth: 1,
  growthTwoWeeks: 1,
  growthThreeWeeks: 1,
  growth4w: 1,
  cumulativeFcr: 1,
  weeklyFcr: 1,
  adjustedFcr: 1,
  feedKgByHa: 1,
  weeklyFeedGiven: 1,
  totalBiomassKg: 1,
  totalFeedGivenKg: 1,
  totalFeedGivenLbs: 1,
  feedAsBiomassPercent: 1,
  survival: 1,
  totalBiomassLbs: 1,
  totalProcessedBiomassLbs: 1,
  totalBiomassLbsByHa: 1,
  biomassLbsByHa: 1,
  animalsRemainingM2: 1,
  animalsRemainingHa: 1,
  survivalWithPartialHarvest: 1,
  totalCosts: 1,
  stockingCosts: 1,
  cumulativeOverheadCosts: 1,
  overheadCostDryDays: 1,
  overheadCostProductiveDays: 1,
  cumulativeFeedCosts: 1,
  accumulatedOtherDirectCosts: 1,
  feedCostPerKg: 1,
  biomassLbs: 1,
  abw: 1,
  biomassKg: 1,
  biomassKgByHa: 1,
  fixedCostsDryDays: 1,
  expectedTargetSurvivalDays: 1,
  costPoundHarvest: 1,
  survivalFeed: 1,
  totalAnimalsHarvested: 1,
  animalsHarvestedM2: 1,
  animalsHarvestedHa: 1,
  totalPartialHarvestBiomassLbs: 1,
  cumulativePartialHarvestRevenue: 1,
  partialRevenuePound: 1,
  partialRevenuePoundHarvest: 1
};

export const historyGqlData: PopulationHistoryRequest = {
  date: 1,
  manualAverageWeight: 1,
  averageWeight: 1,
  abw: 1,
  weightCv: 1,
  total: 1,
  monitoringIds: 1,
  weightDistribution: 1,
  growthLinear: 1,
  growthDaily: 1,
  weeklyGrowth: 1,
  growthTwoWeeks: 1,
  growthThreeWeeks: 1,
  growth4w: 1,
  cumulativeFcr: 1,
  weeklyFcr: 1,
  adjustedFcr: 1,
  feedKgByHa: 1,
  totalFeedGivenKg: 1,
  totalFeedGivenLbs: 1,
  feedAsBiomassPercent: 1,
  survival: 1,
  totalBiomassLbs: 1,
  totalProcessedBiomassLbs: 1,
  totalBiomassLbsByHa: 1,
  biomassLbsByHa: 1,
  animalsRemainingM2: 1,
  animalsRemainingHa: 1,
  survivalWithPartialHarvest: 1,
  profitPerHaPerDay: 1,
  totalProfit: 1,
  rofi: 1,
  totalRevenue: 1,
  costPerPound: 1,
  profitPerPound: 1,
  profitPoundHarvest: 1,
  revenuePerPound: 1,
  revenuePoundHarvest: 1,
  totalRevenuePound: 1,
  totalRevenuePoundHarvest: 1,
  totalCosts: 1,
  stockingCosts: 1,
  cumulativeOverheadCosts: 1,
  accumulatedOtherDirectCosts: 1,
  overheadCostDryDays: 1,
  overheadCostProductiveDays: 1,
  cumulativeFeedCosts: 1,
  biomassLbs: 1,
  feedCostPerKg: 1,
  weeklyFeedGiven: 1,
  costPoundHarvest: 1,
  survivalFeed: 1,
  modelAverageWeight: 1,
  quality: 1,
  strength: 1,
  completeness: 1
};

const productionIndicators: PopulationHarvestPlanIndicatorsRequest = {
  abw: {
    current: {
      color: 1,
      val: 1
    },
    harvest: {
      color: 1,
      val: 1
    }
  },
  biomassLbsByHa: {
    current: {
      color: 1,
      val: 1
    },
    harvest: {
      color: 1,
      val: 1
    }
  },
  costPerPound: {
    current: {
      color: 1,
      val: 1
    },
    harvest: {
      color: 1,
      val: 1
    }
  },
  fcr: {
    current: {
      color: 1,
      val: 1
    },
    harvest: {
      color: 1,
      val: 1
    }
  },
  growth: {
    current: {
      color: 1,
      val: 1
    },
    harvest: {
      color: 1,
      val: 1
    }
  },
  growthLinear: {
    current: {
      color: 1,
      val: 1
    },
    harvest: {
      color: 1,
      val: 1
    }
  },
  profitPerHaPerDay: {
    current: {
      color: 1,
      val: 1
    },
    harvest: {
      color: 1,
      val: 1
    }
  },
  survivalRate: {
    current: {
      color: 1,
      val: 1
    },
    harvest: {
      color: 1,
      val: 1
    }
  }
};

export const commercialClassBreakdownCommonGql: PopulationHarvestCommercialClassBreakdownRequest = {
  headon: {
    totalBiomassProcessedLbs: 1,
    totalBiomassNotProcessedLbs: 1,
    totalRevenue: 1,
    totalRevenuePerPound: 1,
    bucketBins: {
      biomassProcessedLbs: 1,
      biomassNotProcessedLbs: 1,
      revenue: 1,
      revenuePerPound: 1,
      category: 1,
      percentage: 1
    }
  },
  headless: {
    totalBiomassProcessedLbs: 1,
    totalBiomassNotProcessedLbs: 1,
    totalRevenue: 1,
    totalRevenuePerPound: 1,
    bucketBins: {
      biomassProcessedLbs: 1,
      biomassNotProcessedLbs: 1,
      revenue: 1,
      revenuePerPound: 1,
      category: 1,
      percentage: 1
    }
  }
};

export const partialHarvestCommonGql: PopulationPartialHarvestRequest = {
  harvestId: 1,
  weight: 1,
  date: 1,
  quantity: 1,
  revenue: 1,
  revenuePerPound: 1,
  revenuePoundHarvest: 1,
  survival: 1,
  lbsHarvested: 1,
  lbsProcessed: 1,
  fileIds: 1,
  processorId: 1,
  enablesEstimation: 1,
  weightDistribution: 1,
  commercialClassBreakdown: commercialClassBreakdownCommonGql,
  processorPriceList: {
    defaultHarvestType: 1,
    headOn: {
      priceA: 1,
      priceB: 1,
      priceRejection: 1,
      size: 1
    },
    headless: {
      priceA: 1,
      priceB: 1,
      priceRejection: 1,
      size: 1
    },
    percentHarvestAsHeadsOn: 1,
    percentHarvestAsHeadsOnClassA: 1,
    percentHarvestAsHeadsOnClassB: 1,
    percentHarvestAsLeftover: 1,
    percentHeadlessAsClassA: 1,
    percentHeadlessAsClassB: 1,
    percentLeftoverAsHeadlessClassA: 1,
    percentLeftoverAsHeadlessClassB: 1,
    percentTail: 1,
    processLeftoverAs: 1,
    updatedAt: 1,
    updatedBy: 1
  },
  survivalAtRecord: 1
};
export const partialHarvestPlannedCommonGql: PopulationPartialHarvestRequest = {
  date: 1,
  weight: 1,
  fileIds: 1,
  revenue: 1,
  revenuePerPound: 1,
  revenuePoundHarvest: 1,
  survival: 1,
  quantity: 1,
  harvestId: 1,
  processorId: 1,
  lbsHarvested: 1,
  lbsProcessed: 1,
  weightDistribution: 1,
  commercialClassBreakdown: {
    headon: {
      totalBiomassProcessedLbs: 1,
      totalBiomassNotProcessedLbs: 1,
      totalRevenue: 1,
      totalRevenuePerPound: 1,
      bucketBins: {
        biomassProcessedLbs: 1,
        biomassNotProcessedLbs: 1,
        revenue: 1,
        revenuePerPound: 1,
        category: 1,
        percentage: 1
      }
    },
    headless: {
      totalBiomassProcessedLbs: 1,
      totalBiomassNotProcessedLbs: 1,
      totalRevenue: 1,
      totalRevenuePerPound: 1,
      bucketBins: {
        biomassProcessedLbs: 1,
        biomassNotProcessedLbs: 1,
        revenue: 1,
        revenuePerPound: 1,
        category: 1,
        percentage: 1
      }
    }
  },
  processorPriceList: {
    defaultHarvestType: 1,
    headOn: {
      priceA: 1,
      priceB: 1,
      priceRejection: 1,
      size: 1
    },
    headless: {
      priceA: 1,
      priceB: 1,
      priceRejection: 1,
      size: 1
    },
    percentHarvestAsHeadsOn: 1,
    percentHarvestAsHeadsOnClassA: 1,
    percentHarvestAsHeadsOnClassB: 1,
    percentHarvestAsLeftover: 1,
    percentHeadlessAsClassA: 1,
    percentHeadlessAsClassB: 1,
    percentLeftoverAsHeadlessClassA: 1,
    percentLeftoverAsHeadlessClassB: 1,
    percentTail: 1,
    processLeftoverAs: 1,
    updatedAt: 1,
    updatedBy: 1
  }
};
export const harvestCommonGql: PopulationHarvestRequest = {
  harvestId: 1,
  date: 1,
  timezone: 1,
  weight: 1,
  notes: 1,
  lbsHarvested: 1,
  fileIds: 1,
  revenue: 1,
  processorId: 1,
  enablesEstimation: 1,
  commercialClassBreakdown: commercialClassBreakdownCommonGql,
  processorPriceList: {
    defaultHarvestType: 1,
    headOn: {
      priceA: 1,
      priceB: 1,
      priceRejection: 1,
      size: 1
    },
    headless: {
      priceA: 1,
      priceB: 1,
      priceRejection: 1,
      size: 1
    },
    percentHarvestAsHeadsOn: 1,
    percentHarvestAsHeadsOnClassA: 1,
    percentHarvestAsHeadsOnClassB: 1,
    percentHarvestAsLeftover: 1,
    percentHeadlessAsClassA: 1,
    percentHeadlessAsClassB: 1,
    percentLeftoverAsHeadlessClassA: 1,
    percentLeftoverAsHeadlessClassB: 1,
    percentTail: 1,
    processLeftoverAs: 1,
    updatedAt: 1,
    updatedBy: 1
  },
  // production data
  daysOfCulture: 1,
  averageWeight: 1,
  growthLinear: 1,
  growthDaily: 1,
  weeklyGrowth: 1,
  growthTwoWeeks: 1,
  growthThreeWeeks: 1,
  growth4w: 1,
  cumulativeFcr: 1,
  weeklyFcr: 1,
  adjustedFcr: 1,
  feedKgByHa: 1,
  feedKgHaDay: 1,
  totalFeedGivenKg: 1,
  totalFeedGivenLbs: 1,
  feedAsBiomassPercent: 1,
  survival: 1,
  totalBiomassLbs: 1,
  totalProcessedBiomassLbs: 1,
  totalBiomassLbsByHa: 1,
  biomassLbsByHa: 1,
  animalsRemainingM2: 1,
  animalsRemainingHa: 1,
  survivalWithPartialHarvest: 1,
  totalProfit: 1,
  rofi: 1,
  profitPerHaPerDay: 1,
  profitPerPound: 1,
  profitPoundHarvest: 1,
  totalRevenue: 1,
  totalRevenuePound: 1,
  totalRevenuePoundHarvest: 1,
  revenuePerPound: 1,
  revenuePoundHarvest: 1,
  totalCosts: 1,
  stockingCosts: 1,
  costPerPound: 1,
  costPoundHarvest: 1,
  cumulativeFeedCosts: 1,
  cumulativeOverheadCosts: 1,
  overheadCostDryDays: 1,
  overheadCostProductiveDays: 1,
  feedCostPerKg: 1,
  accumulatedOtherDirectCosts: 1,
  biomassLbs: 1,
  weeklyFeedGiven: 1,
  totalAnimalsHarvested: 1,
  animalsHarvestedM2: 1,
  animalsHarvestedHa: 1,
  totalPartialHarvestBiomassLbs: 1,
  cumulativePartialHarvestRevenue: 1,
  partialRevenuePound: 1,
  partialRevenuePoundHarvest: 1,
  hasEstimatedValues: 1,
  weightDistribution: 1,
  numberOfPartialHarvest: 1,
  harvestType: 1
};
export const harvestPlanCommonGql: PopulationHarvestPlanRequest = {
  createdAt: 1,
  createdBy: 1,
  approvedBy: 1,
  approvedAt: 1,
  harvestId: 1,
  harvestType: 1,
  harvestDate: 1,
  selectedProcessorId: 1,
  commercialClassBreakdown: commercialClassBreakdownCommonGql,
  processorPriceList: {
    defaultHarvestType: 1,
    headOn: {
      priceA: 1,
      priceB: 1,
      priceRejection: 1,
      size: 1
    },
    headless: {
      priceA: 1,
      priceB: 1,
      priceRejection: 1,
      size: 1
    },
    percentHarvestAsHeadsOn: 1,
    percentHarvestAsHeadsOnClassA: 1,
    percentHarvestAsHeadsOnClassB: 1,
    percentHarvestAsLeftover: 1,
    percentHeadlessAsClassA: 1,
    percentHeadlessAsClassB: 1,
    percentLeftoverAsHeadlessClassA: 1,
    percentLeftoverAsHeadlessClassB: 1,
    percentTail: 1,
    processLeftoverAs: 1,
    updatedAt: 1,
    updatedBy: 1
  },
  indicators: productionIndicators,
  optimalProcessorId: 1,
  optimalProfitProjectionData: {
    date: 1,
    harvestType: 1,
    headless: {
      biomassKg: 1,
      biomassLbs: 1,
      totalProfit: 1,
      rofi: 1,
      costPerPound: 1,
      totalRevenue: 1,
      profitPerPound: 1,
      profitPoundHarvest: 1,
      revenuePerPound: 1,
      revenuePoundHarvest: 1,
      liveTotalRevenue: 1,
      profitPerHaPerDay: 1,
      totalRevenuePound: 1,
      biomassProcessedLbs: 1
    },
    headon: {
      biomassKg: 1,
      biomassLbs: 1,
      totalProfit: 1,
      rofi: 1,
      costPerPound: 1,
      totalRevenue: 1,
      profitPerPound: 1,
      profitPoundHarvest: 1,
      revenuePerPound: 1,
      revenuePoundHarvest: 1,
      liveTotalRevenue: 1,
      profitPerHaPerDay: 1,
      totalRevenuePound: 1,
      biomassProcessedLbs: 1
    }
  },
  projection: {
    processingStatus: 1,
    profitProjectionData,
    processingStatusReasons: 1,
    updatedAt: 1,
    plannedHarvestIdx: 1,
    optimalHarvestIdx: 1,
    processorId: 1
  }
};
