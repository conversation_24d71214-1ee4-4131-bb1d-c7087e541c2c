import { IApiHookInput, ICallbackParams, IHookInputType, useApiHookWrapper } from '@hooks/use-api-hook-wrapper';
import { getTrans } from '@i18n/get-trans';
import { addToastAction } from '@redux/app';
import { moduleFarm } from '@sdks/module-farm';
import { throwErrorMsg } from '@utils/errors';
import { Head, QueryRequest, v1ListPopulationsOutputRequest } from '@xpertsea/module-farm-sdk';

async function mountFn(props: ICallbackParams<Input>) {
  const { input, successCallback } = props;

  const {
    loadRelated = false,
    params,
    fields = {
      populations: {
        _id: 1,
        pondId: 1,
        cycle: 1,
        harvest: {
          harvestId: 1,
          date: 1,
          timezone: 1,
          lbsHarvested: 1,
          fileIds: 1,
          revenue: 1,
          totalAnimalsHarvested: 1,
          animalsHarvestedM2: 1,
          animalsHarvestedHa: 1,
          survival: 1
        },
        stockedAt: 1,
        stockedAtTimezone: 1,
        seedingAverageWeight: 1,
        seedingQuantity: 1,
        stockingAreaDensity: 1,
        hatcheryName: 1,
        geneticName: 1,
        averageHatcheryWeight: 1,
        hatcheryHarvestDate: 1,
        lastMonitoringDistance: 1
      }
    }
  } = input;

  const { data: populationRes, errors } = await moduleFarm.query({
    v1ListPopulations: [params, fields]
  });

  if (errors) {
    throwErrorMsg(errors);
  }

  const populations = populationRes?.v1ListPopulations?.populations;

  const pondIds = populations?.map((population) => population.pondId) || [];

  if (!loadRelated || !pondIds.length) {
    successCallback?.({ populations });

    return { populations };
  }

  const { data: pondRes, errors: pondErrors } = await moduleFarm.query({
    v1ListPonds: [
      {
        page: { size: 1000, current: 1 },
        filter: {
          id: pondIds,
          isArchived: false
        }
      },
      {
        ponds: {
          _id: 1,
          name: 1,
          farmId: 1,
          size: 1,
          eid: 1
        }
      }
    ]
  });

  if (pondErrors) {
    throwErrorMsg(pondErrors);
  }

  const ponds = pondRes?.v1ListPonds?.ponds;

  const pondsWithPopulation = ponds?.map((pond) => {
    const pondPopulation = populations?.find((population) => population.pondId === pond._id);
    return {
      ...pond,
      currentPopulation: pondPopulation || null
    };
  });

  successCallback?.({ populations, ponds: pondsWithPopulation });

  return { populations, ponds: pondsWithPopulation };
}

async function errorFn(props: ICallbackParams<Input>) {
  const { dispatch, error } = props;
  const { trans } = getTrans();
  dispatch(addToastAction({ type: 'error', title: trans('t_error'), msg: error }));
}

type Input = IApiHookInput<Head<QueryRequest['v1ListPopulations']>, v1ListPopulationsOutputRequest>;
type Output = Awaited<ReturnType<typeof mountFn>>;

export function useListPopulationsApi(props?: IHookInputType<Input, Output>) {
  return useApiHookWrapper({
    mountFn,
    errorFn,
    initialInput: props,
    initialIsLoading: !!props,
    skipInitialApiCallOnEmptyInputs: true,
    unmountFn: undefined,
    __t: props?.__t, // cache buster key
    __cachePrefix: props?.__cachePrefix, // cache key prefix
    __cacheTTL: props?.__cacheTTL // cache ttl in seconds
  });
}
