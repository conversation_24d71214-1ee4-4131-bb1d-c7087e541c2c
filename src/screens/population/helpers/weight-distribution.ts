import { formatNumber } from '@utils/number';

export interface WeightDistributionRage {
  min: number;
  max: number;
  rangeValue: string;
}

/*
 * Get the HDI (The Highest Density Interval) from weight distribution using a proportion of the total size of the array
 * @param {Array} weights - array of weights distribution
 * @param {Number} proportion - the number percentage to be used to trim the array and calculate the highest density interval (ex: 95%)
 * @returns {WeightDistributionRage} - result object with the ranges
 */
export function getRangeFromWeightDistribution(
  weights: number[],
  proportion: number,
  lang: string
): WeightDistributionRage {
  if (!weights?.length) return;

  // sort
  const sortedWeights = [...weights].sort((x, y) => x - y);

  // Get the number of elements to trim
  const minObs = Math.ceil(sortedWeights.length * proportion);

  // Trim the array only with the High weight distribution values
  const highWeights = sortedWeights.slice(minObs);

  // Trim the array only with the Low weight distribution values
  const lowWeights = sortedWeights.slice(0, highWeights.length);

  // Create an array to store the ranges High - Low
  const ranges = [];

  for (let i = 0; i < highWeights.length; i++) {
    ranges.push(highWeights[i] - lowWeights[i]);
  }
  // Get the index of the minimum weight distribution range
  const minRangeIndex = ranges.indexOf(Math.min(...ranges));

  // Get the index of the maximum weight distribution range
  const maxRangeIndex = minRangeIndex + minObs - 1;

  const min = sortedWeights[minRangeIndex];
  const max = sortedWeights[maxRangeIndex];

  const rangeValue =
    !min || !max
      ? ''
      : `${formatNumber(min, { lang, fractionDigits: 2 })} - ${formatNumber(max, { lang, fractionDigits: 2 })}`;

  return {
    min,
    max,
    rangeValue
  };
}
