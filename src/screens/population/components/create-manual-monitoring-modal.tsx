import { Box, BoxProps, chakra, Flex, Text, useDisclosure } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { FormControlDateInput } from '@components/form/form-control-date-input';
import { yupResolver } from '@hookform/resolvers/yup';
import { useAnalytics } from '@hooks/use-analytics';
import { getTrans } from '@i18n/get-trans';
import { actionsName } from '@utils/segment';
import { numberTransform, useYupSchema, Yup, YupResolverType } from '@utils/yup';
import { DateTime } from 'luxon';
import { ReactNode } from 'react';
import { useForm } from 'react-hook-form';
import { BaseFormNumberInput } from '@components/form/base-form-number-input';
import { useCreateManualMonitoringApi } from '@screens/monitoring/hooks/use-create-manual-monitoring-api';
import { DialogB<PERSON>, DialogContent, DialogFooter, DialogHeader, DialogRoot, DialogTitle } from '@components/ui/dialog';

interface CreateManualMonitoringModalProps {
  populationId: string;
  pondName: string;
  pondId: string;
  children: ReactNode;
  defaultValues?: { date: Date; averageWeight: number };
  buttonProps?: BoxProps;
}

export function CreateManualMonitoringModal(props: CreateManualMonitoringModalProps) {
  const { children, populationId, defaultValues, pondName, pondId, buttonProps = {} } = props;

  const { trackAction } = useAnalytics();
  const { schema } = useYupSchema({
    date: Yup.date().transform(numberTransform).required().label('t_date'),
    averageWeight: Yup.number().transform(numberTransform).required().label('t_harvest_abw')
  });

  const { trans } = getTrans();
  const { onOpen, onClose, open } = useDisclosure();
  const [{ isLoading: isUpdating }, createManualMonitoring] = useCreateManualMonitoringApi();

  type FormValues = {
    date: Date;
    averageWeight: number;
  };
  const {
    reset,
    handleSubmit,
    control,
    formState: { errors }
  } = useForm<FormValues>({
    resolver: yupResolver(schema) as YupResolverType<FormValues>,
    defaultValues
  });

  const isEdit = !!defaultValues?.date;

  const handleModalOpen = () => {
    onOpen();

    if (isEdit) {
      trackAction(actionsName.editManualMonitoringClicked, { pondId, pondName }).then();
    } else {
      trackAction(actionsName.createManualMonitoringClicked, { pondId, pondName }).then();
    }

    reset(defaultValues);
  };

  const onSubmit = handleSubmit((data: FormValues) => {
    trackAction(actionsName.manuallyMonitored, { pondId, pondName }).then();
    const date = data?.date ?? DateTime.local().toJSDate();

    createManualMonitoring({
      params: {
        populationId,
        startedAt: date,
        averageWeight: data.averageWeight
      },
      successCallback: () => {
        onClose();
      }
    });
  });

  return (
    <DialogRoot
      open={open}
      onOpenChange={(e) => {
        e.open ? handleModalOpen() : onClose();
      }}
      restoreFocus={false}
    >
      <Box
        w='fit-content'
        onClick={(e) => {
          e.stopPropagation();
          handleModalOpen();
        }}
        {...buttonProps}
      >
        {children}
      </Box>

      <DialogContent overflow='visible' data-cy='harvest-population-modal'>
        <chakra.form display='contents' onSubmit={onSubmit} noValidate>
          <DialogHeader px='md'>
            <DialogTitle>{isEdit ? trans('t_edit_manual_monitoring') : trans('t_abw_g')}</DialogTitle>
          </DialogHeader>

          <DialogBody px='md' pt='2xs'>
            <OneRow label={trans('t_date')} py='xs-alt' px='sm-alt'>
              <FormControlDateInput
                w='150px'
                id='date'
                name='date'
                placeholder={trans('t_select_date')}
                error={errors?.date?.message}
                control={control}
                inputProps={{ size: 'sm', rounded: 'base', bgColor: 'gray.100' }}
                bg='none'
                dateFormat='MMM dd, yyyy'
              />
            </OneRow>

            <OneRow label={trans('t_abw_g')} py='xs-alt' px='sm-alt' isInvalid={!!errors?.averageWeight}>
              <BaseFormNumberInput
                name='averageWeight'
                control={control}
                required
                w='150px'
                numericFormatProps={{ decimalScale: 2 }}
                inputProps={{ size: 'sm', textAlign: 'right', rounded: 'base', bgColor: 'gray.100' }}
              />
            </OneRow>
          </DialogBody>
          <DialogFooter px='md' pt='xs' gap='sm'>
            <BaseButton variant='link' color='text.gray' onClick={onClose}>
              {trans('t_cancel')}
            </BaseButton>
            <BaseButton type='submit' loading={isUpdating} size='sm'>
              {isEdit ? trans('t_update') : trans('t_create')}
            </BaseButton>
          </DialogFooter>
        </chakra.form>
      </DialogContent>
    </DialogRoot>
  );
}

function OneRow(props: BoxProps & { isInvalid?: boolean; label: string; subTitle?: string }) {
  const { isInvalid, label, children, subTitle, ...rest } = props;
  return (
    <Box bg='gray.100' p='sm-alt' mb='xs-alt' rounded='base' {...rest}>
      <Flex justify='space-between' align={subTitle || isInvalid ? 'flex-start' : 'center'}>
        <Flex flexDirection='column'>
          <Text fontSize='md' fontWeight={500} lineHeight={isInvalid ? '36px' : undefined}>
            {label}
          </Text>
          {subTitle && (
            <Text fontSize='md' fontWeight={500} color='gray.700' maxW='234px'>
              {subTitle}
            </Text>
          )}
        </Flex>

        <Box>{children}</Box>
      </Flex>
    </Box>
  );
}
