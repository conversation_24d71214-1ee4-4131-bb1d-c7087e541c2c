import { Box, BoxProps, chakra, Flex, Heading, useDisclosure } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { yupResolver } from '@hookform/resolvers/yup';
import { useAnalytics } from '@hooks/use-analytics';
import { getTrans } from '@i18n/get-trans';
import { useReloadCurrentFarmPonds } from '@screens/farm/hooks/use-reload-current-farm-ponds';
import { actionsName } from '@utils/segment';
import { numberTransform, useYupSchema, Yup, YupResolverType } from '@utils/yup';
import { DateTime } from 'luxon';
import { ReactNode, useRef, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { getPercentageDivided } from '@utils/number';
import { useAppSelector } from '@redux/hooks';
import { EditHarvestConfirmationModal } from '@screens/pond/components/edit-harvest-confirmation-modal';
import { useUpdatePopulationApi } from '@screens/population/hooks/use-update-population-api';
import { useGetPopulationDetailsApi } from '@screens/population/hooks/use-get-population-details-api';
import { PopulationType } from '@redux/farm/set-current-population';
import { DialogContent, DialogRoot } from '@components/ui/dialog';
import ObjectID from 'bson-objectid';
import { AccordionItem, AccordionItemContent, AccordionItemTrigger, AccordionRoot } from '@components/ui/accordion';
import { useGetPriceListSchema } from '@screens/population/hooks/use-get-price-list-schema';
import { defaultProcessorPriceList } from '@screens/settings/constants/default-price-list';
import { HarvestPlanFormValues } from '@screens/population/components/harvest-modals/types';
import { FormSteps, PriceListFormFields } from '@screens/population/components/harvest-modals/price-list-form-fields';
import { MainFormFields } from '@screens/population/components/harvest-modals/main-form-fields';
import { HarvestQualityFormFields } from '@screens/population/components/harvest-modals/harvest-quality-form-fields';
import { useSetPriceListToHarvestForm } from '@screens/population/hooks/use-set-price-list-to-harvest-form';
import { CloseButton } from '@components/ui/close-button';
import { PopulationHarvestPlanInput } from '@xpertsea/module-farm-sdk';
import { HarvestPlanFormProjectionDetails } from '@screens/population/components/harvest-modals/harvest-plan-form-projection-details';
import { useGetProcessorOptions } from '@screens/pond/hooks/use-get-processor-options';

interface EditFinalHarvestPlanModalProps {
  pondName: string;
  pondId: string;
  pondSize: number;
  children: ReactNode;
  isInPondView?: boolean;
  isInPondListView?: boolean;
  triggerContainerProps?: BoxProps;
  onCancel?: () => void;
  onSuccess?: () => void;
  population?: PopulationType;
}

export function EditFinalHarvestPlanModal(props: EditFinalHarvestPlanModalProps) {
  const {
    population,
    children,
    pondName,
    pondId,
    pondSize,
    isInPondView = true,
    isInPondListView,
    triggerContainerProps = {},
    onCancel,
    onSuccess
  } = props;

  const { trans } = getTrans();

  //START-STATE HOOKS
  const { onOpen, onClose, open } = useDisclosure();
  const { onOpen: onConfirmationOpen, onClose: onConfirmationClose, open: isConfirmationOpen } = useDisclosure();
  const [step, setStep] = useState(FormSteps.MainView);

  const submitButtonRef = useRef<HTMLButtonElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  ///START-API HOOKS
  const [{ isLoading: isLoadingPopulation }, updatePopulation] = useUpdatePopulationApi();
  const { reloadCurrentFarmPonds } = useReloadCurrentFarmPonds();
  const [_, getPopulationDetails] = useGetPopulationDetailsApi();

  /// START-REDUX HOOKS
  const { trackAction } = useAnalytics();
  const { processorOptions, activeProcessorPriceLists } = useGetProcessorOptions();
  const { priceListSchema } = useGetPriceListSchema(false);
  const currentPopulation = useAppSelector((state) => state.farm?.currentPopulation);

  const populationToUse = population ?? currentPopulation;

  const { harvestPlan, cycle, _id: populationId, stockedAt, stockedAtTimezone, history } = populationToUse ?? {};
  const minDate = history?.[0]?.date;

  const normalizedHarvest = {
    ...(harvestPlan ?? {}),
    priceList: harvestPlan?.processorPriceList,
    processorId: harvestPlan?.selectedProcessorId,
    date: harvestPlan?.harvestDate
  };

  const {
    date: harvestDate,
    processorId: harvestProcessorId,
    harvestId,
    priceList: harvestPriceList
  } = normalizedHarvest ?? {};

  const harvestIdToUse = harvestId ?? ObjectID().toHexString();
  const harvestAt = harvestDate ? DateTime.fromFormat(harvestDate, 'yyyy-MM-dd') : null;

  const finalDefaultValues: HarvestPlanFormValues = {
    date: harvestAt?.toJSDate() ?? DateTime.local().toJSDate(),
    processorId: harvestProcessorId,
    priceList: {
      defaultHarvestType: harvestPriceList?.defaultHarvestType ?? 'headon',
      headOn: defaultProcessorPriceList.headOn,
      headless: defaultProcessorPriceList.headless,
      percentHarvestAsHeadsOn: null,
      percentHarvestAsHeadsOnClassA: null,
      percentHarvestAsHeadsOnClassB: null,
      percentHarvestAsLeftover: null,
      processLeftoverAs: harvestPriceList?.processLeftoverAs ?? 'headlessAsClassAB',
      percentLeftoverAsHeadlessClassA: null,
      percentLeftoverAsHeadlessClassB: null,
      percentHeadlessAsClassA: null,
      percentHeadlessAsClassB: null,
      percentTail: 67
    }
  };

  // START-FORM HOOKS
  const { schema } = useYupSchema({
    date: Yup.date().transform(numberTransform).required(trans('t_required')),
    processorId: Yup.string().required(trans('t_required')),
    priceList: priceListSchema
  });

  const formMethods = useForm<HarvestPlanFormValues>({
    resolver: yupResolver(schema) as YupResolverType<HarvestPlanFormValues>,
    defaultValues: finalDefaultValues,
    mode: 'onChange'
  });

  const { reset, handleSubmit, watch, setValue } = formMethods;

  const processorIdWatch = watch('processorId');
  const farmPriceList = activeProcessorPriceLists?.find((priceList) => priceList._id === processorIdWatch)?.priceList;

  //EFFECTS
  const { setPriceList } = useSetPriceListToHarvestForm({
    setValue,
    triggerState: open,
    activeProcessorPriceLists,
    farmPriceList,
    harvestPriceList,
    defaultPercentTail: finalDefaultValues.priceList.percentTail
  });

  // START-HANDLERS
  const handleModalOpen = () => {
    onOpen();
    reset(finalDefaultValues);
  };

  const handleModalClose = () => {
    onClose();
    onCancel?.();
    setStep(FormSteps.MainView);
  };

  const onReload = () => {
    if (isInPondView) return getPopulationDetails({ params: { filter: { populationId } }, loadRelated: true });
    if (isInPondListView) return reloadCurrentFarmPonds();
  };

  const onSubmit = handleSubmit((data: HarvestPlanFormValues) => {
    const { date, processorId, priceList, ...rest } = data ?? {};
    const {
      percentHarvestAsHeadsOn,
      percentHarvestAsHeadsOnClassA,
      percentHarvestAsHeadsOnClassB,
      percentHarvestAsLeftover,
      percentHeadlessAsClassA,
      percentHeadlessAsClassB,
      percentLeftoverAsHeadlessClassA,
      percentLeftoverAsHeadlessClassB,
      percentTail,
      ...restPriceList
    } = priceList ?? {};

    const harvestData: PopulationHarvestPlanInput = {
      ...rest,
      harvestDate: DateTime.fromJSDate(date).toFormat('yyyy-MM-dd'),
      selectedProcessorId: processorId,
      harvestId: harvestIdToUse,
      processorPriceList: {
        ...finalDefaultValues.priceList,
        ...restPriceList,
        percentHarvestAsHeadsOn: getPercentageDivided(percentHarvestAsHeadsOn),
        percentHarvestAsHeadsOnClassA: getPercentageDivided(percentHarvestAsHeadsOnClassA),
        percentHarvestAsHeadsOnClassB: getPercentageDivided(percentHarvestAsHeadsOnClassB),
        percentHarvestAsLeftover: getPercentageDivided(percentHarvestAsLeftover),
        percentHeadlessAsClassA: getPercentageDivided(percentHeadlessAsClassA),
        percentHeadlessAsClassB: getPercentageDivided(percentHeadlessAsClassB),
        percentLeftoverAsHeadlessClassA: getPercentageDivided(percentLeftoverAsHeadlessClassA),
        percentLeftoverAsHeadlessClassB: getPercentageDivided(percentLeftoverAsHeadlessClassB),
        percentTail: getPercentageDivided(percentTail)
      }
    };

    updatePopulation({
      params: {
        filter: { populationId },
        set: {
          harvestPlan: { ...harvestPlan, ...harvestData }
        }
      },
      successCallback() {
        onSuccess?.();
        onClose();
        onReload();
        trackAction(actionsName.pondHarvestPlanEdited, { pondId, pondName }).then();
      }
    });
  });

  return (
    <DialogRoot
      open={open}
      onOpenChange={(e) => {
        e.open ? handleModalOpen() : handleModalClose();
      }}
      restoreFocus={false}
      size='lg'
      scrollBehavior='inside'
    >
      <Box
        w='fit-content'
        onClick={(e) => {
          e.stopPropagation();
          handleModalOpen();
        }}
        {...triggerContainerProps}
      >
        {children}
      </Box>

      <DialogContent
        data-cy='harvest-population-modal'
        borderRadius='2xl'
        px='lg'
        py={0}
        bgColor='bg.gray.medium'
        ref={containerRef}
        closeTrigger={false}
      >
        <EditHarvestConfirmationModal
          submitButtonRef={submitButtonRef}
          isOpen={isConfirmationOpen}
          onClose={onConfirmationClose}
          title={trans('t_edit_harvest_plan_confirmation')}
          subTitle={trans('t_harvest_plan_change_description')}
          containerRef={containerRef}
        />

        {step === FormSteps.MainView && (
          <Flex
            pos='sticky'
            bgColor='bg.gray.medium'
            top={0}
            align='center'
            justify='space-between'
            gap='sm-alt'
            pt='lg'
            pb='2lg'
            zIndex={10}
          >
            <Heading size='heavy300'>
              {trans('t_edit_final_harvest_plan_title', {
                pondName,
                cycle
              })}
            </Heading>
            <CloseButton size='sm' onClick={handleModalClose} />
          </Flex>
        )}

        <FormProvider {...formMethods}>
          <chakra.form
            display='flex'
            flexDirection='column'
            gap='2lg'
            onSubmit={(event) => {
              event.stopPropagation();
              onSubmit(event).then();
            }}
            noValidate
            data-cy='add-edit-final-harvest-form'
          >
            {step === FormSteps.MainView && (
              <>
                <MainFormFields
                  minDate={minDate}
                  processorOptions={processorOptions}
                  setStep={setStep}
                  stockedAtTimezone={stockedAtTimezone}
                  stockedAtDate={stockedAt}
                  setPriceList={setPriceList}
                  activeProcessorPriceLists={activeProcessorPriceLists}
                  isPlannedFull
                />
                <AccordionRoot
                  collapsible
                  defaultValue={['quality', 'projected-details']}
                  multiple
                  variant='plain'
                  display='flex'
                  flexDirection='column'
                  gap='2lg'
                >
                  <AccordionItem bgColor='white' borderRadius='2xl' py='md' value='quality'>
                    <AccordionItemTrigger cursor='pointer' py='sm-alt' px='md' textStyle='label.100'>
                      {trans('t_harvest_quality')}
                    </AccordionItemTrigger>
                    <AccordionItemContent display='flex' flexDirection='column' gap='sm-alt' pb={0} pt='sm-alt' px='md'>
                      <HarvestQualityFormFields farmPriceList={farmPriceList} />
                    </AccordionItemContent>
                  </AccordionItem>
                  <AccordionItem bgColor='white' borderRadius='2xl' py='md' value='projected-details'>
                    <AccordionItemTrigger cursor='pointer' py='sm-alt' px='md' textStyle='label.100'>
                      {trans('t_projected_harvest_details')}
                    </AccordionItemTrigger>
                    <AccordionItemContent display='flex' flexDirection='column' gap='sm-alt' pb={0} pt='sm-alt' px='md'>
                      <HarvestPlanFormProjectionDetails
                        pondSize={pondSize}
                        populationId={populationId}
                        harvestId={harvestIdToUse}
                        hasProjection={!!harvestPlan?.projection?.profitProjectionData?.length}
                      />
                    </AccordionItemContent>
                  </AccordionItem>
                </AccordionRoot>
              </>
            )}
            {step === FormSteps.PriceList && (
              <PriceListFormFields
                setStep={setStep}
                pondName={pondName}
                cycle={cycle}
                farmPriceList={farmPriceList}
                processorOptions={processorOptions}
              />
            )}

            {step === FormSteps.MainView && (
              <Flex
                justify='flex-end'
                bgColor='bg.gray.medium'
                gap='sm-alt'
                pos='sticky'
                bottom='0'
                insetEnd='0'
                py='lg'
                zIndex={10}
              >
                <BaseButton
                  variant='secondary'
                  bgColor='transparent'
                  onClick={handleModalClose}
                  analyticsId={actionsName.pondOverviewEditHarvestPlanCancelClicked}
                >
                  {trans('t_cancel')}
                </BaseButton>
                <BaseButton
                  type='button'
                  loading={isLoadingPopulation}
                  onClick={() => {
                    onConfirmationOpen();
                  }}
                  analyticsId={actionsName.pondOverviewEditHarvestPlanSaveClicked}
                >
                  {trans('t_save_changes')}
                </BaseButton>
                <button style={{ display: 'none' }} type='submit' ref={submitButtonRef}>
                  {trans('t_save_changes')}
                </button>
              </Flex>
            )}
          </chakra.form>
        </FormProvider>
      </DialogContent>
    </DialogRoot>
  );
}
