import { chakra, Flex, HTMLChakraProps, Separator, Text, useDisclosure } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import isUndefined from 'lodash/isUndefined';
import { numberTransform, useYupSchema, Yup, yupFormResolver, YupResolverType } from '@utils/yup';
import { DateTime } from 'luxon';
import { Controller, FormProvider, useForm, useFormContext } from 'react-hook-form';
import { Dispatch, SetStateAction, useEffect, useRef, useState } from 'react';
import {
  convertUnitByDivision,
  convertUnitByMultiplication,
  formatNumber,
  getPercentageDivided,
  isNumber
} from '@utils/number';
import { BaseFormNumberInput } from '@components/form/base-form-number-input';
import { AdminOnlyWrapper } from '@components/permission-wrappers/admin-only-wrapper';
import { UploadReport } from '@screens/population/components/upload-liquidation-report';
import { BaseButton } from '@components/base/base-button';
import { HarvestFormOneRow } from '@screens/pond/components/modals/harvest-form-one-row';
import { BaseFormCheckbox } from '@components/form/base-form-checkbox';
import { EditHarvestConfirmationModal } from '@screens/pond/components/edit-harvest-confirmation-modal';
import { PopulationHistory, PopulationPartialHarvestInput } from '@xpertsea/module-farm-sdk';
import { FormSteps, PriceListFormFields } from '@screens/population/components/harvest-modals/price-list-form-fields';
import { RecordHarvestFormValues, SurvivalAtRecord } from '@screens/population/components/harvest-modals/types';
import { useGetPriceListSchema } from '@screens/population/hooks/use-get-price-list-schema';
import { defaultProcessorPriceList } from '@screens/settings/constants/default-price-list';
import { useCalculateHarvestFormDetails } from '@screens/population/hooks/use-calculate-harvest-form-details';
import { MainFormFields } from '@screens/population/components/harvest-modals/main-form-fields';
import { AccordionItem, AccordionItemContent, AccordionItemTrigger, AccordionRoot } from '@components/ui/accordion';
import { HarvestQualityFormFields } from '@screens/population/components/harvest-modals/harvest-quality-form-fields';
import { useSetPriceListToHarvestForm } from '@screens/population/hooks/use-set-price-list-to-harvest-form';
import { FormControlReactSelect } from '@components/form/form-control-react-select';
import { harvestSelectControlStyles } from '@screens/pond/components/modals/helpers/chakra-form-styles';
import { useGetProcessorOptions } from '@screens/pond/hooks/use-get-processor-options';

export interface AddEditPartialHarvestFormProps extends Omit<HTMLChakraProps<'form'>, 'onSubmit'> {
  onCancel: () => void;
  onSubmit: (data: PopulationPartialHarvestInput) => void;
  saveButtonText: string;
  stockedAt: string;
  pondId: string;
  pondSize: number;
  populationId: string;
  stockedAtTimezone: string;
  lastMonitoringDate: string;
  lastMonitoringDateTimezone: string;
  isLoading?: boolean;
  defaultValues?: Omit<RecordHarvestFormValues, 'date'> & { date: string };
  isEdit?: boolean;
  seedingQuantity: number;
  history: PopulationHistory[];
  survivalRate: Record<string, number>;
  setStep: Dispatch<SetStateAction<FormSteps>>;
  step: FormSteps;
  pondName: string;
  cycle: string;
  triggerPriceListState?: boolean | number | string;
  survivalAtRecord?: SurvivalAtRecord;
}

export function AddEditPartialHarvestForm(props: AddEditPartialHarvestFormProps) {
  const {
    pondId,
    pondSize,
    populationId,
    stockedAt,
    stockedAtTimezone,
    defaultValues,
    saveButtonText,
    onCancel,
    onSubmit,
    lastMonitoringDate,
    lastMonitoringDateTimezone,
    isLoading,
    isEdit,
    seedingQuantity,
    history,
    survivalRate,
    setStep,
    step,
    pondName,
    cycle,
    triggerPriceListState,
    survivalAtRecord,
    ...rest
  } = props;
  const { trans } = getTrans();

  // START-STATE HOOKS
  const { onOpen: onConfirmationOpen, onClose: onConfirmationClose, open: isConfirmationOpen } = useDisclosure();
  const submitButtonRef = useRef<HTMLButtonElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [quantityError, setQuantityError] = useState('');

  //START-REDUX HOOKS
  const user = useAppSelector((state) => state.auth.user);

  const { processorOptions, activeProcessorPriceLists } = useGetProcessorOptions();
  const { priceListSchema } = useGetPriceListSchema(false);

  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);

  const lastHistoryItem = history?.[0];
  const lastHistoryBiomass = lastHistoryItem?.biomassLbs
    ? (formatNumber(convertUnitByDivision(history?.[0]?.biomassLbs, unitsConfig?.biomass), {
        asNumber: true,
        fractionDigits: 0
      }) as number)
    : 0;
  const lastHistoryBiomassHa = lastHistoryBiomass
    ? (formatNumber(lastHistoryBiomass / pondSize, {
        asNumber: true,
        fractionDigits: 0
      }) as number)
    : 0;

  const {
    date: harvestDate,
    lbsHarvested: harvestAmount,
    weight: harvestAbw,
    revenue: harvestRevenue,
    fileIds: harvestFileIds,
    processorId: harvestProcessorId,
    enablesEstimation: harvestEnablesEstimation,
    priceList: harvestPriceList
  } = defaultValues ?? {};

  const harvestAtDateTime = harvestDate ? DateTime.fromFormat(harvestDate, 'yyyy-MM-dd') : null;

  const finalDefaultValues: RecordHarvestFormValues = {
    date: harvestAtDateTime?.toJSDate() ?? DateTime.local().toJSDate(),
    lbsHarvested: convertUnitByDivision(harvestAmount, unitsConfig?.biomass),
    lbsHarvestedUnit: 'lbs',
    weight: harvestAbw,
    revenue: !harvestEnablesEstimation ? harvestRevenue : undefined,
    fileIds: harvestFileIds,
    processorId: harvestProcessorId,
    enablesEstimation: harvestEnablesEstimation ?? true,
    priceList: {
      defaultHarvestType: harvestPriceList?.defaultHarvestType ?? 'headon',
      headOn: defaultProcessorPriceList.headOn,
      headless: defaultProcessorPriceList.headless,
      percentHarvestAsHeadsOn: null,
      percentHarvestAsHeadsOnClassA: null,
      percentHarvestAsHeadsOnClassB: null,
      percentHarvestAsLeftover: null,
      processLeftoverAs: harvestPriceList?.processLeftoverAs ?? 'headlessAsClassAB',
      percentLeftoverAsHeadlessClassA: null,
      percentLeftoverAsHeadlessClassB: null,
      percentHeadlessAsClassA: null,
      percentHeadlessAsClassB: null,
      percentTail: 67
    }
  };

  const { schema } = useYupSchema({
    date: Yup.date().transform(numberTransform).required(trans('t_required')),
    lbsHarvested: Yup.number().when('lbsHarvestedUnit', {
      is: 'lbsHa',
      then: () =>
        Yup.number()
          .transform(numberTransform)
          .nullable()
          .required(trans('t_required'))
          .max(lastHistoryBiomassHa, trans('t_exceeds_biomass_in_pond')),
      otherwise: () =>
        Yup.number()
          .transform(numberTransform)
          .nullable()
          .required(trans('t_required'))
          .max(lastHistoryBiomass, trans('t_exceeds_biomass_in_pond'))
    }),
    lbsHarvestedUnit: Yup.string().required(trans('t_required')),
    weight: Yup.number().transform(numberTransform).nullable().required(trans('t_required')),
    revenue: Yup.number().when('enablesEstimation', {
      is: true,
      then: () => Yup.number().transform(numberTransform).nullable(),
      otherwise: () => Yup.number().transform(numberTransform).nullable().required(trans('t_required'))
    }),
    survival: Yup.number().transform(numberTransform).nullable().min(1).max(100).required(trans('t_required')),
    fileIds: Yup.array().of(Yup.string()).label('t_liquidation_report'),
    processorId: Yup.string().required(trans('t_required')),
    enablesEstimation: Yup.boolean().nullable(),
    priceList: priceListSchema,
    quantity: Yup.number().transform(numberTransform).min(1, trans('t_partial_harvest_quantity_error')).nullable(),
    closestMonitoringDate: Yup.string().nullable(trans('t_required'))
  });

  const formMethods = useForm<RecordHarvestFormValues>({
    defaultValues: finalDefaultValues,
    mode: 'onChange',
    resolver: yupFormResolver(schema) as YupResolverType<RecordHarvestFormValues>
  });

  const {
    handleSubmit,
    reset,
    watch,
    setValue,
    trigger,
    formState: { errors },
    setError
  } = formMethods;

  const processorIdWatch = watch('processorId');
  const farmPriceList = activeProcessorPriceLists?.find((priceList) => priceList._id === processorIdWatch)?.priceList;

  //EFFECTS
  const { setPriceList } = useSetPriceListToHarvestForm({
    setValue,
    triggerState: triggerPriceListState,
    activeProcessorPriceLists,
    farmPriceList,
    harvestPriceList,
    defaultPercentTail: finalDefaultValues.priceList.percentTail
  });

  //START-HANDLERS
  const handleOnSubmit = handleSubmit(async (formData: RecordHarvestFormValues) => {
    const { date, lbsHarvested, priceList, survival, quantity, lbsHarvestedUnit, ...rest } = formData ?? {};
    if (quantityError) return;

    const {
      percentHarvestAsHeadsOn,
      percentHarvestAsHeadsOnClassA,
      percentHarvestAsHeadsOnClassB,
      percentHarvestAsLeftover,
      percentHeadlessAsClassA,
      percentHeadlessAsClassB,
      percentLeftoverAsHeadlessClassA,
      percentLeftoverAsHeadlessClassB,
      percentTail,
      ...restPriceList
    } = priceList ?? {};

    const survivalValue = survival ? survival / 100 : undefined;

    const harvestValue = convertUnitByMultiplication(lbsHarvested, unitsConfig?.biomass);

    const lbsHarvestValue = lbsHarvestedUnit === 'lbsHa' ? harvestValue * pondSize : harvestValue;

    const harvestData: PopulationPartialHarvestInput & { closestMonitoringDate?: string } = {
      ...rest,
      date: DateTime.fromJSDate(date).toFormat('yyyy-MM-dd'),
      lbsHarvested: lbsHarvestValue,
      processorPriceList: {
        ...finalDefaultValues.priceList,
        ...restPriceList,
        percentHarvestAsHeadsOn: getPercentageDivided(percentHarvestAsHeadsOn),
        percentHarvestAsHeadsOnClassA: getPercentageDivided(percentHarvestAsHeadsOnClassA),
        percentHarvestAsHeadsOnClassB: getPercentageDivided(percentHarvestAsHeadsOnClassB),
        percentHarvestAsLeftover: getPercentageDivided(percentHarvestAsLeftover),
        percentHeadlessAsClassA: getPercentageDivided(percentHeadlessAsClassA),
        percentHeadlessAsClassB: getPercentageDivided(percentHeadlessAsClassB),
        percentLeftoverAsHeadlessClassA: getPercentageDivided(percentLeftoverAsHeadlessClassA),
        percentLeftoverAsHeadlessClassB: getPercentageDivided(percentLeftoverAsHeadlessClassB),
        percentTail: getPercentageDivided(percentTail)
      },
      survival: survivalValue,
      quantity: isNumber(quantity) ? Math.round(quantity) : undefined
    };

    onSubmit(harvestData);
  });

  return (
    <FormProvider {...formMethods}>
      <chakra.form
        display='flex'
        flexDirection='column'
        gap='2lg'
        ref={containerRef}
        {...rest}
        onSubmit={(event) => {
          event.stopPropagation();
          handleOnSubmit(event).then();
        }}
        noValidate
        data-cy='add-edit-partial-harvest-form'
      >
        <EditHarvestConfirmationModal
          submitButtonRef={submitButtonRef}
          isOpen={isConfirmationOpen}
          onClose={onConfirmationClose}
          title={trans('t_partial_harvest_confirmation')}
          subTitle={trans('t_partial_harvest_change_description')}
          containerRef={containerRef}
        />

        {step === FormSteps.MainView && (
          <>
            <MainFormFields
              processorOptions={processorOptions}
              setStep={setStep}
              stockedAtTimezone={stockedAtTimezone}
              stockedAtDate={stockedAt}
              setPriceList={setPriceList}
              activeProcessorPriceLists={activeProcessorPriceLists}
            />
            <AccordionRoot
              collapsible
              defaultValue={['details']}
              multiple
              variant='plain'
              display='flex'
              flexDirection='column'
              gap='2lg'
            >
              <AccordionItem bgColor='white' borderRadius='2xl' py='md' value='details'>
                <AccordionItemTrigger cursor='pointer' py='sm-alt' px='md' textStyle='label.100'>
                  {trans('t_details')}
                </AccordionItemTrigger>
                <AccordionItemContent display='flex' flexDirection='column' gap='sm-alt' pb={0} pt='sm-alt' px='md'>
                  <DetailsFormFields
                    populationId={populationId}
                    seedingQuantity={seedingQuantity}
                    history={history}
                    survivalRate={survivalRate}
                    pondId={pondId}
                    pondSize={pondSize}
                    unitConfig={unitsConfig?.biomass}
                    isBiomassUnitLbs={isBiomassUnitLbs}
                    defaultRevenue={harvestRevenue}
                    survivalAtRecord={survivalAtRecord}
                    quantityError={quantityError}
                    setQuantityError={setQuantityError}
                  />
                </AccordionItemContent>
              </AccordionItem>
              <AccordionItem bgColor='white' borderRadius='2xl' py='md' value='quality'>
                <AccordionItemTrigger cursor='pointer' py='sm-alt' px='md' textStyle='label.100'>
                  {trans('t_harvest_quality')}
                </AccordionItemTrigger>
                <AccordionItemContent display='flex' flexDirection='column' gap='sm-alt' pb={0} pt='sm-alt' px='md'>
                  <HarvestQualityFormFields farmPriceList={farmPriceList} />
                </AccordionItemContent>
              </AccordionItem>
            </AccordionRoot>
          </>
        )}

        {step === FormSteps.PriceList && (
          <PriceListFormFields
            setStep={setStep}
            pondName={pondName}
            cycle={cycle}
            farmPriceList={farmPriceList}
            processorOptions={processorOptions}
          />
        )}

        {step === FormSteps.MainView && (
          <Flex
            justify='flex-end'
            bgColor='bg.gray.medium'
            gap='sm-alt'
            pos='sticky'
            bottom='0'
            insetEnd='0'
            py='lg'
            zIndex={10}
          >
            <BaseButton
              variant='secondary'
              bgColor='transparent'
              onClick={() => {
                reset(finalDefaultValues);
                onCancel();
              }}
            >
              {trans('t_cancel')}
            </BaseButton>
            <BaseButton
              type={isEdit ? 'button' : 'submit'}
              loading={isLoading}
              onClick={() => {
                isEdit && onConfirmationOpen();
              }}
            >
              {saveButtonText}
            </BaseButton>
            <button style={{ display: 'none' }} type='submit' ref={submitButtonRef}>
              {saveButtonText}
            </button>
          </Flex>
        )}
      </chakra.form>
    </FormProvider>
  );
}

interface DetailsFormFieldsProps
  extends Pick<
    AddEditPartialHarvestFormProps,
    'populationId' | 'pondId' | 'seedingQuantity' | 'history' | 'survivalRate' | 'survivalAtRecord'
  > {
  isBiomassUnitLbs: boolean;
  unitConfig: 'lbs' | 'kg';
  pondSize: number;
  defaultRevenue: number;
  quantityError: string;
  setQuantityError: Dispatch<SetStateAction<string>>;
}

function DetailsFormFields(props: DetailsFormFieldsProps) {
  const {
    pondId,
    pondSize,
    isBiomassUnitLbs,
    defaultRevenue,
    unitConfig,
    populationId,
    survivalRate,
    history,
    seedingQuantity,
    survivalAtRecord,
    quantityError,
    setQuantityError
  } = props;

  const { trans } = getTrans();
  const lang = useAppSelector((state) => state.app.lang);

  const {
    control,
    formState: { errors },
    setValue,
    watch,
    trigger,
    clearErrors,
    setError
  } = useFormContext<RecordHarvestFormValues>();

  const averageWeight = watch('weight');
  const lbsHarvested = watch('lbsHarvested');
  const lbsHarvestedUnit = watch('lbsHarvestedUnit');
  const date = watch('date');
  const fileIds = watch('fileIds');
  const revenue = watch('revenue');
  const watchedEnablesEstimation = watch('enablesEstimation');
  const lbsHarvestedValue = lbsHarvestedUnit === 'lbsHa' ? lbsHarvested * pondSize : lbsHarvested;
  const {
    remainingAnimals,
    animalsHarvested,
    percentageOfHarvested,
    remainingAnimalsPercent,
    firstHistoryDate,
    closestMonitoringDate,
    closestSurvival,
    survivalAtPercent,
    survivalAtDate
  } = useCalculateHarvestFormDetails({
    averageWeight,
    lbsHarvested: convertUnitByMultiplication(lbsHarvestedValue, unitConfig),
    date,
    seedingQuantity,
    history,
    survivalRate,
    survivalAtRecord
  });

  //START-EFFECTS

  useEffect(() => {
    setValue('closestSurvival', closestSurvival);
  }, [closestSurvival]);

  useEffect(() => {
    setValue('closestMonitoringDate', closestMonitoringDate);
  }, [closestMonitoringDate]);

  useEffect(() => {
    if (averageWeight) {
      if (animalsHarvested > remainingAnimals) {
        setQuantityError(trans('t_partial_harvest_quantity_error'));
      } else {
        setQuantityError('');
      }
    } else {
      setQuantityError('');
    }

    setValue('quantity', animalsHarvested);
  }, [animalsHarvested, remainingAnimals]);

  useEffect(() => {
    setValue('enablesEstimation', !revenue);
    trigger('revenue').then();
  }, [revenue]);

  useEffect(() => {
    if (!watchedEnablesEstimation) return;
    setValue('revenue', NaN);
  }, [watchedEnablesEstimation]);

  useEffect(() => {
    if (remainingAnimalsPercent >= 0) {
      setValue('survival', remainingAnimalsPercent);
      clearErrors('survival');
    } else {
      setValue('survival', NaN);
    }
  }, [remainingAnimalsPercent]);

  const quantityFullError = quantityError || errors?.quantity?.message;
  const survivalSubTitle = firstHistoryDate
    ? trans('t_survival_will_not_be_assigned_to_monitoring', {
        date: DateTime.fromISO(firstHistoryDate).toFormat('MMM dd, yyyy', { locale: lang })
      })
    : trans('t_survival_will_be_assigned_to', {
        date: DateTime.fromISO(closestMonitoringDate).toFormat('MMM dd, yyyy', { locale: lang })
      });

  const survivalAtDateFormatted = DateTime.fromISO(survivalAtDate).toFormat('MMM dd, yyyy', {
    locale: lang
  });

  const survivalAtRecordTitle = trans('t_survival_at_x', {
    date: survivalAtDateFormatted
  });

  const noBorderStyles = { borderBottom: 'none', pb: 0 };

  return (
    <>
      <HarvestFormOneRow label={trans('t_biomass_harvested')} {...noBorderStyles}>
        <Flex gap='2xs' align='center'>
          <BaseFormNumberInput
            name='lbsHarvested'
            id='lbsHarvested'
            control={control}
            required
            numericFormatProps={{
              decimalScale: 0
            }}
            minW='92px'
            maxW='92px'
            inputProps={{
              placeholder: trans('t_enter')
            }}
          />

          <FormControlReactSelect
            id='lbsHarvestedUnit'
            name='lbsHarvestedUnit'
            control={control}
            options={[
              { value: 'lbsHa', label: isBiomassUnitLbs ? trans('t_lb_over_ha') : trans('t_kg_over_ha') },
              { value: 'lbs', label: isBiomassUnitLbs ? trans('t_lb') : trans('t_kg') }
            ]}
            formControlProps={{ mb: errors?.lbsHarvested?.message ? 'md' : '0px' }}
            error={errors?.lbsHarvestedUnit?.message}
            isSearchable={false}
            selectControlStyles={{ ...harvestSelectControlStyles, minWidth: '92px', maxWidth: '92px' }}
            onSelectChange={(option) => {
              if (lbsHarvestedUnit === option.value || !lbsHarvested) return;
              const valueToPopulate = option.value === 'lbs' ? lbsHarvested * pondSize : lbsHarvested / pondSize;

              setValue('lbsHarvested', valueToPopulate);
            }}
          />
        </Flex>
      </HarvestFormOneRow>

      <HarvestFormOneRow label={trans('t_abw_g')}>
        <BaseFormNumberInput
          name='weight'
          id='weight'
          control={control}
          required
          numericFormatProps={{
            fixedDecimalScale: true,
            decimalScale: 2
          }}
          w='124px'
          inputProps={{
            placeholder: trans('t_enter')
          }}
        />
      </HarvestFormOneRow>

      <HarvestFormOneRow
        subTitleProps={{ color: quantityFullError ? 'red.500' : 'text.gray.weak' }}
        subTitle={quantityFullError}
        label={trans('t_estimated_animals_harvested')}
      >
        <Text size='label200' w='124px' textAlign='end'>
          {isNumber(animalsHarvested)
            ? formatNumber(animalsHarvested, { fractionDigits: 0, shouldRound: false, lang })
            : '-'}
        </Text>
      </HarvestFormOneRow>

      <HarvestFormOneRow label={trans('t_animals_harvested_%')}>
        <Text size='label200' w='124px' textAlign='end'>
          {isNumber(percentageOfHarvested) && percentageOfHarvested >= 0 ? `${percentageOfHarvested}%` : '-'}
        </Text>
      </HarvestFormOneRow>

      <HarvestFormOneRow label={survivalAtRecordTitle}>
        <Text size='label200' w='124px' textAlign='end'>
          {isNumber(survivalAtPercent) && survivalAtPercent >= 0 ? `${survivalAtPercent}%` : '-'}
        </Text>
      </HarvestFormOneRow>

      <HarvestFormOneRow label={trans('t_survival_%_after_partial_harvest')} subTitle={survivalSubTitle}>
        <BaseFormNumberInput
          name='survival'
          control={control}
          required
          numericFormatProps={{ decimalScale: 0, suffix: '%' }}
          w='124px'
          inputProps={{
            placeholder: trans('t_enter')
          }}
        />
      </HarvestFormOneRow>

      <AdminOnlyWrapper>
        <HarvestFormOneRow borderBottom='none' label={trans('t_partial_harvest_revenue')}>
          <BaseFormNumberInput
            name='revenue'
            control={control}
            numericFormatProps={{ fixedDecimalScale: true, suffix: '$', decimalScale: 0 }}
            w='124px'
            inputProps={{
              placeholder: trans('t_enter')
            }}
          />
        </HarvestFormOneRow>
        <Flex align='center' gap='sm-alt'>
          <Controller
            name='enablesEstimation'
            control={control}
            render={({ field: { value, onChange, ...rest } }) => {
              return (
                <BaseFormCheckbox
                  {...rest}
                  id='enablesEstimation'
                  label={trans('t_use_kampi_expected_revenue')}
                  formControlProps={{
                    w: 'max-content'
                  }}
                  data-cy='enablesEstimation'
                  checked={value}
                  onCheckedChange={({ checked }) => onChange(checked)}
                />
              );
            }}
          />

          {isNumber(defaultRevenue) && (
            <Text as='span' size='label200'>
              ({formatNumber(defaultRevenue, { fractionDigits: 0, lang, isCurrency: true })})
            </Text>
          )}
        </Flex>

        <Separator />

        <UploadReport
          pondId={pondId}
          populationId={populationId}
          defaultValues={fileIds}
          onSuccess={(ids) => setValue('fileIds', ids)}
        />
      </AdminOnlyWrapper>

      {errors?.fileIds?.message && (
        <Text color='red.500' fontSize='md'>
          {errors.fileIds.message}
        </Text>
      )}
    </>
  );
}
