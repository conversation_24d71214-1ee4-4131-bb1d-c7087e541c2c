import { Box, BoxProps, chakra, Flex, Heading, useDisclosure } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { yupResolver } from '@hookform/resolvers/yup';
import { useAnalytics } from '@hooks/use-analytics';
import { getTrans } from '@i18n/get-trans';
import { useReloadCurrentFarmPonds } from '@screens/farm/hooks/use-reload-current-farm-ponds';
import { actionsName } from '@utils/segment';
import { numberTransform, useYupSchema, Yup, YupResolverType } from '@utils/yup';
import { DateTime } from 'luxon';
import { ReactNode, useMemo, useRef, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import {
  convertUnitByDivision,
  convertUnitByMultiplication,
  formatNumber,
  getPercentageDivided,
  isNumber
} from '@utils/number';
import { useAppSelector } from '@redux/hooks';
import { EditHarvestConfirmationModal } from '@screens/pond/components/edit-harvest-confirmation-modal';
import { useUpdatePopulationApi } from '@screens/population/hooks/use-update-population-api';
import { useGetPopulationDetailsApi } from '@screens/population/hooks/use-get-population-details-api';
import { PopulationType } from '@redux/farm/set-current-population';
import { DialogContent, DialogRoot } from '@components/ui/dialog';
import ObjectID from 'bson-objectid';
import { AccordionItem, AccordionItemContent, AccordionItemTrigger, AccordionRoot } from '@components/ui/accordion';
import { useGetPriceListSchema } from '@screens/population/hooks/use-get-price-list-schema';
import { defaultProcessorPriceList } from '@screens/settings/constants/default-price-list';
import { HarvestPlanFormValues } from '@screens/population/components/harvest-modals/types';
import { FormSteps, PriceListFormFields } from '@screens/population/components/harvest-modals/price-list-form-fields';
import { MainFormFields } from '@screens/population/components/harvest-modals/main-form-fields';
import { HarvestQualityFormFields } from '@screens/population/components/harvest-modals/harvest-quality-form-fields';
import { useSetPriceListToHarvestForm } from '@screens/population/hooks/use-set-price-list-to-harvest-form';
import { CloseButton } from '@components/ui/close-button';
import { HarvestPlanFormProjectionDetails } from '@screens/population/components/harvest-modals/harvest-plan-form-projection-details';
import { PopulationPartialHarvest, PopulationPartialHarvestInput } from '@xpertsea/module-farm-sdk';
import { OmitRecursively } from '@utils/types';
import { useGetProcessorOptions } from '@screens/pond/hooks/use-get-processor-options';

interface AddEditPartialHarvestPlanModalProps {
  pondName: string;
  pondId: string;
  pondSize: number;
  harvestId?: string;
  children: ReactNode;
  defaultValues?: Omit<HarvestPlanFormValues, 'date'> & { date: string };
  isInPondView?: boolean;
  isInPondListView?: boolean;
  triggerContainerProps?: BoxProps;
  onCancel?: () => void;
  onSuccess?: () => void;
  population?: PopulationType;
}

export function AddEditPartialHarvestPlanModal(props: AddEditPartialHarvestPlanModalProps) {
  const {
    population,
    children,
    pondName,
    pondId,
    pondSize,
    harvestId,
    isInPondView = true,
    isInPondListView,
    triggerContainerProps = {},
    defaultValues,
    onCancel,
    onSuccess
  } = props;

  const { trans } = getTrans();

  //START-STATE HOOKS
  const { onOpen, onClose, open } = useDisclosure();
  const { onOpen: onConfirmationOpen, onClose: onConfirmationClose, open: isConfirmationOpen } = useDisclosure();
  const [step, setStep] = useState(FormSteps.MainView);

  const submitButtonRef = useRef<HTMLButtonElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  ///START-API HOOKS
  const [{ isLoading: isLoadingPopulation }, updatePopulation] = useUpdatePopulationApi();
  const { reloadCurrentFarmPonds } = useReloadCurrentFarmPonds();
  const [_, getPopulationDetails] = useGetPopulationDetailsApi();

  /// START-REDUX HOOKS
  const { trackAction } = useAnalytics();
  const { processorOptions, activeProcessorPriceLists } = useGetProcessorOptions();
  const { priceListSchema } = useGetPriceListSchema(false);
  const user = useAppSelector((state) => state.auth.user);
  const currentPopulation = useAppSelector((state) => state.farm?.currentPopulation);

  const populationToUse = population ?? currentPopulation;

  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const {
    partialHarvestPlanned,
    cycle,
    _id: populationId,
    stockedAt,
    stockedAtTimezone,
    history,
    harvestPlan,
    productionPrediction
  } = populationToUse ?? {};

  const lastHistoryItem = history?.[0];
  const { harvestDate: harvestPlanDate } = harvestPlan ?? {};

  const filteredProductionPrediction = useMemo(() => {
    if (!harvestPlanDate) return productionPrediction;
    return productionPrediction?.filter((item) => {
      if (!item.date) return false;
      const date = DateTime.fromISO(item.date).startOf('day');
      return date <= DateTime.fromISO(harvestPlanDate).startOf('day');
    });
  }, [JSON.stringify(productionPrediction ?? []), harvestPlanDate]);

  const {
    date: harvestDate,
    processorId: harvestProcessorId,
    priceList: harvestPriceList,
    lbsHarvested: harvestAmount
  } = defaultValues ?? {};

  const harvestIdToUse = harvestId ?? ObjectID().toHexString();
  const harvestAt = harvestDate ? DateTime.fromFormat(harvestDate, 'yyyy-MM-dd') : null;
  const lbsHarvestedValue = isNumber(harvestAmount)
    ? convertUnitByDivision(harvestAmount, unitsConfig?.biomass) / pondSize
    : null;

  const finalDefaultValues: HarvestPlanFormValues = {
    date: harvestAt?.toJSDate() ?? DateTime.local().toJSDate(),
    lbsHarvested: lbsHarvestedValue,
    lbsHarvestedUnit: 'lbsHa',
    processorId: harvestProcessorId,
    priceList: {
      defaultHarvestType: harvestPriceList?.defaultHarvestType ?? 'headon',
      headOn: defaultProcessorPriceList.headOn,
      headless: defaultProcessorPriceList.headless,
      percentHarvestAsHeadsOn: null,
      percentHarvestAsHeadsOnClassA: null,
      percentHarvestAsHeadsOnClassB: null,
      percentHarvestAsLeftover: null,
      processLeftoverAs: harvestPriceList?.processLeftoverAs ?? 'headlessAsClassAB',
      percentLeftoverAsHeadlessClassA: null,
      percentLeftoverAsHeadlessClassB: null,
      percentHeadlessAsClassA: null,
      percentHeadlessAsClassB: null,
      percentTail: 67
    }
  };

  const { schema } = useYupSchema({
    date: Yup.date().transform(numberTransform).required(trans('t_required')),
    lbsHarvested: Yup.number()
      .transform(numberTransform)
      .required(trans('t_required'))
      .min(1, trans('t_min_x', { min: 1 }))
      .test('checkBiomass', trans('t_exceeds_biomass_in_pond'), (value, context) => {
        const { date, lbsHarvestedUnit } = context?.parent ?? {};
        let maxBiomass = 0;
        let maxBiomassHa = 0;

        if (filteredProductionPrediction) {
          const dateFormatted = DateTime.fromJSDate(date).startOf('day').toFormat('yyyy-MM-dd');
          const productionPredictionOnSelectedDate = filteredProductionPrediction.find(
            (item) => item.date === dateFormatted
          );
          const productionPredictionItem =
            productionPredictionOnSelectedDate ?? filteredProductionPrediction[filteredProductionPrediction.length - 1];
          const { biomassLbs, biomassLbsByHa } = productionPredictionItem ?? {};

          maxBiomass = biomassLbs
            ? (formatNumber(convertUnitByDivision(biomassLbs, unitsConfig?.biomass), {
                asNumber: true,
                fractionDigits: 0
              }) as number)
            : 0;
          maxBiomassHa = biomassLbsByHa
            ? (formatNumber(convertUnitByDivision(biomassLbsByHa, unitsConfig?.biomass), {
                asNumber: true,
                fractionDigits: 0
              }) as number)
            : 0;
        } else {
          const { biomassLbs, biomassLbsByHa } = lastHistoryItem ?? {};
          maxBiomass = biomassLbs
            ? (formatNumber(convertUnitByDivision(biomassLbs, unitsConfig?.biomass), {
                asNumber: true,
                fractionDigits: 0
              }) as number)
            : 0;
          maxBiomassHa = biomassLbsByHa
            ? (formatNumber(convertUnitByDivision(biomassLbsByHa, unitsConfig?.biomass), {
                asNumber: true,
                fractionDigits: 0
              }) as number)
            : 0;
        }

        const maxValue = lbsHarvestedUnit === 'lbsHa' ? maxBiomassHa : maxBiomass;
        return !value || value <= maxValue;
      }),
    lbsHarvestedUnit: Yup.string().required(trans('t_required')),
    processorId: Yup.string().required(trans('t_required')),
    priceList: priceListSchema
  });

  const formMethods = useForm<HarvestPlanFormValues>({
    resolver: yupResolver(schema) as YupResolverType<HarvestPlanFormValues>,
    defaultValues: finalDefaultValues,
    mode: 'onChange'
  });

  const { reset, handleSubmit, watch, setValue } = formMethods;

  const processorIdWatch = watch('processorId');

  const farmPriceList = activeProcessorPriceLists?.find((priceList) => priceList._id === processorIdWatch)?.priceList;

  //EFFECTS
  const { setPriceList } = useSetPriceListToHarvestForm({
    setValue,
    triggerState: open,
    activeProcessorPriceLists,
    farmPriceList,
    harvestPriceList,
    defaultPercentTail: finalDefaultValues.priceList.percentTail
  });

  // START-HANDLERS
  const handleModalOpen = () => {
    onOpen();
    reset(finalDefaultValues);
  };

  const handleModalClose = () => {
    onClose();
    onCancel?.();
    setStep(FormSteps.MainView);
  };

  const onReload = () => {
    if (isInPondView) return getPopulationDetails({ params: { filter: { populationId } }, loadRelated: true });
    if (isInPondListView) return reloadCurrentFarmPonds();
  };

  const onSubmit = handleSubmit((data: HarvestPlanFormValues) => {
    const { date, priceList, lbsHarvested, lbsHarvestedUnit, ...rest } = data ?? {};
    const {
      percentHarvestAsHeadsOn,
      percentHarvestAsHeadsOnClassA,
      percentHarvestAsHeadsOnClassB,
      percentHarvestAsLeftover,
      percentHeadlessAsClassA,
      percentHeadlessAsClassB,
      percentLeftoverAsHeadlessClassA,
      percentLeftoverAsHeadlessClassB,
      percentTail,
      ...restPriceList
    } = priceList ?? {};

    const harvestValue = convertUnitByMultiplication(lbsHarvested, unitsConfig?.biomass);

    const lbsHarvestValue = lbsHarvestedUnit === 'lbsHa' ? harvestValue * pondSize : harvestValue;

    const harvestData: PopulationPartialHarvestInput = {
      ...rest,
      date: DateTime.fromJSDate(date).toFormat('yyyy-MM-dd'),
      lbsHarvested: lbsHarvestValue,
      harvestId: harvestIdToUse,
      processorPriceList: {
        ...finalDefaultValues.priceList,
        ...restPriceList,
        percentHarvestAsHeadsOn: getPercentageDivided(percentHarvestAsHeadsOn),
        percentHarvestAsHeadsOnClassA: getPercentageDivided(percentHarvestAsHeadsOnClassA),
        percentHarvestAsHeadsOnClassB: getPercentageDivided(percentHarvestAsHeadsOnClassB),
        percentHarvestAsLeftover: getPercentageDivided(percentHarvestAsLeftover),
        percentHeadlessAsClassA: getPercentageDivided(percentHeadlessAsClassA),
        percentHeadlessAsClassB: getPercentageDivided(percentHeadlessAsClassB),
        percentLeftoverAsHeadlessClassA: getPercentageDivided(percentLeftoverAsHeadlessClassA),
        percentLeftoverAsHeadlessClassB: getPercentageDivided(percentLeftoverAsHeadlessClassB),
        percentTail: getPercentageDivided(percentTail)
      }
    };

    let partialHarvestPlannedData: OmitRecursively<PopulationPartialHarvest, '__typename'>[] =
      partialHarvestPlanned ?? [];
    if (harvestId) {
      partialHarvestPlannedData = partialHarvestPlannedData.map((item) => {
        if (item.harvestId === harvestId) {
          // The backend calculates quantity and revenue during the projection step.
          // Omitting them here ensures the backend performs this calculation.
          const { quantity, revenue, ...rest } = item;
          return {
            ...rest,
            ...harvestData
          };
        }
        return item;
      });
    } else {
      partialHarvestPlannedData = partialHarvestPlannedData.concat([harvestData]);
    }

    updatePopulation({
      params: {
        filter: { populationId },
        set: {
          partialHarvestPlanned: partialHarvestPlannedData
        }
      },
      successCallback() {
        onSuccess?.();
        onClose();
        onReload();
        trackAction(harvestId ? actionsName.pondPartialHarvestPlanEdited : actionsName.pondPartialHarvestPlanAdded, {
          pondId,
          pondName,
          harvestId,
          harvestDate
        }).then();
      }
    });
  });

  return (
    <DialogRoot
      open={open}
      onOpenChange={(e) => {
        e.open ? handleModalOpen() : handleModalClose();
      }}
      restoreFocus={false}
      size='lg'
      scrollBehavior='inside'
    >
      <Box
        w='fit-content'
        onClick={(e) => {
          e.stopPropagation();
          handleModalOpen();
        }}
        {...triggerContainerProps}
      >
        {children}
      </Box>

      <DialogContent
        data-cy='harvest-population-modal'
        borderRadius='2xl'
        px='lg'
        py={0}
        bgColor='bg.gray.medium'
        ref={containerRef}
        closeTrigger={false}
      >
        <EditHarvestConfirmationModal
          submitButtonRef={submitButtonRef}
          isOpen={isConfirmationOpen}
          onClose={onConfirmationClose}
          title={trans('t_edit_partial_harvest_plan_confirmation')}
          subTitle={trans('t_partial_harvest_plan_change_description')}
          containerRef={containerRef}
        />

        {step === FormSteps.MainView && (
          <Flex
            pos='sticky'
            bgColor='bg.gray.medium'
            top={0}
            align='center'
            justify='space-between'
            gap='sm-alt'
            pt='lg'
            pb='2lg'
            zIndex={10}
          >
            <Heading size='heavy300'>
              {trans('t_x_partial_harvest_plan_title', {
                action: harvestId ? trans('t_edit') : trans('t_add'),
                pondName,
                cycle
              })}
            </Heading>
            <CloseButton size='sm' onClick={handleModalClose} />
          </Flex>
        )}

        <FormProvider {...formMethods}>
          <chakra.form
            display='flex'
            flexDirection='column'
            gap='2lg'
            onSubmit={(event) => {
              event.stopPropagation();
              onSubmit(event).then();
            }}
            noValidate
            data-cy='add-edit-final-harvest-form'
          >
            {step === FormSteps.MainView && (
              <>
                <MainFormFields
                  processorOptions={processorOptions}
                  setStep={setStep}
                  stockedAtTimezone={stockedAtTimezone}
                  stockedAtDate={stockedAt}
                  setPriceList={setPriceList}
                  activeProcessorPriceLists={activeProcessorPriceLists}
                  isPlannedPartial
                  pondSize={pondSize}
                  minDate={lastHistoryItem?.date}
                  maxDate={harvestPlanDate}
                />
                <AccordionRoot
                  collapsible
                  defaultValue={['quality', 'projected-details']}
                  multiple
                  variant='plain'
                  display='flex'
                  flexDirection='column'
                  gap='2lg'
                >
                  <AccordionItem bgColor='white' borderRadius='2xl' py='md' value='quality'>
                    <AccordionItemTrigger cursor='pointer' py='sm-alt' px='md' textStyle='label.100'>
                      {trans('t_harvest_quality')}
                    </AccordionItemTrigger>
                    <AccordionItemContent display='flex' flexDirection='column' gap='sm-alt' pb={0} pt='sm-alt' px='md'>
                      <HarvestQualityFormFields farmPriceList={farmPriceList} />
                    </AccordionItemContent>
                  </AccordionItem>
                  <AccordionItem bgColor='white' borderRadius='2xl' py='md' value='projected-details'>
                    <AccordionItemTrigger cursor='pointer' py='sm-alt' px='md' textStyle='label.100'>
                      {trans('t_projected_harvest_details')}
                    </AccordionItemTrigger>
                    <AccordionItemContent display='flex' flexDirection='column' gap='sm-alt' pb={0} pt='sm-alt' px='md'>
                      <HarvestPlanFormProjectionDetails
                        pondSize={pondSize}
                        populationId={populationId}
                        harvestId={harvestIdToUse}
                        hasProjection={!!harvestPlan?.projection?.profitProjectionData?.length}
                      />
                    </AccordionItemContent>
                  </AccordionItem>
                </AccordionRoot>
              </>
            )}
            {step === FormSteps.PriceList && (
              <PriceListFormFields
                setStep={setStep}
                pondName={pondName}
                cycle={cycle}
                farmPriceList={farmPriceList}
                processorOptions={processorOptions}
              />
            )}

            {step === FormSteps.MainView && (
              <Flex
                justify='flex-end'
                bgColor='bg.gray.medium'
                gap='sm-alt'
                pos='sticky'
                bottom='0'
                insetEnd='0'
                py='lg'
                zIndex={10}
              >
                <BaseButton
                  variant='secondary'
                  bgColor='transparent'
                  onClick={handleModalClose}
                  analyticsId={actionsName.pondOverviewPartialHarvestPlanCancelClicked}
                >
                  {trans('t_cancel')}
                </BaseButton>
                <BaseButton
                  type={harvestId ? 'button' : 'submit'}
                  loading={isLoadingPopulation}
                  onClick={() => {
                    harvestId && onConfirmationOpen();
                  }}
                  analyticsId={actionsName.pondOverviewPartialHarvestPlanSaveClicked}
                >
                  {trans('t_save_changes')}
                </BaseButton>
                <button style={{ display: 'none' }} type='submit' ref={submitButtonRef}>
                  {trans('t_save_changes')}
                </button>
              </Flex>
            )}
          </chakra.form>
        </FormProvider>
      </DialogContent>
    </DialogRoot>
  );
}
