import { PriceList, RecordHarvestFormValues } from '@screens/population/components/harvest-modals/types';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import isUndefined from 'lodash/isUndefined';
import { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { useCalculateHarvestRevenueApi } from '@screens/population/hooks/use-calculate-harvest-revenue-api';
import { DateTime } from 'luxon';
import isEqual from 'lodash/isEqual';
import { Flex, Skeleton, Text } from '@chakra-ui/react';
import {
  convertUnitByDivision,
  convertUnitByMultiplication,
  formatNumber,
  getPercentageDivided,
  isNumber
} from '@utils/number';
import { sqPerHectare } from '@utils/constants';
import { BaseButton } from '@components/base/base-button';
import { RefreshIcon } from '@icons/refresh/refresh-icon';
import { HarvestFormOneRow } from '@screens/pond/components/modals/harvest-form-one-row';
import { AdminOnlyWrapper } from '@components/permission-wrappers/admin-only-wrapper';
import cloneDeep from 'lodash/cloneDeep';
import { InfoIconToolTip } from '@components/tooltip/info-icon-tool-tip';

type SubmittedData = {
  date: string;
  harvestId: string;
  processorId: string;
  processorPriceList: PriceList;
  lbsHarvested: number;
};

interface HarvestPlanFormProjectionDetailsProps {
  populationId: string;
  harvestId: string;
  pondSize: number;
  hasProjection: boolean;
}

export function HarvestPlanFormProjectionDetails(props: HarvestPlanFormProjectionDetailsProps) {
  const { populationId, harvestId, pondSize, hasProjection } = props;
  const { trans } = getTrans();
  const lang = useAppSelector((state) => state.app.lang);
  const user = useAppSelector((state) => state.auth.user);

  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const biomassUnit = unitsConfig?.biomass;
  const isBiomassUnitLbs = biomassUnit === 'lbs' || isUndefined(biomassUnit);

  const [submittedData, setSubmittedData] = useState<SubmittedData>();

  const { watch } = useFormContext<RecordHarvestFormValues>();
  const [{ isLoading, data: generatedData }, generateHarvestRevenue] = useCalculateHarvestRevenueApi();

  const processorPriceList = watch('priceList');
  const selectedProcessorId = watch('processorId');
  const currentLbsHarvested = watch('lbsHarvested');
  const lbsHarvestedUnit = watch('lbsHarvestedUnit');
  const date = watch('date');
  const canGenerate = date && !!selectedProcessorId;
  const dateFormatted = DateTime.fromJSDate(date).toFormat('yyyy-MM-dd');

  const checkIfDataChanged = () => {
    if (!submittedData?.date) return false;
    const {
      date,
      harvestId: submittedHarvestId,
      processorId,
      processorPriceList: submittedPriceList,
      lbsHarvested
    } = submittedData;

    return (
      date !== dateFormatted ||
      harvestId !== submittedHarvestId ||
      processorId !== selectedProcessorId ||
      currentLbsHarvested !== lbsHarvested ||
      !isEqual(processorPriceList, submittedPriceList)
    );
  };

  const handleGenerateHarvestRevenue = () => {
    if (!canGenerate) return;
    const currentLbsHarvestedValue =
      lbsHarvestedUnit === 'lbsHa'
        ? convertUnitByMultiplication(currentLbsHarvested * pondSize, biomassUnit)
        : convertUnitByMultiplication(currentLbsHarvested, biomassUnit);

    const commonData = {
      date: dateFormatted,
      harvestId,
      processorId: selectedProcessorId,
      processorPriceList: cloneDeep(processorPriceList),
      lbsHarvested: isNumber(currentLbsHarvested) ? currentLbsHarvestedValue : undefined
    };
    const {
      percentHarvestAsHeadsOn,
      percentHarvestAsHeadsOnClassA,
      percentHarvestAsHeadsOnClassB,
      percentHarvestAsLeftover,
      percentHeadlessAsClassA,
      percentHeadlessAsClassB,
      percentLeftoverAsHeadlessClassA,
      percentLeftoverAsHeadlessClassB,
      percentTail,
      ...rest
    } = commonData.processorPriceList ?? {};
    generateHarvestRevenue({
      params: {
        populationId,
        harvestInfo: {
          ...commonData,
          harvestType: processorPriceList.defaultHarvestType,
          processorPriceList: {
            ...rest,
            percentHarvestAsHeadsOn: getPercentageDivided(percentHarvestAsHeadsOn),
            percentHarvestAsHeadsOnClassA: getPercentageDivided(percentHarvestAsHeadsOnClassA),
            percentHarvestAsHeadsOnClassB: getPercentageDivided(percentHarvestAsHeadsOnClassB),
            percentHarvestAsLeftover: getPercentageDivided(percentHarvestAsLeftover),
            percentHeadlessAsClassA: getPercentageDivided(percentHeadlessAsClassA),
            percentHeadlessAsClassB: getPercentageDivided(percentHeadlessAsClassB),
            percentLeftoverAsHeadlessClassA: getPercentageDivided(percentLeftoverAsHeadlessClassA),
            percentLeftoverAsHeadlessClassB: getPercentageDivided(percentLeftoverAsHeadlessClassB),
            percentTail: getPercentageDivided(percentTail)
          }
        }
      },
      successCallback: () => {
        setSubmittedData(commonData);
      }
    });
  };

  if (isLoading) {
    const emptyArrayOf8Items = new Array(8).fill(0);
    return (
      <>
        {emptyArrayOf8Items.map((_, index) => {
          return (
            <Flex key={index} justify='space-between' h='16px'>
              <Skeleton w='220px' />
              <Skeleton w='100px' />
            </Flex>
          );
        })}
      </>
    );
  }
  const isSubmittedDataChange = checkIfDataChanged();
  const shouldShowGenerate = (isSubmittedDataChange || !generatedData) && hasProjection;
  const { averageWeight, lbsHarvested, lbsHarvestedByHa, quantity, revenue, revenuePoundHarvest } =
    generatedData?.harvestRevenue ?? {};
  const biomassUnitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');
  const biomassUnitLabelHa = isBiomassUnitLbs ? trans('t_lb_over_ha') : trans('t_kg_over_ha');
  const biomass = convertUnitByDivision(lbsHarvested, biomassUnit);
  const revenueByUnit = convertUnitByMultiplication(revenuePoundHarvest, biomassUnit);
  const biomassHa = convertUnitByDivision(lbsHarvestedByHa, biomassUnit);
  const animalsHarvestedHa = quantity / pondSize;
  const animalsHarvestedM2 = animalsHarvestedHa / sqPerHectare;
  const revenueUnitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');

  return (
    <>
      {!canGenerate && shouldShowGenerate && (
        <Text color='red.500' size='label200'>
          {trans('t_choose_a_date_and_processor')}
        </Text>
      )}
      {!hasProjection && (
        <Flex align='center'>
          <Text size='label200'>{trans('t_projections_not_available')}</Text>

          <InfoIconToolTip>
            <Text>{trans('t_projections_not_available_for_pond')}</Text>
          </InfoIconToolTip>
        </Flex>
      )}
      {shouldShowGenerate && (
        <Flex p='sm-alt' bgColor='bg.gray.medium' borderRadius='lg' align='center' justify='space-between'>
          <Text size='label200'>
            {isSubmittedDataChange
              ? trans('t_newly_computed_harvest_projections')
              : trans('t_generate_harvest_projections')}
          </Text>
          <BaseButton p={0} size='sm' variant='link' disabled={!canGenerate} onClick={handleGenerateHarvestRevenue}>
            <RefreshIcon color={!canGenerate ? 'text.gray.disabled' : 'button.link'} />
            {isSubmittedDataChange ? trans('t_refresh') : trans('t_generate')}
          </BaseButton>
        </Flex>
      )}
      {!!generatedData && (
        <>
          <HarvestFormOneRow label={trans('t_abw_g')}>
            <Text size='label200' textAlign='end'>
              {isNumber(averageWeight) ? formatNumber(averageWeight, { lang, fractionDigits: 2 }) : '-'}
            </Text>
          </HarvestFormOneRow>
          <HarvestFormOneRow label={isBiomassUnitLbs ? trans('t_biomass_lb') : trans('t_biomass_kg')}>
            <Text size='label200' textAlign='end'>
              {isNumber(biomass)
                ? `${formatNumber(biomass, { lang, fractionDigits: 0, shouldRound: false, shouldAddZeros: false })} ${biomassUnitLabel}`
                : '-'}
            </Text>
          </HarvestFormOneRow>
          <HarvestFormOneRow label={isBiomassUnitLbs ? trans('t_biomass_lb_ha') : trans('t_biomass_kg_ha')}>
            <Text size='label200' textAlign='end'>
              {isNumber(biomassHa)
                ? `${formatNumber(biomassHa, { lang, fractionDigits: 0, shouldRound: false, shouldAddZeros: false })} ${biomassUnitLabelHa}`
                : '-'}
            </Text>
          </HarvestFormOneRow>
          <HarvestFormOneRow label={trans('t_animals_harvested')}>
            <Text size='label200' textAlign='end'>
              {isNumber(quantity)
                ? formatNumber(quantity, { lang, fractionDigits: 0, shouldRound: false, shouldAddZeros: false })
                : '-'}
            </Text>
          </HarvestFormOneRow>
          <HarvestFormOneRow label={trans('t_animals_harvested_ha')}>
            <Text size='label200' textAlign='end'>
              {isNumber(animalsHarvestedHa)
                ? `${formatNumber(animalsHarvestedHa, {
                    lang,
                    fractionDigits: 0,
                    shouldRound: false,
                    shouldAddZeros: false
                  })} ${trans('t_over_ha')}`
                : '-'}
            </Text>
          </HarvestFormOneRow>
          <HarvestFormOneRow label={trans('t_animals_harvested_per_m2')}>
            <Text size='label200' textAlign='end'>
              {isNumber(animalsHarvestedM2)
                ? `${formatNumber(animalsHarvestedM2, {
                    lang,
                    fractionDigits: 2,
                    shouldRound: false,
                    shouldAddZeros: false
                  })} ${trans('t_over_m2')}`
                : '-'}
            </Text>
          </HarvestFormOneRow>
          <AdminOnlyWrapper>
            <HarvestFormOneRow label={trans('t_revenue')}>
              <Text size='label200' textAlign='end'>
                {isNumber(revenue) ? formatNumber(revenue, { lang, fractionDigits: 0, isCurrency: true }) : '-'}
              </Text>
            </HarvestFormOneRow>
            <HarvestFormOneRow
              label={trans('t_revenue_per_unit_harvested', { unit: revenueUnitLabel })}
              borderBottom='none'
            >
              <Text size='label200' textAlign='end'>
                {isNumber(revenueByUnit)
                  ? formatNumber(revenueByUnit, { lang, fractionDigits: 2, isCurrency: true })
                  : '-'}
              </Text>
            </HarvestFormOneRow>
          </AdminOnlyWrapper>
        </>
      )}
    </>
  );
}
