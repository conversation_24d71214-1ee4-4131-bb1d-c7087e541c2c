import { Flex, Heading, Table, Text } from '@chakra-ui/react';
import { Dispatch, SetStateAction, useState } from 'react';
import { getTrans } from '@i18n/get-trans';
import { useFieldArray, useFormContext } from 'react-hook-form';
import isEqual from 'lodash/isEqual';
import { BaseButton } from '@components/base/base-button';
import { ArrowBigLeftFilled } from '@icons/arrow-big-left/arrow-big-left-filled';
import { HarvestFormOneRow } from '@screens/pond/components/modals/harvest-form-one-row';
import { FormControlReactSelect } from '@components/form/form-control-react-select';
import { harvestSelectControlStyles } from '@screens/pond/components/modals/helpers/chakra-form-styles';
import { TableContainer } from '@components/ui/table-container';
import { ArrowReverseDuotoneIcon } from '@icons/arrow-reverse/arrow-reverse-duotone-icon';
import { BaseFormNumberInput } from '@components/form/base-form-number-input';
import {
  HeadOnTableType,
  PriceList,
  RecordHarvestFormValues
} from '@screens/population/components/harvest-modals/types';

export enum FormSteps {
  MainView = 0,
  PriceList = 1
}

interface PriceListFormFieldsProps {
  farmPriceList: PriceList;
  processorOptions: { value: string; label: string }[];
  setStep: Dispatch<SetStateAction<FormSteps>>;
  pondName: string;
  cycle: string;
}
export function PriceListFormFields(props: PriceListFormFieldsProps) {
  const { processorOptions, farmPriceList, setStep, pondName, cycle } = props;
  const { trans } = getTrans();
  const {
    control,
    formState: { errors },
    setValue,
    watch
  } = useFormContext<RecordHarvestFormValues>();

  const [showErrors, setShowErrors] = useState(false);

  const processorId = watch('processorId');
  const headOn = watch('priceList.headOn');
  const headless = watch('priceList.headless');
  const priceListHarvestType = watch('priceList.defaultHarvestType');
  const isHeadOn = priceListHarvestType === 'headon';

  const selectedProcessor = processorOptions?.find((option) => option.value === processorId);
  const farmHeadOn = farmPriceList?.headOn;
  const farmHeadless = farmPriceList?.headless;

  const { fields: priceListHeadOnFields } = useFieldArray<RecordHarvestFormValues>({
    control,
    name: 'priceList.headOn'
  });

  const { fields: priceListHeadlessFields } = useFieldArray<RecordHarvestFormValues>({
    control,
    name: 'priceList.headless'
  });

  const priceListFields = isHeadOn ? priceListHeadOnFields : priceListHeadlessFields;
  const isDefaultValues = isEqual(farmHeadOn, headOn) && isEqual(farmHeadless, headless);

  const priceListErrors = errors?.priceList?.headOn || errors?.priceList?.headless;
  return (
    <>
      <Flex
        direction='column'
        gap='2lg'
        pos='sticky'
        bgColor='bg.gray.medium'
        top={0}
        pt='lg'
        pb='md'
        mb='2sm'
        zIndex={10}
      >
        <Flex direction='column' gap='sm-alt'>
          <BaseButton
            p={0}
            variant='link'
            color='text.gray'
            onClick={() => {
              if (priceListErrors) {
                setShowErrors(true);
                return;
              }
              setStep(FormSteps.MainView);
            }}
          >
            <ArrowBigLeftFilled />
            <Heading as='span' size='heavy300'>
              {trans('t_back')}
            </Heading>
          </BaseButton>
          {showErrors && (
            <Text color='red.500' fontWeight='bold' size='label200'>
              {trans('t_check_price_list_errors')}
            </Text>
          )}
        </Flex>

        <Heading size='heavy300'>
          {trans('t_price_list_for_pond_cycle_title', {
            pondName,
            cycle
          })}
        </Heading>
      </Flex>

      <Flex p='md' bgColor='white' borderRadius='2xl' direction='column' gap='sm-alt'>
        <HarvestFormOneRow label={trans('t_processor')}>
          <Text size='label200'> {selectedProcessor?.label ?? '-'} </Text>
        </HarvestFormOneRow>
        <HarvestFormOneRow label={trans('t_harvest_type')} borderBottom='none'>
          <FormControlReactSelect
            id='priceList.defaultHarvestType'
            name='priceList.defaultHarvestType'
            control={control}
            placeholder={trans('t_select')}
            options={[
              { value: 'headon', label: trans('t_head_on') },
              { value: 'headless', label: trans('t_headless') }
            ]}
            error={errors?.priceList?.defaultHarvestType?.message}
            isSearchable={false}
            selectControlStyles={harvestSelectControlStyles}
          />
        </HarvestFormOneRow>
      </Flex>
      <TableContainer p='md' bgColor='white' borderRadius='2xl' mb='lg'>
        <Text size='label100' mb='lg'>
          {isHeadOn ? trans('t_head_on') : trans('t_headless')}{' '}
          <span style={{ textTransform: 'lowercase' }}>{trans('t_price_list')}</span>
        </Text>
        {!isDefaultValues && (
          <Flex p='sm-alt' mb='lg' bgColor='bg.gray.medium' borderRadius='lg' align='center' justify='space-between'>
            <Text size='label200'>{trans('t_standard_price_list_not_applied')}</Text>
            <BaseButton
              p={0}
              size='xs'
              variant='link'
              color='text.gray'
              onClick={() => {
                setValue('priceList.headOn', farmHeadOn);
                setValue('priceList.headless', farmHeadless);
              }}
            >
              <ArrowReverseDuotoneIcon boxSize='16px' />
              {trans('t_revert_to_default')}
            </BaseButton>
          </Flex>
        )}
        <Table.Root>
          <Table.Header>
            <Table.Row border='none'>
              <TableHeaderCell title={trans('t_class_size')} />
              <TableHeaderCell
                title={trans('t_class_a_price_unit', { unit: isHeadOn ? trans('t_kg') : trans('t_lb') })}
              />
              <TableHeaderCell
                title={trans('t_class_b_price_unit', { unit: isHeadOn ? trans('t_kg') : trans('t_lb') })}
              />
              {!isHeadOn && (
                <TableHeaderCell
                  title={trans('t_price_rejection_unit', { unit: isHeadOn ? trans('t_kg') : trans('t_lb') })}
                />
              )}
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {priceListFields.map((field, index) => {
              const fieldTyped = field as unknown as HeadOnTableType;
              const headKey = isHeadOn ? 'headOn' : 'headless';
              const isLastField = index === priceListFields.length - 1;
              const borderBottom = isLastField ? 'none' : '0.5px solid {colors.border.gray.weak}';
              return (
                <Table.Row key={`${priceListHarvestType}-${field.id}-${index}`} border='none'>
                  <Table.Cell textAlign='center' borderBottom={borderBottom}>
                    <Text size='label200'>{fieldTyped.size}</Text>
                  </Table.Cell>
                  <Table.Cell textAlign='center' pb='xs-alt' borderBottom={borderBottom}>
                    <Flex justify='center' align='center'>
                      <BaseFormNumberInput
                        name={`priceList.${headKey}.${index}.priceA`}
                        id={`priceList.${headKey}.${index}.priceA`}
                        control={control}
                        numericFormatProps={{
                          decimalScale: 2
                        }}
                        w='124px'
                        inputProps={{
                          placeholder: trans('t_enter')
                        }}
                      />
                    </Flex>
                  </Table.Cell>
                  <Table.Cell textAlign='center' pb='xs-alt' borderBottom={borderBottom}>
                    <Flex justify='center' align='center'>
                      <BaseFormNumberInput
                        name={`priceList.${headKey}.${index}.priceB`}
                        id={`priceList.${headKey}.${index}.priceB`}
                        control={control}
                        numericFormatProps={{
                          decimalScale: 2
                        }}
                        w='124px'
                        inputProps={{
                          placeholder: trans('t_enter')
                        }}
                      />
                    </Flex>
                  </Table.Cell>

                  {!isHeadOn && (
                    <Table.Cell textAlign='center' pb='xs-alt' borderBottom={borderBottom}>
                      <Flex justify='center' align='center'>
                        <BaseFormNumberInput
                          name={`priceList.${headKey}.${index}.priceRejection`}
                          id={`priceList.${headKey}.${index}.priceRejection`}
                          control={control}
                          numericFormatProps={{
                            decimalScale: 2
                          }}
                          w='124px'
                          inputProps={{
                            placeholder: trans('t_enter')
                          }}
                        />
                      </Flex>
                    </Table.Cell>
                  )}
                </Table.Row>
              );
            })}
          </Table.Body>
        </Table.Root>
      </TableContainer>
    </>
  );
}

function TableHeaderCell({ title }: { title: string }) {
  return (
    <Table.ColumnHeader border='none' textAlign='center' p='0'>
      <Text bgColor='gray.100' py='sm' borderRadius='xl' mx='2xs' size='label200'>
        {title}
      </Text>
    </Table.ColumnHeader>
  );
}
