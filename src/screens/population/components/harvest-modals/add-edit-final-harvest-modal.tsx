import { Box, BoxProps, chakra, Flex, Heading, Separator, Text, useDisclosure } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { yupResolver } from '@hookform/resolvers/yup';
import { useAnalytics } from '@hooks/use-analytics';
import { getTrans } from '@i18n/get-trans';
import { useReloadCurrentFarmPonds } from '@screens/farm/hooks/use-reload-current-farm-ponds';
import { actionsName } from '@utils/segment';
import { numberTransform, useYupSchema, Yup, YupResolverType } from '@utils/yup';
import { DateTime } from 'luxon';
import { ReactNode, useEffect, useRef, useState } from 'react';
import { Controller, FormProvider, useForm, useFormContext } from 'react-hook-form';
import { UploadReport } from '@screens/population/components/upload-liquidation-report';
import { BaseFormNumberInput } from '@components/form/base-form-number-input';
import {
  convertUnitByDivision,
  convertUnitByMultiplication,
  formatNumber,
  getPercentageDivided,
  isNumber
} from '@utils/number';
import { useAppSelector } from '@redux/hooks';
import { useHarvestPondApi } from '@screens/population/hooks/use-harvest-pond-api';
import { AdminOnlyWrapper } from '@components/permission-wrappers/admin-only-wrapper';
import isUndefined from 'lodash/isUndefined';
import { HarvestFormOneRow } from '@screens/pond/components/modals/harvest-form-one-row';
import { EditHarvestConfirmationModal } from '@screens/pond/components/edit-harvest-confirmation-modal';
import { BaseFormCheckbox } from '@components/form/base-form-checkbox';
import { useUpdatePopulationApi } from '@screens/population/hooks/use-update-population-api';
import { useGetPopulationDetailsApi } from '@screens/population/hooks/use-get-population-details-api';
import { PopulationHarvestInput } from '@xpertsea/module-farm-sdk';
import { PopulationType } from '@redux/farm/set-current-population';
import { DialogContent, DialogRoot } from '@components/ui/dialog';
import ObjectID from 'bson-objectid';
import { AccordionItem, AccordionItemContent, AccordionItemTrigger, AccordionRoot } from '@components/ui/accordion';
import { useGetPriceListSchema } from '@screens/population/hooks/use-get-price-list-schema';
import { defaultProcessorPriceList } from '@screens/settings/constants/default-price-list';
import { RecordHarvestFormValues } from '@screens/population/components/harvest-modals/types';
import { FormSteps, PriceListFormFields } from '@screens/population/components/harvest-modals/price-list-form-fields';
import { MainFormFields } from '@screens/population/components/harvest-modals/main-form-fields';
import { HarvestQualityFormFields } from '@screens/population/components/harvest-modals/harvest-quality-form-fields';
import { useCalculateHarvestFormDetails } from '@screens/population/hooks/use-calculate-harvest-form-details';
import { useSetPriceListToHarvestForm } from '@screens/population/hooks/use-set-price-list-to-harvest-form';
import { CloseButton } from '@components/ui/close-button';
import { VisionOnlyGuard } from '@components/permission-wrappers/vision-only-guard';
import { useGetProcessorOptions } from '@screens/pond/hooks/use-get-processor-options';

interface AddEditFinalHarvestModalProps {
  pondName: string;
  pondId: string;
  children: ReactNode;
  defaultValues?: Omit<RecordHarvestFormValues, 'date'> & { date: string };
  isInPondView?: boolean;
  isInPondListView?: boolean;
  triggerContainerProps?: BoxProps;
  isEdit?: boolean;
  onCancel?: () => void;
  onSuccess?: () => void;
  population?: PopulationType;
}

export function AddEditFinalHarvestModal(props: AddEditFinalHarvestModalProps) {
  const {
    population,
    children,
    pondName,
    pondId,
    isInPondView,
    isInPondListView,
    triggerContainerProps = {},
    defaultValues,
    isEdit,
    onCancel,
    onSuccess
  } = props;

  const { trans } = getTrans();

  //START-STATE HOOKS
  const { onOpen, onClose, open } = useDisclosure();
  const { onOpen: onConfirmationOpen, onClose: onConfirmationClose, open: isConfirmationOpen } = useDisclosure();
  const [step, setStep] = useState(FormSteps.MainView);

  const submitButtonRef = useRef<HTMLButtonElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  ///START-API HOOKS
  const [{ isLoading: isLoadingHarvest }, harvestPond] = useHarvestPondApi();
  const [{ isLoading: isLoadingPopulation }, updatePopulation] = useUpdatePopulationApi();
  const isUpdating = isLoadingHarvest || isLoadingPopulation;
  const { reloadCurrentFarmPonds } = useReloadCurrentFarmPonds();
  const [_, getPopulationDetails] = useGetPopulationDetailsApi();

  /// START-REDUX HOOKS
  const user = useAppSelector((state) => state.auth.user);
  const currentPopulation = useAppSelector((state) => state.farm?.currentPopulation);
  const productOffering = useAppSelector((state) => state.farm?.currentFarm?.metadata?.productOffering);

  const isVisionOnly = productOffering === 'visionOnly';
  const { trackAction } = useAnalytics();
  const { processorOptions, activeProcessorPriceLists } = useGetProcessorOptions();
  const { priceListSchema } = useGetPriceListSchema(false, isVisionOnly);

  const populationToUse = population ?? currentPopulation;

  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const { harvest, cycle, _id: populationId, stockedAt, stockedAtTimezone } = populationToUse ?? {};

  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);
  const normalizedHarvest = {
    ...(harvest ?? {}),
    priceList: harvest?.processorPriceList
  };
  const defaultData = {
    ...normalizedHarvest,
    ...(defaultValues ?? {})
  };
  const {
    date: harvestDate,
    lbsHarvested: harvestAmount,
    weight: harvestAbw,
    revenue: harvestRevenue,
    fileIds: harvestFileIds,
    processorId: harvestProcessorId,
    enablesEstimation: harvestEnablesEstimation,
    harvestId,
    priceList: harvestPriceList
  } = defaultData ?? {};

  const harvestAt = harvestDate ? DateTime.fromFormat(harvestDate, 'yyyy-MM-dd') : null;
  const revenueValue = !harvestEnablesEstimation ? (harvestRevenue ?? undefined) : undefined;

  const finalDefaultValues: RecordHarvestFormValues = {
    date:
      harvestAt > DateTime.local()
        ? DateTime.local().toJSDate()
        : (harvestAt?.toJSDate() ?? DateTime.local().toJSDate()),
    lbsHarvested: !isVisionOnly ? convertUnitByDivision(harvestAmount, unitsConfig?.biomass) : undefined,
    weight: harvestAbw,
    revenue: !isVisionOnly ? revenueValue : undefined,
    fileIds: harvestFileIds,
    processorId: !isVisionOnly ? harvestProcessorId : undefined,
    enablesEstimation: !isVisionOnly ? (harvestEnablesEstimation ?? true) : undefined,
    priceList: !isVisionOnly
      ? {
          defaultHarvestType: harvestPriceList?.defaultHarvestType ?? 'headon',
          headOn: defaultProcessorPriceList.headOn,
          headless: defaultProcessorPriceList.headless,
          percentHarvestAsHeadsOn: null,
          percentHarvestAsHeadsOnClassA: null,
          percentHarvestAsHeadsOnClassB: null,
          percentHarvestAsLeftover: null,
          processLeftoverAs: harvestPriceList?.processLeftoverAs ?? 'headlessAsClassAB',
          percentLeftoverAsHeadlessClassA: null,
          percentLeftoverAsHeadlessClassB: null,
          percentHeadlessAsClassA: null,
          percentHeadlessAsClassB: null,
          percentTail: 67
        }
      : undefined
  };

  const numberConditionalSchema = isVisionOnly
    ? Yup.number().transform(numberTransform).nullable()
    : Yup.number().transform(numberTransform).nullable().required(trans('t_required'));

  // START-FORM HOOKS
  const { schema } = useYupSchema({
    date: Yup.date().transform(numberTransform).required(trans('t_required')),
    lbsHarvested: numberConditionalSchema,
    weight: numberConditionalSchema,
    revenue: isVisionOnly
      ? Yup.number().transform(numberTransform).nullable()
      : Yup.number().when('enablesEstimation', {
          is: true,
          then: () => Yup.number().transform(numberTransform).nullable(),
          otherwise: () => Yup.number().transform(numberTransform).nullable().required(trans('t_required'))
        }),
    fileIds: Yup.array().of(Yup.string()).label('t_liquidation_report'),
    processorId: isVisionOnly ? Yup.string() : Yup.string().required(trans('t_required')),
    enablesEstimation: Yup.boolean().nullable(),
    priceList: priceListSchema
  });

  const formMethods = useForm<RecordHarvestFormValues>({
    resolver: yupResolver(schema) as YupResolverType<RecordHarvestFormValues>,
    defaultValues: finalDefaultValues,
    mode: 'onChange'
  });

  const { reset, handleSubmit, watch, setValue } = formMethods;

  const processorIdWatch = watch('processorId');
  const farmPriceList = activeProcessorPriceLists?.find((priceList) => priceList._id === processorIdWatch)?.priceList;

  //EFFECTS
  const { setPriceList } = useSetPriceListToHarvestForm({
    setValue,
    triggerState: open,
    activeProcessorPriceLists,
    farmPriceList,
    harvestPriceList,
    defaultPercentTail: finalDefaultValues?.priceList?.percentTail
  });

  // START-HANDLERS
  const handleModalOpen = () => {
    onOpen();
    reset(finalDefaultValues);
  };

  const handleModalClose = () => {
    onClose();
    onCancel?.();
    setStep(FormSteps.MainView);
  };

  const onReload = () => {
    if (isInPondView) return getPopulationDetails({ params: { filter: { populationId } }, loadRelated: true });
    if (isInPondListView) return reloadCurrentFarmPonds();
  };

  const onSubmit = handleSubmit((data: RecordHarvestFormValues) => {
    const { date, lbsHarvested, priceList, ...rest } = data ?? {};
    const {
      percentHarvestAsHeadsOn,
      percentHarvestAsHeadsOnClassA,
      percentHarvestAsHeadsOnClassB,
      percentHarvestAsLeftover,
      percentHeadlessAsClassA,
      percentHeadlessAsClassB,
      percentLeftoverAsHeadlessClassA,
      percentLeftoverAsHeadlessClassB,
      percentTail,
      defaultHarvestType,
      ...restPriceList
    } = priceList ?? {};

    const harvestData: PopulationHarvestInput = {
      ...rest,
      revenue: data.revenue || undefined,
      processorId: data.processorId || undefined,
      enablesEstimation: !isVisionOnly ? data.enablesEstimation : undefined,
      date: DateTime.fromJSDate(date).toFormat('yyyy-MM-dd'),
      lbsHarvested: convertUnitByMultiplication(lbsHarvested, unitsConfig?.biomass),
      harvestId: harvestId ?? ObjectID().toHexString(),
      processorPriceList: defaultHarvestType
        ? {
            ...finalDefaultValues.priceList,
            ...restPriceList,
            defaultHarvestType,
            percentHarvestAsHeadsOn: getPercentageDivided(percentHarvestAsHeadsOn),
            percentHarvestAsHeadsOnClassA: getPercentageDivided(percentHarvestAsHeadsOnClassA),
            percentHarvestAsHeadsOnClassB: getPercentageDivided(percentHarvestAsHeadsOnClassB),
            percentHarvestAsLeftover: getPercentageDivided(percentHarvestAsLeftover),
            percentHeadlessAsClassA: getPercentageDivided(percentHeadlessAsClassA),
            percentHeadlessAsClassB: getPercentageDivided(percentHeadlessAsClassB),
            percentLeftoverAsHeadlessClassA: getPercentageDivided(percentLeftoverAsHeadlessClassA),
            percentLeftoverAsHeadlessClassB: getPercentageDivided(percentLeftoverAsHeadlessClassB),
            percentTail: getPercentageDivided(percentTail)
          }
        : undefined
    };

    if (isEdit) {
      updatePopulation({
        params: {
          filter: { populationId },
          set: {
            harvest: harvestData
          }
        },
        successCallback() {
          onSuccess?.();
          onClose();
          onReload();
          trackAction(actionsName.pondHarvestEdited, { pondId, pondName }).then();
        }
      });
      return;
    }

    harvestPond({
      params: {
        pondId,
        harvest: harvestData
      },
      successCallback: () => {
        onSuccess?.();
        onClose();
        onReload();
        trackAction(actionsName.pondHarvested, { pondId, pondName }).then();
      }
    });
  });

  return (
    <DialogRoot
      open={open}
      onOpenChange={(e) => {
        e.open ? handleModalOpen() : handleModalClose();
      }}
      restoreFocus={false}
      size='lg'
      scrollBehavior='inside'
    >
      <Box
        w='fit-content'
        onClick={(e) => {
          e.stopPropagation();
          handleModalOpen();
        }}
        {...triggerContainerProps}
      >
        {children}
      </Box>

      <DialogContent
        data-cy='harvest-population-modal'
        borderRadius='2xl'
        px='lg'
        py={0}
        bgColor='bg.gray.medium'
        ref={containerRef}
        closeTrigger={false}
      >
        <EditHarvestConfirmationModal
          submitButtonRef={submitButtonRef}
          isOpen={isConfirmationOpen}
          onClose={onConfirmationClose}
          title={trans('t_harvest_pond_confirmation')}
          subTitle={trans('t_harvest_change_description')}
          containerRef={containerRef}
        />

        {step === FormSteps.MainView && (
          <Flex
            pos='sticky'
            bgColor='bg.gray.medium'
            top={0}
            align='center'
            justify='space-between'
            gap='sm-alt'
            pt='lg'
            pb='2lg'
            zIndex={10}
          >
            <Heading size='heavy300'>
              {trans('t_x_final_harvest_title', {
                action: isEdit ? trans('t_edit') : trans('t_record'),
                pondName,
                cycle
              })}
            </Heading>
            <CloseButton size='sm' onClick={handleModalClose} />
          </Flex>
        )}

        <FormProvider {...formMethods}>
          <chakra.form
            display='flex'
            flexDirection='column'
            gap='2lg'
            onSubmit={(event) => {
              event.stopPropagation();
              onSubmit(event).then();
            }}
            noValidate
            data-cy='add-edit-final-harvest-form'
          >
            {step === FormSteps.MainView && (
              <>
                <MainFormFields
                  processorOptions={processorOptions}
                  setStep={setStep}
                  stockedAtTimezone={stockedAtTimezone}
                  stockedAtDate={stockedAt}
                  setPriceList={setPriceList}
                  activeProcessorPriceLists={activeProcessorPriceLists}
                />
                <AccordionRoot
                  collapsible
                  defaultValue={['details']}
                  multiple
                  variant='plain'
                  display='flex'
                  flexDirection='column'
                  gap='2lg'
                >
                  <AccordionItem bgColor='white' borderRadius='2xl' py='md' value='details'>
                    <AccordionItemTrigger cursor='pointer' py='sm-alt' px='md' textStyle='label.100'>
                      {trans('t_details')}
                    </AccordionItemTrigger>
                    <AccordionItemContent display='flex' flexDirection='column' gap='sm-alt' pb={0} pt='sm-alt' px='md'>
                      <DetailsFormFields
                        population={populationToUse}
                        pondId={pondId}
                        unitConfig={unitsConfig?.biomass}
                        isBiomassUnitLbs={isBiomassUnitLbs}
                        defaultRevenue={harvestRevenue}
                      />
                    </AccordionItemContent>
                  </AccordionItem>
                  <VisionOnlyGuard>
                    <AccordionItem bgColor='white' borderRadius='2xl' py='md' value='quality'>
                      <AccordionItemTrigger cursor='pointer' py='sm-alt' px='md' textStyle='label.100'>
                        {trans('t_harvest_quality')}
                      </AccordionItemTrigger>
                      <AccordionItemContent
                        display='flex'
                        flexDirection='column'
                        gap='sm-alt'
                        pb={0}
                        pt='sm-alt'
                        px='md'
                      >
                        <HarvestQualityFormFields farmPriceList={farmPriceList} />
                      </AccordionItemContent>
                    </AccordionItem>
                  </VisionOnlyGuard>
                </AccordionRoot>
              </>
            )}
            {step === FormSteps.PriceList && (
              <PriceListFormFields
                setStep={setStep}
                pondName={pondName}
                cycle={cycle}
                farmPriceList={farmPriceList}
                processorOptions={processorOptions}
              />
            )}

            {step === FormSteps.MainView && (
              <Flex
                justify='flex-end'
                bgColor='bg.gray.medium'
                gap='sm-alt'
                pos='sticky'
                bottom='0'
                insetEnd='0'
                py='lg'
                zIndex={10}
              >
                <BaseButton
                  variant='secondary'
                  bgColor='transparent'
                  onClick={handleModalClose}
                  analyticsId={actionsName.pondOverviewRecordHarvestCancelClicked}
                >
                  {trans('t_cancel')}
                </BaseButton>
                <BaseButton
                  type={isEdit ? 'button' : 'submit'}
                  loading={isUpdating}
                  onClick={() => {
                    isEdit && onConfirmationOpen();
                  }}
                  analyticsId={actionsName.pondOverviewRecordHarvestSaveClicked}
                >
                  {isEdit ? trans('t_save_changes') : trans('t_record_harvest')}
                </BaseButton>
                <button style={{ display: 'none' }} type='submit' ref={submitButtonRef}>
                  {trans('t_harvest')}
                </button>
              </Flex>
            )}
          </chakra.form>
        </FormProvider>
      </DialogContent>
    </DialogRoot>
  );
}

interface DetailsFormFieldsProps {
  isBiomassUnitLbs: boolean;
  unitConfig: 'lbs' | 'kg';
  population: PopulationType;
  pondId: string;
  defaultRevenue: number;
}

function DetailsFormFields(props: DetailsFormFieldsProps) {
  const { population, pondId, isBiomassUnitLbs, defaultRevenue, unitConfig } = props;
  const { trans } = getTrans();
  const lang = useAppSelector((state) => state.app.lang);
  const { seedingQuantity, history, survivalRate, _id: populationId } = population ?? {};

  const {
    control,
    formState: { errors },
    setValue,
    watch,
    trigger
  } = useFormContext<RecordHarvestFormValues>();

  const averageWeight = watch('weight');
  const lbsHarvested = watch('lbsHarvested');
  const date = watch('date');
  const fileIds = watch('fileIds');
  const revenue = watch('revenue');
  const watchedEnablesEstimation = watch('enablesEstimation');

  const { animalsHarvested } = useCalculateHarvestFormDetails({
    averageWeight,
    lbsHarvested: convertUnitByMultiplication(lbsHarvested, unitConfig),
    date,
    history,
    seedingQuantity,
    survivalRate
  });

  useEffect(() => {
    setValue('enablesEstimation', !revenue);
    trigger('revenue').then();
  }, [revenue]);

  useEffect(() => {
    if (!watchedEnablesEstimation) return;
    setValue('revenue', NaN);
  }, [watchedEnablesEstimation]);

  return (
    <>
      <VisionOnlyGuard>
        <HarvestFormOneRow label={trans('t_biomass_harvested')}>
          <BaseFormNumberInput
            name='lbsHarvested'
            id='lbsHarvested'
            control={control}
            required
            numericFormatProps={{
              decimalScale: 0,
              suffix: ` ${isBiomassUnitLbs ? trans('t_lb') : trans('t_kg')}`
            }}
            w='124px'
            inputProps={{
              placeholder: trans('t_enter')
            }}
          />
        </HarvestFormOneRow>
      </VisionOnlyGuard>
      <HarvestFormOneRow label={trans('t_abw_g')}>
        <BaseFormNumberInput
          name='weight'
          id='weight'
          control={control}
          required
          numericFormatProps={{
            fixedDecimalScale: true,
            decimalScale: 2
          }}
          w='124px'
          inputProps={{
            placeholder: trans('t_enter')
          }}
        />
      </HarvestFormOneRow>
      <VisionOnlyGuard>
        <HarvestFormOneRow label={trans('t_estimated_animals_harvested')}>
          <Text size='label200' w='124px' textAlign='end'>
            {isNumber(animalsHarvested) ? formatNumber(animalsHarvested, { fractionDigits: 0, lang }) : '-'}
          </Text>
        </HarvestFormOneRow>
      </VisionOnlyGuard>

      <AdminOnlyWrapper>
        <VisionOnlyGuard>
          <HarvestFormOneRow borderBottom='none' label={trans('t_revenue')}>
            <BaseFormNumberInput
              name='revenue'
              control={control}
              numericFormatProps={{ fixedDecimalScale: true, suffix: '$', decimalScale: 0 }}
              w='124px'
              inputProps={{
                placeholder: trans('t_enter')
              }}
            />
          </HarvestFormOneRow>
          <Flex align='center' gap='2xs'>
            <Controller
              name='enablesEstimation'
              control={control}
              render={({ field: { value, onChange, ...rest } }) => {
                return (
                  <BaseFormCheckbox
                    {...rest}
                    id='enablesEstimation'
                    key='enablesEstimation'
                    label={trans('t_use_kampi_expected_revenue')}
                    formControlProps={{
                      w: 'max-content'
                    }}
                    data-cy='enablesEstimation'
                    checked={value}
                    onCheckedChange={({ checked }) => onChange(checked)}
                  />
                );
              }}
            />

            {isNumber(defaultRevenue) && (
              <Text as='span' size='label200'>
                ({formatNumber(defaultRevenue, { fractionDigits: 0, lang, isCurrency: true })})
              </Text>
            )}
          </Flex>

          <Separator />
        </VisionOnlyGuard>
        <UploadReport
          pondId={pondId}
          populationId={populationId}
          defaultValues={fileIds}
          onSuccess={(ids) => setValue('fileIds', ids)}
        />
      </AdminOnlyWrapper>
      {errors?.fileIds?.message && (
        <Text color='red.500' size='label200'>
          {errors.fileIds.message}
        </Text>
      )}
    </>
  );
}
