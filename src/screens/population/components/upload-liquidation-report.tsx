import { Box, <PERSON>lex, Spinner, Text } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { useCreateFileApi } from '@screens/file/hooks/use-create-file-api';
import { useDeleteFileApi } from '@screens/file/hooks/use-delete-file-api';
import { useListFilesApi } from '@screens/file/hooks/use-list-files-api';
import { useEffect, useState } from 'react';
import { BaseButton } from '@components/base/base-button';
import Dropzone from 'react-dropzone';
import upperFirst from 'lodash/upperFirst';
import { FileUploadIcon } from '@icons/file/file-upload-icon';
import { FileIcon } from '@icons/file/file-icon';
import { formatFileSize } from '@utils/files';
import { TrashCanIcon } from '@icons/trash-can/trash-can-icon';

type UploadReportPropsType = {
  pondId: string;
  populationId: string;
  defaultValues?: string[];
  onSuccess: (id: string[]) => void;
};

export function UploadReport(props: UploadReportPropsType) {
  const { defaultValues = [] } = props;
  const currentFarmId = useAppSelector((state) => state.farm?.currentFarm?._id);
  const [{ isLoading, isFinishedOnce, data }, getFiles] = useListFilesApi();

  useEffect(() => {
    if (!defaultValues?.length) return;
    getFiles({
      params: {
        page: { size: 100000, current: 1 },
        filter: { entity: 'farm', entityId: [currentFarmId], isDeleted: false, id: defaultValues }
      }
    });
  }, [defaultValues]);

  if (!defaultValues?.length) {
    return <UploadComp {...props} defaultValues={undefined} />;
  }

  if (isLoading || !isFinishedOnce) {
    return (
      <Box display='flex' h='150px' justifyContent='center' alignItems='center'>
        <Spinner />
      </Box>
    );
  }

  const values = data?.files?.map((file) => ({
    name: file.name,
    id: file._id,
    size: file.fileSize,
    extension: file.extension
  }));

  return <UploadComp {...props} defaultValues={values} />;
}

type UploadCompProps = {
  pondId: string;
  populationId: string;
  defaultValues?: { name: string; id: string; size: number; extension: string }[];
  onSuccess: (id: string[]) => void;
};

function UploadComp({ pondId, populationId, defaultValues = [], onSuccess }: UploadCompProps) {
  const { currentFarm } = useAppSelector((state) => state.farm);
  const { trans } = getTrans();
  const [value, setValue] = useState(defaultValues[0]);

  const [{ isLoading: isCreating }, createFile] = useCreateFileApi();
  const [_, deleteFile] = useDeleteFileApi();

  const onDelete = (fileId: string) => {
    onSuccess([]);
    setValue(undefined);
    deleteFile({ params: { filter: { fileId } } });
  };

  const onChange = (files: File[]) => {
    const file = files[0];
    createFile({
      params: {
        entity: 'farm',
        entityId: currentFarm?._id,
        pondId,
        file,
        populationId
      },
      successCallback: (data) => {
        onSuccess([data.file._id]);
        setValue({ id: data.file._id, name: file.name, size: file.size, extension: file.type });
      }
    });
  };

  const isDisabled = !!value?.id;

  return (
    <Flex direction='column' gap='sm-alt'>
      <Text size='label200'>{trans('t_add_liquidation_report')}</Text>
      <Dropzone
        maxFiles={1}
        onDrop={onChange}
        disabled={isDisabled}
        multiple={false}
        accept={{ 'text/csv': ['.csv'], 'application/pdf': ['.pdf'], 'application/vnd.ms-excel': ['.xls', '.xlsx'] }}
      >
        {({ getRootProps, getInputProps }) => (
          <Flex
            gap='sm-alt'
            rounded='lg'
            height='150px'
            align='center'
            flexDir='column'
            justify='center'
            cursor={isDisabled ? 'not-allowed' : 'pointer'}
            border='1px dashed'
            bgColor='bg.gray.medium'
            borderColor='border.gray'
            {...getRootProps()}
          >
            <input {...getInputProps()} />
            <FileUploadIcon mb='sm-alt' />
            <Flex align='center' gap='2xs'>
              <Text size='label200'>{trans('t_drag_and_drop_file_or')}</Text>
              <BaseButton h='16px' size='sm' variant='link' w='max-content' loading={isCreating} disabled={isDisabled}>
                {trans('t_brows_computer')}
              </BaseButton>
            </Flex>

            <Text size='light300' color='text.gray.disabled'>
              {trans('t_liquidation_report_format')}
            </Text>
          </Flex>
        )}
      </Dropzone>
      {value?.name && (
        <Flex
          h='40px'
          p='sm-alt'
          align='center'
          justify='space-between'
          border='1px solid'
          borderColor='gray.400'
          borderRadius='lg'
        >
          <Flex align='center' gap='xs'>
            <FileIcon />
            <Text size='label200' maxW='250px' whiteSpace='nowrap' overflow='hidden' textOverflow='ellipsis'>
              {upperFirst(value.name)}
            </Text>
            <Text size='light300' color='text.gray.disabled'>
              {formatFileSize(value.size)}
            </Text>
          </Flex>
          <TrashCanIcon onClick={() => onDelete(value.id)} cursor='pointer' _hover={{ opacity: 0.8 }} />
        </Flex>
      )}
    </Flex>
  );
}
