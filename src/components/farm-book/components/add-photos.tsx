import { ChangeEvent, Ref } from 'react';
import { Box, Flex, Icon, Image, Input } from '@chakra-ui/react';
import { FaTimes } from 'react-icons/fa';

type AddPhotosProps = {
  photos: FileList;
  setPhotos: (photos: FileList) => void;
  inputRef: Ref<HTMLInputElement>;
};

export function AddPhotos(props: AddPhotosProps) {
  const { photos, inputRef, setPhotos } = props;

  return (
    <>
      <Input
        multiple
        as='input'
        type='file'
        display='none'
        accept='image/*'
        ref={inputRef}
        onChange={(event: ChangeEvent<HTMLInputElement>) => {
          if (!event.target.files) return;
          setPhotos(event.target.files);
        }}
      />
      {!!photos?.length && (
        <Flex gap='xs' mb='sm' minWidth='100%' overflowX='auto' css={{ '&::-webkit-scrollbar': { display: 'none' } }}>
          {Object.entries(photos).map(([index, photo]) => {
            return (
              <Box
                key={index}
                position='relative'
                minW='100px'
                _hover={{ '& .delete-icon': { visibility: 'visible' } }}
              >
                <Image
                  h='42px'
                  w='100px'
                  rounded='base'
                  cursor='pointer'
                  alt={photo.name}
                  objectFit='cover'
                  src={URL.createObjectURL(photo)}
                  onClick={() => {
                    const fileURL = URL.createObjectURL(photo);
                    const tab = window.open();
                    tab.location.href = fileURL;
                  }}
                />
                <Flex
                  w='18px'
                  h='18px'
                  top='2px'
                  right='2px'
                  align='center'
                  rounded='full'
                  justify='center'
                  bgColor='white'
                  cursor='pointer'
                  userSelect='none'
                  visibility='hidden'
                  position='absolute'
                  className='delete-icon'
                  _hover={{ bgColor: 'gray.200' }}
                  shadow='0px -1px 8px 0px rgba(0, 0, 0, 0.15)'
                  onClick={(event) => {
                    event.preventDefault();
                    event.stopPropagation();

                    const newPhotos = Array.from(photos);
                    newPhotos.splice(parseInt(index), 1);

                    // Create a new DataTransfer object and add the files to it
                    const dataTransfer = new DataTransfer();
                    newPhotos.forEach((file) => dataTransfer.items.add(file));

                    setPhotos(dataTransfer.files);
                  }}
                >
                  <Icon color='text.gray' fontSize='12px'>
                    <FaTimes />
                  </Icon>
                </Flex>
              </Box>
            );
          })}
        </Flex>
      )}
    </>
  );
}
