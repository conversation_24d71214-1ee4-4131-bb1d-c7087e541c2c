import { DateTime } from 'luxon';
import { useAppDispatch, useAppSelector } from '@redux/hooks';
import { Box, Flex, Text } from '@chakra-ui/react';
import { FarmBookItem as FarmBookItemType } from '@components/farm-book/hooks/use-farm-book';
import { setFarmBookPondIdAction } from '@redux/farm-book';
import { NewMessage } from '@icons/chat/new-message';
import { Avatar } from '@components/ui/avatar';

const getLastMessage = (
  messagesGroupedByDate: FarmBookItemType['messagesGroupedByDate'],
  lastMessageCreatedAt: string,
  lastMessageCreatedAtLuxon: DateTime
) => {
  if (!lastMessageCreatedAtLuxon?.isValid) return;

  const formattedDate = lastMessageCreatedAtLuxon.toFormat('yyyy-MM-dd');
  const messages = messagesGroupedByDate[formattedDate];
  return messages.find((ele) => ele.createdAt === lastMessageCreatedAt);
};

type FarmBookItemProps = FarmBookItemType & {
  isFirstItem: boolean;
  isLastItem: boolean;
};

export function FarmBookItem(props: FarmBookItemProps) {
  const { pondId, name, lastMessageCreatedAt, messagesGroupedByDate, isLastItem, hasUnreadMessages } = props;

  const dispatch = useAppDispatch();
  const lang = useAppSelector((state) => state.app.lang);
  const currentFarm = useAppSelector((state) => state.farm.currentFarm);
  const { timezone: farmTimezone } = currentFarm ?? {};

  const todayLuxon = DateTime.local({ zone: farmTimezone });
  const lastMessageCreatedAtLuxon = lastMessageCreatedAt
    ? DateTime.fromISO(lastMessageCreatedAt, { zone: farmTimezone })
    : undefined;
  const isToday = todayLuxon.toFormat('yyyy-MM-dd') === lastMessageCreatedAtLuxon?.toFormat('yyyy-MM-dd');

  const lastMessage = getLastMessage(messagesGroupedByDate, lastMessageCreatedAt, lastMessageCreatedAtLuxon);

  return (
    <Box
      bg='white'
      p='sm-alt'
      {...(!isLastItem && { mb: 'sm-alt' })}
      rounded='2xl'
      onClick={() => dispatch(setFarmBookPondIdAction(pondId))}
      cursor='pointer'
    >
      <Flex align='center' userSelect='none' justify='space-between'>
        <Text size='label100' lineHeight='20px'>
          {name}
        </Text>
        <Flex justify={'center'} align={'center'} gap={'sm'}>
          {hasUnreadMessages && <NewMessage />}
          {!!lastMessageCreatedAt && (
            <Text size='label400' color='text.gray.disabled'>
              {lastMessageCreatedAtLuxon.toFormat(isToday ? 'hh:mm a' : 'MMM dd', { locale: lang })}
            </Text>
          )}
        </Flex>
      </Flex>

      {lastMessage && <Message message={lastMessage} isUnread={hasUnreadMessages} />}
    </Box>
  );
}

function Message({
  message,
  isUnread = false
}: {
  message: FarmBookItemType['messagesGroupedByDate'][0][0];
  isUnread?: boolean;
}) {
  const { text, user } = message;
  const { profileImageUrl, firstName, lastName, email } = user;
  const fullName = `${firstName ?? email?.split('@')[0] ?? ''} ${lastName ?? ''}`.trim();

  return (
    <Flex mt='sm-alt' gap='sm-alt' align='center'>
      <Avatar
        width='26px'
        height='26px'
        name={fullName}
        src={profileImageUrl}
        bg='bg.brandBlue.profile'
        css={{
          '& .chakra-avatar__initials': {
            color: 'white',
            fontSize: 'sm',
            fontWeight: 600,
            lineHeight: '12px'
          }
        }}
      />
      <Text size='label400' color={isUnread ? 'brandBlue.600' : 'text.gray'}>
        {firstName}: {text}
      </Text>
    </Flex>
  );
}
