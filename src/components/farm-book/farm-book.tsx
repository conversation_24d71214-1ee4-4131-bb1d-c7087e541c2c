import { ChangeEvent, useEffect, useMemo, useRef, useState } from 'react';
import { getTrans } from '@i18n/get-trans';
import { Box, Flex, Input, Presence, Text, Textarea } from '@chakra-ui/react';
import { DateTime } from 'luxon';
import { useAppDispatch, useAppSelector } from '@redux/hooks';
import { BaseButton } from '@components/base/base-button';
import { AddPhotos } from '@components/farm-book/components/add-photos';
import { AddFile } from '@components/farm-book/components/add-file';
import { FarmBookHeader } from '@components/farm-book/components/farm-book-header';
import { useCreateBookMessageApi } from '@hooks/farm-book/use-create-book-message-api';
import { FarmBookItem as FarmBookItemType, useFarmBook } from '@components/farm-book/hooks/use-farm-book';
import { getFileNameFromUrl, getRefererLabel } from '@components/farm-book/helper/farm-book';
import { SetRefererPayloadType } from '@redux/farm-book/set-referer';
import { FarmBookItem } from '@components/farm-book/components/farm-book-item';
import { MessageImage } from '@components/farm-book/components/message-image';
import { setFarmBookPondIdAction, setIsFarmBookOpenAction } from '@redux/farm-book';
import { useRouter } from 'next/router';
import { MinusFilled } from '@icons/minus/minus-filled';
import { ArrowBigLeftFilled } from '@icons/arrow-big-left/arrow-big-left-filled';
import { SearchDuoIcon } from '@icons/search/search-duo-icon';
import { AudioPlayer } from '@components/farm-book/components/audio-player';
import { FileDownloadIcon } from '@icons/file-download-icon';
import { AttachFileIcon } from '@icons/attach-file-icon';
import { ImageAddIcon } from '@icons/image-add-icon';
import { MicrophoneIcon } from '@icons/microphone-icon';
import { useCreateBookChannelAckApi } from '@hooks/farm-book/use-create-book-channel-ack-api';
import { useFarmBookChannelAckDetails } from '@hooks/farm-book/use-farm-book-channel-ack-details-api';
import { VoiceRecord } from '@components/farm-book/components/voice-record';
import { SettingsAdjustFilled } from '@icons/settings-adjust/settings-adjust-filled';
import { MenuButton, MenuContent, MenuItem, MenuRoot } from '@components/ui/menu';
import { InputGroup } from '@components/ui/input-group';
import { Avatar } from '@components/ui/avatar';
import { SendIcon } from '@icons/send-icon';
import { CloseFilled } from '@icons/close/close-filled';

const hideOnPages = ['login', 'forgot-password', 'reset-password', 'signup', 'verify', 'view-invitation'];

export function FarmBook() {
  const { trans } = getTrans();

  const { route, query } = useRouter();
  const dispatch = useAppDispatch();
  const { currentFarm } = useAppSelector((state) => state.farm);
  const currentUser = useAppSelector((state) => state.auth.user);
  const jwtToken = useAppSelector((state) => state.auth.jwtToken);
  const referer = useAppSelector((state) => state.farmBook.referer);
  const currentPond = useAppSelector((state) => state.farm.currentPond);
  const farmBookPondId = useAppSelector((state) => state.farmBook.farmBookPondId);
  const isFarmBookOpen = useAppSelector((state) => state.farmBook.isFarmBookOpen);
  const currentPondNameSplit = query?.pondEid?.toString()?.split('-');
  const currentPondNameFromRoute =
    currentPondNameSplit?.length > 2 ? currentPondNameSplit.slice(1).join('-') : currentPondNameSplit?.[1];

  const { _id: currentPondId } = currentPond ?? {};

  const [file, setFile] = useState<File>();
  const [text, setText] = useState<string>();
  const [search, setSearch] = useState<string>();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const menuContainerRef = useRef<HTMLDivElement>(null);
  const [photos, setPhotos] = useState<FileList>();
  const addFileInputRef = useRef<HTMLInputElement>(null);
  const addPhotosInputRef = useRef<HTMLInputElement>(null);
  const [voiceRecordingBlob, setVoiceRecordingBlob] = useState<Blob>();
  const [isOnlyUnreadMessages, setIsOnlyUnreadMessages] = useState<boolean>();
  const [isVoiceRecordVisible, setIsVoiceRecordVisible] = useState<boolean>(false);
  const [{ farmBook, isLoading: isLoadingFarmBook, isFinishedOnce: isFinishedOnceLoadingFarmBook }, getFarmBook] =
    useFarmBook();
  const [{ isLoading: isLoadingCreateFarmBookMessage }, createFarmBookMessageApi] = useCreateBookMessageApi();
  const [, createBookChannelAckApi] = useCreateBookChannelAckApi();

  const [farmBookChannelAcks, setFarmBookChannelAcks] = useState([]);
  const [, getFarmBookChannelAckDetails] = useFarmBookChannelAckDetails();

  const shouldHideOnPage = hideOnPages.find((page) => route?.toLowerCase()?.includes(page?.toLowerCase()));

  const farmBookFiltered = useMemo(() => {
    if (!search && !isOnlyUnreadMessages) return farmBook;

    const filteredFarms: Record<string, FarmBookItemType> = {};
    Object.entries(farmBook).forEach(([_, pond]) => {
      const { name, pondId, hasUnreadMessages } = pond;

      // Check if pond matches search criteria and unread messages filter
      const matchesSearch = !search || name?.toLowerCase().includes(search?.toLowerCase());
      const matchesUnread = !isOnlyUnreadMessages || hasUnreadMessages;

      if (matchesSearch && matchesUnread) {
        filteredFarms[pondId] = pond;
      }
    });

    return filteredFarms;
  }, [farmBook, search, farmBookChannelAcks, isOnlyUnreadMessages]);

  const lang = useAppSelector((state) => state.app.lang);
  const farmTimezone = useAppSelector((state) => state.farm.currentFarm?.timezone);

  const scrollToBottom = () => {
    messagesEndRef?.current?.scrollIntoView({ behavior: 'instant' });
  };

  const updateLastMessageReadAt = () => {
    const { pondId, farmId } = farmBookItemToView;
    createBookChannelAckApi({
      params: {
        farmId,
        pondId: pondId === 'general' ? undefined : pondId
      }
    });
  };

  /**
   * If user is on the pond page farm book should open
   * to that pond by default.
   */
  useEffect(() => {
    if (route.includes('/pond/[pondEid]') && currentPondId) {
      dispatch(setFarmBookPondIdAction(currentPondId));
    } else {
      dispatch(setFarmBookPondIdAction(undefined));
    }
    if (isFarmBookOpen) {
      dispatch(setIsFarmBookOpenAction(false));
    }
  }, [route, currentPondId]);

  /**
   * If farm book is not open and user navigates to
   * another page reset farm book pond id.
   */
  useEffect(() => {
    if (isFarmBookOpen) return;
    dispatch(setFarmBookPondIdAction(undefined));
  }, [route]);

  useEffect(() => {
    if (shouldHideOnPage) return;
    getFarmBookChannelAckDetails({
      params: route !== '/' && currentFarm?._id ? { farmId: [currentFarm?._id] } : {},
      successCallback: setFarmBookChannelAcks
    });
  }, [currentFarm]);

  /**
   * When user  opens farm book
   * fetch farm book data.
   */
  useEffect(() => {
    if (!isFarmBookOpen) return;
    getFarmBook({
      reload: true
    });
  }, [isFarmBookOpen]);

  useEffect(() => {
    if (farmBookPondId && !!Object.keys(farmBookItemToView?.messagesGroupedByDate || {}).length) {
      updateLastMessageReadAt();
    }
  }, [farmBookPondId]);

  /**
   * Scroll to bottom on every new message and update last message read at.
   */
  useEffect(() => {
    if (!messagesEndRef?.current || !isFarmBookOpen) return;
    scrollToBottom();
  }, [farmBookPondId, isFarmBookOpen, farmBook, messagesEndRef?.current, isVoiceRecordVisible, file, photos]);

  const farmBookItemToView = farmBook?.[farmBookPondId];

  const onSend = () => {
    const { pondId, farmId, populationId } = farmBookItemToView;

    const todayLuxon = DateTime.local({ zone: farmTimezone });
    const voiceRecordingFileName = `${todayLuxon.toFormat('yyyy-MM-dd HH:mm:ss')}.mp3`;

    createFarmBookMessageApi({
      params: {
        record: {
          text,
          farmId,
          ...(pondId !== 'general' ? { pondId } : {}),
          referer,
          media: [
            ...(photos ?? []),
            ...(file ? [file] : []),
            ...(voiceRecordingBlob ? [new File([voiceRecordingBlob], voiceRecordingFileName)] : [])
          ],
          populationId
        }
      },
      successCallback() {
        updateLastMessageReadAt();
        getFarmBook({
          reload: true,
          onSuccess() {
            setText('');
            setFile(undefined);
            setPhotos(undefined);
            setIsVoiceRecordVisible(false);
            setVoiceRecordingBlob(undefined);
            scrollToBottom();
          }
        });
      }
    });
  };

  const isSendButtonDisabled = !file && !photos?.length && !voiceRecordingBlob && !text;

  const isLoading = isLoadingFarmBook && !isFinishedOnceLoadingFarmBook;

  const farmBookMsgEntries = farmBookItemToView?.messagesGroupedByDate
    ? Object.entries(farmBookItemToView?.messagesGroupedByDate)
    : [];

  if (shouldHideOnPage) {
    return null;
  }

  const handleFarmBookToggle = () => {
    dispatch(setIsFarmBookOpenAction(!isFarmBookOpen));
  };

  const handleGoBack = () => {
    dispatch(setFarmBookPondIdAction(undefined));
  };

  const farmBookTitle = farmBookItemToView?.farmId ? farmBookItemToView.name : currentPondNameFromRoute;

  return (
    <Flex
      pe={isFarmBookOpen ? { base: '0', md: 'md' } : 'md'}
      justify='flex-end'
      position='fixed'
      left={{ base: 0, md: 'unset' }}
      right={0}
      top={isFarmBookOpen ? { base: '0', md: 'unset' } : 'unset'}
      bottom={isFarmBookOpen ? { base: 0, md: '16px' } : '16px'}
      zIndex='sticky'
    >
      {(!isFarmBookOpen || isLoading) && (
        <FarmBookHeader
          farmBookTitle={farmBookTitle}
          isLoading={isLoading}
          onClick={handleFarmBookToggle}
          hasUnreadMessages={!!(farmBookChannelAcks || []).filter((ack) => ack?.hasUnreadMessages).length}
        />
      )}
      <Presence
        lazyMount
        unmountOnExit
        present={!isLoading && isFarmBookOpen}
        animationDuration='moderate'
        animationName={{
          _open: 'slide-from-bottom, fade-in',
          _closed: 'slide-to-bottom, fade-out'
        }}
      >
        {!isLoading && isFarmBookOpen && (
          <Flex
            w={{ base: '100%', md: '544px' }}
            height={{ base: 'auto', md: '650px' }}
            flexDir='column'
            borderRadius={{ base: '0', md: '2xl' }}
            gap='2lg'
            bgColor='bg.gray.medium'
            boxShadow='elevation.400'
          >
            <Flex align='center' justify='space-between' p='md' pb='0'>
              <Flex align='center' gap='sm-alt'>
                {!!farmBookItemToView?.farmId && <ArrowBigLeftFilled cursor='pointer' onClick={handleGoBack} />}
                <Text size='label100'>{farmBookTitle || trans('t_farm_book')}</Text>
              </Flex>

              <MinusFilled cursor='pointer' onClick={handleFarmBookToggle} _hover={{ opacity: 0.8 }} />
            </Flex>
            {!farmBookItemToView?.farmId && (
              <>
                <Flex px='md' align='center' gap='sm-alt' ref={menuContainerRef}>
                  <InputGroup
                    w='full'
                    startElement={<SearchDuoIcon pointerEvents='none' />}
                    endElement={
                      search && <CloseFilled hasBackground={false} cursor='pointer' onClick={() => setSearch('')} />
                    }
                  >
                    <Input
                      textStyle='label.100'
                      rounded='full'
                      outline='none'
                      value={search}
                      border='none'
                      bgColor='white'
                      autoComplete='off'
                      _placeholder={{ color: 'text.gray.disabled' }}
                      placeholder={trans('t_search')}
                      onChange={(event: ChangeEvent<HTMLInputElement>) => setSearch(event.target.value)}
                    />
                  </InputGroup>
                  <MenuRoot>
                    <MenuButton variant='secondary'>
                      <SettingsAdjustFilled /> {trans('t_filter')}
                    </MenuButton>
                    <MenuContent portalRef={menuContainerRef}>
                      <MenuItem value='msgs' onClick={() => setIsOnlyUnreadMessages(!isOnlyUnreadMessages)}>
                        {isOnlyUnreadMessages ? trans('t_all_messages') : trans('t_unread_messages')}
                      </MenuItem>
                    </MenuContent>
                  </MenuRoot>
                </Flex>
                <Flex flexDir='column' overflowY='auto' flex={1} px='md' pb='md'>
                  {!Object.keys(farmBookFiltered ?? {})?.length && (
                    <Text flex={1} alignSelf='center' alignContent='center' fontSize='md' color='gray.700'>
                      {trans('t_no_data_to_display')}
                    </Text>
                  )}

                  {Object.entries(farmBookFiltered ?? {}).map(([pondId, pond], index: number) => {
                    const isFirstItem = index === 0;
                    const isLastItem = index === Object.keys(farmBookFiltered).length - 1;

                    return <FarmBookItem {...pond} key={pondId} isLastItem={isLastItem} isFirstItem={isFirstItem} />;
                  })}
                </Flex>
              </>
            )}
            {!!farmBookItemToView?.farmId && (
              <Flex flexDir='column' justify='space-between' flex={1}>
                {!farmBookMsgEntries?.length && (
                  <Flex flex={1} flexDirection='column' align='center' justify='center'>
                    <Text color='gray.700' fontSize='md'>
                      {trans('t_no_data_to_display')}
                    </Text>
                  </Flex>
                )}
                {!!farmBookMsgEntries?.length && (
                  <Flex
                    px='md'
                    pb='md'
                    overflowY='scroll'
                    flexDirection='column'
                    h={isVoiceRecordVisible || !!file || !!photos?.length ? '403px' : '455px'}
                  >
                    <Box flex='1 1 auto' />
                    {farmBookMsgEntries?.map(([date, messages], farmBookMsgEntryIndex) => {
                      const dateFormatted = DateTime.fromFormat(date, 'yyyy-MM-dd').toFormat('ccc, MMM dd', {
                        locale: lang
                      });

                      const isFirstItem = farmBookMsgEntryIndex === 0;

                      return (
                        <Flex key={date} flexDir='column' {...(!isFirstItem && { mt: '2lg' })}>
                          <Flex
                            alignSelf='center'
                            h='20px'
                            w='fit-content'
                            bg='bg.brandBlue.weakShade2'
                            px='md'
                            rounded='4xl'
                            mb='sm-alt'
                            align='center'
                            justify='center'
                          >
                            <Text size='label300'>{dateFormatted}</Text>
                          </Flex>
                          <Flex flexDir='column' gap='sm-alt'>
                            {messages.map((message) => {
                              const { _id: messageId, text, user, referer, media, createdAt, createdBy } = message;
                              const { profileImageUrl, firstName, lastName, email } = user;
                              const fullName = `${firstName ?? email?.split('@')[0] ?? ''} ${lastName ?? ''}`.trim();

                              const mediaType = media?.[0]?.type;
                              const isCurrentUser = currentUser?._id === createdBy;

                              return (
                                <Flex gap='sm-alt' key={messageId} align='flex-start'>
                                  {!isCurrentUser ? (
                                    <Flex
                                      gap='sm-alt'
                                      align='center'
                                      css={{ '&.chakra-avatar': { bgColor: 'bg.brandBlue.profile !important' } }}
                                    >
                                      <Avatar
                                        width='26px'
                                        height='26px'
                                        name={fullName}
                                        src={profileImageUrl}
                                        css={{
                                          '&.chakra-avatar__initials': {
                                            color: 'white',
                                            fontSize: 'xs',
                                            fontWeight: 600,
                                            lineHeight: '12px'
                                          }
                                        }}
                                      />
                                    </Flex>
                                  ) : (
                                    <Box minW='26px' minH='26px' />
                                  )}
                                  <Box pos='relative' flexGrow={1}>
                                    <Box
                                      bg='white'
                                      p='sm-alt'
                                      rounded='lg'
                                      css={{
                                        '&:after': {
                                          content: '""',
                                          position: 'absolute',
                                          top: 0,
                                          width: '28px',
                                          height: '14.5px',
                                          borderRadius: '10px',
                                          backgroundColor: 'white',
                                          transformOrigin: 'top right',
                                          zIndex: -1,
                                          ...(!isCurrentUser && { left: '-12px', transform: 'skew(55deg)' }),
                                          ...(isCurrentUser && { right: '-12px', transform: 'skew(-55deg)' })
                                        }
                                      }}
                                    >
                                      <Flex fontSize='md' align='center' mb='xs-alt' justify='space-between'>
                                        {isCurrentUser ? (
                                          <Box />
                                        ) : (
                                          <Text size='label100' color='text.brandBlue' lineHeight='20px'>
                                            {fullName}
                                          </Text>
                                        )}
                                        <Flex align='center' gap='sm-alt'>
                                          {!!referer && (
                                            <Text size='label400' color='text.gray.disabled'>
                                              {getRefererLabel(referer as SetRefererPayloadType)}
                                            </Text>
                                          )}
                                          {createdAt && (
                                            <Text size='label400' color='text.gray.disabled'>
                                              {DateTime.fromISO(createdAt, { zone: farmTimezone }).toFormat('hh:mm a', {
                                                locale: lang
                                              })}
                                            </Text>
                                          )}
                                        </Flex>
                                      </Flex>
                                      <Flex flexDir='column'>
                                        {!!text && (
                                          <Text
                                            size='label300'
                                            color='text.gray.weak'
                                            {...(!!media?.length && { mb: 'sm-alt' })}
                                          >
                                            {text}
                                          </Text>
                                        )}
                                        {!!media?.length && (
                                          <>
                                            {mediaType.includes('image') && (
                                              <Flex gap='xs-alt' flexWrap='wrap'>
                                                {media.map((file, index) => (
                                                  <MessageImage
                                                    key={index}
                                                    url={file.url}
                                                    containerProps={{ minW: { base: 'auto', sm: '215px' } }}
                                                  />
                                                ))}
                                              </Flex>
                                            )}
                                            {mediaType.includes('pdf') && (
                                              <>
                                                {media.map((file, index) => (
                                                  <Flex key={index} align='center' gap='sm-alt'>
                                                    <Flex
                                                      minW='34px'
                                                      minH='34px'
                                                      rounded='4xl'
                                                      align='center'
                                                      justify='center'
                                                      cursor='pointer'
                                                      bgColor='brandBlue.100'
                                                      _hover={{ opacity: 0.8 }}
                                                      onClick={async () => {
                                                        const result = await fetch(file.url, {
                                                          headers: { Authorization: `Bearer ${jwtToken}` }
                                                        });

                                                        const blob = await result.blob();

                                                        const pdfBlob = new Blob([blob], { type: 'application/pdf' });
                                                        const url = window.URL.createObjectURL(pdfBlob);

                                                        const tempLink = document.createElement('a');
                                                        tempLink.href = url;
                                                        tempLink.setAttribute('download', `${messageId}.pdf`);

                                                        document.body.appendChild(tempLink);
                                                        tempLink.click();

                                                        document.body.removeChild(tempLink);
                                                        window.URL.revokeObjectURL(url);
                                                      }}
                                                    >
                                                      <FileDownloadIcon w='20px' h='20px' color='icon.brandBlue' />
                                                    </Flex>
                                                    <Text size='label300' color='text.gray.weak'>
                                                      {getFileNameFromUrl(file?.url)}
                                                    </Text>
                                                  </Flex>
                                                ))}
                                              </>
                                            )}
                                            {mediaType.includes('audio') && (
                                              <>
                                                {media.map((file, index) => (
                                                  <AudioPlayer key={index} url={file.url} />
                                                ))}
                                              </>
                                            )}
                                          </>
                                        )}
                                      </Flex>
                                    </Box>
                                  </Box>
                                </Flex>
                              );
                            })}
                          </Flex>
                        </Flex>
                      );
                    })}
                    <Box ref={messagesEndRef} />
                  </Flex>
                )}
                <Flex
                  mt='auto'
                  bgColor='white'
                  flexDir='column'
                  overflow='hidden'
                  borderRadius='2xl'
                  shadow='0px -1px 4px 0px rgba(0, 0, 0, 0.08)'
                  py='sm-alt'
                  px='md'
                >
                  <Textarea
                    mb='sm'
                    px='2sm'
                    py='sm-alt'
                    border='none'
                    fontSize='md'
                    resize='none'
                    rounded='4xl'
                    lineHeight='20px'
                    bg='brandBlue.100'
                    value={text}
                    _placeholder={{ color: 'text.gray' }}
                    placeholder={trans('t_i_am_writing_the_message_here')}
                    _focusVisible={{ border: 'none', shadow: 'none' }}
                    minHeight='40px'
                    onChange={(event: ChangeEvent<HTMLTextAreaElement>) => setText(event.target.value)}
                  />
                  <VoiceRecord
                    isVisible={isVoiceRecordVisible}
                    voiceRecordingBlob={voiceRecordingBlob}
                    setVoiceRecordingBlob={setVoiceRecordingBlob}
                  />
                  <AddFile file={file} inputRef={addFileInputRef} setFile={setFile} />
                  <AddPhotos inputRef={addPhotosInputRef} photos={photos} setPhotos={setPhotos} />
                  <Flex
                    pt='sm'
                    align='center'
                    borderTop='0.5px solid'
                    borderTopColor='border.gray'
                    justify='space-between'
                  >
                    <Flex align='center' gap='sm-alt'>
                      <Flex
                        minW='34px'
                        minH='34px'
                        rounded='4xl'
                        align='center'
                        justify='center'
                        cursor='pointer'
                        bgColor='brandBlue.100'
                        _hover={{ opacity: 0.8 }}
                        onClick={() => {
                          setPhotos(undefined);
                          setIsVoiceRecordVisible(false);
                          addFileInputRef.current.value = null;
                          addFileInputRef?.current?.click();
                        }}
                      >
                        <AttachFileIcon w='20px' h='20px' color='icon.brandBlue' />
                      </Flex>
                      <Flex
                        minW='34px'
                        minH='34px'
                        rounded='4xl'
                        align='center'
                        justify='center'
                        cursor='pointer'
                        bgColor='brandBlue.100'
                        _hover={{ opacity: 0.8 }}
                        onClick={() => {
                          setFile(undefined);
                          setIsVoiceRecordVisible(false);
                          addPhotosInputRef.current.value = null;
                          addPhotosInputRef?.current?.click();
                        }}
                      >
                        <ImageAddIcon w='20px' h='20px' color='icon.brandBlue' />
                      </Flex>
                      <Flex
                        minW='34px'
                        minH='34px'
                        rounded='4xl'
                        align='center'
                        justify='center'
                        cursor='pointer'
                        bgColor='brandBlue.100'
                        _hover={{ opacity: 0.8 }}
                        onClick={() => setIsVoiceRecordVisible(!isVoiceRecordVisible)}
                      >
                        <MicrophoneIcon w='20px' h='20px' color='icon.brandBlue' />
                      </Flex>
                    </Flex>
                    <BaseButton
                      onClick={onSend}
                      disabled={isSendButtonDisabled}
                      loading={isLoadingCreateFarmBookMessage}
                    >
                      <SendIcon me='xs' />
                      {trans('t_send')}
                    </BaseButton>
                  </Flex>
                </Flex>
              </Flex>
            )}
          </Flex>
        )}
      </Presence>
    </Flex>
  );
}
