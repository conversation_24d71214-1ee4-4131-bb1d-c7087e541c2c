'use client';

import { Absolute<PERSON>enter, Menu as ChakraMenu, Portal } from '@chakra-ui/react';
import { Lu<PERSON>heck, LuChevronRight } from 'react-icons/lu';
import { BaseButton, BaseButtonProps } from '@components/base/base-button';
import { forwardRef, ReactNode, RefObject } from 'react';

interface MenuContentProps extends ChakraMenu.ContentProps {
  portalled?: boolean;
  portalRef?: RefObject<HTMLElement>;
}

export const MenuContent = forwardRef<HTMLDivElement, MenuContentProps>((props, ref) => {
  const { portalled = true, portalRef, ...rest } = props;
  return (
    <Portal disabled={!portalled} container={portalRef}>
      <ChakraMenu.Positioner>
        <ChakraMenu.Content
          ref={ref}
          minWidth='250px'
          borderRadius='2xl'
          boxShadow='elevation.400'
          p='xs'
          display='flex'
          flexDirection='column'
          textStyle='label.200'
          border='none'
          gap='3xs'
          pos='relative'
          zIndex='dropdown'
          {...rest}
        />
      </ChakraMenu.Positioner>
    </Portal>
  );
});

export const MenuArrow = forwardRef<HTMLDivElement, ChakraMenu.ArrowProps>((props, ref) => {
  return (
    <ChakraMenu.Arrow ref={ref} {...props}>
      <ChakraMenu.ArrowTip />
    </ChakraMenu.Arrow>
  );
});

export const MenuCheckboxItem = forwardRef<HTMLDivElement, ChakraMenu.CheckboxItemProps>((props, ref) => {
  return (
    <ChakraMenu.CheckboxItem ps='8' ref={ref} {...props}>
      <AbsoluteCenter axis='horizontal' insetStart='4' asChild>
        <ChakraMenu.ItemIndicator>
          <LuCheck />
        </ChakraMenu.ItemIndicator>
      </AbsoluteCenter>
      {props.children}
    </ChakraMenu.CheckboxItem>
  );
});

export const MenuRadioItem = forwardRef<HTMLDivElement, ChakraMenu.RadioItemProps>((props, ref) => {
  const { children, ...rest } = props;
  return (
    <ChakraMenu.RadioItem ps='8' ref={ref} {...rest}>
      <AbsoluteCenter axis='horizontal' insetStart='4' asChild>
        <ChakraMenu.ItemIndicator>
          <LuCheck />
        </ChakraMenu.ItemIndicator>
      </AbsoluteCenter>
      <ChakraMenu.ItemText>{children}</ChakraMenu.ItemText>
    </ChakraMenu.RadioItem>
  );
});

export const MenuItemGroup = forwardRef<HTMLDivElement, ChakraMenu.ItemGroupProps>((props, ref) => {
  const { title, children, ...rest } = props;
  return (
    <ChakraMenu.ItemGroup ref={ref} {...rest}>
      {title && <ChakraMenu.ItemGroupLabel userSelect='none'>{title}</ChakraMenu.ItemGroupLabel>}
      {children}
    </ChakraMenu.ItemGroup>
  );
});

export interface MenuTriggerItemProps extends ChakraMenu.ItemProps {
  startIcon?: ReactNode;
}

export const MenuTriggerItem = forwardRef<HTMLDivElement, MenuTriggerItemProps>((props, ref) => {
  const { startIcon, children, ...rest } = props;
  return (
    <ChakraMenu.TriggerItem ref={ref} {...rest}>
      {startIcon}
      {children}
      <LuChevronRight />
    </ChakraMenu.TriggerItem>
  );
});

export interface MenuItemProps extends ChakraMenu.ItemProps {}

export const MenuItem = forwardRef<HTMLDivElement, MenuItemProps>((props, ref) => {
  const { children, ...rest } = props;
  return (
    <ChakraMenu.Item
      ref={ref}
      p='xs'
      h='40px'
      cursor='pointer'
      textStyle='label.200'
      color='text.gray'
      borderRadius='lg-alt'
      _hover={{ bgColor: 'bg.brandBlue.weakShade1' }}
      _active={{ bgColor: 'bg.brandBlue.weakShade1' }}
      _focus={{ bgColor: 'bg.brandBlue.weakShade1' }}
      _highlighted={{ bgColor: 'bg.brandBlue.weakShade1' }}
      {...rest}
    >
      {children}
    </ChakraMenu.Item>
  );
});

export interface MenuButtonProps extends BaseButtonProps {
  selectVariant?: 'primary' | 'secondary';
}

export const MenuButton = forwardRef<HTMLButtonElement, MenuButtonProps>((props, ref) => {
  const { selectVariant = 'primary', ...rest } = props;
  const variantStyles: BaseButtonProps =
    selectVariant === 'secondary'
      ? {
          _hover: { bgColor: 'transparent' },
          _active: { bgColor: 'transparent' },
          bgColor: 'transparent',
          border: 'none'
        }
      : {
          _hover: { bgColor: 'gray.200' },
          _active: { bgColor: 'grayDown.300' }
        };
  return (
    <MenuTrigger ref={ref} asChild {...rest}>
      <BaseButton
        variant='secondary'
        border='none'
        minW='max-content'
        {...variantStyles}
        _focus={{ outline: 'none', bgColor: rest.bgColor || 'white' }}
        _disabled={{ cursor: 'not-allowed', color: 'text.gray.disabled', _hover: { bg: 'white' } }}
        {...rest}
      />
    </MenuTrigger>
  );
});

export const MenuRadioItemGroup = ChakraMenu.RadioItemGroup;
export const MenuContextTrigger = ChakraMenu.ContextTrigger;
export const MenuRoot = ChakraMenu.Root;
export const MenuSeparator = ChakraMenu.Separator;

export const MenuItemText = ChakraMenu.ItemText;
export const MenuItemCommand = ChakraMenu.ItemCommand;
export const MenuTrigger = ChakraMenu.Trigger;
