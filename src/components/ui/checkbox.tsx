import { Checkbox as Chakra<PERSON>heckbox, CheckboxRootProps } from '@chakra-ui/react';
import { forwardRef, InputHTMLAttributes, ReactNode, Ref } from 'react';
import { CheckFilled } from '@icons/check/check-filled';

export interface CheckboxProps extends CheckboxRootProps {
  icon?: ReactNode;
  inputProps?: InputHTMLAttributes<HTMLInputElement>;
  rootRef?: Ref<HTMLLabelElement>;
}

export const Checkbox = forwardRef<HTMLInputElement, CheckboxProps>((props, ref) => {
  const { icon, children, inputProps, rootRef, checked, defaultChecked, ...rest } = props;
  const isChecked = checked || defaultChecked;
  return (
    <ChakraCheckbox.Root
      ref={rootRef}
      gap='xs'
      cursor='pointer'
      checked={checked}
      defaultChecked={defaultChecked}
      {...rest}
    >
      <ChakraCheckbox.HiddenInput ref={ref} {...inputProps} />
      <ChakraCheckbox.Control>
        {icon || <CheckFilled hasBackground={false} color={isChecked ? 'white' : 'transparent'} pos='absolute' />}
      </ChakraCheckbox.Control>
      {children != null && <ChakraCheckbox.Label>{children}</ChakraCheckbox.Label>}
    </ChakraCheckbox.Root>
  );
});
