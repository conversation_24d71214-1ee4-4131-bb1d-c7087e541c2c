import {
  Popover as ChakraPopover,
  PopoverArrowTipProps,
  PopoverBodyProps,
  PopoverCloseTriggerProps,
  Portal
} from '@chakra-ui/react';
import { CloseButton } from './close-button';
import { forwardRef, RefObject } from 'react';

interface PopoverContentProps extends ChakraPopover.ContentProps {
  portalled?: boolean;
  portalRef?: RefObject<HTMLElement>;
}

export const PopoverContent = forwardRef<HTMLDivElement, PopoverContentProps>((props, ref) => {
  const { portalled = true, portalRef, ...rest } = props;
  return (
    <Portal disabled={!portalled} container={portalRef}>
      <ChakraPopover.Positioner>
        <ChakraPopover.Content ref={ref} whiteSpace='normal' {...rest} />
      </ChakraPopover.Positioner>
    </Portal>
  );
});

export const PopoverArrow = forwardRef<HTMLDivElement, PopoverArrowTipProps>((props, ref) => {
  const { bgColor, ...rest } = props;
  const bgColorValue = bgColor ? { bgColor: `${bgColor} !important` } : {};
  return (
    <ChakraPopover.Arrow ref={ref}>
      <ChakraPopover.ArrowTip {...bgColorValue} {...rest} />
    </ChakraPopover.Arrow>
  );
});

export const PopoverCloseTrigger = forwardRef<HTMLButtonElement, PopoverCloseTriggerProps>((props, ref) => {
  return (
    <ChakraPopover.CloseTrigger position='absolute' top='1' insetEnd='1' {...props} asChild ref={ref}>
      <CloseButton size='sm' />
    </ChakraPopover.CloseTrigger>
  );
});

export const PopoverBody = forwardRef<HTMLDivElement, PopoverBodyProps>((props, ref) => {
  return <ChakraPopover.Body overflow='auto' {...props} ref={ref} />;
});

export const PopoverTitle = ChakraPopover.Title;
export const PopoverDescription = ChakraPopover.Description;
export const PopoverFooter = ChakraPopover.Footer;
export const PopoverHeader = ChakraPopover.Header;
export const PopoverRoot = ChakraPopover.Root;
export const PopoverTrigger = ChakraPopover.Trigger;
