import { Portal, Tooltip as ChakraTooltip, TooltipContentProps, TooltipRootProps } from '@chakra-ui/react';
import { forwardRef, ReactNode, RefObject, useState } from 'react';

export interface TooltipProps extends Omit<TooltipRootProps, 'open' | 'onOpenChange'> {
  showArrow?: boolean;
  portalled?: boolean;
  portalRef?: RefObject<HTMLElement>;
  content: ReactNode;
  contentProps?: TooltipContentProps;
  disabled?: boolean;
}

export const Tooltip = forwardRef<HTMLDivElement, TooltipProps>((props, ref) => {
  const { showArrow = true, children, disabled, portalled = true, content, contentProps, portalRef, ...rest } = props;
  const [open, setOpen] = useState(false);
  if (disabled) return children;

  return (
    <ChakraTooltip.Root open={open} onOpenChange={(value) => setOpen(value.open)} {...rest}>
      <ChakraTooltip.Trigger
        asChild
        cursor='pointer'
        onMouseOver={() => {
          setOpen(true);
        }}
        onMouseLeave={() => {
          setOpen(false);
        }}
      >
        {children}
      </ChakraTooltip.Trigger>
      <Portal disabled={!portalled} container={portalRef}>
        <ChakraTooltip.Positioner>
          <ChakraTooltip.Content
            rounded='lg'
            bgColor='gray.200'
            px='md'
            py='sm-alt'
            color='text.gray.weak'
            textStyle='label.200'
            ref={ref}
            {...contentProps}
          >
            {showArrow && (
              <ChakraTooltip.Arrow>
                <ChakraTooltip.ArrowTip bgColor='gray.200 !important' borderColor='gray.200' />
              </ChakraTooltip.Arrow>
            )}
            {content}
          </ChakraTooltip.Content>
        </ChakraTooltip.Positioner>
      </Portal>
    </ChakraTooltip.Root>
  );
});
