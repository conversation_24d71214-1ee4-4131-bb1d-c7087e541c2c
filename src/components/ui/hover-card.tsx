import {
  HoverCard,
  HoverCardArrowTipProps,
  HoverCardContentProps as ChakraHoverCardContentProps,
  Portal
} from '@chakra-ui/react';
import { forwardRef, RefObject } from 'react';

export interface HoverCardContentProps extends ChakraHoverCardContentProps {
  portalled?: boolean;
  portalRef?: RefObject<HTMLElement>;
}

export const HoverCardContent = forwardRef<HTMLDivElement, HoverCardContentProps>((props, ref) => {
  const { portalled = false, portalRef, ...rest } = props;

  return (
    <Portal disabled={!portalled} container={portalRef}>
      <HoverCard.Positioner>
        <HoverCard.Content ref={ref} shadow='elevation.400' bgColor='bg.gray.strong' p='md' rounded='lg' {...rest} />
      </HoverCard.Positioner>
    </Portal>
  );
});

export const HoverCardArrow = forwardRef<HTMLDivElement, HoverCardArrowTipProps>((props, ref) => {
  const { bgColor = 'bg.gray.strong', ...rest } = props;
  const bgColorValue = bgColor ? { bgColor: `${bgColor} !important` } : {};
  return (
    <HoverCard.Arrow ref={ref}>
      <HoverCard.ArrowTip {...bgColorValue} {...rest} />
    </HoverCard.Arrow>
  );
});

export const HoverCardRoot = HoverCard.Root;
export const HoverCardTrigger = HoverCard.Trigger;
