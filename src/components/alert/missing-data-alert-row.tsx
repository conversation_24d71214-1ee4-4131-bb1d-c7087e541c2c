import { FarmAlertKey } from '@components/alert/helper';
import { goToUrl } from '@utils/url';
import { getTrans } from '@i18n/get-trans';
import { AlertRowContainer } from '@components/alert/alert-row-container';
import { ExclamationMarkSolid } from '@icons/exclamation-mark/exclamation-mark-solid';
import { Flex, FlexProps, Text } from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { ArrowBigRight } from '@icons/arrow-big-right/arrow-big-right';

type AlertMapData = Record<
  FarmAlertKey,
  { label: string; action?: { label: string; onClick: (farmEid: string) => void } }
>;

export function MissingDataAlertRow(props: {
  alertKey: FarmAlertKey;
  farmEid: string;
  hasEstimatedData: boolean;
  containerProps?: FlexProps;
}) {
  const { alertKey, farmEid, hasEstimatedData, containerProps } = props;
  const { trans } = getTrans();
  const alertMap: AlertMapData = {
    allPondsMonitoredMoreThanWeek: {
      label: trans('t_this_farm_data_is_outdated')
    },
    allPondsNotMonitoredMoreThanWeekAndMissingData: {
      label: trans('t_missing_latest_data', { extra: hasEstimatedData ? trans('t_estimated_values_are_used') : '' }),
      action: {
        label: trans('t_update_data'),
        onClick(farmEid: string) {
          goToUrl({
            params: { farmEid },
            route: '/farm/[farmEid]/data-updater'
          });
        }
      }
    },
    somePondsNotMonitoredMoreThanWeek: {
      label: trans('t_some_of_your_ponds_have_not_been_monitored')
    },
    missingData: {
      label: trans('t_some_of_your_ponds_are_missing_data', {
        extra: hasEstimatedData ? trans('t_estimated_values_are_used') : ''
      }),
      action: {
        label: trans('t_update_data'),
        onClick(farmEid: string) {
          goToUrl({
            params: { farmEid },
            route: '/farm/[farmEid]/data-updater'
          });
        }
      }
    }
  };

  const alert = alertMap[alertKey];

  if (!alert) return null;

  return (
    <AlertRowContainer status='error' {...containerProps}>
      <ExclamationMarkSolid color='icon.semanticRed' w='20px' h='20px' />

      <Flex flex={1} flexDir={['column', 'row', 'row']} alignItems={['flex-start', 'center', 'center']} gap='xs'>
        <Text size='label200'>{alert.label}</Text>
        {!!alert?.action && (
          <BaseButton variant='link' size='sm' onClick={() => alert.action.onClick(farmEid)}>
            {alert?.action?.label} <ArrowBigRight color='button.link' width='20px' height='20px' />
          </BaseButton>
        )}
      </Flex>
    </AlertRowContainer>
  );
}
