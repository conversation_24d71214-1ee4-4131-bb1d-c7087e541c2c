import { Flex, FlexProps, ThemeProps } from '@chakra-ui/react';

type Status = 'error' | 'success' | 'info';

interface AlertIconContainerProps extends FlexProps {
  size?: 'sm' | 'md' | 'lg';
  status: Status;
}

export function AlertIconContainer(props: AlertIconContainerProps) {
  const { size = 'md', status } = props;
  const sizes = {
    sm: { boxSize: '30px' },
    md: { boxSize: '40px' },
    lg: { boxSize: '50px', css: { '& svg': { boxSize: '30px' } } }
  };

  const statusBgColors: Record<Status, ThemeProps['bgColor']> = {
    error: 'bg.semanticRed.weak',
    success: 'bg.brandGreen.weakShade1',
    info: 'bg.brandBlue.weakShade1'
  };

  return (
    <Flex
      justify='center'
      align='center'
      bgColor={statusBgColors[status]}
      borderRadius='full'
      css={{ '& svg': { boxSize: '20px' } }}
      {...sizes[size]}
      {...props}
    />
  );
}
