import { Flex, FlexProps, ThemeProps } from '@chakra-ui/react';

type Status = 'error' | 'success' | 'info';
type ColorStyles = { borderColor: ThemeProps['borderColor']; bgColor: ThemeProps['bgColor'] };

interface AlertRowContainerProps extends FlexProps {
  status: Status;
}

export function AlertRowContainer(props: AlertRowContainerProps) {
  const { status, ...rest } = props;

  const statusStyles: Record<Status, ColorStyles> = {
    error: { borderColor: 'border.semanticRed', bgColor: 'bg.semanticRed.weak' },
    info: { borderColor: 'border.brandBlue', bgColor: 'bg.brandBlue.weakShade1' },
    success: { borderColor: 'border.brandGreen', bgColor: 'bg.brandGreen.weakShade1' }
  };

  const colorProps = statusStyles[status];
  return (
    <Flex
      gap='sm-alt'
      flexWrap='wrap'
      align='center'
      p='sm-alt'
      borderRadius='4xl'
      border='0.5px solid'
      pos='relative'
      {...colorProps}
      {...rest}
    />
  );
}
