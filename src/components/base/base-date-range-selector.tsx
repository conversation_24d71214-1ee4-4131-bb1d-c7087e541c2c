import { Box, Flex, Text } from '@chakra-ui/react';
import { ArrowBigLeftFilled } from '@icons/arrow-big-left/arrow-big-left-filled';
import { ArrowBigRightFilled } from '@icons/arrow-big-right/arrow-big-right-filled';
import { OptimalCalendarIcon } from '@icons/optimal-calendar';
import { DateTime } from 'luxon';
import { useAppSelector } from '@redux/hooks';
import { getTrans } from '@i18n/get-trans';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { ReactElement } from 'react';

const commonDatePickerElementStyles = {
  m: 0,
  w: '30px',
  h: '30px',
  fontSize: 'sm',
  fontWeight: 600,
  display: 'flex',
  alignItems: 'center',
  fontFamily: 'inherit',
  justifyContent: 'center'
};

type BaseDateRangeSelectorProps = {
  startDate: Date | null;
  endDate: Date | null;
  onChange: (dates: [Date | null, Date | null]) => void;
  customInput?: ReactElement;
};

function CustomHeader({
  date,
  decreaseMonth,
  increaseMonth,
  prevMonthButtonDisabled,
  nextMonthButtonDisabled
}: {
  date: Date;
  decreaseMonth: () => void;
  increaseMonth: () => void;
  prevMonthButtonDisabled: boolean;
  nextMonthButtonDisabled: boolean;
}) {
  return (
    <Flex align='center' justify='space-between' mb='md'>
      <ArrowBigLeftFilled
        _hover={prevMonthButtonDisabled ? {} : { opacity: 0.8 }}
        color={prevMonthButtonDisabled ? 'gray.400' : 'gray.700'}
        cursor={prevMonthButtonDisabled ? 'not-allowed' : 'pointer'}
        onClick={prevMonthButtonDisabled ? undefined : decreaseMonth}
      />
      <Text size='label100' lineHeight='20px'>
        {date.toLocaleString('default', { month: 'long', year: 'numeric' })}
      </Text>
      <ArrowBigRightFilled
        _hover={nextMonthButtonDisabled ? {} : { opacity: 0.8 }}
        color={nextMonthButtonDisabled ? 'gray.400' : 'gray.700'}
        cursor={nextMonthButtonDisabled ? 'not-allowed' : 'pointer'}
        onClick={nextMonthButtonDisabled ? undefined : increaseMonth}
      />
    </Flex>
  );
}

export function BaseDateRangeSelector({
  startDate,
  endDate,
  onChange,
  customInput: CustomInput
}: BaseDateRangeSelectorProps) {
  const { trans } = getTrans();
  const lang = useAppSelector((state) => state.app.lang);

  return (
    <Box
      css={{
        '& .react-datepicker-wrapper': { width: 'fit-content', display: 'flex' },
        '& .react-datepicker': {
          padding: 'md',
          rounded: 'lg',
          border: 'none',
          fontFamily: 'inherit',
          shadow: 'elevation.400'
        },
        '& .react-datepicker__month': {
          m: 0,
          gap: '10px',
          display: 'flex',
          fontFamily: 'inherit',
          flexDirection: 'column'
        },
        '& .react-datepicker__day-names': {
          mb: 0,
          gap: '10px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        },
        '& .react-datepicker__week': {
          gap: '10px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        },
        '& .react-datepicker__day': {
          ...commonDatePickerElementStyles,
          rounded: 'full !important',
          '&--today': {
            fontWeight: 800
          },
          '&--selected, &--in-selecting-range, &--in-range': {
            color: 'white !important',
            backgroundColor: 'graphBrandBlue !important'
          },
          '&:hover': {
            color: 'white !important',
            backgroundColor: 'graphBrandBlue !important'
          }
        },
        '& .react-datepicker__day--outside-month': {
          color: 'gray.500'
        },
        '& .react-datepicker__header': {
          p: 0,
          mb: '10px',
          borderBottom: 'none',
          backgroundColor: 'white'
        },
        '& .react-datepicker__current-month': {
          fontFamily: 'inherit'
        },
        '& .react-datepicker__day-name': {
          ...commonDatePickerElementStyles
        }
      }}
    >
      <DatePicker
        selectsRange
        showPopperArrow={false}
        popperPlacement='bottom-end'
        formatWeekDay={(nameOfDay) => nameOfDay.charAt(0)}
        startDate={startDate}
        endDate={endDate}
        onChange={(dates) => onChange([dates[0], dates[1]])}
        renderCustomHeader={CustomHeader}
        customInput={
          CustomInput ?? (
            <Flex align='center' gap='xs-alt' cursor='pointer' _hover={{ opacity: 0.8 }}>
              <OptimalCalendarIcon color='gray.700' />
              <Text size='label200'>
                {startDate && endDate
                  ? `${DateTime.fromJSDate(startDate).toFormat('LLL dd', { locale: lang })} - ${DateTime.fromJSDate(endDate).toFormat('LLL dd', { locale: lang })}`
                  : trans('t_custom')}
              </Text>
            </Flex>
          )
        }
      />
    </Box>
  );
}
