import { BaseButtonProps } from '@components/base/base-button';
import { OptionType } from '@components/form/form-control-react-select';
import { IconProps, Text, TextProps } from '@chakra-ui/react';
import { ChevronUpFilled } from '@icons/chevron-up/chevron-up-filled';
import { ChevronDownFilled } from '@icons/chevron-down/chevron-down-filled';
import { ReactNode, RefObject, useState } from 'react';
import { MenuButton, MenuContent, MenuItem, MenuRoot, MenuSeparator } from '@components/ui/menu';
import { PositioningOptions } from '@zag-js/popper';

export type SelectOption = OptionType<string, ReactNode> & { disabled?: boolean };

interface BaseMenuProps extends BaseButtonProps {
  placeholder?: ReactNode;
  titlePrefix?: string;
  iconProps?: IconProps;
  selectedOption: SelectOption;
  menuOffset?: PositioningOptions['offset'];
  options: SelectOption[];
  additionalOptionGroup?: {
    title: string;
    titleProps?: TextProps;
    options: SelectOption[];
  };
  selectVariant?: 'primary' | 'secondary';
  onItemSelect(option: SelectOption): unknown;
  onClose?(): void;
  containerRef?: RefObject<HTMLElement>;
  hasPortal?: boolean;
}

export function BaseMenu(props: BaseMenuProps) {
  const {
    menuOffset,
    placeholder,
    titlePrefix,
    selectedOption,
    options,
    onItemSelect,
    additionalOptionGroup,
    onClose,
    disabled,
    iconProps,
    selectVariant = 'primary',
    containerRef,
    hasPortal = false,
    ...rest
  } = props;

  const selectedValue = selectedOption?.value;
  const [open, setOpen] = useState(false);

  return (
    <MenuRoot
      onOpenChange={(e) => {
        setOpen(e.open);
        if (!e.open && onClose) {
          onClose();
        }
      }}
      positioning={{ placement: 'bottom', offset: menuOffset }}
      highlightedValue={selectedValue}
    >
      <MenuButton disabled={disabled} selectVariant={selectVariant} {...rest}>
        {titlePrefix || ''}
        {selectedOption?.label || placeholder}
        {open ? (
          <ChevronUpFilled hasBackground={false} color='icon.gray' ms='xs-alt' {...iconProps} />
        ) : (
          <ChevronDownFilled
            {...iconProps}
            ms='xs-alt'
            hasBackground={false}
            color={disabled ? 'icon.gray.disabled' : 'icon.gray'}
          />
        )}
      </MenuButton>
      <MenuContent portalled={hasPortal} portalRef={containerRef}>
        {options.map((item) => {
          const value = item.value;
          return (
            <MenuItem
              key={value}
              value={value}
              disabled={item.disabled}
              onClick={() => {
                onItemSelect({ label: item.label, value });
              }}
              data-cy={`menuItem-${value}`}
            >
              {item.label}
            </MenuItem>
          );
        })}
        {additionalOptionGroup && (
          <>
            <MenuSeparator my='3xs' borderBottomColor='gray.400' />
            <Text
              px='xs'
              h='40px'
              display='flex'
              size='label200'
              alignItems='center'
              color='text.gray.disabled'
              {...additionalOptionGroup.titleProps}
            >
              {additionalOptionGroup.title}
            </Text>
            {additionalOptionGroup.options?.map((item) => {
              const value = item.value;
              return (
                <MenuItem
                  key={value}
                  value={value}
                  disabled={item.disabled}
                  onClick={() => {
                    onItemSelect({ label: item.label, value });
                  }}
                  data-cy={`menuItem-${value}`}
                >
                  {item.label}
                </MenuItem>
              );
            })}
          </>
        )}
      </MenuContent>
    </MenuRoot>
  );
}
