'use client';
import Highcharts from 'highcharts';
import HighchartsReact, { type HighchartsReactProps } from 'highcharts-react-official';
import 'highcharts/highcharts-more';
import 'highcharts/modules/accessibility';
import { useAppSelector } from '@redux/hooks';

export interface HighchartsBaseProps extends HighchartsReactProps {}

export function HighchartsRawComp(props: HighchartsBaseProps) {
  const { chartRef, containerProps, ...rest } = props;
  const lang = useAppSelector((state) => state.app.lang);
  if (Highcharts) {
    // Set global options for Highcharts
    Highcharts.setOptions({
      lang: { locale: lang },
      credits: { enabled: false },
      legend: { enabled: false }
    });
  }
  return (
    <HighchartsReact
      highcharts={Highcharts}
      ref={chartRef}
      containerProps={{ className: 'highcharts-light', ...containerProps }}
      {...rest}
    />
  );
}
