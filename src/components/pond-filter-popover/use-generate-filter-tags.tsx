import { getCycleDaysFormatted } from '@screens/group-summary/helpers/group-view';
import { Flex, Text } from '@chakra-ui/react';
import { ChevronLeftFilled } from '@icons/chevron-left/chevron-left-filled';
import { ChevronRightFilled } from '@icons/chevron-right/chevron-right-filled';
import { formatNumber } from '@utils/number';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { ReactNode } from 'react';
import { CheckFilled } from '@icons/check/check-filled';
import { StarFilled } from '@icons/star/star-filled';
import { TrafficConeFilled } from '@icons/traffic-cone/traffic-cone-filled';
import { isEmpty } from 'lodash';

export type LessThanMoreThan = {
  lessThan: number;
  moreThan: number;
};

export type PondFilterPopoverFormType = {
  abw: LessThanMoreThan;
  cycleDays: LessThanMoreThan;
  aboveTarget: boolean;
  onTrack: boolean;
  offTrack: boolean;
  flagged: boolean;
  superVisor: { id: string; name: string };
  aboveCarryingCapacity: number;
  aboveCarryingCapacityFlag: boolean;
};

export interface GenerateFilterTagsArgs {
  cycleDaysLessThan: number;
  cycleDaysMoreThan: number;
  abwLessThan: number;
  abwMoreThan: number;
  aboveTarget: boolean;
  onTrack: boolean;
  offTrack: boolean;
  flagged: boolean;
  superVisor: { id: string; name: string };
  aboveCarryingCapacity: number;
}

type FilterTagField =
  | 'cycleDays'
  | 'abw'
  | 'flagged'
  | 'aboveTarget'
  | 'onTrack'
  | 'offTrack'
  | 'aboveCarryingCapacity'
  | 'superVisor';

export type TagItem = {
  field: FilterTagField;
  label: ReactNode;
  extraField?: string;
  shouldRemoveBoth?: boolean;
  shouldRemoveLessThan?: boolean;
  shouldRemoveMoreThan?: boolean;
};

export function useGenerateFilterTags(args: GenerateFilterTagsArgs) {
  const {
    cycleDaysLessThan,
    cycleDaysMoreThan,
    abwMoreThan,
    abwLessThan,
    flagged,
    aboveTarget,
    onTrack,
    offTrack,
    aboveCarryingCapacity,
    superVisor
  } = args;
  const tags: TagItem[] = [];
  const { trans } = getTrans();
  const lang = useAppSelector((state) => state.app.lang);

  if (onTrack) {
    tags.push({
      field: 'onTrack',
      label: (
        <Flex align='center' gap='xs-alt' data-cy='filterTag-onTrack'>
          <CheckFilled width='20px' height='20px' bgColor='bg.brandBlue.weakShade1' color='icon.brandBlue' />
          <Text size='label400'> {trans('t_on_track')}</Text>
        </Flex>
      )
    });
  }

  if (offTrack) {
    tags.push({
      field: 'offTrack',
      label: (
        <Flex align='center' gap='xs-alt' data-cy='filterTag-offTrack'>
          <TrafficConeFilled />
          <Text size='label400'> {trans('t_off_track')}</Text>
        </Flex>
      )
    });
  }

  if (aboveTarget) {
    tags.push({
      field: 'aboveTarget',
      label: (
        <Flex align='center' gap='xs-alt' data-cy='filterTag-aboveTarget'>
          <StarFilled /> <Text size='label400'> {trans('t_above_target')}</Text>
        </Flex>
      )
    });
  }

  if (aboveCarryingCapacity) {
    tags.push({
      field: 'aboveCarryingCapacity',
      extraField: 'aboveCarryingCapacityFlag',
      label: (
        <Text size='label400' data-cy='filterTag-aboveCarryingCapacity'>
          {trans('t_above_x_percent_carrying_capacity', { percent: aboveCarryingCapacity })}
        </Text>
      )
    });
  }

  if (flagged) {
    tags.push({
      field: 'flagged',
      label: (
        <Text size='label400' data-cy='filterTag-flagged'>
          {trans('t_flagged_ponds')}
        </Text>
      )
    });
  }

  if (!isEmpty(superVisor?.name)) {
    tags.push({
      field: 'superVisor',
      label: (
        <Text size='label400' data-cy='filterTag-supervisor'>
          {trans('t_supervisor')}: {superVisor.name}
        </Text>
      )
    });
  }

  if (cycleDaysLessThan && !cycleDaysMoreThan) {
    const cycleDaysFormatted = getCycleDaysFormatted({ number: cycleDaysLessThan, lang });
    tags.push({
      field: 'cycleDays',
      shouldRemoveLessThan: true,
      label: (
        <Flex align='center' textStyle='label.400' gap='2xs' data-cy='filterTag-cycleDays'>
          {trans('t_days_of_culture')}
          <ChevronLeftFilled hasBackground={false} boxSize='20px' />
          <span data-cy='cycleDays-lessThan-value'>{cycleDaysFormatted}</span>
        </Flex>
      )
    });
  }

  if (cycleDaysMoreThan && !cycleDaysLessThan) {
    const cycleDaysFormatted = getCycleDaysFormatted({ number: cycleDaysMoreThan, lang });
    tags.push({
      field: 'cycleDays',
      shouldRemoveMoreThan: true,
      label: (
        <Flex align='center' textStyle='label.400' gap='2xs' data-cy='filterTag-cycleDays'>
          {trans('t_days_of_culture')}
          <ChevronRightFilled hasBackground={false} boxSize='20px' />
          <span data-cy='cycleDays-moreThan-value'>{cycleDaysFormatted}</span>
        </Flex>
      )
    });
  }

  if (cycleDaysMoreThan && cycleDaysLessThan) {
    const cycleDaysMoreThanFormatted = getCycleDaysFormatted({ number: cycleDaysMoreThan, lang });
    const cycleDaysLessThanFormatted = getCycleDaysFormatted({ number: cycleDaysLessThan, lang });
    tags.push({
      field: 'cycleDays',
      shouldRemoveBoth: true,
      label: (
        <Flex align='center' textStyle='label.400' gap='2xs' data-cy='filterTag-cycleDays'>
          <span data-cy='cycleDays-moreThan-value'>{cycleDaysMoreThanFormatted}</span>
          <ChevronLeftFilled hasBackground={false} boxSize='20px' />
          {trans('t_days_of_culture')}
          <ChevronLeftFilled hasBackground={false} boxSize='20px' />
          <span data-cy='cycleDays-lessThan-value'>{cycleDaysLessThanFormatted}</span>
        </Flex>
      )
    });
  }

  if (abwLessThan && !abwMoreThan) {
    const abwFormatted = formatNumber(abwLessThan, { lang, fractionDigits: 2 });
    tags.push({
      field: 'abw',
      shouldRemoveLessThan: true,
      label: (
        <Flex align='center' textStyle='label.400' gap='2xs' data-cy='filterTag-abw'>
          {trans('t_abw_g')}
          <ChevronLeftFilled hasBackground={false} boxSize='20px' />
          <span data-cy='abw-lessThan-value'>{abwFormatted}</span>
        </Flex>
      )
    });
  }

  if (abwMoreThan && !abwLessThan) {
    const abwFormatted = formatNumber(abwMoreThan, { lang, fractionDigits: 2 });
    tags.push({
      field: 'abw',
      shouldRemoveMoreThan: true,
      label: (
        <Flex align='center' textStyle='label.400' gap='2xs' data-cy='filterTag-abw'>
          {trans('t_abw_g')}
          <ChevronRightFilled hasBackground={false} boxSize='20px' />
          <span data-cy='abw-moreThan-value'>{abwFormatted}</span>
        </Flex>
      )
    });
  }

  if (abwMoreThan && abwLessThan) {
    const abwLessThanFormatted = formatNumber(abwLessThan, { lang, fractionDigits: 2 });
    const abwMoreThanFormatted = formatNumber(abwMoreThan, { lang, fractionDigits: 2 });
    tags.push({
      field: 'abw',
      shouldRemoveBoth: true,
      label: (
        <Flex align='center' textStyle='label.400' gap='2xs' data-cy='filterTag-abw'>
          <span data-cy='abw-moreThan-value'>{abwMoreThanFormatted}</span>
          <ChevronLeftFilled hasBackground={false} boxSize='20px' />
          {trans('t_abw_g')}
          <ChevronLeftFilled hasBackground={false} boxSize='20px' />
          <span data-cy='abw-lessThan-value'>{abwLessThanFormatted}</span>
        </Flex>
      )
    });
  }

  return tags;
}
