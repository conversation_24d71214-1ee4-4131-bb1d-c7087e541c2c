import {
  Box,
  chakra,
  FieldRootProps,
  Flex,
  FlexProps,
  IconButton,
  InputProps,
  Separator,
  Text,
  TextProps
} from '@chakra-ui/react';
import { BaseButton } from '@components/base/base-button';
import { getTrans } from '@i18n/get-trans';
import { BaseFormNumberInput } from '@components/form/base-form-number-input';
import { Controller, useForm } from 'react-hook-form';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { numberTransform, useYupSchema, Yup, yupFormResolver, YupResolverType } from '@utils/yup';
import { lessThanFilterTest, moreThanFilterTest } from '@components/pond-filter-popover/pond-filter-popover-helpers';
import { actionsName } from '@utils/segment';

import { PondFilterPopoverFormType } from '@components/pond-filter-popover/use-generate-filter-tags';
import { BaseFormCheckbox } from '@components/form/base-form-checkbox';
import { StarFilled } from '@icons/star/star-filled';
import { CheckFilled } from '@icons/check/check-filled';
import { TrafficConeFilled } from '@icons/traffic-cone/traffic-cone-filled';
import { FilterIcon } from '@icons/filter-icon';
import { PopoverBody, PopoverContent, PopoverRoot, PopoverTrigger } from '@components/ui/popover';
import { VisionOnlyGuard } from '@components/permission-wrappers/vision-only-guard';
import { FormControlReactSelect } from '@components/form/form-control-react-select';
import { UseGetCurrentFarmSupervisorOptions } from '@hooks/use-get-current-farm-supervisor-options';

interface PondFilterPopoverProps extends FlexProps {
  defaultValues?: PondFilterPopoverFormType;
  onApplyFilter: Dispatch<SetStateAction<PondFilterPopoverFormType>>;
}

export function PondFilterPopover(props: PondFilterPopoverProps) {
  const { defaultValues, onApplyFilter, ...rest } = props;

  const { trans } = getTrans();

  const filterRowSchema = Yup.object().shape({
    lessThan: Yup.number().transform(numberTransform).nullable().min(0).test(lessThanFilterTest).label('t_less_than'),
    moreThan: Yup.number().transform(numberTransform).nullable().min(0).test(moreThanFilterTest).label('t_more_than')
  });

  const { schema } = useYupSchema({
    abw: filterRowSchema,
    cycleDays: filterRowSchema,
    aboveTarget: Yup.boolean(),
    onTrack: Yup.boolean(),
    offTrack: Yup.boolean(),
    flagged: Yup.boolean(),
    superVisor: Yup.object().shape({
      id: Yup.string().nullable().label('t_supervisor'),
      name: Yup.string().nullable().label('t_supervisor_name')
    }),
    aboveCarryingCapacityFlag: Yup.boolean(),
    aboveCarryingCapacity: Yup.number().when('aboveCarryingCapacityFlag', {
      is: true,
      then: () => Yup.number().transform(numberTransform).required().min(0).label('t_carrying_capacity'),
      otherwise: () => Yup.number().transform(numberTransform).nullable().label('t_carrying_capacity')
    })
  });

  const { reset, watch, control, setValue, handleSubmit } = useForm<PondFilterPopoverFormType>({
    mode: 'onChange',
    defaultValues,
    resolver: yupFormResolver(schema) as YupResolverType<PondFilterPopoverFormType>
  });

  const { isLoadingCurrentFarmPonds, superVisorOptions } = UseGetCurrentFarmSupervisorOptions();

  const [open, setOpen] = useState(false);

  const cycleDaysWatch = watch('cycleDays');
  const abwWatch = watch('abw');
  const aboveCarryingCapacityFlagWatch = watch('aboveCarryingCapacityFlag');

  useEffect(() => reset(defaultValues), [JSON.stringify(defaultValues)]);

  useEffect(() => {
    if (!aboveCarryingCapacityFlagWatch) {
      setValue('aboveCarryingCapacity', NaN);
    }
  }, [aboveCarryingCapacityFlagWatch]);

  const inputProps: InputProps = {
    w: '100px'
  };

  const checkboxProps: FieldRootProps = {
    w: '100%',
    display: 'flex',
    flexDir: 'column',
    justifyContent: 'center'
  };

  return (
    <Flex align='center' gap='sm-alt' {...rest}>
      <PopoverRoot
        positioning={{ placement: 'bottom-end' }}
        open={open}
        onOpenChange={(e) => setOpen(e.open)}
        lazyMount
        unmountOnExit
      >
        <PopoverTrigger asChild>
          <IconButton
            bgColor={open ? 'brandBlue.100' : 'white'}
            aria-label='filter-button'
            _focus={{ outline: 'none' }}
            data-cy='trigger-btn'
          >
            <FilterIcon
              w='20px'
              h='20px'
              position='relative'
              top='1px'
              color={open ? 'graphBrandBlue' : undefined}
              _hover={{ color: 'graphBrandBlue' }}
            />
          </IconButton>
        </PopoverTrigger>

        <PopoverContent
          width={{ base: '357px', md: '424px' }}
          border='none'
          rounded='2xl'
          bgColor='white'
          boxShadow='elevation.400'
        >
          <PopoverBody py='md' px={{ base: 'sm-alt', md: 'md' }}>
            <chakra.form
              display='flex'
              flexDir='column'
              gap='2lg'
              noValidate
              onSubmit={handleSubmit((data) => {
                onApplyFilter(data);
                setOpen(false);
              })}
              data-cy='ponds-filters-form'
            >
              <Text size='label100'>{trans('t_filter')}</Text>

              <Flex justify='space-between' align='center' pb='sm-alt'>
                <Text size='label200'>{trans('t_supervisor_optional')}</Text>
                <Box w='200px' justifyItems='flex-end'>
                  <FormControlReactSelect
                    isOptional
                    id='superVisor'
                    isClearable={true}
                    control={control}
                    error=''
                    formControlProps={{ w: '200px', h: '30px' }}
                    selectControlStyles={{ minHeight: '30px' }}
                    isLoading={isLoadingCurrentFarmPonds}
                    options={superVisorOptions}
                  />
                </Box>
              </Flex>

              <Separator borderColor='border.gray' />

              <VisionOnlyGuard>
                <Flex direction='column' gap='md'>
                  <Text size='label200'>{trans('t_performance')}</Text>
                  <Controller
                    name='aboveTarget'
                    control={control}
                    render={({ field: { value, onChange, ...rest } }) => {
                      return (
                        <BaseFormCheckbox
                          {...rest}
                          id='aboveTarget'
                          label={
                            <Flex align='center' gap='sm-alt'>
                              <StarFilled /> <Text size='label200'> {trans('t_above_target')}</Text>
                            </Flex>
                          }
                          formControlProps={checkboxProps}
                          data-cy='aboveTargetInput'
                          checked={value}
                          onCheckedChange={({ checked }) => onChange(checked)}
                        />
                      );
                    }}
                  />

                  <Controller
                    name='onTrack'
                    control={control}
                    render={({ field: { value, onChange, ...rest } }) => {
                      return (
                        <BaseFormCheckbox
                          {...rest}
                          id='onTrack'
                          label={
                            <Flex align='center' gap='sm-alt'>
                              <CheckFilled
                                width='20px'
                                height='20px'
                                bgColor='bg.brandBlue.weakShade1'
                                color='icon.brandBlue'
                              />
                              <Text size='label200'> {trans('t_on_track')}</Text>
                            </Flex>
                          }
                          formControlProps={checkboxProps}
                          data-cy='onTrackInput'
                          checked={value}
                          onCheckedChange={({ checked }) => onChange(checked)}
                        />
                      );
                    }}
                  />

                  <Controller
                    name='offTrack'
                    control={control}
                    render={({ field: { value, onChange, ...rest } }) => {
                      return (
                        <BaseFormCheckbox
                          {...rest}
                          id='offTrack'
                          label={
                            <Flex align='center' gap='sm-alt'>
                              <TrafficConeFilled />
                              <Text size='label200'> {trans('t_off_track')}</Text>
                            </Flex>
                          }
                          formControlProps={checkboxProps}
                          data-cy='offTrackInput'
                          checked={value}
                          onCheckedChange={({ checked }) => onChange(checked)}
                        />
                      );
                    }}
                  />
                </Flex>
                <Separator borderColor='border.gray' />
              </VisionOnlyGuard>

              <Flex direction='column' gap='md'>
                <Text size='label200'>{trans('t_show_ponds_that_are')}</Text>

                <Controller
                  name='flagged'
                  control={control}
                  render={({ field: { value, onChange, ...rest } }) => {
                    return (
                      <BaseFormCheckbox
                        {...rest}
                        id='flagged'
                        label={trans('t_flagged_ponds')}
                        formControlProps={checkboxProps}
                        data-cy='FlaggedInput'
                        checked={value}
                        onCheckedChange={({ checked }) => onChange(checked)}
                      />
                    );
                  }}
                />
                <VisionOnlyGuard>
                  <Flex align='center' gap='sm-alt'>
                    <Controller
                      name='aboveCarryingCapacityFlag'
                      control={control}
                      render={({ field: { value, onChange, ...rest } }) => {
                        return (
                          <BaseFormCheckbox
                            {...rest}
                            id='aboveCarryingCapacityFlag'
                            label={trans('t_above')}
                            formControlProps={{ ...checkboxProps, w: 'unset' }}
                            data-cy='aboveCarryingCapacityFlagInput'
                            checked={value}
                            onCheckedChange={({ checked }) => onChange(checked)}
                          />
                        );
                      }}
                    />

                    <BaseFormNumberInput
                      required
                      control={control}
                      name='aboveCarryingCapacity'
                      w='100px'
                      numericFormatProps={{
                        decimalScale: 0
                      }}
                      inputRightElement={<Text size='label200'>%</Text>}
                      inputProps={inputProps}
                      disabled={!aboveCarryingCapacityFlagWatch}
                    />
                    <Text as='span' size='label200' textTransform='lowercase'>
                      {trans('t_carrying_capacity')}
                    </Text>
                  </Flex>
                </VisionOnlyGuard>
              </Flex>

              <Separator borderColor='border.gray' />

              <Flex direction='column' gap='md'>
                <RowContainer title={trans('t_days_of_culture')} titleProps={{ alignSelf: 'flex-end', mb: 'sm' }}>
                  <BaseFormNumberInput
                    label={trans('t_more_than')}
                    formLabelProps={{
                      mb: 'md',
                      color: 'text.gray.weak'
                    }}
                    required
                    control={control}
                    name='cycleDays.moreThan'
                    w='100px'
                    numericFormatProps={{
                      decimalScale: 0
                    }}
                    inputRightElement={
                      <Text size='label200' pe='xs'>
                        {trans('t_days')}
                      </Text>
                    }
                    inputProps={{ ...inputProps, placeholder: trans('t_min') }}
                  />
                  <BaseFormNumberInput
                    label={trans('t_less_than')}
                    formLabelProps={{
                      mb: 'md',
                      color: 'text.gray.weak'
                    }}
                    required
                    control={control}
                    name='cycleDays.lessThan'
                    w='100px'
                    numericFormatProps={{
                      decimalScale: 0
                    }}
                    inputRightElement={
                      <Text size='label200' pe='xs'>
                        {trans('t_days')}
                      </Text>
                    }
                    inputProps={{ ...inputProps, placeholder: trans('t_max') }}
                  />

                  <BaseButton
                    variant='link'
                    size='sm'
                    textStyle='label.200'
                    alignSelf='flex-end'
                    mb='xs'
                    disabled={!cycleDaysWatch?.moreThan && !cycleDaysWatch?.lessThan}
                    onClick={() => {
                      setValue('cycleDays.lessThan', NaN);
                      setValue('cycleDays.moreThan', NaN);
                    }}
                    data-cy='cycleDays-clear-btn'
                  >
                    {trans('t_clear')}
                  </BaseButton>
                </RowContainer>

                <RowContainer title={trans('t_abw_g')}>
                  <BaseFormNumberInput
                    required
                    control={control}
                    name='abw.moreThan'
                    w='100px'
                    numericFormatProps={{
                      decimalScale: 0
                    }}
                    inputProps={{ ...inputProps, placeholder: trans('t_min') }}
                  />
                  <BaseFormNumberInput
                    required
                    control={control}
                    name='abw.lessThan'
                    w='100px'
                    numericFormatProps={{
                      decimalScale: 0
                    }}
                    inputProps={{ ...inputProps, placeholder: trans('t_max') }}
                  />

                  <BaseButton
                    variant='link'
                    size='sm'
                    textStyle='label.200'
                    disabled={!abwWatch?.moreThan && !abwWatch?.lessThan}
                    onClick={() => {
                      setValue('abw.lessThan', NaN);
                      setValue('abw.moreThan', NaN);
                    }}
                    data-cy='abw-clear-btn'
                  >
                    {trans('t_clear')}
                  </BaseButton>
                </RowContainer>
              </Flex>

              <Flex align='center' gap='sm-alt' justify='flex-end'>
                <BaseButton
                  variant='secondary'
                  w='max-content'
                  onClick={() => setOpen(false)}
                  analyticsId={actionsName.pondListViewFilterCancelClicked}
                  data-cy='cancel-btn'
                >
                  {trans('t_cancel')}
                </BaseButton>
                <BaseButton
                  type='submit'
                  w='max-content'
                  analyticsId={actionsName.pondListViewFilterApplyClicked}
                  data-cy='submit-btn'
                >
                  {trans('t_apply')}
                </BaseButton>
              </Flex>
            </chakra.form>
          </PopoverBody>
        </PopoverContent>
      </PopoverRoot>
    </Flex>
  );
}

interface RowContainerProps extends FlexProps {
  title: string;
  titleProps?: TextProps;
}

function RowContainer(props: RowContainerProps) {
  const { title, children, titleProps, ...rest } = props;
  return (
    <Flex align='center' gap={{ base: 'sm-alt', md: 'md' }} {...rest}>
      <Text
        w={{ base: '77px', md: '100px' }}
        overflow='hidden'
        textOverflow='ellipsis'
        title={title}
        size='label200'
        whiteSpace='nowrap'
        {...titleProps}
      >
        {title}
      </Text>
      {children}
    </Flex>
  );
}
