import { ReactNode, useState } from 'react';
import { Box, Heading, useMediaQuery } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { BaseButton, BaseButtonProps } from '@components/base/base-button';
import { LineUpFilled } from '@icons/line-up/line-up-filled';
import { RepeatFilledIcon } from '@icons/repeat/repeat-filled-icon';
import { DialogBody, DialogContent, DialogHeader, DialogRoot, DialogTitle } from '@components/ui/dialog';

interface MobileLandscapeOnlyModalProps {
  title?: string;
  children: ReactNode;
  headerActionComponent?: ReactNode;
  buttonProps?: Omit<BaseButtonProps, 'children'>;
  buttonIcon?: ReactNode;
  buttonTitle: string;
}

//@warning - don't change without testing all other places it is being used on all screens.
export function MobileLandscapeOnlyModal(props: MobileLandscapeOnlyModalProps) {
  const { title, children, headerActionComponent, buttonProps, buttonIcon, buttonTitle } = props;
  const [open, setOpen] = useState(false);
  const [isLandscape, isLargerThan700] = useMediaQuery(['(orientation: landscape)', '(min-width: 700px)'], {
    ssr: false
  });
  const isContentVisible = isLandscape || isLargerThan700;

  return (
    <DialogRoot
      onOpenChange={(e) => setOpen(e.open)}
      size='full'
      open={open}
      restoreFocus={false}
      scrollBehavior='inside'
    >
      <BaseButton
        borderRadius='xl'
        py='sm-alt'
        h='50px'
        w='100%'
        minW='fit-content'
        whiteSpace='normal'
        px='sm-alt'
        variant='outline'
        display={{ base: 'flex', lg: 'none' }}
        onClick={() => setOpen(true)}
        {...buttonProps}
      >
        {buttonIcon || <LineUpFilled />} {buttonTitle}
      </BaseButton>

      <DialogContent closeTriggerProps={{ top: isContentVisible ? '20px' : '56px' }}>
        {title && isContentVisible && (
          <DialogHeader
            gap='sm-alt'
            flexWrap='wrap'
            display='flex'
            alignItems='center'
            justifyContent='space-between'
            pe='2xl-alt'
            pb='md-alt'
          >
            <DialogTitle
              maxW={{ base: '190px', md: 'max-content' }}
              whiteSpace='nowrap'
              overflow='hidden'
              textOverflow='ellipsis'
            >
              {title}
            </DialogTitle>
            {isContentVisible && headerActionComponent}
          </DialogHeader>
        )}

        <PortraitModalBodyWrapper isContentVisible={isContentVisible}>{children}</PortraitModalBodyWrapper>
      </DialogContent>
    </DialogRoot>
  );
}

function PortraitModalBodyWrapper({ children, isContentVisible }: { children: ReactNode; isContentVisible: boolean }) {
  const { trans } = getTrans();

  if (!isContentVisible)
    return (
      <DialogBody display='flex' alignItems='center' justifyContent='center' px='xl'>
        <Box textAlign='center' maxW='300px'>
          <RepeatFilledIcon mb='sm-alt' />
          <Heading>{trans('t_rotate_your_phone_content_msg')}</Heading>
        </Box>
      </DialogBody>
    );

  return (
    <DialogBody display='flex' flexDirection='column' p={0}>
      {children}
    </DialogBody>
  );
}
