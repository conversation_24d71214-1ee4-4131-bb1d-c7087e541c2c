import { Box, BoxProps, SpinnerProps } from '@chakra-ui/react';
import { BaseSpinner } from '@components/base/base-spinner';

type SectionLoaderProps = BoxProps & {
  isLoading?: boolean;
  spinnerProps?: SpinnerProps;
};

export function SectionLoader(props: SectionLoaderProps) {
  const { isLoading = false, spinnerProps, ...boxProps } = props;

  if (!isLoading) {
    return null;
  }

  return (
    <Box
      pos='absolute'
      zIndex='overlay'
      inset='0'
      display='flex'
      justifyContent='center'
      alignItems='center'
      bg='rgba(255, 255, 255, 0.5)'
      data-cy='section-loader'
      {...boxProps}
    >
      <BaseSpinner {...spinnerProps} />
    </Box>
  );
}
