'use client';
import { HotTable, HotTableProps } from '@handsontable/react-wrapper';
import { registerAllModules } from 'handsontable/registry';
import { useRef } from 'react';
import 'handsontable/styles/handsontable.min.css';
import 'handsontable/styles/ht-theme-main.min.css';
import type { CellChange, RowObject } from 'handsontable/common';

registerAllModules();

type DataType = (number | string)[][];

interface HotTableComponentProps extends HotTableProps {
  onChange?: (data: DataType) => unknown;
  data: DataType | RowObject[];
}

export function HotTableComponent(props: HotTableComponentProps) {
  const { data, onChange, ...rest } = props;
  const instance = useRef(null);
  const handleChange = (changes: CellChange[]) => {
    const newData = instance?.current?.hotInstance?.getData();
    if (!changes?.length) return;
    onChange?.(newData);
  };

  const handleMenuChanges = () => {
    const newData = instance?.current?.hotInstance?.getData();
    onChange?.(newData);
  };

  return (
    <HotTable
      ref={instance}
      data={data}
      height='auto'
      width='auto'
      rowHeights={30}
      stretchH='all'
      licenseKey='non-commercial-and-evaluation'
      afterChange={handleChange}
      beforeRemoveRow={(index) => index > 0}
      afterRemoveRow={handleMenuChanges}
      afterCreateRow={handleMenuChanges}
      {...rest}
    />
  );
}
