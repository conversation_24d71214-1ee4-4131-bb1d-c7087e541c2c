import {
  Box,
  BoxProps,
  Field,
  FieldErrorTextProps,
  FieldHelperTextProps,
  FieldLabelProps,
  FieldRootProps
} from '@chakra-ui/react';
import { DayPicker, IDayPickerInput } from '@components/form/day-picker';
import { useAppSelector } from '@redux/hooks';
import { OptionalIndicator } from '@components/base/optional-indicator';
import { ReactNode } from 'react';

interface FormControlDateInputProps<T> extends Omit<FieldRootProps, 'onChange'>, IDayPickerInput<T> {
  error: string;
  id: string;
  label?: string;
  isFlex?: boolean;
  required?: boolean;
  labelProps?: FieldLabelProps;
  isOptional?: boolean;
  containerProps?: BoxProps;
  displayAsDropdown?: boolean;
  hideDropdownIcon?: boolean;
  helperText?: ReactNode;
  helperTextProps?: FieldHelperTextProps;
  errorTextProps?: FieldErrorTextProps;
}

export function FormControlDateInput<T>(props: FormControlDateInputProps<T>) {
  const lang = useAppSelector((state) => state.app?.lang);

  const {
    error,
    id,
    name,
    label,
    disabled = false,
    control,
    isFlex = false,
    required,
    placement,
    labelProps = {},
    minDate,
    maxDate,
    inputProps,
    isOptional = false,
    dateFormat = lang === 'es' ? 'yyyy MMM dd' : 'yyyy-MM-dd',
    placeholder,
    onChange,
    containerProps,
    highlightDates,
    isClearable = true,
    displayAsDropdown = false,
    hideDropdownIcon = false,
    portalId,
    helperText,
    helperTextProps,
    errorTextProps,
    css,
    ...rest
  } = props;

  const flexStyle: FieldRootProps = isFlex ? { orientation: 'horizontal', justifyContent: 'flex-start' } : {};

  return (
    <Field.Root
      css={{
        '& .react-datepicker': {
          fontFamily: 'inherit',
          border: 'none',
          boxShadow: 'elevation.400',
          borderRadius: 'lg'
        },

        '& .react-datepicker__header': {
          backgroundColor: 'white',
          borderBottom: 'none',
          padding: '16px 16px 6px'
        },

        '& .react-datepicker__month': {
          margin: '0',
          paddingBottom: 'md',
          px: 'md'
        },

        '& .react-datepicker__day-name': {
          fontSize: '12px',
          fontWeight: '600',
          width: '30px',
          lineHeight: '30px'
        },

        '& .react-datepicker__triangle': {
          display: 'none'
        },

        '& .react-datepicker__day': {
          outline: 'none',
          width: '30px',
          color: 'gray.800',
          fontSize: '12px',
          fontWeight: '600',
          lineHeight: '30px',
          borderRadius: '100px !important'
        },

        '& .react-datepicker__day--disabled': {
          color: 'gray.500',
          cursor: 'not-allowed'
        },

        '& .react-datepicker__day--keyboard-selected': {
          backgroundColor: 'transparent'
        },

        '& .react-datepicker__day:hover': {
          color: 'gray.800 !important',
          backgroundColor: 'gray.200 !important'
        },

        '& .react-datepicker__day--highlighted': {
          color: 'gray.800 !important',
          backgroundColor: 'brandBlue.200 !important'
        },

        '& .react-datepicker__day--selected': {
          color: 'white !important',
          backgroundColor: 'graphBrandBlue !important'
        },

        '& .react-datepicker__day--outside-month': {
          opacity: '0.5'
        },
        ...css
      }}
      id={id}
      required={required}
      invalid={!!error}
      {...flexStyle}
      {...rest}
    >
      {label && (
        <Field.Label _disabled={{ color: 'text.gray.disabled' }} textStyle='label.200' {...labelProps}>
          {label}
          {!required && isOptional && <OptionalIndicator />}
        </Field.Label>
      )}
      <Box display='flex' flexDirection='column' gap={displayAsDropdown ? 0 : '2xs'} w='full' {...containerProps}>
        <DayPicker<T>
          id={id}
          name={name}
          locale={lang}
          control={control}
          disabled={disabled}
          placement={placement}
          minDate={minDate}
          maxDate={maxDate}
          portalId={portalId}
          isClearable={isClearable}
          dateFormat={dateFormat}
          placeholder={placeholder}
          inputProps={{ ...inputProps, error }}
          onChange={onChange}
          highlightDates={highlightDates}
          displayAsDropdown={displayAsDropdown}
          hideDropdownIcon={hideDropdownIcon}
        />

        {helperText && (
          <Field.HelperText textStyle='label.300' data-cy={`helper-text-${id}`} {...helperTextProps}>
            {helperText}
          </Field.HelperText>
        )}

        {error && (
          <Field.ErrorText
            textStyle='label.300'
            data-cy={`error-${id}`}
            {...errorTextProps}
          >{`${error}`}</Field.ErrorText>
        )}
      </Box>
    </Field.Root>
  );
}
