import { Flex, FlexProps, Grid, Text } from '@chakra-ui/react';
import { BaseLink } from '@components/base/base-link';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { useRouter } from 'next/router';
import { ElementType, useState } from 'react';
import { useAnalytics } from '@hooks/use-analytics';
import { actionsName } from '@utils/segment';
import { GridFourFilled } from '@icons/grid-four/grid-four-filled';
import { HomeFilled } from '@icons/home/<USER>';
import { SettingsFilled } from '@icons/settings/settings-filled';
import { UserFilled } from '@icons/user/user-filled';
import { ChevronDownFilled } from '@icons/chevron-down/chevron-down-filled';
import { ChevronUpFilled } from '@icons/chevron-up/chevron-up-filled';
import { KampiLogo } from '@icons/logo/kampi-logo';
import { useEnableTechnicalFeatures } from '@hooks/use-is-xpertsea-user';
import { BenchmarkIcon } from '@icons/benchmark-icon';
import { BellBadgeIcon } from '@icons/bell-badge-icon';
import { VisionOnlyGuard } from '@components/permission-wrappers/vision-only-guard';

export function NavigationSidebar() {
  const { trans } = getTrans();
  const { route } = useRouter();
  const { trackAction } = useAnalytics();

  const { currentFarmId, currentFarmEid, currentFarmSlug, currentFarmName, isLoadingFarmData } = useAppSelector(
    (state) => state.farm
  );
  const enableTechnicalFeatures = useEnableTechnicalFeatures();
  const unReadNotificationsCount = useAppSelector((state) => state.notifications?.unReadNotificationsCount);

  const nurseryRoutes = [
    '/farm/[farmEid]/nursery',
    '/farm/[farmEid]/nursery/[nurseryEid]',
    '/farm/[farmEid]/nursery/[nurseryEid]/population/[populationId]'
  ];

  const monitoringStatusRoutes = ['/farm/[farmEid]/monitoring-status'];

  const dataUpdaterRoutes = ['/farm/[farmEid]/data-updater', '/farm/[farmEid]/data-updater/nursery'];

  const farmSummaryRoutes = [
    '/farm/[farmEid]',
    '/farm/[farmEid]/harvests',
    '/farm/[farmEid]/pond/[pondEid]',
    '/farm/[farmEid]/pond/[pondEid]/population/[populationId]'
  ];

  const farmRoutes = [...farmSummaryRoutes, ...dataUpdaterRoutes, ...monitoringStatusRoutes, ...nurseryRoutes];

  const accountRoutes = ['/my-account', '/users'];

  return (
    <Flex
      direction='column'
      pt='md'
      pb='md-alt'
      pe='4xs'
      height='100%'
      bg='bg.squidInkPowder.down'
      color='white'
      pos='relative'
      textAlign='center'
      w='100%'
    >
      <KampiLogo px='sm-alt' w='100%' mt='sm' mb='md' display={{ base: 'none', md: 'block' }} />

      {!isLoadingFarmData && (
        <>
          <BaseLink
            mt='4xs'
            onClick={() =>
              trackAction(actionsName.sidebarFarmClicked, { farmEid: currentFarmEid, farmId: currentFarmId })
            }
            route='/farm/[farmEid]'
            params={{ farmEid: currentFarmSlug }}
            display='block'
          >
            <OneLink isActive={farmRoutes.includes(route) || route === '/'} icon={HomeFilled} isMulti>
              <Text size='label200'> {trans('t_farm')}</Text>
            </OneLink>
          </BaseLink>
          {farmRoutes.includes(route) && (
            <Flex direction='column' gap='4xs' mt='4xs'>
              <BaseLink
                onClick={() => {
                  trackAction(actionsName.farmTabSelected, {
                    tab: 'farm-summary',
                    farmEid: currentFarmSlug,
                    farmId: currentFarmId,
                    farmName: currentFarmName
                  }).then();
                }}
                route='/farm/[farmEid]'
                params={{ farmEid: currentFarmSlug }}
                display='block'
                data-cy='farm-summary-link'
              >
                <OneLink isActive={farmSummaryRoutes.includes(route)} isSubLink minH='32px'>
                  <Text size='label300' textAlign='justify'>
                    {trans('t_summary')}
                  </Text>
                </OneLink>
              </BaseLink>
              <VisionOnlyGuard>
                <BaseLink
                  onClick={() => {
                    trackAction(actionsName.farmTabSelected, {
                      tab: 'nursery',
                      farmEid: currentFarmSlug,
                      farmId: currentFarmId,
                      farmName: currentFarmName
                    }).then();
                  }}
                  route='/farm/[farmEid]/nursery'
                  params={{ farmEid: currentFarmSlug }}
                  display='block'
                  data-cy='nursery-link'
                >
                  <OneLink isActive={nurseryRoutes.includes(route)} isSubLink minH='32px'>
                    <Text size='label300' textAlign='justify'>
                      {trans('t_nurseries')}
                    </Text>
                  </OneLink>
                </BaseLink>
              </VisionOnlyGuard>

              <BaseLink
                onClick={() => {
                  trackAction(actionsName.farmTabSelected, {
                    tab: 'data-updater',
                    farmEid: currentFarmSlug,
                    farmId: currentFarmId,
                    farmName: currentFarmName
                  }).then();
                }}
                route='/farm/[farmEid]/data-updater'
                params={{ farmEid: currentFarmSlug }}
                display='block'
                data-cy='data-updater-link'
              >
                <OneLink isActive={dataUpdaterRoutes.includes(route)} isSubLink minH='32px'>
                  <Text size='label300' textAlign='justify'>
                    {trans('t_data_updater')}
                  </Text>
                </OneLink>
              </BaseLink>

              {enableTechnicalFeatures && (
                <BaseLink
                  onClick={() => {
                    trackAction(actionsName.farmTabSelected, {
                      tab: 'monitoring-status',
                      farmEid: currentFarmSlug,
                      farmId: currentFarmId,
                      farmName: currentFarmName
                    }).then();
                  }}
                  route='/farm/[farmEid]/monitoring-status'
                  params={{ farmEid: currentFarmSlug }}
                  display='block'
                  data-cy='monitoring-link'
                >
                  <OneLink isActive={monitoringStatusRoutes.includes(route)} isSubLink minH='32px'>
                    <Text size='label300' textAlign='justify'>
                      {trans('t_monitoring_status')}
                    </Text>
                  </OneLink>
                </BaseLink>
              )}
            </Flex>
          )}
          <VisionOnlyGuard>
            <BaseLink
              onClick={() => {
                trackAction(actionsName.sidebarHomeClicked).then();
              }}
              route={'/farm/[farmEid]/dashboard'}
              params={{ farmEid: currentFarmSlug }}
              display='block'
              data-cy='dashboard-sidebar-link'
            >
              <OneLink isActive={route === '/farm/[farmEid]/dashboard'} icon={GridFourFilled}>
                <Text size='label200'>{trans('t_dashboard')}</Text>
              </OneLink>
            </BaseLink>

            {enableTechnicalFeatures && (
              <BaseLink
                onClick={() => {
                  trackAction(actionsName.sidebarHomeClicked).then();
                }}
                route={'/farm/[farmEid]/group-performance'}
                params={{ farmEid: currentFarmSlug }}
                display='block'
                data-cy='group-performance-sidebar-link'
              >
                <OneLink isActive={route === '/farm/[farmEid]/group-performance'} icon={GridFourFilled}>
                  <Text size='label200'>{trans('t_group_performance')}</Text>
                </OneLink>
              </BaseLink>
            )}

            <BaseLink
              mt='4xs'
              route='/farm/[farmEid]/cycle-comparison'
              params={{ farmEid: currentFarmSlug }}
              display='block'
            >
              <OneLink isActive={route === '/farm/[farmEid]/cycle-comparison'} icon={BenchmarkIcon}>
                <Grid>
                  <Text
                    size='label200'
                    overflow='hidden'
                    whiteSpace='nowrap'
                    textOverflow='ellipsis'
                    title={trans('t_benchmark')}
                  >
                    {trans('t_benchmark')}
                  </Text>
                </Grid>
              </OneLink>
            </BaseLink>
          </VisionOnlyGuard>
        </>
      )}

      <Flex direction='column' mt='auto'>
        {!isLoadingFarmData && enableTechnicalFeatures && (
          <BaseLink
            onClick={() =>
              trackAction(actionsName.sidebarSettingsClicked, { farmEid: currentFarmEid, farmId: currentFarmId })
            }
            route='/farm/[farmEid]/notifications'
            params={{ farmEid: currentFarmSlug }}
            display='block'
            data-cy='notifications-nav-link'
          >
            <OneLink isActive={route === '/farm/[farmEid]/notifications'} icon={BellBadgeIcon} pos='relative'>
              <Text size='label200'>{trans('t_notifications')}</Text>
              {!!unReadNotificationsCount && (
                <Flex
                  align='center'
                  pos='absolute'
                  top='sm'
                  insetStart='xs'
                  bg='semanticRed.600'
                  rounded='full'
                  w='16px'
                  h='16px'
                  lineHeight={'16px'}
                  justify='center'
                >
                  <Text textStyle='paragraph.400.light' color='white'>
                    {unReadNotificationsCount}
                  </Text>
                </Flex>
              )}
            </OneLink>
          </BaseLink>
        )}

        {!isLoadingFarmData && (
          <BaseLink
            onClick={() =>
              trackAction(actionsName.sidebarSettingsClicked, { farmEid: currentFarmEid, farmId: currentFarmId })
            }
            route='/farm/[farmEid]/settings'
            params={{ farmEid: currentFarmSlug }}
            display='block'
            data-cy='settings-nav-link'
          >
            <OneLink isActive={route === '/farm/[farmEid]/settings'} icon={SettingsFilled}>
              <Text size='label200'>{trans('t_config')}</Text>
            </OneLink>
          </BaseLink>
        )}

        <BaseLink route='/my-account' display='block' data-cy='account-nav-link' mt='4xs'>
          <OneLink isActive={accountRoutes.includes(route)} icon={UserFilled} isMulti>
            <Text size='label200'>{trans('t_profile')}</Text>
          </OneLink>
        </BaseLink>
        {accountRoutes.includes(route) && (
          <Flex direction='column' gap='4xs' mt='4xs'>
            <BaseLink route='/my-account' display='block' data-cy='my-team-nav-link'>
              <OneLink isActive={route === '/my-account'} isSubLink minH='32px'>
                <Text size='label300'>{trans('t_account')}</Text>
              </OneLink>
            </BaseLink>
            <BaseLink route='/users' display='block' data-cy='my-team-nav-link'>
              <OneLink isActive={route === '/users'} isSubLink minH='32px'>
                <Text size='label300'>{trans('t_my_team')}</Text>
              </OneLink>
            </BaseLink>
            <BaseLink
              onClick={() => trackAction(actionsName.logoutSuccessful).then()}
              route='/logout'
              display='block'
              data-cy='logout-btn'
            >
              <OneLink isActive={route === '/logout'} isSubLink minH='32px'>
                <Text size='label300'>{trans('t_logout')}</Text>
              </OneLink>
            </BaseLink>
          </Flex>
        )}
      </Flex>
    </Flex>
  );
}

interface OneLinkProps extends FlexProps {
  isActive?: boolean;
  icon?: ElementType;
  isMulti?: boolean;
  isSubLink?: boolean;
  containerProps?: FlexProps;
}

function OneLink(props: OneLinkProps) {
  const { isActive, icon, children, isMulti, isSubLink, containerProps, ...rest } = props;
  const [isHovered, setIsHovered] = useState(false);

  const activeStyles = { color: 'black', bgColor: isSubLink ? 'bg.gray.strong' : 'white' };

  const isActiveIconProps = { firstSectionColor: 'icon.brandBlue', secondSectionColor: 'icon.brandBlue.weakShade3' };
  const Icon = icon;
  return (
    <Flex
      px='sm-alt'
      py={isSubLink ? 'unset' : 'md'}
      gap='xs-alt'
      textAlign='center'
      align='center'
      cursor='pointer'
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      _hover={{ ...activeStyles }}
      {...(isActive ? activeStyles : {})}
      {...rest}
    >
      {!isSubLink && <Icon boxSize='24px' {...(isActive || isHovered ? isActiveIconProps : {})} />}
      <Flex ms={isSubLink ? '2lg' : 0} align='center' justify='space-between' flex={1} {...containerProps}>
        {children}
        {!isActive && isMulti && (
          <ChevronDownFilled color={isHovered ? 'icon.gray' : 'white'} hasBackground={false} w='20px' h='20px' />
        )}
        {isActive && isMulti && <ChevronUpFilled color='icon.gray' hasBackground={false} w='20px' h='20px' />}
      </Flex>
    </Flex>
  );
}
