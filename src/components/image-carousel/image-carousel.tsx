import Image from 'next/image';
import '@splidejs/react-splide/css';
import { Box, Flex, Text } from '@chakra-ui/react';
import { fixedBlurDataUrl } from '@utils/image';
import { Dispatch, SetStateAction, SyntheticEvent, useEffect, useState } from 'react';
import { ChevronLeftFilled } from '@icons/chevron-left/chevron-left-filled';
import { ChevronRightFilled } from '@icons/chevron-right/chevron-right-filled';
// @ts-expect-error waiting Splide update
import { Splide, SplideSlide, SplideTrack } from '@splidejs/react-splide';
import { Splide as SplideCore } from '@splidejs/splide';

interface Slide {
  imageAlt: string;
  imageUrl: string;
}

export type ImageCarouselProps = {
  slides: Slide[];
  isExpanded?: boolean;
  onSlideClick?(imageSlide: Slide): unknown;
  onSlideMove?(imageIndex: number): unknown;
  setIsExpanded?: Dispatch<SetStateAction<boolean>>;
};

export function ImageCarousel(props: ImageCarouselProps) {
  const { slides, isExpanded, onSlideClick, onSlideMove, setIsExpanded } = props;

  const [currentSlide, setCurrentSlide] = useState(0);

  useEffect(() => {
    if (!isExpanded) return;

    const handleEscPress = (event: KeyboardEvent) => {
      if (event.key !== 'Escape') return;
      setIsExpanded?.(false);
    };

    window.addEventListener('keydown', handleEscPress);

    return () => {
      window.removeEventListener('keydown', handleEscPress);
    };
  }, [isExpanded]);

  const isFirstSlide = currentSlide === 0;
  const isLastSlide = currentSlide === slides.length - 1;

  return (
    <Flex flexDir='column' gap='md-alt'>
      <Splide
        hasTrack={false}
        onMove={(core: SplideCore) => {
          onSlideMove?.(core.index);
          setCurrentSlide(core.index);
        }}
        options={{ perPage: 1, pagination: false, height: isExpanded ? 'calc(96vh - 100px)' : '280px' }}
      >
        <SplideTrack>
          {slides.map((slide, index) => (
            <Box
              key={index}
              display='flex'
              as={SplideSlide}
              alignItems='center'
              justifyContent='center'
              onClick={() => onSlideClick?.(slide)}
            >
              <Box
                h='100%'
                display='flex'
                alignItems='center'
                justifyContent='center'
                w={isExpanded ? '90%' : '100%'}
                css={{
                  '&.slide-image': { position: 'unset !important', w: 'unset !important', h: 'unset !important' }
                }}
              >
                <Image
                  fill
                  quality={100}
                  placeholder='blur'
                  unoptimized={true}
                  src={slide.imageUrl}
                  alt={slide.imageAlt}
                  className='slide-image'
                  blurDataURL={fixedBlurDataUrl}
                  onError={(event: SyntheticEvent<HTMLImageElement>) => {
                    //  Remove border radius on error
                    (event.target as HTMLImageElement).style.borderRadius = 'unset';
                  }}
                  style={{
                    maxHeight: '100%',
                    maxWidth: '100%',
                    borderRadius: '16px',
                    cursor: isExpanded ? 'auto' : 'zoom-in',
                    objectFit: 'contain'
                  }}
                />
              </Box>
            </Box>
          ))}
        </SplideTrack>
        <div className='splide__arrows'>
          <Box
            left={0}
            w='unset'
            h='unset'
            rounded='none'
            userSelect='none'
            backgroundColor='transparent'
            opacity={isFirstSlide ? 0.5 : 'unset'}
            className='splide__arrow splide__arrow--prev'
            cursor={isFirstSlide ? 'not-allowed' : 'pointer'}
            _hover={{ opacity: `${isFirstSlide ? 0.5 : 'unset'} !important` }}
          >
            <ChevronLeftFilled
              color='gray.800'
              bgColor='gray.400'
              w='30px !important'
              h='30px !important'
              transform='unset !important'
            />
          </Box>
          <Box
            w='unset'
            h='unset'
            right={0}
            rounded='none'
            userSelect='none'
            backgroundColor='transparent'
            opacity={isLastSlide ? 0.5 : 'unset'}
            className='splide__arrow splide__arrow--next'
            cursor={isLastSlide ? 'not-allowed' : 'pointer'}
            _hover={{ opacity: `${isLastSlide ? 0.5 : 'unset'} !important` }}
          >
            <ChevronRightFilled
              color='gray.800'
              bgColor='gray.400'
              w='30px !important'
              h='30px !important'
              transform='unset !important'
            />
          </Box>
        </div>
      </Splide>
      <Text
        alignSelf='center'
        size='light300'
        left={isExpanded ? '50%' : 'auto'}
        zIndex={isExpanded ? 1001 : 'auto'}
        bottom={isExpanded ? '24px' : 'auto'}
        position={isExpanded ? 'absolute' : 'relative'}
        transform={isExpanded ? 'translateX(-50%)' : 'none'}
      >
        {`${currentSlide + 1}/${slides.length}`}
      </Text>
    </Flex>
  );
}
