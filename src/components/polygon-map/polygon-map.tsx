'use client';
import { Box } from '@chakra-ui/react';
import { useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ygon, <PERSON>up, TileLayer, useMap } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import { Icon, LatLngBounds, Polygon as LeafletPolygon } from 'leaflet';

// Fix for default markers in Leaflet
if (typeof window !== 'undefined') {
  delete (Icon.Default.prototype as unknown as { _getIconUrl: string })._getIconUrl;
  Icon.Default.mergeOptions({
    iconRetinaUrl: '/assets/marker-icon-2x.png',
    iconUrl: '/assets/marker-icon.png',
    shadowUrl: '/assets/marker-shadow.png'
  });
}

interface Coordinate {
  latitude: string;
  longitude: string;
}

interface MarkerData {
  latitude: number;
  longitude: number;
  tooltip?: string;
  color?: string;
  size?: 'small' | 'medium' | 'large';
}

interface PolygonMapProps {
  coordinates: Coordinate[];
  onCoordinatesChange?: (coordinates: Coordinate[]) => void;
  height?: string;
  isDrawingMode?: boolean;
  showMarker?: boolean;
  markerPosition?: { latitude: number; longitude: number };
  markerPositions?: MarkerData[];
}

function DrawingLayer({
  coordinates,
  isDrawingMode
}: {
  coordinates: Coordinate[];
  onCoordinatesChange?: (coordinates: Coordinate[]) => void;
  isDrawingMode?: boolean;
}): null {
  const map = useMap();
  const drawingRef = useRef<LeafletPolygon | null>(null);

  // Update drawing polygon and recenter map
  useEffect(() => {
    if (!isDrawingMode || coordinates.length < 3) return;

    const latlngs = coordinates.map(
      (coord) => [parseFloat(coord.latitude), parseFloat(coord.longitude)] as [number, number]
    );

    if (drawingRef.current) {
      drawingRef.current.setLatLngs(latlngs);
    } else {
      drawingRef.current = new LeafletPolygon(latlngs, {
        color: 'red',
        weight: 2,
        fillColor: 'red',
        fillOpacity: 0.2
      }).addTo(map);
    }

    // Recenter map to fit the polygon
    if (latlngs.length > 0) {
      const bounds = new LatLngBounds(latlngs);
      map.fitBounds(bounds, { padding: [20, 20] });
    }
  }, [coordinates, map, isDrawingMode]);

  return null;
}

function MapController({
  coordinates,
  showMarker,
  markerPosition,
  markerPositions
}: {
  coordinates: Coordinate[];
  showMarker?: boolean;
  markerPosition?: { latitude: number; longitude: number };
  markerPositions?: MarkerData[];
}): null {
  const map = useMap();

  useEffect(() => {
    const validCoordinates = coordinates.filter(
      (coord) =>
        coord.latitude && coord.longitude && !isNaN(parseFloat(coord.latitude)) && !isNaN(parseFloat(coord.longitude))
    );

    // Create bounds that include both markers and polygon coordinates
    let bounds: LatLngBounds | null = null;

    // Add polygon coordinates to bounds if they exist
    if (validCoordinates.length >= 3) {
      const latlngs = validCoordinates.map(
        (coord) => [parseFloat(coord.latitude), parseFloat(coord.longitude)] as [number, number]
      );
      bounds = new LatLngBounds(latlngs);
    }

    // Add marker positions to bounds
    if (markerPositions && markerPositions.length > 0) {
      const markerBounds = new LatLngBounds(markerPositions.map((pos) => [pos.latitude, pos.longitude]));
      if (bounds) {
        bounds.extend(markerBounds);
      } else {
        bounds = markerBounds;
      }
    }

    // Add single marker to bounds if no marker positions
    if (showMarker && markerPosition && !markerPositions?.length) {
      if (bounds) {
        bounds.extend([markerPosition.latitude, markerPosition.longitude] as [number, number]);
      } else {
        bounds = new LatLngBounds([[markerPosition.latitude, markerPosition.longitude]]);
      }
    }

    // Fit bounds if we have any coordinates or markers
    if (bounds) {
      map.fitBounds(bounds, { padding: [80, 80], maxZoom: 16 });
    }
  }, [coordinates, map, showMarker, markerPosition, markerPositions]);

  return null;
}

export function PolygonMap({
  coordinates,
  height = '400px',
  showMarker = false,
  markerPosition,
  markerPositions
}: PolygonMapProps) {
  const validCoordinates = coordinates.filter(
    (coord) =>
      coord.latitude && coord.longitude && !isNaN(parseFloat(coord.latitude)) && !isNaN(parseFloat(coord.longitude))
  );

  const polygonPositions = validCoordinates.map(
    (coord) => [parseFloat(coord.latitude), parseFloat(coord.longitude)] as [number, number]
  );

  // Spread overlapping markers
  const spreadMarkerPositions = markerPositions || [];

  // Calculate center of polygon or use default
  const center =
    validCoordinates.length > 0
      ? ([
          validCoordinates.reduce((sum, coord) => sum + parseFloat(coord.latitude), 0) / validCoordinates.length,
          validCoordinates.reduce((sum, coord) => sum + parseFloat(coord.longitude), 0) / validCoordinates.length
        ] as [number, number])
      : ([0, 0] as [number, number]);

  return (
    <Box height={height} width='100%' border='1px solid' borderColor='gray.200' borderRadius='md'>
      <MapContainer
        center={center}
        zoom={markerPositions && markerPositions.length > 0 ? 8 : validCoordinates.length > 0 ? 10 : 2}
        style={{ height: '100%', width: '100%' }}
        dragging={true}
        zoomControl={true}
        scrollWheelZoom={true}
        doubleClickZoom={true}
        boxZoom={true}
        keyboard={true}
        attributionControl={false}
      >
        <TileLayer url='https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}' />

        {validCoordinates.length >= 3 && (
          <Polygon
            positions={polygonPositions}
            pathOptions={{
              color: 'blue',
              weight: 3,
              fillColor: 'blue',
              fillOpacity: 0.2
            }}
          />
        )}

        {showMarker && markerPosition && <Marker position={[markerPosition.latitude, markerPosition.longitude]} />}
        {spreadMarkerPositions.map((pos, index) => (
          <Marker key={index} position={[pos.latitude, pos.longitude]} icon={new Icon.Default()}>
            {pos.tooltip && (
              <Popup>
                <div dangerouslySetInnerHTML={{ __html: pos.tooltip }} />
              </Popup>
            )}
          </Marker>
        ))}

        <MapController
          coordinates={coordinates}
          showMarker={showMarker}
          markerPosition={markerPosition}
          markerPositions={markerPositions}
        />
      </MapContainer>
    </Box>
  );
}
