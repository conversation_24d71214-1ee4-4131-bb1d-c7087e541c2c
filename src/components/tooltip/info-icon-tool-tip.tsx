import { Box, Flex, FlexProps, HoverCardRootProps } from '@chakra-ui/react';
import { ReactNode, useState } from 'react';
import {
  HoverCardArrow,
  HoverCardContent,
  HoverCardContentProps,
  HoverCardRoot,
  HoverCardTrigger
} from '@components/ui/hover-card';
import { Placement } from '@floating-ui/utils';
import { InfoFilledIcon, InfoFilledIconProps } from '@icons/info/info-filled-icon';

interface InfoIconToolTipProps extends HoverCardRootProps {
  children: ReactNode;
  iconProps?: InfoFilledIconProps;
  position?: Placement;
  triggerComponent?: ReactNode;
  triggerComponentProps?: FlexProps;
  contentProps?: HoverCardContentProps & { 'data-cy'?: string };
  hasArrow?: boolean;
}

export function InfoIconToolTip(props: InfoIconToolTipProps) {
  const {
    children,
    position,
    triggerComponent,
    triggerComponentProps,
    contentProps,
    iconProps,
    hasArrow = true,
    ...rest
  } = props;

  const [open, setOpen] = useState(false);

  return (
    <HoverCardRoot
      open={open}
      onOpenChange={(value) => setOpen(value.open)}
      positioning={{ placement: position ?? 'top' }}
      lazyMount
      unmountOnExit
      {...rest}
    >
      <HoverCardTrigger asChild>
        <Flex
          onMouseOver={() => {
            setOpen(true);
          }}
          onMouseLeave={() => {
            setOpen(false);
          }}
          onClick={(e) => e.stopPropagation()}
          cursor='pointer'
          align='center'
          justify='center'
          boxSize='20px'
          ms='2xs'
          {...triggerComponentProps}
        >
          {triggerComponent ?? (
            <InfoFilledIcon
              boxSize='20px'
              _hover={{ opacity: 0.8 }}
              firstSectionColor='gray.400'
              secondSectionColor='gray.800'
              {...iconProps}
            />
          )}
        </Flex>
      </HoverCardTrigger>

      <HoverCardContent overflow='initial' w='max-content' maxW='260px' {...contentProps}>
        {hasArrow && <HoverCardArrow />}
        <Box textStyle='label.300' whiteSpace='normal'>
          {children}
        </Box>
      </HoverCardContent>
    </HoverCardRoot>
  );
}
