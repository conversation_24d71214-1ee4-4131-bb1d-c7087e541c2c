import { Box, chakra, Flex, Heading, Input, Text, useBreakpointValue } from '@chakra-ui/react';
import { CSS } from '@dnd-kit/utilities';
import { getTrans } from '@i18n/get-trans';
import { actionsName } from '@utils/segment';
import { SearchDuoIcon } from '@icons/search/search-duo-icon';
import { DragIcon } from '@icons/drag/drag-icon';
import { MinusFilled } from '@icons/minus/minus-filled';
import { BaseButton } from '@components/base/base-button';
import { ReactNode, useEffect, useState } from 'react';
import { IndicatorValues } from '@components/pond-card/helper';
import { BaseFormCheckbox } from '@components/form/base-form-checkbox';
import { Controller, useForm, UseFormReset, UseFormSetValue } from 'react-hook-form';
import { restrictToParentElement, restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { arrayMove, SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { closestCenter, DndContext, DragEndEvent, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { FarmSummaryViewVariable } from '@screens/farm-summary/helpers/pond-list';
import { DialogBody, DialogContent, DialogFooter, DialogHeader, DialogRoot, DialogTitle } from '@components/ui/dialog';
import { InputGroup } from '@components/ui/input-group';
import { CloseFilled } from '@icons/close/close-filled';

const maxSelected = 15;

type CustomizeViewModalProps = {
  isOpen: boolean;
  isLoading: boolean;
  children: ReactNode;
  onClose: () => void;
  pondVariables: string[];
  statusIndicatorValue?: IndicatorValues;
  variableLabels: Record<string, string>;
  onSubmit: (selectedKeys: string[]) => void;
  pondViewVariables: Record<string, { title: string; variables: { label: string; value: string }[] }>;
  hasNoLimit?: boolean;
};

type FormType = Record<string, boolean>;

export function CustomizeViewModal(props: CustomizeViewModalProps) {
  const {
    isOpen,
    children,
    isLoading,
    pondVariables,
    variableLabels,
    pondViewVariables,
    statusIndicatorValue,
    onClose,
    hasNoLimit
  } = props;

  const { trans } = getTrans();

  const mobileDisplay = useBreakpointValue({ base: 'flex', md: 'none' });

  const [search, setSearch] = useState('');
  const [showMobileSelectedVariableTab, setShowMobileSelectedVariableTab] = useState(false);

  const { reset, watch, control, handleSubmit, setValue } = useForm<FormType>();

  const handleResetForm = () => {
    const defaultValues = pondVariables.reduce((acc, key) => {
      acc[key] = true;
      return acc;
    }, {} as FormType);
    reset(defaultValues);
  };

  useEffect(() => {
    if (!isOpen) return;
    handleResetForm();
  }, [JSON.stringify(pondVariables ?? []), isOpen]);

  useEffect(() => {
    if (!isOpen) return;
    if (mobileDisplay === 'none') setShowMobileSelectedVariableTab(false);
  }, [mobileDisplay]);

  const handleClose = () => {
    handleResetForm();
    onClose();
  };

  const watchVariables = watch();

  const selectedVariables = Object.values(watchVariables).filter((value) => value);

  const isVariableDisabled = (variable: string) => {
    if (hasNoLimit) return false;
    return selectedVariables.length >= maxSelected && !watchVariables[variable];
  };

  const isSubmitDisabled = selectedVariables.length < 1;
  const onSubmit = handleSubmit((data: FormType) => {
    if (isSubmitDisabled) return;
    const selectedKeys = Object.keys(data).filter((key) => data[key]);
    props.onSubmit(selectedKeys);
  });

  let pondViewVariablesEntries = Object.entries(pondViewVariables);

  if (search) {
    pondViewVariablesEntries = pondViewVariablesEntries.map(([key, viewVariableItem]) => {
      const { title, variables } = viewVariableItem;
      const filteredVariables = variables.filter((variable) =>
        variable.label.toLowerCase().includes(search.toLowerCase())
      );
      return [key, { title, variables: filteredVariables }];
    });
  }

  return (
    <>
      {children}
      <DialogRoot
        open={isOpen}
        onOpenChange={(e) => {
          if (!e.open) {
            handleClose();
          }
        }}
        size={{ base: 'md', md: 'xl' }}
        scrollBehavior='inside'
      >
        <DialogContent borderRadius='2xl' boxShadow='elevation.400' data-cy='customize-view-modal'>
          <chakra.form
            display='contents'
            onSubmit={(event) => {
              event.stopPropagation();
              onSubmit(event).then();
            }}
          >
            <DialogHeader px='md' pt='md' pb='2lg'>
              <DialogTitle
                display='flex'
                gap={{ base: 'xs-alt', md: 'xl' }}
                flexDirection={{ base: 'column', md: 'row' }}
                minW={{ base: 'auto', md: '370px', lg: '423px' }}
                justifyContent={{ base: 'initial', md: 'space-between' }}
              >
                <Box>
                  <Heading size='heavy300'>{trans('t_customize_view')}</Heading>
                  {!hasNoLimit && (
                    <Text size='light300' mt='xs' color='text.gray.weak'>
                      {trans('t_select_up_to_x_variables_you_want', { count: maxSelected })}
                    </Text>
                  )}
                </Box>

                {!hasNoLimit && (
                  <Text size='heavy300' alignSelf='flex-end'>
                    {trans('t_selected', { selected: selectedVariables.length, total: maxSelected })}
                  </Text>
                )}
              </DialogTitle>
            </DialogHeader>

            <DialogBody display='flex' flexDirection={{ base: 'column', md: 'row' }} px='md' pt={0} pb={0}>
              <Flex mb='xs' bgColor='bg.gray.strong' borderRadius='4xl' display={mobileDisplay}>
                <Text
                  py='md'
                  px='2sm'
                  flex={1}
                  size='label100'
                  cursor='pointer'
                  borderRadius='4xl'
                  textAlign='center'
                  bgColor={showMobileSelectedVariableTab ? 'transparent' : 'bg.squidInkPowder.down'}
                  color={showMobileSelectedVariableTab ? 'text.gray' : 'white'}
                  onClick={() => setShowMobileSelectedVariableTab(false)}
                >
                  {trans('t_variables')}
                </Text>
                <Text
                  py='md'
                  px='2sm'
                  flex={1}
                  size='label100'
                  cursor='pointer'
                  textAlign='center'
                  borderRadius='4xl'
                  bgColor={showMobileSelectedVariableTab ? 'bg.squidInkPowder.down' : 'transparent'}
                  color={showMobileSelectedVariableTab ? 'white' : 'text.gray'}
                  onClick={() => setShowMobileSelectedVariableTab(true)}
                >
                  {trans('t_selected_variables')}
                </Text>
              </Flex>

              {!showMobileSelectedVariableTab && (
                <Flex
                  pb='md'
                  maxH='660px'
                  overflow='auto'
                  direction='column'
                  position='relative'
                  pe={{ base: '0', md: 'md' }}
                  borderRight={{ base: 'none', md: '1px solid {colors.gray.400}' }}
                  w={{ base: '100%', md: '370px', lg: '450px' }}
                  data-cy='customize-view-variables-list'
                  className='hideScrollbarTrack'
                >
                  <Box bgColor='white' px='4xs' pt='3xs' pos='sticky' top={0} zIndex={10}>
                    <InputGroup
                      w='full'
                      startElement={<SearchDuoIcon pointerEvents='none' />}
                      endElement={
                        search && <CloseFilled hasBackground={false} cursor='pointer' onClick={() => setSearch('')} />
                      }
                    >
                      <Input
                        value={search}
                        borderRadius='full'
                        fontSize='lg'
                        outline='none'
                        autoFocus
                        autoComplete='off'
                        placeholder={trans('t_search')}
                        _placeholder={{ color: 'text.gray.disabled' }}
                        onChange={(e) => {
                          setSearch(e.target.value);
                          e.stopPropagation();
                        }}
                        data-cy='variables-search-input'
                      />
                    </InputGroup>
                  </Box>

                  <Box css={{ '& > :last-child': { borderBottom: 'none', paddingBottom: 0 } }}>
                    {pondViewVariablesEntries.map(([key, viewVariableItem]) => {
                      const { title, variables } = viewVariableItem;

                      if (!variables?.length) return null;

                      return (
                        <Flex
                          gap='md'
                          py='2lg'
                          key={key}
                          flexDir='column'
                          borderBottom='1px solid'
                          borderColor='border.gray'
                        >
                          <Text size='label200'>{title}</Text>
                          {variables.map((variable) => (
                            <Controller
                              name={variable.value}
                              key={variable.value}
                              control={control}
                              render={({ field: { value, onChange, ...rest } }) => {
                                return (
                                  <BaseFormCheckbox
                                    {...rest}
                                    id={variable.value}
                                    label={variable.label}
                                    checked={value}
                                    onCheckedChange={({ checked }) => onChange(checked)}
                                    disabled={isVariableDisabled(variable.value)}
                                    data-cy={`${variable.value}-label`}
                                  />
                                );
                              }}
                            />
                          ))}
                        </Flex>
                      );
                    })}
                  </Box>
                </Flex>
              )}

              {(showMobileSelectedVariableTab || mobileDisplay === 'none') && (
                <Flex
                  pb='md'
                  maxH='660px'
                  overflow='auto'
                  flex={1}
                  ps='md'
                  pt='3xs'
                  direction='column'
                  gap='md'
                  className='hideScrollbarTrack'
                  data-cy='customize-view-selected-variables'
                >
                  <Box>
                    {!showMobileSelectedVariableTab && (
                      <Text size='label100' lineHeight='20px'>
                        {trans('t_selected_variables')}
                      </Text>
                    )}
                    <Text size='light300' mt='xs' color='text.gray.weak'>
                      {trans('t_selected_variables_sort_desc')}
                    </Text>
                  </Box>
                  <VariablesReorderableList
                    reset={reset}
                    setValue={setValue}
                    variables={watchVariables}
                    variableLabels={variableLabels}
                    statusIndicatorValue={statusIndicatorValue}
                  />
                </Flex>
              )}
            </DialogBody>

            <DialogFooter gap='sm-alt' py='sm-alt' px='md' shadow='elevation.200' roundedBottom='2xl'>
              <BaseButton
                w='max-content'
                variant='secondary'
                onClick={handleClose}
                analyticsId={actionsName.customizeViewCancelClicked}
                data-cy='cancel-btn'
              >
                {trans('t_cancel')}
              </BaseButton>
              <BaseButton
                type='submit'
                w='max-content'
                loading={isLoading}
                disabled={isSubmitDisabled}
                analyticsId={actionsName.pondListViewCustomizeViewSaveClicked}
                data-cy='apply-btn'
              >
                {trans('t_apply')}
              </BaseButton>
            </DialogFooter>
          </chakra.form>
        </DialogContent>
      </DialogRoot>
    </>
  );
}

interface VariablesReorderableListProps {
  variables: FormType;
  reset: UseFormReset<FormType>;
  setValue: UseFormSetValue<FormType>;
  statusIndicatorValue: IndicatorValues;
  variableLabels: CustomizeViewModalProps['variableLabels'];
}

function VariablesReorderableList(props: VariablesReorderableListProps) {
  const { variables, setValue, reset, statusIndicatorValue, variableLabels } = props;
  const { trans } = getTrans();

  const items = Object.keys(variables).filter(
    (key) => variables[key as FarmSummaryViewVariable]
  ) as FarmSummaryViewVariable[];

  const sensors = useSensors(useSensor(PointerSensor));

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over) return;

    if (active.id !== over.id) {
      const oldIndex = items.indexOf(active.id as FarmSummaryViewVariable);
      const newIndex = items.indexOf(over.id as FarmSummaryViewVariable);
      const arrayMoveItems = arrayMove(items, oldIndex, newIndex);
      const formValues = arrayMoveItems.reduce((acc, key) => {
        acc[key] = true;
        return acc;
      }, {} as FormType);

      reset(formValues, { keepDirtyValues: true });
    }
  };

  return (
    <DndContext
      sensors={sensors}
      onDragEnd={handleDragEnd}
      collisionDetection={closestCenter}
      modifiers={[restrictToVerticalAxis, restrictToParentElement]}
    >
      <SortableContext items={items} strategy={verticalListSortingStrategy}>
        <Flex direction='column' gap='sm-alt' align='stretch'>
          {items.map((item) => (
            <SortableItem key={item} id={item}>
              <Flex flex={1} align='center' justify='space-between' data-cy={`variable-${item}`}>
                <Text size='label200'>
                  {variableLabels[item]} {statusIndicatorValue === item ? `(${trans('t_indicating_status')})` : ''}
                </Text>
                <MinusFilled
                  cursor='pointer'
                  userSelect='none'
                  onClick={() => setValue(item, false, { shouldDirty: true })}
                  data-cy='remove-variable'
                />
              </Flex>
            </SortableItem>
          ))}
        </Flex>
      </SortableContext>
    </DndContext>
  );
}

function SortableItem({ id, children }: { id: string; children: ReactNode }) {
  const { isDragging, attributes, listeners, setNodeRef, transform, transition } = useSortable({ id });

  return (
    <Flex
      minH='32px'
      px='sm-alt'
      align='center'
      rounded='lg-alt'
      bg='gray.100'
      gap='sm-alt'
      ref={setNodeRef}
      justify='space-between'
      transition={transition}
      transform={CSS.Transform.toString(transform)}
      {...(isDragging && { zIndex: 1, bgColor: 'gray.300' })}
    >
      {children}
      <DragIcon
        me='-xs-alt'
        cursor='grab'
        {...listeners}
        {...attributes}
        aria-hidden='false'
        _focus={{ outline: 'none' }}
      />
    </Flex>
  );
}
