import { Box, Flex } from '@chakra-ui/react';
import { ToastItemParamType, ToastType } from '@components/toast/types';
import { deleteToastAction } from '@redux/app';
import { useAppDispatch } from '@redux/hooks';
import { useCallback, useEffect, useState } from 'react';
import { CloseFilled } from '@icons/close/close-filled';
import { WarningIcon } from '@icons/warning/warning-icon';
import { CheckFilled } from '@icons/check/check-filled';
import { InfoFilledIcon } from '@icons/info/info-filled-icon';

interface ToastProps extends ToastType {
  deleteToast(): void;

  index: number;

  onHoverChange(isHover: boolean): void;
}

function Toast({ title, msg, deleteToast, type, index, onHoverChange }: ToastProps) {
  let color = 'brandBlue.600';
  let icon = <InfoFilledIcon firstSectionColor={color} secondSectionColor='white' boxSize='20px' />;
  switch (type) {
    case 'error':
      color = 'semanticRed.600';
      icon = <CloseFilled bgColor='semanticRed.600' color='white' boxSize='20px' />;
      break;
    case 'success':
      color = 'brandGreen.700';
      icon = <CheckFilled bgColor='brandGreen.700' color='white' boxSize='20px' />;
      break;
    case 'warning':
      color = 'semanticYellow.700';
      icon = <WarningIcon color='semanticYellow.700' boxSize='20px' />;
  }
  return (
    <Box
      data-cy={`toast-${type}-${index}`}
      role='alert'
      aria-live='assertive'
      aria-atomic='true'
      data-autohide='false'
      border='1px solid'
      borderColor={color}
      borderRadius='md'
      bgColor='white'
      mb='xs'
      p='sm'
      boxShadow='elevation.200'
      onMouseLeave={() => onHoverChange(false)}
      onMouseEnter={() => onHoverChange(true)}
    >
      <Flex
        display='flex'
        justifyContent='space-between'
        pb='2xs'
        color={color}
        flex={1}
        borderBottom='1px solid'
        borderColor='gray.200'
      >
        <Flex gap='xs' align='center'>
          {icon}
          <strong>{title}</strong>
        </Flex>

        <CloseFilled
          hasBackground={false}
          color={color}
          cursor='pointer'
          onClick={deleteToast}
          data-dismiss='toast'
          aria-label='Close'
          className='close-toast'
        />
      </Flex>
      <Box py='xs' mt='2xs'>
        {msg}
      </Box>
    </Box>
  );
}

export function ToastItem({ toast, id, index }: ToastItemParamType) {
  const toastTtl = toast?.timeout ?? 3000;
  const dispatch = useAppDispatch();
  const [isHovered, setIsHovered] = useState(false);

  const handleDeleteToast = useCallback(() => {
    dispatch(deleteToastAction(id));
  }, [id, dispatch]);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (isHovered) return clearTimeout(timer);
    timer = setTimeout(handleDeleteToast, toastTtl);
    return () => {
      clearTimeout(timer);
    };
  }, [toastTtl, handleDeleteToast, isHovered]);

  return (
    <Toast
      {...toast}
      index={index}
      deleteToast={handleDeleteToast}
      onHoverChange={(val: boolean) => setIsHovered(val)}
    />
  );
}
