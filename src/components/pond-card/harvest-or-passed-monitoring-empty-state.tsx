import { getTrans } from '@i18n/get-trans';
import { Flex, Icon, Text } from '@chakra-ui/react';
import { PondCardHeader } from '@components/pond-card/pond-card-header';
import { MobileIcon } from '@icons/mobile-icon';
import { EmptyStateProps } from '@components/pond-card/type';
import { CustomTableCell } from '@components/pond-card/custom-table-cell';
import { ArrowBigRight } from '@icons/arrow-big-right/arrow-big-right';
import { BaseButton } from '@components/base/base-button';
import { pondsTableFirstColumnWidth } from '@screens/farm-summary/components/ponds-tab';
import { AddEditPondProjectionModal } from '@screens/pond/components/modals/add-edit-pond-projection-modal';
import { EditFinalHarvestPlanModal } from '@screens/population/components/harvest-modals/edit-final-harvest-plan-modal';
import { actionsName } from '@utils/segment';
import { useAppSelector } from '@redux/hooks';
import { AdminOrSupervisorOrOperationWrapper } from '@components/permission-wrappers/admin-or-supervisor-or-operation-wrapper';
import { VisionOnlyGuard } from '@components/permission-wrappers/vision-only-guard';

interface HarvestedOrPassedMonitoringEmptyStateProps extends EmptyStateProps {
  isLastMonitoringAfterHarvestPlan: boolean;
  numberOfColumns: number;
}

export function HarvestedOrPassedMonitoringEmptyState(props: HarvestedOrPassedMonitoringEmptyStateProps) {
  const {
    farmId,
    farmEid,
    pondEid,
    pondName,
    pondSize,
    isGridView,
    pondId,
    isFlagged,
    populationId,
    isLastMonitoringAfterHarvestPlan,
    cycleDays,
    numberOfColumns,
    isLastPondCard,
    profitProjectionView,
    setExpandedId,
    expandedId
  } = props;
  const { trans } = getTrans();

  const currentFarmPonds = useAppSelector((state) => state.farm.currentFarmPonds);
  const currentPond = currentFarmPonds?.find((pond) => pond._id === pondId);

  if (isGridView) {
    return (
      <Flex bgColor='white' direction='column' pos='relative' gap='md' minH='215px' flex={1}>
        <PondCardHeader
          farmId={farmId}
          farmEid={farmEid}
          pondEid={pondEid}
          pondName={pondName}
          pondId={pondId}
          cycleDays={cycleDays}
          isFlagged={isFlagged}
          populationId={populationId}
          profitProjectionView={profitProjectionView}
        />
        <Flex direction='column' gap='md-alt' align='center' textAlign='center' m='auto'>
          <MobileIcon w='32px' h='47px' />
          <Text size='label300' textAlign='center' maxW='300px'>
            {isLastMonitoringAfterHarvestPlan
              ? trans('t_pond_monitoring_after_harvest')
              : trans('t_setting_pond_bottom_msg')}
          </Text>
          <VisionOnlyGuard>
            <Flex align='center' gap='sm-alt'>
              {isLastMonitoringAfterHarvestPlan && (
                <EditFinalHarvestPlanModal
                  pondId={pondId}
                  pondName={pondName}
                  pondSize={pondSize}
                  population={currentPond?.currentPopulation}
                  isInPondListView={true}
                  isInPondView={false}
                  triggerContainerProps={{ width: 'unset' }}
                >
                  <BaseButton
                    data-cy='update-plan-btn'
                    analyticsId={actionsName.editHarvestPlanClicked}
                    analyticsData={{
                      pondId,
                      pondName,
                      trigger: 'pond-list-update-plan-button-grid',
                      state: 'edit final harvest plan'
                    }}
                  >
                    {trans('t_update_plan')}
                  </BaseButton>
                </EditFinalHarvestPlanModal>
              )}

              <AdminOrSupervisorOrOperationWrapper>
                <AddEditPondProjectionModal
                  pondName={currentPond?.name}
                  population={currentPond?.currentPopulation}
                  isInPondListView={true}
                  isInPondView={false}
                >
                  <BaseButton>{trans('t_manage_projections')}</BaseButton>
                </AddEditPondProjectionModal>
              </AdminOrSupervisorOrOperationWrapper>
            </Flex>
          </VisionOnlyGuard>
        </Flex>
      </Flex>
    );
  }

  const colSpanOfMessage = isLastMonitoringAfterHarvestPlan ? 5 : 3;
  const isMoreThanSevenColumns = numberOfColumns > colSpanOfMessage + 1;
  const contentColSpan = numberOfColumns > colSpanOfMessage ? colSpanOfMessage : numberOfColumns - 1;
  const remainingColSpan = numberOfColumns - contentColSpan;
  const borderBottom = isLastPondCard ? 'none' : '0.5px solid';
  const isExpanded = !!populationId && expandedId === populationId;
  return (
    <>
      <CustomTableCell tdProps={{ pos: 'sticky', zIndex: 2, left: 0 }}>
        <PondCardHeader
          farmId={farmId}
          farmEid={farmEid}
          pondEid={pondEid}
          pondName={pondName}
          pondId={pondId}
          cycleDays={cycleDays}
          isFlagged={isFlagged}
          populationId={populationId}
          borderBottomStartRadius={isLastPondCard ? '2xl' : 'none'}
          borderBottom={borderBottom}
          borderRight='0.5px solid'
          borderColor='border.gray'
          pos='relative'
          py='sm'
          px='sm-alt'
          h='44px'
          textStyle='label.200'
          isListView={!isGridView}
          profitProjectionView={profitProjectionView}
          setExpandedId={setExpandedId}
          expandedId={expandedId}
        />
      </CustomTableCell>
      <CustomTableCell
        tdProps={{ pos: 'sticky', zIndex: 1, left: `${pondsTableFirstColumnWidth}px`, colSpan: contentColSpan }}
      >
        <Flex
          bgColor={isExpanded ? 'bg.gray.strong' : 'white'}
          p='sm-alt'
          h='44px'
          pos='relative'
          gap='sm-alt'
          align='center'
          borderBottom={borderBottom}
          borderColor='border.gray'
        >
          <Icon as={MobileIcon} boxSize='28px' />

          <Text
            color={isGridView ? 'text.gray' : 'text.gray.weak'}
            textStyle={isGridView ? 'label.200' : 'paragraph.200.light'}
          >
            {isLastMonitoringAfterHarvestPlan
              ? trans('t_pond_monitoring_after_harvest')
              : trans('t_setting_pond_bottom_msg')}
          </Text>
          <VisionOnlyGuard>
            <Flex align='center' gap='lg'>
              {isLastMonitoringAfterHarvestPlan && (
                <EditFinalHarvestPlanModal
                  pondId={pondId}
                  pondName={pondName}
                  pondSize={pondSize}
                  population={currentPond?.currentPopulation}
                  isInPondListView={true}
                  isInPondView={false}
                  triggerContainerProps={{ width: 'unset' }}
                >
                  <BaseButton
                    size='sm'
                    variant='link'
                    p={0}
                    data-cy='update-plan-btn'
                    analyticsId={actionsName.editHarvestPlanClicked}
                    analyticsData={{
                      pondId,
                      pondName,
                      trigger: 'pond-list-update-plan-button-list',
                      state: 'edit final harvest plan'
                    }}
                  >
                    {trans('t_update_plan')} <ArrowBigRight w='20px' h='20px' color='button.link' />
                  </BaseButton>
                </EditFinalHarvestPlanModal>
              )}
              <AdminOrSupervisorOrOperationWrapper>
                <AddEditPondProjectionModal
                  pondName={currentPond?.name}
                  population={currentPond?.currentPopulation}
                  isInPondListView={true}
                  isInPondView={false}
                >
                  <BaseButton size='sm' p={0} variant='link'>
                    {trans('t_manage_projections')} <ArrowBigRight w='20px' h='20px' color='button.link' />
                  </BaseButton>
                </AddEditPondProjectionModal>
              </AdminOrSupervisorOrOperationWrapper>
            </Flex>
          </VisionOnlyGuard>
        </Flex>
      </CustomTableCell>
      {isMoreThanSevenColumns && (
        <CustomTableCell tdProps={{ colSpan: remainingColSpan }}>
          <Flex
            bgColor={isExpanded ? 'bg.gray.strong' : 'white'}
            p='sm-alt'
            h='44px'
            pos='relative'
            gap='md'
            align='center'
            borderBottom={borderBottom}
            borderColor='border.gray'
          />
        </CustomTableCell>
      )}
    </>
  );
}
