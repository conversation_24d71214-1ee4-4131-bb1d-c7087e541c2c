import { getTrans } from '@i18n/get-trans';
import { Box, Flex, Text } from '@chakra-ui/react';
import { PondCardHeader } from '@components/pond-card/pond-card-header';
import { InsightIcon } from '@icons/insight/insight-icon';
import { HideOnMobileView } from '@components/mobile-view/hide-on-mobile-view';
import { AddPondStockingModal } from '@screens/pond/components/modals/add-edit-pond-stocking/add-pond-stocking-modal';
import { BaseButton } from '@components/base/base-button';
import { actionsName } from '@utils/segment';
import { PondCardProps } from '@components/pond-card/pond-card';
import { EmptyStateProps } from '@components/pond-card/type';
import { CustomTableCell } from '@components/pond-card/custom-table-cell';
import { ArrowBigRight } from '@icons/arrow-big-right/arrow-big-right';
import { pondsTableFirstColumnWidth } from '@screens/farm-summary/components/ponds-tab';
import { VisionOnlyAccess } from '@components/permission-wrappers/vision-only-access';
import { VisionOnlyGuard } from '@components/permission-wrappers/vision-only-guard';
import { VisionOnlyAddEditPondStockingModal } from '@screens/pond/components/modals/add-edit-pond-stocking/vision-only-add-edit-pond-stocking-modal';

interface NewPondEmptyStateProps extends EmptyStateProps, Pick<PondCardProps, 'isHarvested' | 'firstMonitoring'> {
  isGridView: boolean;
  numberOfColumns: number;
}

export function NewPondEmptyState(props: NewPondEmptyStateProps) {
  const {
    farmId,
    farmEid,
    pondEid,
    pondName,
    pondId,
    pondSize,
    isFlagged,
    populationId,
    isHarvested,
    firstMonitoring,
    isGridView,
    cycleDays,
    numberOfColumns,
    isLastPondCard,
    profitProjectionView,
    setExpandedId,
    expandedId
  } = props;
  const { trans } = getTrans();

  if (isGridView) {
    return (
      <Flex bgColor='white' direction='column' pos='relative' gap='md' minH='215px' flex={1}>
        <PondCardHeader
          farmId={farmId}
          farmEid={farmEid}
          pondEid={pondEid}
          pondName={pondName}
          pondId={pondId}
          isFlagged={isFlagged}
          cycleDays={cycleDays}
          populationId={populationId}
          pos='relative'
          textStyle='label.200'
          profitProjectionView={profitProjectionView}
        />
        <Flex
          direction='column'
          gap='md'
          align='center'
          textAlign='center'
          data-cy='new-pond-empty-state'
          flexWrap='wrap'
          m='auto'
        >
          <InsightIcon boxSize='55px' />

          <Text size='label200' textAlign='center' maxW='300px'>
            {trans('t_stock_your_pond_to_start_getting')}
          </Text>
          <HideOnMobileView>
            <Box alignSelf='center'>
              <VisionOnlyAccess>
                <VisionOnlyAddEditPondStockingModal
                  pondId={pondId}
                  pondName={pondName}
                  pondSize={pondSize}
                  isHarvested={isHarvested}
                  firstMonitoring={firstMonitoring}
                  isInPondListView
                >
                  <BaseButton
                    size='md'
                    data-cy='stock-pond-btn'
                    analyticsId={actionsName.pondListViewStockPondClicked}
                    analyticsData={{ isVisionOnly: true }}
                  >
                    {trans('t_stock_pond')}
                  </BaseButton>
                </VisionOnlyAddEditPondStockingModal>
              </VisionOnlyAccess>
              <VisionOnlyGuard>
                <AddPondStockingModal
                  pondId={pondId}
                  pondName={pondName}
                  pondSize={pondSize}
                  isHarvested={isHarvested}
                  firstMonitoring={firstMonitoring}
                  isInPondListView
                >
                  <BaseButton size='md' data-cy='stock-pond-btn' analyticsId={actionsName.pondListViewStockPondClicked}>
                    {trans('t_stock_pond')}
                  </BaseButton>
                </AddPondStockingModal>
              </VisionOnlyGuard>
            </Box>
          </HideOnMobileView>
        </Flex>
      </Flex>
    );
  }
  const contentColSpan = numberOfColumns > 3 ? 3 : numberOfColumns - 1;
  const isMoreThanFourColumns = numberOfColumns > 4;
  const remainingColSpan = numberOfColumns - contentColSpan;
  const borderBottom = isLastPondCard ? 'none' : '0.5px solid';

  return (
    <>
      <CustomTableCell tdProps={{ pos: 'sticky', zIndex: 2, left: 0 }}>
        <PondCardHeader
          farmId={farmId}
          farmEid={farmEid}
          pondEid={pondEid}
          pondName={pondName}
          pondId={pondId}
          isFlagged={isFlagged}
          cycleDays={cycleDays}
          populationId={populationId}
          borderBottomStartRadius={isLastPondCard ? '2xl' : 'none'}
          borderBottom={borderBottom}
          borderRight='0.5px solid'
          borderColor='border.gray'
          pos='relative'
          py='sm'
          px='sm-alt'
          h='44px'
          textStyle='label.200'
          profitProjectionView={profitProjectionView}
          isListView={!isGridView}
          setExpandedId={setExpandedId}
          expandedId={expandedId}
        />
      </CustomTableCell>
      <CustomTableCell
        tdProps={{
          pos: { base: 'static', lg: 'sticky' },
          zIndex: 1,
          left: `${pondsTableFirstColumnWidth}px`,
          colSpan: contentColSpan
        }}
      >
        <Flex
          bgColor='white'
          p='sm-alt'
          pos='relative'
          gap='sm-alt'
          align='center'
          data-cy='new-pond-empty-state'
          h='44px'
          borderBottom={borderBottom}
          borderColor='border.gray'
        >
          <InsightIcon boxSize='36px' />

          <Text size='light200' color='gray.700'>
            {trans('t_stock_your_pond_to_start_getting')}
          </Text>

          <HideOnMobileView>
            <VisionOnlyAccess>
              <VisionOnlyAddEditPondStockingModal
                pondId={pondId}
                pondName={pondName}
                pondSize={pondSize}
                isHarvested={isHarvested}
                firstMonitoring={firstMonitoring}
                isInPondListView
              >
                <BaseButton
                  variant='link'
                  size='sm'
                  p={0}
                  data-cy='stock-pond-btn'
                  analyticsId={actionsName.pondListViewStockPondClicked}
                  analyticsData={{ isVisionOnly: true }}
                >
                  {trans('t_stock_pond')} <ArrowBigRight w='20px' h='20px' color='button.link' />
                </BaseButton>
              </VisionOnlyAddEditPondStockingModal>
            </VisionOnlyAccess>
            <VisionOnlyGuard>
              <AddPondStockingModal
                pondId={pondId}
                pondName={pondName}
                pondSize={pondSize}
                isHarvested={isHarvested}
                firstMonitoring={firstMonitoring}
                isInPondListView
              >
                <BaseButton
                  variant='link'
                  size='sm'
                  p={0}
                  data-cy='stock-pond-btn'
                  analyticsId={actionsName.pondListViewStockPondClicked}
                >
                  {trans('t_stock_pond')} <ArrowBigRight w='20px' h='20px' color='button.link' />
                </BaseButton>
              </AddPondStockingModal>
            </VisionOnlyGuard>
          </HideOnMobileView>
        </Flex>
      </CustomTableCell>
      {isMoreThanFourColumns && (
        <CustomTableCell tdProps={{ colSpan: remainingColSpan }}>
          <Flex
            bgColor='white'
            p='sm-alt'
            h='44px'
            pos='relative'
            gap='md'
            align='center'
            borderBottom={borderBottom}
            borderColor='border.gray'
          />
        </CustomTableCell>
      )}
    </>
  );
}
