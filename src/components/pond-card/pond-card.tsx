import { Box, Flex, FlexProps, Heading, SimpleGrid, Skeleton, Text } from '@chakra-ui/react';
import { IndicatorOption, PondCardViewType } from '@components/pond-card/helper';
import { getTrans } from '@i18n/get-trans';
import {
  PopulationCycleInformation,
  PopulationHarvestPlanIndicators,
  PopulationHarvestPlanProjectionProfitProjectionDataHeadon,
  PopulationHistory,
  PopulationManualAverageWeights,
  PopulationPartialHarvest,
  PopulationProductionPrediction,
  PopulationSymmetry
} from '@xpertsea/module-farm-sdk';
import { RefObject, useEffect, useState } from 'react';
import { FarmSummaryViewVariable } from '@screens/farm-summary/helpers/pond-list';
import { DateTime } from 'luxon';
import { AverageWeightChangePopover } from '@screens/data-updater/components/average-weight-change-popover';
import { GroupViewVariable } from '@screens/group-summary/hooks/use-get-group-view-variables';
import { PondCardWrapper } from '@components/pond-card/pond-card-wrapper';
import { PondCardHeader } from '@components/pond-card/pond-card-header';
import { PondCardCycleInfoValues, PondCardViewMapper } from '@components/pond-card/pond-card-view-mapper';
import { HarvestedOrPassedMonitoringEmptyState } from '@components/pond-card/harvest-or-passed-monitoring-empty-state';
import { CustomTableCell } from '@components/pond-card/custom-table-cell';
import { NewPondEmptyState } from '@components/pond-card/new-pond-empty-state';
import { useGetPondCardData } from '@components/pond-card/hooks/use-get-pond-card-data';
import { useListPopulationHistoryApi } from '@components/pond-card/hooks/use-list-population-history';
import { convertUnitByDivision, formatNumber } from '@utils/number';
import { useAppSelector } from '@redux/hooks';
import isUndefined from 'lodash/isUndefined';
import { isNumber } from 'lodash';

export interface PondCardProps {
  selectedIndicatingStatus: IndicatorOption;
  indicators: PopulationHarvestPlanIndicators;
  pondId: string;
  pondName: string;
  profitProjectionView: PondCardViewType;
  pondEid: number;
  farmEid: string;
  farmId: string;
  farmTimezone: string;
  isFlagged: boolean;
  populationId: string;
  lastHistory: PopulationHistory & { cycleDays?: number };
  plannedProfitProjection: PopulationHarvestPlanProjectionProfitProjectionDataHeadon;
  productionPredictionHarvestDate: PopulationProductionPrediction;
  optimalHarvestDate: string;
  viewVariables: GroupViewVariable[] | FarmSummaryViewVariable[];
  manuallyMonitored: boolean;
  symmetry: PopulationSymmetry;
  lastMonitoringTotalAnimals: number;
  seedingAverageWeight: number;
  cycleInformation: PopulationCycleInformation;
  isNewPond?: boolean;
  isHarvested?: boolean;
  hasMonitoring?: boolean;
  pondSize?: number;
  stockedAtDate?: string;
  seedingQuantity?: number;
  partialHarvest?: PopulationPartialHarvest[];
  firstMonitoring?: PopulationHistory;
  hasSurvivalEstimatedValues: boolean;
  hasFeedEstimatedValues: boolean;
  harvestPlanDate: string;
  isGridView?: boolean;
  stockingCostsMillar?: number;
  lastMonitoringDistance?: number;
  isLastPondCard?: boolean;
  manualAverageWeights?: PopulationManualAverageWeights[];
  tableContainerRef?: RefObject<HTMLDivElement>;
}

export function PondCard(props: PondCardProps) {
  const {
    selectedIndicatingStatus,
    indicators,
    pondId,
    pondName,
    profitProjectionView = 'current',
    pondEid,
    farmEid,
    farmTimezone,
    isFlagged,
    populationId,
    lastHistory,
    plannedProfitProjection,
    productionPredictionHarvestDate,
    optimalHarvestDate,
    viewVariables,
    manuallyMonitored,
    symmetry,
    lastMonitoringTotalAnimals,
    seedingAverageWeight,
    cycleInformation,
    isNewPond,
    isHarvested,
    hasMonitoring,
    firstMonitoring,
    hasSurvivalEstimatedValues,
    hasFeedEstimatedValues,
    isGridView = true,
    harvestPlanDate,
    farmId,
    stockingCostsMillar,
    manualAverageWeights,
    isLastPondCard = false,
    tableContainerRef,
    pondSize,
    seedingQuantity,
    partialHarvest,
    stockedAtDate,
    lastMonitoringDistance
  } = props;

  const { trans } = getTrans();
  const lang = useAppSelector((state) => state.app.lang);
  const user = useAppSelector((state) => state.auth.user);
  const productOffering = useAppSelector((state) => state.farm?.currentFarm?.metadata?.productOffering);

  const isVisionOnly = productOffering === 'visionOnly';
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};
  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);

  const cycleDays = lastHistory?.cycleDays;

  //Cycle Information
  const pondSizeFormatted = formatNumber(pondSize, { lang, fractionDigits: 0 });
  const pondSizeValue = pondSize ? `${pondSizeFormatted} ${trans('t_ha')}` : null;
  const seedingQuantityHa = seedingQuantity && pondSize ? seedingQuantity / pondSize : null;
  const seedingQuantityHaFormatted = seedingQuantityHa
    ? formatNumber(seedingQuantityHa, { lang, fractionDigits: 0 })
    : null;

  const sumOfLbsHarvested = partialHarvest?.reduce((acc, curr) => acc + (curr?.lbsHarvested ?? 0), 0);
  const partialHarvestLbsHa = sumOfLbsHarvested && pondSize ? sumOfLbsHarvested / pondSize : null;
  const unitTitle = isBiomassUnitLbs ? trans('t_lb_over_ha') : trans('t_kg_over_ha');
  const partialHarvestHaValue = partialHarvestLbsHa
    ? `${formatNumber(convertUnitByDivision(partialHarvestLbsHa, unitsConfig?.biomass), { lang, fractionDigits: 2 })} ${unitTitle}`
    : null;
  const numberOfPartialHarvests = partialHarvest?.length;
  const lastPartialHarvestDate = partialHarvest?.[numberOfPartialHarvests - 1]?.date;
  const lastMonitoringDistanceFormatted =
    isNumber(lastMonitoringDistance) && lastMonitoringDistance > 0
      ? (formatNumber(lastMonitoringDistance, { lang, fractionDigits: 2 }) as number)
      : null;

  const data = useGetPondCardData({
    indicators,
    selectedIndicatingStatus,
    profitProjectionView,
    optimalHarvestDate,
    lastHistory,
    plannedProfitProjection,
    productionPredictionHarvestDate,
    farmTimezone,
    cycleInformation,
    stockingCostsMillar,
    farmId,
    manualAverageWeights
  });
  const [expandedId, setExpandedId] = useState<string>();

  const isExpanded = !!populationId && expandedId === populationId;
  const shouldHideBottomBorder = isLastPondCard && !isExpanded;

  const isAdjusted = cycleInformation?.projection?.projectedGrowth?.type === 'custom';

  const harvestPlanDateTime = DateTime.fromISO(harvestPlanDate).startOf('day');
  const lastMonitoringDateTime = DateTime.fromISO(lastHistory?.date).startOf('day');
  const isLastMonitoringAfterHarvestPlan =
    profitProjectionView === 'harvest' && lastMonitoringDateTime > harvestPlanDateTime;
  const hasEstimatedValues = hasSurvivalEstimatedValues || hasFeedEstimatedValues;

  const {
    currentIndicatingStatusValueFormatted,
    currentIndicatingStatusColor,
    currentIndicatingStatusIcon: statusIcon,
    ...rest
  } = data;
  const indicatorIconValue = !isGridView ? <Box boxSize='20px' /> : undefined;
  const currentIndicatingStatusIcon = currentIndicatingStatusValueFormatted ? statusIcon : indicatorIconValue;
  const commonTdChildrenProps: FlexProps = {
    bgColor: isExpanded ? 'bg.gray.strong' : 'white',
    py: 'sm',
    px: 'sm-alt',
    h: isGridView ? '60px' : '44px',
    textStyle: 'label.200',
    borderRight: '0.5px solid',
    borderBottom: shouldHideBottomBorder ? 'none' : '0.5px solid',
    borderColor: 'border.gray'
  };

  if (isNewPond) {
    return (
      <PondCardWrapper isGridView={isGridView} tableContainerRef={tableContainerRef} dataCy={`pond-${pondId}`}>
        <NewPondEmptyState
          farmEid={farmEid}
          farmId={farmId}
          pondEid={pondEid}
          pondName={pondName}
          pondSize={pondSize}
          pondId={pondId}
          isFlagged={isFlagged}
          populationId={populationId}
          isHarvested={isHarvested}
          firstMonitoring={firstMonitoring}
          isGridView={isGridView}
          cycleDays={cycleDays}
          numberOfColumns={viewVariables?.length ? viewVariables.length + 1 : 1}
          isLastPondCard={shouldHideBottomBorder}
          profitProjectionView={profitProjectionView}
          setExpandedId={setExpandedId}
          expandedId={expandedId}
        />
      </PondCardWrapper>
    );
  }

  if (!hasMonitoring || isLastMonitoringAfterHarvestPlan) {
    return (
      <>
        <PondCardWrapper isGridView={isGridView} tableContainerRef={tableContainerRef} dataCy={`pond-${pondId}`}>
          <HarvestedOrPassedMonitoringEmptyState
            farmEid={farmEid}
            farmId={farmId}
            pondEid={pondEid}
            pondName={pondName}
            pondId={pondId}
            isFlagged={isFlagged}
            populationId={populationId}
            isGridView={isGridView}
            cycleDays={cycleDays}
            isLastMonitoringAfterHarvestPlan={isLastMonitoringAfterHarvestPlan}
            numberOfColumns={viewVariables?.length ? viewVariables.length + 1 : 1}
            isLastPondCard={shouldHideBottomBorder}
            profitProjectionView={profitProjectionView}
            setExpandedId={setExpandedId}
            expandedId={expandedId}
          />
        </PondCardWrapper>
        {isExpanded && (
          <PastHistoryRows
            selectedIndicatingStatus={selectedIndicatingStatus}
            viewVariables={viewVariables}
            symmetry={symmetry}
            manuallyMonitored={manuallyMonitored}
            indicators={indicators}
            optimalHarvestDate={optimalHarvestDate}
            farmTimezone={farmTimezone}
            cycleInformation={cycleInformation}
            stockingCostsMillar={stockingCostsMillar}
            farmId={farmId}
            manualAverageWeights={manualAverageWeights}
            lastMonitoringTotalAnimals={lastMonitoringTotalAnimals}
            seedingAverageWeight={seedingAverageWeight}
            hasSurvivalEstimatedValues={hasSurvivalEstimatedValues}
            hasFeedEstimatedValues={hasFeedEstimatedValues}
            isGridView={isGridView}
            tableContainerRef={tableContainerRef}
            populationId={populationId}
            productionPredictionHarvestDate={productionPredictionHarvestDate}
            plannedProfitProjection={plannedProfitProjection}
            pondSizeValue={pondSizeValue}
            stockedAtDate={stockedAtDate}
            animalsStockedHaValue={seedingQuantityHaFormatted}
            partialHarvestHaValue={partialHarvestHaValue}
            lastPartialHarvestDate={lastPartialHarvestDate}
            lastMonitoringDistance={lastMonitoringDistanceFormatted}
            harvestPlanDate={harvestPlanDate}
            numberOfPartialHarvests={numberOfPartialHarvests}
            isVisionOnly={isVisionOnly}
            commonTdChildrenProps={{ ...commonTdChildrenProps, bgColor: 'transparent', h: '34px' }}
          />
        )}
      </>
    );
  }

  if (!isGridView) {
    return (
      <>
        <PondCardWrapper isGridView={isGridView} tableContainerRef={tableContainerRef} dataCy={`pond-${pondId}`}>
          <CustomTableCell tdProps={{ pos: 'sticky', left: 0, zIndex: 2, borderBottom: 'none' }}>
            <PondCardHeader
              farmId={farmId}
              farmEid={farmEid}
              pondEid={pondEid}
              pondName={pondName}
              pondId={pondId}
              cycleDays={cycleDays}
              isFlagged={isFlagged}
              populationId={populationId}
              borderBottomStartRadius={shouldHideBottomBorder ? '2xl' : 'none'}
              borderBottom={shouldHideBottomBorder ? 'none' : '0.5px solid'}
              hasEstimatedValues={hasEstimatedValues}
              pos='relative'
              isAdjusted={isAdjusted}
              isListView
              profitProjectionView={profitProjectionView}
              setExpandedId={setExpandedId}
              expandedId={expandedId}
              lastMonitoringDistance={lastMonitoringDistance}
              {...commonTdChildrenProps}
            />
          </CustomTableCell>

          {viewVariables.map((variable, index) => {
            const isLastItem = index === viewVariables.length - 1;
            const borderProps = {
              borderRight: isLastItem ? 'none' : '0.5px solid'
            };

            if (selectedIndicatingStatus?.value === variable) {
              return (
                <CustomTableCell key={variable} tdProps={{ borderBottom: 'none' }}>
                  <Flex {...commonTdChildrenProps} {...borderProps} gap='xs-alt' align='center'>
                    {!isVisionOnly && currentIndicatingStatusIcon}

                    {!!rest?.isManualAverageWeight && variable === 'averageWeight' && (
                      <AverageWeightChangePopover
                        modelAverageWeight={rest?.modelAverageWeight}
                        updatedBy={rest?.manualAverageWeightUpdatedBy}
                      />
                    )}

                    <Text
                      color={isGridView ? 'text.gray' : 'text.gray.weak'}
                      size={isGridView ? 'label200' : 'light200'}
                    >
                      {currentIndicatingStatusValueFormatted ?? '-'}
                    </Text>
                  </Flex>
                </CustomTableCell>
              );
            }

            return (
              <CustomTableCell key={variable} tdProps={{ borderBottom: 'none' }}>
                <Flex {...commonTdChildrenProps} {...borderProps} align='center'>
                  <PondCardViewMapper
                    viewVariable={variable}
                    manuallyMonitored={manuallyMonitored}
                    symmetry={symmetry}
                    lastMonitoringTotalAnimals={lastMonitoringTotalAnimals}
                    seedingAverageWeight={seedingAverageWeight}
                    profitProjectionView={profitProjectionView}
                    hasSurvivalEstimatedValues={hasSurvivalEstimatedValues}
                    hasFeedEstimatedValues={hasFeedEstimatedValues}
                    isGridView={isGridView}
                    pondSizeValue={pondSizeValue}
                    stockedAtDate={stockedAtDate}
                    animalsStockedHaValue={seedingQuantityHaFormatted}
                    partialHarvestHaValue={partialHarvestHaValue}
                    lastPartialHarvestDate={lastPartialHarvestDate}
                    harvestPlanDate={harvestPlanDate}
                    numberOfPartialHarvests={numberOfPartialHarvests}
                    lastMonitoringDistance={lastMonitoringDistanceFormatted}
                    {...rest}
                  />
                </Flex>
              </CustomTableCell>
            );
          })}
        </PondCardWrapper>
        {isExpanded && (
          <PastHistoryRows
            selectedIndicatingStatus={selectedIndicatingStatus}
            viewVariables={viewVariables}
            symmetry={symmetry}
            manuallyMonitored={manuallyMonitored}
            indicators={indicators}
            optimalHarvestDate={optimalHarvestDate}
            farmTimezone={farmTimezone}
            cycleInformation={cycleInformation}
            stockingCostsMillar={stockingCostsMillar}
            farmId={farmId}
            manualAverageWeights={manualAverageWeights}
            lastMonitoringTotalAnimals={lastMonitoringTotalAnimals}
            seedingAverageWeight={seedingAverageWeight}
            hasSurvivalEstimatedValues={hasSurvivalEstimatedValues}
            hasFeedEstimatedValues={hasFeedEstimatedValues}
            isGridView={isGridView}
            tableContainerRef={tableContainerRef}
            populationId={populationId}
            productionPredictionHarvestDate={productionPredictionHarvestDate}
            plannedProfitProjection={plannedProfitProjection}
            pondSizeValue={pondSizeValue}
            stockedAtDate={stockedAtDate}
            animalsStockedHaValue={seedingQuantityHaFormatted}
            partialHarvestHaValue={partialHarvestHaValue}
            lastPartialHarvestDate={lastPartialHarvestDate}
            harvestPlanDate={harvestPlanDate}
            numberOfPartialHarvests={numberOfPartialHarvests}
            isVisionOnly={isVisionOnly}
            lastMonitoringDistance={lastMonitoringDistanceFormatted}
            commonTdChildrenProps={{ ...commonTdChildrenProps, bgColor: 'transparent', h: '34px' }}
          />
        )}
      </>
    );
  }

  return (
    <PondCardWrapper
      isGridView={isGridView}
      tableContainerRef={tableContainerRef}
      currentIndicatingStatusColor={!isVisionOnly && currentIndicatingStatusColor}
      dataCy={`pond-${pondId}`}
    >
      <PondCardHeader
        farmId={farmId}
        farmEid={farmEid}
        pondEid={pondEid}
        pondName={pondName}
        pondId={pondId}
        cycleDays={cycleDays}
        isFlagged={isFlagged}
        populationId={populationId}
        isAdjusted={isAdjusted}
        profitProjectionView={profitProjectionView}
        lastMonitoringDistance={lastMonitoringDistance}
        data-cy='header'
      />

      <Text size='label300' color='text.gray.disabled' my='sm-alt'>
        {hasEstimatedValues ? trans('t_using_imputed_values') : trans('t_up_to_date')}
      </Text>

      {selectedIndicatingStatus && (
        <Flex align='center' gap='2sm' mb='md' mt='xs'>
          {!isVisionOnly && currentIndicatingStatusIcon}

          <Text size='label200'>{selectedIndicatingStatus.label}</Text>
          <Flex align='center' gap='xs-alt'>
            {selectedIndicatingStatus?.value === 'averageWeight' && rest?.isManualAverageWeight && (
              <AverageWeightChangePopover
                modelAverageWeight={rest?.modelAverageWeight}
                updatedBy={rest?.manualAverageWeightUpdatedBy}
              />
            )}
            <Heading size='heavy300' ms='3xs' letterSpacing='none'>
              {currentIndicatingStatusValueFormatted ?? '-'}
            </Heading>
          </Flex>
        </Flex>
      )}

      <SimpleGrid columns={2} gap='sm-alt'>
        {viewVariables.map((variable) => {
          if (selectedIndicatingStatus?.value === variable) return null;
          return (
            <PondCardViewMapper
              key={variable}
              viewVariable={variable}
              manuallyMonitored={manuallyMonitored}
              symmetry={symmetry}
              lastMonitoringTotalAnimals={lastMonitoringTotalAnimals}
              seedingAverageWeight={seedingAverageWeight}
              profitProjectionView={profitProjectionView}
              hasSurvivalEstimatedValues={hasSurvivalEstimatedValues}
              hasFeedEstimatedValues={hasFeedEstimatedValues}
              isGridView={isGridView}
              pondSizeValue={pondSizeValue}
              stockedAtDate={stockedAtDate}
              animalsStockedHaValue={seedingQuantityHaFormatted}
              partialHarvestHaValue={partialHarvestHaValue}
              lastPartialHarvestDate={lastPartialHarvestDate}
              harvestPlanDate={harvestPlanDate}
              numberOfPartialHarvests={numberOfPartialHarvests}
              lastMonitoringDistance={lastMonitoringDistanceFormatted}
              {...rest}
            />
          );
        })}
      </SimpleGrid>
    </PondCardWrapper>
  );
}

type PastHistoryPropsFromPondCard = Pick<
  PondCardProps,
  | 'selectedIndicatingStatus'
  | 'viewVariables'
  | 'symmetry'
  | 'manuallyMonitored'
  | 'indicators'
  | 'optimalHarvestDate'
  | 'farmTimezone'
  | 'cycleInformation'
  | 'stockingCostsMillar'
  | 'farmId'
  | 'manualAverageWeights'
  | 'lastMonitoringTotalAnimals'
  | 'seedingAverageWeight'
  | 'hasSurvivalEstimatedValues'
  | 'hasFeedEstimatedValues'
  | 'isGridView'
  | 'tableContainerRef'
  | 'populationId'
  | 'productionPredictionHarvestDate'
  | 'plannedProfitProjection'
  | 'stockedAtDate'
  | 'harvestPlanDate'
  | 'lastMonitoringDistance'
>;

interface PastHistoryRowsProps extends PastHistoryPropsFromPondCard, PondCardCycleInfoValues {
  commonTdChildrenProps: FlexProps;
  isVisionOnly: boolean;
}

function PastHistoryRows(props: PastHistoryRowsProps) {
  const { isGridView, tableContainerRef, populationId, commonTdChildrenProps, ...rest } = props;
  const { trans } = getTrans();
  const [{ data, error, isLoading }, listHistory] = useListPopulationHistoryApi({});
  const { history } = data ?? {};

  useEffect(() => {
    if (!populationId) return;
    listHistory({
      params: {
        filter: { id: [populationId] },
        page: { size: 100, current: 1 }
      }
    });
  }, [populationId]);

  if (isLoading) {
    return (
      <>
        <PondCardWrapper tableRowProps={{ h: '34px' }} isGridView={isGridView} tableContainerRef={tableContainerRef}>
          <Skeleton as='td' h='34px' pos='absolute' top={0} left={0} right={0} />
        </PondCardWrapper>
        <PondCardWrapper tableRowProps={{ h: '34px' }} isGridView={isGridView} tableContainerRef={tableContainerRef}>
          <Skeleton as='td' h='34px' pos='absolute' top={0} left={0} right={0} />
        </PondCardWrapper>
      </>
    );
  }

  if (error) {
    return (
      <PondCardWrapper tableRowProps={{ h: '34px' }} isGridView={isGridView} tableContainerRef={tableContainerRef}>
        <CustomTableCell
          tdProps={{ pos: 'sticky', left: 0, zIndex: 2, bgColor: 'bg.gray.medium', borderBottom: 'none' }}
        >
          <Flex px='lg' align='center' justify='flex-end'>
            <Text size='light200'> {trans('t_something_went_wrong')}!</Text>
            <Box boxSize='20px' />
          </Flex>
        </CustomTableCell>
      </PondCardWrapper>
    );
  }

  if (!history?.length) {
    return (
      <PondCardWrapper tableRowProps={{ h: '34px' }} isGridView={isGridView} tableContainerRef={tableContainerRef}>
        <CustomTableCell
          tdProps={{ pos: 'sticky', left: 0, zIndex: 2, bgColor: 'bg.gray.medium', borderBottom: 'none' }}
        >
          <Flex px='lg' align='center' justify='flex-end'>
            <Text size='light200'> {trans('t_no_data_to_display')}!</Text>
            <Box boxSize='20px' />
          </Flex>
        </CustomTableCell>
      </PondCardWrapper>
    );
  }

  return (
    <>
      {history?.map((historyItem, index) => {
        const isFirstItem = index === 0;
        const cellTitle = isFirstItem ? trans('t_last_week') : trans('t_x_weeks_ago', { count: index + 1 });
        return (
          <PondCardWrapper
            key={historyItem.date}
            tableRowProps={{ h: '34px' }}
            isGridView={isGridView}
            tableContainerRef={tableContainerRef}
          >
            <CustomTableCell
              tdProps={{ pos: 'sticky', left: 0, zIndex: 2, bgColor: 'bg.gray.medium', borderBottom: 'none' }}
            >
              <Flex px='lg' align='center' justify='flex-end' {...commonTdChildrenProps}>
                <Text
                  minW='84px'
                  {...(!isGridView && { fontWeight: 400 })}
                  color={isGridView ? 'text.gray' : 'text.gray.weak'}
                  textStyle={isGridView ? 'light.200' : 'paragraph.200.light'}
                >
                  {cellTitle}
                </Text>
                <Box boxSize='20px' />
              </Flex>
            </CustomTableCell>

            <PastHistoryCellMapper
              isGridView={isGridView}
              history={historyItem}
              commonTdChildrenProps={commonTdChildrenProps}
              {...rest}
            />
          </PondCardWrapper>
        );
      })}
    </>
  );
}

interface PastHistoryCellMapperProps extends Omit<PastHistoryRowsProps, 'tableContainerRef' | 'populationId'> {
  history: PopulationHistory;
}

function PastHistoryCellMapper(props: PastHistoryCellMapperProps) {
  const {
    isGridView,
    viewVariables,
    selectedIndicatingStatus,
    commonTdChildrenProps,
    symmetry,
    manuallyMonitored,
    indicators,
    optimalHarvestDate,
    history,
    farmTimezone,
    cycleInformation,
    stockingCostsMillar,
    farmId,
    manualAverageWeights,
    lastMonitoringTotalAnimals,
    seedingAverageWeight,
    hasSurvivalEstimatedValues,
    hasFeedEstimatedValues,
    productionPredictionHarvestDate,
    plannedProfitProjection,
    pondSizeValue,
    stockedAtDate,
    animalsStockedHaValue,
    partialHarvestHaValue,
    lastPartialHarvestDate,
    lastMonitoringDistance,
    harvestPlanDate,
    numberOfPartialHarvests,
    isVisionOnly
  } = props;

  const data = useGetPondCardData({
    indicators,
    selectedIndicatingStatus,
    profitProjectionView: 'current',
    optimalHarvestDate,
    lastHistory: history,
    plannedProfitProjection,
    productionPredictionHarvestDate,
    farmTimezone,
    cycleInformation,
    stockingCostsMillar,
    farmId,
    manualAverageWeights
  });

  const {
    currentIndicatingStatusValueFormatted,
    currentIndicatingStatusColor,
    currentIndicatingStatusIcon: statusIcon,
    ...rest
  } = data;

  const indicatorIconValue = !isGridView ? <Box boxSize='20px' /> : undefined;
  const currentIndicatingStatusIcon = currentIndicatingStatusValueFormatted ? statusIcon : indicatorIconValue;

  return (
    <>
      {viewVariables.map((variable, index) => {
        const isLastItem = index === viewVariables.length - 1;
        const tdProps = {
          borderRight: isLastItem ? 'none' : '0.5px solid'
        };

        if (selectedIndicatingStatus?.value === variable) {
          return (
            <CustomTableCell key={variable} tdProps={{ borderBottom: 'none' }}>
              <Flex {...commonTdChildrenProps} {...tdProps} gap='xs' align='center'>
                {!isVisionOnly && currentIndicatingStatusIcon}
                <Text
                  {...(!isGridView && { fontWeight: 400 })}
                  color={isGridView ? 'text.gray' : 'text.gray.weak'}
                  textStyle={isGridView ? 'label.200' : 'paragraph.200.light'}
                >
                  {currentIndicatingStatusValueFormatted ?? '-'}
                </Text>
              </Flex>
            </CustomTableCell>
          );
        }

        return (
          <CustomTableCell key={variable} tdProps={{ borderBottom: 'none' }}>
            <Flex {...commonTdChildrenProps} {...tdProps} align='center'>
              <PondCardViewMapper
                viewVariable={variable}
                manuallyMonitored={manuallyMonitored}
                symmetry={symmetry}
                lastMonitoringTotalAnimals={lastMonitoringTotalAnimals}
                seedingAverageWeight={seedingAverageWeight}
                profitProjectionView='current'
                hasSurvivalEstimatedValues={hasSurvivalEstimatedValues}
                hasFeedEstimatedValues={hasFeedEstimatedValues}
                isGridView={false}
                pondSizeValue={pondSizeValue}
                stockedAtDate={stockedAtDate}
                animalsStockedHaValue={animalsStockedHaValue}
                partialHarvestHaValue={partialHarvestHaValue}
                lastPartialHarvestDate={lastPartialHarvestDate}
                lastMonitoringDistance={lastMonitoringDistance}
                harvestPlanDate={harvestPlanDate}
                numberOfPartialHarvests={numberOfPartialHarvests}
                {...rest}
              />
            </Flex>
          </CustomTableCell>
        );
      })}
    </>
  );
}
