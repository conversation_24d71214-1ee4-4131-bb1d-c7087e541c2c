import { Box, Flex, FlexProps, Text } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { useRouter } from 'next/router';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { useUpdatePopulationApi } from '@screens/population/hooks/use-update-population-api';
import { useAnalytics } from '@hooks/use-analytics';
import { useAppDispatch, useAppSelector } from '@redux/hooks';
import debounce from 'lodash/debounce';
import { FlagSolid } from '@icons/flag/flag-solid';
import { FlagOutlined } from '@icons/flag/flag-outlined';
import { actionsName } from '@utils/segment';
import { BaseLink } from '@components/base/base-link';
import { slugify } from '@utils/string';
import { formatNumber, isNumber } from '@utils/number';
import { ChevronRightFilled } from '@icons/chevron-right/chevron-right-filled';
import { ChevronDownFilled } from '@icons/chevron-down/chevron-down-filled';
import { ChatOutlined } from '@icons/chat/chat-outlined';
import { setFarmBookPondIdAction, setIsFarmBookOpenAction } from '@redux/farm-book';
import { BaseSpinner } from '@components/base/base-spinner';
import { PondCardProps } from '@components/pond-card/pond-card';
import { pondsTableFirstColumnWidth } from '@screens/farm-summary/components/ponds-tab';
import { Tooltip } from '@components/ui/tooltip';
import { AdminOrSupervisorOrOperationWrapper } from '@components/permission-wrappers/admin-or-supervisor-or-operation-wrapper';
import { LocationMarker } from '@icons/location-marker/location-marker-icon';
import { convertGpsDistance } from '@utils/gps';

interface PondCardHeaderProps
  extends Pick<
      PondCardProps,
      | 'farmId'
      | 'farmEid'
      | 'pondEid'
      | 'pondName'
      | 'pondId'
      | 'isFlagged'
      | 'populationId'
      | 'profitProjectionView'
      | 'lastMonitoringDistance'
    >,
    FlexProps {
  cycleDays?: number;
  hasEstimatedValues?: boolean;
  isAdjusted?: boolean;
  isListView?: boolean;
  expandedId?: string;
  setExpandedId?: Dispatch<SetStateAction<string>>;
}

export function PondCardHeader(props: PondCardHeaderProps) {
  const {
    farmId,
    farmEid,
    pondEid,
    pondName,
    pondId,
    cycleDays,
    isFlagged,
    populationId,
    hasEstimatedValues,
    isAdjusted,
    isListView,
    lastMonitoringDistance,
    profitProjectionView,
    expandedId,
    setExpandedId,
    ...rest
  } = props;

  const { trans } = getTrans();
  const { query } = useRouter();

  const { trackAction } = useAnalytics();
  const dispatch = useAppDispatch();

  const lang = useAppSelector((state) => state.app.lang);

  const isExpanded = !!populationId && expandedId === populationId;

  const handleToggleExpandedPopulation = () => {
    setExpandedId(!isExpanded ? populationId : undefined);
    trackAction(actionsName.pondListViewExpandClicked, { populationId }).then();
  };

  useEffect(() => {
    if (profitProjectionView === 'current' || !profitProjectionView) return;
    setExpandedId?.(undefined);
  }, [profitProjectionView]);

  const convertedDistance = convertGpsDistance(lastMonitoringDistance);
  const convertedDistanceToPond = convertedDistance ? convertedDistance.distance : null;
  const convertedDistanceToPondUnit = convertedDistance ? convertedDistance.unit : null;
  const lastMonitoringDistanceFormatted = isNumber(convertedDistanceToPond)
    ? formatNumber(convertedDistanceToPond, { lang, fractionDigits: 2 })
    : null;

  return (
    <Flex
      direction={isListView ? 'row-reverse' : 'row'}
      align='center'
      gap={isListView ? 'sm' : 'lg'}
      justify={isListView ? 'flex-end' : 'space-between'}
      w={isListView ? `${pondsTableFirstColumnWidth}px` : '100%'}
      textStyle='label.200'
      bgColor={isExpanded ? 'bg.gray.strong' : 'white'}
      {...rest}
    >
      <Flex align='center' gap='md'>
        <BaseLink
          minW={isListView ? '66px' : 'unset'}
          _hover={{ textDecoration: 'underline' }}
          route='/farm/[farmEid]/pond/[pondEid]'
          params={{ ...query, farmEid, pondEid: slugify(`${pondEid}-${pondName}`), tab: undefined }}
        >
          <Text
            {...(isListView && { fontWeight: 400, maxW: '66px', overflow: 'hidden', textOverflow: 'ellipsis' })}
            textStyle={isListView ? 'paragraph.200.light' : 'label.100'}
            color={isListView ? 'text.gray.weak' : 'text.gray'}
            lineHeight='20px'
            letterSpacing='none'
            data-cy='pond-name'
            title={pondName}
          >
            {`${pondName}${hasEstimatedValues ? ' *' : ''}`}
          </Text>
        </BaseLink>
        {isNumber(cycleDays) ? (
          <Text size='label300' color='text.gray.disabled' {...(isListView && { fontWeight: 400 })}>
            {trans('t_days_d', { count: formatNumber(cycleDays, { lang, fractionDigits: 0 }) })}
          </Text>
        ) : (
          <Box minW='21px' />
        )}
      </Flex>

      <Flex align='center' gap='sm-alt' minW='84px' w='max-content'>
        {profitProjectionView === 'current' && isListView && !!populationId ? (
          <>
            {isExpanded ? (
              <ChevronDownFilled
                boxSize='20px'
                cursor='pointer'
                color='icon.gray'
                hasBackground={false}
                onClick={handleToggleExpandedPopulation}
              />
            ) : (
              <ChevronRightFilled
                boxSize='20px'
                cursor='pointer'
                hasBackground={false}
                onClick={handleToggleExpandedPopulation}
              />
            )}
          </>
        ) : (
          <Box h='20px' minW='20px' maxW='20px' />
        )}

        {lastMonitoringDistance > 20 ? (
          <Tooltip
            content={
              convertedDistanceToPond > 0
                ? trans('x_monitoring_location_from_the_pond', {
                    distance: lastMonitoringDistanceFormatted,
                    unit: convertedDistanceToPondUnit
                  })
                : trans('t_monitoring_is_near_pond')
            }
          >
            <LocationMarker cursor='pointer' color='icon.semanticRed' boxSize='20px' />
          </Tooltip>
        ) : (
          <Box h='20px' minW='20px' maxW='20px' />
        )}

        {!isListView && (
          <ChatOutlined
            cursor='pointer'
            onClick={() => {
              dispatch(setFarmBookPondIdAction(pondId));
              dispatch(setIsFarmBookOpenAction(true));
              trackAction(actionsName.farmGroupViewPondFarmBookClicked, { pondId }).then();
            }}
          />
        )}

        <AdminOrSupervisorOrOperationWrapper farmId={farmId}>
          <FlagComponent pondId={pondId} populationId={populationId} isFlagged={isFlagged} />
        </AdminOrSupervisorOrOperationWrapper>
      </Flex>
    </Flex>
  );
}

interface FlagComponentProps {
  populationId: string;
  pondId: string;
  isFlagged: boolean;
}
function FlagComponent(props: FlagComponentProps) {
  const { populationId, pondId, isFlagged } = props;
  const [populationIdToUpdateFlag, setPopulationIdToUpdateFlag] = useState<string>();
  const [isUpdatingFlag, setIsUpdatingFlag] = useState<boolean>(false);
  const [isPondFlagged, setIsPondFlagged] = useState<boolean>(isFlagged);

  const { trackAction } = useAnalytics();
  const [_, updatePopulation] = useUpdatePopulationApi();

  const onUpdateFlag = debounce((params: { populationId: string; isPondFlagged: boolean }) => {
    const { isPondFlagged, populationId } = params;

    setIsUpdatingFlag(true);
    setPopulationIdToUpdateFlag(populationId);
    updatePopulation({
      params: {
        filter: { populationId },
        set: { metadata: { isFlagged: !isPondFlagged } }
      },
      successCallback() {
        setIsUpdatingFlag(false);
        setPopulationIdToUpdateFlag(undefined);
        setIsPondFlagged(!isPondFlagged);
      },
      failureCallback() {
        setIsUpdatingFlag(false);
        setPopulationIdToUpdateFlag(undefined);
      }
    });
  }, 250);

  if (isUpdatingFlag && populationIdToUpdateFlag === populationId) {
    return <BaseSpinner borderWidth='2px' size='sm' />;
  }

  const FlagIcon = isPondFlagged ? FlagSolid : FlagOutlined;

  if (populationIdToUpdateFlag !== populationId) {
    return (
      <FlagIcon
        cursor='pointer'
        onClick={() => {
          onUpdateFlag({ isPondFlagged, populationId });
          trackAction(actionsName.farmGroupViewPondFlagClicked, { pondId }).then();
        }}
      />
    );
  }
  return <Box h='20px' minW='20px' maxW='20px' />;
}
