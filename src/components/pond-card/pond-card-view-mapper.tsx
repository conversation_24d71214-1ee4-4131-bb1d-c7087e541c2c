import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { formatNumber, isNumber } from '@utils/number';
import isUndefined from 'lodash/isUndefined';
import { JSX, ReactNode, useState } from 'react';
import { Box, Flex, FlexProps, Text, TextProps } from '@chakra-ui/react';
import { AverageWeightChangePopover } from '@screens/data-updater/components/average-weight-change-popover';
import { InsightsIcon } from '@icons/insights-icon';
import { PondCardProps } from '@components/pond-card/pond-card';
import { GroupViewVariable } from '@screens/group-summary/hooks/use-get-group-view-variables';
import { FarmSummaryViewVariable } from '@screens/farm-summary/helpers/pond-list';
import { UseGetPondCardDataReturn } from '@components/pond-card/hooks/use-get-pond-card-data';
import { formatDate } from '@utils/date';
import { ExclamationMarkSolid } from '@icons/exclamation-mark/exclamation-mark-solid';
import { WarningIcon } from '@icons/warning/warning-icon';
import { HoverCardArrow, HoverCardContent, HoverCardRoot, HoverCardTrigger } from '@components/ui/hover-card';
import { SymmetryTooltipIcon } from '@components/pond-card/symmetry-tooltip/symmetry-tooltip-icon';

export type PondCardCycleInfoValues = {
  pondSizeValue: string;
  animalsStockedHaValue: string | number;
  partialHarvestHaValue: string;
  lastPartialHarvestDate: string;
  numberOfPartialHarvests: number;
};

type ViewVariableOption = GroupViewVariable | FarmSummaryViewVariable;

type PondCardViewMapperPropsFromPondCard = Pick<
  PondCardProps,
  | 'manuallyMonitored'
  | 'symmetry'
  | 'lastMonitoringTotalAnimals'
  | 'seedingAverageWeight'
  | 'stockedAtDate'
  | 'harvestPlanDate'
  | 'lastMonitoringDistance'
>;
type PondCardViewMapperPropsFromUseGetPondCardData = Omit<
  UseGetPondCardDataReturn,
  'currentIndicatingStatusColor' | 'currentIndicatingStatusIcon' | 'currentIndicatingStatusValueFormatted'
>;

interface PondCardViewProps
  extends PondCardViewMapperPropsFromUseGetPondCardData,
    PondCardViewMapperPropsFromPondCard,
    PondCardCycleInfoValues {
  viewVariable: ViewVariableOption;
  profitProjectionView: 'current' | 'harvest';
  hasSurvivalEstimatedValues: boolean;
  hasFeedEstimatedValues: boolean;
  isGridView: boolean;
}

export function PondCardViewMapper(props: PondCardViewProps) {
  const { trans } = getTrans();
  const lang = useAppSelector((state) => state.app.lang);
  const user = useAppSelector((state) => state.auth.user);
  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};

  const {
    modelAverageWeight,
    manualAverageWeightUpdatedBy,
    isManualAverageWeight,
    averageWeightFormatted,
    growthLinearFormatted,
    growthDailyFormatted,
    weeklyGrowthFormatted,
    growthTwoWeeksFormatted,
    growthThreeWeeksFormatted,
    growthFourWeeksFormatted,
    cumulativeFcrFormatted,
    weeklyFcrFormatted,
    adjustedFcrFormatted,
    feedKgByHaFormatted,
    weeklyFeedGivenFormatted,
    totalFeedGivenKgFormatted,
    totalFeedGivenLbsFormatted,
    feedAsBiomassPercentFormatted,
    survivalRateWithPartialHarvestFormatted,
    survivalRateFormatted,
    biomassLbsFormatted,
    totalBiomassLbsFormatted,
    biomassLbsByHaFormatted,
    totalBiomassLbsByHaFormatted,
    animalsRemainingHaFormatted,
    animalsRemainingM2Formatted,
    weightCvFormatted,
    profitPerHaPerDayAdminValue,
    costPerPoundAdminValue,
    costPoundHarvestAdminValue,
    profitPerHaPerDayTextColor,
    costPerPoundTextColor,
    totalProfitAdminValue,
    totalRevenueAdminValue,
    totalCostsAdminValue,
    stockingCostsAdminValue,
    stockingCostsMillarAdminValue,
    feedCostPerKgAdminValue,
    cumulativeOverheadCostsAdminValue,
    cumulativeFeedCostsAdminValue,
    revenuePerPoundAdminValue,
    totalRevenuePerPoundAdminValue,
    profitPerPoundAdminValue,
    daysUntilHarvestFormatted,
    daysOffFromOptimalTitle,
    manuallyMonitored,
    seedingAverageWeight,
    isAverageWeightInvalid,
    symmetry,
    lastMonitoringTotalAnimals,
    viewVariable,
    profitProjectionView,
    hasSurvivalEstimatedValues,
    hasFeedEstimatedValues,
    isGridView,
    survivalFeedFormatted,
    pondSizeValue,
    stockedAtDate,
    animalsStockedHaValue,
    partialHarvestHaValue,
    lastPartialHarvestDate,
    harvestPlanDate,
    numberOfPartialHarvests,
    lastMonitoringDistance
  } = props ?? {};

  const typeText = {
    left: trans('t_left'),
    right: trans('t_right'),
    normal: trans('t_normal')
  };

  const { isRed, isGreen, isYellow } = symmetry?.alert ?? {};
  const hasAlert = isRed || isYellow;

  const seedingAverageWeightFormatted = isNumber(seedingAverageWeight)
    ? formatNumber(seedingAverageWeight, { lang, fractionDigits: 2 })
    : '-';
  const isCurrentView = profitProjectionView === 'current';
  const abw = isCurrentView ? averageWeightFormatted || seedingAverageWeightFormatted : averageWeightFormatted;

  const isBiomassUnitLbs = unitsConfig?.biomass === 'lbs' || isUndefined(unitsConfig?.biomass);

  const unitLabel = isBiomassUnitLbs ? trans('t_lb') : trans('t_kg');

  const lastPartialHarvestDateFormatted = lastPartialHarvestDate
    ? formatDate({ locale: lang, date: lastPartialHarvestDate, format: 'LLL dd, yyyy' })
    : null;

  const harvestPlanDateFormatted = harvestPlanDate
    ? formatDate({ locale: lang, date: harvestPlanDate, format: 'LLL dd, yyyy' })
    : null;

  const textStyle: TextProps = {
    ...(!isGridView && { fontWeight: 400 }),
    color: isGridView ? 'text.gray' : 'text.gray.weak',
    textStyle: isGridView ? 'label.300' : 'paragraph.200.light'
  };

  const componentMap: Record<ViewVariableOption, JSX.Element> = {
    pondSize: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_pond_size')}
        value={<Text {...textStyle}>{pondSizeValue ?? '-'}</Text>}
      />
    ),
    stockingDate: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_stocking_date')}
        value={<Text {...textStyle}>{stockedAtDate ?? '-'}</Text>}
      />
    ),
    animalsStockedHa: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_animals_stocked_ha')}
        value={<Text {...textStyle}>{animalsStockedHaValue ?? '-'}</Text>}
      />
    ),
    partialHarvestLbsHa: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_biomass_from_partials_unit_ha', {
          unit: isBiomassUnitLbs ? trans('t_lb') : trans('t_kg')
        })}
        value={<Text {...textStyle}>{partialHarvestHaValue ?? '-'}</Text>}
      />
    ),
    lastPartialHarvestDate: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_date_of_last_partial_harvest')}
        value={<Text {...textStyle}>{lastPartialHarvestDateFormatted ?? '-'}</Text>}
      />
    ),
    plannedHarvestDate: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_final_harvest_date')}
        value={<Text {...textStyle}>{harvestPlanDateFormatted ?? '-'}</Text>}
      />
    ),
    numberOfPartialHarvests: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_partial_harvest_done')}
        value={<Text {...textStyle}>{numberOfPartialHarvests || '-'}</Text>}
      />
    ),
    averageWeight: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_abw_g')}
        value={
          <>
            {!isAverageWeightInvalid && (
              <Flex align='center' gap='xs-alt'>
                {isManualAverageWeight && isCurrentView && (
                  <AverageWeightChangePopover
                    modelAverageWeight={modelAverageWeight}
                    updatedBy={manualAverageWeightUpdatedBy}
                  />
                )}
                <Text {...textStyle}>{isNumber(abw) ? abw : '-'}</Text>
              </Flex>
            )}
            {isAverageWeightInvalid && (
              <AverageWeightHoverCard averageWeightFormatted={averageWeightFormatted} textStyle={textStyle} />
            )}
          </>
        }
      />
    ),
    growthLinear: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_growth_g_wk')}
        value={<Text {...textStyle}>{growthLinearFormatted || '-'}</Text>}
      />
    ),
    growthDaily: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_growth_g_day')}
        value={<Text {...textStyle}>{growthDailyFormatted || '-'}</Text>}
      />
    ),
    weeklyGrowth: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_growth_monitored_x_week_g_wk', { weeks: 1 })}
        value={<Text {...textStyle}>{weeklyGrowthFormatted || '-'}</Text>}
      />
    ),
    growth1WeekAvg: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_growth_monitored_x_week_g_wk', { weeks: 1 })}
        value={<Text {...textStyle}>{weeklyGrowthFormatted || '-'}</Text>}
      />
    ),
    growth2WeekAvg: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_growth_monitored_x_week_g_wk', { weeks: 2 })}
        value={<Text {...textStyle}>{growthTwoWeeksFormatted || '-'}</Text>}
      />
    ),
    growth3WeekAvg: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_growth_monitored_x_week_g_wk', { weeks: 3 })}
        value={<Text {...textStyle}>{growthThreeWeeksFormatted || '-'}</Text>}
      />
    ),
    growth4w: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_growth_monitored_x_week_g_wk', { weeks: 4 })}
        value={<Text {...textStyle}>{growthFourWeeksFormatted || '-'}</Text>}
      />
    ),
    fcr: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_fcr')}
        value={<Text {...textStyle}>{cumulativeFcrFormatted ?? '-'}</Text>}
      />
    ),
    fcrCumulative: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_fcr')}
        value={<Text {...textStyle}>{cumulativeFcrFormatted ?? '-'}</Text>}
      />
    ),
    fcrWeekly: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_fcr_weekly')}
        value={<Text {...textStyle}>{weeklyFcrFormatted ?? '-'}</Text>}
      />
    ),
    adjustedFcr: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_fcr_adjusted')}
        value={<Text {...textStyle}>{adjustedFcrFormatted ?? '-'}</Text>}
      />
    ),
    kgPerHaPerDayGivenKg: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_feed_given_daily_kg_ha')}
        value={<Text {...textStyle}>{feedKgByHaFormatted || '-'}</Text>}
      />
    ),
    weeklyFeedGivenKg: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_feed_given_weekly_kg')}
        value={<Text {...textStyle}>{weeklyFeedGivenFormatted || '-'}</Text>}
      />
    ),
    totalFeedGivenKg: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        hasEstimatedValues={totalFeedGivenKgFormatted && hasFeedEstimatedValues}
        title={trans('t_feed_given_kg')}
        value={<Text {...textStyle}>{totalFeedGivenKgFormatted || '-'}</Text>}
      />
    ),
    totalFeedGivenLbs: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        hasEstimatedValues={totalFeedGivenLbsFormatted && hasFeedEstimatedValues}
        title={trans('t_feed_given_lg')}
        value={<Text {...textStyle}>{totalFeedGivenLbsFormatted || '-'}</Text>}
      />
    ),
    biomassPercentage: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_feed_%_of_biomass')}
        value={<Text {...textStyle}>{feedAsBiomassPercentFormatted || '-'}</Text>}
      />
    ),
    survivalRate: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        hasEstimatedValues={survivalRateFormatted && hasSurvivalEstimatedValues}
        title={trans('t_survival')}
        value={<Text {...textStyle}>{survivalRateFormatted || '-'}</Text>}
      />
    ),
    survivalFeed: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        hasEstimatedValues={survivalFeedFormatted && hasFeedEstimatedValues}
        title={trans('t_kampi_survival')}
        value={<Text {...textStyle}>{survivalFeedFormatted || '-'}</Text>}
      />
    ),
    survival: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        hasEstimatedValues={survivalRateFormatted && hasSurvivalEstimatedValues}
        title={trans('t_survival')}
        value={<Text {...textStyle}>{survivalRateFormatted || '-'}</Text>}
      />
    ),
    survivalWithPartialHarvest: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        hasEstimatedValues={survivalRateWithPartialHarvestFormatted && hasSurvivalEstimatedValues}
        title={trans('t_survival_include_harvests')}
        value={<Text {...textStyle}>{survivalRateWithPartialHarvestFormatted || '-'}</Text>}
      />
    ),
    totalBiomass: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_biomass_in_pond_unit', { unit: unitLabel })}
        value={<Text {...textStyle}>{biomassLbsFormatted ?? '-'}</Text>}
      />
    ),
    biomassLb: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_biomass_in_pond_unit', { unit: unitLabel })}
        value={<Text {...textStyle}>{biomassLbsFormatted ?? '-'}</Text>}
      />
    ),
    biomassLbTotal: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_biomass_include_harvests_unit', { unit: unitLabel })}
        value={<Text {...textStyle}>{totalBiomassLbsFormatted ?? '-'}</Text>}
      />
    ),
    biomassPerHa: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_biomass_in_pond_unit_ha', { unit: unitLabel })}
        value={<Text {...textStyle}>{biomassLbsByHaFormatted ?? '-'}</Text>}
      />
    ),
    biomassLbHa: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_biomass_in_pond_unit_ha', { unit: unitLabel })}
        value={<Text {...textStyle}>{biomassLbsByHaFormatted ?? '-'}</Text>}
      />
    ),
    totalBiomassLbHa: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_biomass_include_harvests_unit_ha', { unit: unitLabel })}
        value={<Text {...textStyle}>{totalBiomassLbsByHaFormatted ?? '-'}</Text>}
      />
    ),
    animalsRemainingM2: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_animals_in_pond_m2')}
        value={<Text {...textStyle}>{animalsRemainingM2Formatted || '-'}</Text>}
      />
    ),
    animalsRemainingHa: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_animals_in_pond_ha')}
        value={<Text {...textStyle}>{animalsRemainingHaFormatted || '-'}</Text>}
      />
    ),
    dispersion: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_dispersion')}
        value={
          <>
            {manuallyMonitored && <Text {...textStyle}>N/A</Text>}
            {!manuallyMonitored && (
              <>
                {(!weeklyFcrFormatted || isAverageWeightInvalid) && '-'}
                {weeklyFcrFormatted && !isAverageWeightInvalid && (
                  <Text {...textStyle} data-cy='pond-cv'>
                    {weightCvFormatted} {trans('t_cv')}
                  </Text>
                )}
              </>
            )}
          </>
        }
      />
    ),
    symmetry: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_symmetry')}
        value={
          <>
            {manuallyMonitored && (
              <Flex gap='xs-alt' align='center'>
                <Box w={isGridView ? '18px' : '20px'} h={isGridView ? '18px' : '20px'} />
                <Text {...textStyle}>N/A</Text>
              </Flex>
            )}
            {!manuallyMonitored && !symmetry && (
              <Flex gap='xs-alt' align='center'>
                <Box w={isGridView ? '18px' : '20px'} h={isGridView ? '18px' : '20px'} />
                <Text {...textStyle}>-</Text>
              </Flex>
            )}
            {!manuallyMonitored && !!symmetry && (
              <Flex gap='xs-alt' align='center'>
                {hasAlert ? (
                  <SymmetryTooltipIcon
                    totalAnimals={lastMonitoringTotalAnimals}
                    symmetry={symmetry}
                    triggerProps={{
                      pos: isGridView ? 'absolute' : 'static',
                      insetEnd: '8px',
                      top: '12px',
                      boxSize: isGridView ? '18px' : '20px'
                    }}
                    icon={
                      <>
                        {isGreen && (
                          <InsightsIcon
                            pos={isGridView ? 'absolute' : 'static'}
                            insetEnd='0'
                            top='0'
                            boxSize={isGridView ? '18px' : '20px'}
                          />
                        )}
                        {isRed && (
                          <WarningIcon
                            pos={isGridView ? 'absolute' : 'static'}
                            insetEnd='0'
                            top='0'
                            boxSize={isGridView ? '18px' : '20px'}
                            color='shrimpyPinky.600'
                          />
                        )}
                        {isYellow && (
                          <ExclamationMarkSolid
                            pos={isGridView ? 'absolute' : 'static'}
                            insetEnd='0'
                            top='0'
                            boxSize={isGridView ? '18px' : '20px'}
                            color='semanticYellow.600'
                          />
                        )}
                      </>
                    }
                  />
                ) : (
                  <Box w={isGridView ? '18px' : '20px'} h={isGridView ? '18px' : '20px'} />
                )}

                <Text textTransform='capitalize' {...textStyle}>
                  {typeText[symmetry.type] ?? symmetry.type}
                </Text>
              </Flex>
            )}
          </>
        }
      />
    ),
    profitPerHaPerDay: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_profit_include_dry_days_ha_day')}
        value={
          <Text {...textStyle} color={profitPerHaPerDayTextColor ?? 'text.gray'}>
            {profitPerHaPerDayAdminValue ?? '-'}
          </Text>
        }
      />
    ),
    profitPerPound: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={isBiomassUnitLbs ? trans('t_profit_lb') : trans('t_profit_kg')}
        value={<Text {...textStyle}>{profitPerPoundAdminValue ?? '-'}</Text>}
      />
    ),
    totalProfit: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_profit')}
        value={<Text {...textStyle}>{totalProfitAdminValue ?? '-'}</Text>}
      />
    ),
    lastMonitoringDistance: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_monitoring_distance_from_pond')}
        value={<Text {...textStyle}>{lastMonitoringDistance ?? '-'}</Text>}
      />
    ),
    revenuePerPound: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={isBiomassUnitLbs ? trans('t_revenue_per_lb_live') : trans('t_revenue_per_kg_live')}
        value={<Text {...textStyle}>{revenuePerPoundAdminValue ?? '-'}</Text>}
      />
    ),
    totalRevenuePound: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={isBiomassUnitLbs ? trans('t_revenue_per_lb_include_harvest') : trans('t_revenue_per_kg_include_harvest')}
        value={<Text {...textStyle}>{totalRevenuePerPoundAdminValue ?? '-'}</Text>}
      />
    ),
    totalRevenue: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_revenue')}
        value={<Text {...textStyle}>{totalRevenueAdminValue ?? '-'}</Text>}
      />
    ),
    totalCosts: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_cost_$')}
        value={<Text {...textStyle}>{totalCostsAdminValue ?? '-'}</Text>}
      />
    ),
    stockingCosts: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_stocking_cost')}
        value={<Text {...textStyle}>{stockingCostsAdminValue ?? '-'}</Text>}
      />
    ),
    stockingCostsMillar: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_cost_millar')}
        value={<Text {...textStyle}>{stockingCostsMillarAdminValue ?? '-'}</Text>}
      />
    ),
    cumulativeOverheadCosts: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_overhead_cost_cumulative')}
        value={<Text {...textStyle}>{cumulativeOverheadCostsAdminValue ?? '-'}</Text>}
      />
    ),
    cumulativeFeedCosts: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_feed_cost_$')}
        value={<Text {...textStyle}>{cumulativeFeedCostsAdminValue ?? '-'}</Text>}
      />
    ),
    costPerPound: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_cost_per_unit_processed', { unit: unitLabel })}
        value={
          <Text {...textStyle} color={costPerPoundTextColor ?? 'text.gray'}>
            {costPerPoundAdminValue ?? '-'}
          </Text>
        }
      />
    ),
    costPoundHarvest: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_cost_per_unit_harvested', { unit: unitLabel })}
        value={
          <Text {...textStyle} color={costPerPoundTextColor ?? 'text.gray'}>
            {costPoundHarvestAdminValue ?? '-'}
          </Text>
        }
      />
    ),
    feedCostPerKg: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_feed_cost_per_kg')}
        value={<Text {...textStyle}>{feedCostPerKgAdminValue ?? '-'}</Text>}
      />
    ),
    daysUntilHarvest: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_days_until_harvest')}
        value={<Text {...textStyle}>{daysUntilHarvestFormatted ?? '-'} </Text>}
      />
    ),
    daysOffFromOptimal: (
      <SummaryItem
        titleStyle={textStyle}
        isGridView={isGridView}
        title={trans('t_optimal_harvest')}
        value={<Text {...textStyle}> {daysOffFromOptimalTitle ?? '-'} </Text>}
      />
    )
  };

  return <>{componentMap[viewVariable]}</>;
}

function AverageWeightHoverCard(props: { averageWeightFormatted: string | number; textStyle: TextProps }) {
  const { averageWeightFormatted, textStyle } = props;
  const { trans } = getTrans();
  const [open, setOpen] = useState(false);
  return (
    <HoverCardRoot
      open={open}
      onOpenChange={(value) => setOpen(value.open)}
      positioning={{ placement: 'top-start' }}
      lazyMount
      unmountOnExit
    >
      <HoverCardTrigger asChild>
        <Text
          {...textStyle}
          w='max-content'
          pos='relative'
          data-cy='pond-average-weight'
          onMouseOver={() => {
            setOpen(true);
          }}
          onMouseLeave={() => {
            setOpen(false);
          }}
        >
          {averageWeightFormatted}
        </Text>
      </HoverCardTrigger>
      <HoverCardContent overflow='initial' maxW='280px'>
        <HoverCardArrow />

        <Text whiteSpace='initial' size='label200'>
          {trans('t_invalid_weight')}
        </Text>
      </HoverCardContent>
    </HoverCardRoot>
  );
}

type SummaryItemProps = FlexProps & {
  title: string;
  value: ReactNode;
  isGridView: boolean;
  titleStyle?: TextProps;
  hasEstimatedValues?: boolean;
};

function SummaryItem(props: SummaryItemProps) {
  const { title, titleStyle, value, isGridView, hasEstimatedValues, ...rest } = props;

  return (
    <Flex
      px={isGridView ? 'sm-alt' : 0}
      py={isGridView ? 'sm-alt' : 0}
      gap={isGridView ? 'sm-alt' : 0}
      direction='column'
      align={isGridView ? 'unset' : 'center'}
      borderRadius='lg'
      textStyle='label.300'
      border={isGridView ? '1px solid' : 'none'}
      borderColor='border.gray.weak'
      position='relative'
      {...rest}
    >
      {isGridView && <Text {...titleStyle}>{title}:</Text>}
      <Flex gap='3xs'>
        {value}
        <Text {...titleStyle}>{hasEstimatedValues ? '*' : ''}</Text>
      </Flex>
    </Flex>
  );
}
