import { PondCardProps } from '@components/pond-card/pond-card';
import { Dispatch, SetStateAction } from 'react';

export type EmptyStateProps = Pick<
  PondCardProps,
  | 'farmId'
  | 'farmEid'
  | 'pondEid'
  | 'pondName'
  | 'pondId'
  | 'pondSize'
  | 'isFlagged'
  | 'populationId'
  | 'isLastPondCard'
  | 'profitProjectionView'
> & { isGridView: boolean; cycleDays: number; expandedId?: string; setExpandedId?: Dispatch<SetStateAction<string>> };
