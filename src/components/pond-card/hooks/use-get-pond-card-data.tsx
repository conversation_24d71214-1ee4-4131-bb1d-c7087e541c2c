import { indicatingStatusIconMap, indicatorsColorMap } from '@components/pond-card/helper';
import { PondCardProps } from '@components/pond-card/pond-card';
import { useAppSelector } from '@redux/hooks';
import { convertUnitByDivision, formatNumber, isNumber } from '@utils/number';
import { getDaysDiffBetweenDates } from '@screens/farm/helpers/farm-dates';
import {
  calculateAdminFinancialsFnType,
  useCalculateAdminFinancials
} from '@screens/pond/helpers/use-admin-financials';
import { isInvalidABW } from '@screens/farm/helpers/abw';
import { ReactNode } from 'react';
import { getTrans } from '@i18n/get-trans';
import { Box } from '@chakra-ui/react';
import { UserPreferences } from '@xpertsea/module-user-sdk';

type UseGetPondCardDataProps = Pick<
  PondCardProps,
  | 'indicators'
  | 'selectedIndicatingStatus'
  | 'profitProjectionView'
  | 'optimalHarvestDate'
  | 'lastHistory'
  | 'plannedProfitProjection'
  | 'productionPredictionHarvestDate'
  | 'farmTimezone'
  | 'cycleInformation'
  | 'farmId'
  | 'stockingCostsMillar'
  | 'manualAverageWeights'
>;

export type UseGetPondCardDataReturn = {
  modelAverageWeight: number;
  isManualAverageWeight: boolean;
  manualAverageWeightUpdatedBy: string;
  averageWeightFormatted: string | number;
  growthLinearFormatted: string | number;
  growthDailyFormatted: string | number;
  weeklyGrowthFormatted: string | number;
  growthTwoWeeksFormatted: string | number;
  growthThreeWeeksFormatted: string | number;
  growthFourWeeksFormatted: string | number;
  cumulativeFcrFormatted: string | number;
  weeklyFcrFormatted: string | number;
  adjustedFcrFormatted: string | number;
  feedKgByHaFormatted: string | number;
  weeklyFeedGivenFormatted: string | number;
  totalFeedGivenKgFormatted: string | number;
  totalFeedGivenLbsFormatted: string | number;
  feedAsBiomassPercentFormatted: string | number;
  survivalRateWithPartialHarvestFormatted: string | number;
  survivalRateFormatted: string | number;
  biomassLbsFormatted: string | number;
  totalBiomassLbsFormatted: string | number;
  biomassLbsByHaFormatted: string | number;
  totalBiomassLbsByHaFormatted: string | number;
  animalsRemainingHaFormatted: string | number;
  animalsRemainingM2Formatted: string | number;
  weightCvFormatted: string | number;
  currentIndicatingStatusValueFormatted: string | number;
  currentIndicatingStatusColor: string;
  currentIndicatingStatusIcon: ReactNode;
  profitPerHaPerDayAdminValue: string | number;
  costPerPoundAdminValue: string | number;
  costPoundHarvestAdminValue: string | number;
  profitPerHaPerDayTextColor: string;
  costPerPoundTextColor: string;
  totalProfitAdminValue: string | number;
  totalRevenueAdminValue: string | number;
  totalCostsAdminValue: string | number;
  stockingCostsAdminValue: string | number;
  stockingCostsMillarAdminValue: string | number;
  feedCostPerKgAdminValue: string | number;
  cumulativeOverheadCostsAdminValue: string | number;
  cumulativeFeedCostsAdminValue: string | number;
  revenuePerPoundAdminValue: string | number;
  totalRevenuePerPoundAdminValue: string | number;
  profitPerPoundAdminValue: string | number;
  daysUntilHarvestFormatted: string | number;
  daysOffFromOptimalTitle: string;
  isAverageWeightInvalid: boolean;
  survivalFeedFormatted: string | number;
};

type getPondCardDataParamsType = Omit<UseGetPondCardDataProps, 'farmId'> & {
  calculateAdminFinancials: calculateAdminFinancialsFnType;
  unitsConfig: UserPreferences['unitsConfig'];
  lang: string;
};

const getPondCardData = (params: getPondCardDataParamsType): UseGetPondCardDataReturn => {
  const { trans } = getTrans();

  const {
    indicators,
    selectedIndicatingStatus,
    profitProjectionView,
    optimalHarvestDate,
    lastHistory,
    plannedProfitProjection,
    productionPredictionHarvestDate,
    stockingCostsMillar,
    manualAverageWeights,
    calculateAdminFinancials,
    lang,
    unitsConfig
  } = params;

  const isCurrentView = profitProjectionView === 'current';
  const viewData = isCurrentView ? lastHistory : productionPredictionHarvestDate;
  const viewProfitData = isCurrentView ? lastHistory : plannedProfitProjection;

  const manualAverageWeight = manualAverageWeights?.find((item) => item.date === lastHistory?.date);

  const {
    averageWeight,
    abw,
    weeklyFcr,
    cumulativeFcr,
    adjustedFcr,
    feedKgByHa,
    feedAsBiomassPercent,
    weeklyFeedGiven,
    totalFeedGivenKg,
    totalFeedGivenLbs,
    totalCosts,
    stockingCosts,
    costPoundHarvest,
    feedCostPerKg,
    cumulativeOverheadCosts,
    cumulativeFeedCosts,
    weeklyGrowth,
    growthTwoWeeks,
    growthThreeWeeks,
    growth4w,
    growthDaily,
    growthLinear,
    biomassLbsByHa,
    biomassLbs,
    totalBiomassLbs,
    totalBiomassLbsByHa,
    survival,
    survivalWithPartialHarvest,
    animalsRemainingHa,
    animalsRemainingM2,
    survivalFeed
  } = viewData ?? {};

  const {
    profitPerHaPerDay,
    profitPerPound,
    costPerPound,
    totalProfit,
    totalRevenue,
    revenuePerPound,
    totalRevenuePound
  } = viewProfitData ?? {};

  //Financials
  const {
    profitPerHaPerDayAdminValue,
    costPerPoundAdminValue,
    profitPerHaPerDayTextColor,
    costPerPoundTextColor,
    costPoundHarvestAdminValue,
    stockingCostsMillarAdminValue,
    totalProfitAdminValue,
    totalRevenueAdminValue,
    totalCostsAdminValue,
    stockingCostsAdminValue,
    feedCostPerKgAdminValue,
    cumulativeOverheadCostsAdminValue,
    cumulativeFeedCostsAdminValue,
    revenuePerPoundAdminValue,
    totalRevenuePerPoundAdminValue,
    profitPerPoundAdminValue
  } = calculateAdminFinancials({
    profitPerHaPerDay,
    costPerPound,
    costPoundHarvest,
    profitPerPound,
    totalRevenue,
    totalProfit,
    revenuePerPound,
    totalRevenuePound,
    totalCosts,
    stockingCosts,
    feedCostPerKg,
    cumulativeOverheadCosts,
    cumulativeFeedCosts,
    indicators,
    profitProjectionView,
    stockingCostsMillar
  });

  // ABW Variables
  const averageWeightValue = averageWeight ?? abw;
  const { isInvalid, abwLabel } = isInvalidABW(averageWeightValue, true);

  const finalAverageWeight =
    isCurrentView && manualAverageWeight?.averageWeight ? manualAverageWeight?.averageWeight : averageWeightValue;

  const averageWeightFormatted =
    !isInvalid && isNumber(finalAverageWeight)
      ? formatNumber(finalAverageWeight, { lang, fractionDigits: 2 })
      : abwLabel;

  const weeklyGrowthFormatted = isNumber(weeklyGrowth) ? formatNumber(weeklyGrowth, { lang, fractionDigits: 2 }) : null;

  const growthTwoWeeksFormatted = isNumber(growthTwoWeeks)
    ? formatNumber(growthTwoWeeks, { lang, fractionDigits: 2 })
    : null;

  const growthThreeWeeksFormatted = isNumber(growthThreeWeeks)
    ? formatNumber(growthThreeWeeks, { lang, fractionDigits: 2 })
    : null;

  const growthFourWeeksFormatted = isNumber(growth4w) ? formatNumber(growth4w, { lang, fractionDigits: 2 }) : null;

  const growthDailyFormatted = isNumber(growthDaily) ? formatNumber(growthDaily, { lang, fractionDigits: 2 }) : null;

  const growthLinearFormatted = isNumber(growthLinear) ? formatNumber(growthLinear, { lang, fractionDigits: 2 }) : null;

  //FCR Variables
  const cumulativeFcrFormatted = isNumber(cumulativeFcr)
    ? formatNumber(cumulativeFcr, { lang, fractionDigits: 2 })
    : null;

  const weeklyFcrFormatted = isNumber(weeklyFcr) ? formatNumber(weeklyFcr, { lang, fractionDigits: 2 }) : null;

  const adjustedFcrFormatted = isNumber(adjustedFcr) ? formatNumber(adjustedFcr, { lang, fractionDigits: 2 }) : null;

  //Feed Variables
  const feedKgByHaFormatted = isNumber(feedKgByHa) ? formatNumber(feedKgByHa, { lang, fractionDigits: 2 }) : null;

  const weeklyFeedGivenFormatted = isNumber(weeklyFeedGiven)
    ? formatNumber(weeklyFeedGiven, { lang, fractionDigits: 0 })
    : null;

  const totalFeedGivenKgFormatted = isNumber(totalFeedGivenKg)
    ? formatNumber(totalFeedGivenKg, { lang, fractionDigits: 0 })
    : null;

  const totalFeedGivenLbsFormatted = isNumber(totalFeedGivenLbs)
    ? formatNumber(totalFeedGivenLbs, { lang, fractionDigits: 0 })
    : null;

  const feedAsBiomassPercentFormatted = isNumber(feedAsBiomassPercent)
    ? formatNumber(feedAsBiomassPercent, { lang, fractionDigits: 2, isPercentage: true })
    : null;

  //Survival
  const survivalRateFormatted = isNumber(survival)
    ? formatNumber(survival, { lang, fractionDigits: 0, isPercentage: true })
    : null;

  const survivalFeedFormatted = isNumber(survivalFeed)
    ? formatNumber(survivalFeed, { lang, fractionDigits: 2, isPercentage: true })
    : null;

  const survivalRateWithPartialHarvestFormatted = isNumber(survivalWithPartialHarvest)
    ? formatNumber(survivalWithPartialHarvest, { lang, fractionDigits: 0, isPercentage: true })
    : null;

  //Biomass
  const biomassLbsFormatted = isNumber(biomassLbs)
    ? formatNumber(convertUnitByDivision(biomassLbs, unitsConfig?.biomass), { lang, fractionDigits: 0 })
    : null;

  const totalBiomassLbsFormatted = isNumber(totalBiomassLbs)
    ? formatNumber(convertUnitByDivision(totalBiomassLbs, unitsConfig?.biomass), { lang, fractionDigits: 0 })
    : null;

  const biomassLbsByHaFormatted = isNumber(biomassLbsByHa)
    ? formatNumber(convertUnitByDivision(biomassLbsByHa, unitsConfig?.biomass), { lang, fractionDigits: 0 })
    : null;

  const totalBiomassLbsByHaFormatted = isNumber(totalBiomassLbsByHa)
    ? formatNumber(convertUnitByDivision(totalBiomassLbsByHa, unitsConfig?.biomass), { lang, fractionDigits: 0 })
    : null;

  //Animals
  const animalsRemainingHaFormatted = isNumber(animalsRemainingHa)
    ? formatNumber(animalsRemainingHa, { lang, fractionDigits: 0 })
    : null;

  const animalsRemainingM2Formatted = isNumber(animalsRemainingM2)
    ? formatNumber(animalsRemainingM2, { lang, fractionDigits: 2 })
    : null;

  //Dispersion
  const weightCv = lastHistory?.weightCv;

  const weightCvFormatted = isNumber(weightCv)
    ? formatNumber(weightCv, { lang, fractionDigits: 1, isPercentage: true })
    : null;

  //others
  const lastHistoryDate = lastHistory?.date;
  const predictionDate = productionPredictionHarvestDate?.date;

  const daysUntilHarvest = getDaysDiffBetweenDates({
    baseDate: predictionDate,
    dateToCompare: lastHistoryDate
  });

  const daysUntilHarvestFormatted = isNumber(daysUntilHarvest)
    ? formatNumber(daysUntilHarvest, { lang, fractionDigits: 0 })
    : null;

  const daysOffFromOptimal = getDaysDiffBetweenDates({
    baseDate: optimalHarvestDate,
    dateToCompare: predictionDate
  });

  const daysOffFromOptimalFormatted = formatNumber(Math.abs(daysOffFromOptimal), { lang, fractionDigits: 0 });
  const daysOffFromOptimalLater =
    daysOffFromOptimal === 1
      ? trans('t_x_day_later', { days: daysOffFromOptimalFormatted })
      : trans('t_x_days_later', { days: daysOffFromOptimalFormatted });
  const daysOffFromOptimalEarlier =
    daysOffFromOptimal === -1
      ? trans('t_x_day_earlier', { days: daysOffFromOptimalFormatted })
      : trans('t_x_days_earlier', { days: daysOffFromOptimalFormatted });

  const daysOffFromOptimalTitleFormatted = daysOffFromOptimal > 0 ? daysOffFromOptimalLater : daysOffFromOptimalEarlier;

  const daysOffFromOptimalTitle = isNumber(daysOffFromOptimal) ? daysOffFromOptimalTitleFormatted : null;

  //Indicating Status
  const selectedIndicatingStatusValue = selectedIndicatingStatus?.value;

  let currentIndicatingStatusValueFormatted;
  let currentIndicatingStatusColor = '';
  let currentIndicatingStatusIcon = <Box boxSize='20px' />;

  switch (selectedIndicatingStatusValue) {
    case 'profitPerHaPerDay':
      {
        currentIndicatingStatusValueFormatted = profitPerHaPerDayAdminValue;
        const colorToTrack = indicators?.profitPerHaPerDay?.[profitProjectionView]?.color;
        currentIndicatingStatusColor = indicatorsColorMap[colorToTrack];
        currentIndicatingStatusIcon = indicatingStatusIconMap[colorToTrack];
      }
      break;
    case 'costPerPound':
      {
        currentIndicatingStatusValueFormatted = costPerPoundAdminValue;
        const colorToTrack = indicators?.costPerPound?.[profitProjectionView]?.color;
        currentIndicatingStatusColor = indicatorsColorMap[colorToTrack];
        currentIndicatingStatusIcon = indicatingStatusIconMap[colorToTrack];
      }
      break;
    case 'weeklyGrowth':
    case 'growth1WeekAvg':
      {
        currentIndicatingStatusValueFormatted = weeklyGrowthFormatted;
        const colorToTrack = indicators?.growth?.[profitProjectionView]?.color;
        currentIndicatingStatusColor = indicatorsColorMap[colorToTrack];
        currentIndicatingStatusIcon = indicatingStatusIconMap[colorToTrack];
      }

      break;
    case 'growthLinear':
      {
        currentIndicatingStatusValueFormatted = growthLinearFormatted;
        const colorToTrack = indicators?.growthLinear?.[profitProjectionView]?.color;
        currentIndicatingStatusColor = indicatorsColorMap[colorToTrack];
        currentIndicatingStatusIcon = indicatingStatusIconMap[colorToTrack];
      }
      break;
    case 'averageWeight':
      {
        currentIndicatingStatusValueFormatted = averageWeightFormatted;
        const colorToTrack = indicators?.abw?.[profitProjectionView]?.color;
        currentIndicatingStatusColor = indicatorsColorMap[colorToTrack];
        currentIndicatingStatusIcon = indicatingStatusIconMap[colorToTrack];
      }
      break;
    case 'survivalRate':
    case 'survival':
      {
        currentIndicatingStatusValueFormatted = survivalRateFormatted;
        const colorToTrack = indicators?.survivalRate?.[profitProjectionView]?.color;
        currentIndicatingStatusColor = indicatorsColorMap[colorToTrack];
        currentIndicatingStatusIcon = indicatingStatusIconMap[colorToTrack];
      }
      break;
    case 'fcr':
    case 'fcrCumulative':
      {
        currentIndicatingStatusValueFormatted = cumulativeFcrFormatted;
        const colorToTrack = indicators?.fcr?.[profitProjectionView]?.color;
        currentIndicatingStatusColor = indicatorsColorMap[colorToTrack];
        currentIndicatingStatusIcon = indicatingStatusIconMap[colorToTrack];
      }
      break;
    default:
      currentIndicatingStatusValueFormatted = '-';
      break;
  }

  return {
    modelAverageWeight: lastHistory?.modelAverageWeight,
    isManualAverageWeight: !!manualAverageWeight?.averageWeight,
    manualAverageWeightUpdatedBy: manualAverageWeight?.updatedBy,
    averageWeightFormatted,
    growthLinearFormatted,
    growthDailyFormatted,
    weeklyGrowthFormatted,
    growthTwoWeeksFormatted,
    growthThreeWeeksFormatted,
    growthFourWeeksFormatted,
    cumulativeFcrFormatted,
    weeklyFcrFormatted,
    adjustedFcrFormatted,
    feedKgByHaFormatted,
    weeklyFeedGivenFormatted,
    totalFeedGivenKgFormatted,
    totalFeedGivenLbsFormatted,
    feedAsBiomassPercentFormatted,
    survivalRateWithPartialHarvestFormatted,
    survivalRateFormatted,
    biomassLbsFormatted,
    totalBiomassLbsFormatted,
    biomassLbsByHaFormatted,
    totalBiomassLbsByHaFormatted,
    animalsRemainingHaFormatted,
    animalsRemainingM2Formatted,
    weightCvFormatted,
    currentIndicatingStatusValueFormatted,
    currentIndicatingStatusColor,
    currentIndicatingStatusIcon,
    profitPerHaPerDayAdminValue,
    costPerPoundAdminValue,
    costPoundHarvestAdminValue,
    stockingCostsMillarAdminValue,
    profitPerHaPerDayTextColor,
    costPerPoundTextColor,
    totalProfitAdminValue,
    totalRevenueAdminValue,
    totalCostsAdminValue,
    stockingCostsAdminValue,
    feedCostPerKgAdminValue,
    cumulativeOverheadCostsAdminValue,
    cumulativeFeedCostsAdminValue,
    revenuePerPoundAdminValue,
    totalRevenuePerPoundAdminValue,
    profitPerPoundAdminValue,
    daysUntilHarvestFormatted,
    daysOffFromOptimalTitle,
    isAverageWeightInvalid: isInvalid,
    survivalFeedFormatted
  };
};

export function useGetPondCardData(props: UseGetPondCardDataProps): UseGetPondCardDataReturn {
  const { farmId, ...rest } = props;

  const lang = useAppSelector((state) => state.app.lang);
  const user = useAppSelector((state) => state.auth.user);
  const { calculateAdminFinancials } = useCalculateAdminFinancials({ farmId });

  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};

  return getPondCardData({
    calculateAdminFinancials,
    lang,
    unitsConfig,
    ...rest
  });
}

export function useCalculatePondCardData({ farmId }: { farmId: string }): {
  calculatePondCardData: (params: Omit<UseGetPondCardDataProps, 'farmId'>) => UseGetPondCardDataReturn;
} {
  const lang = useAppSelector((state) => state.app.lang);
  const user = useAppSelector((state) => state.auth.user);
  const { calculateAdminFinancials } = useCalculateAdminFinancials({ farmId });

  const { preferences } = user ?? {};
  const { unitsConfig } = preferences ?? {};

  const calculatePondCardData = (params: Omit<UseGetPondCardDataProps, 'farmId'>) => {
    return getPondCardData({ ...params, lang, unitsConfig, calculateAdminFinancials });
  };

  return { calculatePondCardData };
}
