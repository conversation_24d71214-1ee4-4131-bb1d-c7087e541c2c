import { Box, List, Text } from '@chakra-ui/react';
import { getTrans } from '@i18n/get-trans';
import { useAppSelector } from '@redux/hooks';
import { formatNumber } from '@utils/number';
import { ReactNode, useState } from 'react';
import { getWeeklyGrowthInfo } from '@components/growth-weekly-tooltip/helpers';
import { HoverCardArrow, HoverCardContent, HoverCardRoot, HoverCardTrigger } from '@components/ui/hover-card';

export function GrowthWeeklyToolTip(props: {
  lastWeeklyGrowthDays: number;
  lastWeekGrowth: number;
  children: ReactNode;
}) {
  const { trans } = getTrans();
  const lang = useAppSelector((state) => state.app.lang);
  const { lastWeekGrowth, children, lastWeeklyGrowthDays } = props;
  const [open, setOpen] = useState(false);

  if (!lastWeekGrowth || !lastWeeklyGrowthDays) {
    return <>{children}</>;
  }

  const { weeklyGrowth, dailyGrowth, lastGrowth } = getWeeklyGrowthInfo({ lastWeeklyGrowthDays, lastWeekGrowth });
  const growth = formatNumber(lastGrowth, { fractionDigits: 2, lang });
  const daysString = lastWeeklyGrowthDays.toFixed(0);

  const lastWeeklyGrowthDaysString =
    lastWeeklyGrowthDays === 1
      ? trans('t_growth_in', { growth, daysString: `${daysString} ${trans('t_day')}` })
      : trans('t_growth_in', { growth, daysString: `${daysString} ${trans('t_days')}` });

  return (
    <HoverCardRoot
      open={open}
      onOpenChange={(value) => setOpen(value.open)}
      positioning={{ placement: 'bottom', offset: { mainAxis: -15 } }}
      lazyMount
      unmountOnExit
    >
      <HoverCardTrigger asChild>
        <Box
          onMouseOver={() => {
            setOpen(true);
          }}
          onMouseLeave={() => {
            setOpen(false);
          }}
          textAlign='center'
        >
          {children}
        </Box>
      </HoverCardTrigger>

      <HoverCardContent overflow='initial'>
        <HoverCardArrow />

        <Text fontWeight='bold'>{trans('t_growth_rate_since_monitoring')}</Text>
        <List.Root ps='md' mb='md'>
          <List.Item>{lastWeeklyGrowthDaysString}</List.Item>
          <List.Item>
            {trans('t_growth_day', {
              dailyGrowth: formatNumber(dailyGrowth, { fractionDigits: 2, lang })
            })}
          </List.Item>
          <List.Item>
            {trans('t_weekly_growth_seven_days', {
              lastWeekGrowth: formatNumber(weeklyGrowth, { fractionDigits: 2, lang })
            })}
          </List.Item>
        </List.Root>
      </HoverCardContent>
    </HoverCardRoot>
  );
}
