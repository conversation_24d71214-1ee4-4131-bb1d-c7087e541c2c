import React, { ReactNode } from 'react';
import { Box, BoxProps, Table } from '@chakra-ui/react';
import { TableContainer } from '@components/ui/table-container';

interface TableRootContainerProps extends BoxProps {
  children: ReactNode;
  firstColumnWidth: number;
  hasShadow?: boolean;
}
export function TableRootContainer(props: TableRootContainerProps) {
  const { children, firstColumnWidth, hasShadow = true, ...rest } = props;
  return (
    <Box pos='relative'>
      <TableContainer
        overflowY='auto'
        rounded='2xl'
        maxH={{ base: 'calc(100vh - 370px)', sm: 'calc(100vh - 325px)', md: 'calc(100vh - 265px)' }}
        minH='225px'
        _before={
          hasShadow
            ? {
                top: 0,
                left: 0,
                zIndex: 3,
                content: '""',
                height: '100%',
                position: 'absolute',
                boxShadow: 'elevation.200',
                borderTopLeftRadius: '2xl',
                borderBottomLeftRadius: '2xl',
                width: `${firstColumnWidth}px`,
                pointerEvents: 'none'
              }
            : {}
        }
        {...rest}
      >
        <Table.Root
          backgroundColor='transparent'
          borderSpacing='0'
          borderCollapse='separate'
          css={{
            whiteSpace: 'nowrap',
            '& td': {
              py: 'sm-alt',
              backgroundColor: 'white'
            },
            '& thead': { top: 0, zIndex: 2, position: 'sticky' },
            '& th': { py: 'sm', backgroundColor: 'squidInkPowder.100' },
            '& tr:hover td': { backgroundColor: 'squidInkPowder.100' },
            '& tr:last-child td': {
              borderBottom: 'none',
              '&:first-child': {
                borderLeft: 'none'
              }
            },
            '& td, th': {
              px: 'sm-alt',
              border: 'none',
              borderRight: '0.5px solid',
              borderBottom: '0.5px solid',
              borderRightColor: 'gray.400',
              borderBottomColor: 'gray.400',
              '&:nth-last-of-type(2)': { borderRight: 0 },
              '&:not([style*="position: sticky"])': { left: 0, position: 'relative' },
              '&:last-child': {
                borderRight: 'none',
                borderLeft: '0.5px solid',
                borderLeftColor: 'gray.400'
              }
            }
          }}
        >
          {children}
        </Table.Root>
      </TableContainer>
    </Box>
  );
}
