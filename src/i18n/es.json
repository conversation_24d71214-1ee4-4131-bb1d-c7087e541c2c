{"t_home": "<PERSON><PERSON>o", "t_accept": "Aceptar", "t_access": "Acceso", "t_account_details": "Detalles de la cuenta", "t_active": "Activo", "t_add": "Agregar", "t_add_a_farm": "Agregar una finca", "t_add_farm": "Agregar finca", "t_add_pond": "Nueva piscina", "t_add_user": "Agregar usario", "t_admin": "Administrador (a)", "t_user": "Usuario", "t_operations_supervisor": "Supervisor de operaciones", "t_operations_supervisor_can_see": "Supervisor de operaciones (puede ver datos financieros)", "t_production_supervisor": "Supervisor de operaciones (no puede ver datos financieros)", "t_already_have_an_account": "Ya tienes una cuenta", "t_and": "y", "t_animals": "<PERSON>es", "t_archive_pond": "Archivar la piscina", "t_back_to_log_in": "Regresar a inicio de sesión", "t_bulk_invite": "Invitación masiva", "t_by_clicking_create_account": "Al hacer clic en '<PERSON>rear cuenta', aceptas nuestros", "t_cancel": "<PERSON><PERSON><PERSON>", "t_cancel_invitation": "Cancelar invitación", "t_change_password": "Cambiar contraseña", "t_close": "<PERSON><PERSON><PERSON>", "t_code": "Código", "t_confirm": "Confirmar", "t_confirm_archive": "¿Está seguro de que desea archivar?", "t_confirm_new_password": "Confirmar nueva contraseña", "t_confirm_password": "Confirmar con<PERSON>", "t_contact_phone_no": "Teléfono de contacto", "t_continue": "<PERSON><PERSON><PERSON><PERSON>", "t_create": "<PERSON><PERSON><PERSON>", "t_react_select_create_label": "<PERSON>rear \"{{value}}\"", "t_create_account": "<PERSON><PERSON><PERSON> una cuenta", "t_create_invitation": "Crear invitación", "t_created_at": "Creado en", "t_cv": "CV", "t_cycle": "<PERSON><PERSON><PERSON>", "t_cycle_number": "Número de ciclo", "t_date": "<PERSON><PERSON>", "t_day": "día", "t_days": "días", "t_days_2": "Días", "t_hours": "<PERSON><PERSON>", "t_minutes": "<PERSON><PERSON><PERSON>", "t_count_days_of_culture": "{{count}} días de cultivo", "t_did_not_receive_code": "¿No ha recibido un código?", "t_distribution": "Distribución del peso", "t_edit_access": "Editar acceso", "t_edit_pond": "<PERSON><PERSON> piscina", "t_edit_nursery": "<PERSON><PERSON>", "t_edit_stocking": "Editar siembra", "t_email": "Correo Electrónico", "t_enter_the_code_here": "Introduzca el código aquií", "t_farm": "Finca", "t_female": "Femenino", "t_finished_at": "Terminado en", "t_forgot_password": "¿Se te olvidó tu contraseña", "t_forgot_password_description": "Introduzca la dirección de correo electrónico asociada a su cuenta y le enviaremos un enlace para restablecer su contraseña", "t_gender": "<PERSON><PERSON><PERSON>", "t_ha": "ha", "t_harvest": "Cosecha", "t_harvest_date": "<PERSON><PERSON>", "t_id": "ID", "t_in_process": "En proceso", "t_invitations": "Invitaciones", "t_invite_new_user": "Invitar a un nuevo usuario", "t_invited": "<PERSON><PERSON><PERSON><PERSON>", "t_invited_at": "Invitado en", "t_invited_date_time": "Invitado {{dateTime}}", "t_is_active": "¿Está activo?", "t_join_via_invitation": "Unirse a través de una invitación", "t_last_active": "ÚLTIMA ACTIVIDAD", "t_language": "Idioma", "t_loading": "Cargando", "t_log_in": "In<PERSON><PERSON>", "t_manage_preferences": "Para gestionar otras preferencias de correo electrónico, visite su", "t_preferences_updated_successfully": "Sus preferencias han sido actualizadas exitosamente", "t_email_settings": "Configuración de correo electrónico", "t_unsubscribe_message": "Usted ha sido dado de baja con éxito. Ya no recibirá correos electrónicos de este tipo.", "t_log_in_to_your_account": "Acceda a su cuenta", "t_login_password": "Contraseña de inicio de sesión", "t_logout": "<PERSON><PERSON><PERSON>", "t_male": "<PERSON><PERSON><PERSON><PERSON>", "t_manage_monitoring": "Ad<PERSON><PERSON><PERSON>", "t_message": "Men<PERSON><PERSON>", "t_monitoring": "Monitoreo", "t_monitorings": "Muestreos", "t_my_account": "Mi Cuenta", "t_name": "Nombre", "t_new_password": "Nueva contraseña", "t_new_pond": "Nueva Piscina", "t_new_user": "Nuevo usuario", "t_no_data_to_display": "No hay información para mostrar", "t_no_farms": "Sin Fincas", "t_no_invitations": "Sin Invitaciones", "t_no_payments": "No hay pagos", "t_plan": "Plan", "t_provider": "<PERSON><PERSON><PERSON><PERSON>", "t_payment_history": "Historial de pagos", "t_no_monitoring_available": "No hay muestreos disponibles", "t_not_active": "No activo", "t_not_have_account": "¿Quiere saber más sobre la plataforma Kampi?", "t_contact_us": "Contacto", "t_not_receive_the_code": "Si no recibiste el código", "t_old_password": "Contraseña anterior", "t_oops": "Ups", "t_optional": "Opcional", "t_or": "o", "t_other": "<PERSON><PERSON>", "t_password": "Contraseña", "t_phone_number": "Número de teléfono", "t_photo_count": "Foto {{count}}", "t_photos": "Fotos", "t_pond": "Piscina", "t_pond_name_validation_msg": "<PERSON><PERSON><PERSON><PERSON>, letras y - , 15 caracteres como máximo", "t_nursery_name_validation_msg": "<PERSON><PERSON><PERSON><PERSON>, letras y -, 15 caracteres máx", "t_pond_size": "Tamaño de la piscina", "t_past_cycle_summary": "Resumen del ciclo pasado", "t_abw_at_transfer": "Peso promedio en la transferencia", "t_privacy_policy": "Política de privacidad", "t_processing_status": "Estado de procesamiento", "t_quantity": "Cantidad", "t_received_invitation": "Has sido invitado a unirte a", "t_invited_to_farm": "Finca por usuario", "t_reject": "<PERSON><PERSON><PERSON>", "t_resend_code": "Reenviar codigo", "t_reset_code": "Restablecer <PERSON>", "t_reset_password": "Restablecer la contraseña", "t_reset_password_description": "Le hemos enviado un correo electrónico con su código de restablecimiento de contraseña", "t_role": "Rol", "t_save": "Guardar", "t_search_by_user_farm_or_status": "Buscar por usuario, finca o estado", "t_select_existing_monitoring": "Seleccionar muestreo existente", "t_select_existing_or_enter_new_email": "Seleccione existente o ingrese un nuevo correo electrónico", "t_select_farm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "t_select_file": "Seleccione Archivo", "t_select_role": "Seleccionar rol", "t_send": "Enviar", "t_sign_up_instantly": "Regístrese al instante", "t_start_new_monitoring": "Comenzar nuevo Muestreo", "t_status": "Estado", "t_stock": "Siembra", "t_stock_pond": "Siembra", "t_stocking": "Siembra", "t_submit": "Enviar", "t_submit_monitoring": "Enviar muestreo", "t_submitted": "Enviado", "t_supervisor_optional": "Supervisor (opc)", "t_terms_of_service": "Términos de servicio", "t_today": "Hoy", "t_total": "Total", "t_total_images": "Total de fotos", "t_trays": "Contenedores", "t_type_of_access": "Tipo de acceso", "t_unknown": "Desconocido", "t_update": "Actualizar", "t_uploaded_at": "<PERSON><PERSON> en", "t_delete": "Bo<PERSON>r", "t_verification_code": "Código de verificación", "t_verify_email": "Verificar correo electrónico", "t_verify_email_description": "Le hemos enviado un correo electrónico con su enlace de verificación.", "t_verify_your_email": "Verifique su correo electrónico", "t_verify_your_email_address": "Verifique su dirección de correo electrónico", "t_view_images": "<PERSON><PERSON> imágene<PERSON>", "t_weight_distribution": "Distribución del peso", "t_yesterday": "Ayer", "t_email_sent_successfully_check": "El correo electrónico se envió con éxito, por favor revise su correo electrónico", "t_sms_sent_successfully_check_mobile": "El SMS se envió con éxito, verifique su teléfono", "txt_email_schema_error": "El correo electrónico debe tener al menos 7 caracteres (por ejemplo, <EMAIL>)", "txt_password_mismatch": "Contraseña y Confirmar contraseña no coinciden", "txt_passwords_reset_does_not_match": "Nueva contraseña y Confirmar nueva contraseña no coinciden", "yup_email_is_invalid": "El correo electrónico es invalido", "yup_enter_minimum_characters": "Por favor ingrese mínimo {{min}} letras", "yup_is_invalid": "{{label}} es inválida", "yup_is_required": "{{label}} es un campo obligatorio", "yup_not_type": "<PERSON><PERSON><PERSON> in<PERSON>, {{label}} debe ser un tipo '{{type}}'", "yup_should_be_greater_than_more": "{{label}} debe ser mayor que {{more}}", "yup_should_be_greater_than_zero": "{{label}} debe ser mayor que 0", "yup_should_be_one_of": "{{label}} debe ser uno de {{values}}", "yup_should_not_be_less_than": "{{label}} no debe ser inferior a {{min}}", "yup_should_be_less_than_or_equal": "{{label}} debe ser menor o igual {{max}}", "yup_should_be_integer": "{{label}} phải là một số nguyên", "yup_date_must_not_be_later_than": "{{label}} no debe ser posterior a {{date}}", "yup_date_must_not_be_earlier_than": "{{label}} no debe ser anterior a {{date}}", "t_heads_on": "<PERSON><PERSON>", "t_heads_off": "Cola", "t_download": "<PERSON><PERSON><PERSON>", "t_days_ago_plural": "Hace {{count}} días", "go_back": "Regresar", "t_something_went_wrong": "Algo salió mal", "t_actions": "Acciones", "t_data_from_previous_cycle": "Está viendo un ciclo pasado, vuelva a sembrar su piscina para comenzar a monitorear nuevamente.", "t_err_account_not_found": "No hay ninguna cuenta vinculada a ese correo electrónico", "t_err_code_is_invalid": "El código no es válido", "t_err_email_or_password_incorrect": "Correo electrónico o la contraseña son incorrectos", "t_err_user_not_found": "No se pudo encontrar el usuario", "t_err_email_already_in_use": "Correo electrónico ya está en uso", "t_err_unauthorized": "No autorizado", "t_err_pond_not_found": "Piscina no encontrada", "t_stocking_date": "Siembra", "t_processing": "Procesando", "t_processing_failed": "El proceso ha fallado", "t_delete_monitoring": "Excluir este muestreo", "t_delete_monitoring_msg": "Si excluye este monitoreo, no será incluido en la data diaria y no se usará para alertas o proyecciones. ", "t_delete_monitoring_toast_title": "Muestreo excluido", "t_delete_monitoring_toast_msg": "Su muestreo fue excluido", "t_error": "Error", "t_page_not_found": "Página no encontrada", "t_go_home": "Ir al inicio", "t_success": "Éxito", "t_verification_failed": "La verificación ha fallado", "t_login_to_accept_invite": "Necesitas iniciar sesión para aceptar la invitación", "t_code_sent": "El código se envió correctamente, consulte su teléfono", "t_accepted_invitation": "Invitación aceptada con éxito", "t_account_verified": "Ha verificado su cuenta con éxito", "t_logged_in_success_title": "Inicio de sesión con éxito", "t_logged_in_success_msg": "Incio de sessión correcto", "t_profile_updated": "Ha actualizado correctamente su perfil", "t_updated_successfully": "Actualizado correctamente", "t_invitation_email_sent": "Se ha enviado el correo electrónico de invitación", "t_user_membership_updated": "La membresía se ha actualizado con éxito", "t_invitation_rejected": "Invitación rechazada", "t_created_successfully": "Creada con éxito", "t_invitation_created": "Invitación creada con éxito", "t_password_changed": "Contraseña cambiada con éxito", "t_password_reset_success": "Has restablecido correctamente tu contraseña y ha iniciado sesión", "t_invitation_cancelled_title": "Invitación cancelada", "t_invitation_cancelled_msg": "Invitación cancelada", "t_phone_number_updated": "Teléfono actualizado", "t_email_updated": "Correo electrónico actualizado con éxito", "t_monitoring_submitted": "Muestra enviada con éxito", "t_manual_monitoring_created": "Monitoreo manual creado exitosamente", "t_logged_in_success": "Incio de sessión correcto", "t_change_your_email": "Cambiar email", "t_contact_admin_to_change_email": "Por favor comuníquese con el administrador de su finca para cambiar su correo electrónico", "t_farm_must_be_unique": "El nombre de la finca debe ser único", "t_not_invite_yourself": "No puedes invitarte a ti mismo", "t_percentage_sampled_animals": "{{percent}} de la muestra se encuentra en un rango de {{range}}, distribuidos en {{grams}} tamaños", "t_aggregation_in_progress": "Agregación en curso, vuelva a comprobarlo en 20 segundos", "t_get_history_and_growth": "Actualizar Crecimiento - DEV", "t_percentage_of_stocked_animals": "% de animales sembrados", "t_select_or_create_monitoring": "Por favor, seleccione una supervisión existente o cree una desde arriba", "t_cv_learning": "El Coeficiente de Variación (CV) mide la dispersión de tamaños presentes en la piscina y ayuda a determinar si los camarones se alimentan de manera uniforme.", "t_cv_description_high": "Cuando el CV está alto – tiene una gran variación de tallas en la piscina, lo que indica que la pisina ha sido alimentada de manera no uniforme.", "t_cv_description_low": "CV bajo: Menos variación de tamaños, más homogeneidad en piscina. Su estrategia de alimentación funciona bien", "t_cv_fixing_distribution": "Detección y manejo de distribución alta", "t_cv_xpertsea_alert": "Kampi le avisará cuando el CV de una piscina sea alto o muy alto para su peso promedio.", "t_then_you_can": "Le recomendamos:", "t_cv_review_pond_histogram": "Revise el histograma de la piscina para entender mejor la situación", "t_cv_consider_following_our_tips": "Considere seguir nuestros consejos para mejorar su distribución", "t_cv_tips_to_improve": "Consejos para mejorar distribución", "t_cv_how_cv_calculated": "Cómo es calculado el CV", "t_cv_how_determined": "Cómo determinamos si el CV es alto", "t_cv_select_the_average_body_weight": "Seleccione el peso corporal medio ", "t_at": "Con", "t_after": "Después de los", "t_consider_the_following": "Considere hacer lo siguiente", "t_cv_05_description": "es el momento más rentable para optimizar su distribución, lo que le permite actuar con anticipación y maximizar su supervivencia.", "t_cv_10_description": "sigue siendo relativamente barato para asegurar una buena distribución, que luego prepara el estanque para un fuerte ciclo de crecimiento.", "t_cv_15_description": "optimice el crecimiento alimentando considerando los tamaños de los camarones de su piscina.", "t_cv_15_plus_description": "puede reducir la dispersión de tamaños con 2-3 semanas de alimentación enfocada, mientras se prepara para la cosecha.", "t_cv_step_analyze": "Analice el histograma de la piscina en Kampi para ver cuántos camarones son muy pequeños.", "t_cv_step_analyze_to_understand": "Analice el histograma de la piscina en Kampi para comprender la distribución en su piscina. ", "t_cv_step_encourage": "Alimente más los camarones más pequeños usando los tamaños de pellet correctos. Use el histograma para determinar la cantidad correcta para cada tamaño de pellet.", "t_cv_step_encourage_then": "Incentive a los camarones más pequeños para que coman más usando la talla correcta de pellet. Si los camarones no están cerca de los alimentadores automáticos, hágalo en áreas donde normalmente se encuentran.", "t_cv_step_shrimp_far": "Si los camarones más pequeños están lejos de los comederos automáticos, atráigalos con la alimentación al voleo.", "t_cv_step_smaller_shrimp_far": "Si los camarones más pequeños están lejos de los alimentadores automáticos, atráigalos hacia las zonas de comederos automáticos con la alimentación manual.", "t_cv_step_ensure": "Asegúrese de que hay suficientes alimentadores automáticos en la piscina, que están colocados de forma óptima y que funcionan correctamente.", "t_cv_step_feed_away": "Alimente lejos de zonas con fondos poco saludables y mantenga el fondo de la piscina limpio de materia orgánica.", "t_cv_step_collect": "Recoja muestras más grandes durante el seguimiento (>300 camarones) para aumentar su confianza en las tallas que venderá al procesador.", "t_cv_step_focus": "Considere también la alimentación de los camarones más pequeños, centrándose en las zonas de la piscina donde se encuentran.", "t_cv_step_use": "Realice un raleo para tener más espacio y favorecer el crecimiento.", "t_cv_calculation_description": "El cálculo del CV comienza con el cálculo de la desviación estándar de una muestra.", "t_cv_calculation_description_long": "La desviación estándar (SD) se determina calculando primero las diferencias entre el peso de cada camarón y el peso promedio de todos los camarones. Estas diferencias se convierten en un solo número, la SD, que representa la variación de estos valores.", "t_cv_calculation_description_short": "A continuación, se calcula el CV dividiendo la SD por el peso promedio multiplicado por 100. Esto nos permite comparar el CV del peso promedio de los camarones de una piscina.", "t_cv_calculation_examples": "Ejemplos en camarones de 9g:", "t_cv_low": "CV 10%", "t_cv_sd_low": "0.9 SD", "t_cv_medium": "CV 20%", "t_cv_sd_medium": "1.8 SD", "t_cv_high": "CV 28 %", "t_cv_sd_high": "2.5 SD", "t_cv_high_description": "Kampi ha analizado más de 4.5 millones de imágenes de camarones para determinar cuáles son los valores típicos de CV durante el ciclo de crecimiento del camarón.", "t_cv_high_description_long": "En Kampi comparamos el CV de su monitoreo con estos datos anteriores  y lo alertamos si es Alto o Muy Alto:", "t_cv_high_yellow": "Alto (amarillo)", "t_cv_high_yellow_desc": "- El resultado es superior al 51% de sus muestras anteriores para el mismo peso promedio", "t_cv_very_high_red": "<PERSON><PERSON> (roja)", "t_cv_very_high_red_desc": "- Es superior al 85% de nuestras muestras anteriores para el mismo peso promedio.", "t_unharvest_pond": "Unharvested pond (DEV)", "t_dispersion": "Dispersión", "t_target_weight": "Peso objetivo", "t_days_of_production": "Días de producción", "t_target_harvest_date": "<PERSON>cha prevista de cosecha", "t_set_target": "Establecer objetivo", "t_show_users_all_farms": "Mostrar usuarios de todas las fincas", "t_edit": "<PERSON><PERSON>", "t_try_again": "Intentar otra vez", "t_target_at_risk": "Objetivo en riesgo", "t_remove": "Remover", "t_confirm_remove_target": "¿Estás seguro de que quieres eliminar el objetivo de esta piscina?", "t_set_later": "<PERSON><PERSON>lo más tarde", "t_needs_attention": "Necesita atención", "t_select_date": "Seleccione fecha", "t_average_weight_should_be_less_than_first_monitoring_average_weight": "Peso promedio debería ser inferior a {{firstMonitoringAverageWeight}}", "t_growth_rate_since_monitoring": "Tasa de crecimiento desde el último muestreo", "t_growth_in": "{{growth}}g de crecimiento en {{daysString}}", "t_growth_day": "Crecimiento / día = {{dailyGrowth}} g", "t_weekly_growth_seven_days": "Tasa de crecimiento semanal calculada = {{lastWeekGrowth}} g", "t_min_monitored_weight": "Debe ser mayor que el último peso controlado ({{weight}} g)", "t_min_target_weight": "Debe tener al menos {{peso}} g", "t_max_target_weight": "Debe ser inferior a {{weight}} g", "t_min_target_stocking_days": "Debe ser mayor que los días transcurridos desde el almacenamiento ({{days}} días)", "t_min_target_days": "Debe ser de al menos {{días}} días", "t_integer_target_days": "No puede tener un decimal (por e<PERSON><PERSON><PERSON>, 90)", "t_growth_warning_prefix": "Este objetivo requerirá una tasa media de crecimiento del", "t_growth_warning_rate": "{{rate}} g.", "t_growth_warning_suffix": "Edite su objetivo o confirme para continuar.", "t_target_harvest_date_updated": "La fecha de cosecha ha sido actualizada para quedar <b>{{days}}</b> días después de su nueva fecha de siembra.", "t_target_harvest_date_updated_confirm": "Confirme o edite el objetivo y presione Actualizar", "t_set_survival_rate": "Ingresar", "t_survival_form_title": "Supervivencia - {{date}}", "t_enter_survival": "Ingresar supervivencia", "t_edit_survival": "Editar supervivencia", "t_survival_final_harvest": "Supervivencia - Cosecha final", "t_survival": "Supervivencia", "t_kampi_survival": "Supervivencia Kampi", "t_biomass": "Biomasa", "t_survival_rate_tooltip": "Ingrese la supervivencia cada día que monitoree para obtener el cálculo más preciso de la biomasa.", "t_biomass_tooltip": "La biomasa se muestra después de ingresar la supervivencia. Se calcula así: Biomasa = (Número de PLs al momento de la siembra) × (Peso promedio) × (Supervivencia)", "t_stocking_weight_g": "Peso de siembra (g)", "t_processing_failed_tooltip_title": "Sus fotografías se han subido pero el proceso ha fallado.", "t_processing_failed_tooltip_subtitle": "Seleccione Intente de nuevo. Si todavía no se procesa correctamente, contacte a su asesor Kampi.", "t_pallet_size": "<PERSON><PERSON><PERSON> ", "t_practical_analysis_error": "No debería haber valores de peso o tamaño de pellet duplicados", "t_practical_modal_header": "Introduzca a qué pesos aumenta la granulometría en su finca.", "t_practical_analysis_tooltip_header": "Análisis de partículas en el alimento", "t_practical_analysis_tooltip_desc": "Ingrese el peso en el cual se cambia el tamaño de partícula para establecer la biomasa correspondiente a cada tamaño de partícula", "t_cancel_save_changes": "Cancelar los cambios", "t_cancel_save_changes_confirmation": "Está seguro que no quiere guardar los cambios?", "t_weight_error": "El peso debería de ser mayor al peso anterior", "t_weight_g": "Peso (g)", "t_confirm_remove_feed_pallets": "Confirma la eliminación del tamaño de pellet para todas las piscinas de su finca?", "t_invalid_weight": "El peso promedio está >40g o <2g. Estamos constantemente trabajando para ampliar los pesos compatibles, pero en este momento no podemos proporcionar resultados cuando el peso promedio está >40g o <2g.", "t_growth_ahead": "Crecimiento supera el crecimiento objetivo", "t_on_track_to_hit_target": "En camino a alcanzar el objetivo", "t_check_stock": "Validar información de siembra", "t_check_sample": "Validar la muestra", "t_feed_type": "Tipo de alimento", "t_last_updated_on": "Actualizado el", "t_cost": "Costo", "t_cost_$": "Costo ($)", "t_kg": "kg", "t_select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "t_size": "<PERSON><PERSON><PERSON>", "t_daily_mortality": "Mortalidad diaria", "t_abw": "Peso Promedio", "t_abw_g": "PP (g)", "t_no_field_duplicated_values": "Asegúrese de que cada  {{field}} sea único", "t_planned": "Plan", "t_left": "Iz<PERSON>erda", "t_right": "Derecha", "t_save_changes": "Guardar cambios", "t_select_option": "Seleccionar opción", "t_no_data_available": "No hay datos disponibles", "t_monday": "<PERSON><PERSON>", "t_tuesday": "<PERSON><PERSON>", "t_wednesday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "t_thursday": "<PERSON><PERSON>", "t_friday": "Viernes", "t_saturday": "Sábado", "t_sunday": "Domingo", "t_no_options": "No hay opciones", "t_fcr": "FCA", "t_setting_pond_bottom_msg": "Comience monitoreando su piscina", "t_pond_monitoring_after_harvest": "El último monitoreo fue posterior a la fecha planificada de cosecha. Actualice su plan de cosecha ahora para recibir proyecciones", "t_required": "*Requerido", "t_normal": "Normal", "t_err_file_size_limit_exceeded": "Excede el límite de tamaño de archivo", "t_symmetry": "Simetría", "t_symmetry_left_title": "Animales clasificados en tallas inferiores", "t_symmetry_left_info": "¡Mejore su cosecha en ${{revenueLoss}}/lb alimentando para corregir la asimetría! El {{animalsPercentBelowABW}}% de los animales están por debajo del peso promedio, y el {{animalsPercentBelowHeuristic}}% están más de {{heuristicGramsBelowABW}} gramos por debajo de su peso promedio, lo que reduce la clasificación de su cosecha.", "t_symmetry_right_title": "Los animales pequeños están afectando negativamente el FCA", "t_symmetry_right_info": "El {{animalsPercentBelowHeuristic}}% de los animales están más de {{heuristicGramsBelowABW}} gramos por debajo del peso promedio. Enfóquese en hacer crecer estos animales para mejorar el peso promedio y el FCA de la piscina.", "t_symmetry_normal_info": "Los animales están distribuidos de manera uniforme alrededor del promedio", "t_symmetry_few_animals_note": "El tamaño de muestra es demasiado pequeño, aumente el número de animales muestreados, usando al menos 150 camarones, para aumentar la confianza en las estadísticas de distribución", "t_g_in_days": "{{growth}} g/{{days}}d", "t_record_partial_harvest": "Registrar raleo", "t_plan_partial_harvest": "Planificar raleo", "t_biomass_harvested_unit_ha": "Biomasa - Cosechada ({{unit}}/ha)", "t_biomass_harvested": "Biomasa - Cosechada", "t_harvest_plan": "Plan de cosecha", "t_estimated_animals_harvested": "Estimación de animales cosechados", "t_animals_harvested_%": "Animales co<PERSON> (%)", "t_partial_harvest_quantity_error": "La cantidad de animales cosechados debe ser menor que el total de animales y no puede ser cero", "t_add_notes": "Ag<PERSON>gar notas", "t_start_monitoring": "Comenzar monitoreo", "t_edit_manual_monitoring": "Editar monitoreo manual", "t_show_me_how": "Muéstrame c<PERSON> ", "t_back": "Atrás", "t_head_on": "<PERSON><PERSON>", "t_headless": "Cola", "t_configuration": "Configuración", "t_search": "Buscar", "t_survival_%_after_partial_harvest": "% de supervivencia después del raleo", "t_survival_will_be_assigned_to": "La supervivencia se asignará al monitoreo del {{fecha}}", "t_survival_will_not_be_assigned_to_monitoring": "La supervivencia no se asignará al monitoreo - No hay monitoreos antes del {{fecha}}", "t_partial_harvest": "<PERSON><PERSON>", "t_x_harvested_on": "{{percent}}% cosechado el {{date}}", "t_ensure_survival_included": "Asegúrese de que la supervivencia incluye los raleos", "t_ensure_survival_included_tooltip": "P.ej. en la supervivencia ingresada, ya se ha descontado el % correspondiente al raleo. <PERSON><PERSON> decir, animales raleados = animales muertos", "t_growth_rate": "Tasa de crecimiento", "t_revenue": "Ingreso ($)", "t_users_at_farm": "Usuarios en {{farmName}}", "t_users_at_all_farms": "Usuarios en todas las granjas", "t_stock_your_pond_to_start_getting": "Siembre su piscina para empezar a recibir información de Kampi", "t_stock_your_nursery_to_start_getting": "Siembre su precriadero para empezar a recibir información de Kampi", "t_pond_actions": "Acciones de la piscina", "t_edit_pond_details": "Editar de<PERSON> de la piscina", "t_pond_archived_successfully": "Piscina archivada exitosamente ", "t_present": "Presentar", "t_projection_variables": "Variables de proyección", "t_profit_include_dry_days_ha_day": "Utilidad - Incluye días secos ($/ha/día)", "t_partial_harvest_revenue": "Ingresos del raleo", "t_add_price_list": "Agregar lista de precio", "t_empty_cycle": "<PERSON><PERSON><PERSON> vacio", "t_dry_days": "Días secos", "t_days_in_nursery": "Días en el precriadero", "t_nursery_survival": "Supervivencia en el precriadero", "t_larva_order_time": "Tiempo de pedido de larvas", "t_price_list": "Lista de precios", "t_pond_cost": "Costo de la piscina", "t_min_allowed_stocked": "La fecha de siembra no puede tener más de 2 años de antigüedad", "t_profit": "Utilidad ($)", "t_rofi": "Retorno sobre la inversión en alimento (%)", "t_last_updated_on_by": "Última actualización {{date}} por {{name}}", "t_add_lab_name": "Agregar nombre del laboratorio", "t_monitoring_updated_successfully": "Monitoreo actualizado exitosamente", "t_production": "Producción", "t_number_of_images": "Número de imágenes", "t_buy_images": "Compre {{sliderValue}} imágenes ({{totalAmount}} no incluye impuestos)", "t_use_slider_or_input_number_of_images": "Utilice el control deslizante o ingrese manualmente la cantidad de imágenes que desea comprar", "t_weight_stocked_on_date": "{{weight}}g sembrados el {{date}}", "t_revenue_per_pound": "Ingreso ($/lb)", "t_revenue_per_kg": "Ingreso ($/kg)", "t_no_lab_names": "No hay nombres de laboratorio", "t_no_genetic_names": "No hay nombres genéticos", "t_my_team": "Mi equipo", "t_planned_harvest": "Cosecha planificada", "t_recorded_harvest": "Registrar cosecha", "t_no_growth_data": "No tenemos datos de crecimiento para este piscina", "t_enter_weekly_growth_data": "Ingrese datos de crecimiento semanal", "t_enter_growth_data_or_monitor_pond": "¡Ingrese sus datos de crecimiento semanal o monitoree su piscina para ver resultados de las ganancias!", "t_no_past_cycles": "No hay ciclos pasados", "t_your_pond_is_empty": "Su piscina está vacía actualmente ", "t_stock_pond_to_get_started": "Siembre su piscina ahora para comenzar", "t_stock_your_pond": "Se<PERSON><PERSON> piscina", "t_last_monitoring": "<PERSON>ltimo <PERSON>", "t_latest_monitoring": "<PERSON>ltimo <PERSON>", "t_current_biomass": "Biomasa actual", "t_planned_final_harvest": "Cosecha final planificada", "t_projected_profit": "Utilidad proyectada ($)", "t_projected_revenue": "Ingreso proyectado ($)", "t_cost_to_date_estimated": "Costo hasta la fecha (estimado)", "t_feed_given_daily_kg_ha": "Alimento suministrado - Diario (kg/ha)", "t_nurseries": "Precriaderos", "t_harvest_weight_g": "Peso de cosecha (g)", "t_cycle_length": "Duración del ciclo", "t_add_liquidation_report": "Agregar reporte de liquidación", "t_liquidation_report_format": "Formato: aceptamos archivos .xlsx, .csv o .pdf, máximo 5 MB", "t_record_harvest": "Registrar cosecha", "t_custom": "Personalizado", "t_cycle_info": "Información de ciclo", "t_add_new_size": "Agregar nuevo tamaño", "t_no_ponds_in_your_farm": "No hay piscinas en su finca", "t_no_nurseries_in_your_farm": "No hay precriaderos en su finca", "t_create_a_new_pond": "¡<PERSON>ree una piscina nueva ahora para comenzar a configurar su finca!", "t_create_a_new_nursery": "Cree un nuevo precriadero ahora!", "t_end_of_cycle": "Fin del ciclo", "t_hint_you_can_copy_and_paste_from_excel": "Pista: puede copiar y pegar su tabla de Excel aquí", "t_add_weight": "Agregar peso", "t_overhead_cost_tooltip": "El costo indirecto de su finca, incluyendo el alquiler, costos de construcción, la mano de obra y la depreciación del equipo.", "t_gram_g": "g", "t_max_x": "Max {{max}}", "t_min_x": "Min {{min}}", "t_fcr_tooltip": "El alimento restante de este ciclo se estimará basado en su Factor de Conversión Alimenticia (FCA) actual y el FCA predicho por el algoritmo de Kampi.", "t_kg_ha_tooltip": "La cantidad de alimento suministrado diariamente en kilogramos (kg) por hectárea (ha) de la piscina para el resto del ciclo.", "t_field_should_be_less_or_equal_to_x": "{{field}} debe ser menor o igual que {{max}}", "t_count_fcr": "{{count}} FCA", "t_count_days": "{{count}} días", "t_count_day": "{{count}} día", "t_cost_per_pound": "Costo ($/lb)", "t_cost_per_kg": "Costo ($/kg)", "t_update_data": "Actualizar los datos", "t_weekly_programming": "Planeación semanal", "t_feeding_day": "Día de reporte de alimentación", "t_feeding_day_tooltip": "El día que inicia su semana para planificación de la alimentación", "t_monitoring_days": "Días de monitoreo", "t_manual_weight_generic_validation": "Por favor asegúrese que se hayan llenado con información precisa todas las celdas en la tabla", "t_less_than": "<PERSON>os que", "t_harvest_optimizer": "Optimizador de cosecha", "t_data_updater": "Actualizador de Datos", "t_harvest_type": "Tipo de co<PERSON>cha", "t_market": "<PERSON><PERSON><PERSON>", "t_projected_survival_must_be_future": "La supervivencia proyectada debe ser configurada en el futuro.", "t_projected_survival_must_be_lower_than_current": "La supervivencia proyectada debe ser menor que la supervivencia actual.", "t_avg_days_of_culture": "Días de cultivo promedio", "t_avg_survival": "Supervivencia promedio", "t_avg_fcr": "FCA promedio", "t_fcr_cumulative": "FCA - Acumulado", "t_farm_summary": "Resumen de finca", "t_overview": "Resumen", "t_simulation": "Simulación", "t_summary": "Resumen", "t_more_than": "<PERSON><PERSON> que", "t_x_of_culture": "{{dayString}} de cultivo", "t_selected": "{{selected}} / {{total}} seleccionado", "t_growth_g_wk": "Crecimiento lineal (g/sem)", "t_growth_g_day": "Crecimiento (g/día)", "t_growth_monitored_x_week_g_wk": "Crecimiento - Monitoreo {{weeks}} sem (g/sem)", "t_feed": "Alimento", "t_fcr_weekly": "FCA - 1 sem", "t_filter": "Filtro", "t_clear": "<PERSON><PERSON><PERSON>", "t_apply": "Aplicar", "t_group_summary": "Resumen del grupo", "t_expected_at_harvest": "Esperado al cosechar", "t_customize_view": "Personalizar la vista", "t_missing_latest_data": "Falta la última actualización de datos para el monitoreo más reciente. {{extra}}", "t_indicating_status": "Indicadores", "t_financials": "Finanzas", "t_data_shown_from_last_monitoring": "Data para visualizar del último monitoreo", "t_data_shown_based_on_current_date": "Data para visualizar basada en la fecha actual", "t_days_until_harvest": "<PERSON>ías hasta la cosecha", "t_days_of_culture": "Días de cultivo", "t_weights": "Pesos", "t_feed_given_weekly_kg": "<PERSON><PERSON> suministrado - Semanal (kg)", "t_feed_%_of_biomass": "Alimento % de Biomasa", "t_animals_in_pond_m2": "Animales - Vivos/m2", "t_animals_in_pond_ha": "Animales - Vivos/ha", "t_dispersion_cv": "Dispersión - CV", "t_yup_should_be_less_than_max": "{{label}} debería de ser menos de {{max}}", "t_config": "Configuración", "t_account": "C<PERSON><PERSON>", "t_growth_g_over_d": "{{growth}} g/d", "t_lb_over_ha": "lb/ha", "t_kg_over_ha": "kg/ ha", "t_over_m2": "/m2", "t_over_ha": "/ha", "t_total_sale_value": "Valor total de venta", "t_avg_fcr_cumulative": "FCA promedio (acumulado)", "t_avg_kg_ha_day": "Kg/ha/día prom", "t_avg_total_feed_given_kg": "Promedio del alimento total (kg)", "t_avg_total_feed_given_lb": "Promedio del alimento total (lb)", "t_biomass_lb_total": "Biomasa - Total (lb)", "t_biomass_kg_total": "Biomasa - Total (kg)", "t_biomass_lb_ha": "Biomasa (lb/ha)", "t_biomass_kg_ha": "Biomasa (kg/ha)", "t_biomass_lb_ha_day": "Biomasa lb/ha/día", "t_biomass_kg_ha_day": "Biomasa kg/ha/día", "t_financial": "Finanza", "t_avg_abw_g": "PP (g)", "t_avg_2wk_growth": "Crecimiento promedio (2 semanas)", "t_avg_weekly_growth": "Crecimiento semanal promedio", "t_over_ha_over_day": "/ ha / día", "t_kg_over_ha_over_day": "kg / ha / día", "t_g_over_wk": "g/sem", "t_lb": "lb", "t_invitation": "Invitación", "t_what_is_cv_learning": "Cual es el conocimiento CV?", "t_no_farms_found": "No se encontraron fincas", "t_no_ponds_found": "No se encontraron piscinas", "t_no_cycles_found": "No se encontraron ciclos", "t_last_week_growth": "{{lastWeekGrowth}} g/sem", "t_this_farm_data_is_outdated": "La data de la finca no está actualizada! El último monitoreo fue hace  > 7 días. Monitoree su finca ahora.", "t_some_of_your_ponds_have_not_been_monitored": "Algunas de sus piscinas no han sido monitoreadas en > 7 días. Monitoréelas ahora.", "t_some_of_your_ponds_are_missing_data": "A algunas de sus piscinas les faltan datos. {{extra}}", "t_percent_of_harvest_as_head_on": "% de cosecha como entero", "t_percent_of_head_on_as_class_a": "% de entero como clase A", "t_percent_of_head_on_as_class_b": "% de entero como clase B", "t_percent_of_harvest_as_leftover": "% de cosecha para sobrante", "t_process_leftover_as": "Procesar sobrante como", "t_rejects": "<PERSON><PERSON><PERSON>", "t_percent_of_leftover_as_class_a": "% restante como cola clase A", "t_percent_of_leftover_as_class_b": "% restante como cola clase B", "t_percent_of_tail": "% de cola", "t_percent_of_harvest_as_headless_class_a": "% de cosecha como cola clase A", "t_percent_of_harvest_as_headless_class_b": "% de cosecha como cola clase B", "t_processors": "Empacadoras", "t_production_targets": "Objetivos de producción", "t_target_production_cycle": "Ciclo de producción objetivo", "t_stocking_defaults": "Información de siembra por defecto", "t_changes_will_apply_to_all_farms": "Los cambios se aplicarán para todas las fincas del grupo:", "t_group_config_confirmation": "Los cambios que hizo se aplicarán a todas las fincas del grupo.", "t_are_you_sure_you_want_to_save_changes": "¿Está seguro de que quiere guardar los cambios?", "t_change_confirmation": "Cambiar confirmación", "t_feed_tables": "Tablas de alimentación", "t_feed_table": "Tabla de alimentación", "t_of_biomass_to_feed": "% de biomasa para alimentar", "t_select_your_default_feed_table": "Seleccione su tabla de alimentación predeterminada", "t_add_feed_table": "Agregar tabla de alimentación", "t_duplicated_table_name": "El nombre de la tabla ya existe", "t_default": "Por defecto", "t_avg_stocking_weight_g": "Peso de siembra prom. (g)", "t_autofeeders": "Alimentadores automáticos", "t_no_autofeeders": "No hay alimentadores automáticos", "t_add_autofeeder_type": "Agregar el tipo de alimentadores automáticos", "t_aerators": "Aireadores", "t_no_aerators": "No hay aireadores", "t_add_aerator_type": "Agregar tipo de aireadores", "t_add_horsepower": "Agregar fuerza de caballo", "t_recorded": "Registrado", "t_record": "Registro", "t_delete_planned_partial_harvest": "Está seguro que quiere eliminar éste plan de cosecha parcial?", "t_delete_recorded_partial_harvest": "¿Está seguro de que desea eliminar este raleo registrado?", "t_planned_partial_harvest_on": "Cosecha parcial planeada para", "t_planned_harvest_on": "Cosecha planeada para", "t_has_passed": "<PERSON> terminado", "t_partial_harvest_passed": "Registre la cosecha parcial ahora para quese tome en cuenta en los cálculos de rentabilidad o edite su plan de cosechas parciales.", "t_harvest_passed_msg": "Registre su cosecha o edite su plan", "t_edit_harvest_plan": "Editar plan de cosecha", "t_data": "Datos", "t_stocking_cost": "Costo de siembra", "t_targets": "Objetivos", "t_primary_feed_brand": "<PERSON>a de alimento principal", "t_projections": "Proyecciones", "t_feed_amount": "Cantidad de alimento", "t_processor_price_lists": "Listas de precio empacadora", "t_select_your_default_price_list": "Seleccione su lista de precios por defecto", "t_class_a_price_unit": "Clase A - $ /{{unit}}", "t_class_b_price_unit": "Clase B - $ /{{unit}}", "t_price_rejection_unit": "Rechazo - $ / {{unit}}", "t_processor": "Empacadora", "t_enter_price_list_name": "Ingresar el nombre de lista de precio", "t_select_your_default_harvest_type": "Seleccione su tipo de cosecha por defecto:", "t_expected_quality_of_harvest_head_on": "Calidad de cosecha esperada - Entero", "t_percent_of_leftover_as_headless_class_a": "% de sobrante para Cola Clase A", "t_percent_of_leftover_as_headless_class_b": "% de sobrante para Cola Clase B", "t_percent_of_headless_class_a": "% de cola en clase A", "t_percent_of_headless_class_b": "% de cola en clase B", "t_expected_quality_of_harvest_headless_direct": "Calidad de cosecha esperada - Cola directa", "t_expected_percent_tail_of_body": "% de cola esperado", "t_percent_tail": "% cola", "t_carrying_capacity": "Capacidad de carga", "t_feed_data": "Data de alimentación", "t_weeks": "Semanas", "t_no_feed_types_available": "No hay tipo de alimentación disponible", "t_rev_lb": "Ingr ($/lb)", "t_rev_kg": "Ingr ($/kg)", "t_profit_lb": "Utilidad ($/lb)", "t_profit_kg": "Utilidad ($/kg)", "t_feed_types": "Tipos de alimento", "t_select_default_feed_to_use_for_cost_projection": "Seleccione el alimento por defecto para usar en las proyecciones de costo", "t_add_feed_type": "Agregar tipo de alimento", "t_brand": "<PERSON><PERSON>", "t_pellet_size_opt": "<PERSON><PERSON> de pellet (ópt)", "t_cost_over_kg": "Costo ($/kg)", "t_please_select_default_feed_type": "Por favor seleccione el tipo de alimento por defecto", "t_stocking_density_ha": "Densidad de siembra (ha)", "t_stocking_density_m2": "Densidad de siembra (m2)", "t_autofeeder_brand": "Marca de alimentadores automáticos", "t_feed_brand": "Marca de alimento", "t_target_config_tooltip": "Los objetivos de métricas de producción están usados para determinar el benchmark del rendimiento de cada piscina. Ingrese los valores de sus objetivos.", "t_farm_book": "Farm book", "t_cycle_week": "<PERSON><PERSON><PERSON>", "t_revenue_at_harvest": "Ingresos al cosechar", "t_biomass_at_harvest": "Biomasa para cosechar", "t_weekly_production_data": "Data de producción semanal", "t_weekly": "<PERSON><PERSON><PERSON>", "t_daily": "Diario", "t_monthly": "<PERSON><PERSON><PERSON>", "t_percent_compared_to_feed_table": "{{value}}% comparativo a la tabla de alimentación", "t_carrying_capacity_lb_ha": "Capacidad de carga (lb/ha)", "t_carrying_capacity_kg_ha": "Capacidad de carga (kg/ha)", "t_#_of_autofeeders": "# de alimentadores automáticos", "t_#_of_aerators": "# de aireadores", "t_autofeeder_protocol": "Protocolo de alimentador automático", "t_next": "Siguient<PERSON>", "t_lab_source": "Origen Lab", "t_nauplii_source": "Origen maduración", "t_day_s": "día(s)", "t_confirm_stocking_details": "Confirmar de<PERSON><PERSON> de siembra", "t_confirm_targets": "Confirmar objetivos", "t_confirm_cycle_protocol": "Confirmar protocolo de ciclo", "t_timer": "Timer", "t_time_of_day": "Hora del día", "t_anytime": "<PERSON><PERSON><PERSON><PERSON> momento", "t_hydrophone": "Hidrófono", "t_feed_brand_to_use": "Marca de alimento", "t_growout_feed_type": "Tipo de alimento para engorde", "t_record_up_to_one_minute": "<PERSON><PERSON><PERSON> hasta un minuto", "t_general_channel": "Canal general", "t_projected": "Proyectado", "t_aerators_per_ha": "Aereadores/ha", "t_type_of_aerator": "<PERSON><PERSON><PERSON> de aireador", "t_pond_setup": "Equipos", "t_nursery": "Prec<PERSON><PERSON>", "t_worse_than_target": "Peor que el objetivo", "t_better_than_target": "Mejor que el objetivo", "t_you_dont_have_permission_to_see_this_page": "No tiene permiso para visualizar esta página", "t_no_history_registered_for_any_feed_days": "No hay histórico registrado para ningún día(s) de alimentación", "t_upload_report": "Subir archivo", "t_from_feed_table": "de tabla de alimentación", "t_biomass_in_pond_unit": "Biomasa - Vivos ({{unit}})", "t_biomass_in_pond_unit_ha": "Biomasa - Vivos ({{unit}}/ha)", "t_biomass_include_harvests_unit": "Biomasa - Incluye raleos ({{unit}})", "t_biomass_include_harvests_unit_ha": "Biomasa - Incluye raleos ({{unit}}/ha)", "t_survival_live": "Supervivencia - vivos", "t_survival_include_harvests": "Supervivencia - Incluye raleos", "t_": "__SIN_TRADUCCIÓN__", "t_dashboard": "Panel", "t_profile": "Perfil", "t_min": "min", "t_max": "máx", "t_hello": "<PERSON><PERSON>", "t_filtered_on": "Filtrado en", "t_total_number_of_ponds": "Número total de piscinas", "t_stocked_ponds": "Piscinas sembradas", "t_dry_ponds": "Piscinas secas", "t_current_view": "Vista actual", "t_not_selected_in_pond_card": "No está seleccionado en la tarjeta de la piscina", "t_my_farms": "<PERSON><PERSON> fincas", "t_select_up_to_x_variables_you_want": "Seleccione hasta {{count}} variables que desea que se muestren.", "t_gps_distance": "Distancia", "t_monitoring_distance_from_pond": "Monitoreo - Distancia de la piscina (m)", "t_monitoring_location": "Ubicación de monitoreo", "t_monitoring_distance_to_pond": "La ubicación de monitoreo está a {{distance}}{{unit}} de la piscina", "t_monitoring_x_distance_to_pond": "La ubicación de monitoreo {{x}} está a {{distance}}{{unit}} de la piscina", "t_monitoring_x_is_near_pond": "La ubicación de monitoreo {{x}} está cerca de la piscina", "t_near_pond": "cerca de la piscina", "t_monitoring_is_near_pond": "La ubicación de monitoreo está cerca de la piscina", "t_latest_monitoring_location": "Última ubicación de monitoreo", "t_overhead_cost_cumulative": "Costo indirecto - Acumulado", "t_overhead_cost_dry_days": "Costo indirecto - Días secos", "t_data_shown_across_all_ponds": "Vista general de todas las piscinas", "t_farm_not_found": "Finca no encontrada", "t_farm_not_found_desc": "Finca no encontrada", "t_invitations_for_a_farm": "Invitaciones para finca {{farmName}}", "t_payment": "Pago", "t_population": "Población", "t_settings": "<PERSON><PERSON><PERSON><PERSON>", "t_select_farm_to_continue": "Seleccione una finca para continuar", "t_forgot_password_desc": "<PERSON><PERSON><PERSON><PERSON> la contraseña", "t_log_in_desc": "In<PERSON><PERSON>", "t_log_out": "<PERSON><PERSON><PERSON>", "t_unsubscribe": "Cancelar suscripción", "t_unsubscribe_desc": "Cancelar suscripción", "t_reset_password_desc": "Restablecer la contraseña", "t_sign_up": "Registrarse", "t_verify_email_desc": "Verificar correo", "t_view_invitation": "Ver invitación", "t_nursery_archived_successfully": "Precriadero archivado exitosamente", "t_archive_nursery": "Archivar precriadero", "t_stock_nursery": "Sembrar el precriadero", "t_edit_nursery_details": "Editar los detalles del precriadero", "t_add_nursery_pond": "Agregar piscina de precría", "t_this_nursery_is_empty": "¡Este precriadero está vacío!", "t_nursery_summary": "Resumen del precriadero", "t_transfer_date": "<PERSON>cha de transferencia", "t_transfer_pond": "Transferir piscina", "t_edit_transfer": "Editar transfer<PERSON>", "t_transfer": "Transferir", "t_record_transfer": "Registrar transferencia", "t_select_input": "Seleccionar entrada", "t_animals_%": "Animales (%)", "t_add_destination": "Agregar destino", "t_transfer_summary": "Resumen de transferencia", "t_cost_millar": "Costo / millar", "t_type_of_stocking": "Tipo de siembra", "t_destination_ponds": "Piscinas de destino", "t_destination": "<PERSON><PERSON>", "t_planned_stocking": "Siembra planificada", "t_transfer_history": "Historia de transferencia", "t_variables": "Variables", "t_invoiced_quantity": "Cantidad facturada", "t_invoiced_quantity_opt": "Cantidad facturada (opc)", "t_end_of_cycle_survival": "Supervivencia al final del ciclo", "t_feed_cost": "Costo de alimento", "t_indirect_costs": "Costos Indirectos", "t_fixed_costs": "Costos fijos", "t_other_costs": "<PERSON><PERSON><PERSON> costos", "t_past_cycle": "<PERSON><PERSON><PERSON>", "t_nursery_last_cycle_msg": "Está viendo un ciclo pasado, siembre el precriadero para comenzar un nuevo ciclo", "t_past": "pasado", "t_edit_stocking_information_to_link_transfers": "Editar información de siembra para vincular transferencias", "t_add_stocking_information_to_link_transfers": "Agregar información de siembra para vincular transferencias", "t_you_have_incoming_transfers_to_this_pond_from": "Tiene transferencias entrantes a esta piscina desde", "t_growout": "Engorde", "t_you_are_missing_important_stocking_information": "Falta información importante de siembra", "t_add_details_now": "Agre<PERSON> de<PERSON>les ahora", "t_transferred_on": "transferido en", "t_this_pond_has_incoming_transfers_from": "Esta piscina tiene transferencias entrantes desde", "t_this_pond_has_accepted_transfers_from": "Esta piscina ha aceptado transferencias de", "t_is_this_stocking_missing_transfer_information": "¿A esta siembra le falta información de transferencia?", "t_manage_nurseries": "Administrar los precriaderos", "t_edit_direct_stocking": "Editar siembra directa", "t_stock_directly": "Sembrar directamente", "t_stock_from_transfer": "Siembra desde transferencia", "t_would_you_like_to_link_this_cycle_with_those_transfers": "¿Le gustaría vincular este ciclo con esas transferencias?", "t_Total_biomass_transferred": "Biomasa total transferida", "t_transfer_amount": "Cantidad transferida", "t_feed_cost_per_kg": "Costo de alimento ($/kg)", "t_feed_cost_$": "Costo de alimento ($)", "t_up_to_date": "Actualizado", "t_using_imputed_values": "*Usando valores estimados", "t_estimated_values_are_used": "Se utilizan valores estimados para completar los cálculos.", "t_direct": "Directo", "t_flagged_ponds": "Piscinas marcadas", "t_delete_confirmation": "Borrar confirmación", "t_are_you_sure_you_want_to_continue": "¿Está seguro de que quiere continuar?", "t_created_by": "<PERSON><PERSON>o por", "t_head_on_abbreviation": "<PERSON><PERSON>", "t_head_less_abbreviation": "Cola", "t_expected_feed": "Alimentación esperada", "t_transfer_confirmation": "Confirmación de transferencia", "t_edit_transfer_confirmation": "Editar confirmación de transferencia", "t_transfer_confirmation_info": "Está a punto de registrar la transferencia de {{nurseryName}} a {{pondNames}}. Esto cosechará automáticamente este precriadero.", "t_transfer_confirmation_question": "¿Está seguro de que quiere completar esta transferencia?", "t_edit_transfer_confirmation_msg": "Si esta transferencia ya se ha utilizado para sembrar otras piscinas, deberá editar la información de siembra en esas piscinas para obtener los valores actualizados aquí", "t_nursery_data_updater": "Actualizador de datos de precriadero", "t_transfer_data": "Datos de transferencia", "t_harvest_type_classification": "Tipo de cosecha: {{harvestType}}", "t_total_transferred_animals_should_be_less_than": "El total de animales transferidos debe ser inferior a {{value}}", "t_grams_change": "{{grams}}g cambio", "t_count_percent_day": "{{count}} / día", "t_kampi": "Kampi", "t_until_harvest": "Hasta la cosecha", "t_projections_not_available": "Proyecciones no disponibles", "t_projections_not_available_for_pond": "Proyecciones no disponibles para piscinas que estén debajo de los 8g y tengan menos de 3 monitoreos", "t_x_days_earlier": "{{days}} días antes", "t_x_days_later": "{{days}} d<PERSON> después", "t_x_day_earlier": "{{days}} día antes", "t_x_day_later": "{{days}} d<PERSON> después", "t_optimal_harvest": "Cosecha óptima", "t_gps_location_appears_farm_from_the_pond": "La ubicación GPS parece estar lejos de la piscina", "t_estimate_data_alert_msg": "Los cálculos de esta piscina utilizan valores estimados para compensar los datos faltantes.", "t_overhead_cost_ha_day": "Costo indirecto/ha/día", "t_other_direct_costs": "Otros costos directos", "t_revenue_processed_unit": "Ingreso - Procesado ($/{{unit}})", "t_manage_projections": "Gestionar proyecciones", "t_class_size": "Tamaño de la clase", "t_biomass_kg": "Biomasa (kg)", "t_biomass_lb": "<PERSON><PERSON><PERSON><PERSON> (lb)", "t_expected": "Esperado", "t_amount_unit": "{{amount}} / {{unit}}", "t_pond_summary": "Resumen de la piscina", "t_protocol": "Protocolo", "t_harvest_summary": "Resumen de la cosecha", "t_pond_source": "Origen de la piscina", "t_autofeeders_ha": "Alimentadores automáticos /ha", "t_selected_x": "Seleccionado: {{count}}", "t_comparison_to_target": "Comparación con el objetivo", "t_add_manual_weight_entry": "Agregar peso manual", "t_target": "Objetivo", "t_mobile_graph_msg": "Haga clic aquí para ver el gráfico de producción", "t_rotate_your_phone_content_msg": "Gire el teléfono para ver el contenido. Asegúrese de que la rotación automática esté desbloqueada en su dispositivo.", "t_click_here_to_see_graph": "Haga clic aquí para ver el gráfico", "t_click_here_to_see_table": "Haga clic aquí para ver la tabla", "t_click_to_see_weekly_production_data": "Haga clic para ver la data de producción semanal", "t_last_monitored_on_x": "<PERSON><PERSON><PERSON> el {{date}}", "t_range_g": "<PERSON><PERSON> (g)", "t_range": "Ra<PERSON>", "t_feed_type_percentage_sum_error": "La suma de todos los valores de Tipo de Alimento no debe exceder 100%", "t_pond_performance_required": "El rendimiento de la piscina requerido para alcanzar los objetivos de la piscina en peso promedio, supervivencia, biomasa y FCA.", "t_variable": "Variable", "t_biomass_processed_without_price": "Biomasa procesada en clases sin precio, por favor revise la lista de precios de su empacadora.", "t_i_am_writing_the_message_here": "Estoy escribiendo el mensaje aquí...", "t_update_default_processor": "Actualizar empacadora predeterminada", "t_delete_processor": "Borrar empacadora", "t_updated_default_processor_modal_text_1": "Usted ha actualizado la empacadora predeterminada. Esto aplicará a todas las fincas de su grupo.", "t_updated_default_processor_modal_text_2": "¿Le gustaría aplicar esta nueva empacadora predeterminada a todas las piscinas que tengan seleccionada la opción predeterminada?", "t_delete_processor_modal_text_1": "Usted ha eliminado la empacadora. Esto aplicará a todas las fincas de su grupo.", "t_delete_processor_modal_text_2": "¿Le gustaría aplicar esto a todas las piscinas?", "t_no_only_apply_to_future_assignments": "No, aplicar solo para futuras tareas", "t_yes_apply_to_all_ponds_and_farms": "Sí, aplicar a todas las piscinas y fincas", "t_update_processor_confirmation": "Confirmación de actualización de empacadora", "t_update_processor_confirmation_text": "Usted ha hecho actualizaciones a esta lista de precios. ¿Le gustaría aplicar estas actualizaciones a todas las piscinas con esta lista de precios seleccionada?", "t_yes_apply": "Sí, aplicar", "t_no_keep_current_assignments": "No, mantener las tareas actuales", "t_revenue_per_lb_live": "Ingreso - Vivos ($/lb)", "t_revenue_per_kg_live": "Ingreso - Vivos ($/kg)", "t_revenue_per_lb_include_harvest": "Ingreso - Incluye raleos ($/lb)", "t_revenue_per_kg_include_harvest": "Ingreso - Incluye raleos ($/kg)", "t_head_on_distribution": "Distribución entero", "t_head_less_distribution": "Distribución cola", "t_harvest_expected": "Cosecha (esperado)", "t_performance": "Rendimiento", "t_above_target": "Por encima del objetivo", "t_on_track": "A tiempo", "t_off_track": "Fuera de tiempo", "t_show_ponds_that_are": "Mostrar piscinas que están", "t_above": "Por encima", "t_above_x_percent_carrying_capacity": "Por encima del {{percent}}% de capacidad de carga", "t_customize_graph": "<PERSON><PERSON><PERSON>", "t_projection_length": "Duración de la proyección", "t_show_ponds_with_a_carrying_capacity": "Mostrar piscinas con una capacidad de carga superior al 85%", "t_x_ponds_are_above_carrying_capacity": "{{count}} piscinas están por encima del 85% de su capacidad de carga, lo que impacta negativamente el crecimiento.", "t_x_ponds_are_above_carrying_capacity_singular": "{{count}} piscina está por encima del 85% de su capacidad de carga, lo que impacta negativamente el crecimiento.", "t_daily_parameters": "Parámetros diarios", "t_only_override_weights_when_a_monitoring_has_been_taken": "Solo puede reemplazar pesos cuando se ha realizado un monitoreo", "t_no_monitoring": "No hay monitoreo", "t_revert_to_kampi_weight": "Volver al peso de Kampi", "t_kampi_weight_x_g": "Peso Kampi: {{averageWeight}}g", "t_kampi_weight_g": "<PERSON><PERSON><PERSON> (g)", "t_updated_by_name": "Actualizado por: {{name}}", "t_manual_weight_confirmation": "Confirmación de peso manual", "t_manual_weight_confirmation_description": "El peso promedio ingresado aquí se utilizará en toda la plataforma, incluido el histograma.", "t_daily_param_monitoring_alert": "No hay ningún ciclo activo para esta piscina en la fecha seleccionada.", "t_daily_param_not_stocked_alert": "¡Esta piscina aún no está sembrada!", "t_overwrite_weights": "¿Está seguro de que desea sobrescribir los pesos de las siguientes piscinas?", "t_invalid_average_weight": "Ingreso no válido. Ingrese un peso más cercano al resultado de Kampi.", "t_parameter": "Parámetro", "t_%_of_carrying_capacity": "% de capacidad de carga", "t_data_check": "Validación de Datos - DEV", "t_ok": "OK", "t_missing": "<PERSON><PERSON><PERSON><PERSON>", "t_target_type": "Tipo de objetivo", "t_target_survival": "Supervivencia objetivo", "t_target_biomass_to_harvest": "Biomasa objetivo a cosechar", "t_target_cycle_length": "Duración del ciclo objetivo", "t_target_harvest_weight": "Peso de cosecha objetivo", "t_target_fcr": "FCA objetivo", "t_projected_feed_type": "Tipo de alimento proyectado", "t_projected_growth_grams": "Crecimiento proyectado (gramos)", "t_projected_growth_days": "Crecimiento proyectado (días)", "t_projected_survival_type": "Tipo de supervivencia proyectada", "t_daily_mortality_percent": "Mortalidad diaria (%)", "t_expected_target_survival": "Supervivencia objetivo esperada", "t_expected_target_survival_days": "Supervivencia objetivo esperada (días)", "t_feed_data_for_last_monitoring": "Datos de alimentación del último monitoreo", "t_selected_processor": "Empacadora seleccionada (Plan de Cosecha)", "t_selected_processor_exists": "La empacadora seleccionada existe en la lista de precios de empacadoras de la finca", "t_profit_projection": "Proyección de ganancias", "t_production_projection": "Proyección de producción", "t_cycle_comparison_projection": "Proyección de comparación de ciclos", "t_active_processor_price_lists": "Listas de precios de empacadora activa (Finca)", "t_active_feed_table": "Tabla de alimento activa (Finca)", "t_active_feed_types": "Tipos de alimento activos (Finca)", "t_monitoring_days_config": "Configuración de días de monitoreo (Config)", "t_start_of_week_config": "Configuración de Inicio de semana (Config)", "t_farm_production_days": "Días de producción de la finca (Config)", "t_farm_weight": "Peso de la finca (Config)", "t_farm_fcr": "FCA de la finca (Config)", "t_farm_cost_per_pound": "Costo / libra de la finca (Config)", "t_farm_profit_per_ha_per_day": "Utilidad / ha / día de la finca (Config)", "t_farm_biomass_lb_ha": "Biomasa de la finca (lb/ha) (Config)", "t_farm_dry_days": "Días secos de la finca (Config)", "t_farm_growth_density": "Densidad de crecimiento de la finca (Config)", "t_farm_days_in_nursery": "Días en el precriadero de la finca (Config)", "t_farm_nursery_survival": "Supervivencia en precriaderos de la finca (Config)", "t_farm_avg_stocking_weight": "Peso promedio de siembra de la finca (Config)", "t_value": "Valor", "t_using_custom_values_for_projection": "Usando valores personalizados para proyecciones", "t_production_chart_alert": "Se pueden visualizar máximo dos variables al mismo tiempo.", "t_show_daily_parameters": "Mostrar los parámetros diarios", "t_feed_given_kg": "<PERSON><PERSON> suministrado (kg)", "t_feed_given_lg": "<PERSON><PERSON> sumini<PERSON>do (lb)", "t_units": "Unidades", "t_unit": "Unidad", "t_affected_areas_with_biomass_unit": "Las áreas afectadas incluyen biomasa total, biomasa/ha, costo/unidad, utilidad/unidad.", "t_edit_daily_feed_msg": "Solo aquí se puede editar la alimentación diaria. Vaya a Actualizador de Datos para editar la alimentación semanal.", "t_cycle_comparison": "Comparación de ciclo", "t_benchmark": "Análisis comparativo", "t_update_plan": "Actualizar plan", "t_animals_stocked_ha": "Animales sembrados/ha", "t_animals_stocked": "Animales - sembrados", "t_biomass_from_partials_unit_ha": "Biomasa - de raleos ({{unit}}/ha)", "t_biomass_from_partials_unit": "Biomasa - de raleos ({{unit}})", "t_biomass_from_final_unit": "Biomasa - de cosecha final ({{unit}})", "t_biomass_processed": "Biomasa - Procesada ({{unit}})", "t_biomass_unpriced": "Biomasa - <PERSON> pre<PERSON> ({{unit}})", "t_date_of_last_partial_harvest": "<PERSON><PERSON> de último raleo", "t_partial_harvest_done": "<PERSON><PERSON> real<PERSON>", "t_last_week": "Semana pasada", "t_x_weeks_ago": "Hace {{count}} semanas", "t_select_variables_you_want_to_be_shown": "Seleccione las variables que desea que se muestren.", "t_animals_harvested_per_m2": "Animales cosechados/m2", "t_animals_harvested_ha": "Animales cosechados/ha", "t_production_days": "Días de Producción", "t_using_imputed_values_for_calculations": "*Usando valores estimados para los cálculos.", "x_monitoring_location_from_the_pond": "La ubicación de monitoreo parece estar a {{distance}}{{unit}} de la piscina.", "x_monitoring_location_near_the_pond": "La ubicación de monitoreo parece estar cerca de la piscina.", "t_no_cycles": "No hay ciclos", "t_select_cycle": "Seleccione el ciclo", "t_only_add_survival_when_a_monitoring_has_been_taken": "La supervivencia solo se puede agregar en los días en los que se ha realizado un monitoreo.", "t_kampi_mortality": "Mortalidad Kampi", "t_total_biomass_biomass_over_ha": "Biomasa total (Biomasa /ha)", "t_total_profit_profit_over_x": "Utilidad Total (Utilidad /{{unit}} Cosechada)", "t_processed": "Procesado", "t_harvested": "Cosechado", "t_selected_variables": "Variables seleccionadas", "t_selected_variables_sort_desc": "Las variables seleccionadas se mostrarán según este orden predeterminado. Puede reordenar las variables arrastrando las filas hacia arriba y hacia abajo.", "t_cycle_information": "Información de ciclo", "t_revenue_per_unit_processed": "Ingreso - Procesado ($/{{unit}})", "t_revenue_per_unit_harvested": "Ingreso - Cosechado ($/{{unit}})", "t_profit_per_unit_processed": "Utilidad - Procesada ($/{{unit}})", "t_profit_per_unit_harvested": "Utilidad - Cosechada ($/{{unit}})", "t_cost_per_unit_processed": "Costo - Procesado ($/{{unit}})", "t_cost_per_unit_harvested": "Costo - Cosechado ($/{{unit}})", "t_add_parameter": "Agregar parámetro", "t_delete_daily_param_alert": "Está a punto de eliminar {{name}} de Kampi. Está acción no puede ser revertida.", "t_param_name_already_exists": "Nombre del parámetro ya existe", "t_prof_ha_d": "Util/ha/d", "t_weekly_linear_growth": "Crecimiento Lineal Semanal", "t_daily_linear_growth": "Crecimiento Lineal Diario", "t_animals_harvested": "<PERSON><PERSON> co<PERSON>", "t_anim_ha": "Anim /ha", "t_anim_m2": "Anim /m2", "t_biomass_lb_partial_harvest": "Biomasa lb - <PERSON><PERSON> (Biomasa /ha)", "t_biomass_lb_final_harvest": "Biomasa lb - Cosecha final (Biomasa /ha)", "t_bioL_p": "Biom R", "t_bioL_f": "Biom Final", "t_cost_p_lb": "Costo Proc ($/lb)", "t_cost_h_lb": "Costo <PERSON>sch ($/lb)", "t_rev": "Ingr ($)", "t_rev_p": "Ingr Proc ($)", "t_rev_h": "Ingr Csch ($)", "t_prof": "<PERSON><PERSON>", "t_kampi_mortality_survival_target": "Objetivo de Supervivencia Mortalidad Kampi", "t_kampi_mortality_survival_target_tooltip": "Esta supervivencia es utilizada para mejorar las predicciones de la Mortalidad Kampi.", "t_days_d": "{{count}}d", "t_select_pond": "Seleccionar una piscina", "t_count_ponds_plural": "{{count}} piscinas", "t_count_ponds_singular": "{{count}} piscina", "t_enter": "Ingresar", "t_drag_and_drop_file_or": "Arrastrar y soltar archivo, o", "t_brows_computer": "navegar por la computadora", "t_to": "a", "t_use_kampi_expected_revenue": "Utilice los ingresos esperados de Kampi.", "t_partial_harvest_confirmation": "Confirmación de raleo", "t_partial_harvest_change_description": "Se ha actualizado la información de la cosecha. ¿Quiere guardar los cambios?", "t_harvest_pond_confirmation": "Confirmación de cosecha de piscina", "t_harvest_change_description": "Se ha actualizado la información de la cosecha. ¿Quiere guardar los cambios?", "t_harvest_plan_change_description": "Se ha actualizado la información del plan de cosecha. ¿Quiere guardar los cambios?", "t_partial_harvest_plan_change_description": "Se ha actualizado la información del plan de raleo. ¿Quiere guardar los cambios?", "t_unread_messages": "Mensajes no leídos", "t_all_messages": "Todos los mensajes", "t_cycle_summary": "Resumen del ciclo", "t_final_harvest": "Cosecha final", "t_general": "General", "t_profit_revenue": "Utilidad/ingreso", "t_fcr_adjusted": "FCA Ajustado", "t_no_partial_harvest_alert": "No hay raleo registrado o planificado.", "t_partial_harvest_x": "<PERSON><PERSON> {{count}}", "t_final_harvest_plan": "Cosecha final - Plan", "t_final_harvest_recorded": "Cosecha final - Registrada", "t_partial_harvest_count_plan": "<PERSON><PERSON> #{{count}} - Plan", "t_partial_harvest_count_recorded": "<PERSON><PERSON> #{{count}} - Regis<PERSON><PERSON>", "t_export": "Exportar", "t_export_farm_summary_table": "Exportar tabla de resumen de finca", "t_export_daily_parameters_table": "Exportar tabla de parámetros diarios", "t_add_farm_overview_data": "Agregar datos generales de la finca", "t_select_a_view": "Seleccione una vista", "t_total_animals_detected": "{{total}} animales detectados", "t_export_weekly_production_data_table": "Exportar tabla de data de producción semanal", "t_your_data_exported_in_xlsx": "Sus datos serán exportados en formato .xlsx", "t_weekly_monitoring_data": "datos de monitoreo semanales", "t_ponds": "Piscinas", "t_harvests": "Cosechas", "t_final": "Final", "t_partial": "<PERSON><PERSON>", "t_next_30_d": "Próximos 30d", "t_past_30_d": "Últ<PERSON>s 30d", "t_type": "Tipo", "t_delete_harvest_confirmation": "Eliminar confirmación de cosecha", "t_you_are_about_to_delete_harvest": "Está a punto de eliminar la cosecha de {{pondName}}.", "t_count_final_count_partial": "{{finalCount}}F, {{partialCount}}P", "t_count_plan_count_recorded": "{{planCount}}P, {{recordedCount}}R", "t_count_ponds": "{{count}} piscinas", "t_harvest_plan_recorded_msg": "La información sobre la cosecha se mostrará aquí después de que se haya registrado.", "t_harvest_plan_planned_msg": "Los resultados de la cosecha según el último plan de cosecha y proyecciones guardadas", "t_harvest_recorded": "Cosecha (registrada)", "t_count_cycles": "{{count}} ciclos", "t_create_nursery_pond": "<PERSON><PERSON><PERSON> prec<PERSON>", "t_rev_per_pound_processed": "Ingr Proc ($/lb)", "t_rev_per_pound_harvested": "Ingr Csch ($/lb)", "t_count_per_ha": "{{count}} /ha", "t_pls_per_g": "PLs/g", "t_quantity_from_hatcheries": "Cantidad de PL facturadas desde los precriaderos", "t_notifications": "Notificaciones", "t_push": "Notificación push", "t_below": "<PERSON><PERSON><PERSON>", "t_nearing_carrying_capacity": "Acercándose a la capacidad de carga", "t_in": "en", "t_notifications_and_rule_error": "Se deben seleccionar el tipo de notificación y las Reglas", "t_carrying_capacity_reached": "Capacidad de carga alcanzada", "t_current": "Actual", "t_kampi_notification_center": "Centro de notificaciones Kampi", "t_all_notifications": "Todas las notificaciones", "t_read": "<PERSON><PERSON><PERSON>", "t_unread": "No leído", "t_sudden_temperature_drop": "Caída repentina de temperatura", "t_sudden_temperature_increase": "Aumento repentino de temperatura", "t_am_water_temperature_drop": "Temp - AM: {{temperature}}°C", "t_pm_water_temperature_drop": "Temp - PM: {{temperature}}°C", "t_am_do_value": "OD - AM: {{value}} mg/L", "t_pm_do_value": "OD - PM: {{value}} mg/L", "t_temperature_dropped_notification": "La temperatura bajó a {{dropped_to}}°C a las {{dropped_to_time}} (bajó de {{dropped_from}}°C a las {{dropped_from_time}})", "t_temperature_increased_notification": "La temperatura aumentó a {{increased_to}}°C a las {{increased_to_time}} (en comparación con {{increased_from}}°C {{increased_from_time}})", "t_carrying_capacity_reached_notification": "La piscina ha alcanzado su capacidad máxima {{days}}", "t_carrying_capacity_will_be_reached_notification": "La piscina alcanzará su capacidad máxima en {{days}} días", "t_tomorrow": "<PERSON><PERSON><PERSON>", "t_carrying_capacity_reached_on_ponds": "Se ha alcanzado la capacidad de carga en {{count}} piscinas", "t_carrying_capacity_will_be_reached_on_ponds": "La capacidad de carga se alcanzará pronto en {{count}} piscinas", "t_sudden_temperature_change_ponds": "Alerta de temperatura: {{count}} piscinas han experimentado una caída (o aumento) repentino de temperatura", "t_no_notifications": "Sin notificaciones", "t_mg_per_L": "mg/L", "t_degree_celsius": "°C", "t_hide_table": "Esconder tabla", "t_show_table": "Mostrar tabla", "t_average_daily_feed": "Alimento Diario Prom.", "t_bags": "sacos", "t_water_am_temperature_unit": "Temperatura del agua - AM{{unit}}", "t_temp_am_unit": "Temp - AM{{unit}}", "t_water_pm_temperature_unit": "Temperatura del agua - PM{{unit}}", "t_temp_pm_unit": "Temp - PM{{unit}}", "t_dissolved_am_oxygen_unit": "Oxígeno Di<PERSON>elto - AM{{unit}}", "t_DO_am_unit": "OD - AM{{unit}}", "t_dissolved_pm_oxygen_unit": "<PERSON><PERSON><PERSON><PERSON><PERSON>{{unit}}", "t_DO_pm_unit": "OD - PM{{unit}}", "t_harvest_actions": "Acciones de cosecha", "t_x_final_harvest_title": "{{action}} cosecha final para la piscina: {{pondName}} ciclo: {{cycle}}", "t_edit_final_harvest_plan_title": "Editar el plan de cosecha final para la piscina: {{pondName}} ciclo: {{cycle}}", "t_x_partial_harvest_plan_title": "{{action}} plan de raleo para la piscina: {{pondName}} ciclo: {{cycle}}", "t_price_list_for_pond_cycle_title": "Lista de precio para la piscina: {{pondName}} ciclo: {{cycle}}", "t_manage_partial_harvests": "Gestionar raleos", "t_details": "Detalles", "t_harvest_quality": "Calidad de la cosecha", "t_standard_price_list_not_applied": "Lista de precios estándar no aplicada", "t_view_price_list": "Ver lista de precios", "t_revert_to_default": "Volver al predeterminado", "t_harvest_quality_update": "Se ha actualizado la calidad de la cosecha", "t_delete_price_list_title_desc": "Está a punto de sobrescribir la lista de precios guardada.", "t_delete_price_list_title_confirmation": "¿Está seguro de que quiere continuar?", "t_x_partial_harvest_title": "{{action}} raleo para la piscina: {{pondName}} ciclo: {{cycle}}", "t_check_price_list_errors": "Revisar los errores en la lista de precios", "t_please_adjust_your_survival_rate_accordingly": "Por favor, ajuste su tasa de supervivencia según sea necesario.", "t_survival_at_x": "Supervivencia al {{date}}", "t_edit_final_harvest_plan": "Editar plan de cosecha final", "t_edit_final_harvest": "<PERSON><PERSON><PERSON> final", "t_record_final_harvest": "Registrar cosecha final", "t_projected_harvest_details": "Detalles de cosecha proyectada", "t_generate_harvest_projections": "Generar proyecciones de cosecha", "t_generate": "Generar", "t_edit_harvest_plan_confirmation": "Editar la confirmación del plan de cosecha final", "t_edit_partial_harvest_plan_confirmation": "Editar la confirmación del plan de raleo", "t_choose_a_date_and_processor": "Elija una fecha y una empacadora para generar", "t_newly_computed_harvest_projections": "Proyecciones de cosecha recién calculadas", "t_refresh": "Actualizar", "t_mark_as_unread": "Marcar como no leído", "t_number_of_notifications_selected_singular": "{{count}} notificación seleccionada", "t_number_of_notifications_selected_plural": "{{count}} notificaciones seleccionadas", "t_manage_partial_harvests_for_pond_cycle": "Administrar raleos para la piscina: {{pondName}} ciclo: {{cycle}}", "t_this_pond_has_no_pending_transfers": "Esta piscina no tiene transferencias pendientes", "t_daily_parameter_title": "Seleccione los parámetros que desea monitorear. Estos aparecerán en la app de Kampi para que pueda introducir los datos.", "t_custom_numeric_parameter": "Parámetro numérico personalizado", "t_manage_notifications": "Gestionar las notificaciones", "t_overhead_cost_ha_day_tooltip": "Los costos generales de su finca, que incluyen el alquiler, los gastos de construcción y mantenimiento, la mano de obra, la depreciación de equipos, entre otros.", "t_type_name": "Escriba un nombre", "t_exceeds_biomass_in_pond": "Supera la biomasa en la piscina.", "t_parameters": "Parámetros", "t_celsius_c": "°C", "t_fahrenheit_f": "°F", "t_liters_L": "L", "t_ppt": "ppt", "t_cm": "cm", "t_pH": "pH", "t_kampi_growth": "Crecimiento Ka<PERSON>", "t_mortality": "Mortalidad", "t_transparency_unit": "Transparencia{{unit}}", "t_transp_unit": "Transp{{unit}}", "t_turbidity_unit": "Turbidez{{unit}}", "t_turb_unit": "Turb{{unit}}", "t_salinity_unit": "Salinidad{{unit}}", "t_sal_unit": "Sal{{unit}}", "t_alkalinity_unit": "Alcalinidad{{unit}}", "t_alk_unit": "Alc{{unit}}", "t_ammonia_nh3_unit": "Amoníaco [NH3]{{unit}}", "t_nh3_unit": "NH3{{unit}}", "t_nitrite_no2_unit": "Nitrito [NO2]{{unit}}", "t_no2_unit": "NO2{{unit}}", "t_nitrate_no3_unit": "Nitrato [NO3]{{unit}}", "t_no3_unit": "NO3{{unit}}", "t_magnesium_mg_unit": "Magnesio [Mg]{{unit}}", "t_mg_unit": "Mg{{unit}}", "t_calcium_ca_unit": "Calcio [Ca]{{unit}}", "t_ca_unit": "Ca{{unit}}", "t_potassium_k_unit": "Potasio [K]{{unit}}", "t_k_unit": "K{{unit}}", "t_growth": "Crecimiento", "t_feed_given": "Alimento suministrado", "t_all_data": "Todos los datos", "t_final_harvest_date": "Fecha final de cosecha", "t_source": "Origen", "t_device": "Dispositivo", "t_go_to_summary": "Ir a <PERSON>n", "t_version": "versión", "t_os": "OS", "t_hour": "<PERSON><PERSON>", "t_minute": "Min<PERSON>", "t_second": "<PERSON><PERSON><PERSON>", "t_seconds": "<PERSON><PERSON><PERSON>", "t_millisecond": "Milisegund<PERSON>", "t_milliseconds": "Milisegundos", "t_no_trays_available": "No hay bandejas disponibles", "t_showing_x_to_x_of_x_items": " Mostrando {{range1}} a {{range2}} de {{total}} ítems", "t_items": "Ítems", "t_page": "<PERSON><PERSON><PERSON><PERSON>", "t_prev_page": "Página ant.", "t_next_page": "Página sig.", "t_uploaded": "<PERSON><PERSON>", "t_upload": "<PERSON><PERSON>", "t_monitoring_status": "Estado de monitoreo", "t_start": "<PERSON><PERSON>o", "t_end": "Fin", "t_pending_upload": "Carga pendiente", "t_overhead_cost_days_of_culture": "Costo indirecto - Días de cultivo", "t_sharing": "Compartir", "t_sandbox": "Entorno de pruebas", "t_supervisor": "Supervisor", "t_layout_will_be_shared_across_all_farms": "Este diseño se compartirá entre todas las fincas del grupo:", "t_reset_data_confirmation": "Al hacer clic en “Restablecer”, se descartarán los cambios realizados y se restaurarán los últimos valores guardados", "t_projected_at_final_harvest": "Proyectado para la cosecha final", "t_simulate_projections": "Simular proyecciones", "t_no_cv_for_manual_monitoring": "CV no disponible para monitoreos manuales", "t_monitoring_id": "ID Monitoreo", "t_group_performance": "Desempeño del grupo", "t_all_farms": "Todas las fincas", "t_filter_farms": "Filtrar fincas", "t_#_of_cycles": "# de ciclos", "t_average_x_for_completed_cycles_across_farms": "Promedio de {{variable}} para ciclos completados en todas las fincas seleccionadas", "t_breakdown": "<PERSON><PERSON><PERSON>", "t_all_year": "Todo el año", "t_completed_cycles": "<PERSON><PERSON><PERSON> comple<PERSON>", "t_past_x_months": "Últ<PERSON><PERSON> {{months}} meses", "t_no_cycles_completed": "No se completaron ciclos en este período.", "t_reset_to_last_saved_values": "Restablecer a los últimos valores guardados", "t_reset": "Restablecer", "t_proceed_confirmation_msg": "¿Está seguro de que desea continuar?", "t_class_size_without_price_set": "Tamaño de clase sin precio establecido", "t_monitoring_strength": "Monitoreo - Calidad", "t_weak_great_incomplete_desc": "La calidad de la imagen es excelente, pero el tamaño de la muestra es muy pequeño. Para mejorar los futuros monitoreos, agregue algunos camarones más, asegurándose de que no se amontonen, y tome muestras de más bandejas.", "t_moderate_great_partial_desc": "<PERSON>y buena calidad de imagen. Para mejorar los futuros monitoreos, agregue algunos camarones más, asegurándose de que no se amontonen, o tome muestras de más bandejas.", "t_strong_great_complete_desc": "Un resultado fantástico con imágenes de excelente calidad y un tamaño de muestra completo. Estos datos son muy confiables.", "t_great_great_great_desc": "¡Este es un monitoreo perfecto! Ha capturado imágenes excelentes y superado la meta de muestras.", "t_weak_good_incomplete_desc": "La calidad de la imagen es buena. Para obtener un monitoreo confiable, agregue algunos camarones más, asegurándose de que no se amontonen, y tome muestras de más bandejas.", "t_moderate_good_partial_desc": "Estas son buenas imágenes. Para mejorar los futuros monitoreos, concéntrese en agregar algunos camarones más, evitando que se amontonen, y tome muestras de más bandejas.", "t_strong_good_complete_desc": "Un resultado sólido. Ha alcanzado la meta de muestras con buenas imágenes. Para obtener resultados aún mejores, procure evitar el amontonamiento de camarones.", "t_strong_good_great_desc": "Ha recolectado una buena cantidad de camarones con buena calidad. Los resultados ideales se logran utilizando una bandeja seca y evitando el amontonamiento de camarones.", "t_weak_below_standard_incomplete_desc": "Revise la configuración. El problema principal es el amontonamiento de camarones o sombras muy marcadas. Inténtelo nuevamente con más camarones una vez que lo haya corregido.", "t_moderate_below_standard_partial_desc": "El enfoque principal debe ser la calidad. Intente distribuir los camarones para reducir el amontonamiento y evite una mala iluminación.", "t_moderate_below_standard_complete_desc": "Hay suficientes muestras, por lo que ahora el enfoque debe ser la calidad. El problema principal probablemente sea el amontonamiento de camarones o una iluminación deficiente.", "t_moderate_below_standard_great_desc": "¡Ha obtenido una buena cantidad de camarones! <PERSON><PERSON>, enfóquese en reducir el amontonamiento y mejore la iluminación para lograr un monitoreo más confiable", "t_weak_poor_incomplete_desc": "Faltan tanto volumen como calidad. Para mejorar los futuros monitoreos, concéntrese en agregar más camarones, asegurándose de que no se amontonen, y tome muestras de más bandejas.", "t_weak_poor_partial_desc": "La prioridad es corregir la baja calidad de imagen causada por una iluminación deficiente o por el amontonamiento de camarones. Para mejorar los futuros monitoreos, intente distribuirlos de forma uniforme.", "t_weak_poor_complete_desc": "Hay suficientes camarones, pero la mala calidad de la imagen afecta la confiabilidad. Para mejorar los futuros monitoreos, corrija los problemas de amontonamiento o de iluminación.", "t_weak_poor_great_desc": "Una buena cantidad de muestras no aporta mucho si las imágenes son de baja calidad. Para mejorar los futuros monitoreos, la prioridad debe ser corregir el amontonamiento de camarones, la iluminación y otros problemas relacionados con la calidad.", "t_weak": "<PERSON><PERSON><PERSON>", "t_moderate": "Moderado", "t_strong": "<PERSON><PERSON>e", "t_great": "Excelente", "t_incomplete": "Incompleto", "t_complete": "Completo", "t_below_standard": "Debajo del estándar", "t_good": "Buena", "t_poor": "Mala", "t_quality": "Calidad", "t_completeness": "Completitud", "t_x_usual_shrimps_for_weights_out_x_counted": "{{good}} camarones usados para estimación de peso de {{total}} detectados", "t_carrying_capacity_usage": "Capacidad de carga - Uso", "t_do_am_grouping_msg": "OD - AM: Fuera del rango normal en {{count}} piscinas", "t_do_pm_grouping_msg": "OD - PM: Fuera del rango normal en {{count}} piscinas", "t_temperature_am_grouping_msg": "Temperatura - AM: Fuera del rango normal en {{count}} piscinas", "t_temperature_pm_grouping_msg": "Temperatura - PM: Fuera del rango normal en {{count}} piscinas", "t_reaching_full_capacity": "Alcanzando la capacidad máxima", "t_full_capacity_reached": "Capacidad máxima alcanzada", "t_water_temperature_low": "Temperatura del agua - {{type}} es baja: {{temperature}}°C", "t_water_temperature_high": "Temperatura del agua - {{type}} es alta: {{temperature}}°C", "t_do_am_low": "Oxígeno Disuelto - AM es bajo: {{value}} mg/L", "t_do_pm_low": "Oxí<PERSON><PERSON> - PM es bajo: {{value}} mg/L", "t_do_am_high": "Oxígeno <PERSON>elto - AM es alto: {{value}} mg/L", "t_do_pm_high": "<PERSON><PERSON>í<PERSON><PERSON> - PM es alto: {{value}} mg/L", "t_x_daily_parameters_created": "{{count}}/{{total}} parámetros diarios generados", "t_biomass_in_pond_kg_ha": "Biomasa - Vivos (kg/ha)", "t_biomass_in_pond_lb_ha": "Biomasa - Vivos (lb/ha)", "t_stocking_weight": "Peso de siembra", "t_label": "Etiqueta", "t_comment": "Comentario", "t_tray_annotations_created": "Anotaciones de la bandeja creadas correctamente", "t_farm_breakdown": "Desglose por finca", "t_farm_breakdown_description": "Desglose de KPIs por finca para entender qué está impulsando el rendimiento.", "t_change": "Cambio", "t_no_harvests_recorded_this_month": "No hay cosechas registradas este mes", "t_dashboard_name": "Dashboard name", "t_add_new_dashboard": "Add new dashboard", "t_number_of_charts": "Number of charts", "t_select_layout": "Select Layout", "t_save_dashboard": "Save Dashboard", "t_my_dashboards": "My dashboards"}