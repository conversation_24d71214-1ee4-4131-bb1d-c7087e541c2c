{"t_home": "", "t_accept": "Aceitar", "t_access": "Acessar", "t_account_details": "<PERSON>al<PERSON> da conta", "t_active": "Ativar", "t_add": "<PERSON><PERSON><PERSON><PERSON>", "t_add_a_farm": "Adicionar uma fazenda", "t_add_farm": "Adicionar fazenda", "t_add_pond": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "t_add_user": "<PERSON><PERSON><PERSON><PERSON>", "t_admin": "Administrador", "t_user": "<PERSON><PERSON><PERSON><PERSON> ", "t_operations_supervisor": "", "t_operations_supervisor_can_see": "", "t_production_supervisor": "", "t_already_have_an_account": "<PERSON><PERSON> tenho uma conta", "t_and": "e", "t_animals": "<PERSON><PERSON><PERSON>", "t_archive_pond": "<PERSON><PERSON><PERSON><PERSON> vive<PERSON>", "t_back_to_log_in": "Voltar para log in", "t_bulk_invite": "Convite em massa", "t_by_clicking_create_account": "Ao clicar em 'Criar conta' você concorda com nossa", "t_cancel": "<PERSON><PERSON><PERSON>", "t_cancel_invitation": "Cancelar convite", "t_change_password": "<PERSON><PERSON><PERSON><PERSON>", "t_close": "<PERSON><PERSON><PERSON>", "t_code": "Código ", "t_confirm": "Confirmar", "t_confirm_archive": "Tem certeza que deseja arquivar?", "t_confirm_new_password": "Confirme nova senha", "t_confirm_password": "Confirmar <PERSON><PERSON><PERSON>", "t_contact_phone_no": "Telefone para contato", "t_continue": "<PERSON><PERSON><PERSON><PERSON>", "t_create": "<PERSON><PERSON><PERSON>", "t_react_select_create_label": "C<PERSON>r \"{{value}}\"", "t_create_account": "C<PERSON><PERSON> conta", "t_create_invitation": "<PERSON><PERSON><PERSON> convite ", "t_created_at": "C<PERSON><PERSON> em", "t_cv": "CV", "t_cycle": "<PERSON><PERSON><PERSON>", "t_cycle_number": "Número do ciclo", "t_date": "Data", "t_day": "dia", "t_days": "dias", "t_days_2": "", "t_hours": "", "t_minutes": "", "t_count_days_of_culture": "", "t_did_not_receive_code": "Você não recebeu o código?", "t_distribution": "Distribuição", "t_edit_access": "<PERSON><PERSON>", "t_edit_pond": "<PERSON><PERSON> v<PERSON> ", "t_edit_nursery": "", "t_edit_stocking": "<PERSON>ar p<PERSON>o", "t_email": "Email", "t_enter_the_code_here": "Insira o código aqui", "t_farm": "Fazenda", "t_female": "Feminino", "t_finished_at": "<PERSON><PERSON><PERSON> em", "t_forgot_password": "<PERSON><PERSON><PERSON> a senha", "t_forgot_password_description": "Insira o e-mail associado à sua conta e enviaremos um código para redefinir sua senha", "t_gender": "Sexo", "t_ha": "", "t_harvest": "Despesca", "t_harvest_date": "Data despesca", "t_id": "ID", "t_in_process": "Processando ", "t_invitations": "<PERSON><PERSON><PERSON>", "t_invite_new_user": "Convidar novo usuário ", "t_invited": "Convidado", "t_invited_at": "Convidado em", "t_invited_date_time": "Convidado {{dateTime}}", "t_is_active": "Esta ativo?", "t_join_via_invitation": "Entar via convite", "t_last_active": "Último ativo", "t_language": "Idioma", "t_loading": "Carregando ", "t_log_in": "Entrar", "t_manage_preferences": "", "t_preferences_updated_successfully": "", "t_email_settings": "", "t_unsubscribe_message": "", "t_log_in_to_your_account": "Entrar na sua conta", "t_login_password": "<PERSON><PERSON>", "t_logout": "<PERSON><PERSON>", "t_male": "<PERSON><PERSON><PERSON><PERSON>", "t_manage_monitoring": "Gerenciar monitoramento", "t_message": "Mensagem", "t_monitoring": "Monitoramento", "t_monitorings": "Monitoramentos", "t_my_account": "Minha conta", "t_name": "Nome", "t_new_password": "Nova senha", "t_new_pond": "Novo viveiro", "t_new_user": "Novo usuário ", "t_no_data_to_display": "Sem informações para mostrar", "t_no_farms": "Sem fazendas", "t_no_invitations": "Sem convites", "t_no_payments": "", "t_plan": "", "t_provider": "", "t_payment_history": "", "t_no_monitoring_available": "Sem monitoramentos disponíveis", "t_not_active": "Não está ativo", "t_not_have_account": "", "t_contact_us": "Entre em contato", "t_not_receive_the_code": "Se você não recebeu o código", "t_old_password": "<PERSON><PERSON> antiga", "t_oops": "Ops", "t_optional": "Opcional", "t_or": "ou", "t_other": "Outros", "t_password": "<PERSON><PERSON>", "t_phone_number": "Telefone", "t_photo_count": "", "t_photos": "Fotos", "t_pond": "<PERSON><PERSON><PERSON> ", "t_pond_name_validation_msg": "Números, letras e -, 15 caracteres no máximo", "t_nursery_name_validation_msg": "", "t_pond_size": "", "t_past_cycle_summary": "", "t_abw_at_transfer": "", "t_privacy_policy": "Politica de privacidade ", "t_processing_status": "Status do processamento ", "t_quantity": "Quantidade ", "t_received_invitation": "Você foi convidado a participar de", "t_invited_to_farm": "fazenda por usuário", "t_reject": "Recusar", "t_resend_code": "Reenviar código ", "t_reset_code": "Redefinir c<PERSON>", "t_reset_password": "<PERSON><PERSON><PERSON><PERSON>", "t_reset_password_description": "Enviamos um email contendo o código para redefinir sua senha", "t_role": "Função", "t_save": "<PERSON><PERSON>", "t_search_by_user_farm_or_status": "", "t_select_existing_monitoring": "Selecionar monitoramento existente", "t_select_existing_or_enter_new_email": "Selecionar email existente ou inserir novo ", "t_select_farm": "Selecionar fazenda", "t_select_file": "Selecionar arquivo", "t_select_role": "Selecionar função", "t_send": "Enviar", "t_sign_up_instantly": "Inscrever-se imediatamente", "t_start_new_monitoring": "Iniciar novo monitoramento", "t_status": "Status", "t_stock": "Po<PERSON><PERSON>", "t_stock_pond": "Po<PERSON>ar viveiro", "t_stocking": "Povoamento", "t_submit": "Enviar", "t_submit_monitoring": "Enviar monitoramento", "t_submitted": "Enviado", "t_supervisor_optional": "", "t_terms_of_service": "Termos de serviço", "t_today": "Hoje", "t_total": "Total", "t_total_images": "Total de imagens", "t_trays": "Bandejas", "t_type_of_access": "<PERSON><PERSON><PERSON> de <PERSON>sso", "t_unknown": "Desconhecido", "t_update": "<PERSON><PERSON><PERSON><PERSON>", "t_uploaded_at": "Atualizado em", "t_delete": "Excluir", "t_verification_code": "Código de verificação", "t_verify_email": "Verificar email", "t_verify_email_description": "Enviamos um email contendo seu link de verificação", "t_verify_your_email": "Verifique seu email", "t_verify_your_email_address": "Verifique seu endereço de email ", "t_view_images": "Visualizar imagens", "t_weight_distribution": "Distribuição de peso", "t_yesterday": "Ontem", "t_email_sent_successfully_check": "Email enviado com sucesso, por favor, verifique seu e-mail", "t_sms_sent_successfully_check_mobile": "Um SMS foi enviado com sucesso, por favor, verifique seu telefone", "txt_email_schema_error": "Email deve ter no mínimo 7 caracteres (ex: <EMAIL>)", "txt_password_mismatch": "Senha e confirmação da senha não coincidem", "txt_passwords_reset_does_not_match": "Nova senha e confirmação da nova senha não coincidem", "yup_email_is_invalid": "<PERSON><PERSON>", "yup_enter_minimum_characters": "Por favor, insira no mínimo {{min}} caracteres", "yup_is_invalid": "{{label}} inv<PERSON>lido", "yup_is_required": "{{label}} é um campo obrigatório", "yup_not_type": "<PERSON><PERSON>o in<PERSON>, {{label}} deve ser um tipo '{{type}}'", "yup_should_be_greater_than_more": "{{label}} deve ser maior do que {{more}}", "yup_should_be_greater_than_zero": "{{label}} deve ser maior do que 0", "yup_should_be_one_of": "{{label}} deve ser um dos {{values}}", "yup_should_not_be_less_than": "{{label}} não deve ser menor do que {{min}}", "yup_should_be_less_than_or_equal": "{{label}} deve ser menor ou igual a {{max}}", "yup_should_be_integer": "{{label}} deve ser inteiro", "yup_date_must_not_be_later_than": "", "yup_date_must_not_be_earlier_than": "", "t_heads_on": "Inteiros", "t_heads_off": "Descabeçados", "t_download": "Download", "t_days_ago_plural": "", "go_back": "Voltar", "t_something_went_wrong": "<PERSON>go deu errado", "t_actions": "Ações", "t_data_from_previous_cycle": "", "t_err_account_not_found": "Não há nenhuma conta vinculada a este e-mail", "t_err_code_is_invalid": "Código de redefinição inválido", "t_err_email_or_password_incorrect": "Email ou senha estão incorretos", "t_err_user_not_found": "Não foi possível encontrar o usuário", "t_err_email_already_in_use": "Email já está em uso", "t_err_unauthorized": "Não autorizado", "t_err_pond_not_found": "Viveiro não encontrado", "t_stocking_date": "Data do povoamento", "t_processing": "Processando", "t_processing_failed": "Processamento falhou", "t_delete_monitoring": "Excluir monitoramento", "t_delete_monitoring_msg": "Se você excluir esse monitoramento, ele não será incluído nos dados diários e não será usado para alertas ou projeções.", "t_delete_monitoring_toast_title": "Monitoramento excluído", "t_delete_monitoring_toast_msg": "Seu monitoramento foi excluído com sucesso", "t_error": "Erro", "t_page_not_found": "Página não encontrada", "t_go_home": "Ir para início", "t_success": "Sucesso", "t_verification_failed": "Verificação falhou", "t_login_to_accept_invite": "Você precisa entrar para aceitar o convite", "t_code_sent": "O código foi enviado com sucesso, por favor, verifique seu telefone", "t_accepted_invitation": "Você aceitou o convite com sucesso ", "t_account_verified": "Você verificou sua conta com sucesso", "t_logged_in_success_title": "Log in bem sucedido", "t_logged_in_success_msg": "Você entrou com sucesso", "t_profile_updated": "Você atualizou seu perfil com sucesso", "t_updated_successfully": "Atualizado com sucesso", "t_invitation_email_sent": "Email de convite enviado", "t_user_membership_updated": "Inscrição de usuário atualizada com sucesso", "t_invitation_rejected": "Você recusou o convite com sucesso", "t_created_successfully": "Criado com sucesso", "t_invitation_created": "Você criou um convite com sucesso", "t_password_changed": "Senha alterada com sucesso", "t_password_reset_success": "Você redefiniu sua senha e entrou com sucesso", "t_invitation_cancelled_title": "Convite cancelado com sucesso", "t_invitation_cancelled_msg": "Você cancelou o convite com sucesso ", "t_phone_number_updated": "Telefone atualizado com sucesso", "t_email_updated": "Email atualizado com sucesso", "t_monitoring_submitted": "Monitoramento enviado com sucesso", "t_manual_monitoring_created": "", "t_logged_in_success": "Você entrou com sucesso", "t_change_your_email": "Alterar email", "t_contact_admin_to_change_email": "", "t_farm_must_be_unique": "Nome da fazenda deve ser único", "t_not_invite_yourself": "Você não pode convidar você mesmo", "t_percentage_sampled_animals": "{{percent}} dos animais amostrados estão na faixa de  {{grams}} ({{range}})", "t_aggregation_in_progress": "Agregação em progresso, por favor, volte em 20 segundos", "t_get_history_and_growth": "", "t_percentage_of_stocked_animals": "", "t_select_or_create_monitoring": "Por favor, crie um monitoramento ou selecione um existente dos acima", "t_cv_learning": "", "t_cv_description_high": "", "t_cv_description_low": "", "t_cv_fixing_distribution": "Detectando e consertando distribuição alta", "t_cv_xpertsea_alert": "", "t_then_you_can": "Então você pode:", "t_cv_review_pond_histogram": "", "t_cv_consider_following_our_tips": "Considere seguir nossas dicas para melhorar sua uniformidade", "t_cv_tips_to_improve": "Dicas para melhorar sua uniformidade", "t_cv_how_cv_calculated": "Como o CV é calculado", "t_cv_how_determined": "Como determinamos se um CV é alto", "t_cv_select_the_average_body_weight": "Selecione o peso médio", "t_at": "em", "t_after": "<PERSON><PERSON><PERSON>", "t_consider_the_following": "Considere fazer o seguinte:", "t_cv_05_description": "", "t_cv_10_description": "ainda é relativamente barato garantir uma boa uniformidade, o que prepara o viveiro para um forte ciclo de crescimento.", "t_cv_15_description": "otimize seu crescimento alimentando todos os diferentes tamanhos no seu viveiro, não apenas o tamanho médio.", "t_cv_15_plus_description": "você pode reduzir a desuniformidade com 2-3 semanas de alimentação direcionada enquanto se prepara para a despesca.", "t_cv_step_analyze": "", "t_cv_step_analyze_to_understand": "", "t_cv_step_encourage": "", "t_cv_step_encourage_then": "", "t_cv_step_shrimp_far": "Se os camarões estiverem longe dos alimentadores automáticos, atraia-os em direção às zonas dos alimentadores automáticos com alimentação manual.", "t_cv_step_smaller_shrimp_far": "Se os camarões menores estiverem longe dos alimentadores automáticos, atraia-os em direção às zonas dos alimentadores automáticos com alimentação manual.", "t_cv_step_ensure": "Garanta que há alimentadores automáticos suficiente no viveiro, que eles estejam corretamente posicionados, e que estão funcionando adequadamente.", "t_cv_step_feed_away": "Alimente longe de áreas com fundo insalubre e mantenha o fundo do viveiro livre de matéria orgânica decomposta.", "t_cv_step_collect": "Colete amostras maiores durante o monitoramento (>300 camarões) para aumentar sua confiabilidade nos tamanhos que venderá ao seu comprador.", "t_cv_step_focus": "Foque sua alimentação nos camarões menores, concentrado a alimentação nas áreas do viveiro onde eles são encontrados.", "t_cv_step_use": "Use um desbaste para remover os camarões maiores e dar mais espaço para os camarões menores crescerem.", "t_cv_calculation_description": "", "t_cv_calculation_description_long": "O Desvio padrão (DP) é determinado pela determinação prévia das diferenças entre o peso de cada camarão e o peso médio de todos os camarões. Estas diferenças são então convertidas a um único número, o DP, que representa a variação destes valores. ", "t_cv_calculation_description_short": "O CV é então calculado a partir da divisão do DP pelo peso médio. Isso nos permite comparar o CV para diferentes pesos de camarão.", "t_cv_calculation_examples": "Exemplos para camarão de 9g:", "t_cv_low": "CV 10 %", "t_cv_sd_low": "0.9 DP", "t_cv_medium": "CV 20 %", "t_cv_sd_medium": "1.8 DP", "t_cv_high": "CV 28 %", "t_cv_sd_high": "2.5 DP", "t_cv_high_description": "", "t_cv_high_description_long": "", "t_cv_high_yellow": "Alto (amarelo)", "t_cv_high_yellow_desc": "", "t_cv_very_high_red": "<PERSON><PERSON> alto (vermelho)", "t_cv_very_high_red_desc": "- Está mais alto que 85% das suas amostras anteriores para o mesmo peso médio", "t_unharvest_pond": "Viveiro não despescado (DEV)", "t_dispersion": "", "t_target_weight": "Meta de peso", "t_days_of_production": "Dias de produção", "t_target_harvest_date": "Meta de data de despesca", "t_set_target": "Estabeleça meta", "t_show_users_all_farms": "Mostrar usu<PERSON><PERSON>s de todas as fazendas", "t_edit": "<PERSON><PERSON>", "t_try_again": "Tente novamente", "t_target_at_risk": "Meta em risco", "t_remove": "Remover meta", "t_confirm_remove_target": "Tem certeza que deseja remover a meta deste viveiro?", "t_set_later": "<PERSON><PERSON> tarde", "t_needs_attention": "Precisa de atenção", "t_select_date": "Selecione data", "t_average_weight_should_be_less_than_first_monitoring_average_weight": "O peso médio deveria ser menor do que  {{firstMonitoringAverageWeight}}", "t_growth_rate_since_monitoring": "Taxa de crescimento desde o último monitoramento", "t_growth_in": "", "t_growth_day": "Crescimento / dia = {{dailyGrowth}} g", "t_weekly_growth_seven_days": "", "t_min_monitored_weight": "Deve ser superior ao último peso monitorado ({{weight}} g) ", "t_min_target_weight": "Deve ser no mínimo {{weight}} g", "t_max_target_weight": "<PERSON>e ser menos que {{weight}} g", "t_min_target_stocking_days": "Deve ser superior do que os dias desde o povoamento ({{days}} dias)", "t_min_target_days": "Deve ser no mínimo {{weight}} dias", "t_integer_target_days": "Não pode conter um decimal (e.g. 90)", "t_growth_warning_prefix": "Esta meta exigirá uma taxa de crescimento média de", "t_growth_warning_rate": "{{rate}} g.", "t_growth_warning_suffix": "Edite sua meta ou assinale aqui para confirmar e continuar.", "t_target_harvest_date_updated": "A data prevista da despesca foi atualizada para ser <b>{{days}}</b> dias após sua data de povoamento.", "t_target_harvest_date_updated_confirm": "Confirme ou edite a meta e clique e Atualizar", "t_set_survival_rate": "Entrar", "t_survival_form_title": "", "t_enter_survival": "", "t_edit_survival": "", "t_survival_final_harvest": "", "t_survival": "Sobrevivência", "t_kampi_survival": "", "t_biomass": "Biomassa", "t_survival_rate_tooltip": "", "t_biomass_tooltip": "", "t_stocking_weight_g": "", "t_processing_failed_tooltip_title": "Suas fotos foram carregadas mas o processamento falhou.", "t_processing_failed_tooltip_subtitle": "", "t_pallet_size": "", "t_practical_analysis_error": "", "t_practical_modal_header": "", "t_practical_analysis_tooltip_header": "", "t_practical_analysis_tooltip_desc": "", "t_cancel_save_changes": "", "t_cancel_save_changes_confirmation": "", "t_weight_error": "", "t_weight_g": "Peso (g)", "t_confirm_remove_feed_pallets": "", "t_invalid_weight": "", "t_growth_ahead": "", "t_on_track_to_hit_target": "", "t_check_stock": "", "t_check_sample": "", "t_feed_type": "", "t_last_updated_on": "", "t_cost": "", "t_cost_$": "", "t_kg": "", "t_select": "", "t_size": "", "t_daily_mortality": "", "t_abw": "", "t_abw_g": "", "t_no_field_duplicated_values": "", "t_planned": "", "t_left": "", "t_right": "", "t_save_changes": "", "t_select_option": "", "t_no_data_available": "", "t_monday": "", "t_tuesday": "", "t_wednesday": "", "t_thursday": "", "t_friday": "", "t_saturday": "", "t_sunday": "", "t_no_options": "", "t_fcr": "", "t_setting_pond_bottom_msg": "", "t_pond_monitoring_after_harvest": "", "t_required": "", "t_normal": "", "t_err_file_size_limit_exceeded": "", "t_symmetry": "", "t_symmetry_left_title": "", "t_symmetry_left_info": "", "t_symmetry_right_title": "", "t_symmetry_right_info": "", "t_symmetry_normal_info": "", "t_symmetry_few_animals_note": "", "t_g_in_days": "", "t_record_partial_harvest": "", "t_plan_partial_harvest": "", "t_biomass_harvested_unit_ha": "", "t_biomass_harvested": "", "t_harvest_plan": "", "t_estimated_animals_harvested": "", "t_animals_harvested_%": "", "t_partial_harvest_quantity_error": "", "t_add_notes": "", "t_start_monitoring": "", "t_edit_manual_monitoring": "", "t_show_me_how": "", "t_back": "", "t_head_on": "", "t_headless": "", "t_configuration": "", "t_search": "", "t_survival_%_after_partial_harvest": "", "t_survival_will_be_assigned_to": "", "t_survival_will_not_be_assigned_to_monitoring": "", "t_partial_harvest": "", "t_x_harvested_on": "", "t_ensure_survival_included": "", "t_ensure_survival_included_tooltip": "", "t_growth_rate": "", "t_revenue": "", "t_users_at_farm": "", "t_users_at_all_farms": "", "t_stock_your_pond_to_start_getting": "", "t_stock_your_nursery_to_start_getting": "", "t_pond_actions": "", "t_edit_pond_details": "", "t_pond_archived_successfully": "", "t_present": "", "t_projection_variables": "", "t_profit_include_dry_days_ha_day": "", "t_partial_harvest_revenue": "", "t_add_price_list": "", "t_empty_cycle": "", "t_dry_days": "", "t_days_in_nursery": "", "t_nursery_survival": "", "t_larva_order_time": "", "t_price_list": "", "t_pond_cost": "", "t_min_allowed_stocked": "", "t_profit": "", "t_rofi": "", "t_last_updated_on_by": "", "t_add_lab_name": "", "t_monitoring_updated_successfully": "", "t_production": "", "t_number_of_images": "", "t_buy_images": "", "t_use_slider_or_input_number_of_images": "", "t_weight_stocked_on_date": "", "t_revenue_per_pound": "", "t_revenue_per_kg": "", "t_no_lab_names": "", "t_no_genetic_names": "", "t_my_team": "", "t_planned_harvest": "", "t_recorded_harvest": "", "t_no_growth_data": "", "t_enter_weekly_growth_data": "", "t_enter_growth_data_or_monitor_pond": "", "t_no_past_cycles": "", "t_your_pond_is_empty": "", "t_stock_pond_to_get_started": "", "t_stock_your_pond": "", "t_last_monitoring": "", "t_latest_monitoring": "", "t_current_biomass": "", "t_planned_final_harvest": "", "t_projected_profit": "", "t_projected_revenue": "", "t_cost_to_date_estimated": "", "t_feed_given_daily_kg_ha": "", "t_nurseries": "", "t_harvest_weight_g": "", "t_cycle_length": "", "t_add_liquidation_report": "", "t_liquidation_report_format": "", "t_record_harvest": "", "t_custom": "", "t_cycle_info": "", "t_add_new_size": "", "t_no_ponds_in_your_farm": "", "t_no_nurseries_in_your_farm": "", "t_create_a_new_pond": "", "t_create_a_new_nursery": "", "t_end_of_cycle": "", "t_hint_you_can_copy_and_paste_from_excel": "", "t_add_weight": "", "t_overhead_cost_tooltip": "", "t_gram_g": "", "t_max_x": "", "t_min_x": "", "t_fcr_tooltip": "", "t_kg_ha_tooltip": "", "t_field_should_be_less_or_equal_to_x": "", "t_count_fcr": "", "t_count_days": "", "t_count_day": "", "t_cost_per_pound": "", "t_cost_per_kg": "", "t_update_data": "", "t_weekly_programming": "", "t_feeding_day": "", "t_feeding_day_tooltip": "", "t_monitoring_days": "", "t_manual_weight_generic_validation": "", "t_less_than": "", "t_harvest_optimizer": "", "t_data_updater": "", "t_harvest_type": "", "t_market": "", "t_projected_survival_must_be_future": "", "t_projected_survival_must_be_lower_than_current": "", "t_avg_days_of_culture": "", "t_avg_survival": "", "t_avg_fcr": "", "t_fcr_cumulative": "", "t_farm_summary": "", "t_overview": "", "t_simulation": "", "t_summary": "", "t_more_than": "", "t_x_of_culture": "", "t_selected": "", "t_growth_g_wk": "", "t_growth_g_day": "", "t_growth_monitored_x_week_g_wk": "", "t_feed": "", "t_fcr_weekly": "", "t_filter": "", "t_clear": "", "t_apply": "", "t_group_summary": "", "t_expected_at_harvest": "", "t_customize_view": "", "t_missing_latest_data": "", "t_indicating_status": "", "t_financials": "", "t_data_shown_from_last_monitoring": "", "t_data_shown_based_on_current_date": "", "t_days_until_harvest": "", "t_days_of_culture": "", "t_weights": "", "t_feed_given_weekly_kg": "", "t_feed_%_of_biomass": "", "t_animals_in_pond_m2": "", "t_animals_in_pond_ha": "", "t_dispersion_cv": "", "t_yup_should_be_less_than_max": "", "t_config": "", "t_account": "", "t_growth_g_over_d": "", "t_lb_over_ha": "", "t_kg_over_ha": "", "t_over_m2": "", "t_over_ha": "", "t_total_sale_value": "", "t_avg_fcr_cumulative": "", "t_avg_kg_ha_day": "", "t_avg_total_feed_given_kg": "", "t_avg_total_feed_given_lb": "", "t_biomass_lb_total": "", "t_biomass_kg_total": "", "t_biomass_lb_ha": "", "t_biomass_kg_ha": "", "t_biomass_lb_ha_day": "", "t_biomass_kg_ha_day": "", "t_financial": "", "t_avg_abw_g": "", "t_avg_2wk_growth": "", "t_avg_weekly_growth": "", "t_over_ha_over_day": "", "t_kg_over_ha_over_day": "", "t_g_over_wk": "", "t_lb": "", "t_invitation": "", "t_what_is_cv_learning": "", "t_no_farms_found": "", "t_no_ponds_found": "", "t_no_cycles_found": "", "t_last_week_growth": "", "t_this_farm_data_is_outdated": "", "t_some_of_your_ponds_have_not_been_monitored": "", "t_some_of_your_ponds_are_missing_data": "", "t_percent_of_harvest_as_head_on": "", "t_percent_of_head_on_as_class_a": "", "t_percent_of_head_on_as_class_b": "", "t_percent_of_harvest_as_leftover": "", "t_process_leftover_as": "", "t_rejects": "", "t_percent_of_leftover_as_class_a": "", "t_percent_of_leftover_as_class_b": "", "t_percent_of_tail": "", "t_percent_of_harvest_as_headless_class_a": "", "t_percent_of_harvest_as_headless_class_b": "", "t_processors": "", "t_production_targets": "", "t_target_production_cycle": "", "t_stocking_defaults": "", "t_changes_will_apply_to_all_farms": "", "t_group_config_confirmation": "", "t_are_you_sure_you_want_to_save_changes": "", "t_change_confirmation": "", "t_feed_tables": "", "t_feed_table": "", "t_of_biomass_to_feed": "", "t_select_your_default_feed_table": "", "t_add_feed_table": "", "t_duplicated_table_name": "", "t_default": "", "t_avg_stocking_weight_g": "", "t_autofeeders": "", "t_no_autofeeders": "", "t_add_autofeeder_type": "", "t_aerators": "", "t_no_aerators": "", "t_add_aerator_type": "", "t_add_horsepower": "", "t_recorded": "", "t_record": "", "t_delete_planned_partial_harvest": "", "t_delete_recorded_partial_harvest": "", "t_planned_partial_harvest_on": "", "t_planned_harvest_on": "", "t_has_passed": "", "t_partial_harvest_passed": "", "t_harvest_passed_msg": "", "t_edit_harvest_plan": "", "t_data": "", "t_stocking_cost": "", "t_targets": "", "t_primary_feed_brand": "", "t_projections": "", "t_feed_amount": "", "t_processor_price_lists": "", "t_select_your_default_price_list": "", "t_class_a_price_unit": "", "t_class_b_price_unit": "", "t_price_rejection_unit": "", "t_processor": "", "t_enter_price_list_name": "", "t_select_your_default_harvest_type": "", "t_expected_quality_of_harvest_head_on": "", "t_percent_of_leftover_as_headless_class_a": "", "t_percent_of_leftover_as_headless_class_b": "", "t_percent_of_headless_class_a": "", "t_percent_of_headless_class_b": "", "t_expected_quality_of_harvest_headless_direct": "", "t_expected_percent_tail_of_body": "", "t_percent_tail": "", "t_carrying_capacity": "", "t_feed_data": "", "t_weeks": "", "t_no_feed_types_available": "", "t_rev_lb": "", "t_rev_kg": "", "t_profit_lb": "", "t_profit_kg": "", "t_feed_types": "", "t_select_default_feed_to_use_for_cost_projection": "", "t_add_feed_type": "", "t_brand": "", "t_pellet_size_opt": "", "t_cost_over_kg": "", "t_please_select_default_feed_type": "", "t_stocking_density_ha": "", "t_stocking_density_m2": "", "t_autofeeder_brand": "", "t_feed_brand": "", "t_target_config_tooltip": "", "t_farm_book": "", "t_cycle_week": "", "t_revenue_at_harvest": "", "t_biomass_at_harvest": "", "t_weekly_production_data": "", "t_weekly": "", "t_daily": "", "t_monthly": "", "t_percent_compared_to_feed_table": "", "t_carrying_capacity_lb_ha": "", "t_carrying_capacity_kg_ha": "", "t_#_of_autofeeders": "", "t_#_of_aerators": "", "t_autofeeder_protocol": "", "t_next": "", "t_lab_source": "", "t_nauplii_source": "", "t_day_s": "", "t_confirm_stocking_details": "", "t_confirm_targets": "", "t_confirm_cycle_protocol": "", "t_timer": "", "t_time_of_day": "", "t_anytime": "", "t_hydrophone": "", "t_feed_brand_to_use": "", "t_growout_feed_type": "", "t_record_up_to_one_minute": "", "t_general_channel": "", "t_projected": "", "t_aerators_per_ha": "", "t_type_of_aerator": "", "t_pond_setup": "", "t_nursery": "", "t_worse_than_target": "", "t_better_than_target": "", "t_you_dont_have_permission_to_see_this_page": "", "t_no_history_registered_for_any_feed_days": "", "t_upload_report": "", "t_from_feed_table": "", "t_biomass_in_pond_unit": "", "t_biomass_in_pond_unit_ha": "", "t_biomass_include_harvests_unit": "", "t_biomass_include_harvests_unit_ha": "", "t_survival_live": "", "t_survival_include_harvests": "", "t_": "", "t_dashboard": "", "t_profile": "", "t_min": "", "t_max": "", "t_hello": "", "t_filtered_on": "", "t_total_number_of_ponds": "", "t_stocked_ponds": "", "t_dry_ponds": "", "t_current_view": "", "t_not_selected_in_pond_card": "", "t_my_farms": "", "t_select_up_to_x_variables_you_want": "", "t_gps_distance": "", "t_monitoring_distance_from_pond": "", "t_monitoring_location": "", "t_monitoring_distance_to_pond": "", "t_monitoring_x_distance_to_pond": "", "t_monitoring_x_is_near_pond": "", "t_near_pond": "", "t_monitoring_is_near_pond": "", "t_latest_monitoring_location": "", "t_overhead_cost_cumulative": "", "t_overhead_cost_dry_days": "", "t_data_shown_across_all_ponds": "", "t_farm_not_found": "", "t_farm_not_found_desc": "", "t_invitations_for_a_farm": "", "t_payment": "", "t_population": "", "t_settings": "", "t_select_farm_to_continue": "", "t_forgot_password_desc": "", "t_log_in_desc": "", "t_log_out": "", "t_unsubscribe": "", "t_unsubscribe_desc": "", "t_reset_password_desc": "", "t_sign_up": "", "t_verify_email_desc": "", "t_view_invitation": "", "t_nursery_archived_successfully": "", "t_archive_nursery": "", "t_stock_nursery": "", "t_edit_nursery_details": "", "t_add_nursery_pond": "", "t_this_nursery_is_empty": "", "t_nursery_summary": "", "t_transfer_date": "", "t_transfer_pond": "", "t_edit_transfer": "", "t_transfer": "", "t_record_transfer": "", "t_select_input": "", "t_animals_%": "", "t_add_destination": "", "t_transfer_summary": "", "t_cost_millar": "", "t_type_of_stocking": "", "t_destination_ponds": "", "t_destination": "", "t_planned_stocking": "", "t_transfer_history": "", "t_variables": "", "t_invoiced_quantity": "", "t_invoiced_quantity_opt": "", "t_end_of_cycle_survival": "", "t_feed_cost": "", "t_indirect_costs": "", "t_fixed_costs": "", "t_other_costs": "", "t_past_cycle": "", "t_nursery_last_cycle_msg": "", "t_past": "", "t_edit_stocking_information_to_link_transfers": "", "t_add_stocking_information_to_link_transfers": "", "t_you_have_incoming_transfers_to_this_pond_from": "", "t_growout": "", "t_you_are_missing_important_stocking_information": "", "t_add_details_now": "", "t_transferred_on": "", "t_this_pond_has_incoming_transfers_from": "", "t_this_pond_has_accepted_transfers_from": "", "t_is_this_stocking_missing_transfer_information": "", "t_manage_nurseries": "", "t_edit_direct_stocking": "", "t_stock_directly": "", "t_stock_from_transfer": "", "t_would_you_like_to_link_this_cycle_with_those_transfers": "", "t_Total_biomass_transferred": "", "t_transfer_amount": "", "t_feed_cost_per_kg": "", "t_feed_cost_$": "", "t_up_to_date": "", "t_using_imputed_values": "", "t_estimated_values_are_used": "", "t_direct": "", "t_flagged_ponds": "", "t_delete_confirmation": "", "t_are_you_sure_you_want_to_continue": "", "t_created_by": "", "t_head_on_abbreviation": "", "t_head_less_abbreviation": "", "t_expected_feed": "", "t_transfer_confirmation": "", "t_edit_transfer_confirmation": "", "t_transfer_confirmation_info": "", "t_transfer_confirmation_question": "", "t_edit_transfer_confirmation_msg": "", "t_nursery_data_updater": "", "t_transfer_data": "", "t_harvest_type_classification": "", "t_total_transferred_animals_should_be_less_than": "", "t_grams_change": "", "t_count_percent_day": "", "t_kampi": "", "t_until_harvest": "", "t_projections_not_available": "", "t_projections_not_available_for_pond": "", "t_x_days_earlier": "", "t_x_days_later": "", "t_x_day_earlier": "", "t_x_day_later": "", "t_optimal_harvest": "", "t_gps_location_appears_farm_from_the_pond": "", "t_estimate_data_alert_msg": "", "t_overhead_cost_ha_day": "", "t_other_direct_costs": "", "t_revenue_processed_unit": "", "t_manage_projections": "", "t_class_size": "", "t_biomass_kg": "", "t_biomass_lb": "", "t_expected": "", "t_amount_unit": "", "t_pond_summary": "", "t_protocol": "", "t_harvest_summary": "", "t_pond_source": "", "t_autofeeders_ha": "", "t_selected_x": "", "t_comparison_to_target": "", "t_add_manual_weight_entry": "", "t_target": "", "t_mobile_graph_msg": "", "t_rotate_your_phone_content_msg": "", "t_click_here_to_see_graph": "", "t_click_here_to_see_table": "", "t_click_to_see_weekly_production_data": "", "t_last_monitored_on_x": "", "t_range_g": "", "t_range": "", "t_feed_type_percentage_sum_error": "", "t_pond_performance_required": "", "t_variable": "", "t_biomass_processed_without_price": "", "t_i_am_writing_the_message_here": "", "t_update_default_processor": "", "t_delete_processor": "", "t_updated_default_processor_modal_text_1": "", "t_updated_default_processor_modal_text_2": "", "t_delete_processor_modal_text_1": "", "t_delete_processor_modal_text_2": "", "t_no_only_apply_to_future_assignments": "", "t_yes_apply_to_all_ponds_and_farms": "", "t_update_processor_confirmation": "", "t_update_processor_confirmation_text": "", "t_yes_apply": "", "t_no_keep_current_assignments": "", "t_revenue_per_lb_live": "", "t_revenue_per_kg_live": "", "t_revenue_per_lb_include_harvest": "", "t_revenue_per_kg_include_harvest": "", "t_head_on_distribution": "", "t_head_less_distribution": "", "t_harvest_expected": "", "t_performance": "", "t_above_target": "", "t_on_track": "", "t_off_track": "", "t_show_ponds_that_are": "", "t_above": "", "t_above_x_percent_carrying_capacity": "", "t_customize_graph": "", "t_projection_length": "", "t_show_ponds_with_a_carrying_capacity": "", "t_x_ponds_are_above_carrying_capacity": "", "t_x_ponds_are_above_carrying_capacity_singular": "", "t_daily_parameters": "", "t_only_override_weights_when_a_monitoring_has_been_taken": "", "t_no_monitoring": "", "t_revert_to_kampi_weight": "", "t_kampi_weight_x_g": "", "t_kampi_weight_g": "", "t_updated_by_name": "", "t_manual_weight_confirmation": "", "t_manual_weight_confirmation_description": "", "t_daily_param_monitoring_alert": "", "t_daily_param_not_stocked_alert": "", "t_overwrite_weights": "", "t_invalid_average_weight": "", "t_parameter": "", "t_%_of_carrying_capacity": "", "t_data_check": "", "t_ok": "", "t_missing": "", "t_target_type": "", "t_target_survival": "", "t_target_biomass_to_harvest": "", "t_target_cycle_length": "", "t_target_harvest_weight": "", "t_target_fcr": "", "t_projected_feed_type": "", "t_projected_growth_grams": "", "t_projected_growth_days": "", "t_projected_survival_type": "", "t_daily_mortality_percent": "", "t_expected_target_survival": "", "t_expected_target_survival_days": "", "t_feed_data_for_last_monitoring": "", "t_selected_processor": "", "t_selected_processor_exists": "", "t_profit_projection": "", "t_production_projection": "", "t_cycle_comparison_projection": "", "t_active_processor_price_lists": "", "t_active_feed_table": "", "t_active_feed_types": "", "t_monitoring_days_config": "", "t_start_of_week_config": "", "t_farm_production_days": "", "t_farm_weight": "", "t_farm_fcr": "", "t_farm_cost_per_pound": "", "t_farm_profit_per_ha_per_day": "", "t_farm_biomass_lb_ha": "", "t_farm_dry_days": "", "t_farm_growth_density": "", "t_farm_days_in_nursery": "", "t_farm_nursery_survival": "", "t_farm_avg_stocking_weight": "", "t_value": "", "t_using_custom_values_for_projection": "", "t_production_chart_alert": "", "t_show_daily_parameters": "", "t_feed_given_kg": "", "t_feed_given_lg": "", "t_units": "", "t_unit": "", "t_affected_areas_with_biomass_unit": "", "t_edit_daily_feed_msg": "", "t_cycle_comparison": "", "t_benchmark": "", "t_update_plan": "", "t_animals_stocked_ha": "", "t_animals_stocked": "", "t_biomass_from_partials_unit_ha": "", "t_biomass_from_partials_unit": "", "t_biomass_from_final_unit": "", "t_biomass_processed": "", "t_biomass_unpriced": "", "t_date_of_last_partial_harvest": "", "t_partial_harvest_done": "", "t_last_week": "", "t_x_weeks_ago": "", "t_select_variables_you_want_to_be_shown": "", "t_animals_harvested_per_m2": "", "t_animals_harvested_ha": "", "t_production_days": "", "t_using_imputed_values_for_calculations": "", "x_monitoring_location_from_the_pond": "", "x_monitoring_location_near_the_pond": "", "t_no_cycles": "", "t_select_cycle": "", "t_only_add_survival_when_a_monitoring_has_been_taken": "", "t_kampi_mortality": "", "t_total_biomass_biomass_over_ha": "", "t_total_profit_profit_over_x": "", "t_processed": "", "t_harvested": "", "t_selected_variables": "", "t_selected_variables_sort_desc": "", "t_cycle_information": "", "t_revenue_per_unit_processed": "", "t_revenue_per_unit_harvested": "", "t_profit_per_unit_processed": "", "t_profit_per_unit_harvested": "", "t_cost_per_unit_processed": "", "t_cost_per_unit_harvested": "", "t_add_parameter": "", "t_delete_daily_param_alert": "", "t_param_name_already_exists": "", "t_prof_ha_d": "", "t_weekly_linear_growth": "", "t_daily_linear_growth": "", "t_animals_harvested": "", "t_anim_ha": "", "t_anim_m2": "", "t_biomass_lb_partial_harvest": "", "t_biomass_lb_final_harvest": "", "t_bioL_p": "", "t_bioL_f": "", "t_cost_p_lb": "", "t_cost_h_lb": "", "t_rev": "", "t_rev_p": "", "t_rev_h": "", "t_prof": "", "t_kampi_mortality_survival_target": "", "t_kampi_mortality_survival_target_tooltip": "", "t_days_d": "", "t_select_pond": "", "t_count_ponds_plural": "", "t_count_ponds_singular": "", "t_enter": "", "t_drag_and_drop_file_or": "", "t_brows_computer": "", "t_to": "", "t_use_kampi_expected_revenue": "", "t_partial_harvest_confirmation": "", "t_partial_harvest_change_description": "", "t_harvest_pond_confirmation": "", "t_harvest_change_description": "", "t_harvest_plan_change_description": "", "t_partial_harvest_plan_change_description": "", "t_unread_messages": "", "t_all_messages": "", "t_cycle_summary": "", "t_final_harvest": "", "t_general": "", "t_profit_revenue": "", "t_fcr_adjusted": "", "t_no_partial_harvest_alert": "", "t_partial_harvest_x": "", "t_final_harvest_plan": "", "t_final_harvest_recorded": "", "t_partial_harvest_count_plan": "", "t_partial_harvest_count_recorded": "", "t_export": "", "t_export_farm_summary_table": "", "t_export_daily_parameters_table": "", "t_add_farm_overview_data": "", "t_select_a_view": "", "t_total_animals_detected": "", "t_export_weekly_production_data_table": "", "t_your_data_exported_in_xlsx": "", "t_weekly_monitoring_data": "", "t_ponds": "", "t_harvests": "", "t_final": "", "t_partial": "", "t_next_30_d": "", "t_past_30_d": "", "t_type": "", "t_delete_harvest_confirmation": "", "t_you_are_about_to_delete_harvest": "", "t_count_final_count_partial": "", "t_count_plan_count_recorded": "", "t_count_ponds": "", "t_harvest_plan_recorded_msg": "", "t_harvest_plan_planned_msg": "", "t_harvest_recorded": "", "t_count_cycles": "", "t_create_nursery_pond": "", "t_rev_per_pound_processed": "", "t_rev_per_pound_harvested": "", "t_count_per_ha": "", "t_pls_per_g": "", "t_quantity_from_hatcheries": "", "t_notifications": "", "t_push": "", "t_below": "", "t_nearing_carrying_capacity": "", "t_in": "", "t_notifications_and_rule_error": "", "t_carrying_capacity_reached": "", "t_current": "", "t_kampi_notification_center": "", "t_all_notifications": "", "t_read": "", "t_unread": "", "t_sudden_temperature_drop": "", "t_sudden_temperature_increase": "", "t_am_water_temperature_drop": "", "t_pm_water_temperature_drop": "", "t_am_do_value": "", "t_pm_do_value": "", "t_temperature_dropped_notification": "", "t_temperature_increased_notification": "", "t_carrying_capacity_reached_notification": "", "t_carrying_capacity_will_be_reached_notification": "", "t_tomorrow": "", "t_carrying_capacity_reached_on_ponds": "", "t_carrying_capacity_will_be_reached_on_ponds": "", "t_sudden_temperature_change_ponds": "", "t_no_notifications": "", "t_mg_per_L": "", "t_degree_celsius": "", "t_hide_table": "", "t_show_table": "", "t_average_daily_feed": "", "t_bags": "", "t_water_am_temperature_unit": "", "t_temp_am_unit": "", "t_water_pm_temperature_unit": "", "t_temp_pm_unit": "", "t_dissolved_am_oxygen_unit": "", "t_DO_am_unit": "", "t_dissolved_pm_oxygen_unit": "", "t_DO_pm_unit": "", "t_harvest_actions": "", "t_x_final_harvest_title": "", "t_edit_final_harvest_plan_title": "", "t_x_partial_harvest_plan_title": "", "t_price_list_for_pond_cycle_title": "", "t_manage_partial_harvests": "", "t_details": "", "t_harvest_quality": "", "t_standard_price_list_not_applied": "", "t_view_price_list": "", "t_revert_to_default": "", "t_harvest_quality_update": "", "t_delete_price_list_title_desc": "", "t_delete_price_list_title_confirmation": "", "t_x_partial_harvest_title": "", "t_check_price_list_errors": "", "t_please_adjust_your_survival_rate_accordingly": "", "t_survival_at_x": "", "t_edit_final_harvest_plan": "", "t_edit_final_harvest": "", "t_record_final_harvest": "", "t_projected_harvest_details": "", "t_generate_harvest_projections": "", "t_generate": "", "t_edit_harvest_plan_confirmation": "", "t_edit_partial_harvest_plan_confirmation": "", "t_choose_a_date_and_processor": "", "t_newly_computed_harvest_projections": "", "t_refresh": "", "t_mark_as_unread": "", "t_number_of_notifications_selected_singular": "", "t_number_of_notifications_selected_plural": "", "t_manage_partial_harvests_for_pond_cycle": "", "t_this_pond_has_no_pending_transfers": "", "t_daily_parameter_title": "", "t_custom_numeric_parameter": "", "t_manage_notifications": "", "t_overhead_cost_ha_day_tooltip": "", "t_type_name": "", "t_exceeds_biomass_in_pond": "", "t_parameters": "", "t_celsius_c": "", "t_fahrenheit_f": "", "t_liters_L": "", "t_ppt": "", "t_cm": "", "t_pH": "", "t_kampi_growth": "", "t_mortality": "", "t_transparency_unit": "", "t_transp_unit": "", "t_turbidity_unit": "", "t_turb_unit": "", "t_salinity_unit": "", "t_sal_unit": "", "t_alkalinity_unit": "", "t_alk_unit": "", "t_ammonia_nh3_unit": "", "t_nh3_unit": "", "t_nitrite_no2_unit": "", "t_no2_unit": "", "t_nitrate_no3_unit": "", "t_no3_unit": "", "t_magnesium_mg_unit": "", "t_mg_unit": "", "t_calcium_ca_unit": "", "t_ca_unit": "", "t_potassium_k_unit": "", "t_k_unit": "", "t_growth": "", "t_feed_given": "", "t_all_data": "", "t_final_harvest_date": "", "t_source": "", "t_device": "", "t_go_to_summary": "", "t_version": "", "t_os": "", "t_hour": "", "t_minute": "", "t_second": "", "t_seconds": "", "t_millisecond": "", "t_milliseconds": "", "t_no_trays_available": "", "t_showing_x_to_x_of_x_items": "", "t_items": "", "t_page": "", "t_prev_page": "", "t_next_page": "", "t_uploaded": "", "t_upload": "", "t_monitoring_status": "", "t_start": "", "t_end": "", "t_pending_upload": "", "t_overhead_cost_days_of_culture": "", "t_add_new_layout": "", "t_layouts": "", "t_sharing": "", "t_create_new_layout": "", "t_rename_layout": "", "t_sandbox": "", "t_supervisor": "", "t_layout_will_be_shared_across_all_farms": "", "t_duplicate_layout": "", "t_reset_data_confirmation": "", "t_projected_at_final_harvest": "", "t_simulate_projections": "", "t_no_cv_for_manual_monitoring": "", "t_monitoring_id": "", "t_group_performance": "", "t_all_farms": "", "t_filter_farms": "", "t_#_of_cycles": "", "t_average_x_for_completed_cycles_across_farms": "", "t_breakdown": "", "t_all_year": "", "t_completed_cycles": "", "t_past_x_months": "", "t_no_cycles_completed": "", "t_reset_to_last_saved_values": "", "t_reset": "", "t_proceed_confirmation_msg": "", "t_class_size_without_price_set": "", "t_monitoring_strength": "", "t_weak_great_incomplete_desc": "", "t_moderate_great_partial_desc": "", "t_strong_great_complete_desc": "", "t_great_great_great_desc": "", "t_weak_good_incomplete_desc": "", "t_moderate_good_partial_desc": "", "t_strong_good_complete_desc": "", "t_strong_good_great_desc": "", "t_weak_below_standard_incomplete_desc": "", "t_moderate_below_standard_partial_desc": "", "t_moderate_below_standard_complete_desc": "", "t_moderate_below_standard_great_desc": "", "t_weak_poor_incomplete_desc": "", "t_weak_poor_partial_desc": "", "t_weak_poor_complete_desc": "", "t_weak_poor_great_desc": "", "t_weak": "", "t_moderate": "", "t_strong": "", "t_great": "", "t_incomplete": "", "t_complete": "", "t_below_standard": "", "t_good": "", "t_poor": "", "t_quality": "", "t_completeness": "", "t_x_usual_shrimps_for_weights_out_x_counted": "", "t_carrying_capacity_usage": "", "t_do_am_grouping_msg": "", "t_do_pm_grouping_msg": "", "t_temperature_am_grouping_msg": "", "t_temperature_pm_grouping_msg": "", "t_reaching_full_capacity": "", "t_full_capacity_reached": "", "t_water_temperature_low": "", "t_water_temperature_high": "", "t_do_am_low": "", "t_do_pm_low": "", "t_do_am_high": "", "t_do_pm_high": "", "t_x_daily_parameters_created": "", "t_biomass_in_pond_kg_ha": "", "t_biomass_in_pond_lb_ha": "", "t_stocking_weight": "", "t_label": "", "t_comment": "", "t_tray_annotations_created": "", "t_farm_breakdown": "", "t_farm_breakdown_description": "", "t_change": "", "t_no_harvests_recorded_this_month": ""}