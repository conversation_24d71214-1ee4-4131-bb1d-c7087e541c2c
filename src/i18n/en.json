{"t_home": "Home", "t_accept": "Accept", "t_access": "Access", "t_account_details": "Account details", "t_active": "Active", "t_add": "Add", "t_add_a_farm": "Add a farm", "t_add_farm": "Add farm", "t_add_pond": "Add pond", "t_add_user": "Add user", "t_admin": "Admin", "t_user": "User", "t_operations_supervisor": "Operations supervisor", "t_operations_supervisor_can_see": "Operations supervisor (can see financials)", "t_production_supervisor": "Production supervisor (can't see financials)", "t_already_have_an_account": "Already have an account", "t_and": "and", "t_animals": "Animals", "t_archive_pond": "Archive pond", "t_back_to_log_in": "Back to log in", "t_bulk_invite": "Bulk invite", "t_by_clicking_create_account": "By clicking 'Create account' you agree to our", "t_cancel": "Cancel", "t_cancel_invitation": "Cancel invitation", "t_change_password": "Change password", "t_close": "Close", "t_code": "Code", "t_confirm": "Confirm", "t_confirm_archive": "Are you sure you want to archive?", "t_confirm_new_password": "Confirm new password", "t_confirm_password": "Confirm password", "t_contact_phone_no": "Contact phone no", "t_continue": "Continue", "t_create": "Create", "t_react_select_create_label": "Create \"{{value}}\"", "t_create_account": "Create account", "t_create_invitation": "Create invitation", "t_created_at": "Created at", "t_cv": "CV", "t_cycle": "Cycle", "t_cycle_number": "Cycle number", "t_date": "Date", "t_day": "day", "t_days": "days", "t_days_2": "Days", "t_hours": "Hours", "t_minutes": "Minutes", "t_count_days_of_culture": "{{count}} days of culture", "t_did_not_receive_code": "You didn't receive the code?", "t_distribution": "Distribution", "t_edit_access": "Edit access", "t_edit_pond": "Edit pond", "t_edit_nursery": "Edit nursery", "t_edit_stocking": "Edit stocking", "t_email": "Email", "t_enter_the_code_here": "Enter the code here", "t_farm": "Farm", "t_female": "Female", "t_finished_at": "Finished at", "t_forgot_password": "Forgot password", "t_forgot_password_description": "Enter the email address associated with your account and we will send you a code to reset your password", "t_gender": "Gender", "t_ha": "ha", "t_harvest": "Harvest", "t_harvest_date": "Harvest date", "t_id": "ID", "t_in_process": "In process", "t_invitations": "Invitations", "t_invite_new_user": "Invite new user", "t_invited": "Invited", "t_invited_at": "Invited at", "t_invited_date_time": "Invited {{dateTime}}", "t_is_active": "Is active?", "t_join_via_invitation": "Join via invitation", "t_last_active": "Last active", "t_language": "Language", "t_loading": "Loading", "t_log_in": "Log in", "t_manage_preferences": "To manage other email preferences, please visit your", "t_preferences_updated_successfully": "Your preferences have been updated successfully", "t_email_settings": "<PERSON><PERSON>s", "t_unsubscribe_message": "You have been successfully unsubscribed. You will no longer receive email for this type of notification.", "t_log_in_to_your_account": "Log in to your account", "t_login_password": "Login password", "t_logout": "Logout", "t_male": "Male", "t_manage_monitoring": "Manage monitoring", "t_message": "Message", "t_monitoring": "Monitoring", "t_monitorings": "Monitorings", "t_my_account": "My account", "t_name": "Name", "t_new_password": "New password", "t_new_pond": "New pond", "t_new_user": "New user", "t_no_data_to_display": "No data to display", "t_no_farms": "No farms", "t_no_invitations": "No invitations", "t_no_payments": "No payments", "t_plan": "Plan", "t_provider": "Provider", "t_payment_history": "Payment history", "t_no_monitoring_available": "No monitoring available", "t_not_active": "Not active", "t_not_have_account": "Would you like to find out more about <PERSON><PERSON><PERSON>'s platform?", "t_contact_us": "Contact us", "t_not_receive_the_code": "If you didn't receive the code", "t_old_password": "Old password", "t_oops": "Oops", "t_optional": "Optional", "t_or": "or", "t_other": "Other", "t_password": "Password", "t_phone_number": "Phone number", "t_photo_count": "Photo {{count}}", "t_photos": "Photos", "t_pond": "Pond", "t_pond_name_validation_msg": "Numbers, letters and -, 15 characters max", "t_nursery_name_validation_msg": "Numbers, letters and -, 15 characters max", "t_pond_size": "Pond size", "t_past_cycle_summary": "Past cycle summary", "t_abw_at_transfer": "ABW at transfer", "t_privacy_policy": "Privacy policy", "t_processing_status": "Processing status", "t_quantity": "Quantity", "t_received_invitation": "You have been invited to join the", "t_invited_to_farm": "farm by user", "t_reject": "Reject", "t_resend_code": "Resend code", "t_reset_code": "Reset code", "t_reset_password": "Reset password", "t_reset_password_description": "We have sent you an email containing your password reset code", "t_role": "Role", "t_save": "Save", "t_search_by_user_farm_or_status": "Search by user, farm or status", "t_select_existing_monitoring": "Select existing monitoring", "t_select_existing_or_enter_new_email": "Select existing or enter new email", "t_select_farm": "Select farm", "t_select_file": "Select file", "t_select_role": "Select role", "t_send": "Send", "t_sign_up_instantly": "Sign up instantly", "t_start_new_monitoring": "Start new monitoring", "t_status": "Status", "t_stock": "Stock", "t_stock_pond": "Stock pond", "t_stocking": "Stocking", "t_submit": "Submit", "t_submit_monitoring": "Submit monitoring", "t_submitted": "Submitted", "t_supervisor_optional": "Supervisor (opt)", "t_terms_of_service": "Terms of service", "t_today": "Today", "t_total": "Total", "t_total_images": "Total images", "t_trays": "Trays", "t_type_of_access": "Type of access", "t_unknown": "Unknown", "t_update": "Update", "t_uploaded_at": "Uploaded at", "t_delete": "Delete", "t_verification_code": "Verification code", "t_verify_email": "Verify email", "t_verify_email_description": "We have sent you an email containing your verification link", "t_verify_your_email": "Verify your email", "t_verify_your_email_address": "Verify your email address", "t_view_images": "View images", "t_weight_distribution": "Weight distribution", "t_yesterday": "Yesterday", "t_email_sent_successfully_check": "Email was sent successfully, please check your email", "t_sms_sent_successfully_check_mobile": "SMS was sent successfully, please check your phone", "txt_email_schema_error": "Email must be at least 7 characters (ex: <EMAIL>)", "txt_password_mismatch": "Password and Confirm Password does not match", "txt_passwords_reset_does_not_match": "New Password and Confirm New Password does not match", "yup_email_is_invalid": "<PERSON><PERSON> is invalid", "yup_enter_minimum_characters": "Please enter minimum {{min}} characters", "yup_is_invalid": "{{label}} is invalid", "yup_is_required": "{{label}} is a required field", "yup_not_type": "Invalid type, {{label}} must be a '{{type}}' type", "yup_should_be_greater_than_more": "{{label}} should be greater than {{more}}", "yup_should_be_greater_than_zero": "{{label}} should be greater than 0", "yup_should_be_one_of": "{{label}} should be one of {{values}}", "yup_should_not_be_less_than": "{{label}} should not be less than {{min}}", "yup_should_be_less_than_or_equal": "{{label}} should be less than or equal to {{max}}", "yup_should_be_integer": "{{label}} should be an integer", "yup_date_must_not_be_later_than": "{{label}} must not be later than {{date}}", "yup_date_must_not_be_earlier_than": "{{label}} must not be earlier than {{date}}", "t_heads_on": "Heads on", "t_heads_off": "Heads off", "t_download": "Download", "t_days_ago_plural": "{{count}} days ago", "go_back": "Go back", "t_something_went_wrong": "Something went wrong", "t_actions": "Actions", "t_data_from_previous_cycle": "You are viewing a past cycle, re-stock your pond to start monitoring again.", "t_err_account_not_found": "There is no account linked to that email", "t_err_code_is_invalid": "Reset code is invalid", "t_err_email_or_password_incorrect": "Email or password is incorrect", "t_err_user_not_found": "Unable to find the user", "t_err_email_already_in_use": "Email is already in use", "t_err_unauthorized": "Unauthorized", "t_err_pond_not_found": "Pond not found", "t_stocking_date": "Stocking date", "t_processing": "Processing", "t_processing_failed": "Processing failed", "t_delete_monitoring": "Exclude monitoring", "t_delete_monitoring_msg": "If you exclude this monitoring, it will not be included in the daily data and will not be used for alerts or projections.", "t_delete_monitoring_toast_title": "Monitoring excluded", "t_delete_monitoring_toast_msg": "Your monitoring was successfully excluded", "t_error": "Error", "t_page_not_found": "Page not found", "t_go_home": "Go home", "t_success": "Success", "t_verification_failed": "Verification failed", "t_login_to_accept_invite": "You need to login to accept the invitation", "t_code_sent": "Code was sent successfully, please check your phone", "t_accepted_invitation": "You have successfully accepted the invitation", "t_account_verified": "You have successfully verified your account", "t_logged_in_success_title": "Log in successful", "t_logged_in_success_msg": "You have successfully logged in", "t_profile_updated": "You have successfully updated your profile", "t_updated_successfully": "Updated successfully", "t_invitation_email_sent": "Invitation email has been sent", "t_user_membership_updated": "User membership updated successfully", "t_invitation_rejected": "You have successfully rejected the invitation", "t_created_successfully": "Created successfully", "t_invitation_created": "You have successfully created an invitation", "t_password_changed": "Password changed successfully", "t_password_reset_success": "You have successfully reset your password and logged in", "t_invitation_cancelled_title": "Invitation canceled successfully", "t_invitation_cancelled_msg": "You have successfully canceled an invitation", "t_phone_number_updated": "Phone number updated successfully", "t_email_updated": "Email updated successfully", "t_monitoring_submitted": "Monitoring submitted successfully", "t_manual_monitoring_created": "Manual monitoring created successfully", "t_logged_in_success": "You have successfully logged in", "t_change_your_email": "Change your email", "t_contact_admin_to_change_email": "Please contact your farm's administrator to change your email address.", "t_farm_must_be_unique": "Farm name must be unique", "t_not_invite_yourself": "You can not invite yourself", "t_percentage_sampled_animals": "{{percent}} of sampled animals are in a {{grams}} range ({{range}})", "t_aggregation_in_progress": "Aggregation in progress, please check back in 20 seconds", "t_get_history_and_growth": "Refresh Growth - DEV", "t_percentage_of_stocked_animals": "% of stocked animals", "t_select_or_create_monitoring": "Please select an existing or create a monitoring from above", "t_cv_learning": "The Coefficient of Variation (CV) is a way to measure the number of sizes in a pond, and if your shrimp are feeding evenly.", "t_cv_description_high": "When CV is high – you have a large variation of sizes in the pond, indicating that the pond is feeding unevenly.", "t_cv_description_low": "When CV is low – fewer sizes in the pond, closer to the average. This indicates your feed strategy is working nicely.", "t_cv_fixing_distribution": "Detecting and fixing high distribution", "t_cv_xpertsea_alert": "Ka<PERSON><PERSON> will alert you when a pond's CV is high or very high for its average body weight.", "t_then_you_can": "Then you can:", "t_cv_review_pond_histogram": "Review the pond's histogram to better understand the situation", "t_cv_consider_following_our_tips": "Consider following our tips to improve your distribution", "t_cv_tips_to_improve": "Tips to improve your distribution", "t_cv_how_cv_calculated": "How CV is calculated", "t_cv_how_determined": "How we determine if CV is high", "t_cv_select_the_average_body_weight": "Select the average body weight", "t_at": "at", "t_after": "After", "t_consider_the_following": "Consider doing the following:", "t_cv_05_description": "it is the cheapest period to improve your distribution, meaning that you can act sooner to maximize your survival.", "t_cv_10_description": "it is still relatively cheap to ensure a good distribution, which then sets up the pond for a strong growth cycle.", "t_cv_15_description": "optimize your growth by feeding all of the different sizes in your pond, not just the average size.", "t_cv_15_plus_description": "you can reduce the dispersion of sizes with 2-3 weeks of focused feeding as you prepare for harvest.", "t_cv_step_analyze": "Analyze the pond's histogram on Kampi to see how many shrimp are too small.", "t_cv_step_analyze_to_understand": "Analyze the pond's histogram on Kampi to understand the distribution in your pond.", "t_cv_step_encourage": "Encourage smaller shrimp to eat more by using the right pellet sizes. Use the histogram to determine the amount of each pallet size to use.", "t_cv_step_encourage_then": "Encourage smaller shrimp to eat more by using the right pellet sizes. If the shrimp aren't near auto feeders, do this in areas where they are currently found.", "t_cv_step_shrimp_far": "If shrimp are far away from auto feeders, lure them towards the auto feeder zones with manual feeding.", "t_cv_step_smaller_shrimp_far": "If smaller shrimp are far away from auto feeders, then lure them towards the auto feeder zones with manual feeding.", "t_cv_step_ensure": "Ensure that there are enough auto feeders on the pond, that they are positioned optimally, and that they are function reliably.", "t_cv_step_feed_away": "Feed away from areas with unhealthy pond bottoms and keep the pond bottom clear of decomposed organic material.", "t_cv_step_collect": "Collect larger samples during monitoring (>300 shrimps) to increase your confidence in the sizes that you will sell to the processor.", "t_cv_step_focus": "Focus your feeding on smaller shrimp, targeting feeding in the areas of the pond where they are found.", "t_cv_step_use": "Use a raleo to remove larger shrimp to give smaller shrimp more space to grow.", "t_cv_calculation_description": "Calculating CV starts with the calculation of the standard deviation of a sample.", "t_cv_calculation_description_long": "Standard deviation (SD) is determined by first calculating the differences between the weight of each shrimp and the average weight of all shrimp. These differences are then converted into a single number, the SD, that represents the variation of these values.", "t_cv_calculation_description_short": "CV is then calculated by dividing SD by the average weight. This allows us to compare CV for different weights of shrimp.", "t_cv_calculation_examples": "Examples for 9g shrimp:", "t_cv_low": "CV 10 %", "t_cv_sd_low": "0.9 SD", "t_cv_medium": "CV 20 %", "t_cv_sd_medium": "1.8 SD", "t_cv_high": "CV 28 %", "t_cv_sd_high": "2.5 SD", "t_cv_high_description": "<PERSON><PERSON><PERSON> has analyzed over 4.5 million images of shrimp to determine what CV values typically are throughout the shrimp's growth cycle.", "t_cv_high_description_long": "On Kampi, we compare the CV from your monitoring to this previous data and we alert you if it is High or Very High:", "t_cv_high_yellow": "High (yellow)", "t_cv_high_yellow_desc": "- It is higher than 51 % of our past samples for the same average weight", "t_cv_very_high_red": "Very high (red)", "t_cv_very_high_red_desc": "- It is higher than 85 % of our past samples for the same average weight", "t_unharvest_pond": "Unharvested pond (DEV)", "t_dispersion": "Dispersion", "t_target_weight": "Target weight", "t_days_of_production": "Days of production", "t_target_harvest_date": "Target harvest date", "t_set_target": "Set target", "t_show_users_all_farms": "Show users for all farms", "t_edit": "Edit", "t_try_again": "Try again", "t_target_at_risk": "Target at risk", "t_remove": "Remove", "t_confirm_remove_target": "Are you sure you want to remove the target for this pond?", "t_set_later": "Later", "t_needs_attention": "Needs attention", "t_select_date": "Select date", "t_average_weight_should_be_less_than_first_monitoring_average_weight": "Average weight should be less than {{firstMonitoringAverageWeight}}", "t_growth_rate_since_monitoring": "Growth rate since last monitoring", "t_growth_in": "{{growth}}g growth in {{daysString}}", "t_growth_day": "Growth / day = {{dailyGrowth}} g", "t_weekly_growth_seven_days": "Extrapolated weekly growth rate = {{lastWeekGrowth}} g", "t_min_monitored_weight": "Must be greater than the last monitored weight ({{weight}} g)", "t_min_target_weight": "Must be at least {{weight}} g", "t_max_target_weight": "Must be fewer than {{weight}} g", "t_min_target_stocking_days": "Must be greater than the days since stocking ({{days}} days)", "t_min_target_days": "Must be at least {{days}} days", "t_integer_target_days": "Cannot have a decimal (e.g. 90)", "t_growth_warning_prefix": "This target will require an average growth rate of", "t_growth_warning_rate": "{{rate}} g.", "t_growth_warning_suffix": "Either edit your target or check here to confirm and continue.", "t_target_harvest_date_updated": "The Target harvest date below has been updated to be <b>{{days}}</b> days after your new Stocking date.", "t_target_harvest_date_updated_confirm": "Confirm or edit the target and click Update.", "t_set_survival_rate": "Enter", "t_survival_form_title": "Survival - {{date}}", "t_enter_survival": "Enter survival", "t_edit_survival": "Edit survival", "t_survival_final_harvest": "Survival - Final harvest", "t_survival": "Survival", "t_kampi_survival": "Kampi survival", "t_biomass": "Biomass", "t_survival_rate_tooltip": "Enter the survival each day that you monitor to get the most accurate calculation of biomass.", "t_biomass_tooltip": "Biomass is shown after you enter the survival. It is calculated as: Biomass = (Number of PLs at stocking) × (Average weight) × (Survival)", "t_stocking_weight_g": "Stocking weight (g)", "t_processing_failed_tooltip_title": "Your photos have been uploaded but processing failed.", "t_processing_failed_tooltip_subtitle": "Select Try again. If it still doesn't process correctly, contact <PERSON><PERSON><PERSON>.", "t_pallet_size": "Pellet size", "t_practical_analysis_error": "There shouldn't be any duplicate weight or pellet size values", "t_practical_modal_header": "Input at what weights the particle size is increased on your farm.", "t_practical_analysis_tooltip_header": "Feed particle analysis", "t_practical_analysis_tooltip_desc": "Enter weight in which the particle size changes to establish the biomass corresponding to each particle size.", "t_cancel_save_changes": "Cancel save changes", "t_cancel_save_changes_confirmation": "Are you sure you don't want to save your changes?", "t_weight_error": "Weight must be bigger than previous weight", "t_weight_g": "Weight (g)", "t_confirm_remove_feed_pallets": "Do you confirm the removal of Feed Pallet from all ponds on your farm?", "t_invalid_weight": "Your ABW seems to be > 40g or < 2g. We are actively working on expanding to support weights in this range, but for now we are not able to show data when ABW is > 40g or < 2 g.", "t_growth_ahead": "Growth ahead of target!", "t_on_track_to_hit_target": "On track to hit target", "t_check_stock": "Check stocking info", "t_check_sample": "Check sample", "t_feed_type": "Feed type", "t_last_updated_on": "Last updated on", "t_cost": "Cost", "t_cost_$": "Cost ($)", "t_kg": "kg", "t_select": "Select", "t_size": "Size", "t_daily_mortality": "Daily mortality", "t_abw": "ABW", "t_abw_g": "ABW (g)", "t_no_field_duplicated_values": "Please ensure that each {{field}} value is unique", "t_planned": "Planned", "t_left": "Left", "t_right": "Right", "t_save_changes": "Save changes", "t_select_option": "Select option", "t_no_data_available": "No data available", "t_monday": "Monday", "t_tuesday": "Tuesday", "t_wednesday": "Wednesday", "t_thursday": "Thursday", "t_friday": "Friday", "t_saturday": "Saturday", "t_sunday": "Sunday", "t_no_options": "No options", "t_fcr": "FCR", "t_setting_pond_bottom_msg": "Get started by monitoring your pond", "t_pond_monitoring_after_harvest": "Last monitoring was past the planned harvest date. Update your harvest plan now to receive projections", "t_required": "*Required", "t_normal": "Normal", "t_err_file_size_limit_exceeded": "File size limit exceeded", "t_symmetry": "Symmetry", "t_symmetry_left_title": "Animals under-classified", "t_symmetry_left_info": "Improve your harvest by ${{revenueLoss}} /lb by feeding to correct asymmetry! {{animalsPercentBelowABW}}% of animals are below the average weight, with {{animalsPercentBelowHeuristic}}% of animals more than {{heuristicGramsBelowABW}} grams below your average weight, reducing your harvest classification.", "t_symmetry_right_title": "Small animals holding back your FCR", "t_symmetry_right_info": "{{animalsPercentBelowHeuristic}}% of animals are more than {{heuristicGramsBelowABW}} grams below below your average weight. Focus on growing these animals to improve the average weight and FCR of the pond.", "t_symmetry_normal_info": "Animals are distributed evenly across the average", "t_symmetry_few_animals_note": "Sample size too small, increase animals sampled to at least 150 to increase confidence in distribution statistics.", "t_g_in_days": "{{growth}} g/{{days}}d", "t_record_partial_harvest": "Record partial harvest", "t_plan_partial_harvest": "Plan partial harvest", "t_biomass_harvested_unit_ha": "Biomass - Harvested ({{unit}}/ha)", "t_biomass_harvested": "Biomass - Harvested", "t_harvest_plan": "Harvest plan", "t_estimated_animals_harvested": "Estimated animals harvested", "t_animals_harvested_%": "Animals harvested (%)", "t_partial_harvest_quantity_error": "Animals harvested must be lower than total animals and not zero", "t_add_notes": "Add notes", "t_start_monitoring": "Start monitoring", "t_edit_manual_monitoring": "Edit manual monitoring", "t_show_me_how": "Show me how", "t_back": "Back", "t_head_on": "Head-on", "t_headless": "Headless", "t_configuration": "Configuration", "t_search": "Search", "t_survival_%_after_partial_harvest": "Survival % after partial harvest", "t_survival_will_be_assigned_to": "Survival will be assigned to the monitoring of {{date}}", "t_survival_will_not_be_assigned_to_monitoring": "Survival will not be assigned to monitoring - No monitorings before {{date}}", "t_partial_harvest": "Partial harvest", "t_x_harvested_on": "{{percent}}% harvested on {{date}}", "t_ensure_survival_included": "Ensure survival includes partial harvests", "t_ensure_survival_included_tooltip": "E.g. survival being entered has % of animals partial harvested subtracted, partially harvested animals = dead animals", "t_growth_rate": "Growth rate", "t_revenue": "Revenue ($)", "t_users_at_farm": "Users at {{farmName}}", "t_users_at_all_farms": "Users at all farms", "t_stock_your_pond_to_start_getting": "Stock your pond to start getting all the insights from <PERSON><PERSON><PERSON>", "t_stock_your_nursery_to_start_getting": "Stock your nursery to start getting all the insights from <PERSON><PERSON><PERSON>", "t_pond_actions": "Pond actions", "t_edit_pond_details": "Edit pond details", "t_pond_archived_successfully": "Pond archived successfully", "t_present": "Present", "t_projection_variables": "Projection variables", "t_profit_include_dry_days_ha_day": "Profit - Include dry days ($/ha/day)", "t_partial_harvest_revenue": "Partial harvest revenue", "t_add_price_list": "Add price list", "t_empty_cycle": "Empty cycle", "t_dry_days": "Dry days", "t_days_in_nursery": "Days in nursery", "t_nursery_survival": "Nursery survival", "t_larva_order_time": "Larva order time", "t_price_list": "Price list", "t_pond_cost": "pond cost", "t_min_allowed_stocked": "Stocked at date cannot be older than 2 years", "t_profit": "Profit ($)", "t_rofi": "Return on feed investment (%)", "t_last_updated_on_by": "Last updated on {{date}} by {{name}}", "t_add_lab_name": "Add lab name", "t_monitoring_updated_successfully": "Monitoring updated successfully", "t_production": "Production", "t_number_of_images": "Number of images", "t_buy_images": "Buy {{sliderValue}} images ({{totalAmount}} excluding taxes)", "t_use_slider_or_input_number_of_images": "Use slider or manually input the number of images that you want to purchase", "t_weight_stocked_on_date": "{{weight}}g stocked on {{date}}", "t_revenue_per_pound": "Revenue ($/lb)", "t_revenue_per_kg": "Revenue ($/kg)", "t_no_lab_names": "No lab names", "t_no_genetic_names": "No genetic names", "t_my_team": "My team", "t_planned_harvest": "Planned harvest", "t_recorded_harvest": "Recorded harvest", "t_no_growth_data": "We have no growth data for this pond", "t_enter_weekly_growth_data": "Enter weekly growth data", "t_enter_growth_data_or_monitor_pond": "Enter your weekly growth data or monitor your pond to see profit results!", "t_no_past_cycles": "No past cycles", "t_your_pond_is_empty": "Your pond is currently empty", "t_stock_pond_to_get_started": "Stock your pond now to get started", "t_stock_your_pond": "Stock your pond", "t_last_monitoring": "Last monitoring", "t_latest_monitoring": "Latest monitoring", "t_current_biomass": "Current biomass", "t_planned_final_harvest": "Planned final harvest", "t_projected_profit": "Projected profit ($)", "t_projected_revenue": "Projected revenue ($)", "t_cost_to_date_estimated": "Cost to date (estimated)", "t_feed_given_daily_kg_ha": "Feed given - Daily (kg/ha)", "t_nurseries": "Nurseries", "t_harvest_weight_g": "Harvest weight (g)", "t_cycle_length": "Cycle length", "t_add_liquidation_report": "Add liquidation report", "t_liquidation_report_format": "Format: we accept .xlsx, .csv or .pdf files, maximum 5MB", "t_record_harvest": "Record harvest", "t_custom": "Custom", "t_cycle_info": "Cycle info", "t_add_new_size": "Add new size", "t_no_ponds_in_your_farm": "No ponds in your farm", "t_no_nurseries_in_your_farm": "No nurseries in your farm", "t_create_a_new_pond": "Create a new pond now to start setting up your farm!", "t_create_a_new_nursery": "Create a new nursery now!", "t_end_of_cycle": "End of cycle", "t_hint_you_can_copy_and_paste_from_excel": "Hint: you can copy and paste your excel table here", "t_add_weight": "Add weight", "t_overhead_cost_tooltip": "The overhead cost of your farm including rent & building costs, labour and equipment depreciation.", "t_gram_g": "g", "t_max_x": "Max {{max}}", "t_min_x": "Min {{min}}", "t_fcr_tooltip": "The remaining feed for this cycle will be estimated based on your current Feed Conversion Ratio (FCR) and the FCR predicted by <PERSON><PERSON><PERSON>'s algorithm.", "t_kg_ha_tooltip": "The feed amount given daily in kilograms (kg) per hectare (ha) of pond for the remainder of the cycle.", "t_field_should_be_less_or_equal_to_x": "{{field}} should be less or equal to {{max}}", "t_count_fcr": "{{count}} FCR", "t_count_days": "{{count}} days", "t_count_day": "{{count}} day", "t_cost_per_pound": "Cost ($/lb)", "t_cost_per_kg": "Cost ($/kg)", "t_update_data": "Update data", "t_weekly_programming": "Weekly programming", "t_feeding_day": "Feeding day", "t_feeding_day_tooltip": "The day that you start your week for feed planning", "t_monitoring_days": "Monitoring days", "t_manual_weight_generic_validation": "Please ensure all cells in the table have been populated with accurate information", "t_less_than": "Less than", "t_harvest_optimizer": "Harvest optimizer", "t_data_updater": "Data updater", "t_harvest_type": "Harvest type", "t_market": "Market", "t_projected_survival_must_be_future": "Projected survival must be set in the future.", "t_projected_survival_must_be_lower_than_current": "Projected survival must be lower than the current survival.", "t_avg_days_of_culture": "Avg. days of culture", "t_avg_survival": "Avg. survival", "t_avg_fcr": "Avg. FCR", "t_fcr_cumulative": "FCR - Cumulative", "t_farm_summary": "Farm summary", "t_overview": "Overview", "t_simulation": "Simulation", "t_summary": "Summary", "t_more_than": "More than", "t_x_of_culture": "{{dayString}} of culture", "t_selected": "{{selected}} / {{total}} selected", "t_growth_g_wk": "Growth (g/wk)", "t_growth_g_day": "Growth (g/day)", "t_growth_monitored_x_week_g_wk": "Growth - Monitored {{weeks}} wk (g/wk)", "t_feed": "Feed", "t_fcr_weekly": "FCR - 1wk", "t_filter": "Filter", "t_clear": "Clear", "t_apply": "Apply", "t_group_summary": "Group summary", "t_expected_at_harvest": "Expected at harvest", "t_customize_view": "Customize view", "t_missing_latest_data": "Missing the latest data update for the most recent monitoring. {{extra}}", "t_indicating_status": "Indicating status", "t_financials": "Financials", "t_data_shown_from_last_monitoring": "Data shown from last monitoring", "t_data_shown_based_on_current_date": "Data shown based on current date", "t_days_until_harvest": "Days until harvest", "t_days_of_culture": "Days of culture", "t_weights": "Weights", "t_feed_given_weekly_kg": "Feed given - Weekly (kg)", "t_feed_%_of_biomass": "Feed % of biomass", "t_animals_in_pond_m2": "Animals - In pond /m2", "t_animals_in_pond_ha": "Animals - In pond /ha", "t_dispersion_cv": "Dispersion - CV", "t_yup_should_be_less_than_max": "{{label}} should be less than {{max}}", "t_config": "Config", "t_account": "Account", "t_growth_g_over_d": "{{growth}} g/d", "t_lb_over_ha": "lb/ha", "t_kg_over_ha": "kg/ha", "t_over_m2": "/m2", "t_over_ha": "/ha", "t_total_sale_value": "Total sale value", "t_avg_fcr_cumulative": "Avg. FCR (cumulative)", "t_avg_kg_ha_day": "Avg. Kg / ha / day", "t_avg_total_feed_given_kg": "Avg total feed given (kg)", "t_avg_total_feed_given_lb": "Avg total feed given (lb)", "t_biomass_lb_total": "Biomass - Total (lb)", "t_biomass_kg_total": "Biomass - Total (kg)", "t_biomass_lb_ha": "Biomass (lb/ha)", "t_biomass_kg_ha": "Biomass (kg/ha)", "t_biomass_lb_ha_day": "Biomass lb / ha / day", "t_biomass_kg_ha_day": "Biomass kg / ha / day", "t_financial": "Financial", "t_avg_abw_g": "Avg. ABW (g)", "t_avg_2wk_growth": "Avg. 2wk growth", "t_avg_weekly_growth": "Avg. weekly growth", "t_over_ha_over_day": "/ ha / day", "t_kg_over_ha_over_day": "kg / ha / day", "t_g_over_wk": "g/wk", "t_lb": "lb", "t_invitation": "Invitation", "t_what_is_cv_learning": "What is CV Learning?", "t_no_farms_found": "No farms found", "t_no_ponds_found": "No ponds found", "t_no_cycles_found": "No cycles found", "t_last_week_growth": "{{lastWeekGrowth}} g/wk", "t_this_farm_data_is_outdated": "This farm's data is outdated! Last monitoring was > 7 days ago. Monitor your farm now.", "t_some_of_your_ponds_have_not_been_monitored": "Some of your ponds haven't been monitored in > 7 days, monitor them now.", "t_some_of_your_ponds_are_missing_data": "Some of your ponds are missing data. {{extra}}", "t_percent_of_harvest_as_head_on": "% of harvest as head-on", "t_percent_of_head_on_as_class_a": "% of head-on as class A", "t_percent_of_head_on_as_class_b": "% of head-on as class B", "t_percent_of_harvest_as_leftover": "% of harvest as leftover", "t_process_leftover_as": "Process leftover as", "t_rejects": "Rejects", "t_percent_of_leftover_as_class_a": "% of leftover as headless class A", "t_percent_of_leftover_as_class_b": "% of leftover as headless class B", "t_percent_of_tail": "% of tail", "t_percent_of_harvest_as_headless_class_a": "% of harvest as headless class A", "t_percent_of_harvest_as_headless_class_b": "% of harvest as headless class B", "t_processors": "Processors", "t_production_targets": "Production targets", "t_target_production_cycle": "Target Production Cycle", "t_stocking_defaults": "Stocking defaults", "t_changes_will_apply_to_all_farms": "Changes will apply to all farms in the group:", "t_group_config_confirmation": "Changes you made will apply to all farms in the group.", "t_are_you_sure_you_want_to_save_changes": "Are you sure you want to save changes?", "t_change_confirmation": "Change confirmation", "t_feed_tables": "Feed tables", "t_feed_table": "Feed table", "t_of_biomass_to_feed": "% of biomass to feed", "t_select_your_default_feed_table": "Select your default feed table", "t_add_feed_table": "Add feed table", "t_duplicated_table_name": "Table name already exists", "t_default": "<PERSON><PERSON><PERSON>", "t_avg_stocking_weight_g": "Avg. stocking weight (g)", "t_autofeeders": "Autofeeders", "t_no_autofeeders": "No autofeeders", "t_add_autofeeder_type": "Add autofeeder type", "t_aerators": "Aerators", "t_no_aerators": "No aerators", "t_add_aerator_type": "Add aerator type", "t_add_horsepower": "Add horsepower", "t_recorded": "Recorded", "t_record": "Record", "t_delete_planned_partial_harvest": "Are you sure you want to delete this planned partial harvest?", "t_delete_recorded_partial_harvest": "Are you sure you want to delete this recorded partial harvest?", "t_planned_partial_harvest_on": "Planned partial harvest on", "t_planned_harvest_on": "Planned harvest on", "t_has_passed": "has passed", "t_partial_harvest_passed": "Record your partial harvest now to have it accounted in your profit calculations or edit your partial harvest plans.", "t_harvest_passed_msg": "Record your harvest or edit your plan", "t_edit_harvest_plan": "Edit harvest plan", "t_data": "Data", "t_stocking_cost": "Stocking cost", "t_targets": "Targets", "t_primary_feed_brand": "Primary feed brand", "t_projections": "Projections", "t_feed_amount": "Feed amount", "t_processor_price_lists": "Processor price lists", "t_select_your_default_price_list": "Select your default price list", "t_class_a_price_unit": "Class A - $ /{{unit}}", "t_class_b_price_unit": "Class B - $ /{{unit}}", "t_price_rejection_unit": "Rejection - $ /{{unit}}", "t_processor": "Processor", "t_enter_price_list_name": "Enter price list name", "t_select_your_default_harvest_type": "Select your default harvest type:", "t_expected_quality_of_harvest_head_on": "Expected quality of harvest - Head-on", "t_percent_of_leftover_as_headless_class_a": "% of leftover as Headless class A", "t_percent_of_leftover_as_headless_class_b": "% of leftover as Headless class B", "t_percent_of_headless_class_a": "% of headless as class A", "t_percent_of_headless_class_b": "% of headless as class B", "t_expected_quality_of_harvest_headless_direct": "Expected quality of harvest - Headless direct", "t_expected_percent_tail_of_body": "Expected % tail of body", "t_percent_tail": "% tail", "t_carrying_capacity": "Carrying capacity", "t_feed_data": "Feed data", "t_weeks": "Weeks", "t_no_feed_types_available": "No feed types available", "t_rev_lb": "Rev ($/lb)", "t_rev_kg": "Rev ($/kg)", "t_profit_lb": "Profit ($/lb)", "t_profit_kg": "Profit ($/kg)", "t_feed_types": "Feed types", "t_select_default_feed_to_use_for_cost_projection": "Select default feed to use for cost projections", "t_add_feed_type": "Add feed type", "t_brand": "Brand", "t_pellet_size_opt": "Pellet size (opt)", "t_cost_over_kg": "Cost ($/kg)", "t_please_select_default_feed_type": "Please select a default feed type", "t_stocking_density_ha": "Stocking density (ha)", "t_stocking_density_m2": "Stocking density (m2)", "t_autofeeder_brand": "Autofeeder brand", "t_feed_brand": "Feed brand", "t_target_config_tooltip": "The target productions metrics are used to benchmark pond performance. Enter the values that you target.", "t_farm_book": "Farm book", "t_cycle_week": "Cycle week", "t_revenue_at_harvest": "Revenue at harvest", "t_biomass_at_harvest": "Biomass at harvest", "t_weekly_production_data": "Weekly production data", "t_weekly": "Weekly", "t_daily": "Daily", "t_monthly": "Monthly", "t_percent_compared_to_feed_table": "{{value}}% compared to feed table", "t_carrying_capacity_lb_ha": "Carrying capacity (lb/ha)", "t_carrying_capacity_kg_ha": "Carrying capacity (kg/ha)", "t_#_of_autofeeders": "# of autofeeders", "t_#_of_aerators": "# of aerators", "t_autofeeder_protocol": "Autofeeder protocol", "t_next": "Next", "t_lab_source": "Lab source", "t_nauplii_source": "Nauplii source", "t_day_s": "day(s)", "t_confirm_stocking_details": "Confirm Stocking Details", "t_confirm_targets": "Confirm Targets", "t_confirm_cycle_protocol": "Confirm Cycle Protocol", "t_timer": "Timer", "t_time_of_day": "Time of day", "t_anytime": "Anytime", "t_hydrophone": "Hydrophone", "t_feed_brand_to_use": "Feed brand to use", "t_growout_feed_type": "Growout feed type", "t_record_up_to_one_minute": "Record up to one minute", "t_general_channel": "General channel", "t_projected": "Projected", "t_aerators_per_ha": "Aerators /ha", "t_type_of_aerator": "Type of aerator", "t_pond_setup": "Pond setup", "t_nursery": "Nursery", "t_worse_than_target": "Worse than target", "t_better_than_target": "Better than target", "t_you_dont_have_permission_to_see_this_page": "You don't have permission to see this page", "t_no_history_registered_for_any_feed_days": "No history registered for any feed day(s)", "t_upload_report": "Upload report", "t_from_feed_table": "from feed table", "t_biomass_in_pond_unit": "Biomass - In pond ({{unit}})", "t_biomass_in_pond_unit_ha": "Biomass - In pond ({{unit}}/ha)", "t_biomass_include_harvests_unit": "Biomass - Include harvests ({{unit}})", "t_biomass_include_harvests_unit_ha": "Biomass - Include harvests ({{unit}}/ha)", "t_survival_live": "Survival - Live", "t_survival_include_harvests": "Survival - Include harvests", "t_": "__STRING_NOT_TRANSLATED__", "t_dashboard": "Dashboard", "t_profile": "Profile", "t_min": "min", "t_max": "max", "t_hello": "Hello", "t_filtered_on": "Filtered on", "t_total_number_of_ponds": "Total number of ponds", "t_stocked_ponds": "Stocked ponds", "t_dry_ponds": "Dry ponds", "t_current_view": "Current view", "t_not_selected_in_pond_card": "Not selected in pond card", "t_my_farms": "My farms", "t_select_up_to_x_variables_you_want": "Select up to {{count}} variables you want to be shown.", "t_gps_distance": "Distance", "t_monitoring_distance_from_pond": "Monitoring - Distance from pond (m)", "t_monitoring_location": "Monitoring location", "t_monitoring_distance_to_pond": "Monitoring location is {{distance}}{{unit}} from the pond", "t_monitoring_x_distance_to_pond": "Monitoring {{x}} location is {{distance}}{{unit}} from the pond", "t_monitoring_x_is_near_pond": "Monitoring {{x}} location is near the pond", "t_near_pond": "near pond", "t_monitoring_is_near_pond": "Monitoring location is near the pond", "t_latest_monitoring_location": "Latest monitoring location", "t_overhead_cost_cumulative": "Overhead cost - Cumulative", "t_overhead_cost_dry_days": "Overhead cost - Dry days", "t_data_shown_across_all_ponds": "Data shown across all ponds", "t_farm_not_found": "Farm not found", "t_farm_not_found_desc": "Farm not found", "t_invitations_for_a_farm": "Invitations for {{farmName}} farm", "t_payment": "Payment", "t_population": "Population", "t_settings": "Settings", "t_select_farm_to_continue": "Select farm to continue", "t_forgot_password_desc": "Forget password", "t_log_in_desc": "Log in", "t_log_out": "Log out", "t_unsubscribe": "Unsubscribe", "t_unsubscribe_desc": "Unsubscribe", "t_reset_password_desc": "Reset password", "t_sign_up": "Sign up", "t_verify_email_desc": "Verify email", "t_view_invitation": "View invitation", "t_nursery_archived_successfully": "Nursery archived successfully", "t_archive_nursery": "Archive nursery", "t_stock_nursery": "Stock nursery", "t_edit_nursery_details": "Edit nursery details", "t_add_nursery_pond": "Add nursery pond", "t_this_nursery_is_empty": "This nursery is empty!", "t_nursery_summary": "Nursery summary", "t_transfer_date": "Transfer date", "t_transfer_pond": "Transfer pond", "t_edit_transfer": "Edit transfer", "t_transfer": "Transfer", "t_record_transfer": "Record transfer", "t_select_input": "Select input", "t_animals_%": "Animals (%)", "t_add_destination": "Add destination", "t_transfer_summary": "Transfer summary", "t_cost_millar": "Cost / millar", "t_type_of_stocking": "Type of stocking", "t_destination_ponds": "Destination ponds", "t_destination": "Destination", "t_planned_stocking": "Planned stocking", "t_transfer_history": "Transfer history", "t_variables": "Variables", "t_invoiced_quantity": "Invoiced quantity", "t_invoiced_quantity_opt": "Invoiced quantity (opt)", "t_end_of_cycle_survival": "End of cycle survival", "t_feed_cost": "Feed cost", "t_indirect_costs": "Indirect costs", "t_fixed_costs": "Fixed costs", "t_other_costs": "Other costs", "t_past_cycle": "Past cycle", "t_nursery_last_cycle_msg": "You're viewing a past cycle, stock nursery to start a new cycle", "t_past": "past", "t_edit_stocking_information_to_link_transfers": "Edit stocking information to link transfers", "t_add_stocking_information_to_link_transfers": "Add stocking information to link transfers", "t_you_have_incoming_transfers_to_this_pond_from": "You have incoming transfers to this pond from", "t_growout": "Growout", "t_you_are_missing_important_stocking_information": "You are missing important stocking information.", "t_add_details_now": "Add details now", "t_transferred_on": "transferred on", "t_this_pond_has_incoming_transfers_from": "This pond has incoming transfers from", "t_this_pond_has_accepted_transfers_from": "This pond has accepted transfers from", "t_is_this_stocking_missing_transfer_information": "Is this stocking missing transfer information?", "t_manage_nurseries": "Manage nurseries", "t_edit_direct_stocking": "Edit direct stocking", "t_stock_directly": "Stock directly", "t_stock_from_transfer": "Stock from transfer", "t_would_you_like_to_link_this_cycle_with_those_transfers": "Would you like to link this cycle with those transfers", "t_Total_biomass_transferred": "Total biomass transferred", "t_transfer_amount": "Transfer amount", "t_feed_cost_per_kg": "Feed cost ($/kg)", "t_feed_cost_$": "Feed cost ($)", "t_up_to_date": "Up to date", "t_using_imputed_values": "*Using imputed values", "t_estimated_values_are_used": "Estimated values are used to complete calculations.", "t_direct": "Direct", "t_flagged_ponds": "Flagged ponds", "t_delete_confirmation": "Delete confirmation", "t_are_you_sure_you_want_to_continue": "Are you sure you want to continue?", "t_created_by": "Created by", "t_head_on_abbreviation": "HO", "t_head_less_abbreviation": "HL", "t_expected_feed": "Expected feed", "t_transfer_confirmation": "Transfer confirmation", "t_edit_transfer_confirmation": "Edit transfer confirmation", "t_transfer_confirmation_info": "You are about to record the transfer for {{nurseryName}} to {{pondNames}}. This will automatically harvest this nursery pond.", "t_transfer_confirmation_question": "Are you sure you want to complete this transfer?", "t_edit_transfer_confirmation_msg": "If this transfer has already been used to stock other ponds, you will need to edit the stocking information in those ponds to get the updated values here", "t_nursery_data_updater": "Nursery data updater", "t_transfer_data": "Transfer data", "t_harvest_type_classification": "Harvest type: {{harvestType}}", "t_total_transferred_animals_should_be_less_than": "Total transferred animals should be less than {{value}}", "t_grams_change": "{{grams}}g change", "t_count_percent_day": "{{count}} / day", "t_kampi": "Kampi", "t_until_harvest": "Until harvest", "t_projections_not_available": "Projections not available", "t_projections_not_available_for_pond": "Projections not available for ponds that are under 8g with less than 3 monitorings", "t_x_days_earlier": "{{days}} days earlier", "t_x_days_later": "{{days}} days later", "t_x_day_earlier": "{{days}} day earlier", "t_x_day_later": "{{days}} day later", "t_optimal_harvest": "Optimal harvest", "t_gps_location_appears_farm_from_the_pond": "GPS location appears far from the pond", "t_estimate_data_alert_msg": "This pond's calculations use imputed values to account for missing data.", "t_overhead_cost_ha_day": "Overhead cost/ha/day", "t_other_direct_costs": "Other direct costs", "t_revenue_processed_unit": "Revenue - Processed ($/{{unit}})", "t_manage_projections": "Manage projections", "t_class_size": "Class size", "t_biomass_kg": "Biomass (kg)", "t_biomass_lb": "Biomass (lb)", "t_expected": "Expected", "t_amount_unit": "{{amount}} / {{unit}}", "t_pond_summary": "Pond summary", "t_protocol": "Protocol", "t_harvest_summary": "Harvest summary", "t_pond_source": "Pond source", "t_autofeeders_ha": "Autofeeders /ha", "t_selected_x": "Selected: {{count}}", "t_comparison_to_target": "Comparison to target", "t_add_manual_weight_entry": "Add manual weight entry", "t_target": "Target", "t_mobile_graph_msg": "Click here to see the production graph", "t_rotate_your_phone_content_msg": "Rotate your phone to see the content. Ensure rotation is unlocked on your device.", "t_click_here_to_see_graph": "Click here to see the graph", "t_click_here_to_see_table": "Click here to see the table", "t_click_to_see_weekly_production_data": "Click to see weekly production data", "t_last_monitored_on_x": "Last monitored on {{date}}", "t_range_g": "Range (g)", "t_range": "Range", "t_feed_type_percentage_sum_error": "The sum of all Feed Type values must not exceed 100%", "t_pond_performance_required": "The pond performance required to achieve the pond targets for average weight, survival, biomass and FCR.", "t_variable": "Variable", "t_biomass_processed_without_price": "Biomass processed in classes without a price, please check your processor price list.", "t_i_am_writing_the_message_here": "I'm writing the message here ...", "t_update_default_processor": "Update default processor", "t_delete_processor": "Delete processor", "t_updated_default_processor_modal_text_1": "You've updated the default processor. This will apply to all farms in your group.", "t_updated_default_processor_modal_text_2": "Would you like to apply this new default processor to all ponds with default selected?", "t_delete_processor_modal_text_1": "You've deleted the processor. This will apply to all farms in your group.", "t_delete_processor_modal_text_2": "Would you like to apply this to all ponds?", "t_no_only_apply_to_future_assignments": "No, apply only to future assignments", "t_yes_apply_to_all_ponds_and_farms": "Yes, apply to all ponds and farms", "t_update_processor_confirmation": "Update processor confirmation", "t_update_processor_confirmation_text": "You've made updates to this price list. Would you like to apply these updates to all ponds with this price list selected?", "t_yes_apply": "Yes, apply", "t_no_keep_current_assignments": "No, keep current assignments", "t_revenue_per_lb_live": "Revenue - Live ($/lb)", "t_revenue_per_kg_live": "Revenue - Live ($/kg)", "t_revenue_per_lb_include_harvest": "Revenue - Include harvests ($/lb)", "t_revenue_per_kg_include_harvest": "Revenue - Include harvests ($/kg)", "t_head_on_distribution": "Head-on distribution", "t_head_less_distribution": "Headless distribution", "t_harvest_expected": "Harvest (expected)", "t_performance": "Performance", "t_above_target": "Above target", "t_on_track": "On track", "t_off_track": "Off track", "t_show_ponds_that_are": "Show ponds that are", "t_above": "Above", "t_above_x_percent_carrying_capacity": "Above {{percent}}% carrying capacity", "t_customize_graph": "Customize graph", "t_projection_length": "Projection length", "t_show_ponds_with_a_carrying_capacity": "Show ponds with a carrying capacity above 85%", "t_x_ponds_are_above_carrying_capacity": "{{count}} ponds are above 85% of its carrying capacity, negatively impacting growth.", "t_x_ponds_are_above_carrying_capacity_singular": "{{count}} pond is above 85% of its carrying capacity, negatively impacting growth.", "t_daily_parameters": "Daily parameters", "t_only_override_weights_when_a_monitoring_has_been_taken": "You can only override weights when a monitoring has been taken", "t_no_monitoring": "No monitoring", "t_revert_to_kampi_weight": "Revert to Kampi weight", "t_kampi_weight_x_g": "Kampi weight: {{averageWeight}}g", "t_kampi_weight_g": "Kampi weight (g)", "t_updated_by_name": "Updated by: {{name}}", "t_manual_weight_confirmation": "Manual weight confirmation", "t_manual_weight_confirmation_description": "An average weight entered here will be used throughout the platform, including the histogram.", "t_daily_param_monitoring_alert": "No active cycle for this pond on the selected date.", "t_daily_param_not_stocked_alert": "This pond is not stocked yet!", "t_overwrite_weights": "Are you sure you want to overwrite the weights on the following ponds?", "t_invalid_average_weight": "Invalid entry. Please enter a weight closer to the Kampi result.", "t_parameter": "Parameter", "t_%_of_carrying_capacity": "% of carrying capacity", "t_data_check": "Data Check - DEV", "t_ok": "OK", "t_missing": "Missing", "t_target_type": "Target type", "t_target_survival": "Target survival", "t_target_biomass_to_harvest": "Target biomass to harvest", "t_target_cycle_length": "Target cycle length", "t_target_harvest_weight": "Target harvest weight", "t_target_fcr": "Target FCR", "t_projected_feed_type": "Projected feed type", "t_projected_growth_grams": "Projected growth (grams)", "t_projected_growth_days": "Projected growth (days)", "t_projected_survival_type": "Projected survival type", "t_daily_mortality_percent": "Daily mortality (%)", "t_expected_target_survival": "Expected target survival", "t_expected_target_survival_days": "Expected target survival (days)", "t_feed_data_for_last_monitoring": "Feed data for last monitoring", "t_selected_processor": "Selected processor (Harvest Plan)", "t_selected_processor_exists": "The selected processor exists in the farm's processor price list", "t_profit_projection": "Profit projection", "t_production_projection": "Production projection", "t_cycle_comparison_projection": "Cycle comparison projection", "t_active_processor_price_lists": "Active processor price lists (Farm)", "t_active_feed_table": "Active feed table (Farm)", "t_active_feed_types": "Active feed types (Farm)", "t_monitoring_days_config": "Monitoring days config (Config)", "t_start_of_week_config": "Start of week config (Config)", "t_farm_production_days": "Farm production days (Config)", "t_farm_weight": "Farm weight (Config)", "t_farm_fcr": "Farm FCR (Config)", "t_farm_cost_per_pound": "Farm cost / pound (Config)", "t_farm_profit_per_ha_per_day": "Farm profit / ha / day (Config)", "t_farm_biomass_lb_ha": "Farm biomass (lb/ha) (Config)", "t_farm_dry_days": "Farm dry days (Config)", "t_farm_growth_density": "Farm growth density (Config)", "t_farm_days_in_nursery": "Farm days in nursery (Config)", "t_farm_nursery_survival": "Farm nursery survival (Config)", "t_farm_avg_stocking_weight": " Farm avg stocking weight (Config)", "t_value": "Value", "t_using_custom_values_for_projection": "Using custom values for projections", "t_production_chart_alert": "A maximum of two variables can be viewed at the same time.", "t_show_daily_parameters": "Show daily parameters", "t_feed_given_kg": "Feed given (kg)", "t_feed_given_lg": "Feed given (lb)", "t_units": "Units", "t_unit": "Unit", "t_affected_areas_with_biomass_unit": "Affected areas include total biomass, biomass /ha, cost /unit, profit /unit.", "t_edit_daily_feed_msg": "You can only edit daily feed here. Go to Data updater to edit feed by week.", "t_cycle_comparison": "Cycle comparison", "t_benchmark": "Benchmark", "t_update_plan": "Update plan", "t_animals_stocked_ha": "Animals - Stocked /ha", "t_animals_stocked": "Animals - Stocked", "t_biomass_from_partials_unit_ha": "Biomass - From partials ({{unit}}/ha)", "t_biomass_from_partials_unit": "Biomass - From partials ({{unit}})", "t_biomass_from_final_unit": "Biomass - From final ({{unit}})", "t_biomass_processed": "Biomass - Processed ({{unit}})", "t_biomass_unpriced": "Biomass - Unpriced ({{unit}})", "t_date_of_last_partial_harvest": "Date of last partial harvest", "t_partial_harvest_done": "Partial harvest done", "t_last_week": "Last week", "t_x_weeks_ago": "{{count}} weeks ago", "t_select_variables_you_want_to_be_shown": "Select variables you want to be shown.", "t_animals_harvested_per_m2": "Animals - Harvested /m2", "t_animals_harvested_ha": "Animals - Harvested /ha", "t_production_days": "Production Days", "t_using_imputed_values_for_calculations": "*Using imputed values for calculations.", "x_monitoring_location_from_the_pond": "Monitoring location appears to be {{distance}}{{unit}} from the pond.", "x_monitoring_location_near_the_pond": "Monitoring location appears to be near the pond.", "t_no_cycles": "No cycles", "t_select_cycle": "Select cycle", "t_only_add_survival_when_a_monitoring_has_been_taken": "Survival can only be added on days where there has been a monitoring.", "t_kampi_mortality": "Kampi mortality", "t_total_biomass_biomass_over_ha": "Total Biomass (Biomass /ha)", "t_total_profit_profit_over_x": "Total Profit (Profit /{{unit}} Harvested)", "t_processed": "Processed", "t_harvested": "Harvested", "t_selected_variables": "Selected variables", "t_selected_variables_sort_desc": "Selected variables will be shown based on this default order. You can reorder variables by dragging the rows up and down.", "t_cycle_information": "Cycle information", "t_revenue_per_unit_processed": "Revenue - Processed ($/{{unit}})", "t_revenue_per_unit_harvested": "Revenue - Harvested ($/{{unit}})", "t_profit_per_unit_processed": "Profit - Processed ($/{{unit}})", "t_profit_per_unit_harvested": "Profit - Harvested ($/{{unit}})", "t_cost_per_unit_processed": "Cost - Processed ($/{{unit}})", "t_cost_per_unit_harvested": "Cost - Harvested ($/{{unit}})", "t_add_parameter": "Add parameter", "t_delete_daily_param_alert": "You are about to delete {{name}} from Kampi. This action cannot be undone.", "t_param_name_already_exists": "Parameter name already exists", "t_prof_ha_d": "Prof / ha / d", "t_weekly_linear_growth": "Weekly Linear Growth", "t_daily_linear_growth": "Daily Linear Growth", "t_animals_harvested": "Animals - Harvested", "t_anim_ha": "Anim /ha", "t_anim_m2": "Anim /m2", "t_biomass_lb_partial_harvest": "Biomass lb - Partial harvest (Biomass /ha)", "t_biomass_lb_final_harvest": "Biomass lb - Final harvest (Biomass /ha)", "t_bioL_p": "BioL P", "t_bioL_f": "BioL F", "t_cost_p_lb": "Cost P ($/lb)", "t_cost_h_lb": "Cost H ($/lb)", "t_rev": "Rev ($)", "t_rev_p": "Rev P ($)", "t_rev_h": "Rev H ($)", "t_prof": "Prof", "t_kampi_mortality_survival_target": "Kampi mortality survival target", "t_kampi_mortality_survival_target_tooltip": "This survival is used to improve predictions for Kampi Mortality.", "t_days_d": "{{count}}d", "t_select_pond": "Select pond", "t_count_ponds_plural": "{{count}} ponds", "t_count_ponds_singular": "{{count}} pond", "t_enter": "Enter", "t_drag_and_drop_file_or": "Drag-and-drop file, or", "t_brows_computer": "browse computer", "t_to": "to", "t_use_kampi_expected_revenue": "Use Kampi expected revenue.", "t_partial_harvest_confirmation": "Partial harvest confirmation", "t_partial_harvest_change_description": "The harvest information has been updated. Save changes?", "t_harvest_pond_confirmation": "Harvest pond confirmation", "t_harvest_change_description": "The harvest information has been updated. Save changes?", "t_harvest_plan_change_description": "The harvest plan information has been updated. Save changes?", "t_partial_harvest_plan_change_description": "The partial harvest plan information has been updated. Save changes?", "t_unread_messages": "Unread messages", "t_all_messages": "All messages", "t_cycle_summary": "Cycle summary", "t_final_harvest": "Final harvest", "t_general": "General", "t_profit_revenue": "Profit / revenue", "t_fcr_adjusted": "FCR - Adjusted", "t_no_partial_harvest_alert": "There is no partial harvest recorded or planned.", "t_partial_harvest_x": "Partial harvest {{count}}", "t_final_harvest_plan": "Final harvest - Plan", "t_final_harvest_recorded": "Final harvest - Recorded", "t_partial_harvest_count_plan": "Partial harvest #{{count}} - Plan", "t_partial_harvest_count_recorded": "Partial harvest #{{count}} - Recorded", "t_export": "Export", "t_export_farm_summary_table": "Export farm summary table", "t_export_daily_parameters_table": "Export daily parameters table", "t_add_farm_overview_data": "Add farm overview data", "t_select_a_view": "Select a view", "t_total_animals_detected": "{{total}} animals detected", "t_export_weekly_production_data_table": "Export weekly production data table", "t_your_data_exported_in_xlsx": "Your data will be exported in .xlsx format.", "t_weekly_monitoring_data": "weekly monitoring data", "t_ponds": "Ponds", "t_harvests": "Harvests", "t_final": "Final", "t_partial": "Partial", "t_next_30_d": "Next 30d", "t_past_30_d": "Past 30d", "t_type": "Type", "t_delete_harvest_confirmation": "Delete harvest confirmation", "t_you_are_about_to_delete_harvest": "You are about to delete the harvest for {{pondName}}.", "t_count_final_count_partial": "{{finalCount}}F, {{partialCount}}P", "t_count_plan_count_recorded": "{{planCount}}P, {{recordedCount}}R", "t_count_ponds": "{{count}} ponds", "t_harvest_plan_recorded_msg": "Information about the harvest will be shown here after it has been recorded.", "t_harvest_plan_planned_msg": "The harvest results according to the last saved harvest plan and projections", "t_harvest_recorded": "Harvest (recorded)", "t_count_cycles": "{{count}} cycles", "t_create_nursery_pond": "Create nursery pond", "t_rev_per_pound_processed": "Rev P ($/lb)", "t_rev_per_pound_harvested": "Rev H ($/lb)", "t_count_per_ha": "{{count}} /ha", "t_pls_per_g": "PLs /g", "t_quantity_from_hatcheries": "Quantity of PLs invoiced from hatcheries", "t_notifications": "Notifications", "t_push": "<PERSON><PERSON>", "t_below": "Below", "t_nearing_carrying_capacity": "Nearing carrying capacity", "t_in": "in", "t_notifications_and_rule_error": "Notification type and Rules should be selected", "t_carrying_capacity_reached": "Carrying capacity reached", "t_current": "Current", "t_kampi_notification_center": "Kampi notification center", "t_all_notifications": "All notifications", "t_read": "Read", "t_unread": "Unread", "t_sudden_temperature_drop": "Sudden temperature drop", "t_sudden_temperature_increase": "Sudden temperature increase", "t_am_water_temperature_drop": "Temp - AM: {{temperature}}°C", "t_pm_water_temperature_drop": "Temp - PM: {{temperature}}°C", "t_am_do_value": "DO - AM: {{value}} mg/L", "t_pm_do_value": "DO - PM: {{value}} mg/L", "t_temperature_dropped_notification": "Temperature dropped to {{dropped_to}}°C at {{dropped_to_time}} (down from {{dropped_from}}°C at {{dropped_from_time}})", "t_temperature_increased_notification": "Temperature increased to {{increased_to}}°C at {{increased_to_time}} (up from {{increased_from}}°C {{increased_from_time}})", "t_carrying_capacity_reached_notification": "Pond has reached full capacity {{days}}", "t_carrying_capacity_will_be_reached_notification": "Pond will reach full capacity in {{days}} days", "t_tomorrow": "Tomorrow", "t_carrying_capacity_reached_on_ponds": "Carrying capacity has been reached in  {{count}} ponds", "t_carrying_capacity_will_be_reached_on_ponds": "Carrying capacity will soon be reached in {{count}} ponds", "t_sudden_temperature_change_ponds": "Temperature Alert: {{count}} ponds have had a sudden temperature drop (or rise)", "t_no_notifications": "No notifications", "t_mg_per_L": "mg/L", "t_degree_celsius": "°C", "t_hide_table": "Hide table", "t_show_table": "Show table", "t_average_daily_feed": "Avg. Daily feed", "t_bags": "bags", "t_water_am_temperature_unit": "Water temperature - AM{{unit}}", "t_temp_am_unit": "Temp - AM{{unit}}", "t_water_pm_temperature_unit": "Water temperature - PM{{unit}}", "t_temp_pm_unit": "Temp - PM{{unit}}", "t_dissolved_am_oxygen_unit": "Dissolved oxygen - AM{{unit}}", "t_DO_am_unit": "DO - AM{{unit}}", "t_dissolved_pm_oxygen_unit": "Dissolved oxygen - PM{{unit}}", "t_DO_pm_unit": "DO - PM{{unit}}", "t_harvest_actions": "Harvest actions", "t_x_final_harvest_title": "{{action}} final harvest for pond: {{pondName}} cycle: {{cycle}}", "t_edit_final_harvest_plan_title": "Edit final harvest plan for pond: {{pondName}} cycle: {{cycle}}", "t_x_partial_harvest_plan_title": "{{action}} partial harvest plan for pond: {{pondName}} cycle: {{cycle}}", "t_price_list_for_pond_cycle_title": "Price list for pond: {{pondName}} cycle: {{cycle}}", "t_manage_partial_harvests": "Manage partial harvests", "t_details": "Details", "t_harvest_quality": "Harvest quality", "t_standard_price_list_not_applied": "Standard price list not applied", "t_view_price_list": "View price list ", "t_revert_to_default": "Revert to default", "t_harvest_quality_update": "Harvest quality has been updated", "t_delete_price_list_title_desc": "You are about to override the saved price list.", "t_delete_price_list_title_confirmation": "Are you sure you want to continue?", "t_x_partial_harvest_title": "{{action}} partial harvest for pond: {{pondName}} cycle: {{cycle}}", "t_check_price_list_errors": "Check price list errors", "t_please_adjust_your_survival_rate_accordingly": "Please adjust your survival accordingly.", "t_survival_at_x": "Survival at {{date}}", "t_edit_final_harvest_plan": "Edit final harvest plan", "t_edit_final_harvest": "Edit final harvest", "t_record_final_harvest": "Record final harvest", "t_projected_harvest_details": "Projected harvest details", "t_generate_harvest_projections": "Generate harvest projections", "t_generate": "Generate", "t_edit_harvest_plan_confirmation": "Edit final harvest plan confirmation", "t_edit_partial_harvest_plan_confirmation": "Edit partial harvest plan confirmation", "t_choose_a_date_and_processor": "Choose a date and processor to generate", "t_newly_computed_harvest_projections": "Newly computed harvest projections", "t_refresh": "Refresh", "t_mark_as_unread": "<PERSON> as unread", "t_number_of_notifications_selected_singular": "{{count}} notification selected", "t_number_of_notifications_selected_plural": "{{count}} notifications selected", "t_manage_partial_harvests_for_pond_cycle": "Manage partial harvests for pond: {{pondName}} cycle: {{cycle}}", "t_this_pond_has_no_pending_transfers": "This pond has no pending transfers", "t_daily_parameter_title": "Select the parameters you want to track. These will appear in the Kampi app for data entry.", "t_custom_numeric_parameter": "Custom numeric parameter", "t_manage_notifications": "Manage notifications", "t_overhead_cost_ha_day_tooltip": "The general cost of your farm, which includes rent, construction and maintenance expenses, labor, equipment depreciation, among others.", "t_type_name": "Type a name", "t_exceeds_biomass_in_pond": "Exceeds biomass in the pond.", "t_parameters": "Parameters", "t_celsius_c": "°C", "t_fahrenheit_f": "°F", "t_liters_L": "L", "t_ppt": "ppt", "t_cm": "cm", "t_pH": "pH", "t_kampi_growth": "Kampi growth", "t_mortality": "Mortality", "t_transparency_unit": "Transparency{{unit}}", "t_transp_unit": "Transp{{unit}}", "t_turbidity_unit": "Turbidity{{unit}}", "t_turb_unit": "Turb{{unit}}", "t_salinity_unit": "Salinity{{unit}}", "t_sal_unit": "Sal{{unit}}", "t_alkalinity_unit": "Alkalinity{{unit}}", "t_alk_unit": "Alk{{unit}}", "t_ammonia_nh3_unit": "Ammonia [NH3]{{unit}}", "t_nh3_unit": "NH3{{unit}}", "t_nitrite_no2_unit": "Nitrite [NO2]{{unit}}", "t_no2_unit": "NO2{{unit}}", "t_nitrate_no3_unit": "Nitrate [NO3]{{unit}}", "t_no3_unit": "NO3{{unit}}", "t_magnesium_mg_unit": "Magnesium [Mg]{{unit}}", "t_mg_unit": "Mg{{unit}}", "t_calcium_ca_unit": "Calcium [Ca]{{unit}}", "t_ca_unit": "Ca{{unit}}", "t_potassium_k_unit": "Potassium [K]{{unit}}", "t_k_unit": "K{{unit}}", "t_growth": "Growth", "t_feed_given": "Feed given", "t_all_data": "All Data", "t_final_harvest_date": "Final harvest date", "t_source": "Source", "t_device": "<PERSON><PERSON>", "t_go_to_summary": "Go to summary", "t_version": "version", "t_os": "OS", "t_hour": "Hour", "t_minute": "Minute", "t_second": "Second", "t_seconds": "Seconds", "t_millisecond": "Millisecond", "t_milliseconds": "Milliseconds", "t_no_trays_available": "No trays available", "t_showing_x_to_x_of_x_items": " Showing {{range1}} to {{range2}} of {{total}} items", "t_items": "Items", "t_page": "Page", "t_prev_page": "Prev page", "t_next_page": "Next page", "t_uploaded": "Uploaded", "t_upload": "Upload", "t_monitoring_status": "Monitoring status", "t_start": "Start", "t_end": "End", "t_pending_upload": "Pending upload", "t_overhead_cost_days_of_culture": "Overhead cost - Days of culture", "t_sharing": "Sharing", "t_sandbox": "Sandbox", "t_supervisor": "Supervisor", "t_layout_will_be_shared_across_all_farms": "This layout will be shared across all farms in the group:", "t_reset_data_confirmation": "By clicking “Reset” the changes you have made will be discarded and it will restore the last saved values", "t_projected_at_final_harvest": "Projected at final harvest", "t_simulate_projections": "Simulate projections", "t_no_cv_for_manual_monitoring": "CV not available for manual monitoring", "t_monitoring_id": "Monitoring id", "t_group_performance": "Group performance", "t_all_farms": "All farms", "t_filter_farms": "Filter farms", "t_#_of_cycles": "# of cycles", "t_average_x_for_completed_cycles_across_farms": "Average {{variable}} for completed cycles across all selected farms", "t_breakdown": "Breakdown", "t_all_year": "All year", "t_completed_cycles": "Completed cycles", "t_past_x_months": "Past {{months}} months", "t_no_cycles_completed": "No cycles completed in this period.", "t_reset_to_last_saved_values": "Reset to last saved values", "t_reset": "Reset", "t_proceed_confirmation_msg": "Are you sure you want to proceed?", "t_class_size_without_price_set": "Class size without price set", "t_monitoring_strength": "Monitoring - Strength", "t_weak_great_incomplete_desc": "Image quality is excellent, but sample size is too small. To improve future monitorings, add a few more shrimps, ensuring they don't overlap, and sample more trays.", "t_moderate_great_partial_desc": "Great image quality. To improve future monitorings, add a few more shrimps, ensuring they don't overlap, or sample more trays.", "t_strong_great_complete_desc": "A fantastic result with great images and a complete sample size. This data is very reliable.", "t_great_great_great_desc": "This is a perfect monitoring! You've captured excellent images and exceeded the sample target.", "t_weak_good_incomplete_desc": "Image quality is good. To get a reliable monitoring, add a few more shrimps, ensuring they don't overlap, and sample more trays.", "t_moderate_good_partial_desc": "These are good images. To improve future monitorings, focus on adding a few more shrimps, ensuring they don't overlap, and sample more trays.", "t_strong_good_complete_desc": "A solid result. You met the sample target with good images. For even better results, try to avoid shrimp overlap.", "t_strong_good_great_desc": "You've collected plenty of shrimps with good quality. Ideal results are achieved using a dry tray, by removing shrimp overlap.", "t_weak_below_standard_incomplete_desc": "Let's review the setup. The main issue is shrimp overlap or poor lighting. Try again with more shrimps once that's fixed.", "t_moderate_below_standard_partial_desc": "The main focus should be quality. Try spreading the shrimps to reduce shrimp overlap and avoid poor lighting.", "t_moderate_below_standard_complete_desc": "There are enough samples, so now the focus is on quality. The primary issue is likely shrimp overlap or poor lighting.", "t_moderate_below_standard_great_desc": "There are plenty of shrimps! Now, let's focus on reducing overlap and improving lighting for a more reliable monitoring.", "t_weak_poor_incomplete_desc": "Both volume and quality are lacking. To improve future monitorings, focus on adding more shrimps, ensuring they don't overlap, and sample more trays.", "t_weak_poor_partial_desc": "The priority is fixing the poor image quality caused by bad lighting or shrimp overlap. To improve future monitorings, try spreading them out evenly.", "t_weak_poor_complete_desc": "There are enough shrimps, but the poor image quality hinders reliability. To improve future monitorings, address issues of shrimps overlap or lighting.", "t_weak_poor_great_desc": "A great sample count doesn't help much if the images are of poor quality. To improve future monitorings, the priority is to fix shrimp overlap, lighting, and other quality issues.", "t_weak": "Weak", "t_moderate": "Moderate", "t_strong": "Strong", "t_great": "Great", "t_incomplete": "Incomplete", "t_complete": "Complete", "t_below_standard": "Below standard", "t_good": "Good", "t_poor": "Poor", "t_quality": "Quality", "t_completeness": "Completeness", "t_x_usual_shrimps_for_weights_out_x_counted": "{{good}} shrimps usable for weight estimation out of {{total}} detected", "t_carrying_capacity_usage": "Carrying capacity - Usage", "t_do_am_grouping_msg": "DO - AM: Outside normal range in {{count}} ponds", "t_do_pm_grouping_msg": "DO - PM: Outside normal range in {{count}} ponds", "t_temperature_am_grouping_msg": "Temperature - AM: Outside normal range in {{count}} ponds", "t_temperature_pm_grouping_msg": "Temperature - PM: Outside normal range in {{count}} ponds", "t_reaching_full_capacity": "Reaching full capacity", "t_full_capacity_reached": "Full capacity reached", "t_water_temperature_low": "Water temperature - {{type}} is low: {{temperature}}°C", "t_water_temperature_high": "Water temperature - {{type}} is high: {{temperature}}°C", "t_do_am_low": "Dissolved oxygen - AM is low: {{value}} mg/L", "t_do_pm_low": "Dissolved oxygen - PM is low: {{value}} mg/L", "t_do_am_high": "Dissolved oxygen - AM is high: {{value}} mg/L", "t_do_pm_high": "Dissolved oxygen - PM is high: {{value}} mg/L", "t_x_daily_parameters_created": "{{count}}/{{total}} daily parameters created", "t_biomass_in_pond_kg_ha": "Biomass - In pond (kg/ha)", "t_biomass_in_pond_lb_ha": "Biomass - In pond (lb/ha)", "t_stocking_weight": "Stocking weight", "t_label": "Label", "t_comment": "Comment", "t_tray_annotations_created": "Tray annotations created successfully", "t_farm_breakdown": "Farm breakdown", "t_farm_breakdown_description": "Breakdown KPIs by farm to understand what’s driving performance.", "t_change": "Change", "t_no_harvests_recorded_this_month": "No harvests recorded this month", "t_save_dashboard": "Save Dashboard", "t_add_new_dashboard": "Add new dashboard", "t_dashboard_name": "Dashboard name", "t_my_dashboards": "My dashboards", "t_number_of_charts": "Number of charts", "t_select_layout": "Select Layout"}