{"t_home": "", "t_accept": "<PERSON><PERSON><PERSON>", "t_access": "<PERSON><PERSON><PERSON><PERSON>", "t_account_details": "Th<PERSON>ng tin tài khoản chi tiết", "t_active": "<PERSON><PERSON> ho<PERSON>t động", "t_add": "<PERSON><PERSON><PERSON><PERSON>", "t_add_a_farm": "<PERSON><PERSON><PERSON><PERSON> một trại nuôi", "t_add_farm": "Thêm trại nuôi", "t_add_pond": "<PERSON>hê<PERSON> ao nuôi", "t_add_user": "<PERSON><PERSON><PERSON><PERSON><PERSON> sử dụng", "t_admin": "<PERSON><PERSON><PERSON><PERSON> trị viên", "t_user": "<PERSON>ư<PERSON>i sử dụng", "t_operations_supervisor": "", "t_operations_supervisor_can_see": "", "t_production_supervisor": "", "t_already_have_an_account": "<PERSON><PERSON> có tài k<PERSON>n", "t_and": "và", "t_animals": "Tôm", "t_archive_pond": "<PERSON><PERSON> đang nuôi", "t_back_to_log_in": "Quay lại để đăng nhập", "t_bulk_invite": "<PERSON><PERSON><PERSON> người sử dụng", "t_by_clicking_create_account": "Bằng cách chọn '<PERSON><PERSON><PERSON> tài khoản' quý vị đồng ý rằng", "t_cancel": "Huỷ bỏ", "t_cancel_invitation": "Huỷ bỏ lời mời", "t_change_password": "<PERSON>hay đổi mật mã", "t_close": "Đ<PERSON><PERSON>", "t_code": "Mã code", "t_confirm": "<PERSON><PERSON><PERSON>", "t_confirm_archive": "<PERSON><PERSON><PERSON> vị chắc chắn muốn đóng lại", "t_confirm_new_password": "<PERSON><PERSON><PERSON> n<PERSON>ận mật mã mới", "t_confirm_password": "<PERSON><PERSON><PERSON> n<PERSON>n mật mã", "t_contact_phone_no": "<PERSON><PERSON> điện tho<PERSON>i liện hệ", "t_continue": "<PERSON><PERSON><PERSON><PERSON>", "t_create": "Tạo", "t_react_select_create_label": "T<PERSON>o \"{{value}}\"", "t_create_account": "<PERSON><PERSON><PERSON> t<PERSON>", "t_create_invitation": "<PERSON><PERSON><PERSON> lời mời", "t_created_at": "Đã tạo lúc", "t_cv": "CV", "t_cycle": "<PERSON><PERSON> nuôi", "t_cycle_number": "Số vụ nuôi", "t_date": "<PERSON><PERSON><PERSON>", "t_day": "<PERSON><PERSON><PERSON>", "t_days": "ng<PERSON>y", "t_days_2": "", "t_hours": "", "t_minutes": "", "t_count_days_of_culture": "", "t_did_not_receive_code": "Quý vị đã không nhận được mã code?", "t_distribution": "Sự phân bố cỡ tôm", "t_edit_access": "Chỉnh sửa truy cập", "t_edit_pond": "Chỉnh sửa ao nuôi", "t_edit_nursery": "", "t_edit_stocking": "Chỉnh sửa thả giống", "t_email": "Email", "t_enter_the_code_here": "Nhập mã code ở đây", "t_farm": "Trại nuôi tôm", "t_female": "<PERSON><PERSON>", "t_finished_at": "Đ<PERSON> xong lúc", "t_forgot_password": "<PERSON><PERSON><PERSON><PERSON> mật mã", "t_forgot_password_description": "Nhập địa chỉ email mà quý vị đã dùng để tạo tà<PERSON>, và chúng tôi sẽ gởi mã code để quý vị khôi phục mật mã", "t_gender": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "t_ha": "", "t_harvest": "<PERSON><PERSON>", "t_harvest_date": "<PERSON><PERSON><PERSON> thu ho<PERSON>ch", "t_id": "ID", "t_in_process": "<PERSON><PERSON> n<PERSON>", "t_invitations": "<PERSON><PERSON><PERSON> mời", "t_invite_new_user": "<PERSON><PERSON><PERSON> người sử dụng mới", "t_invited": "Đ<PERSON> mời", "t_invited_at": "Đã mời lúc", "t_invited_date_time": "Đ<PERSON> mời", "t_is_active": "<PERSON><PERSON> nuôi?", "t_join_via_invitation": "Tham gia qua lời mời", "t_last_active": "<PERSON><PERSON><PERSON> ho<PERSON>t động gần đây nhất", "t_language": "<PERSON><PERSON><PERSON>", "t_loading": "<PERSON><PERSON> t<PERSON>", "t_log_in": "<PERSON><PERSON><PERSON>", "t_manage_preferences": "", "t_preferences_updated_successfully": "", "t_email_settings": "", "t_unsubscribe_message": "", "t_log_in_to_your_account": "<PERSON><PERSON><PERSON><PERSON>p v<PERSON><PERSON>", "t_login_password": "<PERSON><PERSON><PERSON> mã đăng nhập", "t_logout": "<PERSON><PERSON><PERSON> xu<PERSON>", "t_male": "Nam", "t_manage_monitoring": "<PERSON><PERSON><PERSON><PERSON> lý việ<PERSON> kiểm tra tôm", "t_message": "<PERSON>", "t_monitoring": "<PERSON><PERSON> kiểm tra", "t_monitorings": "<PERSON><PERSON> kiểm tra", "t_my_account": "<PERSON><PERSON><PERSON>n của tôi", "t_name": "<PERSON><PERSON><PERSON>", "t_new_password": "Mật mã mới", "t_new_pond": "Ao nuôi mới", "t_new_user": "Người sử dụng mới", "t_no_data_to_display": "<PERSON><PERSON><PERSON><PERSON> có số liệu hiển thị", "t_no_farms": "<PERSON><PERSON><PERSON>ng có trại nuôi nào", "t_no_invitations": "<PERSON><PERSON><PERSON><PERSON> có lời mời nào", "t_no_payments": "<PERSON><PERSON><PERSON><PERSON> thanh toán", "t_plan": "<PERSON><PERSON> ho<PERSON>", "t_provider": "<PERSON><PERSON><PERSON> cung cấp", "t_payment_history": "<PERSON><PERSON><PERSON> sử thanh toán", "t_no_monitoring_available": "<PERSON><PERSON><PERSON><PERSON> có lần kiểm tra tôm nào có sẵn", "t_not_active": "<PERSON><PERSON><PERSON>", "t_not_have_account": "", "t_contact_us": "<PERSON><PERSON><PERSON> h<PERSON> với chúng tôi", "t_not_receive_the_code": "Nếu quý vị vẫn chưa nhận được mã code", "t_old_password": "<PERSON><PERSON><PERSON> mã cũ", "t_oops": "Oops", "t_optional": "<PERSON><PERSON>", "t_or": "hoặc", "t_other": "K<PERSON><PERSON><PERSON>", "t_password": "<PERSON><PERSON><PERSON> mã", "t_phone_number": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "t_photo_count": "", "t_photos": "<PERSON><PERSON><PERSON>", "t_pond": "Ao nuôi", "t_pond_name_validation_msg": "<PERSON><PERSON>, ký tự và -, tối đa 15 ký tự ", "t_nursery_name_validation_msg": "", "t_pond_size": "", "t_past_cycle_summary": "", "t_abw_at_transfer": "", "t_privacy_policy": "<PERSON><PERSON><PERSON> s<PERSON> b<PERSON><PERSON> mật", "t_processing_status": "<PERSON><PERSON><PERSON><PERSON> thái xử lý", "t_quantity": "Số lượng", "t_received_invitation": "<PERSON><PERSON><PERSON> vị vừa đ<PERSON><PERSON><PERSON> mời tham gia", "t_invited_to_farm": "bởi người sử dụng", "t_reject": "<PERSON><PERSON> chối", "t_resend_code": "Gởi lại mã code", "t_reset_code": "Gởi lại mã code", "t_reset_password": "Gởi lại mật mã", "t_reset_password_description": "<PERSON><PERSON>g tôi vừa gởi một email có chứa mã code đến quý vị", "t_role": "<PERSON>ai trò", "t_save": "<PERSON><PERSON><PERSON>", "t_search_by_user_farm_or_status": "T<PERSON>m ki<PERSON>m theo tên, trang trại hay tình trạng", "t_select_existing_monitoring": "<PERSON><PERSON><PERSON> lần kiểm tra tôm hiện nay", "t_select_existing_or_enter_new_email": "<PERSON><PERSON><PERSON> email đã có sẵn hoặc nhập email mới", "t_select_farm": "<PERSON><PERSON>n trại nuôi", "t_select_file": "<PERSON><PERSON><PERSON> tài li<PERSON>u", "t_select_role": "<PERSON><PERSON><PERSON> vai trò", "t_send": "Gởi", "t_sign_up_instantly": "<PERSON><PERSON><PERSON> ký liền", "t_start_new_monitoring": "<PERSON><PERSON><PERSON> đ<PERSON>u kiểm tra tôm", "t_status": "<PERSON><PERSON><PERSON><PERSON> thái", "t_stock": "<PERSON><PERSON><PERSON> g<PERSON>", "t_stock_pond": "<PERSON><PERSON><PERSON> nu<PERSON>i", "t_stocking": "<PERSON><PERSON><PERSON> độ thả", "t_submit": "Gởi", "t_submit_monitoring": "Gởi lần kiểm tra tôm", "t_submitted": "Đã gởi", "t_supervisor_optional": "", "t_terms_of_service": "<PERSON><PERSON><PERSON><PERSON> c<PERSON><PERSON> dịch vụ", "t_today": "<PERSON><PERSON><PERSON> nay", "t_total": "Tổng", "t_total_images": "Tổng số hình <PERSON>nh", "t_trays": "<PERSON><PERSON>", "t_type_of_access": "<PERSON><PERSON><PERSON> truy c<PERSON>p", "t_unknown": "<PERSON><PERSON><PERSON>", "t_update": "<PERSON><PERSON><PERSON>", "t_uploaded_at": "<PERSON><PERSON> tải lên l<PERSON>c", "t_delete": "Delete", "t_verification_code": "Mã code xác thực", "t_verify_email": "<PERSON><PERSON><PERSON> thực email", "t_verify_email_description": "<PERSON><PERSON>g tôi đã gởi một email có chứa link xác thực đến quý vị", "t_verify_your_email": "<PERSON><PERSON><PERSON> email của quý vị", "t_verify_your_email_address": "<PERSON><PERSON><PERSON> thực địa chỉ email của quý vị", "t_view_images": "<PERSON><PERSON> h<PERSON>nh <PERSON>", "t_weight_distribution": "<PERSON><PERSON> phân bố trọng lư<PERSON>", "t_yesterday": "<PERSON><PERSON><PERSON> qua", "t_email_sent_successfully_check": "<PERSON>ail đã được gởi thành công, quý vị vui lòng kiểm tra", "t_sms_sent_successfully_check_mobile": "Tin nhắn đã được gởi thành công, quý vị vui lòng kiểm tra", "txt_email_schema_error": "<PERSON><PERSON> ph<PERSON>i có tối thiểu 7 ký tự (ex:<EMAIL>)", "txt_password_mismatch": "Mật mã và <PERSON> nhận Mật mã chưa khớp", "txt_passwords_reset_does_not_match": "Mật mã và <PERSON>c nhận Mật mã mới chưa khớp", "yup_email_is_invalid": "<PERSON><PERSON> đ<PERSON> hết hi<PERSON><PERSON> l<PERSON>c", "yup_enter_minimum_characters": "<PERSON><PERSON> lòng nhập tối thiểu  {{min}} ký tự", "yup_is_invalid": "{{label}} h<PERSON><PERSON> hi<PERSON> l<PERSON>c", "yup_is_required": "{{label}} l<PERSON> mụ<PERSON> cần ph<PERSON>i điền", "yup_not_type": "<PERSON><PERSON>, {{label}} b<PERSON><PERSON> bu<PERSON> ph<PERSON>i", "yup_should_be_greater_than_more": "{{label}} ph<PERSON><PERSON> có nhi<PERSON>u hơn {{more}}", "yup_should_be_greater_than_zero": "{{label}} ph<PERSON><PERSON> có n<PERSON> h<PERSON> 0", "yup_should_be_one_of": "{{label}} ph<PERSON>i là một của", "yup_should_not_be_less_than": "{{label}} ph<PERSON><PERSON> không đ<PERSON><PERSON>t hơn {{min}}", "yup_should_be_less_than_or_equal": "{{label}} nên ít hơn hoặc bằng {{max}}", "yup_should_be_integer": "{{label}} phải là một số nguyên", "yup_date_must_not_be_later_than": "", "yup_date_must_not_be_earlier_than": "", "t_heads_on": "<PERSON><PERSON><PERSON> đ<PERSON>u", "t_heads_off": "<PERSON><PERSON><PERSON>", "t_download": "<PERSON><PERSON><PERSON>", "t_days_ago_plural": "", "go_back": "Quay trở lại", "t_something_went_wrong": "Đ<PERSON> có sự cố", "t_actions": "<PERSON><PERSON><PERSON> đ<PERSON>", "t_data_from_previous_cycle": "<PERSON><PERSON><PERSON> vị đang xem vụ nuôi tr<PERSON><PERSON><PERSON>, thả giống để bắt đầu gi<PERSON>m sát ", "t_err_account_not_found": "<PERSON><PERSON><PERSON><PERSON> có tài khoản nào kết nối với email này", "t_err_code_is_invalid": "Mã code khôi phục đã hết hiệu lực", "t_err_email_or_password_incorrect": "<PERSON><PERSON> ho<PERSON> mật mã chưa đúng", "t_err_user_not_found": "kh<PERSON>ng thể tìm kiếm người sử dụng", "t_err_email_already_in_use": "<PERSON>ail này đã đư<PERSON>c sử dụng", "t_err_unauthorized": "<PERSON><PERSON><PERSON><PERSON> thể chứng thực đ<PERSON>", "t_err_pond_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy ao nào", "t_stocking_date": "<PERSON><PERSON><PERSON> gi<PERSON>", "t_processing": "<PERSON><PERSON> lý", "t_processing_failed": "<PERSON><PERSON> lý không thành công", "t_delete_monitoring": "<PERSON><PERSON><PERSON><PERSON> bao gồm lần kiểm tra", "t_delete_monitoring_msg": "Nếu quý vị loại trừ lần kiểm tra này, nó sẽ không được đưa vào dữ liệu hàng ngày và sẽ không được sử dụng cho các cảnh báo hoặc dự đoán.", "t_delete_monitoring_toast_title": "<PERSON><PERSON><PERSON> kiểm tra tôm đã được lo<PERSON>i bỏ", "t_delete_monitoring_toast_msg": "<PERSON><PERSON><PERSON> kiểm tra tôm đã được loại bỏ thành công", "t_error": "Sự cố", "t_page_not_found": "<PERSON><PERSON><PERSON><PERSON> tìm thấy trang nào", "t_go_home": "<PERSON><PERSON> trang chủ", "t_success": "<PERSON><PERSON><PERSON><PERSON> công", "t_verification_failed": "<PERSON><PERSON><PERSON> thực không thành công", "t_login_to_accept_invite": "<PERSON><PERSON><PERSON> vị cần đăng nhập để chấp nhận lời mời", "t_code_sent": "Mã code đã gởi thành công, quý vị vui lòng kiểm tra", "t_accepted_invitation": "<PERSON><PERSON><PERSON> vị đã chấp nhận lời mời", "t_account_verified": "<PERSON><PERSON><PERSON> vị đã xác thực tài khoản của mình thành công", "t_logged_in_success_title": "<PERSON><PERSON><PERSON> nh<PERSON>p thành công", "t_logged_in_success_msg": "<PERSON><PERSON><PERSON> vị đã đăng nhập thành công", "t_profile_updated": "<PERSON><PERSON><PERSON> vị đã cập nhật thông tin cá nhân thành công ", "t_updated_successfully": "<PERSON><PERSON><PERSON> nh<PERSON>t thành công", "t_invitation_email_sent": "<PERSON><PERSON> mời gia nhập đã được gởi", "t_user_membership_updated": "<PERSON><PERSON>ng thành viên đã đư<PERSON><PERSON> cập nhật thành công", "t_invitation_rejected": "<PERSON>uý vị đã từ chối lời mời thành công", "t_created_successfully": "Đ<PERSON> tạo xong ", "t_invitation_created": "<PERSON><PERSON><PERSON> vị đã tạo lời mời thành công", "t_password_changed": "<PERSON><PERSON>t mã thay đổi thành công", "t_password_reset_success": "<PERSON>uý vị đã khôi phục mật mã và đăng nhập thành công", "t_invitation_cancelled_title": "Huỷ bỏ lời mời", "t_invitation_cancelled_msg": "Quý vị đã huỷ bỏ lời mời", "t_phone_number_updated": "<PERSON><PERSON> điện thoại đã đ<PERSON><PERSON><PERSON> cập nhật", "t_email_updated": "<PERSON><PERSON> đ<PERSON> đ<PERSON><PERSON><PERSON> cậ<PERSON> nh<PERSON>t", "t_monitoring_submitted": "<PERSON><PERSON>n kiểm tra tôm đã được gởi lên hệ thống thành công", "t_manual_monitoring_created": "<PERSON><PERSON><PERSON><PERSON> sát thủ công đã đư<PERSON>c tạo thành công", "t_logged_in_success": "<PERSON><PERSON><PERSON> vị đã đăng nhập thành công", "t_change_your_email": "Thay <PERSON> email", "t_contact_admin_to_change_email": "", "t_farm_must_be_unique": "Tên trại nuôi phải là duy nhất", "t_not_invite_yourself": "<PERSON><PERSON><PERSON> vị không thể tự mời ch<PERSON>h mình", "t_percentage_sampled_animals": "{{percent}} c<PERSON><PERSON> số mẫu tôm nằm trong khoảng {{grams}} tới {{range}}", "t_aggregation_in_progress": "<PERSON><PERSON> đ<PERSON>ử lý, quý vị vui lòng chờ trong gi<PERSON> lát", "t_get_history_and_growth": "", "t_percentage_of_stocked_animals": "", "t_select_or_create_monitoring": "<PERSON><PERSON> lòng chọn lần kiểm tra tôm đã có sẵn hoặc lấy mẫu mới từ trên đây", "t_cv_learning": "CV là chỉ số để biểu thị lượng cỡ tôm trong ao và tôm có ăn đều không.", "t_cv_description_high": "", "t_cv_description_low": "<PERSON>hi CV thấp-tô<PERSON> đều,  có ít cỡ trong ao của quý vị, biểu thị quý vị đang làm rất tốt việc cho ăn.", "t_cv_fixing_distribution": "<PERSON><PERSON> phát hiện và sửa lỗi sự chênh lệch về kích cỡ tôm cao", "t_cv_xpertsea_alert": "", "t_then_you_can": "<PERSON><PERSON> đ<PERSON>, quý vị có thể:", "t_cv_review_pond_histogram": "", "t_cv_consider_following_our_tips": "<PERSON><PERSON> nhắc làm theo các mẹo của chúng tôi để cải thiện tình hình", "t_cv_tips_to_improve": "Mẹo để cải thiện", "t_cv_how_cv_calculated": "<PERSON><PERSON>ch tính CV", "t_cv_how_determined": "<PERSON><PERSON><PERSON> chúng tôi xác đ<PERSON>nh xem CV có cao không", "t_cv_select_the_average_body_weight": "<PERSON><PERSON><PERSON> trọng lượng tôm trung bình", "t_at": "<PERSON><PERSON><PERSON>", "t_after": "Sau", "t_consider_the_following": "<PERSON><PERSON> nh<PERSON>c thực hiện những việc sau:", "t_cv_05_description": "", "t_cv_10_description": "Vẫn còn tương đối tốt để đảm bảo sự phân đàn tốt, sau đó thiết lập ao cho một chu kỳ tăng trưởng mạnh mẽ.", "t_cv_15_description": "tối ưu hóa tốc độ tăng trưởng của tôm bằng cách cho ăn tất cả các kích cỡ khác nhau trong ao, không chỉ kích thước trung bình.", "t_cv_15_plus_description": "<PERSON><PERSON><PERSON> vị có thể giảm thiểu sự phân đàn trong 2-3 tuần bằng cách cho ăn tập trung trước khi thu hoạch.", "t_cv_step_analyze": "", "t_cv_step_analyze_to_understand": "", "t_cv_step_encourage": "<PERSON><PERSON><PERSON>ến khích tôm nhỏ ăn nhiều hơn bằng cách sử dụng kích cỡ viên thức ăn phù hợp. Sử dụng biểu đồ để xác định số lượng từng kích thước viên thức ăn sẽ sử dụng.", "t_cv_step_encourage_then": "", "t_cv_step_shrimp_far": "<PERSON><PERSON>u tôm ở xa máy cho ăn tự động, hãy dụ tôm đến khu vực cho ăn tự động bằng cách cho ăn thủ công.", "t_cv_step_smaller_shrimp_far": "<PERSON><PERSON><PERSON> tôm nhỏ hơn ở xa máy cho ăn tự động, hãy dụ chúng đến khu vực cho ăn tự động bằng cách cho ăn thủ công.", "t_cv_step_ensure": "<PERSON><PERSON><PERSON> bảo rằng có đủ máy cho ăn tự động trong ao, đ<PERSON><PERSON><PERSON> đặt ở vị trí phù hợp và hoạt động ổn định.", "t_cv_step_feed_away": "Cho tôm ăn xa những khu vực có đáy ao không sạch và giữ cho đáy ao không có chất hữu cơ bị phân hủy.", "t_cv_step_collect": "Kiểm tra nhiều mẫu tôm hơn trong quá trình nuôi (>300 con tôm) để tăng độ tin cậy của quý vị về kích cỡ tôm mà quý vị sẽ bán cho nhà máy chế biến.", "t_cv_step_focus": "Tập trung cho ăn những con tôm nhỏ hơn, nhắm mục tiêu cho ăn ở những khu vực trong ao nơi chúng được tìm thấy.", "t_cv_step_use": "Sử dụng raleo để loại bỏ tôm lớn hơn để tôm nhỏ có thêm không gian phát triển.", "t_cv_calculation_description": "<PERSON><PERSON><PERSON><PERSON> tính CV được bắt đầu bằng sự tính SD của mẫu.", "t_cv_calculation_description_long": "<PERSON><PERSON> lệch chuẩn (SD) được xác định bằng cách trước tiên tính toán sự khác biệt giữa trọng lượng của mỗi con tôm và trọng lượng trung bình của tất cả tôm. Những khác biệt này sau đó được chuyển đổi thành một số duy nhất, SD, đại diện cho sự thay đổi của các giá trị này.", "t_cv_calculation_description_short": "CV sau đó được tính bằng cách chia SD cho trọng lượng trung bình. Đi<PERSON>u này cho phép chúng tôi so sánh CV cho các trọng lượng tôm khác nhau.", "t_cv_calculation_examples": "Th<PERSON> dụ cho tôm cỡ 9g:", "t_cv_low": "CV 10 %", "t_cv_sd_low": "0.9 SD", "t_cv_medium": "CV 20 %", "t_cv_sd_medium": "1.8 SD", "t_cv_high": "CV 28 %", "t_cv_sd_high": "CV 28%", "t_cv_high_description": "", "t_cv_high_description_long": "", "t_cv_high_yellow": "<PERSON> (màu vàng)", "t_cv_high_yellow_desc": "<PERSON> hơ<PERSON> 51% các mẫu trước đây với cùng trọng lượng trung bình", "t_cv_very_high_red": "<PERSON><PERSON><PERSON> cao (màu đỏ)", "t_cv_very_high_red_desc": "- <PERSON><PERSON> cao hơn 85% các mẫu trước đây với cùng trọng lượng trung bình", "t_unharvest_pond": "<PERSON><PERSON> ch<PERSON><PERSON> thu ho<PERSON> (DEV)", "t_dispersion": "Sự phân tán", "t_target_weight": "Cỡ tôm kỳ vọng", "t_days_of_production": "<PERSON><PERSON> ngày nuôi", "t_target_harvest_date": "<PERSON><PERSON><PERSON> thu ho<PERSON>ch dự kiến", "t_set_target": "Cài đặt mục tiêu", "t_show_users_all_farms": "<PERSON><PERSON><PERSON> thị người sử dụng cho tất cả trại nuôi ", "t_edit": "Chỉnh sửa", "t_try_again": "<PERSON><PERSON><PERSON> lại", "t_target_at_risk": "<PERSON><PERSON><PERSON> tiêu có rủi ro", "t_remove": "Loại bỏ", "t_confirm_remove_target": "Quý vị chắc chắn muốn loại bỏ mục tiêu cho ao này?", "t_set_later": "Đợi sau", "t_needs_attention": "<PERSON><PERSON><PERSON> thêm s<PERSON> chú ý", "t_select_date": "<PERSON><PERSON><PERSON>", "t_average_weight_should_be_less_than_first_monitoring_average_weight": " <PERSON><PERSON><PERSON><PERSON> lượng trung bình nên thấp hơn {{firstMonitoringAverageWeight}}", "t_growth_rate_since_monitoring": "Tốc độ tăng trưởng từ lần cân tôm trước đây", "t_growth_in": "", "t_growth_day": "Tăng trưởng / ngày = {{dailyGrowth}} g", "t_weekly_growth_seven_days": "T<PERSON><PERSON> độ tăng trưởng tuần = {{lastWeekGrowth}} g", "t_min_monitored_weight": "<PERSON><PERSON>i đạt kích cỡ tốt hơn lần kiểm tra tôm gần đây ({{weight}} g)", "t_min_target_weight": "<PERSON><PERSON><PERSON> đạt tối thiểu {{weight}} g", "t_max_target_weight": "<PERSON><PERSON><PERSON> thấp hơn {{weight}} g", "t_min_target_stocking_days": "<PERSON><PERSON><PERSON> đạt kích cỡ lớn hơn từ lúc thả giống ({{days}} days)", "t_min_target_days": "<PERSON><PERSON><PERSON> tối thiểu {{days}} ngày", "t_integer_target_days": "<PERSON><PERSON><PERSON><PERSON> thể có số thập phân (e.g. 90)", "t_growth_warning_prefix": "<PERSON><PERSON><PERSON> tiêu sản xuất sẽ cần yêu cầu tốc độ tăng trưởng trung bình của", "t_growth_warning_rate": "{{rate}} g.", "t_growth_warning_suffix": "Hoặc chỉnh sửa mục tiêu của quý vị, hoặc kiểm tra ở đây để xác nhận và tiếp tục.", "t_target_harvest_date_updated": "Ng<PERSON><PERSON> dự kiến thu hoạch đã đ<PERSON><PERSON><PERSON> cập nh<PERSON> <b>{{ngày}}</b> ngày sau khi thả giống", "t_target_harvest_date_updated_confirm": "<PERSON><PERSON><PERSON> nhận hoặc chỉnh sửa mục tiêu và chọn C<PERSON> nhật ", "t_set_survival_rate": "<PERSON><PERSON><PERSON><PERSON>", "t_survival_form_title": "", "t_enter_survival": "", "t_edit_survival": "", "t_survival_final_harvest": "", "t_survival": "Tỷ l<PERSON> sống", "t_kampi_survival": "", "t_biomass": "<PERSON><PERSON><PERSON>", "t_survival_rate_tooltip": "", "t_biomass_tooltip": "", "t_stocking_weight_g": "", "t_processing_failed_tooltip_title": "<PERSON><PERSON><PERSON>nh của quý vị đã đư<PERSON><PERSON> tải lên nhưng quá trình xử lý không thành công.", "t_processing_failed_tooltip_subtitle": "", "t_pallet_size": "Cỡ viên thức ăn", "t_practical_analysis_error": "<PERSON><PERSON><PERSON><PERSON> được có bất kỳ giá trị trọng lượng hoặc kích thước viên thức ăn nào bị trùng lặp", "t_practical_modal_header": "<PERSON><PERSON><PERSON><PERSON> kích thư<PERSON>c viên đ<PERSON><PERSON><PERSON> tăng lên trong trại của quý vị.", "t_practical_analysis_tooltip_header": "<PERSON><PERSON> tích kích thước viên thức ăn", "t_practical_analysis_tooltip_desc": "<PERSON><PERSON><PERSON><PERSON> trọng lượng trong đó kích thước hạt thay đổi để thiết lập sản lượng tương ứng với từng kích thước viên thức ăn.", "t_cancel_save_changes": "<PERSON><PERSON><PERSON> l<PERSON>u thay đổi", "t_cancel_save_changes_confirmation": "", "t_weight_error": "Trọng lượng phải lớn hơn trọng lượng trước đó", "t_weight_g": "<PERSON><PERSON><PERSON><PERSON> (g)", "t_confirm_remove_feed_pallets": "Bạn có xác nhận việc loại bỏ viên Thức ăn khỏi tất cả các ao trong trại nuôi của bạn không?", "t_invalid_weight": "ABW của bạn có vẻ lớn hơn 40g hoặc nhỏ hơn 2g. Chúng tôi đang tích cực làm việc để mở rộng hỗ trợ các trọng lượng trong phạm vi này, nhưng hiện tại chúng tôi không thể hiển thị dữ liệu khi ABW lớn hơn 40g nhỏ hơn 2g.", "t_growth_ahead": "Tăng trưởng vượt mục tiêu!", "t_on_track_to_hit_target": "", "t_check_stock": "<PERSON><PERSON><PERSON> tra thông tin thả giống", "t_check_sample": "<PERSON><PERSON><PERSON> tra mẫu tôm", "t_feed_type": "<PERSON><PERSON><PERSON> thức ăn", "t_last_updated_on": "<PERSON><PERSON><PERSON> nhật gần nhất vào", "t_cost": "", "t_cost_$": "", "t_kg": "kg", "t_select": "<PERSON><PERSON><PERSON>", "t_size": "<PERSON><PERSON><PERSON> cỡ", "t_daily_mortality": "Tỷ l<PERSON> chết hàng ngày", "t_abw": "<PERSON>r<PERSON><PERSON> lượng trung bình", "t_abw_g": "", "t_no_field_duplicated_values": "<PERSON><PERSON><PERSON> đ<PERSON> bảo mỗi giá trị {{field}} là duy nhất", "t_planned": "<PERSON><PERSON><PERSON><PERSON> lên kế ho<PERSON>ch", "t_left": "Trái", "t_right": "<PERSON><PERSON><PERSON>", "t_save_changes": "<PERSON><PERSON><PERSON> sự thay đổi", "t_select_option": "<PERSON><PERSON><PERSON> tùy chọn", "t_no_data_available": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "t_monday": "<PERSON><PERSON><PERSON> hai", "t_tuesday": "<PERSON><PERSON><PERSON> ba", "t_wednesday": "<PERSON><PERSON><PERSON> tư", "t_thursday": "<PERSON><PERSON><PERSON> năm", "t_friday": "<PERSON><PERSON><PERSON> s<PERSON>u", "t_saturday": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "t_sunday": "<PERSON>ủ <PERSON>h<PERSON>", "t_no_options": "<PERSON><PERSON><PERSON><PERSON> có tùy chọn", "t_fcr": "FCR", "t_setting_pond_bottom_msg": "<PERSON><PERSON><PERSON> đầu gi<PERSON>m sát ao tôm", "t_pond_monitoring_after_harvest": "", "t_required": "*<PERSON><PERSON><PERSON> bu<PERSON>", "t_normal": "<PERSON><PERSON><PERSON>", "t_err_file_size_limit_exceeded": "Dung lượng file v<PERSON><PERSON><PERSON> quá giới hạn ", "t_symmetry": "<PERSON><PERSON>", "t_symmetry_left_title": "Tôm dưới cỡ phân loại", "t_symmetry_left_info": "", "t_symmetry_right_title": "Tôm nhỏ nhiểu ảnh hưởng đến FCR của quý vị", "t_symmetry_right_info": "{{animalsPrecentBelowHeuristic}}% tôm dưới trọng lượng trung bình {{heuristicGramsBelowABW}} grams. Tập trung nuôi số tôm này để cải thiện trọng lượng trung bình và và FCR của ao nuôi.", "t_symmetry_normal_info": "Cỡ tôm phân bố đều trên trọng lượng trung bình", "t_symmetry_few_animals_note": "<PERSON><PERSON> lượng mẫu tôm quá ít, cần tăng lượng tôm lên ít nhất 150 để tăng độ tin cậy trong sồ liệu phân tích", "t_g_in_days": "", "t_record_partial_harvest": "<PERSON><PERSON>u thu tỉa", "t_plan_partial_harvest": "", "t_biomass_harvested_unit_ha": "", "t_biomass_harvested": "", "t_harvest_plan": "", "t_estimated_animals_harvested": "Ước lượng lượng tôm thu hoạch", "t_animals_harvested_%": "", "t_partial_harvest_quantity_error": "", "t_add_notes": "<PERSON><PERSON><PERSON><PERSON> ghi chú", "t_start_monitoring": "", "t_edit_manual_monitoring": "Chỉnh sửa giám sát thủ công", "t_show_me_how": "Chỉ tôi như thế nào", "t_back": "Quay trở lại", "t_head_on": "", "t_headless": "", "t_configuration": "Cài đặt", "t_search": "<PERSON><PERSON><PERSON>", "t_survival_%_after_partial_harvest": "", "t_survival_will_be_assigned_to": "", "t_survival_will_not_be_assigned_to_monitoring": "", "t_partial_harvest": "<PERSON>hu tỉa", "t_x_harvested_on": "{{percent}}% đ<PERSON><PERSON><PERSON> thu hoạch vào {{date}}", "t_ensure_survival_included": "", "t_ensure_survival_included_tooltip": "Ví dụ: Tỉ lệ sống đang được nhập có % số tôm được thu tỉa bị trừ đi, số tôm được thu tỉa = số tôm chết", "t_growth_rate": "<PERSON><PERSON><PERSON> độ tăng trưởng", "t_revenue": "", "t_users_at_farm": "Ngư<PERSON>i nuôi tôm ở {{farmName}}", "t_users_at_all_farms": "Ngườ<PERSON> dùng tất cả các trang trại", "t_stock_your_pond_to_start_getting": "", "t_stock_your_nursery_to_start_getting": "", "t_pond_actions": "<PERSON><PERSON><PERSON> động ao", "t_edit_pond_details": "Chỉnh sửa chi tiết ao", "t_pond_archived_successfully": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> lưu trữ thành công", "t_present": "<PERSON><PERSON><PERSON> t<PERSON>i", "t_projection_variables": "<PERSON><PERSON> đo<PERSON> các <PERSON>n", "t_profit_include_dry_days_ha_day": "", "t_partial_harvest_revenue": "<PERSON><PERSON>h thu thu tỉa", "t_add_price_list": "<PERSON><PERSON><PERSON><PERSON> danh s<PERSON>ch giá", "t_empty_cycle": "<PERSON><PERSON> nuôi trống", "t_dry_days": "<PERSON><PERSON><PERSON>", "t_days_in_nursery": "<PERSON><PERSON> ng<PERSON> ", "t_nursery_survival": "Tỉ lệ sống ương", "t_larva_order_time": "<PERSON>h<PERSON>i gian đặt giống tôm", "t_price_list": "<PERSON><PERSON> s<PERSON>ch giá", "t_pond_cost": "Chi phí ao nuôi", "t_min_allowed_stocked": "<PERSON><PERSON><PERSON> thả không đ<PERSON><PERSON><PERSON> quá 2 năm", "t_profit": "", "t_rofi": "", "t_last_updated_on_by": "<PERSON><PERSON><PERSON> cập nhật gần nhất vào {{date}} bởi {{name}}", "t_add_lab_name": "<PERSON>hê<PERSON> tên phòng thí nghiệm", "t_monitoring_updated_successfully": "<PERSON><PERSON><PERSON> nh<PERSON>t gi<PERSON>m sát thành công", "t_production": "<PERSON><PERSON><PERSON>", "t_number_of_images": "Số lư<PERSON>", "t_buy_images": "<PERSON>a {{sliderValue}} h<PERSON>nh <PERSON> ({{totalAmount}} ch<PERSON>a bao gồm thuế)", "t_use_slider_or_input_number_of_images": "<PERSON>ử dụng thanh trượt hoặc nhập số lượng hình ảnh quý vị muốn mua", "t_weight_stocked_on_date": "{{weight}}g thả vào ng<PERSON>y {{date}}", "t_revenue_per_pound": "", "t_revenue_per_kg": "", "t_no_lab_names": "Không có tên phòng Lab", "t_no_genetic_names": "<PERSON><PERSON><PERSON><PERSON> có tên nguồn gen", "t_my_team": "<PERSON>hóm của tôi", "t_planned_harvest": "<PERSON><PERSON> kiến kế hoạch thu hoạch", "t_recorded_harvest": "", "t_no_growth_data": "<PERSON><PERSON><PERSON>ng có thông tin tăng trường cho ao này", "t_enter_weekly_growth_data": "<PERSON><PERSON><PERSON><PERSON> tốc độ tăng trưởng tuần", "t_enter_growth_data_or_monitor_pond": "Nhập dữ liệu tăng trưởng hoặc giám sát ao tôm của bạn để thấy kết quả lợi nhuận!", "t_no_past_cycles": "Không có vụ nuôi trước", "t_your_pond_is_empty": "<PERSON><PERSON> hi<PERSON>n đang trống", "t_stock_pond_to_get_started": "<PERSON><PERSON><PERSON> tôm để bắt đầu ngay", "t_stock_your_pond": "", "t_last_monitoring": "<PERSON><PERSON><PERSON> gi<PERSON>m s<PERSON>t gần nh<PERSON>t", "t_latest_monitoring": "", "t_current_biomass": "<PERSON><PERSON><PERSON> l<PERSON><PERSON> hiện tại", "t_planned_final_harvest": "", "t_projected_profit": "", "t_projected_revenue": "", "t_cost_to_date_estimated": "<PERSON> phí tính đến hiện tại (ư<PERSON>c t<PERSON>)", "t_feed_given_daily_kg_ha": "", "t_nurseries": "", "t_harvest_weight_g": "", "t_cycle_length": "", "t_add_liquidation_report": "", "t_liquidation_report_format": "", "t_record_harvest": "", "t_custom": "", "t_cycle_info": "", "t_add_new_size": "", "t_no_ponds_in_your_farm": "", "t_no_nurseries_in_your_farm": "", "t_create_a_new_pond": "", "t_create_a_new_nursery": "", "t_end_of_cycle": "", "t_hint_you_can_copy_and_paste_from_excel": "", "t_add_weight": "", "t_overhead_cost_tooltip": "", "t_gram_g": "", "t_max_x": "", "t_min_x": "", "t_fcr_tooltip": "", "t_kg_ha_tooltip": "", "t_field_should_be_less_or_equal_to_x": "", "t_count_fcr": "", "t_count_days": "", "t_count_day": "", "t_cost_per_pound": "", "t_cost_per_kg": "", "t_update_data": "", "t_weekly_programming": "", "t_feeding_day": "", "t_feeding_day_tooltip": "", "t_monitoring_days": "", "t_manual_weight_generic_validation": "", "t_less_than": "", "t_harvest_optimizer": "", "t_data_updater": "", "t_harvest_type": "", "t_market": "", "t_projected_survival_must_be_future": "", "t_projected_survival_must_be_lower_than_current": "", "t_avg_days_of_culture": "", "t_avg_survival": "", "t_avg_fcr": "", "t_fcr_cumulative": "", "t_farm_summary": "", "t_overview": "", "t_simulation": "", "t_summary": "", "t_more_than": "", "t_x_of_culture": "", "t_selected": "", "t_growth_g_wk": "", "t_growth_g_day": "", "t_growth_monitored_x_week_g_wk": "", "t_feed": "", "t_fcr_weekly": "", "t_filter": "", "t_clear": "", "t_apply": "", "t_group_summary": "", "t_expected_at_harvest": "", "t_customize_view": "", "t_missing_latest_data": "", "t_indicating_status": "", "t_financials": "", "t_data_shown_from_last_monitoring": "", "t_data_shown_based_on_current_date": "", "t_days_until_harvest": "", "t_days_of_culture": "", "t_weights": "", "t_feed_given_weekly_kg": "", "t_feed_%_of_biomass": "", "t_animals_in_pond_m2": "", "t_animals_in_pond_ha": "", "t_dispersion_cv": "", "t_yup_should_be_less_than_max": "", "t_config": "", "t_account": "", "t_growth_g_over_d": "", "t_lb_over_ha": "", "t_kg_over_ha": "", "t_over_m2": "", "t_over_ha": "", "t_total_sale_value": "", "t_avg_fcr_cumulative": "", "t_avg_kg_ha_day": "", "t_avg_total_feed_given_kg": "", "t_avg_total_feed_given_lb": "", "t_biomass_lb_total": "", "t_biomass_kg_total": "", "t_biomass_lb_ha": "", "t_biomass_kg_ha": "", "t_biomass_lb_ha_day": "", "t_biomass_kg_ha_day": "", "t_financial": "", "t_avg_abw_g": "", "t_avg_2wk_growth": "", "t_avg_weekly_growth": "", "t_over_ha_over_day": "", "t_kg_over_ha_over_day": "", "t_g_over_wk": "", "t_lb": "", "t_invitation": "", "t_what_is_cv_learning": "", "t_no_farms_found": "", "t_no_ponds_found": "", "t_no_cycles_found": "", "t_last_week_growth": "", "t_this_farm_data_is_outdated": "", "t_some_of_your_ponds_have_not_been_monitored": "", "t_some_of_your_ponds_are_missing_data": "", "t_percent_of_harvest_as_head_on": "", "t_percent_of_head_on_as_class_a": "", "t_percent_of_head_on_as_class_b": "", "t_percent_of_harvest_as_leftover": "", "t_process_leftover_as": "", "t_rejects": "", "t_percent_of_leftover_as_class_a": "", "t_percent_of_leftover_as_class_b": "", "t_percent_of_tail": "", "t_percent_of_harvest_as_headless_class_a": "", "t_percent_of_harvest_as_headless_class_b": "", "t_processors": "", "t_production_targets": "", "t_target_production_cycle": "", "t_stocking_defaults": "", "t_changes_will_apply_to_all_farms": "", "t_group_config_confirmation": "", "t_are_you_sure_you_want_to_save_changes": "", "t_change_confirmation": "", "t_feed_tables": "", "t_feed_table": "", "t_of_biomass_to_feed": "", "t_select_your_default_feed_table": "", "t_add_feed_table": "", "t_duplicated_table_name": "", "t_default": "", "t_avg_stocking_weight_g": "", "t_autofeeders": "", "t_no_autofeeders": "", "t_add_autofeeder_type": "", "t_aerators": "", "t_no_aerators": "", "t_add_aerator_type": "", "t_add_horsepower": "", "t_recorded": "", "t_record": "", "t_delete_planned_partial_harvest": "", "t_delete_recorded_partial_harvest": "", "t_planned_partial_harvest_on": "", "t_planned_harvest_on": "", "t_has_passed": "", "t_partial_harvest_passed": "", "t_harvest_passed_msg": "", "t_edit_harvest_plan": "", "t_data": "", "t_stocking_cost": "", "t_targets": "", "t_primary_feed_brand": "", "t_projections": "", "t_feed_amount": "", "t_processor_price_lists": "", "t_select_your_default_price_list": "", "t_class_a_price_unit": "", "t_class_b_price_unit": "", "t_price_rejection_unit": "", "t_processor": "", "t_enter_price_list_name": "", "t_select_your_default_harvest_type": "", "t_expected_quality_of_harvest_head_on": "", "t_percent_of_leftover_as_headless_class_a": "", "t_percent_of_leftover_as_headless_class_b": "", "t_percent_of_headless_class_a": "", "t_percent_of_headless_class_b": "", "t_expected_quality_of_harvest_headless_direct": "", "t_expected_percent_tail_of_body": "", "t_percent_tail": "", "t_carrying_capacity": "", "t_feed_data": "", "t_weeks": "", "t_no_feed_types_available": "", "t_rev_lb": "", "t_rev_kg": "", "t_profit_lb": "", "t_profit_kg": "", "t_feed_types": "", "t_select_default_feed_to_use_for_cost_projection": "", "t_add_feed_type": "", "t_brand": "", "t_pellet_size_opt": "", "t_cost_over_kg": "", "t_please_select_default_feed_type": "", "t_stocking_density_ha": "", "t_stocking_density_m2": "", "t_autofeeder_brand": "", "t_feed_brand": "", "t_target_config_tooltip": "", "t_farm_book": "", "t_cycle_week": "", "t_revenue_at_harvest": "", "t_biomass_at_harvest": "", "t_weekly_production_data": "", "t_weekly": "", "t_daily": "", "t_monthly": "", "t_percent_compared_to_feed_table": "", "t_carrying_capacity_lb_ha": "", "t_carrying_capacity_kg_ha": "", "t_#_of_autofeeders": "", "t_#_of_aerators": "", "t_autofeeder_protocol": "", "t_next": "", "t_lab_source": "", "t_nauplii_source": "", "t_day_s": "", "t_confirm_stocking_details": "", "t_confirm_targets": "", "t_confirm_cycle_protocol": "", "t_timer": "", "t_time_of_day": "", "t_anytime": "", "t_hydrophone": "", "t_feed_brand_to_use": "", "t_growout_feed_type": "", "t_record_up_to_one_minute": "", "t_general_channel": "", "t_projected": "", "t_aerators_per_ha": "", "t_type_of_aerator": "", "t_pond_setup": "", "t_nursery": "", "t_worse_than_target": "", "t_better_than_target": "", "t_you_dont_have_permission_to_see_this_page": "", "t_no_history_registered_for_any_feed_days": "", "t_upload_report": "", "t_from_feed_table": "", "t_biomass_in_pond_unit": "", "t_biomass_in_pond_unit_ha": "", "t_biomass_include_harvests_unit": "", "t_biomass_include_harvests_unit_ha": "", "t_survival_live": "", "t_survival_include_harvests": "", "t_": "", "t_dashboard": "", "t_profile": "", "t_min": "", "t_max": "", "t_hello": "", "t_filtered_on": "", "t_total_number_of_ponds": "", "t_stocked_ponds": "", "t_dry_ponds": "", "t_current_view": "", "t_not_selected_in_pond_card": "", "t_my_farms": "", "t_select_up_to_x_variables_you_want": "", "t_gps_distance": "", "t_monitoring_distance_from_pond": "", "t_monitoring_location": "", "t_monitoring_distance_to_pond": "", "t_monitoring_x_distance_to_pond": "", "t_monitoring_x_is_near_pond": "", "t_near_pond": "", "t_monitoring_is_near_pond": "", "t_latest_monitoring_location": "", "t_overhead_cost_cumulative": "", "t_overhead_cost_dry_days": "", "t_data_shown_across_all_ponds": "", "t_farm_not_found": "", "t_farm_not_found_desc": "", "t_invitations_for_a_farm": "", "t_payment": "", "t_population": "", "t_settings": "", "t_select_farm_to_continue": "", "t_forgot_password_desc": "", "t_log_in_desc": "", "t_log_out": "", "t_unsubscribe": "", "t_unsubscribe_desc": "", "t_reset_password_desc": "", "t_sign_up": "", "t_verify_email_desc": "", "t_view_invitation": "", "t_nursery_archived_successfully": "", "t_archive_nursery": "", "t_stock_nursery": "", "t_edit_nursery_details": "", "t_add_nursery_pond": "", "t_this_nursery_is_empty": "", "t_nursery_summary": "", "t_transfer_date": "", "t_transfer_pond": "", "t_edit_transfer": "", "t_transfer": "", "t_record_transfer": "", "t_select_input": "", "t_animals_%": "", "t_add_destination": "", "t_transfer_summary": "", "t_cost_millar": "", "t_type_of_stocking": "", "t_destination_ponds": "", "t_destination": "", "t_planned_stocking": "", "t_transfer_history": "", "t_variables": "", "t_invoiced_quantity": "", "t_invoiced_quantity_opt": "", "t_end_of_cycle_survival": "", "t_feed_cost": "", "t_indirect_costs": "", "t_fixed_costs": "", "t_other_costs": "", "t_past_cycle": "", "t_nursery_last_cycle_msg": "", "t_past": "", "t_edit_stocking_information_to_link_transfers": "", "t_add_stocking_information_to_link_transfers": "", "t_you_have_incoming_transfers_to_this_pond_from": "", "t_growout": "", "t_you_are_missing_important_stocking_information": "", "t_add_details_now": "", "t_transferred_on": "", "t_this_pond_has_incoming_transfers_from": "", "t_this_pond_has_accepted_transfers_from": "", "t_is_this_stocking_missing_transfer_information": "", "t_manage_nurseries": "", "t_edit_direct_stocking": "", "t_stock_directly": "", "t_stock_from_transfer": "", "t_would_you_like_to_link_this_cycle_with_those_transfers": "", "t_Total_biomass_transferred": "", "t_transfer_amount": "", "t_feed_cost_per_kg": "", "t_feed_cost_$": "", "t_up_to_date": "", "t_using_imputed_values": "", "t_estimated_values_are_used": "", "t_direct": "", "t_flagged_ponds": "", "t_delete_confirmation": "", "t_are_you_sure_you_want_to_continue": "", "t_created_by": "", "t_head_on_abbreviation": "", "t_head_less_abbreviation": "", "t_expected_feed": "", "t_transfer_confirmation": "", "t_edit_transfer_confirmation": "", "t_transfer_confirmation_info": "", "t_transfer_confirmation_question": "", "t_edit_transfer_confirmation_msg": "", "t_nursery_data_updater": "", "t_transfer_data": "", "t_harvest_type_classification": "", "t_total_transferred_animals_should_be_less_than": "", "t_grams_change": "", "t_count_percent_day": "", "t_kampi": "", "t_until_harvest": "", "t_projections_not_available": "", "t_projections_not_available_for_pond": "", "t_x_days_earlier": "", "t_x_days_later": "", "t_x_day_earlier": "", "t_x_day_later": "", "t_optimal_harvest": "", "t_gps_location_appears_farm_from_the_pond": "", "t_estimate_data_alert_msg": "", "t_overhead_cost_ha_day": "", "t_other_direct_costs": "", "t_revenue_processed_unit": "", "t_manage_projections": "", "t_class_size": "", "t_biomass_kg": "", "t_biomass_lb": "", "t_expected": "", "t_amount_unit": "", "t_pond_summary": "", "t_protocol": "", "t_harvest_summary": "", "t_pond_source": "", "t_autofeeders_ha": "", "t_selected_x": "", "t_comparison_to_target": "", "t_add_manual_weight_entry": "", "t_target": "", "t_mobile_graph_msg": "", "t_rotate_your_phone_content_msg": "", "t_click_here_to_see_graph": "", "t_click_here_to_see_table": "", "t_click_to_see_weekly_production_data": "", "t_last_monitored_on_x": "", "t_range_g": "", "t_range": "", "t_feed_type_percentage_sum_error": "", "t_pond_performance_required": "", "t_variable": "", "t_biomass_processed_without_price": "", "t_i_am_writing_the_message_here": "", "t_update_default_processor": "", "t_delete_processor": "", "t_updated_default_processor_modal_text_1": "", "t_updated_default_processor_modal_text_2": "", "t_delete_processor_modal_text_1": "", "t_delete_processor_modal_text_2": "", "t_no_only_apply_to_future_assignments": "", "t_yes_apply_to_all_ponds_and_farms": "", "t_update_processor_confirmation": "", "t_update_processor_confirmation_text": "", "t_yes_apply": "", "t_no_keep_current_assignments": "", "t_revenue_per_lb_live": "", "t_revenue_per_kg_live": "", "t_revenue_per_lb_include_harvest": "", "t_revenue_per_kg_include_harvest": "", "t_head_on_distribution": "", "t_head_less_distribution": "", "t_harvest_expected": "", "t_performance": "", "t_above_target": "", "t_on_track": "", "t_off_track": "", "t_show_ponds_that_are": "", "t_above": "", "t_above_x_percent_carrying_capacity": "", "t_customize_graph": "", "t_projection_length": "", "t_show_ponds_with_a_carrying_capacity": "", "t_x_ponds_are_above_carrying_capacity": "", "t_x_ponds_are_above_carrying_capacity_singular": "", "t_daily_parameters": "", "t_only_override_weights_when_a_monitoring_has_been_taken": "", "t_no_monitoring": "", "t_revert_to_kampi_weight": "", "t_kampi_weight_x_g": "", "t_kampi_weight_g": "", "t_updated_by_name": "", "t_manual_weight_confirmation": "", "t_manual_weight_confirmation_description": "", "t_daily_param_monitoring_alert": "", "t_daily_param_not_stocked_alert": "", "t_overwrite_weights": "", "t_invalid_average_weight": "", "t_parameter": "", "t_%_of_carrying_capacity": "", "t_data_check": "", "t_ok": "", "t_missing": "", "t_target_type": "", "t_target_survival": "", "t_target_biomass_to_harvest": "", "t_target_cycle_length": "", "t_target_harvest_weight": "", "t_target_fcr": "", "t_projected_feed_type": "", "t_projected_growth_grams": "", "t_projected_growth_days": "", "t_projected_survival_type": "", "t_daily_mortality_percent": "", "t_expected_target_survival": "", "t_expected_target_survival_days": "", "t_feed_data_for_last_monitoring": "", "t_selected_processor": "", "t_selected_processor_exists": "", "t_profit_projection": "", "t_production_projection": "", "t_cycle_comparison_projection": "", "t_active_processor_price_lists": "", "t_active_feed_table": "", "t_active_feed_types": "", "t_monitoring_days_config": "", "t_start_of_week_config": "", "t_farm_production_days": "", "t_farm_weight": "", "t_farm_fcr": "", "t_farm_cost_per_pound": "", "t_farm_profit_per_ha_per_day": "", "t_farm_biomass_lb_ha": "", "t_farm_dry_days": "", "t_farm_growth_density": "", "t_farm_days_in_nursery": "", "t_farm_nursery_survival": "", "t_farm_avg_stocking_weight": "", "t_value": "", "t_using_custom_values_for_projection": "", "t_production_chart_alert": "", "t_show_daily_parameters": "", "t_feed_given_kg": "", "t_feed_given_lg": "", "t_units": "", "t_unit": "", "t_affected_areas_with_biomass_unit": "", "t_edit_daily_feed_msg": "", "t_cycle_comparison": "", "t_benchmark": "", "t_update_plan": "", "t_animals_stocked_ha": "", "t_animals_stocked": "", "t_biomass_from_partials_unit_ha": "", "t_biomass_from_partials_unit": "", "t_biomass_from_final_unit": "", "t_biomass_processed": "", "t_biomass_unpriced": "", "t_date_of_last_partial_harvest": "", "t_partial_harvest_done": "", "t_last_week": "", "t_x_weeks_ago": "", "t_select_variables_you_want_to_be_shown": "", "t_animals_harvested_per_m2": "", "t_animals_harvested_ha": "", "t_production_days": "", "t_using_imputed_values_for_calculations": "", "x_monitoring_location_from_the_pond": "", "x_monitoring_location_near_the_pond": "", "t_no_cycles": "", "t_select_cycle": "", "t_only_add_survival_when_a_monitoring_has_been_taken": "", "t_kampi_mortality": "", "t_total_biomass_biomass_over_ha": "", "t_total_profit_profit_over_x": "", "t_processed": "", "t_harvested": "", "t_selected_variables": "", "t_selected_variables_sort_desc": "", "t_cycle_information": "", "t_revenue_per_unit_processed": "", "t_revenue_per_unit_harvested": "", "t_profit_per_unit_processed": "", "t_profit_per_unit_harvested": "", "t_cost_per_unit_processed": "", "t_cost_per_unit_harvested": "", "t_add_parameter": "", "t_delete_daily_param_alert": "", "t_param_name_already_exists": "", "t_prof_ha_d": "", "t_weekly_linear_growth": "", "t_daily_linear_growth": "", "t_animals_harvested": "", "t_anim_ha": "", "t_anim_m2": "", "t_biomass_lb_partial_harvest": "", "t_biomass_lb_final_harvest": "", "t_bioL_p": "", "t_bioL_f": "", "t_cost_p_lb": "", "t_cost_h_lb": "", "t_rev": "", "t_rev_p": "", "t_rev_h": "", "t_prof": "", "t_kampi_mortality_survival_target": "", "t_kampi_mortality_survival_target_tooltip": "", "t_days_d": "", "t_select_pond": "", "t_count_ponds_plural": "", "t_count_ponds_singular": "", "t_enter": "", "t_drag_and_drop_file_or": "", "t_brows_computer": "", "t_to": "", "t_use_kampi_expected_revenue": "", "t_partial_harvest_confirmation": "", "t_partial_harvest_change_description": "", "t_harvest_pond_confirmation": "", "t_harvest_change_description": "", "t_harvest_plan_change_description": "", "t_partial_harvest_plan_change_description": "", "t_unread_messages": "", "t_all_messages": "", "t_cycle_summary": "", "t_final_harvest": "", "t_general": "", "t_profit_revenue": "", "t_fcr_adjusted": "", "t_no_partial_harvest_alert": "", "t_partial_harvest_x": "", "t_final_harvest_plan": "", "t_final_harvest_recorded": "", "t_partial_harvest_count_plan": "", "t_partial_harvest_count_recorded": "", "t_export": "", "t_export_farm_summary_table": "", "t_export_daily_parameters_table": "", "t_add_farm_overview_data": "", "t_select_a_view": "", "t_total_animals_detected": "", "t_export_weekly_production_data_table": "", "t_your_data_exported_in_xlsx": "", "t_weekly_monitoring_data": "", "t_ponds": "", "t_harvests": "", "t_final": "", "t_partial": "", "t_next_30_d": "", "t_past_30_d": "", "t_type": "", "t_delete_harvest_confirmation": "", "t_you_are_about_to_delete_harvest": "", "t_count_final_count_partial": "", "t_count_plan_count_recorded": "", "t_count_ponds": "", "t_harvest_plan_recorded_msg": "", "t_harvest_plan_planned_msg": "", "t_harvest_recorded": "", "t_count_cycles": "", "t_create_nursery_pond": "", "t_rev_per_pound_processed": "", "t_rev_per_pound_harvested": "", "t_count_per_ha": "", "t_pls_per_g": "", "t_quantity_from_hatcheries": "", "t_notifications": "", "t_push": "", "t_below": "", "t_nearing_carrying_capacity": "", "t_in": "", "t_notifications_and_rule_error": "", "t_carrying_capacity_reached": "", "t_current": "", "t_kampi_notification_center": "", "t_all_notifications": "", "t_read": "", "t_unread": "", "t_sudden_temperature_drop": "", "t_sudden_temperature_increase": "", "t_am_water_temperature_drop": "", "t_pm_water_temperature_drop": "", "t_am_do_value": "", "t_pm_do_value": "", "t_temperature_dropped_notification": "", "t_temperature_increased_notification": "", "t_carrying_capacity_reached_notification": "", "t_carrying_capacity_will_be_reached_notification": "", "t_tomorrow": "", "t_carrying_capacity_reached_on_ponds": "", "t_carrying_capacity_will_be_reached_on_ponds": "", "t_sudden_temperature_change_ponds": "", "t_no_notifications": "", "t_mg_per_L": "", "t_degree_celsius": "", "t_hide_table": "", "t_show_table": "", "t_average_daily_feed": "", "t_bags": "", "t_water_am_temperature_unit": "", "t_temp_am_unit": "", "t_water_pm_temperature_unit": "", "t_temp_pm_unit": "", "t_dissolved_am_oxygen_unit": "", "t_DO_am_unit": "", "t_dissolved_pm_oxygen_unit": "", "t_DO_pm_unit": "", "t_harvest_actions": "", "t_x_final_harvest_title": "", "t_edit_final_harvest_plan_title": "", "t_x_partial_harvest_plan_title": "", "t_price_list_for_pond_cycle_title": "", "t_manage_partial_harvests": "", "t_details": "", "t_harvest_quality": "", "t_standard_price_list_not_applied": "", "t_view_price_list": "", "t_revert_to_default": "", "t_harvest_quality_update": "", "t_delete_price_list_title_desc": "", "t_delete_price_list_title_confirmation": "", "t_x_partial_harvest_title": "", "t_check_price_list_errors": "", "t_please_adjust_your_survival_rate_accordingly": "", "t_survival_at_x": "", "t_edit_final_harvest_plan": "", "t_edit_final_harvest": "", "t_record_final_harvest": "", "t_projected_harvest_details": "", "t_generate_harvest_projections": "", "t_generate": "", "t_edit_harvest_plan_confirmation": "", "t_edit_partial_harvest_plan_confirmation": "", "t_choose_a_date_and_processor": "", "t_newly_computed_harvest_projections": "", "t_refresh": "", "t_mark_as_unread": "", "t_number_of_notifications_selected_singular": "", "t_number_of_notifications_selected_plural": "", "t_manage_partial_harvests_for_pond_cycle": "", "t_this_pond_has_no_pending_transfers": "", "t_daily_parameter_title": "", "t_custom_numeric_parameter": "", "t_manage_notifications": "", "t_overhead_cost_ha_day_tooltip": "", "t_type_name": "", "t_exceeds_biomass_in_pond": "", "t_parameters": "", "t_celsius_c": "", "t_fahrenheit_f": "", "t_liters_L": "", "t_ppt": "", "t_cm": "", "t_pH": "", "t_kampi_growth": "", "t_mortality": "", "t_transparency_unit": "", "t_transp_unit": "", "t_turbidity_unit": "", "t_turb_unit": "", "t_salinity_unit": "", "t_sal_unit": "", "t_alkalinity_unit": "", "t_alk_unit": "", "t_ammonia_nh3_unit": "", "t_nh3_unit": "", "t_nitrite_no2_unit": "", "t_no2_unit": "", "t_nitrate_no3_unit": "", "t_no3_unit": "", "t_magnesium_mg_unit": "", "t_mg_unit": "", "t_calcium_ca_unit": "", "t_ca_unit": "", "t_potassium_k_unit": "", "t_k_unit": "", "t_growth": "", "t_feed_given": "", "t_all_data": "", "t_final_harvest_date": "", "t_source": "", "t_device": "", "t_go_to_summary": "", "t_version": "", "t_os": "", "t_hour": "", "t_minute": "", "t_second": "", "t_seconds": "", "t_millisecond": "", "t_milliseconds": "", "t_no_trays_available": "", "t_showing_x_to_x_of_x_items": "", "t_items": "", "t_page": "", "t_prev_page": "", "t_next_page": "", "t_uploaded": "", "t_upload": "", "t_monitoring_status": "", "t_start": "", "t_end": "", "t_pending_upload": "", "t_overhead_cost_days_of_culture": "", "t_sharing": "", "t_sandbox": "", "t_supervisor": "", "t_layout_will_be_shared_across_all_farms": "", "t_reset_data_confirmation": "", "t_projected_at_final_harvest": "", "t_simulate_projections": "", "t_no_cv_for_manual_monitoring": "", "t_monitoring_id": "", "t_group_performance": "", "t_all_farms": "", "t_filter_farms": "", "t_#_of_cycles": "", "t_average_x_for_completed_cycles_across_farms": "", "t_breakdown": "", "t_all_year": "", "t_completed_cycles": "", "t_past_x_months": "", "t_no_cycles_completed": "", "t_reset_to_last_saved_values": "", "t_reset": "", "t_proceed_confirmation_msg": "", "t_class_size_without_price_set": "", "t_monitoring_strength": "", "t_weak_great_incomplete_desc": "", "t_moderate_great_partial_desc": "", "t_strong_great_complete_desc": "", "t_great_great_great_desc": "", "t_weak_good_incomplete_desc": "", "t_moderate_good_partial_desc": "", "t_strong_good_complete_desc": "", "t_strong_good_great_desc": "", "t_weak_below_standard_incomplete_desc": "", "t_moderate_below_standard_partial_desc": "", "t_moderate_below_standard_complete_desc": "", "t_moderate_below_standard_great_desc": "", "t_weak_poor_incomplete_desc": "", "t_weak_poor_partial_desc": "", "t_weak_poor_complete_desc": "", "t_weak_poor_great_desc": "", "t_weak": "", "t_moderate": "", "t_strong": "", "t_great": "", "t_incomplete": "", "t_complete": "", "t_below_standard": "", "t_good": "", "t_poor": "", "t_quality": "", "t_completeness": "", "t_x_usual_shrimps_for_weights_out_x_counted": "", "t_carrying_capacity_usage": "", "t_do_am_grouping_msg": "", "t_do_pm_grouping_msg": "", "t_temperature_am_grouping_msg": "", "t_temperature_pm_grouping_msg": "", "t_reaching_full_capacity": "", "t_full_capacity_reached": "", "t_water_temperature_low": "", "t_water_temperature_high": "", "t_do_am_low": "", "t_do_pm_low": "", "t_do_am_high": "", "t_do_pm_high": "", "t_x_daily_parameters_created": "", "t_biomass_in_pond_kg_ha": "", "t_biomass_in_pond_lb_ha": "", "t_stocking_weight": "", "t_label": "", "t_comment": "", "t_tray_annotations_created": "", "t_farm_breakdown": "", "t_farm_breakdown_description": "", "t_change": "", "t_no_harvests_recorded_this_month": "", "t_dashboard_name": "Dashboard name", "t_add_new_dashboard": "Add new dashboard", "t_number_of_charts": "Number of charts", "t_select_layout": "Select Layout", "t_save_dashboard": "Save Dashboard", "t_my_dashboards": "My dashboards"}