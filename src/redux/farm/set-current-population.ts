import { getPopulationAnalyticsInfo } from '@hooks/use-analytics';
import { FarmStateType } from '@redux/farm';
import { PayloadAction } from '@reduxjs/toolkit';
import { actionsName, analyticsTrack, JSONValue } from '@utils/segment';
import { Population, PopulationHarvestPlan } from '@xpertsea/module-farm-sdk';
import { User } from '@xpertsea/module-user-sdk';

type PopulationPayload = Partial<Population> & {
  harvestPlan?: PopulationHarvestPlan & {
    createdByUser?: User;
    approvedByUser?: User;
  };
};

export interface PopulationType extends PopulationPayload {}

// payload
export type SetCurrentPopulationPayloadType = PopulationType;

// action reducer
export function setCurrentPopulationReducer(
  state: FarmStateType,
  action?: PayloadAction<SetCurrentPopulationPayloadType>
) {
  const properties: JSONValue = {};
  const { name: farmName, _id: farmId } = state?.currentFarm ?? {};
  const population = action.payload;
  const analyticsInfo = getPopulationAnalyticsInfo({ pond: state.currentPond, population });
  const farmAnalyticsInfo = { farmName, farmId };
  Object.assign(properties, analyticsInfo);
  Object.assign(properties, farmAnalyticsInfo);

  analyticsTrack(actionsName.populationSelected, properties).then();

  //update pond list current population on population update
  const pondList = state.currentFarmPonds ?? [];
  const currentPopulationPondId = action?.payload?.pondId;
  const pondToUpdate = pondList.find((pond) => pond._id === currentPopulationPondId);
  if (pondToUpdate) {
    const pondPopulation = pondToUpdate.currentPopulation ?? ({} as Population);
    const finalPondDetails = {
      ...pondToUpdate,
      currentPopulation: {
        ...pondPopulation,
        ...action.payload
      }
    };
    const pondIndex = pondList.findIndex((pond) => pond._id === currentPopulationPondId);
    state.currentFarmPonds[pondIndex] = finalPondDetails;
  }

  // action.payload
  state.currentPopulation = action?.payload;
}
