import { chakra, Icon, IconProps } from '@chakra-ui/react';

export function LocationMarker(props: IconProps) {
  const { color = 'icon.semanticRed', ...rest } = props;
  return (
    <Icon as='svg' w='20px' h='20px' viewBox='0 0 12 14' fill='none' {...rest}>
      <chakra.path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M6 0.333252C2.68629 0.333252 0 3.01954 0 6.33325C0 8.35085 1.20651 10.0611 2.5967 11.2659L4.89098 13.2543C5.52745 13.8059 6.47255 13.8059 7.10902 13.2543L9.4033 11.2659C10.7935 10.0611 12 8.35085 12 6.33325C12 3.01954 9.31371 0.333252 6 0.333252ZM6 3.66659C6.36819 3.66659 6.66667 3.96506 6.66667 4.33325V5.66659C6.66667 6.03478 6.36819 6.33325 6 6.33325C5.63181 6.33325 5.33333 6.03478 5.33333 5.66659V4.33325C5.33333 3.96506 5.63181 3.66659 6 3.66659ZM6 7.66659C5.63181 7.66659 5.33333 7.96506 5.33333 8.33325C5.33333 8.70144 5.63181 8.99992 6 8.99992C6.36819 8.99992 6.66667 8.70144 6.66667 8.33325C6.66667 7.96506 6.36819 7.66659 6 7.66659Z'
        fill={color}
      />
    </Icon>
  );
}
