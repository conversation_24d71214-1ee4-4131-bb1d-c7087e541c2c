import { chakra, Icon, IconProps } from '@chakra-ui/react';

interface TrafficConeFilledProps extends IconProps {
  hasBackground?: boolean;
}

export function StarFilled(props: TrafficConeFilledProps) {
  const { hasBackground = true, bgColor = 'bg.brandGreen.weakShade1', color = 'icon.brandGreen', ...rest } = props;
  return (
    <Icon as='svg' w='20px' h='20px' viewBox='0 0 20 20' fill='none' {...rest}>
      {hasBackground && <chakra.rect width='20px' height='20px' rx='10' fill={bgColor} />}
      <chakra.path
        d='M9.16522 4.56341C9.46828 3.8122 10.5318 3.8122 10.8349 4.56341L11.9975 7.4453C12.04 7.55058 12.1382 7.62288 12.2514 7.63211L15.1717 7.8704C15.9636 7.93501 16.2896 8.91888 15.693 9.44356L13.477 11.3926C13.395 11.4647 13.3587 11.5757 13.3821 11.6824L14.0908 14.9033C14.2678 15.7079 13.3606 16.3079 12.6895 15.83L10.1741 14.0391C10.0699 13.9649 9.93018 13.9649 9.82601 14.0391L7.31063 15.83C6.63952 16.3079 5.73231 15.7079 5.90932 14.9033L6.61795 11.6824C6.64142 11.5757 6.60507 11.4647 6.52306 11.3926L4.30697 9.44357C3.7104 8.9189 4.03643 7.93501 4.82827 7.8704L7.74872 7.63211C7.86187 7.62288 7.96012 7.55058 8.00259 7.4453L9.16522 4.56341Z'
        fill={color}
      />
    </Icon>
  );
}
