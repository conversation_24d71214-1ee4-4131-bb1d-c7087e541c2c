import { chakra, Icon, IconProps } from '@chakra-ui/react';

export function ImageEditIcon(
  props: IconProps & {
    firstSectionColor?: IconProps['color'];
    secondSectionColor?: IconProps['color'];
  }
) {
  const { firstSectionColor = '#BABDCC', secondSectionColor = 'black', ...rest } = props;

  return (
    <Icon as='svg' width='24px' height='24px' viewBox='0 0 24 24' fill='none' {...rest}>
      <chakra.path
        fill={firstSectionColor}
        d='M3 9.4C3 7.15979 3 6.03968 3.43597 5.18404C3.81947 4.43139 4.43139 3.81947 5.18404 3.43597C6.03968 3 7.15979 3 9.4 3H14.6C16.8402 3 17.9603 3 18.816 3.43597C19.5686 3.81947 20.1805 4.43139 20.564 5.18404C21 6.03968 21 7.15979 21 9.4V14.6C21 16.8402 21 17.9603 20.564 18.816C20.1805 19.5686 19.5686 20.1805 18.816 20.564C17.9603 21 16.8402 21 14.6 21H9.4C7.15979 21 6.03968 21 5.18404 20.564C4.43139 20.1805 3.81947 19.5686 3.43597 18.816C3 17.9603 3 16.8402 3 14.6V9.4Z'
      />
      <chakra.path
        fill={secondSectionColor}
        d='M9 11C10.1046 11 11 10.1046 11 9C11 7.89543 10.1046 7 9 7C7.89543 7 7 7.89543 7 9C7 10.1046 7.89543 11 9 11Z'
      />
      <chakra.path
        fill={secondSectionColor}
        d='M3 15.0876C3.00116 16.0588 3.0095 16.7991 3.06965 17.3959L6.40166 15.3357C6.86667 15.0481 6.92276 15.026 6.96158 15.016C7.0268 14.9993 7.09474 14.9958 7.16131 15.006C7.20094 15.012 7.25896 15.0283 7.75057 15.2676L8.27893 15.5247L8.36869 15.5685C8.74311 15.7517 9.11943 15.9357 9.52752 15.9895C9.88348 16.0364 10.2454 16.0061 10.5886 15.9006C10.982 15.7797 11.3225 15.5356 11.6612 15.2927L11.7424 15.2346L14.2481 13.4455C14.7946 13.0553 14.864 13.0246 14.9095 13.0125C14.987 12.9918 15.0684 12.9901 15.1467 13.0075C15.1926 13.0177 15.2633 13.0454 15.8259 13.412L16.4538 13.8212C16.9166 14.1227 17.5361 13.9921 17.8376 13.5294C18.1392 13.0667 18.0085 12.4471 17.5458 12.1456L16.9179 11.7364L16.822 11.6737C16.4264 11.4146 16.0294 11.1546 15.5798 11.0549C15.1881 10.9681 14.7814 10.9767 14.3938 11.0801C13.9489 11.1989 13.5633 11.4755 13.179 11.7512L13.0859 11.8179L10.5803 13.6069C10.1011 13.949 10.0417 13.9764 10.001 13.9889C9.93239 14.01 9.86001 14.016 9.78882 14.0067C9.74664 14.0011 9.68349 13.984 9.15411 13.7263L8.62575 13.4692L8.5426 13.4286C8.19396 13.2581 7.84333 13.0867 7.46198 13.0287C7.1291 12.9781 6.78942 12.9952 6.46333 13.0791C6.08976 13.1752 5.75817 13.3811 5.42847 13.5858L5.34984 13.6346L3 15.0876Z'
      />
      <chakra.path
        fill={secondSectionColor}
        d='M15.9239 19.1824L15.0375 21.3084C14.8656 21.7206 15.2792 22.1342 15.6914 21.9624L17.8175 21.076C17.938 21.0258 18.0475 20.9525 18.1398 20.8601L21.7029 17.297C22.0934 16.9065 22.0934 16.2733 21.7029 15.8828L21.1171 15.297C20.7266 14.9065 20.0934 14.9065 19.7029 15.297L16.1398 18.8602C16.0475 18.9525 15.9741 19.0619 15.9239 19.1824Z'
      />
    </Icon>
  );
}
