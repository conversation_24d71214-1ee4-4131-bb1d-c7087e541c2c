# This pre-commit hook script checks the linting and typechecking,
# extracts translations, and stages updated translation keys.

# Get the current directory of this script
CURRENT_DIRECTORY=$(dirname "$0")

# Find the root directory of the project
ROOT_DIRECTORY=$(git rev-parse --show-toplevel)

# Run yarn commands
yarn lint-staged
yarn typecheck
yarn translations:extract

# Stage updated translations
echo "Staging updated translations..."
git add "${ROOT_DIRECTORY}/src/i18n/*.json"
echo "Translations keys updated"
