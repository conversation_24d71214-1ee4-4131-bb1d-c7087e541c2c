{"name": "web-insights", "version": "1.0.0", "private": true, "scripts": {"analyze": "ANALYZE=true yarn build", "build": "next build", "dev": "API_ENV=development next dev -p ${PORT:-4500}", "e2e:dev-build": "./node_modules/.bin/concurrently 'yarn dev' 'yarn e2e:test' --kill-others --success first", "e2e:prod-build": "./node_modules/.bin/concurrently 'yarn start' 'yarn e2e:test' --kill-others --success first", "e2e:test": "sleep 5 && yarn --cwd ./e2e test:ci", "favicon": "NODE_ENV=production node ./.scripts/favicon.js 'kampi.ai' 'kampi.ai' 'kampi.ai team' 'https://kampi.ai'", "lint": "next lint", "lint:fix": "next lint --fix --ext .ts,.tsx,.js,.jsx", "local": "API_ENV=local next dev -p ${PORT:-4500}", "prepare": "husky", "prettier": "prettier --write \"src/**/*.{ts,tsx,js,jsx}\" \"pages/**/*.{ts,tsx,js,jsx}\"", "prod": "API_ENV=production next dev -p ${PORT:-4500}", "start": "next start -p ${PORT:-4500}", "test": "jest --c __tests__/jest.config.js --ci", "test:watch": "jest --c __tests__/jest.config.js --watch", "transifex:install": "bash .scripts/install-transifex.sh", "translation:commit": "bash .scripts/i18n/commit-translations.sh", "translation:import": "node .scripts/i18n/importTranslations.js", "translation:publish": "node .scripts/i18n/exportTranslations.js", "translations:extract": "i18next-scanner --config .scripts/i18n/i18next-scanner.config.js", "typecheck": "tsc --pretty --skip<PERSON><PERSON><PERSON><PERSON><PERSON> --noEmit", "update-sdks": "node ./.scripts/update-sdks.js", "clean-up": "rm -rf ./node_modules && rm -f ./yarn.lock", "postinstall": "npx @chakra-ui/cli typegen ./src/styles/app-theme.ts", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org xpertsea --project kampi-web ./.next && sentry-cli sourcemaps upload --org xpertsea --project kampi-web ./.next"}, "resolutions": {"string-width": "4.2.3", "strip-ansi": "6.0.1"}, "dependencies": {"@chakra-ui/react": "3.24.2", "@dnd-kit/core": "6.3.1", "@dnd-kit/modifiers": "9.0.0", "@dnd-kit/sortable": "10.0.0", "@emotion/react": "11.14.0", "@fontsource-variable/nunito": "5.2.6", "@handsontable/react-wrapper": "15.3.0", "@hookform/resolvers": "5.2.1", "@next/bundle-analyzer": "15.4.6", "@redux-devtools/extension": "3.3.0", "@reduxjs/toolkit": "2.8.2", "@sentry/nextjs": "10.5.0", "@serwist/next": "9.1.1", "@splidejs/react-splide": "0.7.12", "@wavesurfer/react": "1.0.11", "@xpertsea/module-data-sdk": "ssh://**************/xpertsea/module-data-sdk-js#develop-25-07-31-19-36-19-15c7457", "@xpertsea/module-farm-sdk": "ssh://**************/xpertsea/module-farm-sdk-js#develop-25-08-11-11-30-56-0b65271", "@xpertsea/module-user-sdk": "ssh://**************/xpertsea/module-user-sdk-js#develop-25-08-01-03-43-16-808893a", "axios": "1.11.0", "bson-objectid": "2.0.4", "cookie": "1.0.2", "handsontable": "15.3.0", "highcharts": "12.3.0", "highcharts-react-official": "3.2.2", "ismobilejs": "1.1.1", "isomorphic-base64": "1.0.2", "leaflet": "^2.0.0-alpha", "lodash": "4.17.21", "lunarphase-js": "2.0.3", "luxon": "3.7.1", "millify": "6.1.0", "next": "15.4.6", "next-localization": "0.12.0", "next-pwa": "5.6.0", "next-seo": "6.8.0", "next-themes": "0.4.6", "rc-pagination": "5.1.0", "react": "19.1.1", "react-content-loader": "7.1.1", "react-datepicker": "8.5.0", "react-dom": "19.1.1", "react-dropzone": "14.3.8", "react-error-boundary": "6.0.0", "react-hook-form": "7.62.0", "react-icons": "5.5.0", "react-intersection-observer": "9.16.0", "react-leaflet": "^5.0.0", "react-number-format": "5.4.4", "react-redux": "9.2.0", "react-select": "5.10.2", "react-zoom-pan-pinch": "^3.7.0", "semver": "^7.7.2", "timezones-list": "3.1.0", "type-fest": "4.41.0", "wavesurfer.js": "7.10.1", "xlsx": "0.18.5", "yup": "1.7.0"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "4.6.2", "@sentry/cli": "2.52.0", "@testing-library/dom": "10.4.1", "@testing-library/jest-dom": "6.7.0", "@testing-library/react": "16.3.0", "@types/jest": "30.0.0", "@types/leaflet": "^1.9.20", "@types/lodash": "4.17.20", "@types/luxon": "3.7.1", "@types/minimatch": "5.1.2", "@types/next-pwa": "5.6.9", "@types/node": "24.3.0", "@types/react": "19.1.10", "@types/react-dom": "19.1.7", "@types/semver": "^7.7.0", "@types/wavesurfer.js": "6.0.12", "@typescript-eslint/eslint-plugin": "8.39.1", "autoprefixer": "10.4.21", "concurrently": "9.2.0", "cssnano": "7.1.0", "dotenv": "17.2.1", "eslint": "9.33.0", "eslint-config-next": "15.4.6", "eslint-config-prettier": "10.1.8", "eslint-plugin-import": "2.32.0", "eslint-plugin-react-perf": "3.3.3", "eslint-plugin-unused-imports": "4.2.0", "husky": "9.1.7", "i18next-scanner": "4.6.0", "jest": "30.0.5", "jest-environment-jsdom": "30.0.5", "lint-staged": "16.1.5", "nodemon": "3.1.10", "postcss": "8.5.6", "prettier": "3.6.2", "serwist": "9.1.1", "ts-jest": "29.4.1", "ts-node": "10.9.2", "typescript": "5.9.2"}, "packageManager": "yarn@1.22.22", "engines": {"node": ">=17"}, "engineStrict": false, "os": ["darwin", "linux"]}